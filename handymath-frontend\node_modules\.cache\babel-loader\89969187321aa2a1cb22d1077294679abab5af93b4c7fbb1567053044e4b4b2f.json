{"ast": null, "code": "export var toDocs = {\n  name: 'to',\n  category: 'Units',\n  syntax: ['x to unit', 'to(x, unit)'],\n  description: 'Change the unit of a value.',\n  examples: ['5 inch to cm', '3.2kg to g', '16 bytes in bits'],\n  seealso: []\n};", "map": {"version": 3, "names": ["toDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/units/to.js"], "sourcesContent": ["export var toDocs = {\n  name: 'to',\n  category: 'Units',\n  syntax: ['x to unit', 'to(x, unit)'],\n  description: 'Change the unit of a value.',\n  examples: ['5 inch to cm', '3.2kg to g', '16 bytes in bits'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACpCC,WAAW,EAAE,6BAA6B;EAC1CC,QAAQ,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,kBAAkB,CAAC;EAC5DC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}