{"ast": null, "code": "export var normDocs = {\n  name: 'norm',\n  category: 'Arithmetic',\n  syntax: ['norm(x)', 'norm(x, p)'],\n  description: 'Calculate the norm of a number, vector or matrix.',\n  examples: ['abs(-3.5)', 'norm(-3.5)', 'norm(3 - 4i)', 'norm([1, 2, -3], Infinity)', 'norm([1, 2, -3], -Infinity)', 'norm([3, 4], 2)', 'norm([[1, 2], [3, 4]], 1)', 'norm([[1, 2], [3, 4]], \"inf\")', 'norm([[1, 2], [3, 4]], \"fro\")']\n};", "map": {"version": 3, "names": ["normDocs", "name", "category", "syntax", "description", "examples"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/norm.js"], "sourcesContent": ["export var normDocs = {\n  name: 'norm',\n  category: 'Arithmetic',\n  syntax: ['norm(x)', 'norm(x, p)'],\n  description: 'Calculate the norm of a number, vector or matrix.',\n  examples: ['abs(-3.5)', 'norm(-3.5)', 'norm(3 - 4i)', 'norm([1, 2, -3], Infinity)', 'norm([1, 2, -3], -Infinity)', 'norm([3, 4], 2)', 'norm([[1, 2], [3, 4]], 1)', 'norm([[1, 2], [3, 4]], \"inf\")', 'norm([[1, 2], [3, 4]], \"fro\")']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;EACjCC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,4BAA4B,EAAE,6BAA6B,EAAE,iBAAiB,EAAE,2BAA2B,EAAE,+BAA+B,EAAE,+BAA+B;AACrO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}