{"ast": null, "code": "export var createUnitDocs = {\n  name: 'createUnit',\n  category: 'Construction',\n  syntax: ['createUnit(definitions)', 'createUnit(name, definition)'],\n  description: 'Create a user-defined unit and register it with the Unit type.',\n  examples: ['createUnit(\"foo\")', 'createUnit(\"knot\", {definition: \"0.********* m/s\", aliases: [\"knots\", \"kt\", \"kts\"]})', 'createUnit(\"mph\", \"1 mile/hour\")'],\n  seealso: ['unit', 'splitUnit']\n};", "map": {"version": 3, "names": ["createUnitDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/createUnit.js"], "sourcesContent": ["export var createUnitDocs = {\n  name: 'createUnit',\n  category: 'Construction',\n  syntax: ['createUnit(definitions)', 'createUnit(name, definition)'],\n  description: 'Create a user-defined unit and register it with the Unit type.',\n  examples: ['createUnit(\"foo\")', 'createUnit(\"knot\", {definition: \"0.********* m/s\", aliases: [\"knots\", \"kt\", \"kts\"]})', 'createUnit(\"mph\", \"1 mile/hour\")'],\n  seealso: ['unit', 'splitUnit']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,yBAAyB,EAAE,8BAA8B,CAAC;EACnEC,WAAW,EAAE,gEAAgE;EAC7EC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,sFAAsF,EAAE,kCAAkC,CAAC;EAC3JC,OAAO,EAAE,CAAC,MAAM,EAAE,WAAW;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}