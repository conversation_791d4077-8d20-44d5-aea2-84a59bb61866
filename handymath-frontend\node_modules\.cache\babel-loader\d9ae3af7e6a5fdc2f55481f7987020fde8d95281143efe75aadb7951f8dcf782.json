{"ast": null, "code": "export var typedDocs = {\n  name: 'typed',\n  category: 'Core',\n  syntax: ['typed(signatures)', 'typed(name, signatures)'],\n  description: 'Create a typed function.',\n  examples: ['double = typed({ \"number\": f(x)=x+x, \"string\": f(x)=concat(x,x) })', 'double(2)', 'double(\"hello\")'],\n  seealso: []\n};", "map": {"version": 3, "names": ["typedDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/core/typed.js"], "sourcesContent": ["export var typedDocs = {\n  name: 'typed',\n  category: 'Core',\n  syntax: ['typed(signatures)', 'typed(name, signatures)'],\n  description: 'Create a typed function.',\n  examples: ['double = typed({ \"number\": f(x)=x+x, \"string\": f(x)=concat(x,x) })', 'double(2)', 'double(\"hello\")'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,yBAAyB,CAAC;EACxDC,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE,CAAC,oEAAoE,EAAE,WAAW,EAAE,iBAAiB,CAAC;EAChHC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}