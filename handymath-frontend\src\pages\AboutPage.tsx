import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SimpleHeader from '../components/SimpleHeader';

const AboutPage: React.FC = () => {
  const features = [
    {
      icon: '🧮',
      title: 'Résolution d\'équations',
      description: 'Résolvez des équations mathématiques complexes avec des étapes détaillées'
    },
    {
      icon: '📸',
      title: 'Reconnaissance OCR',
      description: 'Prenez une photo de votre équation et obtenez la solution instantanément'
    },
    {
      icon: '📚',
      title: 'Exercices interactifs',
      description: 'Pratiquez avec des exercices adaptés à votre niveau'
    },
    {
      icon: '📊',
      title: 'Suivi de progression',
      description: 'Suivez vos progrès et identifiez vos points d\'amélioration'
    },
    {
      icon: '🎯',
      title: 'Apprentissage adaptatif',
      description: 'Contenu personnalisé selon vos besoins et votre niveau'
    },
    {
      icon: '🌙',
      title: 'Mode sombre',
      description: 'Interface adaptée pour étudier confortablement à toute heure'
    }
  ];



  const stats = [
    { number: '1000+', label: 'Équations résolues' },
    { number: '500+', label: 'Utilisateurs actifs' },
    { number: '50+', label: 'Exercices disponibles' },
    { number: '99%', label: 'Taux de satisfaction' }
  ];

  return (
    <>
      <SimpleHeader title="À propos" showBackButton />
      <div className="container mx-auto px-4 py-8">

        {/* Hero Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="text-6xl mb-6">🧮</div>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            HandyMath
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Votre assistant personnel pour l'apprentissage des mathématiques.
            Résolvez, apprenez et progressez avec notre plateforme innovante.
          </p>
        </motion.div>

        {/* Mission */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              Notre Mission
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Rendre les mathématiques accessibles à tous en combinant technologie moderne et pédagogie innovante.
              Nous croyons que chaque étudiant peut exceller en mathématiques avec les bons outils et le bon accompagnement.
            </p>
          </div>
        </motion.div>

        {/* Statistiques */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Fonctionnalités */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
            Fonctionnalités Principales
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>



        {/* Technologie */}
        <motion.div
          className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-xl p-8 mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              Technologie Moderne
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
              HandyMath utilise les dernières technologies web et d'intelligence artificielle
              pour offrir une expérience d'apprentissage optimale et personnalisée.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              {['React', 'TypeScript', 'Python', 'Django', 'OCR', 'IA'].map((tech, index) => (
                <span
                  key={index}
                  className="px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 shadow-md"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            Prêt à commencer ?
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
            Rejoignez des milliers d'étudiants qui améliorent leurs compétences en mathématiques avec HandyMath.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors"
            >
              Créer un compte
            </Link>
            <Link
              to="/solver"
              className="px-8 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
            >
              Essayer le solveur
            </Link>
            <Link
              to="/contact"
              className="px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium rounded-lg transition-colors"
            >
              Nous contacter
            </Link>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default AboutPage;
