#!/usr/bin/env python3
"""
Script de diagnostic pour identifier les problèmes de stabilité de progression des cours
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import User, Course, Chapter, Lesson, LessonProgress, CourseEnrollment
from django.db.models import Count, Q
from collections import defaultdict

def analyze_progression_stability():
    """Analyser la stabilité de la progression des cours"""
    print("🔍 DIAGNOSTIC DE STABILITÉ DE PROGRESSION DES COURS")
    print("=" * 60)
    
    # 1. Vérifier les cours avec des progressions incohérentes
    print("\n1️⃣ ANALYSE DES INCOHÉRENCES DE PROGRESSION")
    print("-" * 40)
    
    inconsistent_users = []
    
    for user in User.objects.all():
        print(f"\n👤 Utilisateur: {user.username}")
        user_issues = []
        
        for course in Course.objects.filter(status='published'):
            # Progression calculée par la méthode
            calculated_progress = course.get_progress_for_user(user)
            
            # Progression manuelle
            total_lessons = Lesson.objects.filter(
                chapter__course=course,
                is_published=True
            ).count()
            
            completed_lessons = LessonProgress.objects.filter(
                user=user,
                lesson__chapter__course=course,
                lesson__is_published=True,
                completed=True
            ).count()
            
            manual_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
            
            # Vérifier l'incohérence
            if abs(calculated_progress - manual_progress) > 0.1:
                issue = {
                    'course': course.title,
                    'calculated': calculated_progress,
                    'manual': manual_progress,
                    'total_lessons': total_lessons,
                    'completed_lessons': completed_lessons
                }
                user_issues.append(issue)
                print(f"   ⚠️ {course.title}:")
                print(f"      Calculée: {calculated_progress}%")
                print(f"      Manuelle: {manual_progress}%")
                print(f"      Leçons: {completed_lessons}/{total_lessons}")
            elif calculated_progress > 0:
                print(f"   ✅ {course.title}: {calculated_progress}%")
        
        if user_issues:
            inconsistent_users.append({
                'user': user.username,
                'issues': user_issues
            })
    
    # 2. Analyser les progressions qui changent sans action utilisateur
    print("\n\n2️⃣ ANALYSE DES PROGRESSIONS FANTÔMES")
    print("-" * 40)
    
    # Créer un utilisateur test pour vérifier
    test_user, created = User.objects.get_or_create(
        username='stability_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Stability',
            'last_name': 'Test'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Utilisateur test créé: {test_user.username}")
    else:
        print(f"👤 Utilisateur test existant: {test_user.username}")
    
    # Mesurer la progression initiale
    initial_progressions = {}
    for course in Course.objects.filter(status='published'):
        initial_progressions[course.id] = course.get_progress_for_user(test_user)
    
    print(f"\n📊 Progressions initiales:")
    for course in Course.objects.filter(status='published'):
        progress = initial_progressions[course.id]
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        print(f"   📚 {course.title}: {progress}% ({total_lessons} leçons)")
        
        if progress > 0 and total_lessons > 0:
            print(f"      ⚠️ PROBLÈME: Progression > 0 pour un utilisateur sans activité!")
    
    # 3. Analyser les doublons de progression
    print("\n\n3️⃣ ANALYSE DES DOUBLONS DE PROGRESSION")
    print("-" * 40)
    
    duplicates = LessonProgress.objects.values('user', 'lesson').annotate(
        count=Count('id')
    ).filter(count__gt=1)
    
    if duplicates.exists():
        print(f"⚠️ {duplicates.count()} doublons de progression trouvés:")
        for dup in duplicates:
            user = User.objects.get(id=dup['user'])
            lesson = Lesson.objects.get(id=dup['lesson'])
            print(f"   - {user.username} - {lesson.title} ({dup['count']} entrées)")
    else:
        print("✅ Aucun doublon de progression trouvé")
    
    return inconsistent_users, initial_progressions

def analyze_lesson_creation_patterns():
    """Analyser les patterns de création de progression de leçons"""
    print("\n\n4️⃣ ANALYSE DES PATTERNS DE CRÉATION")
    print("-" * 40)
    
    # Vérifier les progressions créées automatiquement
    auto_created = LessonProgress.objects.filter(
        completed=False,
        time_spent=0
    ).count()
    
    manually_completed = LessonProgress.objects.filter(
        completed=True
    ).count()
    
    print(f"📊 Progressions automatiques: {auto_created}")
    print(f"✅ Progressions complétées: {manually_completed}")
    
    # Analyser par utilisateur
    user_stats = defaultdict(lambda: {'auto': 0, 'completed': 0})
    
    for progress in LessonProgress.objects.all():
        if progress.completed:
            user_stats[progress.user.username]['completed'] += 1
        else:
            user_stats[progress.user.username]['auto'] += 1
    
    print(f"\n📈 Statistiques par utilisateur:")
    for username, stats in user_stats.items():
        total = stats['auto'] + stats['completed']
        completion_rate = (stats['completed'] / total * 100) if total > 0 else 0
        print(f"   👤 {username}: {stats['completed']}/{total} ({completion_rate:.1f}%)")

def check_course_structure_issues():
    """Vérifier les problèmes de structure des cours"""
    print("\n\n5️⃣ ANALYSE DE LA STRUCTURE DES COURS")
    print("-" * 40)
    
    for course in Course.objects.filter(status='published'):
        print(f"\n📚 Cours: {course.title}")
        
        # Vérifier les chapitres
        chapters = course.chapters.all()
        print(f"   📖 Chapitres: {chapters.count()}")
        
        total_lessons = 0
        published_lessons = 0
        
        for chapter in chapters:
            chapter_lessons = chapter.lessons.all()
            chapter_published = chapter.lessons.filter(is_published=True)
            
            total_lessons += chapter_lessons.count()
            published_lessons += chapter_published.count()
            
            print(f"      - {chapter.title}: {chapter_published.count()}/{chapter_lessons.count()} leçons publiées")
        
        print(f"   📊 Total: {published_lessons}/{total_lessons} leçons publiées")
        
        if published_lessons == 0:
            print(f"      ⚠️ PROBLÈME: Cours publié sans leçons publiées!")

def main():
    """Fonction principale de diagnostic"""
    try:
        # Analyser la stabilité
        inconsistent_users, initial_progressions = analyze_progression_stability()
        
        # Analyser les patterns de création
        analyze_lesson_creation_patterns()
        
        # Vérifier la structure des cours
        check_course_structure_issues()
        
        # Résumé des problèmes
        print("\n\n📋 RÉSUMÉ DES PROBLÈMES IDENTIFIÉS")
        print("=" * 60)
        
        if inconsistent_users:
            print(f"⚠️ {len(inconsistent_users)} utilisateurs avec des progressions incohérentes")
            for user_data in inconsistent_users:
                print(f"   - {user_data['user']}: {len(user_data['issues'])} cours affectés")
        else:
            print("✅ Aucune incohérence de progression détectée")
        
        # Vérifier les progressions fantômes
        phantom_courses = []
        for course in Course.objects.filter(status='published'):
            if initial_progressions[course.id] > 0:
                total_lessons = Lesson.objects.filter(
                    chapter__course=course,
                    is_published=True
                ).count()
                if total_lessons > 0:
                    phantom_courses.append(course.title)
        
        if phantom_courses:
            print(f"⚠️ {len(phantom_courses)} cours avec progressions fantômes:")
            for course_title in phantom_courses:
                print(f"   - {course_title}")
        else:
            print("✅ Aucune progression fantôme détectée")
        
        print("\n🔧 RECOMMANDATIONS:")
        if inconsistent_users or phantom_courses:
            print("   1. Exécuter le script de correction des progressions")
            print("   2. Vérifier la logique de calcul de progression")
            print("   3. Nettoyer les progressions automatiques incorrectes")
        else:
            print("   ✅ Le système de progression semble stable")
        
    except Exception as e:
        print(f"❌ Erreur lors du diagnostic: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
