{"ast": null, "code": "export var simplifyDocs = {\n  name: 'simplify',\n  category: 'Algebra',\n  syntax: ['simplify(expr)', 'simplify(expr, rules)'],\n  description: 'Simplify an expression tree.',\n  examples: ['simplify(\"3 + 2 / 4\")', 'simplify(\"2x + x\")', 'f = parse(\"x * (x + 2 + x)\")', 'simplified = simplify(f)', 'simplified.evaluate({x: 2})'],\n  seealso: ['simplifyCore', 'derivative', 'evaluate', 'parse', 'rationalize', 'resolve']\n};", "map": {"version": 3, "names": ["simplifyDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/simplify.js"], "sourcesContent": ["export var simplifyDocs = {\n  name: 'simplify',\n  category: 'Algebra',\n  syntax: ['simplify(expr)', 'simplify(expr, rules)'],\n  description: 'Simplify an expression tree.',\n  examples: ['simplify(\"3 + 2 / 4\")', 'simplify(\"2x + x\")', 'f = parse(\"x * (x + 2 + x)\")', 'simplified = simplify(f)', 'simplified.evaluate({x: 2})'],\n  seealso: ['simplifyCore', 'derivative', 'evaluate', 'parse', 'rationalize', 'resolve']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;EACnDC,WAAW,EAAE,8BAA8B;EAC3CC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,oBAAoB,EAAE,8BAA8B,EAAE,0BAA0B,EAAE,6BAA6B,CAAC;EACpJC,OAAO,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS;AACvF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}