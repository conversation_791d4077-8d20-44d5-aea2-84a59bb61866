#!/usr/bin/env python3
"""
Script pour trouver l'origine du problème de progression à 45%
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import User, Course, Chapter, Lesson, LessonProgress, CourseEnrollment

def find_45_percent_issue():
    """Trouver l'origine du problème de progression à 45%"""
    print("🔍 RECHERCHE: Origine du problème de progression à 45%")
    print("=" * 60)
    
    # Chercher tous les utilisateurs avec une progression proche de 45%
    problematic_users = []
    
    for user in User.objects.all():
        print(f"\n👤 Utilisateur: {user.username}")
        user_issues = []
        
        for course in Course.objects.filter(status='published'):
            progress = course.get_progress_for_user(user)
            
            # Chercher les progressions entre 40% et 50% (autour de 45%)
            if 40 <= progress <= 50:
                print(f"   📊 {course.title}: {progress}% ⚠️")
                
                # Analyser en détail cette progression
                total_lessons = Lesson.objects.filter(
                    chapter__course=course,
                    is_published=True
                ).count()
                
                completed_lessons = LessonProgress.objects.filter(
                    user=user,
                    lesson__chapter__course=course,
                    lesson__is_published=True,
                    completed=True
                ).count()
                
                all_progressions = LessonProgress.objects.filter(
                    user=user,
                    lesson__chapter__course=course
                )
                
                print(f"      📖 Total leçons: {total_lessons}")
                print(f"      ✅ Leçons complétées: {completed_lessons}")
                print(f"      📋 Progressions créées: {all_progressions.count()}")
                
                # Calculer la progression manuelle
                manual_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
                print(f"      🧮 Progression manuelle: {manual_progress}%")
                
                # Analyser chaque progression
                print(f"      🔍 Détail des progressions:")
                for prog in all_progressions:
                    print(f"         - {prog.lesson.title}: completed={prog.completed}, time_spent={prog.time_spent}")
                
                user_issues.append({
                    'course': course.title,
                    'progress': progress,
                    'total_lessons': total_lessons,
                    'completed_lessons': completed_lessons,
                    'manual_progress': manual_progress
                })
            elif progress > 0:
                print(f"   📊 {course.title}: {progress}%")
        
        if user_issues:
            problematic_users.append({
                'user': user.username,
                'issues': user_issues
            })
    
    return problematic_users

def analyze_specific_course_progressions():
    """Analyser les progressions spécifiques qui pourraient donner 45%"""
    print(f"\n\n🔍 ANALYSE: Progressions spécifiques donnant ~45%")
    print("=" * 60)
    
    for course in Course.objects.filter(status='published'):
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        print(f"\n📚 Cours: {course.title} ({total_lessons} leçons)")
        
        # Calculer quelles combinaisons de leçons complétées donnent ~45%
        for completed in range(total_lessons + 1):
            if total_lessons > 0:
                progress = (completed / total_lessons) * 100
                if 40 <= progress <= 50:
                    print(f"   🎯 {completed}/{total_lessons} leçons = {progress:.1f}%")

def check_lesson_progress_anomalies():
    """Vérifier les anomalies dans les progressions de leçons"""
    print(f"\n\n🔍 ANALYSE: Anomalies dans les progressions de leçons")
    print("=" * 60)
    
    # Chercher les progressions avec des valeurs suspectes
    suspicious_progressions = LessonProgress.objects.filter(
        completed=True,
        time_spent=0
    )
    
    if suspicious_progressions.exists():
        print(f"⚠️ {suspicious_progressions.count()} progressions complétées sans temps passé:")
        
        users_affected = {}
        for prog in suspicious_progressions:
            user = prog.user.username
            course = prog.lesson.chapter.course.title
            
            if user not in users_affected:
                users_affected[user] = {}
            if course not in users_affected[user]:
                users_affected[user][course] = 0
            users_affected[user][course] += 1
        
        for user, courses in users_affected.items():
            print(f"   👤 {user}:")
            for course, count in courses.items():
                print(f"      📚 {course}: {count} leçons")
    
    # Chercher les progressions avec des temps très élevés
    high_time_progressions = LessonProgress.objects.filter(
        time_spent__gt=3600  # Plus d'1 heure
    )
    
    if high_time_progressions.exists():
        print(f"\n⏰ {high_time_progressions.count()} progressions avec temps élevé:")
        for prog in high_time_progressions[:10]:  # Limiter à 10
            print(f"   - {prog.user.username}: {prog.lesson.title} ({prog.time_spent}s)")

def simulate_new_user_enrollment():
    """Simuler l'inscription d'un nouvel utilisateur pour reproduire le problème"""
    print(f"\n\n🧪 SIMULATION: Inscription d'un nouvel utilisateur")
    print("=" * 60)
    
    # Créer un utilisateur comme dans l'interface
    new_user, created = User.objects.get_or_create(
        username='simulate_new_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Simulate',
            'last_name': 'New'
        }
    )
    
    if not created:
        # Nettoyer les données existantes
        LessonProgress.objects.filter(user=new_user).delete()
        CourseEnrollment.objects.filter(user=new_user).delete()
        print(f"🧹 Données nettoyées pour: {new_user.username}")
    else:
        new_user.set_password('testpass123')
        new_user.save()
        print(f"✅ Nouvel utilisateur créé: {new_user.username}")
    
    # Tester l'inscription à chaque cours
    for course in Course.objects.filter(status='published'):
        print(f"\n📚 Test d'inscription au cours: {course.title}")
        
        # Progression avant inscription
        progress_before = course.get_progress_for_user(new_user)
        print(f"   📊 Avant inscription: {progress_before}%")
        
        # Inscription
        enrollment, enrollment_created = CourseEnrollment.objects.get_or_create(
            user=new_user,
            course=course,
            defaults={'is_active': True}
        )
        
        # Progression après inscription
        progress_after = course.get_progress_for_user(new_user)
        print(f"   📊 Après inscription: {progress_after}%")
        
        # Simuler l'accès au cours (comme dans get_course_detail)
        chapters = course.chapters.filter(is_published=True).order_by('order')
        progressions_created = 0
        
        for chapter in chapters:
            lessons = chapter.lessons.filter(is_published=True).order_by('order')
            for lesson in lessons:
                # Vérifier l'accessibilité de la leçon
                is_accessible = lesson.is_accessible_for_user(new_user)
                
                if is_accessible:
                    # Créer la progression comme dans l'API
                    lesson_progress, created = LessonProgress.objects.get_or_create(
                        user=new_user,
                        lesson=lesson,
                        defaults={'completed': False, 'time_spent': 0}
                    )
                    if created:
                        progressions_created += 1
        
        print(f"   📋 Progressions créées: {progressions_created}")
        
        # Progression finale
        progress_final = course.get_progress_for_user(new_user)
        print(f"   📊 Progression finale: {progress_final}%")
        
        if progress_final != progress_before:
            print(f"   ⚠️ CHANGEMENT DÉTECTÉ: {progress_before}% → {progress_final}%")

def main():
    """Fonction principale"""
    try:
        # 1. Chercher les utilisateurs avec ~45% de progression
        problematic_users = find_45_percent_issue()
        
        # 2. Analyser les progressions spécifiques
        analyze_specific_course_progressions()
        
        # 3. Vérifier les anomalies
        check_lesson_progress_anomalies()
        
        # 4. Simuler un nouvel utilisateur
        simulate_new_user_enrollment()
        
        # Résumé
        print(f"\n\n📋 RÉSUMÉ")
        print("=" * 30)
        
        if problematic_users:
            print(f"⚠️ {len(problematic_users)} utilisateurs avec des progressions ~45%:")
            for user_data in problematic_users:
                print(f"   👤 {user_data['user']}: {len(user_data['issues'])} cours affectés")
        else:
            print("✅ Aucun utilisateur avec progression ~45% trouvé actuellement")
        
        print(f"\n💡 RECOMMANDATIONS:")
        print(f"   1. Vérifier la logique d'accessibilité des leçons")
        print(f"   2. Vérifier si des progressions sont marquées comme complétées automatiquement")
        print(f"   3. Corriger l'ordre des cours (Algèbre et Algèbre de Base ont le même ordre)")
        print(f"   4. Vérifier la logique de sélection de cours dans le frontend")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
