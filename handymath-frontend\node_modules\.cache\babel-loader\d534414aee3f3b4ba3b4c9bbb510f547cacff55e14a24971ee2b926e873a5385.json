{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { isInteger as isIntegerNumber } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isInteger';\nvar dependencies = ['typed'];\nexport var createIsInteger = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is an integer number.\n   * The function supports `number`, `BigNumber`, and `Fraction`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isInteger(x)\n   *\n   * Examples:\n   *\n   *    math.isInteger(2)                     // returns true\n   *    math.isInteger(0)                     // returns true\n   *    math.isInteger(0.5)                   // returns false\n   *    math.isInteger(math.bignumber(500))   // returns true\n   *    math.isInteger(math.fraction(4))      // returns true\n   *    math.isInteger('3')                   // returns true\n   *    math.isInteger([3, 0.5, -2])          // returns [true, false, true]\n   *    math.isInteger(math.complex('2-4i'))  // throws TypeError\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isNegative, isZero\n   *\n   * @param {number | BigNumber | bigint | Fraction | Array | Matrix} x   Value to be tested\n   * @return {boolean}  Returns true when `x` contains a numeric, integer value.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: isIntegerNumber,\n    // TODO: what to do with isInteger(add(0.1, 0.2))  ?\n\n    BigNumber: function BigNumber(x) {\n      return x.isInt();\n    },\n    bigint: function bigint(x) {\n      return true;\n    },\n    Fraction: function Fraction(x) {\n      return x.d === 1n;\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "isInteger", "isIntegerNumber", "factory", "name", "dependencies", "createIsInteger", "_ref", "typed", "number", "BigNumber", "x", "isInt", "bigint", "Fraction", "d", "referToSelf", "self"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/isInteger.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { isInteger as isIntegerNumber } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isInteger';\nvar dependencies = ['typed'];\nexport var createIsInteger = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is an integer number.\n   * The function supports `number`, `BigNumber`, and `Fraction`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isInteger(x)\n   *\n   * Examples:\n   *\n   *    math.isInteger(2)                     // returns true\n   *    math.isInteger(0)                     // returns true\n   *    math.isInteger(0.5)                   // returns false\n   *    math.isInteger(math.bignumber(500))   // returns true\n   *    math.isInteger(math.fraction(4))      // returns true\n   *    math.isInteger('3')                   // returns true\n   *    math.isInteger([3, 0.5, -2])          // returns [true, false, true]\n   *    math.isInteger(math.complex('2-4i'))  // throws TypeError\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isNegative, isZero\n   *\n   * @param {number | BigNumber | bigint | Fraction | Array | Matrix} x   Value to be tested\n   * @return {boolean}  Returns true when `x` contains a numeric, integer value.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: isIntegerNumber,\n    // TODO: what to do with isInteger(add(0.1, 0.2))  ?\n\n    BigNumber: function BigNumber(x) {\n      return x.isInt();\n    },\n    bigint: function bigint(x) {\n      return true;\n    },\n    Fraction: function Fraction(x) {\n      return x.d === 1n;\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,IAAIC,eAAe,QAAQ,uBAAuB;AACpE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,WAAW;AACtB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC9E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBK,MAAM,EAAEP,eAAe;IACvB;;IAEAQ,SAAS,EAAE,SAASA,SAASA,CAACC,CAAC,EAAE;MAC/B,OAAOA,CAAC,CAACC,KAAK,CAAC,CAAC;IAClB,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACF,CAAC,EAAE;MACzB,OAAO,IAAI;IACb,CAAC;IACDG,QAAQ,EAAE,SAASA,QAAQA,CAACH,CAAC,EAAE;MAC7B,OAAOA,CAAC,CAACI,CAAC,KAAK,EAAE;IACnB,CAAC;IACD,gBAAgB,EAAEP,KAAK,CAACQ,WAAW,CAACC,IAAI,IAAIN,CAAC,IAAIX,OAAO,CAACW,CAAC,EAAEM,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}