{"ast": null, "code": "import { isArray, isBig<PERSON><PERSON>ber, isCollection, isIndex, isMatrix, isNumber, isString, typeOf } from '../../utils/is.js';\nimport { isInteger } from '../../utils/number.js';\nimport { format } from '../../utils/string.js';\nimport { clone, deepStrictEqual } from '../../utils/object.js';\nimport { arraySize, getArrayDataType, processSizesWildcard, unsqueeze, validateIndex } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nimport { DimensionError } from '../../error/DimensionError.js';\nimport { optimizeCallback } from '../../utils/optimizeCallback.js';\nvar name = 'SparseMatrix';\nvar dependencies = ['typed', 'equalScalar', 'Matrix'];\nexport var createSparseMatrixClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar,\n    Matrix\n  } = _ref;\n  /**\n   * Sparse Matrix implementation. This type implements\n   * a [Compressed Column Storage](https://en.wikipedia.org/wiki/Sparse_matrix#Compressed_sparse_column_(CSC_or_CCS))\n   * format for two-dimensional sparse matrices.\n   * @class SparseMatrix\n   */\n  function SparseMatrix(data, datatype) {\n    if (!(this instanceof SparseMatrix)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n    if (datatype && !isString(datatype)) {\n      throw new Error('Invalid datatype: ' + datatype);\n    }\n    if (isMatrix(data)) {\n      // create from matrix\n      _createFromMatrix(this, data, datatype);\n    } else if (data && isArray(data.index) && isArray(data.ptr) && isArray(data.size)) {\n      // initialize fields\n      this._values = data.values;\n      this._index = data.index;\n      this._ptr = data.ptr;\n      this._size = data.size;\n      this._datatype = datatype || data.datatype;\n    } else if (isArray(data)) {\n      // create from array\n      _createFromArray(this, data, datatype);\n    } else if (data) {\n      // unsupported type\n      throw new TypeError('Unsupported type of data (' + typeOf(data) + ')');\n    } else {\n      // nothing provided\n      this._values = [];\n      this._index = [];\n      this._ptr = [0];\n      this._size = [0, 0];\n      this._datatype = datatype;\n    }\n  }\n  function _createFromMatrix(matrix, source, datatype) {\n    // check matrix type\n    if (source.type === 'SparseMatrix') {\n      // clone arrays\n      matrix._values = source._values ? clone(source._values) : undefined;\n      matrix._index = clone(source._index);\n      matrix._ptr = clone(source._ptr);\n      matrix._size = clone(source._size);\n      matrix._datatype = datatype || source._datatype;\n    } else {\n      // build from matrix data\n      _createFromArray(matrix, source.valueOf(), datatype || source._datatype);\n    }\n  }\n  function _createFromArray(matrix, data, datatype) {\n    // initialize fields\n    matrix._values = [];\n    matrix._index = [];\n    matrix._ptr = [];\n    matrix._datatype = datatype;\n    // discover rows & columns, do not use math.size() to avoid looping array twice\n    var rows = data.length;\n    var columns = 0;\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [datatype, datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, datatype);\n    }\n\n    // check we have rows (empty array)\n    if (rows > 0) {\n      // column index\n      var j = 0;\n      do {\n        // store pointer to values index\n        matrix._ptr.push(matrix._index.length);\n        // loop rows\n        for (var i = 0; i < rows; i++) {\n          // current row\n          var row = data[i];\n          // check row is an array\n          if (isArray(row)) {\n            // update columns if needed (only on first column)\n            if (j === 0 && columns < row.length) {\n              columns = row.length;\n            }\n            // check row has column\n            if (j < row.length) {\n              // value\n              var v = row[j];\n              // check value != 0\n              if (!eq(v, zero)) {\n                // store value\n                matrix._values.push(v);\n                // index\n                matrix._index.push(i);\n              }\n            }\n          } else {\n            // update columns if needed (only on first column)\n            if (j === 0 && columns < 1) {\n              columns = 1;\n            }\n            // check value != 0 (row is a scalar)\n            if (!eq(row, zero)) {\n              // store value\n              matrix._values.push(row);\n              // index\n              matrix._index.push(i);\n            }\n          }\n        }\n        // increment index\n        j++;\n      } while (j < columns);\n    }\n    // store number of values in ptr\n    matrix._ptr.push(matrix._index.length);\n    // size\n    matrix._size = [rows, columns];\n  }\n  SparseMatrix.prototype = new Matrix();\n\n  /**\n   * Create a new SparseMatrix\n   */\n  SparseMatrix.prototype.createSparseMatrix = function (data, datatype) {\n    return new SparseMatrix(data, datatype);\n  };\n\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(SparseMatrix, 'name', {\n    value: 'SparseMatrix'\n  });\n  SparseMatrix.prototype.constructor = SparseMatrix;\n  SparseMatrix.prototype.type = 'SparseMatrix';\n  SparseMatrix.prototype.isSparseMatrix = true;\n\n  /**\n   * Get the matrix type\n   *\n   * Usage:\n   *    const matrixType = matrix.getDataType()  // retrieves the matrix type\n   *\n   * @memberOf SparseMatrix\n   * @return {string}   type information; if multiple types are found from the Matrix, it will return \"mixed\"\n   */\n  SparseMatrix.prototype.getDataType = function () {\n    return getArrayDataType(this._values, typeOf);\n  };\n\n  /**\n   * Get the storage format used by the matrix.\n   *\n   * Usage:\n   *     const format = matrix.storage()   // retrieve storage format\n   *\n   * @memberof SparseMatrix\n   * @return {string}           The storage format.\n   */\n  SparseMatrix.prototype.storage = function () {\n    return 'sparse';\n  };\n\n  /**\n   * Get the datatype of the data stored in the matrix.\n   *\n   * Usage:\n   *     const format = matrix.datatype()    // retrieve matrix datatype\n   *\n   * @memberof SparseMatrix\n   * @return {string}           The datatype.\n   */\n  SparseMatrix.prototype.datatype = function () {\n    return this._datatype;\n  };\n\n  /**\n   * Create a new SparseMatrix\n   * @memberof SparseMatrix\n   * @param {Array} data\n   * @param {string} [datatype]\n   */\n  SparseMatrix.prototype.create = function (data, datatype) {\n    return new SparseMatrix(data, datatype);\n  };\n\n  /**\n   * Get the matrix density.\n   *\n   * Usage:\n   *     const density = matrix.density()                   // retrieve matrix density\n   *\n   * @memberof SparseMatrix\n   * @return {number}           The matrix density.\n   */\n  SparseMatrix.prototype.density = function () {\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    // calculate density\n    return rows !== 0 && columns !== 0 ? this._index.length / (rows * columns) : 0;\n  };\n\n  /**\n   * Get a subset of the matrix, or replace a subset of the matrix.\n   *\n   * Usage:\n   *     const subset = matrix.subset(index)               // retrieve subset\n   *     const value = matrix.subset(index, replacement)   // replace subset\n   *\n   * @memberof SparseMatrix\n   * @param {Index} index\n   * @param {Array | Matrix | *} [replacement]\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be filled with zeros.\n   */\n  SparseMatrix.prototype.subset = function (index, replacement, defaultValue) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke subset on a Pattern only matrix');\n    }\n\n    // check arguments\n    switch (arguments.length) {\n      case 1:\n        return _getsubset(this, index);\n\n      // intentional fall through\n      case 2:\n      case 3:\n        return _setsubset(this, index, replacement, defaultValue);\n      default:\n        throw new SyntaxError('Wrong number of arguments');\n    }\n  };\n  function _getsubset(matrix, idx) {\n    // check idx\n    if (!isIndex(idx)) {\n      throw new TypeError('Invalid index');\n    }\n    var isScalar = idx.isScalar();\n    if (isScalar) {\n      // return a scalar\n      return matrix.get(idx.min());\n    }\n    // validate dimensions\n    var size = idx.size();\n    if (size.length !== matrix._size.length) {\n      throw new DimensionError(size.length, matrix._size.length);\n    }\n\n    // vars\n    var i, ii, k, kk;\n\n    // validate if any of the ranges in the index is out of range\n    var min = idx.min();\n    var max = idx.max();\n    for (i = 0, ii = matrix._size.length; i < ii; i++) {\n      validateIndex(min[i], matrix._size[i]);\n      validateIndex(max[i], matrix._size[i]);\n    }\n\n    // matrix arrays\n    var mvalues = matrix._values;\n    var mindex = matrix._index;\n    var mptr = matrix._ptr;\n\n    // rows & columns dimensions for result matrix\n    var rows = idx.dimension(0);\n    var columns = idx.dimension(1);\n\n    // workspace & permutation vector\n    var w = [];\n    var pv = [];\n\n    // loop rows in resulting matrix\n    rows.forEach(function (i, r) {\n      // update permutation vector\n      pv[i] = r[0];\n      // mark i in workspace\n      w[i] = true;\n    });\n\n    // result matrix arrays\n    var values = mvalues ? [] : undefined;\n    var index = [];\n    var ptr = [];\n\n    // loop columns in result matrix\n    columns.forEach(function (j) {\n      // update ptr\n      ptr.push(index.length);\n      // loop values in column j\n      for (k = mptr[j], kk = mptr[j + 1]; k < kk; k++) {\n        // row\n        i = mindex[k];\n        // check row is in result matrix\n        if (w[i] === true) {\n          // push index\n          index.push(pv[i]);\n          // check we need to process values\n          if (values) {\n            values.push(mvalues[k]);\n          }\n        }\n      }\n    });\n    // update ptr\n    ptr.push(index.length);\n\n    // return matrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size,\n      datatype: matrix._datatype\n    });\n  }\n  function _setsubset(matrix, index, submatrix, defaultValue) {\n    // check index\n    if (!index || index.isIndex !== true) {\n      throw new TypeError('Invalid index');\n    }\n\n    // get index size and check whether the index contains a single value\n    var iSize = index.size();\n    var isScalar = index.isScalar();\n\n    // calculate the size of the submatrix, and convert it into an Array if needed\n    var sSize;\n    if (isMatrix(submatrix)) {\n      // submatrix size\n      sSize = submatrix.size();\n      // use array representation\n      submatrix = submatrix.toArray();\n    } else {\n      // get submatrix size (array, scalar)\n      sSize = arraySize(submatrix);\n    }\n\n    // check index is a scalar\n    if (isScalar) {\n      // verify submatrix is a scalar\n      if (sSize.length !== 0) {\n        throw new TypeError('Scalar expected');\n      }\n      // set value\n      matrix.set(index.min(), submatrix, defaultValue);\n    } else {\n      // validate dimensions, index size must be one or two dimensions\n      if (iSize.length !== 1 && iSize.length !== 2) {\n        throw new DimensionError(iSize.length, matrix._size.length, '<');\n      }\n\n      // check submatrix and index have the same dimensions\n      if (sSize.length < iSize.length) {\n        // calculate number of missing outer dimensions\n        var i = 0;\n        var outer = 0;\n        while (iSize[i] === 1 && sSize[i] === 1) {\n          i++;\n        }\n        while (iSize[i] === 1) {\n          outer++;\n          i++;\n        }\n        // unsqueeze both outer and inner dimensions\n        submatrix = unsqueeze(submatrix, iSize.length, outer, sSize);\n      }\n\n      // check whether the size of the submatrix matches the index size\n      if (!deepStrictEqual(iSize, sSize)) {\n        throw new DimensionError(iSize, sSize, '>');\n      }\n\n      // insert the sub matrix\n      if (iSize.length === 1) {\n        // if the replacement index only has 1 dimension, go trough each one and set its value\n        var range = index.dimension(0);\n        range.forEach(function (dataIndex, subIndex) {\n          validateIndex(dataIndex);\n          matrix.set([dataIndex, 0], submatrix[subIndex[0]], defaultValue);\n        });\n      } else {\n        // if the replacement index has 2 dimensions, go through each one and set the value in the correct index\n        var firstDimensionRange = index.dimension(0);\n        var secondDimensionRange = index.dimension(1);\n        firstDimensionRange.forEach(function (firstDataIndex, firstSubIndex) {\n          validateIndex(firstDataIndex);\n          secondDimensionRange.forEach(function (secondDataIndex, secondSubIndex) {\n            validateIndex(secondDataIndex);\n            matrix.set([firstDataIndex, secondDataIndex], submatrix[firstSubIndex[0]][secondSubIndex[0]], defaultValue);\n          });\n        });\n      }\n    }\n    return matrix;\n  }\n\n  /**\n   * Get a single element from the matrix.\n   * @memberof SparseMatrix\n   * @param {number[]} index   Zero-based index\n   * @return {*} value\n   */\n  SparseMatrix.prototype.get = function (index) {\n    if (!isArray(index)) {\n      throw new TypeError('Array expected');\n    }\n    if (index.length !== this._size.length) {\n      throw new DimensionError(index.length, this._size.length);\n    }\n\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke get on a Pattern only matrix');\n    }\n\n    // row and column\n    var i = index[0];\n    var j = index[1];\n\n    // check i, j are valid\n    validateIndex(i, this._size[0]);\n    validateIndex(j, this._size[1]);\n\n    // find value index\n    var k = _getValueIndex(i, this._ptr[j], this._ptr[j + 1], this._index);\n    // check k is prior to next column k and it is in the correct row\n    if (k < this._ptr[j + 1] && this._index[k] === i) {\n      return this._values[k];\n    }\n    return 0;\n  };\n\n  /**\n   * Replace a single element in the matrix.\n   * @memberof SparseMatrix\n   * @param {number[]} index   Zero-based index\n   * @param {*} v\n   * @param {*} [defaultValue]        Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be set to zero.\n   * @return {SparseMatrix} self\n   */\n  SparseMatrix.prototype.set = function (index, v, defaultValue) {\n    if (!isArray(index)) {\n      throw new TypeError('Array expected');\n    }\n    if (index.length !== this._size.length) {\n      throw new DimensionError(index.length, this._size.length);\n    }\n\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke set on a Pattern only matrix');\n    }\n\n    // row and column\n    var i = index[0];\n    var j = index[1];\n\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(this._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [this._datatype, this._datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, this._datatype);\n    }\n\n    // check we need to resize matrix\n    if (i > rows - 1 || j > columns - 1) {\n      // resize matrix\n      _resize(this, Math.max(i + 1, rows), Math.max(j + 1, columns), defaultValue);\n      // update rows & columns\n      rows = this._size[0];\n      columns = this._size[1];\n    }\n\n    // check i, j are valid\n    validateIndex(i, rows);\n    validateIndex(j, columns);\n\n    // find value index\n    var k = _getValueIndex(i, this._ptr[j], this._ptr[j + 1], this._index);\n    // check k is prior to next column k and it is in the correct row\n    if (k < this._ptr[j + 1] && this._index[k] === i) {\n      // check value != 0\n      if (!eq(v, zero)) {\n        // update value\n        this._values[k] = v;\n      } else {\n        // remove value from matrix\n        _remove(k, j, this._values, this._index, this._ptr);\n      }\n    } else {\n      if (!eq(v, zero)) {\n        // insert value @ (i, j)\n        _insert(k, i, j, v, this._values, this._index, this._ptr);\n      }\n    }\n    return this;\n  };\n  function _getValueIndex(i, top, bottom, index) {\n    // check row is on the bottom side\n    if (bottom - top === 0) {\n      return bottom;\n    }\n    // loop rows [top, bottom[\n    for (var r = top; r < bottom; r++) {\n      // check we found value index\n      if (index[r] === i) {\n        return r;\n      }\n    }\n    // we did not find row\n    return top;\n  }\n  function _remove(k, j, values, index, ptr) {\n    // remove value @ k\n    values.splice(k, 1);\n    index.splice(k, 1);\n    // update pointers\n    for (var x = j + 1; x < ptr.length; x++) {\n      ptr[x]--;\n    }\n  }\n  function _insert(k, i, j, v, values, index, ptr) {\n    // insert value\n    values.splice(k, 0, v);\n    // update row for k\n    index.splice(k, 0, i);\n    // update column pointers\n    for (var x = j + 1; x < ptr.length; x++) {\n      ptr[x]++;\n    }\n  }\n\n  /**\n   * Resize the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (resize in place).\n   *\n   * @memberof SparseMatrix\n   * @param {number[] | Matrix} size  The new size the matrix should have.\n   *                                  Since sparse matrices are always two-dimensional,\n   *                                  size must be two numbers in either an array or a matrix\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries.\n   *                                  If not provided, the matrix elements will\n   *                                  be filled with zeros.\n   * @param {boolean} [copy]          Return a resized copy of the matrix\n   *\n   * @return {Matrix}                 The resized matrix\n   */\n  SparseMatrix.prototype.resize = function (size, defaultValue, copy) {\n    // validate arguments\n    if (!isCollection(size)) {\n      throw new TypeError('Array or Matrix expected');\n    }\n\n    // SparseMatrix input is always 2d, flatten this into 1d if it's indeed a vector\n    var sizeArray = size.valueOf().map(value => {\n      return Array.isArray(value) && value.length === 1 ? value[0] : value;\n    });\n    if (sizeArray.length !== 2) {\n      throw new Error('Only two dimensions matrix are supported');\n    }\n\n    // check sizes\n    sizeArray.forEach(function (value) {\n      if (!isNumber(value) || !isInteger(value) || value < 0) {\n        throw new TypeError('Invalid size, must contain positive integers ' + '(size: ' + format(sizeArray) + ')');\n      }\n    });\n\n    // matrix to resize\n    var m = copy ? this.clone() : this;\n    // resize matrix\n    return _resize(m, sizeArray[0], sizeArray[1], defaultValue);\n  };\n  function _resize(matrix, rows, columns, defaultValue) {\n    // value to insert at the time of growing matrix\n    var value = defaultValue || 0;\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(matrix._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [matrix._datatype, matrix._datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, matrix._datatype);\n      // convert value to the same datatype\n      value = typed.convert(value, matrix._datatype);\n    }\n\n    // should we insert the value?\n    var ins = !eq(value, zero);\n\n    // old columns and rows\n    var r = matrix._size[0];\n    var c = matrix._size[1];\n    var i, j, k;\n\n    // check we need to increase columns\n    if (columns > c) {\n      // loop new columns\n      for (j = c; j < columns; j++) {\n        // update matrix._ptr for current column\n        matrix._ptr[j] = matrix._values.length;\n        // check we need to insert matrix._values\n        if (ins) {\n          // loop rows\n          for (i = 0; i < r; i++) {\n            // add new matrix._values\n            matrix._values.push(value);\n            // update matrix._index\n            matrix._index.push(i);\n          }\n        }\n      }\n      // store number of matrix._values in matrix._ptr\n      matrix._ptr[columns] = matrix._values.length;\n    } else if (columns < c) {\n      // truncate matrix._ptr\n      matrix._ptr.splice(columns + 1, c - columns);\n      // truncate matrix._values and matrix._index\n      matrix._values.splice(matrix._ptr[columns], matrix._values.length);\n      matrix._index.splice(matrix._ptr[columns], matrix._index.length);\n    }\n    // update columns\n    c = columns;\n\n    // check we need to increase rows\n    if (rows > r) {\n      // check we have to insert values\n      if (ins) {\n        // inserts\n        var n = 0;\n        // loop columns\n        for (j = 0; j < c; j++) {\n          // update matrix._ptr for current column\n          matrix._ptr[j] = matrix._ptr[j] + n;\n          // where to insert matrix._values\n          k = matrix._ptr[j + 1] + n;\n          // pointer\n          var p = 0;\n          // loop new rows, initialize pointer\n          for (i = r; i < rows; i++, p++) {\n            // add value\n            matrix._values.splice(k + p, 0, value);\n            // update matrix._index\n            matrix._index.splice(k + p, 0, i);\n            // increment inserts\n            n++;\n          }\n        }\n        // store number of matrix._values in matrix._ptr\n        matrix._ptr[c] = matrix._values.length;\n      }\n    } else if (rows < r) {\n      // deletes\n      var d = 0;\n      // loop columns\n      for (j = 0; j < c; j++) {\n        // update matrix._ptr for current column\n        matrix._ptr[j] = matrix._ptr[j] - d;\n        // where matrix._values start for next column\n        var k0 = matrix._ptr[j];\n        var k1 = matrix._ptr[j + 1] - d;\n        // loop matrix._index\n        for (k = k0; k < k1; k++) {\n          // row\n          i = matrix._index[k];\n          // check we need to delete value and matrix._index\n          if (i > rows - 1) {\n            // remove value\n            matrix._values.splice(k, 1);\n            // remove item from matrix._index\n            matrix._index.splice(k, 1);\n            // increase deletes\n            d++;\n          }\n        }\n      }\n      // update matrix._ptr for current column\n      matrix._ptr[j] = matrix._values.length;\n    }\n    // update matrix._size\n    matrix._size[0] = rows;\n    matrix._size[1] = columns;\n    // return matrix\n    return matrix;\n  }\n\n  /**\n   * Reshape the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (reshape in place).\n   *\n   * NOTE: This might be better suited to copy by default, instead of modifying\n   *       in place. For now, it operates in place to remain consistent with\n   *       resize().\n   *\n   * @memberof SparseMatrix\n   * @param {number[]} sizes          The new size the matrix should have.\n   *                                  Since sparse matrices are always two-dimensional,\n   *                                  size must be two numbers in either an array or a matrix\n   * @param {boolean} [copy]          Return a reshaped copy of the matrix\n   *\n   * @return {Matrix}                 The reshaped matrix\n   */\n  SparseMatrix.prototype.reshape = function (sizes, copy) {\n    // validate arguments\n    if (!isArray(sizes)) {\n      throw new TypeError('Array expected');\n    }\n    if (sizes.length !== 2) {\n      throw new Error('Sparse matrices can only be reshaped in two dimensions');\n    }\n\n    // check sizes\n    sizes.forEach(function (value) {\n      if (!isNumber(value) || !isInteger(value) || value <= -2 || value === 0) {\n        throw new TypeError('Invalid size, must contain positive integers or -1 ' + '(size: ' + format(sizes) + ')');\n      }\n    });\n    var currentLength = this._size[0] * this._size[1];\n    sizes = processSizesWildcard(sizes, currentLength);\n    var newLength = sizes[0] * sizes[1];\n\n    // m * n must not change\n    if (currentLength !== newLength) {\n      throw new Error('Reshaping sparse matrix will result in the wrong number of elements');\n    }\n\n    // matrix to reshape\n    var m = copy ? this.clone() : this;\n\n    // return unchanged if the same shape\n    if (this._size[0] === sizes[0] && this._size[1] === sizes[1]) {\n      return m;\n    }\n\n    // Convert to COO format (generate a column index)\n    var colIndex = [];\n    for (var i = 0; i < m._ptr.length; i++) {\n      for (var j = 0; j < m._ptr[i + 1] - m._ptr[i]; j++) {\n        colIndex.push(i);\n      }\n    }\n\n    // Clone the values array\n    var values = m._values.slice();\n\n    // Clone the row index array\n    var rowIndex = m._index.slice();\n\n    // Transform the (row, column) indices\n    for (var _i = 0; _i < m._index.length; _i++) {\n      var r1 = rowIndex[_i];\n      var c1 = colIndex[_i];\n      var flat = r1 * m._size[1] + c1;\n      colIndex[_i] = flat % sizes[1];\n      rowIndex[_i] = Math.floor(flat / sizes[1]);\n    }\n\n    // Now reshaping is supposed to preserve the row-major order, BUT these sparse matrices are stored\n    // in column-major order, so we have to reorder the value array now. One option is to use a multisort,\n    // sorting several arrays based on some other array.\n\n    // OR, we could easily just:\n\n    // 1. Remove all values from the matrix\n    m._values.length = 0;\n    m._index.length = 0;\n    m._ptr.length = sizes[1] + 1;\n    m._size = sizes.slice();\n    for (var _i2 = 0; _i2 < m._ptr.length; _i2++) {\n      m._ptr[_i2] = 0;\n    }\n\n    // 2. Re-insert all elements in the proper order (simplified code from SparseMatrix.prototype.set)\n    // This step is probably the most time-consuming\n    for (var h = 0; h < values.length; h++) {\n      var _i3 = rowIndex[h];\n      var _j = colIndex[h];\n      var v = values[h];\n      var k = _getValueIndex(_i3, m._ptr[_j], m._ptr[_j + 1], m._index);\n      _insert(k, _i3, _j, v, m._values, m._index, m._ptr);\n    }\n\n    // The value indices are inserted out of order, but apparently that's... still OK?\n\n    return m;\n  };\n\n  /**\n   * Create a clone of the matrix\n   * @memberof SparseMatrix\n   * @return {SparseMatrix} clone\n   */\n  SparseMatrix.prototype.clone = function () {\n    var m = new SparseMatrix({\n      values: this._values ? clone(this._values) : undefined,\n      index: clone(this._index),\n      ptr: clone(this._ptr),\n      size: clone(this._size),\n      datatype: this._datatype\n    });\n    return m;\n  };\n\n  /**\n   * Retrieve the size of the matrix.\n   * @memberof SparseMatrix\n   * @returns {number[]} size\n   */\n  SparseMatrix.prototype.size = function () {\n    return this._size.slice(0); // copy the Array\n  };\n\n  /**\n   * Create a new matrix with the results of the callback function executed on\n   * each entry of the matrix.\n   * @memberof SparseMatrix\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *\n   * @return {SparseMatrix} matrix\n   */\n  SparseMatrix.prototype.map = function (callback, skipZeros) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke map on a Pattern only matrix');\n    }\n    // matrix instance\n    var me = this;\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    var fastCallback = optimizeCallback(callback, me, 'map');\n    // invoke callback\n    var invoke = function invoke(v, i, j) {\n      // invoke callback\n      return fastCallback.fn(v, [i, j], me);\n    };\n    // invoke _map\n    return _map(this, 0, rows - 1, 0, columns - 1, invoke, skipZeros);\n  };\n\n  /**\n   * Create a new matrix with the results of the callback function executed on the interval\n   * [minRow..maxRow, minColumn..maxColumn].\n   */\n  function _map(matrix, minRow, maxRow, minColumn, maxColumn, callback, skipZeros) {\n    // result arrays\n    var values = [];\n    var index = [];\n    var ptr = [];\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(matrix._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [matrix._datatype, matrix._datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, matrix._datatype);\n    }\n\n    // invoke callback\n    var invoke = function invoke(v, x, y) {\n      // invoke callback\n      var value = callback(v, x, y);\n      // check value != 0\n      if (!eq(value, zero)) {\n        // store value\n        values.push(value);\n        // index\n        index.push(x);\n      }\n    };\n    // loop columns\n    for (var j = minColumn; j <= maxColumn; j++) {\n      // store pointer to values index\n      ptr.push(values.length);\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = matrix._ptr[j];\n      var k1 = matrix._ptr[j + 1];\n      if (skipZeros) {\n        // loop k within [k0, k1[\n        for (var k = k0; k < k1; k++) {\n          // row index\n          var i = matrix._index[k];\n          // check i is in range\n          if (i >= minRow && i <= maxRow) {\n            // value @ k\n            invoke(matrix._values[k], i - minRow, j - minColumn);\n          }\n        }\n      } else {\n        // create a cache holding all defined values\n        var _values = {};\n        for (var _k = k0; _k < k1; _k++) {\n          var _i4 = matrix._index[_k];\n          _values[_i4] = matrix._values[_k];\n        }\n\n        // loop over all rows (indexes can be unordered so we can't use that),\n        // and either read the value or zero\n        for (var _i5 = minRow; _i5 <= maxRow; _i5++) {\n          var value = _i5 in _values ? _values[_i5] : 0;\n          invoke(value, _i5 - minRow, j - minColumn);\n        }\n      }\n    }\n\n    // store number of values in ptr\n    ptr.push(values.length);\n    // return sparse matrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [maxRow - minRow + 1, maxColumn - minColumn + 1]\n    });\n  }\n\n  /**\n   * Execute a callback function on each entry of the matrix.\n   * @memberof SparseMatrix\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *                              If false, the indices are guaranteed to be in order,\n   *                              if true, the indices can be unordered.\n   */\n  SparseMatrix.prototype.forEach = function (callback, skipZeros) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke forEach on a Pattern only matrix');\n    }\n    // matrix instance\n    var me = this;\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    var fastCallback = optimizeCallback(callback, me, 'forEach');\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      if (skipZeros) {\n        // loop k within [k0, k1[\n        for (var k = k0; k < k1; k++) {\n          // row index\n          var i = this._index[k];\n\n          // value @ k\n          // TODO apply a non indexed version of algorithm in case fastCallback is not optimized\n          fastCallback.fn(this._values[k], [i, j], me);\n        }\n      } else {\n        // create a cache holding all defined values\n        var values = {};\n        for (var _k2 = k0; _k2 < k1; _k2++) {\n          var _i6 = this._index[_k2];\n          values[_i6] = this._values[_k2];\n        }\n\n        // loop over all rows (indexes can be unordered so we can't use that),\n        // and either read the value or zero\n        for (var _i7 = 0; _i7 < rows; _i7++) {\n          var value = _i7 in values ? values[_i7] : 0;\n          fastCallback.fn(value, [_i7, j], me);\n        }\n      }\n    }\n  };\n\n  /**\n   * Iterate over the matrix elements, skipping zeros\n   * @return {Iterable<{ value, index: number[] }>}\n   */\n  SparseMatrix.prototype[Symbol.iterator] = function* () {\n    if (!this._values) {\n      throw new Error('Cannot iterate a Pattern only matrix');\n    }\n    var columns = this._size[1];\n    for (var j = 0; j < columns; j++) {\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      for (var k = k0; k < k1; k++) {\n        // row index\n        var i = this._index[k];\n        yield {\n          value: this._values[k],\n          index: [i, j]\n        };\n      }\n    }\n  };\n\n  /**\n   * Create an Array with a copy of the data of the SparseMatrix\n   * @memberof SparseMatrix\n   * @returns {Array} array\n   */\n  SparseMatrix.prototype.toArray = function () {\n    return _toArray(this._values, this._index, this._ptr, this._size, true);\n  };\n\n  /**\n   * Get the primitive value of the SparseMatrix: a two dimensions array\n   * @memberof SparseMatrix\n   * @returns {Array} array\n   */\n  SparseMatrix.prototype.valueOf = function () {\n    return _toArray(this._values, this._index, this._ptr, this._size, false);\n  };\n  function _toArray(values, index, ptr, size, copy) {\n    // rows and columns\n    var rows = size[0];\n    var columns = size[1];\n    // result\n    var a = [];\n    // vars\n    var i, j;\n    // initialize array\n    for (i = 0; i < rows; i++) {\n      a[i] = [];\n      for (j = 0; j < columns; j++) {\n        a[i][j] = 0;\n      }\n    }\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = ptr[j];\n      var k1 = ptr[j + 1];\n      // loop k within [k0, k1[\n      for (var k = k0; k < k1; k++) {\n        // row index\n        i = index[k];\n        // set value (use one for pattern matrix)\n        a[i][j] = values ? copy ? clone(values[k]) : values[k] : 1;\n      }\n    }\n    return a;\n  }\n\n  /**\n   * Get a string representation of the matrix, with optional formatting options.\n   * @memberof SparseMatrix\n   * @param {Object | number | Function} [options]  Formatting options. See\n   *                                                lib/utils/number:format for a\n   *                                                description of the available\n   *                                                options.\n   * @returns {string} str\n   */\n  SparseMatrix.prototype.format = function (options) {\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    // density\n    var density = this.density();\n    // rows & columns\n    var str = 'Sparse Matrix [' + format(rows, options) + ' x ' + format(columns, options) + '] density: ' + format(density, options) + '\\n';\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      // loop k within [k0, k1[\n      for (var k = k0; k < k1; k++) {\n        // row index\n        var i = this._index[k];\n        // append value\n        str += '\\n    (' + format(i, options) + ', ' + format(j, options) + ') ==> ' + (this._values ? format(this._values[k], options) : 'X');\n      }\n    }\n    return str;\n  };\n\n  /**\n   * Get a string representation of the matrix\n   * @memberof SparseMatrix\n   * @returns {string} str\n   */\n  SparseMatrix.prototype.toString = function () {\n    return format(this.toArray());\n  };\n\n  /**\n   * Get a JSON representation of the matrix\n   * @memberof SparseMatrix\n   * @returns {Object}\n   */\n  SparseMatrix.prototype.toJSON = function () {\n    return {\n      mathjs: 'SparseMatrix',\n      values: this._values,\n      index: this._index,\n      ptr: this._ptr,\n      size: this._size,\n      datatype: this._datatype\n    };\n  };\n\n  /**\n   * Get the kth Matrix diagonal.\n   *\n   * @memberof SparseMatrix\n   * @param {number | BigNumber} [k=0]     The kth diagonal where the vector will retrieved.\n   *\n   * @returns {Matrix}                     The matrix vector with the diagonal values.\n   */\n  SparseMatrix.prototype.diagonal = function (k) {\n    // validate k if any\n    if (k) {\n      // convert BigNumber to a number\n      if (isBigNumber(k)) {\n        k = k.toNumber();\n      }\n      // is must be an integer\n      if (!isNumber(k) || !isInteger(k)) {\n        throw new TypeError('The parameter k must be an integer number');\n      }\n    } else {\n      // default value\n      k = 0;\n    }\n    var kSuper = k > 0 ? k : 0;\n    var kSub = k < 0 ? -k : 0;\n\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n\n    // number diagonal values\n    var n = Math.min(rows - kSub, columns - kSuper);\n\n    // diagonal arrays\n    var values = [];\n    var index = [];\n    var ptr = [];\n    // initial ptr value\n    ptr[0] = 0;\n    // loop columns\n    for (var j = kSuper; j < columns && values.length < n; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      // loop x within [k0, k1[\n      for (var x = k0; x < k1; x++) {\n        // row index\n        var i = this._index[x];\n        // check row\n        if (i === j - kSuper + kSub) {\n          // value on this column\n          values.push(this._values[x]);\n          // store row\n          index[values.length - 1] = i - kSub;\n          // exit loop\n          break;\n        }\n      }\n    }\n    // close ptr\n    ptr.push(values.length);\n    // return matrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [n, 1]\n    });\n  };\n\n  /**\n   * Generate a matrix from a JSON object\n   * @memberof SparseMatrix\n   * @param {Object} json  An object structured like\n   *                       `{\"mathjs\": \"SparseMatrix\", \"values\": [], \"index\": [], \"ptr\": [], \"size\": []}`,\n   *                       where mathjs is optional\n   * @returns {SparseMatrix}\n   */\n  SparseMatrix.fromJSON = function (json) {\n    return new SparseMatrix(json);\n  };\n\n  /**\n   * Create a diagonal matrix.\n   *\n   * @memberof SparseMatrix\n   * @param {Array} size                       The matrix size.\n   * @param {number | Array | Matrix } value   The values for the diagonal.\n   * @param {number | BigNumber} [k=0]         The kth diagonal where the vector will be filled in.\n   * @param {number} [defaultValue]            The default value for non-diagonal\n   * @param {string} [datatype]                The Matrix datatype, values must be of this datatype.\n   *\n   * @returns {SparseMatrix}\n   */\n  SparseMatrix.diagonal = function (size, value, k, defaultValue, datatype) {\n    if (!isArray(size)) {\n      throw new TypeError('Array expected, size parameter');\n    }\n    if (size.length !== 2) {\n      throw new Error('Only two dimensions matrix are supported');\n    }\n\n    // map size & validate\n    size = size.map(function (s) {\n      // check it is a big number\n      if (isBigNumber(s)) {\n        // convert it\n        s = s.toNumber();\n      }\n      // validate arguments\n      if (!isNumber(s) || !isInteger(s) || s < 1) {\n        throw new Error('Size values must be positive integers');\n      }\n      return s;\n    });\n\n    // validate k if any\n    if (k) {\n      // convert BigNumber to a number\n      if (isBigNumber(k)) {\n        k = k.toNumber();\n      }\n      // is must be an integer\n      if (!isNumber(k) || !isInteger(k)) {\n        throw new TypeError('The parameter k must be an integer number');\n      }\n    } else {\n      // default value\n      k = 0;\n    }\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [datatype, datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, datatype);\n    }\n    var kSuper = k > 0 ? k : 0;\n    var kSub = k < 0 ? -k : 0;\n\n    // rows and columns\n    var rows = size[0];\n    var columns = size[1];\n\n    // number of non-zero items\n    var n = Math.min(rows - kSub, columns - kSuper);\n\n    // value extraction function\n    var _value;\n\n    // check value\n    if (isArray(value)) {\n      // validate array\n      if (value.length !== n) {\n        // number of values in array must be n\n        throw new Error('Invalid value array length');\n      }\n      // define function\n      _value = function _value(i) {\n        // return value @ i\n        return value[i];\n      };\n    } else if (isMatrix(value)) {\n      // matrix size\n      var ms = value.size();\n      // validate matrix\n      if (ms.length !== 1 || ms[0] !== n) {\n        // number of values in array must be n\n        throw new Error('Invalid matrix length');\n      }\n      // define function\n      _value = function _value(i) {\n        // return value @ i\n        return value.get([i]);\n      };\n    } else {\n      // define function\n      _value = function _value() {\n        // return value\n        return value;\n      };\n    }\n\n    // create arrays\n    var values = [];\n    var index = [];\n    var ptr = [];\n\n    // loop items\n    for (var j = 0; j < columns; j++) {\n      // number of rows with value\n      ptr.push(values.length);\n      // diagonal index\n      var i = j - kSuper;\n      // check we need to set diagonal value\n      if (i >= 0 && i < n) {\n        // get value @ i\n        var v = _value(i);\n        // check for zero\n        if (!eq(v, zero)) {\n          // column\n          index.push(i + kSub);\n          // add value\n          values.push(v);\n        }\n      }\n    }\n    // last value should be number of values\n    ptr.push(values.length);\n    // create SparseMatrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [rows, columns]\n    });\n  };\n\n  /**\n   * Swap rows i and j in Matrix.\n   *\n   * @memberof SparseMatrix\n   * @param {number} i       Matrix row index 1\n   * @param {number} j       Matrix row index 2\n   *\n   * @return {Matrix}        The matrix reference\n   */\n  SparseMatrix.prototype.swapRows = function (i, j) {\n    // check index\n    if (!isNumber(i) || !isInteger(i) || !isNumber(j) || !isInteger(j)) {\n      throw new Error('Row index must be positive integers');\n    }\n    // check dimensions\n    if (this._size.length !== 2) {\n      throw new Error('Only two dimensional matrix is supported');\n    }\n    // validate index\n    validateIndex(i, this._size[0]);\n    validateIndex(j, this._size[0]);\n\n    // swap rows\n    SparseMatrix._swapRows(i, j, this._size[1], this._values, this._index, this._ptr);\n    // return current instance\n    return this;\n  };\n\n  /**\n   * Loop rows with data in column j.\n   *\n   * @param {number} j            Column\n   * @param {Array} values        Matrix values\n   * @param {Array} index         Matrix row indeces\n   * @param {Array} ptr           Matrix column pointers\n   * @param {Function} callback   Callback function invoked for every row in column j\n   */\n  SparseMatrix._forEachRow = function (j, values, index, ptr, callback) {\n    // indeces for column j\n    var k0 = ptr[j];\n    var k1 = ptr[j + 1];\n\n    // loop\n    for (var k = k0; k < k1; k++) {\n      // invoke callback\n      callback(index[k], values[k]);\n    }\n  };\n\n  /**\n   * Swap rows x and y in Sparse Matrix data structures.\n   *\n   * @param {number} x         Matrix row index 1\n   * @param {number} y         Matrix row index 2\n   * @param {number} columns   Number of columns in matrix\n   * @param {Array} values     Matrix values\n   * @param {Array} index      Matrix row indeces\n   * @param {Array} ptr        Matrix column pointers\n   */\n  SparseMatrix._swapRows = function (x, y, columns, values, index, ptr) {\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = ptr[j];\n      var k1 = ptr[j + 1];\n      // find value index @ x\n      var kx = _getValueIndex(x, k0, k1, index);\n      // find value index @ x\n      var ky = _getValueIndex(y, k0, k1, index);\n      // check both rows exist in matrix\n      if (kx < k1 && ky < k1 && index[kx] === x && index[ky] === y) {\n        // swap values (check for pattern matrix)\n        if (values) {\n          var v = values[kx];\n          values[kx] = values[ky];\n          values[ky] = v;\n        }\n        // next column\n        continue;\n      }\n      // check x row exist & no y row\n      if (kx < k1 && index[kx] === x && (ky >= k1 || index[ky] !== y)) {\n        // value @ x (check for pattern matrix)\n        var vx = values ? values[kx] : undefined;\n        // insert value @ y\n        index.splice(ky, 0, y);\n        if (values) {\n          values.splice(ky, 0, vx);\n        }\n        // remove value @ x (adjust array index if needed)\n        index.splice(ky <= kx ? kx + 1 : kx, 1);\n        if (values) {\n          values.splice(ky <= kx ? kx + 1 : kx, 1);\n        }\n        // next column\n        continue;\n      }\n      // check y row exist & no x row\n      if (ky < k1 && index[ky] === y && (kx >= k1 || index[kx] !== x)) {\n        // value @ y (check for pattern matrix)\n        var vy = values ? values[ky] : undefined;\n        // insert value @ x\n        index.splice(kx, 0, x);\n        if (values) {\n          values.splice(kx, 0, vy);\n        }\n        // remove value @ y (adjust array index if needed)\n        index.splice(kx <= ky ? ky + 1 : ky, 1);\n        if (values) {\n          values.splice(kx <= ky ? ky + 1 : ky, 1);\n        }\n      }\n    }\n  };\n  return SparseMatrix;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["isArray", "isBigNumber", "isCollection", "isIndex", "isMatrix", "isNumber", "isString", "typeOf", "isInteger", "format", "clone", "deepStrictEqual", "arraySize", "getArrayDataType", "processSizesWildcard", "unsqueeze", "validateIndex", "factory", "DimensionError", "optimizeCallback", "name", "dependencies", "createSparseMatrixClass", "_ref", "typed", "equalScalar", "Matrix", "SparseMatrix", "data", "datatype", "SyntaxError", "Error", "_createFromMatrix", "index", "ptr", "size", "_values", "values", "_index", "_ptr", "_size", "_datatype", "_createFromArray", "TypeError", "matrix", "source", "type", "undefined", "valueOf", "rows", "length", "columns", "eq", "zero", "find", "convert", "j", "push", "i", "row", "v", "prototype", "createSparseMatrix", "Object", "defineProperty", "value", "constructor", "isSparseMatrix", "getDataType", "storage", "create", "density", "subset", "replacement", "defaultValue", "arguments", "_getsubset", "_setsubset", "idx", "isScalar", "get", "min", "ii", "k", "kk", "max", "mvalues", "mindex", "mptr", "dimension", "w", "pv", "for<PERSON>ach", "r", "submatrix", "iSize", "sSize", "toArray", "set", "outer", "range", "dataIndex", "subIndex", "firstDimensionRange", "secondDimensionRange", "firstDataIndex", "firstSubIndex", "secondDataIndex", "secondSubIndex", "_getValueIndex", "_resize", "Math", "_remove", "_insert", "top", "bottom", "splice", "x", "resize", "copy", "sizeArray", "map", "Array", "m", "ins", "c", "n", "p", "d", "k0", "k1", "reshape", "sizes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "colIndex", "slice", "rowIndex", "_i", "r1", "c1", "flat", "floor", "_i2", "h", "_i3", "_j", "callback", "skipZ<PERSON><PERSON>", "me", "fastCallback", "invoke", "fn", "_map", "minRow", "maxRow", "minColumn", "maxColumn", "y", "_k", "_i4", "_i5", "_k2", "_i6", "_i7", "Symbol", "iterator", "_toArray", "a", "options", "str", "toString", "toJSON", "mathjs", "diagonal", "toNumber", "kSuper", "kSub", "fromJSON", "json", "s", "_value", "ms", "swapRows", "_swapRows", "_forEachRow", "kx", "ky", "vx", "vy", "isClass"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/SparseMatrix.js"], "sourcesContent": ["import { isArray, isBig<PERSON><PERSON>ber, isCollection, isIndex, isMatrix, isNumber, isString, typeOf } from '../../utils/is.js';\nimport { isInteger } from '../../utils/number.js';\nimport { format } from '../../utils/string.js';\nimport { clone, deepStrictEqual } from '../../utils/object.js';\nimport { arraySize, getArrayDataType, processSizesWildcard, unsqueeze, validateIndex } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nimport { DimensionError } from '../../error/DimensionError.js';\nimport { optimizeCallback } from '../../utils/optimizeCallback.js';\nvar name = 'SparseMatrix';\nvar dependencies = ['typed', 'equalScalar', 'Matrix'];\nexport var createSparseMatrixClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar,\n    Matrix\n  } = _ref;\n  /**\n   * Sparse Matrix implementation. This type implements\n   * a [Compressed Column Storage](https://en.wikipedia.org/wiki/Sparse_matrix#Compressed_sparse_column_(CSC_or_CCS))\n   * format for two-dimensional sparse matrices.\n   * @class SparseMatrix\n   */\n  function SparseMatrix(data, datatype) {\n    if (!(this instanceof SparseMatrix)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n    if (datatype && !isString(datatype)) {\n      throw new Error('Invalid datatype: ' + datatype);\n    }\n    if (isMatrix(data)) {\n      // create from matrix\n      _createFromMatrix(this, data, datatype);\n    } else if (data && isArray(data.index) && isArray(data.ptr) && isArray(data.size)) {\n      // initialize fields\n      this._values = data.values;\n      this._index = data.index;\n      this._ptr = data.ptr;\n      this._size = data.size;\n      this._datatype = datatype || data.datatype;\n    } else if (isArray(data)) {\n      // create from array\n      _createFromArray(this, data, datatype);\n    } else if (data) {\n      // unsupported type\n      throw new TypeError('Unsupported type of data (' + typeOf(data) + ')');\n    } else {\n      // nothing provided\n      this._values = [];\n      this._index = [];\n      this._ptr = [0];\n      this._size = [0, 0];\n      this._datatype = datatype;\n    }\n  }\n  function _createFromMatrix(matrix, source, datatype) {\n    // check matrix type\n    if (source.type === 'SparseMatrix') {\n      // clone arrays\n      matrix._values = source._values ? clone(source._values) : undefined;\n      matrix._index = clone(source._index);\n      matrix._ptr = clone(source._ptr);\n      matrix._size = clone(source._size);\n      matrix._datatype = datatype || source._datatype;\n    } else {\n      // build from matrix data\n      _createFromArray(matrix, source.valueOf(), datatype || source._datatype);\n    }\n  }\n  function _createFromArray(matrix, data, datatype) {\n    // initialize fields\n    matrix._values = [];\n    matrix._index = [];\n    matrix._ptr = [];\n    matrix._datatype = datatype;\n    // discover rows & columns, do not use math.size() to avoid looping array twice\n    var rows = data.length;\n    var columns = 0;\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [datatype, datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, datatype);\n    }\n\n    // check we have rows (empty array)\n    if (rows > 0) {\n      // column index\n      var j = 0;\n      do {\n        // store pointer to values index\n        matrix._ptr.push(matrix._index.length);\n        // loop rows\n        for (var i = 0; i < rows; i++) {\n          // current row\n          var row = data[i];\n          // check row is an array\n          if (isArray(row)) {\n            // update columns if needed (only on first column)\n            if (j === 0 && columns < row.length) {\n              columns = row.length;\n            }\n            // check row has column\n            if (j < row.length) {\n              // value\n              var v = row[j];\n              // check value != 0\n              if (!eq(v, zero)) {\n                // store value\n                matrix._values.push(v);\n                // index\n                matrix._index.push(i);\n              }\n            }\n          } else {\n            // update columns if needed (only on first column)\n            if (j === 0 && columns < 1) {\n              columns = 1;\n            }\n            // check value != 0 (row is a scalar)\n            if (!eq(row, zero)) {\n              // store value\n              matrix._values.push(row);\n              // index\n              matrix._index.push(i);\n            }\n          }\n        }\n        // increment index\n        j++;\n      } while (j < columns);\n    }\n    // store number of values in ptr\n    matrix._ptr.push(matrix._index.length);\n    // size\n    matrix._size = [rows, columns];\n  }\n  SparseMatrix.prototype = new Matrix();\n\n  /**\n   * Create a new SparseMatrix\n   */\n  SparseMatrix.prototype.createSparseMatrix = function (data, datatype) {\n    return new SparseMatrix(data, datatype);\n  };\n\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(SparseMatrix, 'name', {\n    value: 'SparseMatrix'\n  });\n  SparseMatrix.prototype.constructor = SparseMatrix;\n  SparseMatrix.prototype.type = 'SparseMatrix';\n  SparseMatrix.prototype.isSparseMatrix = true;\n\n  /**\n   * Get the matrix type\n   *\n   * Usage:\n   *    const matrixType = matrix.getDataType()  // retrieves the matrix type\n   *\n   * @memberOf SparseMatrix\n   * @return {string}   type information; if multiple types are found from the Matrix, it will return \"mixed\"\n   */\n  SparseMatrix.prototype.getDataType = function () {\n    return getArrayDataType(this._values, typeOf);\n  };\n\n  /**\n   * Get the storage format used by the matrix.\n   *\n   * Usage:\n   *     const format = matrix.storage()   // retrieve storage format\n   *\n   * @memberof SparseMatrix\n   * @return {string}           The storage format.\n   */\n  SparseMatrix.prototype.storage = function () {\n    return 'sparse';\n  };\n\n  /**\n   * Get the datatype of the data stored in the matrix.\n   *\n   * Usage:\n   *     const format = matrix.datatype()    // retrieve matrix datatype\n   *\n   * @memberof SparseMatrix\n   * @return {string}           The datatype.\n   */\n  SparseMatrix.prototype.datatype = function () {\n    return this._datatype;\n  };\n\n  /**\n   * Create a new SparseMatrix\n   * @memberof SparseMatrix\n   * @param {Array} data\n   * @param {string} [datatype]\n   */\n  SparseMatrix.prototype.create = function (data, datatype) {\n    return new SparseMatrix(data, datatype);\n  };\n\n  /**\n   * Get the matrix density.\n   *\n   * Usage:\n   *     const density = matrix.density()                   // retrieve matrix density\n   *\n   * @memberof SparseMatrix\n   * @return {number}           The matrix density.\n   */\n  SparseMatrix.prototype.density = function () {\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    // calculate density\n    return rows !== 0 && columns !== 0 ? this._index.length / (rows * columns) : 0;\n  };\n\n  /**\n   * Get a subset of the matrix, or replace a subset of the matrix.\n   *\n   * Usage:\n   *     const subset = matrix.subset(index)               // retrieve subset\n   *     const value = matrix.subset(index, replacement)   // replace subset\n   *\n   * @memberof SparseMatrix\n   * @param {Index} index\n   * @param {Array | Matrix | *} [replacement]\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be filled with zeros.\n   */\n  SparseMatrix.prototype.subset = function (index, replacement, defaultValue) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke subset on a Pattern only matrix');\n    }\n\n    // check arguments\n    switch (arguments.length) {\n      case 1:\n        return _getsubset(this, index);\n\n      // intentional fall through\n      case 2:\n      case 3:\n        return _setsubset(this, index, replacement, defaultValue);\n      default:\n        throw new SyntaxError('Wrong number of arguments');\n    }\n  };\n  function _getsubset(matrix, idx) {\n    // check idx\n    if (!isIndex(idx)) {\n      throw new TypeError('Invalid index');\n    }\n    var isScalar = idx.isScalar();\n    if (isScalar) {\n      // return a scalar\n      return matrix.get(idx.min());\n    }\n    // validate dimensions\n    var size = idx.size();\n    if (size.length !== matrix._size.length) {\n      throw new DimensionError(size.length, matrix._size.length);\n    }\n\n    // vars\n    var i, ii, k, kk;\n\n    // validate if any of the ranges in the index is out of range\n    var min = idx.min();\n    var max = idx.max();\n    for (i = 0, ii = matrix._size.length; i < ii; i++) {\n      validateIndex(min[i], matrix._size[i]);\n      validateIndex(max[i], matrix._size[i]);\n    }\n\n    // matrix arrays\n    var mvalues = matrix._values;\n    var mindex = matrix._index;\n    var mptr = matrix._ptr;\n\n    // rows & columns dimensions for result matrix\n    var rows = idx.dimension(0);\n    var columns = idx.dimension(1);\n\n    // workspace & permutation vector\n    var w = [];\n    var pv = [];\n\n    // loop rows in resulting matrix\n    rows.forEach(function (i, r) {\n      // update permutation vector\n      pv[i] = r[0];\n      // mark i in workspace\n      w[i] = true;\n    });\n\n    // result matrix arrays\n    var values = mvalues ? [] : undefined;\n    var index = [];\n    var ptr = [];\n\n    // loop columns in result matrix\n    columns.forEach(function (j) {\n      // update ptr\n      ptr.push(index.length);\n      // loop values in column j\n      for (k = mptr[j], kk = mptr[j + 1]; k < kk; k++) {\n        // row\n        i = mindex[k];\n        // check row is in result matrix\n        if (w[i] === true) {\n          // push index\n          index.push(pv[i]);\n          // check we need to process values\n          if (values) {\n            values.push(mvalues[k]);\n          }\n        }\n      }\n    });\n    // update ptr\n    ptr.push(index.length);\n\n    // return matrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size,\n      datatype: matrix._datatype\n    });\n  }\n  function _setsubset(matrix, index, submatrix, defaultValue) {\n    // check index\n    if (!index || index.isIndex !== true) {\n      throw new TypeError('Invalid index');\n    }\n\n    // get index size and check whether the index contains a single value\n    var iSize = index.size();\n    var isScalar = index.isScalar();\n\n    // calculate the size of the submatrix, and convert it into an Array if needed\n    var sSize;\n    if (isMatrix(submatrix)) {\n      // submatrix size\n      sSize = submatrix.size();\n      // use array representation\n      submatrix = submatrix.toArray();\n    } else {\n      // get submatrix size (array, scalar)\n      sSize = arraySize(submatrix);\n    }\n\n    // check index is a scalar\n    if (isScalar) {\n      // verify submatrix is a scalar\n      if (sSize.length !== 0) {\n        throw new TypeError('Scalar expected');\n      }\n      // set value\n      matrix.set(index.min(), submatrix, defaultValue);\n    } else {\n      // validate dimensions, index size must be one or two dimensions\n      if (iSize.length !== 1 && iSize.length !== 2) {\n        throw new DimensionError(iSize.length, matrix._size.length, '<');\n      }\n\n      // check submatrix and index have the same dimensions\n      if (sSize.length < iSize.length) {\n        // calculate number of missing outer dimensions\n        var i = 0;\n        var outer = 0;\n        while (iSize[i] === 1 && sSize[i] === 1) {\n          i++;\n        }\n        while (iSize[i] === 1) {\n          outer++;\n          i++;\n        }\n        // unsqueeze both outer and inner dimensions\n        submatrix = unsqueeze(submatrix, iSize.length, outer, sSize);\n      }\n\n      // check whether the size of the submatrix matches the index size\n      if (!deepStrictEqual(iSize, sSize)) {\n        throw new DimensionError(iSize, sSize, '>');\n      }\n\n      // insert the sub matrix\n      if (iSize.length === 1) {\n        // if the replacement index only has 1 dimension, go trough each one and set its value\n        var range = index.dimension(0);\n        range.forEach(function (dataIndex, subIndex) {\n          validateIndex(dataIndex);\n          matrix.set([dataIndex, 0], submatrix[subIndex[0]], defaultValue);\n        });\n      } else {\n        // if the replacement index has 2 dimensions, go through each one and set the value in the correct index\n        var firstDimensionRange = index.dimension(0);\n        var secondDimensionRange = index.dimension(1);\n        firstDimensionRange.forEach(function (firstDataIndex, firstSubIndex) {\n          validateIndex(firstDataIndex);\n          secondDimensionRange.forEach(function (secondDataIndex, secondSubIndex) {\n            validateIndex(secondDataIndex);\n            matrix.set([firstDataIndex, secondDataIndex], submatrix[firstSubIndex[0]][secondSubIndex[0]], defaultValue);\n          });\n        });\n      }\n    }\n    return matrix;\n  }\n\n  /**\n   * Get a single element from the matrix.\n   * @memberof SparseMatrix\n   * @param {number[]} index   Zero-based index\n   * @return {*} value\n   */\n  SparseMatrix.prototype.get = function (index) {\n    if (!isArray(index)) {\n      throw new TypeError('Array expected');\n    }\n    if (index.length !== this._size.length) {\n      throw new DimensionError(index.length, this._size.length);\n    }\n\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke get on a Pattern only matrix');\n    }\n\n    // row and column\n    var i = index[0];\n    var j = index[1];\n\n    // check i, j are valid\n    validateIndex(i, this._size[0]);\n    validateIndex(j, this._size[1]);\n\n    // find value index\n    var k = _getValueIndex(i, this._ptr[j], this._ptr[j + 1], this._index);\n    // check k is prior to next column k and it is in the correct row\n    if (k < this._ptr[j + 1] && this._index[k] === i) {\n      return this._values[k];\n    }\n    return 0;\n  };\n\n  /**\n   * Replace a single element in the matrix.\n   * @memberof SparseMatrix\n   * @param {number[]} index   Zero-based index\n   * @param {*} v\n   * @param {*} [defaultValue]        Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be set to zero.\n   * @return {SparseMatrix} self\n   */\n  SparseMatrix.prototype.set = function (index, v, defaultValue) {\n    if (!isArray(index)) {\n      throw new TypeError('Array expected');\n    }\n    if (index.length !== this._size.length) {\n      throw new DimensionError(index.length, this._size.length);\n    }\n\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke set on a Pattern only matrix');\n    }\n\n    // row and column\n    var i = index[0];\n    var j = index[1];\n\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(this._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [this._datatype, this._datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, this._datatype);\n    }\n\n    // check we need to resize matrix\n    if (i > rows - 1 || j > columns - 1) {\n      // resize matrix\n      _resize(this, Math.max(i + 1, rows), Math.max(j + 1, columns), defaultValue);\n      // update rows & columns\n      rows = this._size[0];\n      columns = this._size[1];\n    }\n\n    // check i, j are valid\n    validateIndex(i, rows);\n    validateIndex(j, columns);\n\n    // find value index\n    var k = _getValueIndex(i, this._ptr[j], this._ptr[j + 1], this._index);\n    // check k is prior to next column k and it is in the correct row\n    if (k < this._ptr[j + 1] && this._index[k] === i) {\n      // check value != 0\n      if (!eq(v, zero)) {\n        // update value\n        this._values[k] = v;\n      } else {\n        // remove value from matrix\n        _remove(k, j, this._values, this._index, this._ptr);\n      }\n    } else {\n      if (!eq(v, zero)) {\n        // insert value @ (i, j)\n        _insert(k, i, j, v, this._values, this._index, this._ptr);\n      }\n    }\n    return this;\n  };\n  function _getValueIndex(i, top, bottom, index) {\n    // check row is on the bottom side\n    if (bottom - top === 0) {\n      return bottom;\n    }\n    // loop rows [top, bottom[\n    for (var r = top; r < bottom; r++) {\n      // check we found value index\n      if (index[r] === i) {\n        return r;\n      }\n    }\n    // we did not find row\n    return top;\n  }\n  function _remove(k, j, values, index, ptr) {\n    // remove value @ k\n    values.splice(k, 1);\n    index.splice(k, 1);\n    // update pointers\n    for (var x = j + 1; x < ptr.length; x++) {\n      ptr[x]--;\n    }\n  }\n  function _insert(k, i, j, v, values, index, ptr) {\n    // insert value\n    values.splice(k, 0, v);\n    // update row for k\n    index.splice(k, 0, i);\n    // update column pointers\n    for (var x = j + 1; x < ptr.length; x++) {\n      ptr[x]++;\n    }\n  }\n\n  /**\n   * Resize the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (resize in place).\n   *\n   * @memberof SparseMatrix\n   * @param {number[] | Matrix} size  The new size the matrix should have.\n   *                                  Since sparse matrices are always two-dimensional,\n   *                                  size must be two numbers in either an array or a matrix\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries.\n   *                                  If not provided, the matrix elements will\n   *                                  be filled with zeros.\n   * @param {boolean} [copy]          Return a resized copy of the matrix\n   *\n   * @return {Matrix}                 The resized matrix\n   */\n  SparseMatrix.prototype.resize = function (size, defaultValue, copy) {\n    // validate arguments\n    if (!isCollection(size)) {\n      throw new TypeError('Array or Matrix expected');\n    }\n\n    // SparseMatrix input is always 2d, flatten this into 1d if it's indeed a vector\n    var sizeArray = size.valueOf().map(value => {\n      return Array.isArray(value) && value.length === 1 ? value[0] : value;\n    });\n    if (sizeArray.length !== 2) {\n      throw new Error('Only two dimensions matrix are supported');\n    }\n\n    // check sizes\n    sizeArray.forEach(function (value) {\n      if (!isNumber(value) || !isInteger(value) || value < 0) {\n        throw new TypeError('Invalid size, must contain positive integers ' + '(size: ' + format(sizeArray) + ')');\n      }\n    });\n\n    // matrix to resize\n    var m = copy ? this.clone() : this;\n    // resize matrix\n    return _resize(m, sizeArray[0], sizeArray[1], defaultValue);\n  };\n  function _resize(matrix, rows, columns, defaultValue) {\n    // value to insert at the time of growing matrix\n    var value = defaultValue || 0;\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(matrix._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [matrix._datatype, matrix._datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, matrix._datatype);\n      // convert value to the same datatype\n      value = typed.convert(value, matrix._datatype);\n    }\n\n    // should we insert the value?\n    var ins = !eq(value, zero);\n\n    // old columns and rows\n    var r = matrix._size[0];\n    var c = matrix._size[1];\n    var i, j, k;\n\n    // check we need to increase columns\n    if (columns > c) {\n      // loop new columns\n      for (j = c; j < columns; j++) {\n        // update matrix._ptr for current column\n        matrix._ptr[j] = matrix._values.length;\n        // check we need to insert matrix._values\n        if (ins) {\n          // loop rows\n          for (i = 0; i < r; i++) {\n            // add new matrix._values\n            matrix._values.push(value);\n            // update matrix._index\n            matrix._index.push(i);\n          }\n        }\n      }\n      // store number of matrix._values in matrix._ptr\n      matrix._ptr[columns] = matrix._values.length;\n    } else if (columns < c) {\n      // truncate matrix._ptr\n      matrix._ptr.splice(columns + 1, c - columns);\n      // truncate matrix._values and matrix._index\n      matrix._values.splice(matrix._ptr[columns], matrix._values.length);\n      matrix._index.splice(matrix._ptr[columns], matrix._index.length);\n    }\n    // update columns\n    c = columns;\n\n    // check we need to increase rows\n    if (rows > r) {\n      // check we have to insert values\n      if (ins) {\n        // inserts\n        var n = 0;\n        // loop columns\n        for (j = 0; j < c; j++) {\n          // update matrix._ptr for current column\n          matrix._ptr[j] = matrix._ptr[j] + n;\n          // where to insert matrix._values\n          k = matrix._ptr[j + 1] + n;\n          // pointer\n          var p = 0;\n          // loop new rows, initialize pointer\n          for (i = r; i < rows; i++, p++) {\n            // add value\n            matrix._values.splice(k + p, 0, value);\n            // update matrix._index\n            matrix._index.splice(k + p, 0, i);\n            // increment inserts\n            n++;\n          }\n        }\n        // store number of matrix._values in matrix._ptr\n        matrix._ptr[c] = matrix._values.length;\n      }\n    } else if (rows < r) {\n      // deletes\n      var d = 0;\n      // loop columns\n      for (j = 0; j < c; j++) {\n        // update matrix._ptr for current column\n        matrix._ptr[j] = matrix._ptr[j] - d;\n        // where matrix._values start for next column\n        var k0 = matrix._ptr[j];\n        var k1 = matrix._ptr[j + 1] - d;\n        // loop matrix._index\n        for (k = k0; k < k1; k++) {\n          // row\n          i = matrix._index[k];\n          // check we need to delete value and matrix._index\n          if (i > rows - 1) {\n            // remove value\n            matrix._values.splice(k, 1);\n            // remove item from matrix._index\n            matrix._index.splice(k, 1);\n            // increase deletes\n            d++;\n          }\n        }\n      }\n      // update matrix._ptr for current column\n      matrix._ptr[j] = matrix._values.length;\n    }\n    // update matrix._size\n    matrix._size[0] = rows;\n    matrix._size[1] = columns;\n    // return matrix\n    return matrix;\n  }\n\n  /**\n   * Reshape the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (reshape in place).\n   *\n   * NOTE: This might be better suited to copy by default, instead of modifying\n   *       in place. For now, it operates in place to remain consistent with\n   *       resize().\n   *\n   * @memberof SparseMatrix\n   * @param {number[]} sizes          The new size the matrix should have.\n   *                                  Since sparse matrices are always two-dimensional,\n   *                                  size must be two numbers in either an array or a matrix\n   * @param {boolean} [copy]          Return a reshaped copy of the matrix\n   *\n   * @return {Matrix}                 The reshaped matrix\n   */\n  SparseMatrix.prototype.reshape = function (sizes, copy) {\n    // validate arguments\n    if (!isArray(sizes)) {\n      throw new TypeError('Array expected');\n    }\n    if (sizes.length !== 2) {\n      throw new Error('Sparse matrices can only be reshaped in two dimensions');\n    }\n\n    // check sizes\n    sizes.forEach(function (value) {\n      if (!isNumber(value) || !isInteger(value) || value <= -2 || value === 0) {\n        throw new TypeError('Invalid size, must contain positive integers or -1 ' + '(size: ' + format(sizes) + ')');\n      }\n    });\n    var currentLength = this._size[0] * this._size[1];\n    sizes = processSizesWildcard(sizes, currentLength);\n    var newLength = sizes[0] * sizes[1];\n\n    // m * n must not change\n    if (currentLength !== newLength) {\n      throw new Error('Reshaping sparse matrix will result in the wrong number of elements');\n    }\n\n    // matrix to reshape\n    var m = copy ? this.clone() : this;\n\n    // return unchanged if the same shape\n    if (this._size[0] === sizes[0] && this._size[1] === sizes[1]) {\n      return m;\n    }\n\n    // Convert to COO format (generate a column index)\n    var colIndex = [];\n    for (var i = 0; i < m._ptr.length; i++) {\n      for (var j = 0; j < m._ptr[i + 1] - m._ptr[i]; j++) {\n        colIndex.push(i);\n      }\n    }\n\n    // Clone the values array\n    var values = m._values.slice();\n\n    // Clone the row index array\n    var rowIndex = m._index.slice();\n\n    // Transform the (row, column) indices\n    for (var _i = 0; _i < m._index.length; _i++) {\n      var r1 = rowIndex[_i];\n      var c1 = colIndex[_i];\n      var flat = r1 * m._size[1] + c1;\n      colIndex[_i] = flat % sizes[1];\n      rowIndex[_i] = Math.floor(flat / sizes[1]);\n    }\n\n    // Now reshaping is supposed to preserve the row-major order, BUT these sparse matrices are stored\n    // in column-major order, so we have to reorder the value array now. One option is to use a multisort,\n    // sorting several arrays based on some other array.\n\n    // OR, we could easily just:\n\n    // 1. Remove all values from the matrix\n    m._values.length = 0;\n    m._index.length = 0;\n    m._ptr.length = sizes[1] + 1;\n    m._size = sizes.slice();\n    for (var _i2 = 0; _i2 < m._ptr.length; _i2++) {\n      m._ptr[_i2] = 0;\n    }\n\n    // 2. Re-insert all elements in the proper order (simplified code from SparseMatrix.prototype.set)\n    // This step is probably the most time-consuming\n    for (var h = 0; h < values.length; h++) {\n      var _i3 = rowIndex[h];\n      var _j = colIndex[h];\n      var v = values[h];\n      var k = _getValueIndex(_i3, m._ptr[_j], m._ptr[_j + 1], m._index);\n      _insert(k, _i3, _j, v, m._values, m._index, m._ptr);\n    }\n\n    // The value indices are inserted out of order, but apparently that's... still OK?\n\n    return m;\n  };\n\n  /**\n   * Create a clone of the matrix\n   * @memberof SparseMatrix\n   * @return {SparseMatrix} clone\n   */\n  SparseMatrix.prototype.clone = function () {\n    var m = new SparseMatrix({\n      values: this._values ? clone(this._values) : undefined,\n      index: clone(this._index),\n      ptr: clone(this._ptr),\n      size: clone(this._size),\n      datatype: this._datatype\n    });\n    return m;\n  };\n\n  /**\n   * Retrieve the size of the matrix.\n   * @memberof SparseMatrix\n   * @returns {number[]} size\n   */\n  SparseMatrix.prototype.size = function () {\n    return this._size.slice(0); // copy the Array\n  };\n\n  /**\n   * Create a new matrix with the results of the callback function executed on\n   * each entry of the matrix.\n   * @memberof SparseMatrix\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *\n   * @return {SparseMatrix} matrix\n   */\n  SparseMatrix.prototype.map = function (callback, skipZeros) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke map on a Pattern only matrix');\n    }\n    // matrix instance\n    var me = this;\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    var fastCallback = optimizeCallback(callback, me, 'map');\n    // invoke callback\n    var invoke = function invoke(v, i, j) {\n      // invoke callback\n      return fastCallback.fn(v, [i, j], me);\n    };\n    // invoke _map\n    return _map(this, 0, rows - 1, 0, columns - 1, invoke, skipZeros);\n  };\n\n  /**\n   * Create a new matrix with the results of the callback function executed on the interval\n   * [minRow..maxRow, minColumn..maxColumn].\n   */\n  function _map(matrix, minRow, maxRow, minColumn, maxColumn, callback, skipZeros) {\n    // result arrays\n    var values = [];\n    var index = [];\n    var ptr = [];\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(matrix._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [matrix._datatype, matrix._datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, matrix._datatype);\n    }\n\n    // invoke callback\n    var invoke = function invoke(v, x, y) {\n      // invoke callback\n      var value = callback(v, x, y);\n      // check value != 0\n      if (!eq(value, zero)) {\n        // store value\n        values.push(value);\n        // index\n        index.push(x);\n      }\n    };\n    // loop columns\n    for (var j = minColumn; j <= maxColumn; j++) {\n      // store pointer to values index\n      ptr.push(values.length);\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = matrix._ptr[j];\n      var k1 = matrix._ptr[j + 1];\n      if (skipZeros) {\n        // loop k within [k0, k1[\n        for (var k = k0; k < k1; k++) {\n          // row index\n          var i = matrix._index[k];\n          // check i is in range\n          if (i >= minRow && i <= maxRow) {\n            // value @ k\n            invoke(matrix._values[k], i - minRow, j - minColumn);\n          }\n        }\n      } else {\n        // create a cache holding all defined values\n        var _values = {};\n        for (var _k = k0; _k < k1; _k++) {\n          var _i4 = matrix._index[_k];\n          _values[_i4] = matrix._values[_k];\n        }\n\n        // loop over all rows (indexes can be unordered so we can't use that),\n        // and either read the value or zero\n        for (var _i5 = minRow; _i5 <= maxRow; _i5++) {\n          var value = _i5 in _values ? _values[_i5] : 0;\n          invoke(value, _i5 - minRow, j - minColumn);\n        }\n      }\n    }\n\n    // store number of values in ptr\n    ptr.push(values.length);\n    // return sparse matrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [maxRow - minRow + 1, maxColumn - minColumn + 1]\n    });\n  }\n\n  /**\n   * Execute a callback function on each entry of the matrix.\n   * @memberof SparseMatrix\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *                              If false, the indices are guaranteed to be in order,\n   *                              if true, the indices can be unordered.\n   */\n  SparseMatrix.prototype.forEach = function (callback, skipZeros) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke forEach on a Pattern only matrix');\n    }\n    // matrix instance\n    var me = this;\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    var fastCallback = optimizeCallback(callback, me, 'forEach');\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      if (skipZeros) {\n        // loop k within [k0, k1[\n        for (var k = k0; k < k1; k++) {\n          // row index\n          var i = this._index[k];\n\n          // value @ k\n          // TODO apply a non indexed version of algorithm in case fastCallback is not optimized\n          fastCallback.fn(this._values[k], [i, j], me);\n        }\n      } else {\n        // create a cache holding all defined values\n        var values = {};\n        for (var _k2 = k0; _k2 < k1; _k2++) {\n          var _i6 = this._index[_k2];\n          values[_i6] = this._values[_k2];\n        }\n\n        // loop over all rows (indexes can be unordered so we can't use that),\n        // and either read the value or zero\n        for (var _i7 = 0; _i7 < rows; _i7++) {\n          var value = _i7 in values ? values[_i7] : 0;\n          fastCallback.fn(value, [_i7, j], me);\n        }\n      }\n    }\n  };\n\n  /**\n   * Iterate over the matrix elements, skipping zeros\n   * @return {Iterable<{ value, index: number[] }>}\n   */\n  SparseMatrix.prototype[Symbol.iterator] = function* () {\n    if (!this._values) {\n      throw new Error('Cannot iterate a Pattern only matrix');\n    }\n    var columns = this._size[1];\n    for (var j = 0; j < columns; j++) {\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      for (var k = k0; k < k1; k++) {\n        // row index\n        var i = this._index[k];\n        yield {\n          value: this._values[k],\n          index: [i, j]\n        };\n      }\n    }\n  };\n\n  /**\n   * Create an Array with a copy of the data of the SparseMatrix\n   * @memberof SparseMatrix\n   * @returns {Array} array\n   */\n  SparseMatrix.prototype.toArray = function () {\n    return _toArray(this._values, this._index, this._ptr, this._size, true);\n  };\n\n  /**\n   * Get the primitive value of the SparseMatrix: a two dimensions array\n   * @memberof SparseMatrix\n   * @returns {Array} array\n   */\n  SparseMatrix.prototype.valueOf = function () {\n    return _toArray(this._values, this._index, this._ptr, this._size, false);\n  };\n  function _toArray(values, index, ptr, size, copy) {\n    // rows and columns\n    var rows = size[0];\n    var columns = size[1];\n    // result\n    var a = [];\n    // vars\n    var i, j;\n    // initialize array\n    for (i = 0; i < rows; i++) {\n      a[i] = [];\n      for (j = 0; j < columns; j++) {\n        a[i][j] = 0;\n      }\n    }\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = ptr[j];\n      var k1 = ptr[j + 1];\n      // loop k within [k0, k1[\n      for (var k = k0; k < k1; k++) {\n        // row index\n        i = index[k];\n        // set value (use one for pattern matrix)\n        a[i][j] = values ? copy ? clone(values[k]) : values[k] : 1;\n      }\n    }\n    return a;\n  }\n\n  /**\n   * Get a string representation of the matrix, with optional formatting options.\n   * @memberof SparseMatrix\n   * @param {Object | number | Function} [options]  Formatting options. See\n   *                                                lib/utils/number:format for a\n   *                                                description of the available\n   *                                                options.\n   * @returns {string} str\n   */\n  SparseMatrix.prototype.format = function (options) {\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n    // density\n    var density = this.density();\n    // rows & columns\n    var str = 'Sparse Matrix [' + format(rows, options) + ' x ' + format(columns, options) + '] density: ' + format(density, options) + '\\n';\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      // loop k within [k0, k1[\n      for (var k = k0; k < k1; k++) {\n        // row index\n        var i = this._index[k];\n        // append value\n        str += '\\n    (' + format(i, options) + ', ' + format(j, options) + ') ==> ' + (this._values ? format(this._values[k], options) : 'X');\n      }\n    }\n    return str;\n  };\n\n  /**\n   * Get a string representation of the matrix\n   * @memberof SparseMatrix\n   * @returns {string} str\n   */\n  SparseMatrix.prototype.toString = function () {\n    return format(this.toArray());\n  };\n\n  /**\n   * Get a JSON representation of the matrix\n   * @memberof SparseMatrix\n   * @returns {Object}\n   */\n  SparseMatrix.prototype.toJSON = function () {\n    return {\n      mathjs: 'SparseMatrix',\n      values: this._values,\n      index: this._index,\n      ptr: this._ptr,\n      size: this._size,\n      datatype: this._datatype\n    };\n  };\n\n  /**\n   * Get the kth Matrix diagonal.\n   *\n   * @memberof SparseMatrix\n   * @param {number | BigNumber} [k=0]     The kth diagonal where the vector will retrieved.\n   *\n   * @returns {Matrix}                     The matrix vector with the diagonal values.\n   */\n  SparseMatrix.prototype.diagonal = function (k) {\n    // validate k if any\n    if (k) {\n      // convert BigNumber to a number\n      if (isBigNumber(k)) {\n        k = k.toNumber();\n      }\n      // is must be an integer\n      if (!isNumber(k) || !isInteger(k)) {\n        throw new TypeError('The parameter k must be an integer number');\n      }\n    } else {\n      // default value\n      k = 0;\n    }\n    var kSuper = k > 0 ? k : 0;\n    var kSub = k < 0 ? -k : 0;\n\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1];\n\n    // number diagonal values\n    var n = Math.min(rows - kSub, columns - kSuper);\n\n    // diagonal arrays\n    var values = [];\n    var index = [];\n    var ptr = [];\n    // initial ptr value\n    ptr[0] = 0;\n    // loop columns\n    for (var j = kSuper; j < columns && values.length < n; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n      // loop x within [k0, k1[\n      for (var x = k0; x < k1; x++) {\n        // row index\n        var i = this._index[x];\n        // check row\n        if (i === j - kSuper + kSub) {\n          // value on this column\n          values.push(this._values[x]);\n          // store row\n          index[values.length - 1] = i - kSub;\n          // exit loop\n          break;\n        }\n      }\n    }\n    // close ptr\n    ptr.push(values.length);\n    // return matrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [n, 1]\n    });\n  };\n\n  /**\n   * Generate a matrix from a JSON object\n   * @memberof SparseMatrix\n   * @param {Object} json  An object structured like\n   *                       `{\"mathjs\": \"SparseMatrix\", \"values\": [], \"index\": [], \"ptr\": [], \"size\": []}`,\n   *                       where mathjs is optional\n   * @returns {SparseMatrix}\n   */\n  SparseMatrix.fromJSON = function (json) {\n    return new SparseMatrix(json);\n  };\n\n  /**\n   * Create a diagonal matrix.\n   *\n   * @memberof SparseMatrix\n   * @param {Array} size                       The matrix size.\n   * @param {number | Array | Matrix } value   The values for the diagonal.\n   * @param {number | BigNumber} [k=0]         The kth diagonal where the vector will be filled in.\n   * @param {number} [defaultValue]            The default value for non-diagonal\n   * @param {string} [datatype]                The Matrix datatype, values must be of this datatype.\n   *\n   * @returns {SparseMatrix}\n   */\n  SparseMatrix.diagonal = function (size, value, k, defaultValue, datatype) {\n    if (!isArray(size)) {\n      throw new TypeError('Array expected, size parameter');\n    }\n    if (size.length !== 2) {\n      throw new Error('Only two dimensions matrix are supported');\n    }\n\n    // map size & validate\n    size = size.map(function (s) {\n      // check it is a big number\n      if (isBigNumber(s)) {\n        // convert it\n        s = s.toNumber();\n      }\n      // validate arguments\n      if (!isNumber(s) || !isInteger(s) || s < 1) {\n        throw new Error('Size values must be positive integers');\n      }\n      return s;\n    });\n\n    // validate k if any\n    if (k) {\n      // convert BigNumber to a number\n      if (isBigNumber(k)) {\n        k = k.toNumber();\n      }\n      // is must be an integer\n      if (!isNumber(k) || !isInteger(k)) {\n        throw new TypeError('The parameter k must be an integer number');\n      }\n    } else {\n      // default value\n      k = 0;\n    }\n\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    if (isString(datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [datatype, datatype]) || equalScalar;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, datatype);\n    }\n    var kSuper = k > 0 ? k : 0;\n    var kSub = k < 0 ? -k : 0;\n\n    // rows and columns\n    var rows = size[0];\n    var columns = size[1];\n\n    // number of non-zero items\n    var n = Math.min(rows - kSub, columns - kSuper);\n\n    // value extraction function\n    var _value;\n\n    // check value\n    if (isArray(value)) {\n      // validate array\n      if (value.length !== n) {\n        // number of values in array must be n\n        throw new Error('Invalid value array length');\n      }\n      // define function\n      _value = function _value(i) {\n        // return value @ i\n        return value[i];\n      };\n    } else if (isMatrix(value)) {\n      // matrix size\n      var ms = value.size();\n      // validate matrix\n      if (ms.length !== 1 || ms[0] !== n) {\n        // number of values in array must be n\n        throw new Error('Invalid matrix length');\n      }\n      // define function\n      _value = function _value(i) {\n        // return value @ i\n        return value.get([i]);\n      };\n    } else {\n      // define function\n      _value = function _value() {\n        // return value\n        return value;\n      };\n    }\n\n    // create arrays\n    var values = [];\n    var index = [];\n    var ptr = [];\n\n    // loop items\n    for (var j = 0; j < columns; j++) {\n      // number of rows with value\n      ptr.push(values.length);\n      // diagonal index\n      var i = j - kSuper;\n      // check we need to set diagonal value\n      if (i >= 0 && i < n) {\n        // get value @ i\n        var v = _value(i);\n        // check for zero\n        if (!eq(v, zero)) {\n          // column\n          index.push(i + kSub);\n          // add value\n          values.push(v);\n        }\n      }\n    }\n    // last value should be number of values\n    ptr.push(values.length);\n    // create SparseMatrix\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [rows, columns]\n    });\n  };\n\n  /**\n   * Swap rows i and j in Matrix.\n   *\n   * @memberof SparseMatrix\n   * @param {number} i       Matrix row index 1\n   * @param {number} j       Matrix row index 2\n   *\n   * @return {Matrix}        The matrix reference\n   */\n  SparseMatrix.prototype.swapRows = function (i, j) {\n    // check index\n    if (!isNumber(i) || !isInteger(i) || !isNumber(j) || !isInteger(j)) {\n      throw new Error('Row index must be positive integers');\n    }\n    // check dimensions\n    if (this._size.length !== 2) {\n      throw new Error('Only two dimensional matrix is supported');\n    }\n    // validate index\n    validateIndex(i, this._size[0]);\n    validateIndex(j, this._size[0]);\n\n    // swap rows\n    SparseMatrix._swapRows(i, j, this._size[1], this._values, this._index, this._ptr);\n    // return current instance\n    return this;\n  };\n\n  /**\n   * Loop rows with data in column j.\n   *\n   * @param {number} j            Column\n   * @param {Array} values        Matrix values\n   * @param {Array} index         Matrix row indeces\n   * @param {Array} ptr           Matrix column pointers\n   * @param {Function} callback   Callback function invoked for every row in column j\n   */\n  SparseMatrix._forEachRow = function (j, values, index, ptr, callback) {\n    // indeces for column j\n    var k0 = ptr[j];\n    var k1 = ptr[j + 1];\n\n    // loop\n    for (var k = k0; k < k1; k++) {\n      // invoke callback\n      callback(index[k], values[k]);\n    }\n  };\n\n  /**\n   * Swap rows x and y in Sparse Matrix data structures.\n   *\n   * @param {number} x         Matrix row index 1\n   * @param {number} y         Matrix row index 2\n   * @param {number} columns   Number of columns in matrix\n   * @param {Array} values     Matrix values\n   * @param {Array} index      Matrix row indeces\n   * @param {Array} ptr        Matrix column pointers\n   */\n  SparseMatrix._swapRows = function (x, y, columns, values, index, ptr) {\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = ptr[j];\n      var k1 = ptr[j + 1];\n      // find value index @ x\n      var kx = _getValueIndex(x, k0, k1, index);\n      // find value index @ x\n      var ky = _getValueIndex(y, k0, k1, index);\n      // check both rows exist in matrix\n      if (kx < k1 && ky < k1 && index[kx] === x && index[ky] === y) {\n        // swap values (check for pattern matrix)\n        if (values) {\n          var v = values[kx];\n          values[kx] = values[ky];\n          values[ky] = v;\n        }\n        // next column\n        continue;\n      }\n      // check x row exist & no y row\n      if (kx < k1 && index[kx] === x && (ky >= k1 || index[ky] !== y)) {\n        // value @ x (check for pattern matrix)\n        var vx = values ? values[kx] : undefined;\n        // insert value @ y\n        index.splice(ky, 0, y);\n        if (values) {\n          values.splice(ky, 0, vx);\n        }\n        // remove value @ x (adjust array index if needed)\n        index.splice(ky <= kx ? kx + 1 : kx, 1);\n        if (values) {\n          values.splice(ky <= kx ? kx + 1 : kx, 1);\n        }\n        // next column\n        continue;\n      }\n      // check y row exist & no x row\n      if (ky < k1 && index[ky] === y && (kx >= k1 || index[kx] !== x)) {\n        // value @ y (check for pattern matrix)\n        var vy = values ? values[ky] : undefined;\n        // insert value @ x\n        index.splice(kx, 0, x);\n        if (values) {\n          values.splice(kx, 0, vy);\n        }\n        // remove value @ y (adjust array index if needed)\n        index.splice(kx <= ky ? ky + 1 : ky, 1);\n        if (values) {\n          values.splice(kx <= ky ? ky + 1 : ky, 1);\n        }\n      }\n    }\n  };\n  return SparseMatrix;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,SAASA,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,mBAAmB;AACrH,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,KAAK,EAAEC,eAAe,QAAQ,uBAAuB;AAC9D,SAASC,SAAS,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,aAAa,QAAQ,sBAAsB;AAClH,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,IAAIC,IAAI,GAAG,cAAc;AACzB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC;AACrD,OAAO,IAAIC,uBAAuB,GAAG,eAAeL,OAAO,CAACG,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACtF,IAAI;IACFC,KAAK;IACLC,WAAW;IACXC;EACF,CAAC,GAAGH,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;EACE,SAASI,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACpC,IAAI,EAAE,IAAI,YAAYF,YAAY,CAAC,EAAE;MACnC,MAAM,IAAIG,WAAW,CAAC,kDAAkD,CAAC;IAC3E;IACA,IAAID,QAAQ,IAAI,CAACvB,QAAQ,CAACuB,QAAQ,CAAC,EAAE;MACnC,MAAM,IAAIE,KAAK,CAAC,oBAAoB,GAAGF,QAAQ,CAAC;IAClD;IACA,IAAIzB,QAAQ,CAACwB,IAAI,CAAC,EAAE;MAClB;MACAI,iBAAiB,CAAC,IAAI,EAAEJ,IAAI,EAAEC,QAAQ,CAAC;IACzC,CAAC,MAAM,IAAID,IAAI,IAAI5B,OAAO,CAAC4B,IAAI,CAACK,KAAK,CAAC,IAAIjC,OAAO,CAAC4B,IAAI,CAACM,GAAG,CAAC,IAAIlC,OAAO,CAAC4B,IAAI,CAACO,IAAI,CAAC,EAAE;MACjF;MACA,IAAI,CAACC,OAAO,GAAGR,IAAI,CAACS,MAAM;MAC1B,IAAI,CAACC,MAAM,GAAGV,IAAI,CAACK,KAAK;MACxB,IAAI,CAACM,IAAI,GAAGX,IAAI,CAACM,GAAG;MACpB,IAAI,CAACM,KAAK,GAAGZ,IAAI,CAACO,IAAI;MACtB,IAAI,CAACM,SAAS,GAAGZ,QAAQ,IAAID,IAAI,CAACC,QAAQ;IAC5C,CAAC,MAAM,IAAI7B,OAAO,CAAC4B,IAAI,CAAC,EAAE;MACxB;MACAc,gBAAgB,CAAC,IAAI,EAAEd,IAAI,EAAEC,QAAQ,CAAC;IACxC,CAAC,MAAM,IAAID,IAAI,EAAE;MACf;MACA,MAAM,IAAIe,SAAS,CAAC,4BAA4B,GAAGpC,MAAM,CAACqB,IAAI,CAAC,GAAG,GAAG,CAAC;IACxE,CAAC,MAAM;MACL;MACA,IAAI,CAACQ,OAAO,GAAG,EAAE;MACjB,IAAI,CAACE,MAAM,GAAG,EAAE;MAChB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,CAAC;MACf,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAGZ,QAAQ;IAC3B;EACF;EACA,SAASG,iBAAiBA,CAACY,MAAM,EAAEC,MAAM,EAAEhB,QAAQ,EAAE;IACnD;IACA,IAAIgB,MAAM,CAACC,IAAI,KAAK,cAAc,EAAE;MAClC;MACAF,MAAM,CAACR,OAAO,GAAGS,MAAM,CAACT,OAAO,GAAG1B,KAAK,CAACmC,MAAM,CAACT,OAAO,CAAC,GAAGW,SAAS;MACnEH,MAAM,CAACN,MAAM,GAAG5B,KAAK,CAACmC,MAAM,CAACP,MAAM,CAAC;MACpCM,MAAM,CAACL,IAAI,GAAG7B,KAAK,CAACmC,MAAM,CAACN,IAAI,CAAC;MAChCK,MAAM,CAACJ,KAAK,GAAG9B,KAAK,CAACmC,MAAM,CAACL,KAAK,CAAC;MAClCI,MAAM,CAACH,SAAS,GAAGZ,QAAQ,IAAIgB,MAAM,CAACJ,SAAS;IACjD,CAAC,MAAM;MACL;MACAC,gBAAgB,CAACE,MAAM,EAAEC,MAAM,CAACG,OAAO,CAAC,CAAC,EAAEnB,QAAQ,IAAIgB,MAAM,CAACJ,SAAS,CAAC;IAC1E;EACF;EACA,SAASC,gBAAgBA,CAACE,MAAM,EAAEhB,IAAI,EAAEC,QAAQ,EAAE;IAChD;IACAe,MAAM,CAACR,OAAO,GAAG,EAAE;IACnBQ,MAAM,CAACN,MAAM,GAAG,EAAE;IAClBM,MAAM,CAACL,IAAI,GAAG,EAAE;IAChBK,MAAM,CAACH,SAAS,GAAGZ,QAAQ;IAC3B;IACA,IAAIoB,IAAI,GAAGrB,IAAI,CAACsB,MAAM;IACtB,IAAIC,OAAO,GAAG,CAAC;;IAEf;IACA,IAAIC,EAAE,GAAG3B,WAAW;IACpB;IACA,IAAI4B,IAAI,GAAG,CAAC;IACZ,IAAI/C,QAAQ,CAACuB,QAAQ,CAAC,EAAE;MACtB;MACAuB,EAAE,GAAG5B,KAAK,CAAC8B,IAAI,CAAC7B,WAAW,EAAE,CAACI,QAAQ,EAAEA,QAAQ,CAAC,CAAC,IAAIJ,WAAW;MACjE;MACA4B,IAAI,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,EAAE1B,QAAQ,CAAC;IACnC;;IAEA;IACA,IAAIoB,IAAI,GAAG,CAAC,EAAE;MACZ;MACA,IAAIO,CAAC,GAAG,CAAC;MACT,GAAG;QACD;QACAZ,MAAM,CAACL,IAAI,CAACkB,IAAI,CAACb,MAAM,CAACN,MAAM,CAACY,MAAM,CAAC;QACtC;QACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,EAAES,CAAC,EAAE,EAAE;UAC7B;UACA,IAAIC,GAAG,GAAG/B,IAAI,CAAC8B,CAAC,CAAC;UACjB;UACA,IAAI1D,OAAO,CAAC2D,GAAG,CAAC,EAAE;YAChB;YACA,IAAIH,CAAC,KAAK,CAAC,IAAIL,OAAO,GAAGQ,GAAG,CAACT,MAAM,EAAE;cACnCC,OAAO,GAAGQ,GAAG,CAACT,MAAM;YACtB;YACA;YACA,IAAIM,CAAC,GAAGG,GAAG,CAACT,MAAM,EAAE;cAClB;cACA,IAAIU,CAAC,GAAGD,GAAG,CAACH,CAAC,CAAC;cACd;cACA,IAAI,CAACJ,EAAE,CAACQ,CAAC,EAAEP,IAAI,CAAC,EAAE;gBAChB;gBACAT,MAAM,CAACR,OAAO,CAACqB,IAAI,CAACG,CAAC,CAAC;gBACtB;gBACAhB,MAAM,CAACN,MAAM,CAACmB,IAAI,CAACC,CAAC,CAAC;cACvB;YACF;UACF,CAAC,MAAM;YACL;YACA,IAAIF,CAAC,KAAK,CAAC,IAAIL,OAAO,GAAG,CAAC,EAAE;cAC1BA,OAAO,GAAG,CAAC;YACb;YACA;YACA,IAAI,CAACC,EAAE,CAACO,GAAG,EAAEN,IAAI,CAAC,EAAE;cAClB;cACAT,MAAM,CAACR,OAAO,CAACqB,IAAI,CAACE,GAAG,CAAC;cACxB;cACAf,MAAM,CAACN,MAAM,CAACmB,IAAI,CAACC,CAAC,CAAC;YACvB;UACF;QACF;QACA;QACAF,CAAC,EAAE;MACL,CAAC,QAAQA,CAAC,GAAGL,OAAO;IACtB;IACA;IACAP,MAAM,CAACL,IAAI,CAACkB,IAAI,CAACb,MAAM,CAACN,MAAM,CAACY,MAAM,CAAC;IACtC;IACAN,MAAM,CAACJ,KAAK,GAAG,CAACS,IAAI,EAAEE,OAAO,CAAC;EAChC;EACAxB,YAAY,CAACkC,SAAS,GAAG,IAAInC,MAAM,CAAC,CAAC;;EAErC;AACF;AACA;EACEC,YAAY,CAACkC,SAAS,CAACC,kBAAkB,GAAG,UAAUlC,IAAI,EAAEC,QAAQ,EAAE;IACpE,OAAO,IAAIF,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;EACzC,CAAC;;EAED;AACF;AACA;EACEkC,MAAM,CAACC,cAAc,CAACrC,YAAY,EAAE,MAAM,EAAE;IAC1CsC,KAAK,EAAE;EACT,CAAC,CAAC;EACFtC,YAAY,CAACkC,SAAS,CAACK,WAAW,GAAGvC,YAAY;EACjDA,YAAY,CAACkC,SAAS,CAACf,IAAI,GAAG,cAAc;EAC5CnB,YAAY,CAACkC,SAAS,CAACM,cAAc,GAAG,IAAI;;EAE5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExC,YAAY,CAACkC,SAAS,CAACO,WAAW,GAAG,YAAY;IAC/C,OAAOvD,gBAAgB,CAAC,IAAI,CAACuB,OAAO,EAAE7B,MAAM,CAAC;EAC/C,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoB,YAAY,CAACkC,SAAS,CAACQ,OAAO,GAAG,YAAY;IAC3C,OAAO,QAAQ;EACjB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1C,YAAY,CAACkC,SAAS,CAAChC,QAAQ,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACY,SAAS;EACvB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACEd,YAAY,CAACkC,SAAS,CAACS,MAAM,GAAG,UAAU1C,IAAI,EAAEC,QAAQ,EAAE;IACxD,OAAO,IAAIF,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;EACzC,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEF,YAAY,CAACkC,SAAS,CAACU,OAAO,GAAG,YAAY;IAC3C;IACA,IAAItB,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;IAC3B;IACA,OAAOS,IAAI,KAAK,CAAC,IAAIE,OAAO,KAAK,CAAC,GAAG,IAAI,CAACb,MAAM,CAACY,MAAM,IAAID,IAAI,GAAGE,OAAO,CAAC,GAAG,CAAC;EAChF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExB,YAAY,CAACkC,SAAS,CAACW,MAAM,GAAG,UAAUvC,KAAK,EAAEwC,WAAW,EAAEC,YAAY,EAAE;IAC1E;IACA,IAAI,CAAC,IAAI,CAACtC,OAAO,EAAE;MACjB,MAAM,IAAIL,KAAK,CAAC,+CAA+C,CAAC;IAClE;;IAEA;IACA,QAAQ4C,SAAS,CAACzB,MAAM;MACtB,KAAK,CAAC;QACJ,OAAO0B,UAAU,CAAC,IAAI,EAAE3C,KAAK,CAAC;;MAEhC;MACA,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAO4C,UAAU,CAAC,IAAI,EAAE5C,KAAK,EAAEwC,WAAW,EAAEC,YAAY,CAAC;MAC3D;QACE,MAAM,IAAI5C,WAAW,CAAC,2BAA2B,CAAC;IACtD;EACF,CAAC;EACD,SAAS8C,UAAUA,CAAChC,MAAM,EAAEkC,GAAG,EAAE;IAC/B;IACA,IAAI,CAAC3E,OAAO,CAAC2E,GAAG,CAAC,EAAE;MACjB,MAAM,IAAInC,SAAS,CAAC,eAAe,CAAC;IACtC;IACA,IAAIoC,QAAQ,GAAGD,GAAG,CAACC,QAAQ,CAAC,CAAC;IAC7B,IAAIA,QAAQ,EAAE;MACZ;MACA,OAAOnC,MAAM,CAACoC,GAAG,CAACF,GAAG,CAACG,GAAG,CAAC,CAAC,CAAC;IAC9B;IACA;IACA,IAAI9C,IAAI,GAAG2C,GAAG,CAAC3C,IAAI,CAAC,CAAC;IACrB,IAAIA,IAAI,CAACe,MAAM,KAAKN,MAAM,CAACJ,KAAK,CAACU,MAAM,EAAE;MACvC,MAAM,IAAIhC,cAAc,CAACiB,IAAI,CAACe,MAAM,EAAEN,MAAM,CAACJ,KAAK,CAACU,MAAM,CAAC;IAC5D;;IAEA;IACA,IAAIQ,CAAC,EAAEwB,EAAE,EAAEC,CAAC,EAAEC,EAAE;;IAEhB;IACA,IAAIH,GAAG,GAAGH,GAAG,CAACG,GAAG,CAAC,CAAC;IACnB,IAAII,GAAG,GAAGP,GAAG,CAACO,GAAG,CAAC,CAAC;IACnB,KAAK3B,CAAC,GAAG,CAAC,EAAEwB,EAAE,GAAGtC,MAAM,CAACJ,KAAK,CAACU,MAAM,EAAEQ,CAAC,GAAGwB,EAAE,EAAExB,CAAC,EAAE,EAAE;MACjD1C,aAAa,CAACiE,GAAG,CAACvB,CAAC,CAAC,EAAEd,MAAM,CAACJ,KAAK,CAACkB,CAAC,CAAC,CAAC;MACtC1C,aAAa,CAACqE,GAAG,CAAC3B,CAAC,CAAC,EAAEd,MAAM,CAACJ,KAAK,CAACkB,CAAC,CAAC,CAAC;IACxC;;IAEA;IACA,IAAI4B,OAAO,GAAG1C,MAAM,CAACR,OAAO;IAC5B,IAAImD,MAAM,GAAG3C,MAAM,CAACN,MAAM;IAC1B,IAAIkD,IAAI,GAAG5C,MAAM,CAACL,IAAI;;IAEtB;IACA,IAAIU,IAAI,GAAG6B,GAAG,CAACW,SAAS,CAAC,CAAC,CAAC;IAC3B,IAAItC,OAAO,GAAG2B,GAAG,CAACW,SAAS,CAAC,CAAC,CAAC;;IAE9B;IACA,IAAIC,CAAC,GAAG,EAAE;IACV,IAAIC,EAAE,GAAG,EAAE;;IAEX;IACA1C,IAAI,CAAC2C,OAAO,CAAC,UAAUlC,CAAC,EAAEmC,CAAC,EAAE;MAC3B;MACAF,EAAE,CAACjC,CAAC,CAAC,GAAGmC,CAAC,CAAC,CAAC,CAAC;MACZ;MACAH,CAAC,CAAChC,CAAC,CAAC,GAAG,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAIrB,MAAM,GAAGiD,OAAO,GAAG,EAAE,GAAGvC,SAAS;IACrC,IAAId,KAAK,GAAG,EAAE;IACd,IAAIC,GAAG,GAAG,EAAE;;IAEZ;IACAiB,OAAO,CAACyC,OAAO,CAAC,UAAUpC,CAAC,EAAE;MAC3B;MACAtB,GAAG,CAACuB,IAAI,CAACxB,KAAK,CAACiB,MAAM,CAAC;MACtB;MACA,KAAKiC,CAAC,GAAGK,IAAI,CAAChC,CAAC,CAAC,EAAE4B,EAAE,GAAGI,IAAI,CAAChC,CAAC,GAAG,CAAC,CAAC,EAAE2B,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;QAC/C;QACAzB,CAAC,GAAG6B,MAAM,CAACJ,CAAC,CAAC;QACb;QACA,IAAIO,CAAC,CAAChC,CAAC,CAAC,KAAK,IAAI,EAAE;UACjB;UACAzB,KAAK,CAACwB,IAAI,CAACkC,EAAE,CAACjC,CAAC,CAAC,CAAC;UACjB;UACA,IAAIrB,MAAM,EAAE;YACVA,MAAM,CAACoB,IAAI,CAAC6B,OAAO,CAACH,CAAC,CAAC,CAAC;UACzB;QACF;MACF;IACF,CAAC,CAAC;IACF;IACAjD,GAAG,CAACuB,IAAI,CAACxB,KAAK,CAACiB,MAAM,CAAC;;IAEtB;IACA,OAAO,IAAIvB,YAAY,CAAC;MACtBU,MAAM;MACNJ,KAAK;MACLC,GAAG;MACHC,IAAI;MACJN,QAAQ,EAAEe,MAAM,CAACH;IACnB,CAAC,CAAC;EACJ;EACA,SAASoC,UAAUA,CAACjC,MAAM,EAAEX,KAAK,EAAE6D,SAAS,EAAEpB,YAAY,EAAE;IAC1D;IACA,IAAI,CAACzC,KAAK,IAAIA,KAAK,CAAC9B,OAAO,KAAK,IAAI,EAAE;MACpC,MAAM,IAAIwC,SAAS,CAAC,eAAe,CAAC;IACtC;;IAEA;IACA,IAAIoD,KAAK,GAAG9D,KAAK,CAACE,IAAI,CAAC,CAAC;IACxB,IAAI4C,QAAQ,GAAG9C,KAAK,CAAC8C,QAAQ,CAAC,CAAC;;IAE/B;IACA,IAAIiB,KAAK;IACT,IAAI5F,QAAQ,CAAC0F,SAAS,CAAC,EAAE;MACvB;MACAE,KAAK,GAAGF,SAAS,CAAC3D,IAAI,CAAC,CAAC;MACxB;MACA2D,SAAS,GAAGA,SAAS,CAACG,OAAO,CAAC,CAAC;IACjC,CAAC,MAAM;MACL;MACAD,KAAK,GAAGpF,SAAS,CAACkF,SAAS,CAAC;IAC9B;;IAEA;IACA,IAAIf,QAAQ,EAAE;MACZ;MACA,IAAIiB,KAAK,CAAC9C,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM,IAAIP,SAAS,CAAC,iBAAiB,CAAC;MACxC;MACA;MACAC,MAAM,CAACsD,GAAG,CAACjE,KAAK,CAACgD,GAAG,CAAC,CAAC,EAAEa,SAAS,EAAEpB,YAAY,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAIqB,KAAK,CAAC7C,MAAM,KAAK,CAAC,IAAI6C,KAAK,CAAC7C,MAAM,KAAK,CAAC,EAAE;QAC5C,MAAM,IAAIhC,cAAc,CAAC6E,KAAK,CAAC7C,MAAM,EAAEN,MAAM,CAACJ,KAAK,CAACU,MAAM,EAAE,GAAG,CAAC;MAClE;;MAEA;MACA,IAAI8C,KAAK,CAAC9C,MAAM,GAAG6C,KAAK,CAAC7C,MAAM,EAAE;QAC/B;QACA,IAAIQ,CAAC,GAAG,CAAC;QACT,IAAIyC,KAAK,GAAG,CAAC;QACb,OAAOJ,KAAK,CAACrC,CAAC,CAAC,KAAK,CAAC,IAAIsC,KAAK,CAACtC,CAAC,CAAC,KAAK,CAAC,EAAE;UACvCA,CAAC,EAAE;QACL;QACA,OAAOqC,KAAK,CAACrC,CAAC,CAAC,KAAK,CAAC,EAAE;UACrByC,KAAK,EAAE;UACPzC,CAAC,EAAE;QACL;QACA;QACAoC,SAAS,GAAG/E,SAAS,CAAC+E,SAAS,EAAEC,KAAK,CAAC7C,MAAM,EAAEiD,KAAK,EAAEH,KAAK,CAAC;MAC9D;;MAEA;MACA,IAAI,CAACrF,eAAe,CAACoF,KAAK,EAAEC,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI9E,cAAc,CAAC6E,KAAK,EAAEC,KAAK,EAAE,GAAG,CAAC;MAC7C;;MAEA;MACA,IAAID,KAAK,CAAC7C,MAAM,KAAK,CAAC,EAAE;QACtB;QACA,IAAIkD,KAAK,GAAGnE,KAAK,CAACwD,SAAS,CAAC,CAAC,CAAC;QAC9BW,KAAK,CAACR,OAAO,CAAC,UAAUS,SAAS,EAAEC,QAAQ,EAAE;UAC3CtF,aAAa,CAACqF,SAAS,CAAC;UACxBzD,MAAM,CAACsD,GAAG,CAAC,CAACG,SAAS,EAAE,CAAC,CAAC,EAAEP,SAAS,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE5B,YAAY,CAAC;QAClE,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI6B,mBAAmB,GAAGtE,KAAK,CAACwD,SAAS,CAAC,CAAC,CAAC;QAC5C,IAAIe,oBAAoB,GAAGvE,KAAK,CAACwD,SAAS,CAAC,CAAC,CAAC;QAC7Cc,mBAAmB,CAACX,OAAO,CAAC,UAAUa,cAAc,EAAEC,aAAa,EAAE;UACnE1F,aAAa,CAACyF,cAAc,CAAC;UAC7BD,oBAAoB,CAACZ,OAAO,CAAC,UAAUe,eAAe,EAAEC,cAAc,EAAE;YACtE5F,aAAa,CAAC2F,eAAe,CAAC;YAC9B/D,MAAM,CAACsD,GAAG,CAAC,CAACO,cAAc,EAAEE,eAAe,CAAC,EAAEb,SAAS,CAACY,aAAa,CAAC,CAAC,CAAC,CAAC,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC,EAAElC,YAAY,CAAC;UAC7G,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IACA,OAAO9B,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEjB,YAAY,CAACkC,SAAS,CAACmB,GAAG,GAAG,UAAU/C,KAAK,EAAE;IAC5C,IAAI,CAACjC,OAAO,CAACiC,KAAK,CAAC,EAAE;MACnB,MAAM,IAAIU,SAAS,CAAC,gBAAgB,CAAC;IACvC;IACA,IAAIV,KAAK,CAACiB,MAAM,KAAK,IAAI,CAACV,KAAK,CAACU,MAAM,EAAE;MACtC,MAAM,IAAIhC,cAAc,CAACe,KAAK,CAACiB,MAAM,EAAE,IAAI,CAACV,KAAK,CAACU,MAAM,CAAC;IAC3D;;IAEA;IACA,IAAI,CAAC,IAAI,CAACd,OAAO,EAAE;MACjB,MAAM,IAAIL,KAAK,CAAC,4CAA4C,CAAC;IAC/D;;IAEA;IACA,IAAI2B,CAAC,GAAGzB,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIuB,CAAC,GAAGvB,KAAK,CAAC,CAAC,CAAC;;IAEhB;IACAjB,aAAa,CAAC0C,CAAC,EAAE,IAAI,CAAClB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/BxB,aAAa,CAACwC,CAAC,EAAE,IAAI,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE/B;IACA,IAAI2C,CAAC,GAAG0B,cAAc,CAACnD,CAAC,EAAE,IAAI,CAACnB,IAAI,CAACiB,CAAC,CAAC,EAAE,IAAI,CAACjB,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClB,MAAM,CAAC;IACtE;IACA,IAAI6C,CAAC,GAAG,IAAI,CAAC5C,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAClB,MAAM,CAAC6C,CAAC,CAAC,KAAKzB,CAAC,EAAE;MAChD,OAAO,IAAI,CAACtB,OAAO,CAAC+C,CAAC,CAAC;IACxB;IACA,OAAO,CAAC;EACV,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExD,YAAY,CAACkC,SAAS,CAACqC,GAAG,GAAG,UAAUjE,KAAK,EAAE2B,CAAC,EAAEc,YAAY,EAAE;IAC7D,IAAI,CAAC1E,OAAO,CAACiC,KAAK,CAAC,EAAE;MACnB,MAAM,IAAIU,SAAS,CAAC,gBAAgB,CAAC;IACvC;IACA,IAAIV,KAAK,CAACiB,MAAM,KAAK,IAAI,CAACV,KAAK,CAACU,MAAM,EAAE;MACtC,MAAM,IAAIhC,cAAc,CAACe,KAAK,CAACiB,MAAM,EAAE,IAAI,CAACV,KAAK,CAACU,MAAM,CAAC;IAC3D;;IAEA;IACA,IAAI,CAAC,IAAI,CAACd,OAAO,EAAE;MACjB,MAAM,IAAIL,KAAK,CAAC,4CAA4C,CAAC;IAC/D;;IAEA;IACA,IAAI2B,CAAC,GAAGzB,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIuB,CAAC,GAAGvB,KAAK,CAAC,CAAC,CAAC;;IAEhB;IACA,IAAIgB,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;;IAE3B;IACA,IAAIY,EAAE,GAAG3B,WAAW;IACpB;IACA,IAAI4B,IAAI,GAAG,CAAC;IACZ,IAAI/C,QAAQ,CAAC,IAAI,CAACmC,SAAS,CAAC,EAAE;MAC5B;MACAW,EAAE,GAAG5B,KAAK,CAAC8B,IAAI,CAAC7B,WAAW,EAAE,CAAC,IAAI,CAACgB,SAAS,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,IAAIhB,WAAW;MAC7E;MACA4B,IAAI,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,EAAE,IAAI,CAACd,SAAS,CAAC;IACzC;;IAEA;IACA,IAAIiB,CAAC,GAAGT,IAAI,GAAG,CAAC,IAAIO,CAAC,GAAGL,OAAO,GAAG,CAAC,EAAE;MACnC;MACA2D,OAAO,CAAC,IAAI,EAAEC,IAAI,CAAC1B,GAAG,CAAC3B,CAAC,GAAG,CAAC,EAAET,IAAI,CAAC,EAAE8D,IAAI,CAAC1B,GAAG,CAAC7B,CAAC,GAAG,CAAC,EAAEL,OAAO,CAAC,EAAEuB,YAAY,CAAC;MAC5E;MACAzB,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;MACpBW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;IACzB;;IAEA;IACAxB,aAAa,CAAC0C,CAAC,EAAET,IAAI,CAAC;IACtBjC,aAAa,CAACwC,CAAC,EAAEL,OAAO,CAAC;;IAEzB;IACA,IAAIgC,CAAC,GAAG0B,cAAc,CAACnD,CAAC,EAAE,IAAI,CAACnB,IAAI,CAACiB,CAAC,CAAC,EAAE,IAAI,CAACjB,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClB,MAAM,CAAC;IACtE;IACA,IAAI6C,CAAC,GAAG,IAAI,CAAC5C,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAClB,MAAM,CAAC6C,CAAC,CAAC,KAAKzB,CAAC,EAAE;MAChD;MACA,IAAI,CAACN,EAAE,CAACQ,CAAC,EAAEP,IAAI,CAAC,EAAE;QAChB;QACA,IAAI,CAACjB,OAAO,CAAC+C,CAAC,CAAC,GAAGvB,CAAC;MACrB,CAAC,MAAM;QACL;QACAoD,OAAO,CAAC7B,CAAC,EAAE3B,CAAC,EAAE,IAAI,CAACpB,OAAO,EAAE,IAAI,CAACE,MAAM,EAAE,IAAI,CAACC,IAAI,CAAC;MACrD;IACF,CAAC,MAAM;MACL,IAAI,CAACa,EAAE,CAACQ,CAAC,EAAEP,IAAI,CAAC,EAAE;QAChB;QACA4D,OAAO,CAAC9B,CAAC,EAAEzB,CAAC,EAAEF,CAAC,EAAEI,CAAC,EAAE,IAAI,CAACxB,OAAO,EAAE,IAAI,CAACE,MAAM,EAAE,IAAI,CAACC,IAAI,CAAC;MAC3D;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD,SAASsE,cAAcA,CAACnD,CAAC,EAAEwD,GAAG,EAAEC,MAAM,EAAElF,KAAK,EAAE;IAC7C;IACA,IAAIkF,MAAM,GAAGD,GAAG,KAAK,CAAC,EAAE;MACtB,OAAOC,MAAM;IACf;IACA;IACA,KAAK,IAAItB,CAAC,GAAGqB,GAAG,EAAErB,CAAC,GAAGsB,MAAM,EAAEtB,CAAC,EAAE,EAAE;MACjC;MACA,IAAI5D,KAAK,CAAC4D,CAAC,CAAC,KAAKnC,CAAC,EAAE;QAClB,OAAOmC,CAAC;MACV;IACF;IACA;IACA,OAAOqB,GAAG;EACZ;EACA,SAASF,OAAOA,CAAC7B,CAAC,EAAE3B,CAAC,EAAEnB,MAAM,EAAEJ,KAAK,EAAEC,GAAG,EAAE;IACzC;IACAG,MAAM,CAAC+E,MAAM,CAACjC,CAAC,EAAE,CAAC,CAAC;IACnBlD,KAAK,CAACmF,MAAM,CAACjC,CAAC,EAAE,CAAC,CAAC;IAClB;IACA,KAAK,IAAIkC,CAAC,GAAG7D,CAAC,GAAG,CAAC,EAAE6D,CAAC,GAAGnF,GAAG,CAACgB,MAAM,EAAEmE,CAAC,EAAE,EAAE;MACvCnF,GAAG,CAACmF,CAAC,CAAC,EAAE;IACV;EACF;EACA,SAASJ,OAAOA,CAAC9B,CAAC,EAAEzB,CAAC,EAAEF,CAAC,EAAEI,CAAC,EAAEvB,MAAM,EAAEJ,KAAK,EAAEC,GAAG,EAAE;IAC/C;IACAG,MAAM,CAAC+E,MAAM,CAACjC,CAAC,EAAE,CAAC,EAAEvB,CAAC,CAAC;IACtB;IACA3B,KAAK,CAACmF,MAAM,CAACjC,CAAC,EAAE,CAAC,EAAEzB,CAAC,CAAC;IACrB;IACA,KAAK,IAAI2D,CAAC,GAAG7D,CAAC,GAAG,CAAC,EAAE6D,CAAC,GAAGnF,GAAG,CAACgB,MAAM,EAAEmE,CAAC,EAAE,EAAE;MACvCnF,GAAG,CAACmF,CAAC,CAAC,EAAE;IACV;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1F,YAAY,CAACkC,SAAS,CAACyD,MAAM,GAAG,UAAUnF,IAAI,EAAEuC,YAAY,EAAE6C,IAAI,EAAE;IAClE;IACA,IAAI,CAACrH,YAAY,CAACiC,IAAI,CAAC,EAAE;MACvB,MAAM,IAAIQ,SAAS,CAAC,0BAA0B,CAAC;IACjD;;IAEA;IACA,IAAI6E,SAAS,GAAGrF,IAAI,CAACa,OAAO,CAAC,CAAC,CAACyE,GAAG,CAACxD,KAAK,IAAI;MAC1C,OAAOyD,KAAK,CAAC1H,OAAO,CAACiE,KAAK,CAAC,IAAIA,KAAK,CAACf,MAAM,KAAK,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;IACtE,CAAC,CAAC;IACF,IAAIuD,SAAS,CAACtE,MAAM,KAAK,CAAC,EAAE;MAC1B,MAAM,IAAInB,KAAK,CAAC,0CAA0C,CAAC;IAC7D;;IAEA;IACAyF,SAAS,CAAC5B,OAAO,CAAC,UAAU3B,KAAK,EAAE;MACjC,IAAI,CAAC5D,QAAQ,CAAC4D,KAAK,CAAC,IAAI,CAACzD,SAAS,CAACyD,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACtD,MAAM,IAAItB,SAAS,CAAC,+CAA+C,GAAG,SAAS,GAAGlC,MAAM,CAAC+G,SAAS,CAAC,GAAG,GAAG,CAAC;MAC5G;IACF,CAAC,CAAC;;IAEF;IACA,IAAIG,CAAC,GAAGJ,IAAI,GAAG,IAAI,CAAC7G,KAAK,CAAC,CAAC,GAAG,IAAI;IAClC;IACA,OAAOoG,OAAO,CAACa,CAAC,EAAEH,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAE9C,YAAY,CAAC;EAC7D,CAAC;EACD,SAASoC,OAAOA,CAAClE,MAAM,EAAEK,IAAI,EAAEE,OAAO,EAAEuB,YAAY,EAAE;IACpD;IACA,IAAIT,KAAK,GAAGS,YAAY,IAAI,CAAC;;IAE7B;IACA,IAAItB,EAAE,GAAG3B,WAAW;IACpB;IACA,IAAI4B,IAAI,GAAG,CAAC;IACZ,IAAI/C,QAAQ,CAACsC,MAAM,CAACH,SAAS,CAAC,EAAE;MAC9B;MACAW,EAAE,GAAG5B,KAAK,CAAC8B,IAAI,CAAC7B,WAAW,EAAE,CAACmB,MAAM,CAACH,SAAS,EAAEG,MAAM,CAACH,SAAS,CAAC,CAAC,IAAIhB,WAAW;MACjF;MACA4B,IAAI,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,EAAEX,MAAM,CAACH,SAAS,CAAC;MACzC;MACAwB,KAAK,GAAGzC,KAAK,CAAC+B,OAAO,CAACU,KAAK,EAAErB,MAAM,CAACH,SAAS,CAAC;IAChD;;IAEA;IACA,IAAImF,GAAG,GAAG,CAACxE,EAAE,CAACa,KAAK,EAAEZ,IAAI,CAAC;;IAE1B;IACA,IAAIwC,CAAC,GAAGjD,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC;IACvB,IAAIqF,CAAC,GAAGjF,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC;IACvB,IAAIkB,CAAC,EAAEF,CAAC,EAAE2B,CAAC;;IAEX;IACA,IAAIhC,OAAO,GAAG0E,CAAC,EAAE;MACf;MACA,KAAKrE,CAAC,GAAGqE,CAAC,EAAErE,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;QAC5B;QACAZ,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC,GAAGZ,MAAM,CAACR,OAAO,CAACc,MAAM;QACtC;QACA,IAAI0E,GAAG,EAAE;UACP;UACA,KAAKlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,CAAC,EAAEnC,CAAC,EAAE,EAAE;YACtB;YACAd,MAAM,CAACR,OAAO,CAACqB,IAAI,CAACQ,KAAK,CAAC;YAC1B;YACArB,MAAM,CAACN,MAAM,CAACmB,IAAI,CAACC,CAAC,CAAC;UACvB;QACF;MACF;MACA;MACAd,MAAM,CAACL,IAAI,CAACY,OAAO,CAAC,GAAGP,MAAM,CAACR,OAAO,CAACc,MAAM;IAC9C,CAAC,MAAM,IAAIC,OAAO,GAAG0E,CAAC,EAAE;MACtB;MACAjF,MAAM,CAACL,IAAI,CAAC6E,MAAM,CAACjE,OAAO,GAAG,CAAC,EAAE0E,CAAC,GAAG1E,OAAO,CAAC;MAC5C;MACAP,MAAM,CAACR,OAAO,CAACgF,MAAM,CAACxE,MAAM,CAACL,IAAI,CAACY,OAAO,CAAC,EAAEP,MAAM,CAACR,OAAO,CAACc,MAAM,CAAC;MAClEN,MAAM,CAACN,MAAM,CAAC8E,MAAM,CAACxE,MAAM,CAACL,IAAI,CAACY,OAAO,CAAC,EAAEP,MAAM,CAACN,MAAM,CAACY,MAAM,CAAC;IAClE;IACA;IACA2E,CAAC,GAAG1E,OAAO;;IAEX;IACA,IAAIF,IAAI,GAAG4C,CAAC,EAAE;MACZ;MACA,IAAI+B,GAAG,EAAE;QACP;QACA,IAAIE,CAAC,GAAG,CAAC;QACT;QACA,KAAKtE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,CAAC,EAAErE,CAAC,EAAE,EAAE;UACtB;UACAZ,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC,GAAGZ,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC,GAAGsE,CAAC;UACnC;UACA3C,CAAC,GAAGvC,MAAM,CAACL,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,GAAGsE,CAAC;UAC1B;UACA,IAAIC,CAAC,GAAG,CAAC;UACT;UACA,KAAKrE,CAAC,GAAGmC,CAAC,EAAEnC,CAAC,GAAGT,IAAI,EAAES,CAAC,EAAE,EAAEqE,CAAC,EAAE,EAAE;YAC9B;YACAnF,MAAM,CAACR,OAAO,CAACgF,MAAM,CAACjC,CAAC,GAAG4C,CAAC,EAAE,CAAC,EAAE9D,KAAK,CAAC;YACtC;YACArB,MAAM,CAACN,MAAM,CAAC8E,MAAM,CAACjC,CAAC,GAAG4C,CAAC,EAAE,CAAC,EAAErE,CAAC,CAAC;YACjC;YACAoE,CAAC,EAAE;UACL;QACF;QACA;QACAlF,MAAM,CAACL,IAAI,CAACsF,CAAC,CAAC,GAAGjF,MAAM,CAACR,OAAO,CAACc,MAAM;MACxC;IACF,CAAC,MAAM,IAAID,IAAI,GAAG4C,CAAC,EAAE;MACnB;MACA,IAAImC,CAAC,GAAG,CAAC;MACT;MACA,KAAKxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,CAAC,EAAErE,CAAC,EAAE,EAAE;QACtB;QACAZ,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC,GAAGZ,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC,GAAGwE,CAAC;QACnC;QACA,IAAIC,EAAE,GAAGrF,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC;QACvB,IAAI0E,EAAE,GAAGtF,MAAM,CAACL,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,GAAGwE,CAAC;QAC/B;QACA,KAAK7C,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;UACxB;UACAzB,CAAC,GAAGd,MAAM,CAACN,MAAM,CAAC6C,CAAC,CAAC;UACpB;UACA,IAAIzB,CAAC,GAAGT,IAAI,GAAG,CAAC,EAAE;YAChB;YACAL,MAAM,CAACR,OAAO,CAACgF,MAAM,CAACjC,CAAC,EAAE,CAAC,CAAC;YAC3B;YACAvC,MAAM,CAACN,MAAM,CAAC8E,MAAM,CAACjC,CAAC,EAAE,CAAC,CAAC;YAC1B;YACA6C,CAAC,EAAE;UACL;QACF;MACF;MACA;MACApF,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC,GAAGZ,MAAM,CAACR,OAAO,CAACc,MAAM;IACxC;IACA;IACAN,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC,GAAGS,IAAI;IACtBL,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC,GAAGW,OAAO;IACzB;IACA,OAAOP,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjB,YAAY,CAACkC,SAAS,CAACsE,OAAO,GAAG,UAAUC,KAAK,EAAEb,IAAI,EAAE;IACtD;IACA,IAAI,CAACvH,OAAO,CAACoI,KAAK,CAAC,EAAE;MACnB,MAAM,IAAIzF,SAAS,CAAC,gBAAgB,CAAC;IACvC;IACA,IAAIyF,KAAK,CAAClF,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAInB,KAAK,CAAC,wDAAwD,CAAC;IAC3E;;IAEA;IACAqG,KAAK,CAACxC,OAAO,CAAC,UAAU3B,KAAK,EAAE;MAC7B,IAAI,CAAC5D,QAAQ,CAAC4D,KAAK,CAAC,IAAI,CAACzD,SAAS,CAACyD,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,CAAC,IAAIA,KAAK,KAAK,CAAC,EAAE;QACvE,MAAM,IAAItB,SAAS,CAAC,qDAAqD,GAAG,SAAS,GAAGlC,MAAM,CAAC2H,KAAK,CAAC,GAAG,GAAG,CAAC;MAC9G;IACF,CAAC,CAAC;IACF,IAAIC,aAAa,GAAG,IAAI,CAAC7F,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC;IACjD4F,KAAK,GAAGtH,oBAAoB,CAACsH,KAAK,EAAEC,aAAa,CAAC;IAClD,IAAIC,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;;IAEnC;IACA,IAAIC,aAAa,KAAKC,SAAS,EAAE;MAC/B,MAAM,IAAIvG,KAAK,CAAC,qEAAqE,CAAC;IACxF;;IAEA;IACA,IAAI4F,CAAC,GAAGJ,IAAI,GAAG,IAAI,CAAC7G,KAAK,CAAC,CAAC,GAAG,IAAI;;IAElC;IACA,IAAI,IAAI,CAAC8B,KAAK,CAAC,CAAC,CAAC,KAAK4F,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC5F,KAAK,CAAC,CAAC,CAAC,KAAK4F,KAAK,CAAC,CAAC,CAAC,EAAE;MAC5D,OAAOT,CAAC;IACV;;IAEA;IACA,IAAIY,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,CAAC,CAACpF,IAAI,CAACW,MAAM,EAAEQ,CAAC,EAAE,EAAE;MACtC,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,CAAC,CAACpF,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAGiE,CAAC,CAACpF,IAAI,CAACmB,CAAC,CAAC,EAAEF,CAAC,EAAE,EAAE;QAClD+E,QAAQ,CAAC9E,IAAI,CAACC,CAAC,CAAC;MAClB;IACF;;IAEA;IACA,IAAIrB,MAAM,GAAGsF,CAAC,CAACvF,OAAO,CAACoG,KAAK,CAAC,CAAC;;IAE9B;IACA,IAAIC,QAAQ,GAAGd,CAAC,CAACrF,MAAM,CAACkG,KAAK,CAAC,CAAC;;IAE/B;IACA,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGf,CAAC,CAACrF,MAAM,CAACY,MAAM,EAAEwF,EAAE,EAAE,EAAE;MAC3C,IAAIC,EAAE,GAAGF,QAAQ,CAACC,EAAE,CAAC;MACrB,IAAIE,EAAE,GAAGL,QAAQ,CAACG,EAAE,CAAC;MACrB,IAAIG,IAAI,GAAGF,EAAE,GAAGhB,CAAC,CAACnF,KAAK,CAAC,CAAC,CAAC,GAAGoG,EAAE;MAC/BL,QAAQ,CAACG,EAAE,CAAC,GAAGG,IAAI,GAAGT,KAAK,CAAC,CAAC,CAAC;MAC9BK,QAAQ,CAACC,EAAE,CAAC,GAAG3B,IAAI,CAAC+B,KAAK,CAACD,IAAI,GAAGT,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA;IACA;;IAEA;;IAEA;IACAT,CAAC,CAACvF,OAAO,CAACc,MAAM,GAAG,CAAC;IACpByE,CAAC,CAACrF,MAAM,CAACY,MAAM,GAAG,CAAC;IACnByE,CAAC,CAACpF,IAAI,CAACW,MAAM,GAAGkF,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5BT,CAAC,CAACnF,KAAK,GAAG4F,KAAK,CAACI,KAAK,CAAC,CAAC;IACvB,KAAK,IAAIO,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpB,CAAC,CAACpF,IAAI,CAACW,MAAM,EAAE6F,GAAG,EAAE,EAAE;MAC5CpB,CAAC,CAACpF,IAAI,CAACwG,GAAG,CAAC,GAAG,CAAC;IACjB;;IAEA;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3G,MAAM,CAACa,MAAM,EAAE8F,CAAC,EAAE,EAAE;MACtC,IAAIC,GAAG,GAAGR,QAAQ,CAACO,CAAC,CAAC;MACrB,IAAIE,EAAE,GAAGX,QAAQ,CAACS,CAAC,CAAC;MACpB,IAAIpF,CAAC,GAAGvB,MAAM,CAAC2G,CAAC,CAAC;MACjB,IAAI7D,CAAC,GAAG0B,cAAc,CAACoC,GAAG,EAAEtB,CAAC,CAACpF,IAAI,CAAC2G,EAAE,CAAC,EAAEvB,CAAC,CAACpF,IAAI,CAAC2G,EAAE,GAAG,CAAC,CAAC,EAAEvB,CAAC,CAACrF,MAAM,CAAC;MACjE2E,OAAO,CAAC9B,CAAC,EAAE8D,GAAG,EAAEC,EAAE,EAAEtF,CAAC,EAAE+D,CAAC,CAACvF,OAAO,EAAEuF,CAAC,CAACrF,MAAM,EAAEqF,CAAC,CAACpF,IAAI,CAAC;IACrD;;IAEA;;IAEA,OAAOoF,CAAC;EACV,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEhG,YAAY,CAACkC,SAAS,CAACnD,KAAK,GAAG,YAAY;IACzC,IAAIiH,CAAC,GAAG,IAAIhG,YAAY,CAAC;MACvBU,MAAM,EAAE,IAAI,CAACD,OAAO,GAAG1B,KAAK,CAAC,IAAI,CAAC0B,OAAO,CAAC,GAAGW,SAAS;MACtDd,KAAK,EAAEvB,KAAK,CAAC,IAAI,CAAC4B,MAAM,CAAC;MACzBJ,GAAG,EAAExB,KAAK,CAAC,IAAI,CAAC6B,IAAI,CAAC;MACrBJ,IAAI,EAAEzB,KAAK,CAAC,IAAI,CAAC8B,KAAK,CAAC;MACvBX,QAAQ,EAAE,IAAI,CAACY;IACjB,CAAC,CAAC;IACF,OAAOkF,CAAC;EACV,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEhG,YAAY,CAACkC,SAAS,CAAC1B,IAAI,GAAG,YAAY;IACxC,OAAO,IAAI,CAACK,KAAK,CAACgG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7G,YAAY,CAACkC,SAAS,CAAC4D,GAAG,GAAG,UAAU0B,QAAQ,EAAEC,SAAS,EAAE;IAC1D;IACA,IAAI,CAAC,IAAI,CAAChH,OAAO,EAAE;MACjB,MAAM,IAAIL,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IACA;IACA,IAAIsH,EAAE,GAAG,IAAI;IACb;IACA,IAAIpG,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;IAC3B,IAAI8G,YAAY,GAAGnI,gBAAgB,CAACgI,QAAQ,EAAEE,EAAE,EAAE,KAAK,CAAC;IACxD;IACA,IAAIE,MAAM,GAAG,SAASA,MAAMA,CAAC3F,CAAC,EAAEF,CAAC,EAAEF,CAAC,EAAE;MACpC;MACA,OAAO8F,YAAY,CAACE,EAAE,CAAC5F,CAAC,EAAE,CAACF,CAAC,EAAEF,CAAC,CAAC,EAAE6F,EAAE,CAAC;IACvC,CAAC;IACD;IACA,OAAOI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAExG,IAAI,GAAG,CAAC,EAAE,CAAC,EAAEE,OAAO,GAAG,CAAC,EAAEoG,MAAM,EAAEH,SAAS,CAAC;EACnE,CAAC;;EAED;AACF;AACA;AACA;EACE,SAASK,IAAIA,CAAC7G,MAAM,EAAE8G,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEV,QAAQ,EAAEC,SAAS,EAAE;IAC/E;IACA,IAAI/G,MAAM,GAAG,EAAE;IACf,IAAIJ,KAAK,GAAG,EAAE;IACd,IAAIC,GAAG,GAAG,EAAE;;IAEZ;IACA,IAAIkB,EAAE,GAAG3B,WAAW;IACpB;IACA,IAAI4B,IAAI,GAAG,CAAC;IACZ,IAAI/C,QAAQ,CAACsC,MAAM,CAACH,SAAS,CAAC,EAAE;MAC9B;MACAW,EAAE,GAAG5B,KAAK,CAAC8B,IAAI,CAAC7B,WAAW,EAAE,CAACmB,MAAM,CAACH,SAAS,EAAEG,MAAM,CAACH,SAAS,CAAC,CAAC,IAAIhB,WAAW;MACjF;MACA4B,IAAI,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,EAAEX,MAAM,CAACH,SAAS,CAAC;IAC3C;;IAEA;IACA,IAAI8G,MAAM,GAAG,SAASA,MAAMA,CAAC3F,CAAC,EAAEyD,CAAC,EAAEyC,CAAC,EAAE;MACpC;MACA,IAAI7F,KAAK,GAAGkF,QAAQ,CAACvF,CAAC,EAAEyD,CAAC,EAAEyC,CAAC,CAAC;MAC7B;MACA,IAAI,CAAC1G,EAAE,CAACa,KAAK,EAAEZ,IAAI,CAAC,EAAE;QACpB;QACAhB,MAAM,CAACoB,IAAI,CAACQ,KAAK,CAAC;QAClB;QACAhC,KAAK,CAACwB,IAAI,CAAC4D,CAAC,CAAC;MACf;IACF,CAAC;IACD;IACA,KAAK,IAAI7D,CAAC,GAAGoG,SAAS,EAAEpG,CAAC,IAAIqG,SAAS,EAAErG,CAAC,EAAE,EAAE;MAC3C;MACAtB,GAAG,CAACuB,IAAI,CAACpB,MAAM,CAACa,MAAM,CAAC;MACvB;MACA,IAAI+E,EAAE,GAAGrF,MAAM,CAACL,IAAI,CAACiB,CAAC,CAAC;MACvB,IAAI0E,EAAE,GAAGtF,MAAM,CAACL,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC;MAC3B,IAAI4F,SAAS,EAAE;QACb;QACA,KAAK,IAAIjE,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;UAC5B;UACA,IAAIzB,CAAC,GAAGd,MAAM,CAACN,MAAM,CAAC6C,CAAC,CAAC;UACxB;UACA,IAAIzB,CAAC,IAAIgG,MAAM,IAAIhG,CAAC,IAAIiG,MAAM,EAAE;YAC9B;YACAJ,MAAM,CAAC3G,MAAM,CAACR,OAAO,CAAC+C,CAAC,CAAC,EAAEzB,CAAC,GAAGgG,MAAM,EAAElG,CAAC,GAAGoG,SAAS,CAAC;UACtD;QACF;MACF,CAAC,MAAM;QACL;QACA,IAAIxH,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,IAAI2H,EAAE,GAAG9B,EAAE,EAAE8B,EAAE,GAAG7B,EAAE,EAAE6B,EAAE,EAAE,EAAE;UAC/B,IAAIC,GAAG,GAAGpH,MAAM,CAACN,MAAM,CAACyH,EAAE,CAAC;UAC3B3H,OAAO,CAAC4H,GAAG,CAAC,GAAGpH,MAAM,CAACR,OAAO,CAAC2H,EAAE,CAAC;QACnC;;QAEA;QACA;QACA,KAAK,IAAIE,GAAG,GAAGP,MAAM,EAAEO,GAAG,IAAIN,MAAM,EAAEM,GAAG,EAAE,EAAE;UAC3C,IAAIhG,KAAK,GAAGgG,GAAG,IAAI7H,OAAO,GAAGA,OAAO,CAAC6H,GAAG,CAAC,GAAG,CAAC;UAC7CV,MAAM,CAACtF,KAAK,EAAEgG,GAAG,GAAGP,MAAM,EAAElG,CAAC,GAAGoG,SAAS,CAAC;QAC5C;MACF;IACF;;IAEA;IACA1H,GAAG,CAACuB,IAAI,CAACpB,MAAM,CAACa,MAAM,CAAC;IACvB;IACA,OAAO,IAAIvB,YAAY,CAAC;MACtBU,MAAM;MACNJ,KAAK;MACLC,GAAG;MACHC,IAAI,EAAE,CAACwH,MAAM,GAAGD,MAAM,GAAG,CAAC,EAAEG,SAAS,GAAGD,SAAS,GAAG,CAAC;IACvD,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjI,YAAY,CAACkC,SAAS,CAAC+B,OAAO,GAAG,UAAUuD,QAAQ,EAAEC,SAAS,EAAE;IAC9D;IACA,IAAI,CAAC,IAAI,CAAChH,OAAO,EAAE;MACjB,MAAM,IAAIL,KAAK,CAAC,gDAAgD,CAAC;IACnE;IACA;IACA,IAAIsH,EAAE,GAAG,IAAI;IACb;IACA,IAAIpG,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;IAC3B,IAAI8G,YAAY,GAAGnI,gBAAgB,CAACgI,QAAQ,EAAEE,EAAE,EAAE,SAAS,CAAC;IAC5D;IACA,KAAK,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAChC;MACA,IAAIyE,EAAE,GAAG,IAAI,CAAC1F,IAAI,CAACiB,CAAC,CAAC;MACrB,IAAI0E,EAAE,GAAG,IAAI,CAAC3F,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC;MACzB,IAAI4F,SAAS,EAAE;QACb;QACA,KAAK,IAAIjE,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;UAC5B;UACA,IAAIzB,CAAC,GAAG,IAAI,CAACpB,MAAM,CAAC6C,CAAC,CAAC;;UAEtB;UACA;UACAmE,YAAY,CAACE,EAAE,CAAC,IAAI,CAACpH,OAAO,CAAC+C,CAAC,CAAC,EAAE,CAACzB,CAAC,EAAEF,CAAC,CAAC,EAAE6F,EAAE,CAAC;QAC9C;MACF,CAAC,MAAM;QACL;QACA,IAAIhH,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI6H,GAAG,GAAGjC,EAAE,EAAEiC,GAAG,GAAGhC,EAAE,EAAEgC,GAAG,EAAE,EAAE;UAClC,IAAIC,GAAG,GAAG,IAAI,CAAC7H,MAAM,CAAC4H,GAAG,CAAC;UAC1B7H,MAAM,CAAC8H,GAAG,CAAC,GAAG,IAAI,CAAC/H,OAAO,CAAC8H,GAAG,CAAC;QACjC;;QAEA;QACA;QACA,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGnH,IAAI,EAAEmH,GAAG,EAAE,EAAE;UACnC,IAAInG,KAAK,GAAGmG,GAAG,IAAI/H,MAAM,GAAGA,MAAM,CAAC+H,GAAG,CAAC,GAAG,CAAC;UAC3Cd,YAAY,CAACE,EAAE,CAACvF,KAAK,EAAE,CAACmG,GAAG,EAAE5G,CAAC,CAAC,EAAE6F,EAAE,CAAC;QACtC;MACF;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE1H,YAAY,CAACkC,SAAS,CAACwG,MAAM,CAACC,QAAQ,CAAC,GAAG,aAAa;IACrD,IAAI,CAAC,IAAI,CAAClI,OAAO,EAAE;MACjB,MAAM,IAAIL,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACA,IAAIoB,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAChC,IAAIyE,EAAE,GAAG,IAAI,CAAC1F,IAAI,CAACiB,CAAC,CAAC;MACrB,IAAI0E,EAAE,GAAG,IAAI,CAAC3F,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC;MACzB,KAAK,IAAI2B,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;QAC5B;QACA,IAAIzB,CAAC,GAAG,IAAI,CAACpB,MAAM,CAAC6C,CAAC,CAAC;QACtB,MAAM;UACJlB,KAAK,EAAE,IAAI,CAAC7B,OAAO,CAAC+C,CAAC,CAAC;UACtBlD,KAAK,EAAE,CAACyB,CAAC,EAAEF,CAAC;QACd,CAAC;MACH;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE7B,YAAY,CAACkC,SAAS,CAACoC,OAAO,GAAG,YAAY;IAC3C,OAAOsE,QAAQ,CAAC,IAAI,CAACnI,OAAO,EAAE,IAAI,CAACE,MAAM,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC;EACzE,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEb,YAAY,CAACkC,SAAS,CAACb,OAAO,GAAG,YAAY;IAC3C,OAAOuH,QAAQ,CAAC,IAAI,CAACnI,OAAO,EAAE,IAAI,CAACE,MAAM,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,KAAK,EAAE,KAAK,CAAC;EAC1E,CAAC;EACD,SAAS+H,QAAQA,CAAClI,MAAM,EAAEJ,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEoF,IAAI,EAAE;IAChD;IACA,IAAItE,IAAI,GAAGd,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIgB,OAAO,GAAGhB,IAAI,CAAC,CAAC,CAAC;IACrB;IACA,IAAIqI,CAAC,GAAG,EAAE;IACV;IACA,IAAI9G,CAAC,EAAEF,CAAC;IACR;IACA,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,EAAES,CAAC,EAAE,EAAE;MACzB8G,CAAC,CAAC9G,CAAC,CAAC,GAAG,EAAE;MACT,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;QAC5BgH,CAAC,CAAC9G,CAAC,CAAC,CAACF,CAAC,CAAC,GAAG,CAAC;MACb;IACF;;IAEA;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAC5B;MACA,IAAIyE,EAAE,GAAG/F,GAAG,CAACsB,CAAC,CAAC;MACf,IAAI0E,EAAE,GAAGhG,GAAG,CAACsB,CAAC,GAAG,CAAC,CAAC;MACnB;MACA,KAAK,IAAI2B,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;QAC5B;QACAzB,CAAC,GAAGzB,KAAK,CAACkD,CAAC,CAAC;QACZ;QACAqF,CAAC,CAAC9G,CAAC,CAAC,CAACF,CAAC,CAAC,GAAGnB,MAAM,GAAGkF,IAAI,GAAG7G,KAAK,CAAC2B,MAAM,CAAC8C,CAAC,CAAC,CAAC,GAAG9C,MAAM,CAAC8C,CAAC,CAAC,GAAG,CAAC;MAC5D;IACF;IACA,OAAOqF,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7I,YAAY,CAACkC,SAAS,CAACpD,MAAM,GAAG,UAAUgK,OAAO,EAAE;IACjD;IACA,IAAIxH,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;IAC3B;IACA,IAAI+B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;IAC5B;IACA,IAAImG,GAAG,GAAG,iBAAiB,GAAGjK,MAAM,CAACwC,IAAI,EAAEwH,OAAO,CAAC,GAAG,KAAK,GAAGhK,MAAM,CAAC0C,OAAO,EAAEsH,OAAO,CAAC,GAAG,aAAa,GAAGhK,MAAM,CAAC8D,OAAO,EAAEkG,OAAO,CAAC,GAAG,IAAI;IACxI;IACA,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAChC;MACA,IAAIyE,EAAE,GAAG,IAAI,CAAC1F,IAAI,CAACiB,CAAC,CAAC;MACrB,IAAI0E,EAAE,GAAG,IAAI,CAAC3F,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC;MACzB;MACA,KAAK,IAAI2B,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;QAC5B;QACA,IAAIzB,CAAC,GAAG,IAAI,CAACpB,MAAM,CAAC6C,CAAC,CAAC;QACtB;QACAuF,GAAG,IAAI,SAAS,GAAGjK,MAAM,CAACiD,CAAC,EAAE+G,OAAO,CAAC,GAAG,IAAI,GAAGhK,MAAM,CAAC+C,CAAC,EAAEiH,OAAO,CAAC,GAAG,QAAQ,IAAI,IAAI,CAACrI,OAAO,GAAG3B,MAAM,CAAC,IAAI,CAAC2B,OAAO,CAAC+C,CAAC,CAAC,EAAEsF,OAAO,CAAC,GAAG,GAAG,CAAC;MACxI;IACF;IACA,OAAOC,GAAG;EACZ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE/I,YAAY,CAACkC,SAAS,CAAC8G,QAAQ,GAAG,YAAY;IAC5C,OAAOlK,MAAM,CAAC,IAAI,CAACwF,OAAO,CAAC,CAAC,CAAC;EAC/B,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEtE,YAAY,CAACkC,SAAS,CAAC+G,MAAM,GAAG,YAAY;IAC1C,OAAO;MACLC,MAAM,EAAE,cAAc;MACtBxI,MAAM,EAAE,IAAI,CAACD,OAAO;MACpBH,KAAK,EAAE,IAAI,CAACK,MAAM;MAClBJ,GAAG,EAAE,IAAI,CAACK,IAAI;MACdJ,IAAI,EAAE,IAAI,CAACK,KAAK;MAChBX,QAAQ,EAAE,IAAI,CAACY;IACjB,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEd,YAAY,CAACkC,SAAS,CAACiH,QAAQ,GAAG,UAAU3F,CAAC,EAAE;IAC7C;IACA,IAAIA,CAAC,EAAE;MACL;MACA,IAAIlF,WAAW,CAACkF,CAAC,CAAC,EAAE;QAClBA,CAAC,GAAGA,CAAC,CAAC4F,QAAQ,CAAC,CAAC;MAClB;MACA;MACA,IAAI,CAAC1K,QAAQ,CAAC8E,CAAC,CAAC,IAAI,CAAC3E,SAAS,CAAC2E,CAAC,CAAC,EAAE;QACjC,MAAM,IAAIxC,SAAS,CAAC,2CAA2C,CAAC;MAClE;IACF,CAAC,MAAM;MACL;MACAwC,CAAC,GAAG,CAAC;IACP;IACA,IAAI6F,MAAM,GAAG7F,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC;IAC1B,IAAI8F,IAAI,GAAG9F,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC;;IAEzB;IACA,IAAIlC,IAAI,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIW,OAAO,GAAG,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;;IAE3B;IACA,IAAIsF,CAAC,GAAGf,IAAI,CAAC9B,GAAG,CAAChC,IAAI,GAAGgI,IAAI,EAAE9H,OAAO,GAAG6H,MAAM,CAAC;;IAE/C;IACA,IAAI3I,MAAM,GAAG,EAAE;IACf,IAAIJ,KAAK,GAAG,EAAE;IACd,IAAIC,GAAG,GAAG,EAAE;IACZ;IACAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACV;IACA,KAAK,IAAIsB,CAAC,GAAGwH,MAAM,EAAExH,CAAC,GAAGL,OAAO,IAAId,MAAM,CAACa,MAAM,GAAG4E,CAAC,EAAEtE,CAAC,EAAE,EAAE;MAC1D;MACA,IAAIyE,EAAE,GAAG,IAAI,CAAC1F,IAAI,CAACiB,CAAC,CAAC;MACrB,IAAI0E,EAAE,GAAG,IAAI,CAAC3F,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC;MACzB;MACA,KAAK,IAAI6D,CAAC,GAAGY,EAAE,EAAEZ,CAAC,GAAGa,EAAE,EAAEb,CAAC,EAAE,EAAE;QAC5B;QACA,IAAI3D,CAAC,GAAG,IAAI,CAACpB,MAAM,CAAC+E,CAAC,CAAC;QACtB;QACA,IAAI3D,CAAC,KAAKF,CAAC,GAAGwH,MAAM,GAAGC,IAAI,EAAE;UAC3B;UACA5I,MAAM,CAACoB,IAAI,CAAC,IAAI,CAACrB,OAAO,CAACiF,CAAC,CAAC,CAAC;UAC5B;UACApF,KAAK,CAACI,MAAM,CAACa,MAAM,GAAG,CAAC,CAAC,GAAGQ,CAAC,GAAGuH,IAAI;UACnC;UACA;QACF;MACF;IACF;IACA;IACA/I,GAAG,CAACuB,IAAI,CAACpB,MAAM,CAACa,MAAM,CAAC;IACvB;IACA,OAAO,IAAIvB,YAAY,CAAC;MACtBU,MAAM;MACNJ,KAAK;MACLC,GAAG;MACHC,IAAI,EAAE,CAAC2F,CAAC,EAAE,CAAC;IACb,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEnG,YAAY,CAACuJ,QAAQ,GAAG,UAAUC,IAAI,EAAE;IACtC,OAAO,IAAIxJ,YAAY,CAACwJ,IAAI,CAAC;EAC/B,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExJ,YAAY,CAACmJ,QAAQ,GAAG,UAAU3I,IAAI,EAAE8B,KAAK,EAAEkB,CAAC,EAAET,YAAY,EAAE7C,QAAQ,EAAE;IACxE,IAAI,CAAC7B,OAAO,CAACmC,IAAI,CAAC,EAAE;MAClB,MAAM,IAAIQ,SAAS,CAAC,gCAAgC,CAAC;IACvD;IACA,IAAIR,IAAI,CAACe,MAAM,KAAK,CAAC,EAAE;MACrB,MAAM,IAAInB,KAAK,CAAC,0CAA0C,CAAC;IAC7D;;IAEA;IACAI,IAAI,GAAGA,IAAI,CAACsF,GAAG,CAAC,UAAU2D,CAAC,EAAE;MAC3B;MACA,IAAInL,WAAW,CAACmL,CAAC,CAAC,EAAE;QAClB;QACAA,CAAC,GAAGA,CAAC,CAACL,QAAQ,CAAC,CAAC;MAClB;MACA;MACA,IAAI,CAAC1K,QAAQ,CAAC+K,CAAC,CAAC,IAAI,CAAC5K,SAAS,CAAC4K,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;QAC1C,MAAM,IAAIrJ,KAAK,CAAC,uCAAuC,CAAC;MAC1D;MACA,OAAOqJ,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIjG,CAAC,EAAE;MACL;MACA,IAAIlF,WAAW,CAACkF,CAAC,CAAC,EAAE;QAClBA,CAAC,GAAGA,CAAC,CAAC4F,QAAQ,CAAC,CAAC;MAClB;MACA;MACA,IAAI,CAAC1K,QAAQ,CAAC8E,CAAC,CAAC,IAAI,CAAC3E,SAAS,CAAC2E,CAAC,CAAC,EAAE;QACjC,MAAM,IAAIxC,SAAS,CAAC,2CAA2C,CAAC;MAClE;IACF,CAAC,MAAM;MACL;MACAwC,CAAC,GAAG,CAAC;IACP;;IAEA;IACA,IAAI/B,EAAE,GAAG3B,WAAW;IACpB;IACA,IAAI4B,IAAI,GAAG,CAAC;IACZ,IAAI/C,QAAQ,CAACuB,QAAQ,CAAC,EAAE;MACtB;MACAuB,EAAE,GAAG5B,KAAK,CAAC8B,IAAI,CAAC7B,WAAW,EAAE,CAACI,QAAQ,EAAEA,QAAQ,CAAC,CAAC,IAAIJ,WAAW;MACjE;MACA4B,IAAI,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,EAAE1B,QAAQ,CAAC;IACnC;IACA,IAAImJ,MAAM,GAAG7F,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC;IAC1B,IAAI8F,IAAI,GAAG9F,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC;;IAEzB;IACA,IAAIlC,IAAI,GAAGd,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIgB,OAAO,GAAGhB,IAAI,CAAC,CAAC,CAAC;;IAErB;IACA,IAAI2F,CAAC,GAAGf,IAAI,CAAC9B,GAAG,CAAChC,IAAI,GAAGgI,IAAI,EAAE9H,OAAO,GAAG6H,MAAM,CAAC;;IAE/C;IACA,IAAIK,MAAM;;IAEV;IACA,IAAIrL,OAAO,CAACiE,KAAK,CAAC,EAAE;MAClB;MACA,IAAIA,KAAK,CAACf,MAAM,KAAK4E,CAAC,EAAE;QACtB;QACA,MAAM,IAAI/F,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MACA;MACAsJ,MAAM,GAAG,SAASA,MAAMA,CAAC3H,CAAC,EAAE;QAC1B;QACA,OAAOO,KAAK,CAACP,CAAC,CAAC;MACjB,CAAC;IACH,CAAC,MAAM,IAAItD,QAAQ,CAAC6D,KAAK,CAAC,EAAE;MAC1B;MACA,IAAIqH,EAAE,GAAGrH,KAAK,CAAC9B,IAAI,CAAC,CAAC;MACrB;MACA,IAAImJ,EAAE,CAACpI,MAAM,KAAK,CAAC,IAAIoI,EAAE,CAAC,CAAC,CAAC,KAAKxD,CAAC,EAAE;QAClC;QACA,MAAM,IAAI/F,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MACA;MACAsJ,MAAM,GAAG,SAASA,MAAMA,CAAC3H,CAAC,EAAE;QAC1B;QACA,OAAOO,KAAK,CAACe,GAAG,CAAC,CAACtB,CAAC,CAAC,CAAC;MACvB,CAAC;IACH,CAAC,MAAM;MACL;MACA2H,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QACzB;QACA,OAAOpH,KAAK;MACd,CAAC;IACH;;IAEA;IACA,IAAI5B,MAAM,GAAG,EAAE;IACf,IAAIJ,KAAK,GAAG,EAAE;IACd,IAAIC,GAAG,GAAG,EAAE;;IAEZ;IACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAChC;MACAtB,GAAG,CAACuB,IAAI,CAACpB,MAAM,CAACa,MAAM,CAAC;MACvB;MACA,IAAIQ,CAAC,GAAGF,CAAC,GAAGwH,MAAM;MAClB;MACA,IAAItH,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGoE,CAAC,EAAE;QACnB;QACA,IAAIlE,CAAC,GAAGyH,MAAM,CAAC3H,CAAC,CAAC;QACjB;QACA,IAAI,CAACN,EAAE,CAACQ,CAAC,EAAEP,IAAI,CAAC,EAAE;UAChB;UACApB,KAAK,CAACwB,IAAI,CAACC,CAAC,GAAGuH,IAAI,CAAC;UACpB;UACA5I,MAAM,CAACoB,IAAI,CAACG,CAAC,CAAC;QAChB;MACF;IACF;IACA;IACA1B,GAAG,CAACuB,IAAI,CAACpB,MAAM,CAACa,MAAM,CAAC;IACvB;IACA,OAAO,IAAIvB,YAAY,CAAC;MACtBU,MAAM;MACNJ,KAAK;MACLC,GAAG;MACHC,IAAI,EAAE,CAACc,IAAI,EAAEE,OAAO;IACtB,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExB,YAAY,CAACkC,SAAS,CAAC0H,QAAQ,GAAG,UAAU7H,CAAC,EAAEF,CAAC,EAAE;IAChD;IACA,IAAI,CAACnD,QAAQ,CAACqD,CAAC,CAAC,IAAI,CAAClD,SAAS,CAACkD,CAAC,CAAC,IAAI,CAACrD,QAAQ,CAACmD,CAAC,CAAC,IAAI,CAAChD,SAAS,CAACgD,CAAC,CAAC,EAAE;MAClE,MAAM,IAAIzB,KAAK,CAAC,qCAAqC,CAAC;IACxD;IACA;IACA,IAAI,IAAI,CAACS,KAAK,CAACU,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAInB,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IACA;IACAf,aAAa,CAAC0C,CAAC,EAAE,IAAI,CAAClB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/BxB,aAAa,CAACwC,CAAC,EAAE,IAAI,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE/B;IACAb,YAAY,CAAC6J,SAAS,CAAC9H,CAAC,EAAEF,CAAC,EAAE,IAAI,CAAChB,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACJ,OAAO,EAAE,IAAI,CAACE,MAAM,EAAE,IAAI,CAACC,IAAI,CAAC;IACjF;IACA,OAAO,IAAI;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEZ,YAAY,CAAC8J,WAAW,GAAG,UAAUjI,CAAC,EAAEnB,MAAM,EAAEJ,KAAK,EAAEC,GAAG,EAAEiH,QAAQ,EAAE;IACpE;IACA,IAAIlB,EAAE,GAAG/F,GAAG,CAACsB,CAAC,CAAC;IACf,IAAI0E,EAAE,GAAGhG,GAAG,CAACsB,CAAC,GAAG,CAAC,CAAC;;IAEnB;IACA,KAAK,IAAI2B,CAAC,GAAG8C,EAAE,EAAE9C,CAAC,GAAG+C,EAAE,EAAE/C,CAAC,EAAE,EAAE;MAC5B;MACAgE,QAAQ,CAAClH,KAAK,CAACkD,CAAC,CAAC,EAAE9C,MAAM,CAAC8C,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExD,YAAY,CAAC6J,SAAS,GAAG,UAAUnE,CAAC,EAAEyC,CAAC,EAAE3G,OAAO,EAAEd,MAAM,EAAEJ,KAAK,EAAEC,GAAG,EAAE;IACpE;IACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAChC;MACA,IAAIyE,EAAE,GAAG/F,GAAG,CAACsB,CAAC,CAAC;MACf,IAAI0E,EAAE,GAAGhG,GAAG,CAACsB,CAAC,GAAG,CAAC,CAAC;MACnB;MACA,IAAIkI,EAAE,GAAG7E,cAAc,CAACQ,CAAC,EAAEY,EAAE,EAAEC,EAAE,EAAEjG,KAAK,CAAC;MACzC;MACA,IAAI0J,EAAE,GAAG9E,cAAc,CAACiD,CAAC,EAAE7B,EAAE,EAAEC,EAAE,EAAEjG,KAAK,CAAC;MACzC;MACA,IAAIyJ,EAAE,GAAGxD,EAAE,IAAIyD,EAAE,GAAGzD,EAAE,IAAIjG,KAAK,CAACyJ,EAAE,CAAC,KAAKrE,CAAC,IAAIpF,KAAK,CAAC0J,EAAE,CAAC,KAAK7B,CAAC,EAAE;QAC5D;QACA,IAAIzH,MAAM,EAAE;UACV,IAAIuB,CAAC,GAAGvB,MAAM,CAACqJ,EAAE,CAAC;UAClBrJ,MAAM,CAACqJ,EAAE,CAAC,GAAGrJ,MAAM,CAACsJ,EAAE,CAAC;UACvBtJ,MAAM,CAACsJ,EAAE,CAAC,GAAG/H,CAAC;QAChB;QACA;QACA;MACF;MACA;MACA,IAAI8H,EAAE,GAAGxD,EAAE,IAAIjG,KAAK,CAACyJ,EAAE,CAAC,KAAKrE,CAAC,KAAKsE,EAAE,IAAIzD,EAAE,IAAIjG,KAAK,CAAC0J,EAAE,CAAC,KAAK7B,CAAC,CAAC,EAAE;QAC/D;QACA,IAAI8B,EAAE,GAAGvJ,MAAM,GAAGA,MAAM,CAACqJ,EAAE,CAAC,GAAG3I,SAAS;QACxC;QACAd,KAAK,CAACmF,MAAM,CAACuE,EAAE,EAAE,CAAC,EAAE7B,CAAC,CAAC;QACtB,IAAIzH,MAAM,EAAE;UACVA,MAAM,CAAC+E,MAAM,CAACuE,EAAE,EAAE,CAAC,EAAEC,EAAE,CAAC;QAC1B;QACA;QACA3J,KAAK,CAACmF,MAAM,CAACuE,EAAE,IAAID,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAGA,EAAE,EAAE,CAAC,CAAC;QACvC,IAAIrJ,MAAM,EAAE;UACVA,MAAM,CAAC+E,MAAM,CAACuE,EAAE,IAAID,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAGA,EAAE,EAAE,CAAC,CAAC;QAC1C;QACA;QACA;MACF;MACA;MACA,IAAIC,EAAE,GAAGzD,EAAE,IAAIjG,KAAK,CAAC0J,EAAE,CAAC,KAAK7B,CAAC,KAAK4B,EAAE,IAAIxD,EAAE,IAAIjG,KAAK,CAACyJ,EAAE,CAAC,KAAKrE,CAAC,CAAC,EAAE;QAC/D;QACA,IAAIwE,EAAE,GAAGxJ,MAAM,GAAGA,MAAM,CAACsJ,EAAE,CAAC,GAAG5I,SAAS;QACxC;QACAd,KAAK,CAACmF,MAAM,CAACsE,EAAE,EAAE,CAAC,EAAErE,CAAC,CAAC;QACtB,IAAIhF,MAAM,EAAE;UACVA,MAAM,CAAC+E,MAAM,CAACsE,EAAE,EAAE,CAAC,EAAEG,EAAE,CAAC;QAC1B;QACA;QACA5J,KAAK,CAACmF,MAAM,CAACsE,EAAE,IAAIC,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAGA,EAAE,EAAE,CAAC,CAAC;QACvC,IAAItJ,MAAM,EAAE;UACVA,MAAM,CAAC+E,MAAM,CAACsE,EAAE,IAAIC,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAGA,EAAE,EAAE,CAAC,CAAC;QAC1C;MACF;IACF;EACF,CAAC;EACD,OAAOhK,YAAY;AACrB,CAAC,EAAE;EACDmK,OAAO,EAAE;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}