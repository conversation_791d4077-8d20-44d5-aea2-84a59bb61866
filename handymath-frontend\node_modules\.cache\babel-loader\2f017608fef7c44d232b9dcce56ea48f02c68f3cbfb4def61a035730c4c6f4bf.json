{"ast": null, "code": "/**\n * This is a util function for generating a random matrix recursively.\n * @param {number[]} size\n * @param {function} random\n * @returns {Array}\n */\nexport function randomMatrix(size, random) {\n  var data = [];\n  size = size.slice(0);\n  if (size.length > 1) {\n    for (var i = 0, length = size.shift(); i < length; i++) {\n      data.push(randomMatrix(size, random));\n    }\n  } else {\n    for (var _i = 0, _length = size.shift(); _i < _length; _i++) {\n      data.push(random());\n    }\n  }\n  return data;\n}", "map": {"version": 3, "names": ["randomMatrix", "size", "random", "data", "slice", "length", "i", "shift", "push", "_i", "_length"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/probability/util/randomMatrix.js"], "sourcesContent": ["/**\n * This is a util function for generating a random matrix recursively.\n * @param {number[]} size\n * @param {function} random\n * @returns {Array}\n */\nexport function randomMatrix(size, random) {\n  var data = [];\n  size = size.slice(0);\n  if (size.length > 1) {\n    for (var i = 0, length = size.shift(); i < length; i++) {\n      data.push(randomMatrix(size, random));\n    }\n  } else {\n    for (var _i = 0, _length = size.shift(); _i < _length; _i++) {\n      data.push(random());\n    }\n  }\n  return data;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACzC,IAAIC,IAAI,GAAG,EAAE;EACbF,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;EACpB,IAAIH,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,MAAM,GAAGJ,IAAI,CAACM,KAAK,CAAC,CAAC,EAAED,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;MACtDH,IAAI,CAACK,IAAI,CAACR,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;IACvC;EACF,CAAC,MAAM;IACL,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEC,OAAO,GAAGT,IAAI,CAACM,KAAK,CAAC,CAAC,EAAEE,EAAE,GAAGC,OAAO,EAAED,EAAE,EAAE,EAAE;MAC3DN,IAAI,CAACK,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC;IACrB;EACF;EACA,OAAOC,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}