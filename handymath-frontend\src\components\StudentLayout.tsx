import React from 'react';
import StudentBreadcrumbs from './StudentBreadcrumbs';
import FloatingNavigation from './FloatingNavigation';
import Footer from './Footer';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigate } from 'react-router-dom';

interface StudentLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showBreadcrumbs?: boolean;
  breadcrumbItems?: Array<{
    label: string;
    path?: string;
    icon?: string;
    isActive?: boolean;
  }>;
  actions?: React.ReactNode;
  className?: string;
  showFloatingNav?: boolean;
}

const StudentLayout: React.FC<StudentLayoutProps> = ({
  children,
  title,
  subtitle,
  showBreadcrumbs = true,
  breadcrumbItems,
  actions,
  className = '',
  showFloatingNav = true
}) => {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Navigation flottante pour l'espace étudiant
  const floatingNavItems = [
    {
      id: 'dashboard',
      label: 'Tableau de bord',
      path: '/etudiant/dashboard',
      icon: 'DB',
      shortcut: 'd'
    },
    {
      id: 'courses',
      label: 'Mes Cours',
      path: '/courses',
      icon: 'CO',
      shortcut: 'c'
    },
    {
      id: 'exercises',
      label: 'Exercices',
      path: '/exercises',
      icon: 'EX',
      shortcut: 'e'
    },
    {
      id: 'progress',
      label: 'Ma Progression',
      path: '/progress',
      icon: 'PR',
      shortcut: 'p'
    },
    {
      id: 'solver',
      label: 'Résolveur',
      path: '/solver',
      icon: 'SO',
      shortcut: 's'
    },
    {
      id: 'visualizer',
      label: 'Visualiseur',
      path: '/visualizer',
      icon: 'VI',
      shortcut: 'v'
    },
    // Séparateur
    { id: 'separator-1', label: '', path: '', icon: '', isSeparator: true },
    // Section Utilisateur
    {
      id: 'profile',
      label: 'Mon Profil',
      path: '/profile',
      icon: 'US',
      shortcut: 'u'
    },
    {
      id: 'settings',
      label: 'Paramètres',
      path: '/settings',
      icon: 'SE',
      shortcut: 't'
    },
    // Séparateur
    { id: 'separator-2', label: '', path: '', icon: '', isSeparator: true },
    // Actions
    {
      id: 'theme',
      label: theme === 'light' ? 'Mode Sombre' : 'Mode Clair',
      path: '',
      icon: theme === 'light' ? 'MO' : 'SU',
      shortcut: 'm',
      isAction: true,
      action: toggleTheme
    },
    {
      id: 'logout',
      label: 'Déconnexion',
      path: '',
      icon: 'LO',
      shortcut: 'q',
      isAction: true,
      action: handleLogout
    }
  ];
  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Contenu principal */}
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Breadcrumbs */}
          {showBreadcrumbs && (
            <div className="mb-6">
              <StudentBreadcrumbs items={breadcrumbItems} />
            </div>
          )}
          
          {/* En-tête de page */}
          {(title || subtitle || actions) && (
            <div className="mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div className="flex-1">
                  {title && (
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                      {title}
                    </h1>
                  )}
                  {subtitle && (
                    <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
                      {subtitle}
                    </p>
                  )}
                </div>
                
                {/* Actions */}
                {actions && (
                  <div className="flex-shrink-0">
                    {actions}
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Contenu de la page */}
          <div className={className}>
            {children}
          </div>
        </div>
      </main>

      {/* Footer */}
      <Footer />

      {/* Navigation flottante */}
      {showFloatingNav && (
        <FloatingNavigation
          items={floatingNavItems}
          position="bottom-right"
        />
      )}
    </div>
  );
};

export default StudentLayout;
