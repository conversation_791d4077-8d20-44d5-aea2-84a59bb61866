"""
Tests de base pour l'API HandyMath
"""
import pytest
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse


@pytest.mark.django_db
class TestAPIBasics:
    """Tests de base pour l'API"""
    
    def test_api_client_creation(self):
        """Test de création du client API"""
        client = APIClient()
        assert client is not None
    
    def test_api_root_endpoint(self):
        """Test de l'endpoint racine de l'API"""
        client = APIClient()
        
        try:
            response = client.get('/api/')
            # L'endpoint peut retourner 200, 404, ou autre selon la configuration
            assert response.status_code in [200, 404, 405]
        except Exception:
            # Si l'endpoint n'existe pas, c'est normal pour ce test de base
            pass
    
    def test_django_settings_loaded(self):
        """Test que les settings Django sont chargés"""
        from django.conf import settings
        
        assert hasattr(settings, 'INSTALLED_APPS')
        assert hasattr(settings, 'DATABASES')
        assert 'rest_framework' in settings.INSTALLED_APPS


@pytest.mark.django_db
class TestUserModel:
    """Tests pour le modèle utilisateur personnalisé"""
    
    def test_custom_user_model_import(self):
        """Test d'import du modèle utilisateur personnalisé"""
        try:
            from api.models import User
            assert User is not None
        except ImportError:
            # Si le modèle n'existe pas encore, on skip le test
            pytest.skip("Modèle User personnalisé non trouvé")
    
    def test_user_creation_with_custom_model(self):
        """Test de création d'utilisateur avec le modèle personnalisé"""
        try:
            from api.models import User
            
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpassword123'
            )
            
            assert user.id is not None
            assert user.username == 'testuser'
            assert user.email == '<EMAIL>'
            
        except ImportError:
            pytest.skip("Modèle User personnalisé non trouvé")
        except Exception as e:
            pytest.skip(f"Erreur lors de la création d'utilisateur: {e}")


@pytest.mark.unit
class TestAPIUtilities:
    """Tests pour les utilitaires de l'API"""
    
    def test_response_formatting(self):
        """Test de formatage des réponses API"""
        
        def format_api_response(data, success=True, message=None):
            """Formater une réponse API standard"""
            response = {
                'success': success,
                'data': data
            }
            if message:
                response['message'] = message
            return response
        
        # Test de réponse de succès
        success_response = format_api_response({'result': 'test'}, True, 'Success')
        assert success_response['success'] is True
        assert success_response['data']['result'] == 'test'
        assert success_response['message'] == 'Success'
        
        # Test de réponse d'erreur
        error_response = format_api_response(None, False, 'Error occurred')
        assert error_response['success'] is False
        assert error_response['data'] is None
        assert error_response['message'] == 'Error occurred'
    
    def test_equation_data_validation(self):
        """Test de validation des données d'équation"""
        
        def validate_equation_data(data):
            """Valider les données d'équation pour l'API"""
            if not isinstance(data, dict):
                return False, "Les données doivent être un dictionnaire"
            
            if 'equation' not in data:
                return False, "Le champ 'equation' est requis"
            
            equation = data['equation']
            if not equation or not equation.strip():
                return False, "L'équation ne peut pas être vide"
            
            if '=' not in equation:
                return False, "L'équation doit contenir le signe '='"
            
            return True, "Données valides"
        
        # Tests de validation
        valid_data = {'equation': '2x + 3 = 11'}
        is_valid, message = validate_equation_data(valid_data)
        assert is_valid is True
        assert message == "Données valides"
        
        # Test avec données invalides
        invalid_data = {'equation': ''}
        is_valid, message = validate_equation_data(invalid_data)
        assert is_valid is False
        assert "vide" in message
        
        # Test sans champ equation
        missing_field = {'other_field': 'value'}
        is_valid, message = validate_equation_data(missing_field)
        assert is_valid is False
        assert "requis" in message
    
    def test_pagination_helpers(self):
        """Test des helpers de pagination"""
        
        def paginate_results(results, page=1, page_size=10):
            """Helper de pagination simple"""
            start = (page - 1) * page_size
            end = start + page_size
            
            paginated_results = results[start:end]
            
            return {
                'results': paginated_results,
                'page': page,
                'page_size': page_size,
                'total': len(results),
                'has_next': end < len(results),
                'has_previous': page > 1
            }
        
        # Test de pagination
        test_data = list(range(25))  # 25 éléments
        
        # Page 1
        page1 = paginate_results(test_data, page=1, page_size=10)
        assert len(page1['results']) == 10
        assert page1['page'] == 1
        assert page1['has_next'] is True
        assert page1['has_previous'] is False
        
        # Page 2
        page2 = paginate_results(test_data, page=2, page_size=10)
        assert len(page2['results']) == 10
        assert page2['has_next'] is True
        assert page2['has_previous'] is True
        
        # Dernière page
        page3 = paginate_results(test_data, page=3, page_size=10)
        assert len(page3['results']) == 5
        assert page3['has_next'] is False
        assert page3['has_previous'] is True


@pytest.mark.unit
class TestMathAPILogic:
    """Tests pour la logique mathématique de l'API"""
    
    def test_equation_solving_api_logic(self):
        """Test de la logique de résolution d'équations pour l'API"""
        
        def solve_equation_for_api(equation_str):
            """Résoudre une équation et formater pour l'API"""
            import sympy as sp
            
            try:
                # Validation
                if not equation_str or '=' not in equation_str:
                    return {
                        'success': False,
                        'error': 'Équation invalide'
                    }
                
                # Résolution
                x = sp.Symbol('x')
                left, right = equation_str.split('=')
                left_expr = sp.sympify(left.strip())
                right_expr = sp.sympify(right.strip())
                equation = sp.Eq(left_expr, right_expr)
                solution = sp.solve(equation, x)
                
                # Formatage pour l'API
                return {
                    'success': True,
                    'equation': equation_str,
                    'solution': [float(sol) if sol.is_real else str(sol) for sol in solution],
                    'steps': [
                        f"Équation: {equation_str}",
                        f"Solution: x = {solution}"
                    ]
                }
                
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Erreur de résolution: {str(e)}'
                }
        
        # Test avec équation valide
        result1 = solve_equation_for_api("2*x + 3 = 11")
        assert result1['success'] is True
        assert result1['solution'] == [4.0]
        assert 'steps' in result1
        
        # Test avec équation invalide
        result2 = solve_equation_for_api("invalid")
        assert result2['success'] is False
        assert 'error' in result2
    
    def test_ocr_result_processing(self):
        """Test de traitement des résultats OCR"""
        
        def process_ocr_result(ocr_text, confidence=0.0):
            """Traiter le résultat OCR pour l'API"""
            
            # Nettoyage du texte
            cleaned_text = ocr_text.strip()
            
            # Validation de base
            if not cleaned_text:
                return {
                    'success': False,
                    'error': 'Aucun texte détecté'
                }
            
            # Vérification de la confiance
            if confidence < 0.5:
                return {
                    'success': True,
                    'text': cleaned_text,
                    'confidence': confidence,
                    'warning': 'Confiance faible dans la reconnaissance'
                }
            
            return {
                'success': True,
                'text': cleaned_text,
                'confidence': confidence
            }
        
        # Test avec texte valide et bonne confiance
        result1 = process_ocr_result("2x + 3 = 11", 0.95)
        assert result1['success'] is True
        assert result1['text'] == "2x + 3 = 11"
        assert result1['confidence'] == 0.95
        assert 'warning' not in result1
        
        # Test avec confiance faible
        result2 = process_ocr_result("x + 1 = 5", 0.3)
        assert result2['success'] is True
        assert 'warning' in result2
        
        # Test avec texte vide
        result3 = process_ocr_result("", 0.9)
        assert result3['success'] is False
        assert 'error' in result3


@pytest.mark.integration
class TestAPIIntegration:
    """Tests d'intégration pour l'API"""
    
    def test_equation_solving_pipeline(self):
        """Test du pipeline complet de résolution d'équation"""
        
        def complete_equation_pipeline(equation_data):
            """Pipeline complet: validation -> résolution -> formatage"""
            
            # 1. Validation des données d'entrée
            if not isinstance(equation_data, dict) or 'equation' not in equation_data:
                return {
                    'success': False,
                    'error': 'Données invalides'
                }
            
            equation_str = equation_data['equation']
            
            # 2. Validation de l'équation
            if not equation_str or '=' not in equation_str:
                return {
                    'success': False,
                    'error': 'Équation invalide'
                }
            
            # 3. Résolution
            import sympy as sp
            try:
                x = sp.Symbol('x')
                left, right = equation_str.split('=')
                left_expr = sp.sympify(left.strip())
                right_expr = sp.sympify(right.strip())
                equation = sp.Eq(left_expr, right_expr)
                solution = sp.solve(equation, x)
                
                # 4. Formatage de la réponse
                return {
                    'success': True,
                    'data': {
                        'original_equation': equation_str,
                        'solution': solution,
                        'solution_count': len(solution),
                        'steps': [
                            f"Équation donnée: {equation_str}",
                            f"Résolution: {equation}",
                            f"Solution(s): {solution}"
                        ]
                    }
                }
                
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Erreur de calcul: {str(e)}'
                }
        
        # Test du pipeline complet
        test_cases = [
            {'equation': 'x + 1 = 5'},
            {'equation': '2*x + 3 = 11'},
            {'equation': 'x**2 - 4 = 0'}
        ]
        
        for test_case in test_cases:
            result = complete_equation_pipeline(test_case)
            assert result['success'] is True
            assert 'data' in result
            assert 'solution' in result['data']
            assert 'steps' in result['data']
        
        # Test avec données invalides
        invalid_result = complete_equation_pipeline({'invalid': 'data'})
        assert invalid_result['success'] is False
        assert 'error' in invalid_result
