{"ast": null, "code": "export var numberDocs = {\n  name: 'number',\n  category: 'Construction',\n  syntax: ['x', 'number(x)', 'number(unit, valuelessUnit)'],\n  description: 'Create a number or convert a string or boolean into a number.',\n  examples: ['2', '2e3', '4.05', 'number(2)', 'number(\"7.2\")', 'number(true)', 'number([true, false, true, true])', 'number(unit(\"52cm\"), \"m\")'],\n  seealso: ['bignumber', 'bigint', 'boolean', 'complex', 'fraction', 'index', 'matrix', 'string', 'unit']\n};", "map": {"version": 3, "names": ["numberDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/number.js"], "sourcesContent": ["export var numberDocs = {\n  name: 'number',\n  category: 'Construction',\n  syntax: ['x', 'number(x)', 'number(unit, valuelessUnit)'],\n  description: 'Create a number or convert a string or boolean into a number.',\n  examples: ['2', '2e3', '4.05', 'number(2)', 'number(\"7.2\")', 'number(true)', 'number([true, false, true, true])', 'number(unit(\"52cm\"), \"m\")'],\n  seealso: ['bignumber', 'bigint', 'boolean', 'complex', 'fraction', 'index', 'matrix', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,6BAA6B,CAAC;EACzDC,WAAW,EAAE,+DAA+D;EAC5EC,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,mCAAmC,EAAE,2BAA2B,CAAC;EAC9IC,OAAO,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AACxG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}