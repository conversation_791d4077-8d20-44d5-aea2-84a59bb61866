{"ast": null, "code": "export var usolveAllDocs = {\n  name: 'usolveAll',\n  category: 'Algebra',\n  syntax: ['x=usolve(U, b)'],\n  description: 'Finds all solutions of the linear system U * x = b where U is an [n x n] upper triangular matrix and b is a [n] column vector.',\n  examples: ['x=usolve(sparse([1, 1, 1, 1; 0, 1, 1, 1; 0, 0, 1, 1; 0, 0, 0, 1]), [1; 2; 3; 4])'],\n  seealso: ['usolve', 'lup', 'lusolve', 'lsolve', 'matrix', 'sparse']\n};", "map": {"version": 3, "names": ["usolveAllDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/usolveAll.js"], "sourcesContent": ["export var usolveAllDocs = {\n  name: 'usolveAll',\n  category: 'Algebra',\n  syntax: ['x=usolve(U, b)'],\n  description: 'Finds all solutions of the linear system U * x = b where U is an [n x n] upper triangular matrix and b is a [n] column vector.',\n  examples: ['x=usolve(sparse([1, 1, 1, 1; 0, 1, 1, 1; 0, 0, 1, 1; 0, 0, 0, 1]), [1; 2; 3; 4])'],\n  seealso: ['usolve', 'lup', 'lusolve', 'lsolve', 'matrix', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gBAAgB,CAAC;EAC1BC,WAAW,EAAE,gIAAgI;EAC7IC,QAAQ,EAAE,CAAC,kFAAkF,CAAC;EAC9FC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}