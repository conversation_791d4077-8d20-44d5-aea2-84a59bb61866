from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.routers import Default<PERSON>outer
from rest_framework_simplejwt.views import TokenRefreshView
from api.authentication import CustomTokenObtainPairView
from api.views import (
    UserViewSet, CourseViewSet, EquationViewSet, ExerciseViewSet,
    health_check, current_user, test_api, solve_equation, recognize_equation_from_image,
    get_course_recommendations, get_learning_path, get_daily_challenge, get_study_insights,
    get_admin_dashboard, get_user_management, toggle_user_status, get_system_health,
    get_exercises, get_exercise_detail, start_exercise, submit_exercise,
    get_user_progress, get_leaderboard, get_user_gamification,
    get_structured_courses, get_course_detail, get_lesson_detail,
    enroll_in_course, complete_lesson,
    # Vues pour les messages de contact
    contact_message_create, contact_messages_list, contact_message_detail,
    contact_message_update, contact_message_mark_resolved, contact_messages_stats
)

# Créer un routeur pour les vues de l'API
router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'courses', CourseViewSet)
router.register(r'equations', EquationViewSet)
router.register(r'exercises', ExerciseViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/health/', health_check, name='health_check'),
    path('api/test/', test_api, name='test_api'),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/users/me/', current_user, name='current_user'),
    path('api/equations/solve/', solve_equation, name='solve_equation'),
    path('api/equations/recognize/', recognize_equation_from_image, name='recognize_equation'),
    path('api/courses/recommendations/', get_course_recommendations, name='course_recommendations'),
    path('api/learning/path/', get_learning_path, name='learning_path'),
    path('api/learning/daily-challenge/', get_daily_challenge, name='daily_challenge'),
    path('api/learning/insights/', get_study_insights, name='study_insights'),
    path('api/admin/dashboard/', get_admin_dashboard, name='admin_dashboard'),
    path('api/admin/users/', get_user_management, name='user_management'),
    path('api/admin/users/<int:user_id>/toggle/', toggle_user_status, name='toggle_user_status'),
    path('api/admin/system-health/', get_system_health, name='system_health'),
    path('api/exercises/', get_exercises, name='get_exercises'),
    path('api/exercises/<int:exercise_id>/', get_exercise_detail, name='get_exercise_detail'),
    path('api/exercises/<int:exercise_id>/start/', start_exercise, name='start_exercise'),
    path('api/exercises/<int:exercise_id>/submit/', submit_exercise, name='submit_exercise'),
    path('api/progress/', get_user_progress, name='get_user_progress'),
    path('api/leaderboard/', get_leaderboard, name='get_leaderboard'),
    path('api/gamification/', get_user_gamification, name='get_user_gamification'),
    path('api/courses/', get_structured_courses, name='get_structured_courses'),
    path('api/courses/<int:course_id>/', get_course_detail, name='get_course_detail'),
    path('api/courses/<int:course_id>/enroll/', enroll_in_course, name='enroll_in_course'),
    path('api/lessons/<int:lesson_id>/', get_lesson_detail, name='get_lesson_detail'),
    path('api/lessons/<int:lesson_id>/complete/', complete_lesson, name='complete_lesson'),

    # URLs pour les messages de contact
    path('api/contact/send/', contact_message_create, name='contact_message_create'),
    path('api/admin/contact/messages/', contact_messages_list, name='contact_messages_list'),
    path('api/admin/contact/messages/<int:message_id>/', contact_message_detail, name='contact_message_detail'),
    path('api/admin/contact/messages/<int:message_id>/update/', contact_message_update, name='contact_message_update'),
    path('api/admin/contact/messages/<int:message_id>/resolve/', contact_message_mark_resolved, name='contact_message_mark_resolved'),
    path('api/admin/contact/stats/', contact_messages_stats, name='contact_messages_stats'),

    path('api/', include(router.urls)),
    path('api-auth/', include('rest_framework.urls')),
]

# Servir les fichiers statiques et médias en développement
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

