import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface TabItem {
  id: string;
  label: string;
  icon?: string;
  badge?: string | number;
  content: React.ReactNode;
  disabled?: boolean;
}

interface TabNavigationProps {
  tabs: TabItem[];
  defaultTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  variant?: 'default' | 'pills' | 'underline';
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  defaultTab,
  onTabChange,
  className = '',
  variant = 'default'
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id || '');

  const handleTabChange = (tabId: string) => {
    if (tabs.find(tab => tab.id === tabId)?.disabled) return;
    
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const getTabClasses = (tab: TabItem, isActive: boolean) => {
    const baseClasses = 'relative flex items-center px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2';
    
    if (tab.disabled) {
      return `${baseClasses} text-gray-400 dark:text-gray-600 cursor-not-allowed`;
    }

    switch (variant) {
      case 'pills':
        return `${baseClasses} rounded-lg ${
          isActive
            ? 'bg-primary-600 text-white shadow-sm'
            : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'
        }`;
      
      case 'underline':
        return `${baseClasses} border-b-2 ${
          isActive
            ? 'border-primary-600 text-primary-600 dark:text-primary-400'
            : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:border-gray-300 dark:hover:border-gray-600'
        }`;
      
      default:
        return `${baseClasses} rounded-t-lg border-l border-r border-t ${
          isActive
            ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-primary-600 dark:text-primary-400 -mb-px'
            : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
        }`;
    }
  };

  const getContainerClasses = () => {
    switch (variant) {
      case 'pills':
        return 'flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg';
      case 'underline':
        return 'flex space-x-8 border-b border-gray-200 dark:border-gray-700';
      default:
        return 'flex space-x-1';
    }
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className={`tab-navigation ${className}`}>
      {/* Tab Headers */}
      <div className={getContainerClasses()}>
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id;
          
          return (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={getTabClasses(tab, isActive)}
              disabled={tab.disabled}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
            >
              {/* Icon */}
              {tab.icon && (
                <span className="mr-2 text-lg">{tab.icon}</span>
              )}
              
              {/* Label */}
              <span>{tab.label}</span>
              
              {/* Badge */}
              {tab.badge && (
                <span className={`ml-2 min-w-[1.25rem] h-5 flex items-center justify-center text-xs font-bold rounded-full ${
                  isActive && variant === 'pills'
                    ? 'bg-white text-primary-600'
                    : 'bg-red-500 text-white'
                }`}>
                  {typeof tab.badge === 'number' && tab.badge > 99 ? '99+' : tab.badge}
                </span>
              )}
              
              {/* Active indicator for underline variant */}
              {isActive && variant === 'underline' && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600"
                  layoutId="activeTab"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className={`tab-content mt-6 ${variant === 'default' ? 'border border-gray-300 dark:border-gray-600 border-t-0 rounded-b-lg bg-white dark:bg-gray-800 p-6' : ''}`}>
        <div
          id={`tabpanel-${activeTab}`}
          role="tabpanel"
          aria-labelledby={`tab-${activeTab}`}
        >
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {activeTabContent}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default TabNavigation;
