{"ast": null, "code": "import { isArray, isMatrix, isDenseMatrix, isSparseMatrix } from '../../../../utils/is.js';\nimport { arraySize } from '../../../../utils/array.js';\nimport { format } from '../../../../utils/string.js';\nexport function createSolveValidation(_ref) {\n  var {\n    DenseMatrix\n  } = _ref;\n  /**\n   * Validates matrix and column vector b for backward/forward substitution algorithms.\n   *\n   * @param {Matrix} m            An N x N matrix\n   * @param {Array | Matrix} b    A column vector\n   * @param {Boolean} copy        Return a copy of vector b\n   *\n   * @return {DenseMatrix}        Dense column vector b\n   */\n  return function solveValidation(m, b, copy) {\n    var mSize = m.size();\n    if (mSize.length !== 2) {\n      throw new RangeError('Matrix must be two dimensional (size: ' + format(mSize) + ')');\n    }\n    var rows = mSize[0];\n    var columns = mSize[1];\n    if (rows !== columns) {\n      throw new RangeError('Matrix must be square (size: ' + format(mSize) + ')');\n    }\n    var data = [];\n    if (isMatrix(b)) {\n      var bSize = b.size();\n      var bdata = b._data;\n\n      // 1-dim vector\n      if (bSize.length === 1) {\n        if (bSize[0] !== rows) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        for (var i = 0; i < rows; i++) {\n          data[i] = [bdata[i]];\n        }\n        return new DenseMatrix({\n          data,\n          size: [rows, 1],\n          datatype: b._datatype\n        });\n      }\n\n      // 2-dim column\n      if (bSize.length === 2) {\n        if (bSize[0] !== rows || bSize[1] !== 1) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        if (isDenseMatrix(b)) {\n          if (copy) {\n            data = [];\n            for (var _i = 0; _i < rows; _i++) {\n              data[_i] = [bdata[_i][0]];\n            }\n            return new DenseMatrix({\n              data,\n              size: [rows, 1],\n              datatype: b._datatype\n            });\n          }\n          return b;\n        }\n        if (isSparseMatrix(b)) {\n          for (var _i2 = 0; _i2 < rows; _i2++) {\n            data[_i2] = [0];\n          }\n          var values = b._values;\n          var index = b._index;\n          var ptr = b._ptr;\n          for (var k1 = ptr[1], k = ptr[0]; k < k1; k++) {\n            var _i3 = index[k];\n            data[_i3][0] = values[k];\n          }\n          return new DenseMatrix({\n            data,\n            size: [rows, 1],\n            datatype: b._datatype\n          });\n        }\n      }\n      throw new RangeError('Dimension mismatch. The right side has to be either 1- or 2-dimensional vector.');\n    }\n    if (isArray(b)) {\n      var bsize = arraySize(b);\n      if (bsize.length === 1) {\n        if (bsize[0] !== rows) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        for (var _i4 = 0; _i4 < rows; _i4++) {\n          data[_i4] = [b[_i4]];\n        }\n        return new DenseMatrix({\n          data,\n          size: [rows, 1]\n        });\n      }\n      if (bsize.length === 2) {\n        if (bsize[0] !== rows || bsize[1] !== 1) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        for (var _i5 = 0; _i5 < rows; _i5++) {\n          data[_i5] = [b[_i5][0]];\n        }\n        return new DenseMatrix({\n          data,\n          size: [rows, 1]\n        });\n      }\n      throw new RangeError('Dimension mismatch. The right side has to be either 1- or 2-dimensional vector.');\n    }\n  };\n}", "map": {"version": 3, "names": ["isArray", "isMatrix", "isDenseMatrix", "isSparseMatrix", "arraySize", "format", "createSolveValidation", "_ref", "DenseMatrix", "solveValidation", "m", "b", "copy", "mSize", "size", "length", "RangeError", "rows", "columns", "data", "bSize", "bdata", "_data", "i", "datatype", "_datatype", "_i", "_i2", "values", "_values", "index", "_index", "ptr", "_ptr", "k1", "k", "_i3", "bsize", "_i4", "_i5"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/solver/utils/solveValidation.js"], "sourcesContent": ["import { isArray, isMatrix, isDenseMatrix, isSparseMatrix } from '../../../../utils/is.js';\nimport { arraySize } from '../../../../utils/array.js';\nimport { format } from '../../../../utils/string.js';\nexport function createSolveValidation(_ref) {\n  var {\n    DenseMatrix\n  } = _ref;\n  /**\n   * Validates matrix and column vector b for backward/forward substitution algorithms.\n   *\n   * @param {Matrix} m            An N x N matrix\n   * @param {Array | Matrix} b    A column vector\n   * @param {Boolean} copy        Return a copy of vector b\n   *\n   * @return {DenseMatrix}        Dense column vector b\n   */\n  return function solveValidation(m, b, copy) {\n    var mSize = m.size();\n    if (mSize.length !== 2) {\n      throw new RangeError('Matrix must be two dimensional (size: ' + format(mSize) + ')');\n    }\n    var rows = mSize[0];\n    var columns = mSize[1];\n    if (rows !== columns) {\n      throw new RangeError('Matrix must be square (size: ' + format(mSize) + ')');\n    }\n    var data = [];\n    if (isMatrix(b)) {\n      var bSize = b.size();\n      var bdata = b._data;\n\n      // 1-dim vector\n      if (bSize.length === 1) {\n        if (bSize[0] !== rows) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        for (var i = 0; i < rows; i++) {\n          data[i] = [bdata[i]];\n        }\n        return new DenseMatrix({\n          data,\n          size: [rows, 1],\n          datatype: b._datatype\n        });\n      }\n\n      // 2-dim column\n      if (bSize.length === 2) {\n        if (bSize[0] !== rows || bSize[1] !== 1) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        if (isDenseMatrix(b)) {\n          if (copy) {\n            data = [];\n            for (var _i = 0; _i < rows; _i++) {\n              data[_i] = [bdata[_i][0]];\n            }\n            return new DenseMatrix({\n              data,\n              size: [rows, 1],\n              datatype: b._datatype\n            });\n          }\n          return b;\n        }\n        if (isSparseMatrix(b)) {\n          for (var _i2 = 0; _i2 < rows; _i2++) {\n            data[_i2] = [0];\n          }\n          var values = b._values;\n          var index = b._index;\n          var ptr = b._ptr;\n          for (var k1 = ptr[1], k = ptr[0]; k < k1; k++) {\n            var _i3 = index[k];\n            data[_i3][0] = values[k];\n          }\n          return new DenseMatrix({\n            data,\n            size: [rows, 1],\n            datatype: b._datatype\n          });\n        }\n      }\n      throw new RangeError('Dimension mismatch. The right side has to be either 1- or 2-dimensional vector.');\n    }\n    if (isArray(b)) {\n      var bsize = arraySize(b);\n      if (bsize.length === 1) {\n        if (bsize[0] !== rows) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        for (var _i4 = 0; _i4 < rows; _i4++) {\n          data[_i4] = [b[_i4]];\n        }\n        return new DenseMatrix({\n          data,\n          size: [rows, 1]\n        });\n      }\n      if (bsize.length === 2) {\n        if (bsize[0] !== rows || bsize[1] !== 1) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n        for (var _i5 = 0; _i5 < rows; _i5++) {\n          data[_i5] = [b[_i5][0]];\n        }\n        return new DenseMatrix({\n          data,\n          size: [rows, 1]\n        });\n      }\n      throw new RangeError('Dimension mismatch. The right side has to be either 1- or 2-dimensional vector.');\n    }\n  };\n}"], "mappings": "AAAA,SAASA,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,cAAc,QAAQ,yBAAyB;AAC1F,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,MAAM,QAAQ,6BAA6B;AACpD,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAE;IAC1C,IAAIC,KAAK,GAAGH,CAAC,CAACI,IAAI,CAAC,CAAC;IACpB,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIC,UAAU,CAAC,wCAAwC,GAAGX,MAAM,CAACQ,KAAK,CAAC,GAAG,GAAG,CAAC;IACtF;IACA,IAAII,IAAI,GAAGJ,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIK,OAAO,GAAGL,KAAK,CAAC,CAAC,CAAC;IACtB,IAAII,IAAI,KAAKC,OAAO,EAAE;MACpB,MAAM,IAAIF,UAAU,CAAC,+BAA+B,GAAGX,MAAM,CAACQ,KAAK,CAAC,GAAG,GAAG,CAAC;IAC7E;IACA,IAAIM,IAAI,GAAG,EAAE;IACb,IAAIlB,QAAQ,CAACU,CAAC,CAAC,EAAE;MACf,IAAIS,KAAK,GAAGT,CAAC,CAACG,IAAI,CAAC,CAAC;MACpB,IAAIO,KAAK,GAAGV,CAAC,CAACW,KAAK;;MAEnB;MACA,IAAIF,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;QACtB,IAAIK,KAAK,CAAC,CAAC,CAAC,KAAKH,IAAI,EAAE;UACrB,MAAM,IAAID,UAAU,CAAC,8DAA8D,CAAC;QACtF;QACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,EAAEM,CAAC,EAAE,EAAE;UAC7BJ,IAAI,CAACI,CAAC,CAAC,GAAG,CAACF,KAAK,CAACE,CAAC,CAAC,CAAC;QACtB;QACA,OAAO,IAAIf,WAAW,CAAC;UACrBW,IAAI;UACJL,IAAI,EAAE,CAACG,IAAI,EAAE,CAAC,CAAC;UACfO,QAAQ,EAAEb,CAAC,CAACc;QACd,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIL,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;QACtB,IAAIK,KAAK,CAAC,CAAC,CAAC,KAAKH,IAAI,IAAIG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UACvC,MAAM,IAAIJ,UAAU,CAAC,8DAA8D,CAAC;QACtF;QACA,IAAId,aAAa,CAACS,CAAC,CAAC,EAAE;UACpB,IAAIC,IAAI,EAAE;YACRO,IAAI,GAAG,EAAE;YACT,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGT,IAAI,EAAES,EAAE,EAAE,EAAE;cAChCP,IAAI,CAACO,EAAE,CAAC,GAAG,CAACL,KAAK,CAACK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B;YACA,OAAO,IAAIlB,WAAW,CAAC;cACrBW,IAAI;cACJL,IAAI,EAAE,CAACG,IAAI,EAAE,CAAC,CAAC;cACfO,QAAQ,EAAEb,CAAC,CAACc;YACd,CAAC,CAAC;UACJ;UACA,OAAOd,CAAC;QACV;QACA,IAAIR,cAAc,CAACQ,CAAC,CAAC,EAAE;UACrB,KAAK,IAAIgB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGV,IAAI,EAAEU,GAAG,EAAE,EAAE;YACnCR,IAAI,CAACQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UACjB;UACA,IAAIC,MAAM,GAAGjB,CAAC,CAACkB,OAAO;UACtB,IAAIC,KAAK,GAAGnB,CAAC,CAACoB,MAAM;UACpB,IAAIC,GAAG,GAAGrB,CAAC,CAACsB,IAAI;UAChB,KAAK,IAAIC,EAAE,GAAGF,GAAG,CAAC,CAAC,CAAC,EAAEG,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,EAAEG,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;YAC7C,IAAIC,GAAG,GAAGN,KAAK,CAACK,CAAC,CAAC;YAClBhB,IAAI,CAACiB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGR,MAAM,CAACO,CAAC,CAAC;UAC1B;UACA,OAAO,IAAI3B,WAAW,CAAC;YACrBW,IAAI;YACJL,IAAI,EAAE,CAACG,IAAI,EAAE,CAAC,CAAC;YACfO,QAAQ,EAAEb,CAAC,CAACc;UACd,CAAC,CAAC;QACJ;MACF;MACA,MAAM,IAAIT,UAAU,CAAC,iFAAiF,CAAC;IACzG;IACA,IAAIhB,OAAO,CAACW,CAAC,CAAC,EAAE;MACd,IAAI0B,KAAK,GAAGjC,SAAS,CAACO,CAAC,CAAC;MACxB,IAAI0B,KAAK,CAACtB,MAAM,KAAK,CAAC,EAAE;QACtB,IAAIsB,KAAK,CAAC,CAAC,CAAC,KAAKpB,IAAI,EAAE;UACrB,MAAM,IAAID,UAAU,CAAC,8DAA8D,CAAC;QACtF;QACA,KAAK,IAAIsB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGrB,IAAI,EAAEqB,GAAG,EAAE,EAAE;UACnCnB,IAAI,CAACmB,GAAG,CAAC,GAAG,CAAC3B,CAAC,CAAC2B,GAAG,CAAC,CAAC;QACtB;QACA,OAAO,IAAI9B,WAAW,CAAC;UACrBW,IAAI;UACJL,IAAI,EAAE,CAACG,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC;MACJ;MACA,IAAIoB,KAAK,CAACtB,MAAM,KAAK,CAAC,EAAE;QACtB,IAAIsB,KAAK,CAAC,CAAC,CAAC,KAAKpB,IAAI,IAAIoB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UACvC,MAAM,IAAIrB,UAAU,CAAC,8DAA8D,CAAC;QACtF;QACA,KAAK,IAAIuB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtB,IAAI,EAAEsB,GAAG,EAAE,EAAE;UACnCpB,IAAI,CAACoB,GAAG,CAAC,GAAG,CAAC5B,CAAC,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;QACA,OAAO,IAAI/B,WAAW,CAAC;UACrBW,IAAI;UACJL,IAAI,EAAE,CAACG,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC;MACJ;MACA,MAAM,IAAID,UAAU,CAAC,iFAAiF,CAAC;IACzG;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}