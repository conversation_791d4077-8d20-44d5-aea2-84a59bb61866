{"ast": null, "code": "export var getMatrixDataTypeDocs = {\n  name: 'getMatrixDataType',\n  category: 'Matrix',\n  syntax: ['getMatrixDataType(x)'],\n  description: 'Find the data type of all elements in a matrix or array, ' + 'for example \"number\" if all items are a number ' + 'and \"Complex\" if all values are complex numbers. ' + 'If a matrix contains more than one data type, it will return \"mixed\".',\n  examples: ['getMatrixDataType([1, 2, 3])', 'getMatrixDataType([[5 cm], [2 inch]])', 'getMatrixDataType([1, \"text\"])', 'getMatrixDataType([1, bignumber(4)])'],\n  seealso: ['matrix', 'sparse', 'typeOf']\n};", "map": {"version": 3, "names": ["getMatrixDataTypeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/getMatrixDataType.js"], "sourcesContent": ["export var getMatrixDataTypeDocs = {\n  name: 'getMatrixDataType',\n  category: 'Matrix',\n  syntax: ['getMatrixDataType(x)'],\n  description: 'Find the data type of all elements in a matrix or array, ' + 'for example \"number\" if all items are a number ' + 'and \"Complex\" if all values are complex numbers. ' + 'If a matrix contains more than one data type, it will return \"mixed\".',\n  examples: ['getMatrixDataType([1, 2, 3])', 'getMatrixDataType([[5 cm], [2 inch]])', 'getMatrixDataType([1, \"text\"])', 'getMatrixDataType([1, bignumber(4)])'],\n  seealso: ['matrix', 'sparse', 'typeOf']\n};"], "mappings": "AAAA,OAAO,IAAIA,qBAAqB,GAAG;EACjCC,IAAI,EAAE,mBAAmB;EACzBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,sBAAsB,CAAC;EAChCC,WAAW,EAAE,2DAA2D,GAAG,iDAAiD,GAAG,mDAAmD,GAAG,uEAAuE;EAC5PC,QAAQ,EAAE,CAAC,8BAA8B,EAAE,uCAAuC,EAAE,gCAAgC,EAAE,sCAAsC,CAAC;EAC7JC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}