{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'matrix';\nvar dependencies = ['typed', 'Matrix', 'DenseMatrix', 'SparseMatrix'];\nexport var createMatrix = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Matrix,\n    DenseMatrix,\n    SparseMatrix\n  } = _ref;\n  /**\n   * Create a Matrix. The function creates a new `math.Matrix` object from\n   * an `Array`. A Matrix has utility functions to manipulate the data in the\n   * matrix, like getting the size and getting or setting values in the matrix.\n   * Supported storage formats are 'dense' and 'sparse'.\n   *\n   * Syntax:\n   *\n   *    math.matrix()                         // creates an empty matrix using default storage format (dense).\n   *    math.matrix(data)                     // creates a matrix with initial data using default storage format (dense).\n   *    math.matrix('dense')                  // creates an empty matrix using the given storage format.\n   *    math.matrix(data, 'dense')            // creates a matrix with initial data using the given storage format.\n   *    math.matrix(data, 'sparse')           // creates a sparse matrix with initial data.\n   *    math.matrix(data, 'sparse', 'number') // creates a sparse matrix with initial data, number data type.\n   *\n   * Examples:\n   *\n   *    let m = math.matrix([[1, 2], [3, 4]])\n   *    m.size()                        // Array [2, 2]\n   *    m.resize([3, 2], 5)\n   *    m.valueOf()                     // Array [[1, 2], [3, 4], [5, 5]]\n   *    m.get([1, 0])                    // number 3\n   *\n   * See also:\n   *\n   *    bignumber, boolean, complex, index, number, string, unit, sparse\n   *\n   * @param {Array | Matrix} [data]    A multi dimensional array\n   * @param {string} [format]          The Matrix storage format, either `'dense'` or `'sparse'`\n   * @param {string} [datatype]        Type of the values\n   *\n   * @return {Matrix} The created matrix\n   */\n  return typed(name, {\n    '': function _() {\n      return _create([]);\n    },\n    string: function string(format) {\n      return _create([], format);\n    },\n    'string, string': function string_string(format, datatype) {\n      return _create([], format, datatype);\n    },\n    Array: function Array(data) {\n      return _create(data);\n    },\n    Matrix: function Matrix(data) {\n      return _create(data, data.storage());\n    },\n    'Array | Matrix, string': _create,\n    'Array | Matrix, string, string': _create\n  });\n\n  /**\n   * Create a new Matrix with given storage format\n   * @param {Array} data\n   * @param {string} [format]\n   * @param {string} [datatype]\n   * @returns {Matrix} Returns a new Matrix\n   * @private\n   */\n  function _create(data, format, datatype) {\n    // get storage format constructor\n    if (format === 'dense' || format === 'default' || format === undefined) {\n      return new DenseMatrix(data, datatype);\n    }\n    if (format === 'sparse') {\n      return new SparseMatrix(data, datatype);\n    }\n    throw new TypeError('Unknown matrix type ' + JSON.stringify(format) + '.');\n  }\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatrix", "_ref", "typed", "Matrix", "DenseMatrix", "SparseMatrix", "_", "_create", "string", "format", "string_string", "datatype", "Array", "data", "storage", "undefined", "TypeError", "JSON", "stringify"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/function/matrix.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nvar name = 'matrix';\nvar dependencies = ['typed', 'Matrix', 'DenseMatrix', 'SparseMatrix'];\nexport var createMatrix = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Matrix,\n    DenseMatrix,\n    SparseMatrix\n  } = _ref;\n  /**\n   * Create a Matrix. The function creates a new `math.Matrix` object from\n   * an `Array`. A Matrix has utility functions to manipulate the data in the\n   * matrix, like getting the size and getting or setting values in the matrix.\n   * Supported storage formats are 'dense' and 'sparse'.\n   *\n   * Syntax:\n   *\n   *    math.matrix()                         // creates an empty matrix using default storage format (dense).\n   *    math.matrix(data)                     // creates a matrix with initial data using default storage format (dense).\n   *    math.matrix('dense')                  // creates an empty matrix using the given storage format.\n   *    math.matrix(data, 'dense')            // creates a matrix with initial data using the given storage format.\n   *    math.matrix(data, 'sparse')           // creates a sparse matrix with initial data.\n   *    math.matrix(data, 'sparse', 'number') // creates a sparse matrix with initial data, number data type.\n   *\n   * Examples:\n   *\n   *    let m = math.matrix([[1, 2], [3, 4]])\n   *    m.size()                        // Array [2, 2]\n   *    m.resize([3, 2], 5)\n   *    m.valueOf()                     // Array [[1, 2], [3, 4], [5, 5]]\n   *    m.get([1, 0])                    // number 3\n   *\n   * See also:\n   *\n   *    bignumber, boolean, complex, index, number, string, unit, sparse\n   *\n   * @param {Array | Matrix} [data]    A multi dimensional array\n   * @param {string} [format]          The Matrix storage format, either `'dense'` or `'sparse'`\n   * @param {string} [datatype]        Type of the values\n   *\n   * @return {Matrix} The created matrix\n   */\n  return typed(name, {\n    '': function _() {\n      return _create([]);\n    },\n    string: function string(format) {\n      return _create([], format);\n    },\n    'string, string': function string_string(format, datatype) {\n      return _create([], format, datatype);\n    },\n    Array: function Array(data) {\n      return _create(data);\n    },\n    Matrix: function Matrix(data) {\n      return _create(data, data.storage());\n    },\n    'Array | Matrix, string': _create,\n    'Array | Matrix, string, string': _create\n  });\n\n  /**\n   * Create a new Matrix with given storage format\n   * @param {Array} data\n   * @param {string} [format]\n   * @param {string} [datatype]\n   * @returns {Matrix} Returns a new Matrix\n   * @private\n   */\n  function _create(data, format, datatype) {\n    // get storage format constructor\n    if (format === 'dense' || format === 'default' || format === undefined) {\n      return new DenseMatrix(data, datatype);\n    }\n    if (format === 'sparse') {\n      return new SparseMatrix(data, datatype);\n    }\n    throw new TypeError('Unknown matrix type ' + JSON.stringify(format) + '.');\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,IAAI,GAAG,QAAQ;AACnB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,CAAC;AACrE,OAAO,IAAIC,YAAY,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC3E,IAAI;IACFC,KAAK;IACLC,MAAM;IACNC,WAAW;IACXC;EACF,CAAC,GAAGJ,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,EAAE,EAAE,SAASQ,CAACA,CAAA,EAAG;MACf,OAAOC,OAAO,CAAC,EAAE,CAAC;IACpB,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACC,MAAM,EAAE;MAC9B,OAAOF,OAAO,CAAC,EAAE,EAAEE,MAAM,CAAC;IAC5B,CAAC;IACD,gBAAgB,EAAE,SAASC,aAAaA,CAACD,MAAM,EAAEE,QAAQ,EAAE;MACzD,OAAOJ,OAAO,CAAC,EAAE,EAAEE,MAAM,EAAEE,QAAQ,CAAC;IACtC,CAAC;IACDC,KAAK,EAAE,SAASA,KAAKA,CAACC,IAAI,EAAE;MAC1B,OAAON,OAAO,CAACM,IAAI,CAAC;IACtB,CAAC;IACDV,MAAM,EAAE,SAASA,MAAMA,CAACU,IAAI,EAAE;MAC5B,OAAON,OAAO,CAACM,IAAI,EAAEA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IACtC,CAAC;IACD,wBAAwB,EAAEP,OAAO;IACjC,gCAAgC,EAAEA;EACpC,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACM,IAAI,EAAEJ,MAAM,EAAEE,QAAQ,EAAE;IACvC;IACA,IAAIF,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAKM,SAAS,EAAE;MACtE,OAAO,IAAIX,WAAW,CAACS,IAAI,EAAEF,QAAQ,CAAC;IACxC;IACA,IAAIF,MAAM,KAAK,QAAQ,EAAE;MACvB,OAAO,IAAIJ,YAAY,CAACQ,IAAI,EAAEF,QAAQ,CAAC;IACzC;IACA,MAAM,IAAIK,SAAS,CAAC,sBAAsB,GAAGC,IAAI,CAACC,SAAS,CAACT,MAAM,CAAC,GAAG,GAAG,CAAC;EAC5E;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}