{"ast": null, "code": "// function utils\n\nimport { lruQueue } from './lruQueue.js';\n\n/**\n * Memoize a given function by caching the computed result.\n * The cache of a memoized function can be cleared by deleting the `cache`\n * property of the function.\n *\n * @param {function} fn                     The function to be memoized.\n *                                          Must be a pure function.\n * @param {Object} [options]\n * @param {function(args: Array): string} [options.hasher]\n *    A custom hash builder. Is JSON.stringify by default.\n * @param {number | undefined} [options.limit]\n *    Maximum number of values that may be cached. Undefined indicates\n *    unlimited (default)\n * @return {function}                       Returns the memoized function\n */\nexport function memoize(fn) {\n  var {\n    hasher,\n    limit\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  limit = limit == null ? Number.POSITIVE_INFINITY : limit;\n  hasher = hasher == null ? JSON.stringify : hasher;\n  return function memoize() {\n    if (typeof memoize.cache !== 'object') {\n      memoize.cache = {\n        values: new Map(),\n        lru: lruQueue(limit || Number.POSITIVE_INFINITY)\n      };\n    }\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n    var hash = hasher(args);\n    if (memoize.cache.values.has(hash)) {\n      memoize.cache.lru.hit(hash);\n      return memoize.cache.values.get(hash);\n    }\n    var newVal = fn.apply(fn, args);\n    memoize.cache.values.set(hash, newVal);\n    memoize.cache.values.delete(memoize.cache.lru.hit(hash));\n    return newVal;\n  };\n}\n\n/**\n * Memoize a given function by caching all results and the arguments,\n * and comparing against the arguments of previous results before\n * executing again.\n * This is less performant than `memoize` which calculates a hash,\n * which is very fast to compare. Use `memoizeCompare` only when it is\n * not possible to create a unique serializable hash from the function\n * arguments.\n * The isEqual function must compare two sets of arguments\n * and return true when equal (can be a deep equality check for example).\n * @param {function} fn\n * @param {function(a: *, b: *) : boolean} isEqual\n * @returns {function}\n */\nexport function memoizeCompare(fn, isEqual) {\n  var memoize = function memoize() {\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n    for (var c = 0; c < memoize.cache.length; c++) {\n      var cached = memoize.cache[c];\n      if (isEqual(args, cached.args)) {\n        // TODO: move this cache entry to the top so recently used entries move up?\n        return cached.res;\n      }\n    }\n    var res = fn.apply(fn, args);\n    memoize.cache.unshift({\n      args,\n      res\n    });\n    return res;\n  };\n  memoize.cache = [];\n  return memoize;\n}", "map": {"version": 3, "names": ["lruQueue", "memoize", "fn", "hasher", "limit", "arguments", "length", "undefined", "Number", "POSITIVE_INFINITY", "JSON", "stringify", "cache", "values", "Map", "lru", "args", "i", "hash", "has", "hit", "get", "newVal", "apply", "set", "delete", "memoizeCompare", "isEqual", "c", "cached", "res", "unshift"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/function.js"], "sourcesContent": ["// function utils\n\nimport { lruQueue } from './lruQueue.js';\n\n/**\n * Memoize a given function by caching the computed result.\n * The cache of a memoized function can be cleared by deleting the `cache`\n * property of the function.\n *\n * @param {function} fn                     The function to be memoized.\n *                                          Must be a pure function.\n * @param {Object} [options]\n * @param {function(args: Array): string} [options.hasher]\n *    A custom hash builder. Is JSON.stringify by default.\n * @param {number | undefined} [options.limit]\n *    Maximum number of values that may be cached. Undefined indicates\n *    unlimited (default)\n * @return {function}                       Returns the memoized function\n */\nexport function memoize(fn) {\n  var {\n    hasher,\n    limit\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  limit = limit == null ? Number.POSITIVE_INFINITY : limit;\n  hasher = hasher == null ? JSON.stringify : hasher;\n  return function memoize() {\n    if (typeof memoize.cache !== 'object') {\n      memoize.cache = {\n        values: new Map(),\n        lru: lruQueue(limit || Number.POSITIVE_INFINITY)\n      };\n    }\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n    var hash = hasher(args);\n    if (memoize.cache.values.has(hash)) {\n      memoize.cache.lru.hit(hash);\n      return memoize.cache.values.get(hash);\n    }\n    var newVal = fn.apply(fn, args);\n    memoize.cache.values.set(hash, newVal);\n    memoize.cache.values.delete(memoize.cache.lru.hit(hash));\n    return newVal;\n  };\n}\n\n/**\n * Memoize a given function by caching all results and the arguments,\n * and comparing against the arguments of previous results before\n * executing again.\n * This is less performant than `memoize` which calculates a hash,\n * which is very fast to compare. Use `memoizeCompare` only when it is\n * not possible to create a unique serializable hash from the function\n * arguments.\n * The isEqual function must compare two sets of arguments\n * and return true when equal (can be a deep equality check for example).\n * @param {function} fn\n * @param {function(a: *, b: *) : boolean} isEqual\n * @returns {function}\n */\nexport function memoizeCompare(fn, isEqual) {\n  var memoize = function memoize() {\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n    for (var c = 0; c < memoize.cache.length; c++) {\n      var cached = memoize.cache[c];\n      if (isEqual(args, cached.args)) {\n        // TODO: move this cache entry to the top so recently used entries move up?\n        return cached.res;\n      }\n    }\n    var res = fn.apply(fn, args);\n    memoize.cache.unshift({\n      args,\n      res\n    });\n    return res;\n  };\n  memoize.cache = [];\n  return memoize;\n}"], "mappings": "AAAA;;AAEA,SAASA,QAAQ,QAAQ,eAAe;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,EAAE,EAAE;EAC1B,IAAI;IACFC,MAAM;IACNC;EACF,CAAC,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1ED,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAGI,MAAM,CAACC,iBAAiB,GAAGL,KAAK;EACxDD,MAAM,GAAGA,MAAM,IAAI,IAAI,GAAGO,IAAI,CAACC,SAAS,GAAGR,MAAM;EACjD,OAAO,SAASF,OAAOA,CAAA,EAAG;IACxB,IAAI,OAAOA,OAAO,CAACW,KAAK,KAAK,QAAQ,EAAE;MACrCX,OAAO,CAACW,KAAK,GAAG;QACdC,MAAM,EAAE,IAAIC,GAAG,CAAC,CAAC;QACjBC,GAAG,EAAEf,QAAQ,CAACI,KAAK,IAAII,MAAM,CAACC,iBAAiB;MACjD,CAAC;IACH;IACA,IAAIO,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,SAAS,CAACC,MAAM,EAAEW,CAAC,EAAE,EAAE;MACzCD,IAAI,CAACC,CAAC,CAAC,GAAGZ,SAAS,CAACY,CAAC,CAAC;IACxB;IACA,IAAIC,IAAI,GAAGf,MAAM,CAACa,IAAI,CAAC;IACvB,IAAIf,OAAO,CAACW,KAAK,CAACC,MAAM,CAACM,GAAG,CAACD,IAAI,CAAC,EAAE;MAClCjB,OAAO,CAACW,KAAK,CAACG,GAAG,CAACK,GAAG,CAACF,IAAI,CAAC;MAC3B,OAAOjB,OAAO,CAACW,KAAK,CAACC,MAAM,CAACQ,GAAG,CAACH,IAAI,CAAC;IACvC;IACA,IAAII,MAAM,GAAGpB,EAAE,CAACqB,KAAK,CAACrB,EAAE,EAAEc,IAAI,CAAC;IAC/Bf,OAAO,CAACW,KAAK,CAACC,MAAM,CAACW,GAAG,CAACN,IAAI,EAAEI,MAAM,CAAC;IACtCrB,OAAO,CAACW,KAAK,CAACC,MAAM,CAACY,MAAM,CAACxB,OAAO,CAACW,KAAK,CAACG,GAAG,CAACK,GAAG,CAACF,IAAI,CAAC,CAAC;IACxD,OAAOI,MAAM;EACf,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,cAAcA,CAACxB,EAAE,EAAEyB,OAAO,EAAE;EAC1C,IAAI1B,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAIe,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,SAAS,CAACC,MAAM,EAAEW,CAAC,EAAE,EAAE;MACzCD,IAAI,CAACC,CAAC,CAAC,GAAGZ,SAAS,CAACY,CAAC,CAAC;IACxB;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,OAAO,CAACW,KAAK,CAACN,MAAM,EAAEsB,CAAC,EAAE,EAAE;MAC7C,IAAIC,MAAM,GAAG5B,OAAO,CAACW,KAAK,CAACgB,CAAC,CAAC;MAC7B,IAAID,OAAO,CAACX,IAAI,EAAEa,MAAM,CAACb,IAAI,CAAC,EAAE;QAC9B;QACA,OAAOa,MAAM,CAACC,GAAG;MACnB;IACF;IACA,IAAIA,GAAG,GAAG5B,EAAE,CAACqB,KAAK,CAACrB,EAAE,EAAEc,IAAI,CAAC;IAC5Bf,OAAO,CAACW,KAAK,CAACmB,OAAO,CAAC;MACpBf,IAAI;MACJc;IACF,CAAC,CAAC;IACF,OAAOA,GAAG;EACZ,CAAC;EACD7B,OAAO,CAACW,KAAK,GAAG,EAAE;EAClB,OAAOX,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}