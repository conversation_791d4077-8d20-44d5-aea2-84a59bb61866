{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createReplacer } from '../../factoriesAny.js';\nexport var replacerDependencies = {\n  createReplacer\n};", "map": {"version": 3, "names": ["createReplacer", "replacerDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesReplacer.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createReplacer } from '../../factoriesAny.js';\nexport var replacerDependencies = {\n  createReplacer\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,oBAAoB,GAAG;EAChCD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}