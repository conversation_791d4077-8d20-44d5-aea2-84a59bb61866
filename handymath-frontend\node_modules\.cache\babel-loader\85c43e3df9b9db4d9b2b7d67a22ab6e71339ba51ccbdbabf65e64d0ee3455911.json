{"ast": null, "code": "export var octDocs = {\n  name: 'oct',\n  category: 'Utils',\n  syntax: ['oct(value)'],\n  description: 'Format a number as octal',\n  examples: ['oct(56)'],\n  seealso: ['bin', 'hex']\n};", "map": {"version": 3, "names": ["octDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/oct.js"], "sourcesContent": ["export var octDocs = {\n  name: 'oct',\n  category: 'Utils',\n  syntax: ['oct(value)'],\n  description: 'Format a number as octal',\n  examples: ['oct(56)'],\n  seealso: ['bin', 'hex']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE,CAAC,SAAS,CAAC;EACrBC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}