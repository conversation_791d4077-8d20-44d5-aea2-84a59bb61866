{"ast": null, "code": "export var usolveDocs = {\n  name: 'usolve',\n  category: 'Algebra',\n  syntax: ['x=usolve(U, b)'],\n  description: 'Finds one solution of the linear system U * x = b where U is an [n x n] upper triangular matrix and b is a [n] column vector.',\n  examples: ['x=usolve(sparse([1, 1, 1, 1; 0, 1, 1, 1; 0, 0, 1, 1; 0, 0, 0, 1]), [1; 2; 3; 4])'],\n  seealso: ['usolveAll', 'lup', 'lusolve', 'lsolve', 'matrix', 'sparse']\n};", "map": {"version": 3, "names": ["usolveDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/usolve.js"], "sourcesContent": ["export var usolveDocs = {\n  name: 'usolve',\n  category: 'Algebra',\n  syntax: ['x=usolve(U, b)'],\n  description: 'Finds one solution of the linear system U * x = b where U is an [n x n] upper triangular matrix and b is a [n] column vector.',\n  examples: ['x=usolve(sparse([1, 1, 1, 1; 0, 1, 1, 1; 0, 0, 1, 1; 0, 0, 0, 1]), [1; 2; 3; 4])'],\n  seealso: ['usolveAll', 'lup', 'lusolve', 'lsolve', 'matrix', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gBAAgB,CAAC;EAC1BC,WAAW,EAAE,+HAA+H;EAC5IC,QAAQ,EAAE,CAAC,kFAAkF,CAAC;EAC9FC,OAAO,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}