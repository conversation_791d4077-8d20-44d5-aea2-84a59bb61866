{"ast": null, "code": "export var bellNumbersDocs = {\n  name: 'bellNumbers',\n  category: 'Combinatorics',\n  syntax: ['bellNumbers(n)'],\n  description: 'The Bell Numbers count the number of partitions of a set. A partition is a pairwise disjoint subset of S whose union is S. `bellNumbers` only takes integer arguments. The following condition must be enforced: n >= 0.',\n  examples: ['bellNumbers(3)', 'bellNumbers(8)'],\n  seealso: ['stirlingS2']\n};", "map": {"version": 3, "names": ["bellNumbersDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/combinatorics/bellNumbers.js"], "sourcesContent": ["export var bellNumbersDocs = {\n  name: 'bellNumbers',\n  category: 'Combinatorics',\n  syntax: ['bellNumbers(n)'],\n  description: 'The Bell Numbers count the number of partitions of a set. A partition is a pairwise disjoint subset of S whose union is S. `bellNumbers` only takes integer arguments. The following condition must be enforced: n >= 0.',\n  examples: ['bellNumbers(3)', 'bellNumbers(8)'],\n  seealso: ['stirlingS2']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,gBAAgB,CAAC;EAC1BC,WAAW,EAAE,0NAA0N;EACvOC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;EAC9CC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}