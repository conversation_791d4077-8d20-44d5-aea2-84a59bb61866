{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { combinationsDependencies } from './dependenciesCombinations.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isNegativeDependencies } from './dependenciesIsNegative.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createStirlingS2 } from '../../factoriesAny.js';\nexport var stirlingS2Dependencies = {\n  bignumberDependencies,\n  addScalarDependencies,\n  combinationsDependencies,\n  divideScalarDependencies,\n  factorialDependencies,\n  isIntegerDependencies,\n  isNegativeDependencies,\n  largerDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  subtractScalarDependencies,\n  typedDependencies,\n  createStirlingS2\n};", "map": {"version": 3, "names": ["bignumberDependencies", "addScalarDependencies", "combinationsDependencies", "divideScalarDependencies", "factorialDependencies", "isIntegerDependencies", "isNegativeDependencies", "largerDependencies", "multiplyScalarDependencies", "numberDependencies", "powDependencies", "subtractScalarDependencies", "typedDependencies", "createStirlingS2", "stirlingS2Dependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesStirlingS2.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { combinationsDependencies } from './dependenciesCombinations.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isNegativeDependencies } from './dependenciesIsNegative.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createStirlingS2 } from '../../factoriesAny.js';\nexport var stirlingS2Dependencies = {\n  bignumberDependencies,\n  addScalarDependencies,\n  combinationsDependencies,\n  divideScalarDependencies,\n  factorialDependencies,\n  isIntegerDependencies,\n  isNegativeDependencies,\n  largerDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  subtractScalarDependencies,\n  typedDependencies,\n  createStirlingS2\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,sBAAsB,GAAG;EAClCd,qBAAqB;EACrBC,qBAAqB;EACrBC,wBAAwB;EACxBC,wBAAwB;EACxBC,qBAAqB;EACrBC,qBAAqB;EACrBC,sBAAsB;EACtBC,kBAAkB;EAClBC,0BAA0B;EAC1BC,kBAAkB;EAClBC,eAAe;EACfC,0BAA0B;EAC1BC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}