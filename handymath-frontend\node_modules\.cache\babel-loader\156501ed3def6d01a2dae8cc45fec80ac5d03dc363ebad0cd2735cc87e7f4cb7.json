{"ast": null, "code": "export var cubeDocs = {\n  name: 'cube',\n  category: 'Arithmetic',\n  syntax: ['cube(x)'],\n  description: 'Compute the cube of a value. The cube of x is x * x * x.',\n  examples: ['cube(2)', '2^3', '2 * 2 * 2'],\n  seealso: ['multiply', 'square', 'pow']\n};", "map": {"version": 3, "names": ["cubeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/cube.js"], "sourcesContent": ["export var cubeDocs = {\n  name: 'cube',\n  category: 'Arithmetic',\n  syntax: ['cube(x)'],\n  description: 'Compute the cube of a value. The cube of x is x * x * x.',\n  examples: ['cube(2)', '2^3', '2 * 2 * 2'],\n  seealso: ['multiply', 'square', 'pow']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,0DAA0D;EACvEC,QAAQ,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC;EACzCC,OAAO,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}