{"ast": null, "code": "export var polynomialRootDocs = {\n  name: 'polynomialRoot',\n  category: 'Algebra',\n  syntax: ['x=polynomialRoot(-6, 3)', 'x=polynomialRoot(4, -4, 1)', 'x=polynomialRoot(-8, 12, -6, 1)'],\n  description: 'Finds the roots of a univariate polynomial given by its coefficients starting from constant, linear, and so on, increasing in degree.',\n  examples: ['a = polynomialRoot(-6, 11, -6, 1)'],\n  seealso: ['cbrt', 'sqrt']\n};", "map": {"version": 3, "names": ["polynomialRootDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/polynomialRoot.js"], "sourcesContent": ["export var polynomialRootDocs = {\n  name: 'polynomialRoot',\n  category: 'Algebra',\n  syntax: ['x=polynomialRoot(-6, 3)', 'x=polynomialRoot(4, -4, 1)', 'x=polynomialRoot(-8, 12, -6, 1)'],\n  description: 'Finds the roots of a univariate polynomial given by its coefficients starting from constant, linear, and so on, increasing in degree.',\n  examples: ['a = polynomialRoot(-6, 11, -6, 1)'],\n  seealso: ['cbrt', 'sqrt']\n};"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG;EAC9BC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,yBAAyB,EAAE,4BAA4B,EAAE,iCAAiC,CAAC;EACpGC,WAAW,EAAE,uIAAuI;EACpJC,QAAQ,EAAE,CAAC,mCAAmC,CAAC;EAC/CC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}