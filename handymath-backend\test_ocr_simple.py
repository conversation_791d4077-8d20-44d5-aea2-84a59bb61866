#!/usr/bin/env python
"""
Test simple de la fonctionnalité OCR sans authentification
"""
import os
import sys
import django
from PIL import Image, ImageDraw, ImageFont
import io

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

def test_ocr_components():
    """Tester les composants OCR individuellement"""
    print("=== Test des composants OCR ===")
    
    # Test 1: Import des modules
    try:
        import pytesseract
        print("✅ pytesseract importé avec succès")
        print(f"   Chemin Tesseract: {pytesseract.pytesseract.tesseract_cmd}")
    except ImportError as e:
        print(f"❌ Erreur import pytesseract: {e}")
        return
    
    try:
        import cv2
        print("✅ OpenCV importé avec succès")
        print(f"   Version OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ Erreur import OpenCV: {e}")
        return
    
    # Test 2: Fonctions OCR du projet
    try:
        from api.utils.ocr import extract_text_from_image, preprocess_image, clean_ocr_text
        print("✅ Fonctions OCR du projet importées avec succès")
    except ImportError as e:
        print(f"❌ Erreur import fonctions OCR: {e}")
        return
    
    # Test 3: Créer une image de test
    print("\n--- Test de reconnaissance sur image générée ---")
    
    # Créer une image simple
    img = Image.new('RGB', (300, 80), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 32)
    except:
        font = ImageFont.load_default()
    
    equation = "x + 3 = 8"
    
    # Centrer le texte
    bbox = draw.textbbox((0, 0), equation, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (300 - text_width) // 2
    y = (80 - text_height) // 2
    
    draw.text((x, y), equation, fill='black', font=font)
    
    # Sauvegarder l'image
    img.save("test_simple_equation.png")
    print(f"Image créée: test_simple_equation.png avec l'équation '{equation}'")
    
    # Test 4: OCR direct avec pytesseract
    print("\n--- Test OCR direct ---")
    try:
        # Configuration basique
        basic_result = pytesseract.image_to_string(img)
        print(f"OCR basique: '{basic_result.strip()}'")
        
        # Configuration mathématique
        math_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '
        math_result = pytesseract.image_to_string(img, config=math_config)
        print(f"OCR mathématique: '{math_result.strip()}'")
        
    except Exception as e:
        print(f"❌ Erreur OCR direct: {e}")
    
    # Test 5: Fonctions du projet
    print("\n--- Test fonctions du projet ---")
    try:
        # Test préprocessing
        processed_img = preprocess_image(img, enhance_contrast=True, grayscale=True)
        processed_img.save("test_processed.png")
        print("✅ Préprocessing réussi, image sauvée: test_processed.png")
        
        # Test extraction de texte
        extracted_text = extract_text_from_image(img, clean_text=True)
        print(f"✅ Texte extrait: '{extracted_text}'")
        
        # Test nettoyage
        dirty_text = "x + 3 = 8 noise text"
        cleaned_text = clean_ocr_text(dirty_text)
        print(f"✅ Nettoyage: '{dirty_text}' -> '{cleaned_text}'")

        # Test correction des puissances
        power_tests = [
            "x42=9",  # Erreur typique de reconnaissance
            "x2+3=7",
            "2x43-1=0"
        ]

        for power_text in power_tests:
            corrected = clean_ocr_text(power_text)
            print(f"✅ Correction puissance: '{power_text}' -> '{corrected}'")
        
    except Exception as e:
        print(f"❌ Erreur fonctions du projet: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 6: Fonction de vue (sans authentification)
    print("\n--- Test fonction de vue ---")
    try:
        from api.views import preprocess_image_for_ocr, clean_ocr_text as view_clean_ocr_text
        
        # Test préprocessing de la vue
        processed_view = preprocess_image_for_ocr(img)
        processed_view.save("test_view_processed.png")
        print("✅ Préprocessing de la vue réussi")
        
        # Test OCR avec la configuration de la vue
        config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '
        view_result = pytesseract.image_to_string(processed_view, config=config)
        cleaned_view_result = view_clean_ocr_text(view_result)
        print(f"✅ OCR avec config de la vue: '{cleaned_view_result}'")
        
    except Exception as e:
        print(f"❌ Erreur fonction de vue: {e}")
        import traceback
        traceback.print_exc()

def test_different_equations():
    """Tester avec différentes équations"""
    print("\n=== Test avec différentes équations ===")
    
    equations = [
        "x = 5",
        "2x = 10", 
        "x + 1 = 4",
        "3x - 2 = 7",
        "x^2 = 9"
    ]
    
    from api.utils.ocr import extract_text_from_image
    
    for i, equation in enumerate(equations):
        print(f"\n--- Test équation {i+1}: {equation} ---")
        
        # Créer l'image
        img = Image.new('RGB', (250, 60), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 28)
        except:
            font = ImageFont.load_default()
        
        # Centrer le texte
        bbox = draw.textbbox((0, 0), equation, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (250 - text_width) // 2
        y = (60 - text_height) // 2
        
        draw.text((x, y), equation, fill='black', font=font)
        
        # Sauvegarder
        filename = f"test_eq_{i+1}.png"
        img.save(filename)
        
        # Tester OCR
        try:
            result = extract_text_from_image(img, clean_text=True)
            success = "✅" if equation.replace(" ", "") in result.replace(" ", "") else "⚠️"
            print(f"{success} Attendu: '{equation}' | Obtenu: '{result}'")
        except Exception as e:
            print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_ocr_components()
    test_different_equations()
    print("\n=== Tests terminés ===")
