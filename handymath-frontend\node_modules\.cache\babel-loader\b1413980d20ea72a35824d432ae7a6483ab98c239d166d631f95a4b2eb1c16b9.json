{"ast": null, "code": "export var logDocs = {\n  name: 'log',\n  category: 'Arithmetic',\n  syntax: ['log(x)', 'log(x, base)'],\n  description: 'Compute the logarithm of a value. If no base is provided, the natural logarithm of x is calculated. If base if provided, the logarithm is calculated for the specified base. log(x, base) is defined as log(x) / log(base).',\n  examples: ['log(3.5)', 'a = log(2.4)', 'exp(a)', '10 ^ 4', 'log(10000, 10)', 'log(10000) / log(10)', 'b = log(1024, 2)', '2 ^ b'],\n  seealso: ['exp', 'log1p', 'log2', 'log10']\n};", "map": {"version": 3, "names": ["logDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/log.js"], "sourcesContent": ["export var logDocs = {\n  name: 'log',\n  category: 'Arithmetic',\n  syntax: ['log(x)', 'log(x, base)'],\n  description: 'Compute the logarithm of a value. If no base is provided, the natural logarithm of x is calculated. If base if provided, the logarithm is calculated for the specified base. log(x, base) is defined as log(x) / log(base).',\n  examples: ['log(3.5)', 'a = log(2.4)', 'exp(a)', '10 ^ 4', 'log(10000, 10)', 'log(10000) / log(10)', 'b = log(1024, 2)', '2 ^ b'],\n  seealso: ['exp', 'log1p', 'log2', 'log10']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC;EAClCC,WAAW,EAAE,6NAA6N;EAC1OC,QAAQ,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,OAAO,CAAC;EACjIC,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}