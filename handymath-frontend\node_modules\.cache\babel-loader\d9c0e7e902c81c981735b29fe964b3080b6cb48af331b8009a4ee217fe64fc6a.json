{"ast": null, "code": "export var diffDocs = {\n  name: 'diff',\n  category: 'Matrix',\n  syntax: ['diff(arr)', 'diff(arr, dim)'],\n  description: ['Create a new matrix or array with the difference of the passed matrix or array.', 'Dim parameter is optional and used to indicate the dimension of the array/matrix to apply the difference', 'If no dimension parameter is passed it is assumed as dimension 0', 'Dimension is zero-based in javascript and one-based in the parser', 'Arrays must be \\'rectangular\\' meaning arrays like [1, 2]', 'If something is passed as a matrix it will be returned as a matrix but other than that all matrices are converted to arrays'],\n  examples: ['A = [1, 2, 4, 7, 0]', 'diff(A)', 'diff(A, 1)', 'B = [[1, 2], [3, 4]]', 'diff(B)', 'diff(B, 1)', 'diff(B, 2)', 'diff(B, bignumber(2))', 'diff([[1, 2], matrix([3, 4])], 2)'],\n  seealso: ['subtract', 'partitionSelect']\n};", "map": {"version": 3, "names": ["diffDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/diff.js"], "sourcesContent": ["export var diffDocs = {\n  name: 'diff',\n  category: 'Matrix',\n  syntax: ['diff(arr)', 'diff(arr, dim)'],\n  description: ['Create a new matrix or array with the difference of the passed matrix or array.', 'Dim parameter is optional and used to indicate the dimension of the array/matrix to apply the difference', 'If no dimension parameter is passed it is assumed as dimension 0', 'Dimension is zero-based in javascript and one-based in the parser', 'Arrays must be \\'rectangular\\' meaning arrays like [1, 2]', 'If something is passed as a matrix it will be returned as a matrix but other than that all matrices are converted to arrays'],\n  examples: ['A = [1, 2, 4, 7, 0]', 'diff(A)', 'diff(A, 1)', 'B = [[1, 2], [3, 4]]', 'diff(B)', 'diff(B, 1)', 'diff(B, 2)', 'diff(B, bignumber(2))', 'diff([[1, 2], matrix([3, 4])], 2)'],\n  seealso: ['subtract', 'partitionSelect']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;EACvCC,WAAW,EAAE,CAAC,iFAAiF,EAAE,0GAA0G,EAAE,kEAAkE,EAAE,mEAAmE,EAAE,2DAA2D,EAAE,6HAA6H,CAAC;EACjhBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,SAAS,EAAE,YAAY,EAAE,sBAAsB,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,uBAAuB,EAAE,mCAAmC,CAAC;EACvLC,OAAO,EAAE,CAAC,UAAU,EAAE,iBAAiB;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}