# Generated by Django 5.2.1 on 2025-06-04 00:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0006_add_exercise_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='Badge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('category', models.CharField(choices=[('achievement', 'Accomplissement'), ('streak', 'Série'), ('mastery', 'Maîtrise'), ('social', 'Social'), ('special', 'Spécial')], default='achievement', max_length=20)),
                ('icon', models.CharField(default='🏆', max_length=10)),
                ('points_required', models.IntegerField(default=0)),
                ('exercises_required', models.IntegerField(default=0)),
                ('streak_required', models.IntegerField(default=0)),
                ('difficulty_required', models.CharField(blank=True, max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Badge',
                'verbose_name_plural': 'Badges',
            },
        ),
        migrations.CreateModel(
            name='Streak',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_streak', models.IntegerField(default=0)),
                ('longest_streak', models.IntegerField(default=0)),
                ('last_activity_date', models.DateField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='streak', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Série utilisateur',
                'verbose_name_plural': 'Séries utilisateurs',
            },
        ),
        migrations.CreateModel(
            name='UserLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_level', models.IntegerField(default=1)),
                ('total_xp', models.IntegerField(default=0)),
                ('xp_to_next_level', models.IntegerField(default=100)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='level', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Niveau utilisateur',
                'verbose_name_plural': 'Niveaux utilisateurs',
            },
        ),
        migrations.CreateModel(
            name='UserBadge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('earned_at', models.DateTimeField(auto_now_add=True)),
                ('badge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.badge')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='badges', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Badge utilisateur',
                'verbose_name_plural': 'Badges utilisateurs',
                'unique_together': {('user', 'badge')},
            },
        ),
    ]
