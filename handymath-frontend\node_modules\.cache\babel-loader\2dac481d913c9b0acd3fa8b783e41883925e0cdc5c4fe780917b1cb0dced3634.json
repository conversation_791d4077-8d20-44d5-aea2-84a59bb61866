{"ast": null, "code": "export var expmDocs = {\n  name: 'expm',\n  category: 'Arithmetic',\n  syntax: ['exp(x)'],\n  description: 'Compute the matrix exponential, expm(A) = e^A. ' + 'The matrix must be square. ' + 'Not to be confused with exp(a), which performs element-wise exponentiation.',\n  examples: ['expm([[0,2],[0,0]])'],\n  seealso: ['exp']\n};", "map": {"version": 3, "names": ["expmDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/expm.js"], "sourcesContent": ["export var expmDocs = {\n  name: 'expm',\n  category: 'Arithmetic',\n  syntax: ['exp(x)'],\n  description: 'Compute the matrix exponential, expm(A) = e^A. ' + 'The matrix must be square. ' + 'Not to be confused with exp(a), which performs element-wise exponentiation.',\n  examples: ['expm([[0,2],[0,0]])'],\n  seealso: ['exp']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,iDAAiD,GAAG,6BAA6B,GAAG,6EAA6E;EAC9KC,QAAQ,EAAE,CAAC,qBAAqB,CAAC;EACjCC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}