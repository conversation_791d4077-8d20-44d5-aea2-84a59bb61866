{"ast": null, "code": "export var acosDocs = {\n  name: 'acos',\n  category: 'Trigonometry',\n  syntax: ['acos(x)'],\n  description: 'Compute the inverse cosine of a value in radians.',\n  examples: ['acos(0.5)', 'acos(cos(2.3))'],\n  seealso: ['cos', 'atan', 'asin']\n};", "map": {"version": 3, "names": ["acosDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/acos.js"], "sourcesContent": ["export var acosDocs = {\n  name: 'acos',\n  category: 'Trigonometry',\n  syntax: ['acos(x)'],\n  description: 'Compute the inverse cosine of a value in radians.',\n  examples: ['acos(0.5)', 'acos(cos(2.3))'],\n  seealso: ['cos', 'atan', 'asin']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;EACzCC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}