{"ast": null, "code": "import { ObjectWrappingMap, PartitionedMap } from './map.js';\n\n/**\n * Create a new scope which can access the parent scope,\n * but does not affect it when written. This is suitable for variable definitions\n * within a block node, or function definition.\n *\n * If parent scope has a createSubScope method, it delegates to that. Otherwise,\n * creates an empty map, and copies the parent scope to it, adding in\n * the remaining `args`.\n *\n * @param {Map} parentScope\n * @param  {Object} args\n * @returns {PartitionedMap}\n */\nexport function createSubScope(parentScope, args) {\n  return new PartitionedMap(parentScope, new ObjectWrappingMap(args), new Set(Object.keys(args)));\n}", "map": {"version": 3, "names": ["ObjectWrappingMap", "PartitionedMap", "createSubScope", "parentScope", "args", "Set", "Object", "keys"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/scope.js"], "sourcesContent": ["import { ObjectWrappingMap, PartitionedMap } from './map.js';\n\n/**\n * Create a new scope which can access the parent scope,\n * but does not affect it when written. This is suitable for variable definitions\n * within a block node, or function definition.\n *\n * If parent scope has a createSubScope method, it delegates to that. Otherwise,\n * creates an empty map, and copies the parent scope to it, adding in\n * the remaining `args`.\n *\n * @param {Map} parentScope\n * @param  {Object} args\n * @returns {PartitionedMap}\n */\nexport function createSubScope(parentScope, args) {\n  return new PartitionedMap(parentScope, new ObjectWrappingMap(args), new Set(Object.keys(args)));\n}"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,cAAc,QAAQ,UAAU;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAEC,IAAI,EAAE;EAChD,OAAO,IAAIH,cAAc,CAACE,WAAW,EAAE,IAAIH,iBAAiB,CAACI,IAAI,CAAC,EAAE,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC;AACjG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}