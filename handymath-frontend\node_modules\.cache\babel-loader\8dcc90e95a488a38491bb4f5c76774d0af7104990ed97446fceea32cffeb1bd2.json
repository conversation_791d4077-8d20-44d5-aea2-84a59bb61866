{"ast": null, "code": "export var largerDocs = {\n  name: 'larger',\n  category: 'Relational',\n  syntax: ['x > y', 'larger(x, y)'],\n  description: 'Check if value x is larger than y. Returns true if x is larger than y, and false if not. Comparing a value with NaN returns false.',\n  examples: ['2 > 3', '5 > 2*2', 'a = 3.3', 'b = 6-2.8', '(a > b)', '(b < a)', '5 cm > 2 inch'],\n  seealso: ['equal', 'unequal', 'smaller', 'smallerEq', 'largerEq', 'compare']\n};", "map": {"version": 3, "names": ["largerDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/larger.js"], "sourcesContent": ["export var largerDocs = {\n  name: 'larger',\n  category: 'Relational',\n  syntax: ['x > y', 'larger(x, y)'],\n  description: 'Check if value x is larger than y. Returns true if x is larger than y, and false if not. Comparing a value with NaN returns false.',\n  examples: ['2 > 3', '5 > 2*2', 'a = 3.3', 'b = 6-2.8', '(a > b)', '(b < a)', '5 cm > 2 inch'],\n  seealso: ['equal', 'unequal', 'smaller', 'smallerEq', 'largerEq', 'compare']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC;EACjCC,WAAW,EAAE,oIAAoI;EACjJC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC;EAC7FC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS;AAC7E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}