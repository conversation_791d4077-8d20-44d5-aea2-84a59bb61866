{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\nimport { csFlip } from './csFlip.js';\n\n/**\n * Marks the node at w[j]\n *\n * @param {Array}   w               The array\n * @param {Number}  j               The array index\n */\nexport function csMark(w, j) {\n  // mark w[j]\n  w[j] = csFlip(w[j]);\n}", "map": {"version": 3, "names": ["csFlip", "csMark", "w", "j"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csMark.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\nimport { csFlip } from './csFlip.js';\n\n/**\n * Marks the node at w[j]\n *\n * @param {Array}   w               The array\n * @param {Number}  j               The array index\n */\nexport function csMark(w, j) {\n  // mark w[j]\n  w[j] = csFlip(w[j]);\n}"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3B;EACAD,CAAC,CAACC,CAAC,CAAC,GAAGH,MAAM,CAACE,CAAC,CAACC,CAAC,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}