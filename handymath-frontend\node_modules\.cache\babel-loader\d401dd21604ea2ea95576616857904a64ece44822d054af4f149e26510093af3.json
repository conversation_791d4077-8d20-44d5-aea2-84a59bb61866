{"ast": null, "code": "export var printTemplate = /\\$([\\w.]+)/g;", "map": {"version": 3, "names": ["printTemplate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/print.js"], "sourcesContent": ["export var printTemplate = /\\$([\\w.]+)/g;"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}