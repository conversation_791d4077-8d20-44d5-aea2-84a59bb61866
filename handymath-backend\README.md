# HandyMath Backend

Application backend pour HandyMath, une plateforme d'aide aux mathématiques.

## Fonctionnalités

- Résolution d'équations mathématiques
- Reconnaissance d'équations à partir d'images
- Gestion des utilisateurs (étudiants, professeurs, administrateurs)
- API REST pour l'intégration avec le frontend

## Installation

1. <PERSON><PERSON><PERSON> le dép<PERSON>t
```bash
git clone https://github.com/votre-username/handymath-backend.git
cd handymath-backend
```

2. C<PERSON>er un environnement virtuel
```bash
python -m venv venv
source venv/bin/activate  # Sur Windows: venv\Scripts\activate
```

3. Installer les dépendances
```bash
pip install -r requirements.txt
```

4. Configurer les variables d'environnement
```bash
cp .env.example .env
# Modifier .env avec vos propres valeurs
```

5. Appliquer les migrations
```bash
python manage.py migrate
```

6. <PERSON><PERSON><PERSON> un superutilisateur
```bash
python manage.py createsuperuser
```

7. <PERSON><PERSON> le serveur
```bash
python manage.py runserver
```

## Structure du projet

- `core/` - Modèles et logique métier principale
- `api/` - API REST et endpoints
- `handymath_backend/` - Configuration du projet Django

## API Endpoints

- `/api/token/` - Obtenir un token JWT
- `/api/users/` - Gestion des utilisateurs
- `/api/equations/solve/` - Résoudre une équation
- `/api/equations/recognize/` - Reconnaître une équation à partir d'une image

## Technologies utilisées

- Django 4.2+
- Django REST Framework
- SymPy (pour la résolution d'équations)
- OpenCV et PyTesseract (pour la reconnaissance d'images)
- PyTorch (pour les modèles d'IA)