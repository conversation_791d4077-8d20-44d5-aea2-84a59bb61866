{"ast": null, "code": "export var NaNDocs = {\n  name: 'NaN',\n  category: 'Constants',\n  syntax: ['NaN'],\n  description: 'Not a number',\n  examples: ['NaN', '0 / 0'],\n  seealso: []\n};", "map": {"version": 3, "names": ["NaNDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/NaN.js"], "sourcesContent": ["export var NaNDocs = {\n  name: 'NaN',\n  category: 'Constants',\n  syntax: ['NaN'],\n  description: 'Not a number',\n  examples: ['NaN', '0 / 0'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,KAAK,CAAC;EACfC,WAAW,EAAE,cAAc;EAC3BC,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}