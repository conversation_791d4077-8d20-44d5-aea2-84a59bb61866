{"ast": null, "code": "export var fixDocs = {\n  name: 'fix',\n  category: 'Arithmetic',\n  syntax: ['fix(x)', 'fix(x, n)', 'fix(unit, valuelessUnit)', 'fix(unit, n, valuelessUnit)'],\n  description: 'Round a value towards zero. If x is complex, both real and imaginary part are rounded towards zero.',\n  examples: ['fix(3.2)', 'fix(3.8)', 'fix(-4.2)', 'fix(-4.8)', 'fix(3.241cm, cm)', 'fix(3.241cm, 2, cm)'],\n  seealso: ['ceil', 'floor', 'round']\n};", "map": {"version": 3, "names": ["fixDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/fix.js"], "sourcesContent": ["export var fixDocs = {\n  name: 'fix',\n  category: 'Arithmetic',\n  syntax: ['fix(x)', 'fix(x, n)', 'fix(unit, valuelessUnit)', 'fix(unit, n, valuelessUnit)'],\n  description: 'Round a value towards zero. If x is complex, both real and imaginary part are rounded towards zero.',\n  examples: ['fix(3.2)', 'fix(3.8)', 'fix(-4.2)', 'fix(-4.8)', 'fix(3.241cm, cm)', 'fix(3.241cm, 2, cm)'],\n  seealso: ['ceil', 'floor', 'round']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE,6BAA6B,CAAC;EAC1FC,WAAW,EAAE,qGAAqG;EAClHC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,qBAAqB,CAAC;EACvGC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}