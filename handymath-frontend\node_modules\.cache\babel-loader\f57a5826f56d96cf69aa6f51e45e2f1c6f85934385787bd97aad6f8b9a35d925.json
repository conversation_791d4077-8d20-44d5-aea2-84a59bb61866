{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { MatrixDependencies } from './dependenciesMatrixClass.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSparseMatrixClass } from '../../factoriesAny.js';\nexport var SparseMatrixDependencies = {\n  MatrixDependencies,\n  equalScalarDependencies,\n  typedDependencies,\n  createSparseMatrixClass\n};", "map": {"version": 3, "names": ["MatrixDependencies", "equalScalarDependencies", "typedDependencies", "createSparseMatrixClass", "SparseMatrixDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSparseMatrixClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { MatrixDependencies } from './dependenciesMatrixClass.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSparseMatrixClass } from '../../factoriesAny.js';\nexport var SparseMatrixDependencies = {\n  MatrixDependencies,\n  equalScalarDependencies,\n  typedDependencies,\n  createSparseMatrixClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,OAAO,IAAIC,wBAAwB,GAAG;EACpCJ,kBAAkB;EAClBC,uBAAuB;EACvBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}