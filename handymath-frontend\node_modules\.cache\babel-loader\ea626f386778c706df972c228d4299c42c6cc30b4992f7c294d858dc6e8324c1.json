{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createSymbolNode } from '../../factoriesAny.js';\nexport var SymbolNodeDependencies = {\n  UnitDependencies,\n  NodeDependencies,\n  createSymbolNode\n};", "map": {"version": 3, "names": ["UnitDependencies", "NodeDependencies", "createSymbolNode", "SymbolNodeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSymbolNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createSymbolNode } from '../../factoriesAny.js';\nexport var SymbolNodeDependencies = {\n  UnitDependencies,\n  NodeDependencies,\n  createSymbolNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,sBAAsB,GAAG;EAClCH,gBAAgB;EAChBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}