{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { sqrtDependencies } from './dependenciesSqrt.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { varianceDependencies } from './dependenciesVariance.generated.js';\nimport { createStdTransform } from '../../factoriesAny.js';\nexport var stdTransformDependencies = {\n  mapDependencies,\n  sqrtDependencies,\n  typedDependencies,\n  varianceDependencies,\n  createStdTransform\n};", "map": {"version": 3, "names": ["mapDependencies", "sqrtDependencies", "typedDependencies", "varianceDependencies", "createStdTransform", "stdTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesStdTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { sqrtDependencies } from './dependenciesSqrt.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { varianceDependencies } from './dependenciesVariance.generated.js';\nimport { createStdTransform } from '../../factoriesAny.js';\nexport var stdTransformDependencies = {\n  mapDependencies,\n  sqrtDependencies,\n  typedDependencies,\n  varianceDependencies,\n  createStdTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCL,eAAe;EACfC,gBAAgB;EAChBC,iBAAiB;EACjBC,oBAAoB;EACpBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}