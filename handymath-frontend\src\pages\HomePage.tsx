import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import { useAuth } from '../contexts/AuthContext';

const HomePage: React.FC = () => {
  const canvasRef = React.useRef<HTMLCanvasElement>(null);
  const { user } = useAuth();

  // Animation 3D avec Three.js
  React.useEffect(() => {
    if (!canvasRef.current) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      alpha: true,
      antialias: true
    });

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);

    // Créer des formes mathématiques 3D
    const geometry = new THREE.TorusKnotGeometry(10, 3, 100, 16);
    const material = new THREE.MeshNormalMaterial();
    const torusKnot = new THREE.Mesh(geometry, material);
    scene.add(torusKnot);

    camera.position.z = 30;

    // Animation
    const animate = () => {
      requestAnimationFrame(animate);
      torusKnot.rotation.x += 0.01;
      torusKnot.rotation.y += 0.01;
      renderer.render(scene, camera);
    };

    animate();

    // Responsive
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      // Nettoyer Three.js
      geometry.dispose();
      material.dispose();
      renderer.dispose();
    };
  }, []);

  // Données des fonctionnalités
  const features = [
    {
      title: "Résolution d'équations",
      description: "Algébriques, différentielles, systèmes d'équations et plus encore.",
      icon: "🔢"
    },
    {
      title: "Reconnaissance d'écriture",
      description: "Prenez une photo de votre équation manuscrite et obtenez la solution.",
      icon: "📷"
    },
    {
      title: "Explications détaillées",
      description: "Comprenez chaque étape de la résolution avec des explications claires.",
      icon: "🧠"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="relative z-10"
    >
      {/* Canvas 3D en arrière-plan */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full -z-10"
      />

      {/* Section héro */}
      <div className="max-w-4xl mx-auto pt-20 pb-16 text-center">
        <motion.h1
          className="text-5xl md:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-secondary-500"
          initial={{ y: -50 }}
          animate={{ y: 0 }}
          transition={{ type: "spring", stiffness: 100 }}
        >
          HandyMath
        </motion.h1>

        <motion.p
          className="text-xl md:text-2xl mb-8 text-gray-700 dark:text-gray-300"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Résolvez n'importe quelle équation mathématique en un instant, avec des
          explications détaillées et des visualisations interactives.
        </motion.p>

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          {user ? (
            // Utilisateur connecté - Afficher les actions principales
            <>
              <div className="mb-4 text-center">
                <p className="text-lg text-gray-700 dark:text-gray-300 mb-4">
                  Bonjour <span className="font-semibold text-primary-600">{user.prenom || user.username}</span> ! 👋
                </p>
              </div>
              <Link
                to="/solver"
                className="px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
              >
                🧮 Résoudre une équation
              </Link>
              <Link
                to="/exercises"
                className="px-8 py-4 bg-secondary-500 hover:bg-secondary-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
              >
                📚 Faire des exercices
              </Link>
              <Link
                to={user.role === 'admin' ? '/admin/dashboard' : '/etudiant/dashboard'}
                className="px-8 py-4 bg-green-500 hover:bg-green-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
              >
                📊 Mon tableau de bord
              </Link>
            </>
          ) : (
            // Utilisateur non connecté - Afficher connexion/inscription
            <>
              <Link
                to="/login"
                className="px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
              >
                Se connecter
              </Link>
              <Link
                to="/register"
                className="px-8 py-4 bg-secondary-500 hover:bg-secondary-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
              >
                S'inscrire
              </Link>
            </>
          )}
        </motion.div>
      </div>

      {/* Section fonctionnalités */}
      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
        {features.map((feature, index) => (
          <motion.div
            key={index}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 + index * 0.1 }}
          >
            <div className="text-4xl mb-4">{feature.icon}</div>
            <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
            <p className="text-gray-600 dark:text-gray-400">
              {feature.description}
            </p>
          </motion.div>
        ))}
      </div>

      {/* Section statistiques */}
      <div className="max-w-4xl mx-auto mt-20 mb-16">
        <motion.div
          className="bg-gradient-to-r from-primary-600 to-secondary-500 rounded-2xl p-8 text-white text-center"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <h2 className="text-3xl font-bold mb-6">
            Rejoignez des milliers d'étudiants
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="text-4xl font-bold mb-2">10K+</div>
              <div className="text-lg opacity-90">Équations résolues</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-lg opacity-90">Étudiants actifs</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">98%</div>
              <div className="text-lg opacity-90">Taux de satisfaction</div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Section CTA finale */}
      <div className="max-w-4xl mx-auto text-center pb-20">
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1.0 }}
        >
          {user ? (
            // Utilisateur connecté - Encourager à utiliser les fonctionnalités
            <>
              <h2 className="text-3xl font-bold mb-4">
                Continuez votre apprentissage des mathématiques !
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Explorez toutes les fonctionnalités de HandyMath pour améliorer vos compétences.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/solver"
                  className="inline-block px-8 py-4 bg-gradient-to-r from-primary-600 to-secondary-500 text-white font-bold rounded-lg shadow-xl hover:shadow-2xl transition-all transform hover:-translate-y-1 hover:scale-105"
                >
                  🧮 Résoudre une équation
                </Link>
                <Link
                  to="/exercises"
                  className="inline-block px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 text-white font-bold rounded-lg shadow-xl hover:shadow-2xl transition-all transform hover:-translate-y-1 hover:scale-105"
                >
                  📚 Faire des exercices
                </Link>
              </div>
            </>
          ) : (
            // Utilisateur non connecté - Encourager à s'inscrire
            <>
              <h2 className="text-3xl font-bold mb-4">
                Prêt à révolutionner votre apprentissage des mathématiques ?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Commencez dès maintenant et découvrez une nouvelle façon d'apprendre.
              </p>
              <Link
                to="/register"
                className="inline-block px-10 py-4 bg-gradient-to-r from-primary-600 to-secondary-500 text-white font-bold rounded-lg shadow-xl hover:shadow-2xl transition-all transform hover:-translate-y-1 hover:scale-105"
              >
                Commencer gratuitement
              </Link>
            </>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default HomePage;
