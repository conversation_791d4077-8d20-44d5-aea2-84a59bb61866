import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../components/NotificationSystem';
import StudentLayout from '../components/StudentLayout';
import api from '../services/api';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  nom: string;
  prenom: string;
  role: string;
  niveau?: string;
  date_joined?: string;
  bio?: string;
  profile_picture?: string;
  stats?: {
    total_equations: number;
    total_exercises: number;
    total_points: number;
    success_rate: number;
  };
}

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { addNotification } = useNotifications();

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    prenom: '',
    nom: '',
    email: '',
    bio: ''
  });

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const response = await api.get('/users/me/');
      setProfile(response.data);
      setFormData({
        prenom: response.data.prenom || '',
        nom: response.data.nom || '',
        email: response.data.email || '',
        bio: response.data.bio || ''
      });
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error);
      addNotification({
        type: 'error',
        title: 'Erreur',
        message: 'Impossible de charger le profil'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const response = await api.patch('/users/me/', formData);
      setProfile(response.data);
      setEditing(false);
      updateUser(response.data);
      addNotification({
        type: 'success',
        title: 'Profil mis à jour',
        message: 'Vos informations ont été sauvegardées'
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      addNotification({
        type: 'error',
        title: 'Erreur',
        message: 'Impossible de sauvegarder le profil'
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      prenom: profile?.prenom || '',
      nom: profile?.nom || '',
      email: profile?.email || '',
      bio: profile?.bio || ''
    });
    setEditing(false);
  };

  if (!user) {
    return (
      <StudentLayout title="Profil">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Accès refusé</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Vous devez être connecté pour voir votre profil.
          </p>
        </div>
      </StudentLayout>
    );
  }

  if (loading) {
    return (
      <StudentLayout title="Profil">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement du profil...</p>
        </div>
      </StudentLayout>
    );
  }

  return (
    <StudentLayout
      title="Mon Profil"
      subtitle="Gérez vos informations personnelles et vos préférences"
    >
      <div className="max-w-4xl mx-auto">
          {/* En-tête du profil */}
          <motion.div
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold ${
                  user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'
                }`}>
                  {(profile?.prenom || user.username).charAt(0).toUpperCase()}
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {profile?.prenom && profile?.nom
                      ? `${profile.prenom} ${profile.nom}`
                      : profile?.username
                    }
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400">@{profile?.username}</p>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {user.role === 'admin' ? 'Administrateur' : 'Étudiant'}
                  </span>
                </div>
              </div>
              <button
                onClick={() => setEditing(!editing)}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                {editing ? 'Annuler' : 'Modifier'}
              </button>
            </div>

            {/* Informations du profil */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Prénom
                </label>
                {editing ? (
                  <input
                    type="text"
                    value={formData.prenom}
                    onChange={(e) => setFormData({...formData, prenom: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{profile?.prenom || 'Non renseigné'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nom
                </label>
                {editing ? (
                  <input
                    type="text"
                    value={formData.nom}
                    onChange={(e) => setFormData({...formData, nom: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{profile?.nom || 'Non renseigné'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email
                </label>
                {editing ? (
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{profile?.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Membre depuis
                </label>
                <p className="text-gray-900 dark:text-white">
                  {profile?.date_joined ? new Date(profile.date_joined).toLocaleDateString('fr-FR') : 'N/A'}
                </p>
              </div>
            </div>

            {/* Bio */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Biographie
              </label>
              {editing ? (
                <textarea
                  value={formData.bio}
                  onChange={(e) => setFormData({...formData, bio: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Parlez-nous de vous..."
                />
              ) : (
                <p className="text-gray-900 dark:text-white">
                  {profile?.bio || 'Aucune biographie renseignée.'}
                </p>
              )}
            </div>

            {/* Boutons de sauvegarde */}
            {editing && (
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                  Sauvegarder
                </button>
              </div>
            )}
          </motion.div>

          {/* Statistiques (pour les étudiants) */}
          {user.role === 'student' && profile?.stats && (
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                Mes statistiques
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{profile.stats.total_equations}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Équations résolues</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{profile.stats.total_exercises}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Exercices complétés</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{profile.stats.total_points}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Points gagnés</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{Math.round(profile.stats.success_rate)}%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Taux de réussite</div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
    </StudentLayout>
  );
};

export default ProfilePage;
