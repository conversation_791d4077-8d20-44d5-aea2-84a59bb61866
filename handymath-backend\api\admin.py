from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import (
    User, Course, Equation, Exercise, EquationImage, ExerciseChoice, ExerciseAttempt,
    Badge, UserBadge, UserLevel, Streak, Chapter, Lesson, LessonProgress,
    CourseEnrollment, LearningPath, LearningPathCourse
)

class UserAdmin(BaseUserAdmin):
    # Ajouter 'role' aux champs affichés
    list_display = ('username', 'email', 'role', 'is_staff')
    list_filter = ('role', 'is_staff', 'is_active')
    search_fields = ('username', 'email', 'first_name', 'last_name')

    # Ajouter 'role' aux fieldsets
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Informations supplémentaires', {'fields': ('role', 'bio', 'profile_picture')}),
    )

    # Ajouter 'role' aux champs lors de la création d'un utilisateur
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Informations supplémentaires', {'fields': ('role', 'bio', 'profile_picture')}),
    )

@admin.register(User)
class CustomUserAdmin(UserAdmin):
    pass

class ChapterInline(admin.TabularInline):
    model = Chapter
    extra = 1
    fields = ('title', 'order', 'is_published')


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('title', 'level', 'status', 'is_featured', 'order', 'created_at')
    list_filter = ('level', 'status', 'is_featured', 'created_at')
    search_fields = ('title', 'description')
    list_editable = ('order', 'is_featured', 'status')
    inlines = [ChapterInline]
    filter_horizontal = ('prerequisites',)

@admin.register(Equation)
class EquationAdmin(admin.ModelAdmin):
    list_display = ('equation_text', 'user', 'created_at', 'validated')
    list_filter = ('validated', 'created_at')
    search_fields = ('equation_text', 'user__username')

@admin.register(Exercise)
class ExerciseAdmin(admin.ModelAdmin):
    list_display = ('title', 'course', 'difficulty', 'exercise_type', 'points', 'created_at')
    list_filter = ('difficulty', 'exercise_type', 'course', 'created_at')
    search_fields = ('title', 'description', 'question')


class ExerciseChoiceInline(admin.TabularInline):
    model = ExerciseChoice
    extra = 4
    fields = ('choice_text', 'is_correct', 'order')


@admin.register(ExerciseChoice)
class ExerciseChoiceAdmin(admin.ModelAdmin):
    list_display = ('exercise', 'choice_text', 'is_correct', 'order')
    list_filter = ('is_correct', 'exercise__difficulty')
    search_fields = ('choice_text', 'exercise__title')


@admin.register(ExerciseAttempt)
class ExerciseAttemptAdmin(admin.ModelAdmin):
    list_display = ('user', 'exercise', 'is_correct', 'points_earned', 'status', 'started_at')
    list_filter = ('is_correct', 'status', 'exercise__difficulty', 'started_at')
    search_fields = ('user__username', 'exercise__title')
    readonly_fields = ('started_at', 'completed_at')


@admin.register(EquationImage)
class EquationImageAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'recognized_text', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('recognized_text', 'user__username')


@admin.register(Badge)
class BadgeAdmin(admin.ModelAdmin):
    list_display = ('icon', 'name', 'category', 'points_required', 'exercises_required', 'is_active')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    list_editable = ('is_active',)


@admin.register(UserBadge)
class UserBadgeAdmin(admin.ModelAdmin):
    list_display = ('user', 'badge', 'earned_at')
    list_filter = ('badge__category', 'earned_at')
    search_fields = ('user__username', 'badge__name')
    readonly_fields = ('earned_at',)


@admin.register(UserLevel)
class UserLevelAdmin(admin.ModelAdmin):
    list_display = ('user', 'current_level', 'total_xp', 'xp_to_next_level', 'updated_at')
    list_filter = ('current_level', 'updated_at')
    search_fields = ('user__username',)
    readonly_fields = ('updated_at',)


class LessonInline(admin.TabularInline):
    model = Lesson
    extra = 1
    fields = ('title', 'lesson_type', 'order', 'estimated_duration', 'is_published')


@admin.register(Chapter)
class ChapterAdmin(admin.ModelAdmin):
    list_display = ('title', 'course', 'order', 'is_published', 'created_at')
    list_filter = ('course', 'is_published', 'created_at')
    search_fields = ('title', 'course__title')
    list_editable = ('order', 'is_published')
    inlines = [LessonInline]


@admin.register(Lesson)
class LessonAdmin(admin.ModelAdmin):
    list_display = ('title', 'chapter', 'lesson_type', 'order', 'estimated_duration', 'is_published')
    list_filter = ('lesson_type', 'is_published', 'chapter__course')
    search_fields = ('title', 'chapter__title', 'content')
    list_editable = ('order', 'is_published')


@admin.register(LessonProgress)
class LessonProgressAdmin(admin.ModelAdmin):
    list_display = ('user', 'lesson', 'completed', 'time_spent', 'started_at', 'completed_at')
    list_filter = ('completed', 'lesson__lesson_type', 'started_at')
    search_fields = ('user__username', 'lesson__title')
    readonly_fields = ('started_at', 'completed_at')


@admin.register(CourseEnrollment)
class CourseEnrollmentAdmin(admin.ModelAdmin):
    list_display = ('user', 'course', 'enrolled_at', 'completed_at', 'is_active')
    list_filter = ('is_active', 'enrolled_at', 'completed_at')
    search_fields = ('user__username', 'course__title')
    readonly_fields = ('enrolled_at', 'completed_at')


class LearningPathCourseInline(admin.TabularInline):
    model = LearningPathCourse
    extra = 1
    fields = ('course', 'order', 'is_optional')


@admin.register(LearningPath)
class LearningPathAdmin(admin.ModelAdmin):
    list_display = ('title', 'difficulty', 'estimated_duration', 'is_featured', 'is_published', 'created_at')
    list_filter = ('difficulty', 'is_featured', 'is_published', 'created_at')
    search_fields = ('title', 'description')
    list_editable = ('is_featured', 'is_published')
    inlines = [LearningPathCourseInline]


@admin.register(LearningPathCourse)
class LearningPathCourseAdmin(admin.ModelAdmin):
    list_display = ('learning_path', 'course', 'order', 'is_optional')
    list_filter = ('is_optional', 'learning_path')
    search_fields = ('learning_path__title', 'course__title')
    list_editable = ('order', 'is_optional')

