{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\nimport { csMarked } from './csMarked.js';\nimport { csMark } from './csMark.js';\nimport { csDfs } from './csDfs.js';\n\n/**\n * The csReach function computes X = Reach(B), where B is the nonzero pattern of the n-by-1\n * sparse column of vector b. The function returns the set of nodes reachable from any node in B. The\n * nonzero pattern xi of the solution x to the sparse linear system Lx=b is given by X=Reach(B).\n *\n * @param {Matrix}  g               The G matrix\n * @param {Matrix}  b               The B matrix\n * @param {Number}  k               The kth column in B\n * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n * @param {Array}   pinv            The inverse row permutation vector\n *\n * @return {Number}                 The index for the nonzero pattern\n */\nexport function csReach(g, b, k, xi, pinv) {\n  // g arrays\n  var gptr = g._ptr;\n  var gsize = g._size;\n  // b arrays\n  var bindex = b._index;\n  var bptr = b._ptr;\n  // columns\n  var n = gsize[1];\n  // vars\n  var p, p0, p1;\n  // initialize top\n  var top = n;\n  // loop column indeces in B\n  for (p0 = bptr[k], p1 = bptr[k + 1], p = p0; p < p1; p++) {\n    // node i\n    var i = bindex[p];\n    // check node i is marked\n    if (!csMarked(gptr, i)) {\n      // start a dfs at unmarked node i\n      top = csDfs(i, g, top, xi, pinv);\n    }\n  }\n  // loop columns from top -> n - 1\n  for (p = top; p < n; p++) {\n    // restore G\n    csMark(gptr, xi[p]);\n  }\n  return top;\n}", "map": {"version": 3, "names": ["csMarked", "csMark", "csDfs", "csReach", "g", "b", "k", "xi", "pinv", "gptr", "_ptr", "gsize", "_size", "bindex", "_index", "bptr", "n", "p", "p0", "p1", "top", "i"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csReach.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\nimport { csMarked } from './csMarked.js';\nimport { csMark } from './csMark.js';\nimport { csDfs } from './csDfs.js';\n\n/**\n * The csReach function computes X = Reach(B), where B is the nonzero pattern of the n-by-1\n * sparse column of vector b. The function returns the set of nodes reachable from any node in B. The\n * nonzero pattern xi of the solution x to the sparse linear system Lx=b is given by X=Reach(B).\n *\n * @param {Matrix}  g               The G matrix\n * @param {Matrix}  b               The B matrix\n * @param {Number}  k               The kth column in B\n * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n * @param {Array}   pinv            The inverse row permutation vector\n *\n * @return {Number}                 The index for the nonzero pattern\n */\nexport function csReach(g, b, k, xi, pinv) {\n  // g arrays\n  var gptr = g._ptr;\n  var gsize = g._size;\n  // b arrays\n  var bindex = b._index;\n  var bptr = b._ptr;\n  // columns\n  var n = gsize[1];\n  // vars\n  var p, p0, p1;\n  // initialize top\n  var top = n;\n  // loop column indeces in B\n  for (p0 = bptr[k], p1 = bptr[k + 1], p = p0; p < p1; p++) {\n    // node i\n    var i = bindex[p];\n    // check node i is marked\n    if (!csMarked(gptr, i)) {\n      // start a dfs at unmarked node i\n      top = csDfs(i, g, top, xi, pinv);\n    }\n  }\n  // loop columns from top -> n - 1\n  for (p = top; p < n; p++) {\n    // restore G\n    csMark(gptr, xi[p]);\n  }\n  return top;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,KAAK,QAAQ,YAAY;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAE;EACzC;EACA,IAAIC,IAAI,GAAGL,CAAC,CAACM,IAAI;EACjB,IAAIC,KAAK,GAAGP,CAAC,CAACQ,KAAK;EACnB;EACA,IAAIC,MAAM,GAAGR,CAAC,CAACS,MAAM;EACrB,IAAIC,IAAI,GAAGV,CAAC,CAACK,IAAI;EACjB;EACA,IAAIM,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;EAChB;EACA,IAAIM,CAAC,EAAEC,EAAE,EAAEC,EAAE;EACb;EACA,IAAIC,GAAG,GAAGJ,CAAC;EACX;EACA,KAAKE,EAAE,GAAGH,IAAI,CAACT,CAAC,CAAC,EAAEa,EAAE,GAAGJ,IAAI,CAACT,CAAC,GAAG,CAAC,CAAC,EAAEW,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;IACxD;IACA,IAAII,CAAC,GAAGR,MAAM,CAACI,CAAC,CAAC;IACjB;IACA,IAAI,CAACjB,QAAQ,CAACS,IAAI,EAAEY,CAAC,CAAC,EAAE;MACtB;MACAD,GAAG,GAAGlB,KAAK,CAACmB,CAAC,EAAEjB,CAAC,EAAEgB,GAAG,EAAEb,EAAE,EAAEC,IAAI,CAAC;IAClC;EACF;EACA;EACA,KAAKS,CAAC,GAAGG,GAAG,EAAEH,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;IACxB;IACAhB,MAAM,CAACQ,IAAI,EAAEF,EAAE,CAACU,CAAC,CAAC,CAAC;EACrB;EACA,OAAOG,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}