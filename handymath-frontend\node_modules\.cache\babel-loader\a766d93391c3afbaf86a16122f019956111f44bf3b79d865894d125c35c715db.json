{"ast": null, "code": "import { isCollection } from '../../../utils/is.js';\nimport { dimToZeroBase, isNumberOrBigNumber } from './dimToZeroBase.js';\n/**\n * Change last argument dim from one-based to zero-based.\n */\nexport function lastDimToZeroBase(args) {\n  if (args.length === 2 && isCollection(args[0])) {\n    args = args.slice();\n    var dim = args[1];\n    if (isNumberOrBigNumber(dim)) {\n      args[1] = dimToZeroBase(dim);\n    }\n  }\n  return args;\n}", "map": {"version": 3, "names": ["isCollection", "dimToZeroBase", "isNumberOrBigNumber", "lastDimToZeroBase", "args", "length", "slice", "dim"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/transform/utils/lastDimToZeroBase.js"], "sourcesContent": ["import { isCollection } from '../../../utils/is.js';\nimport { dimToZeroBase, isNumberOrBigNumber } from './dimToZeroBase.js';\n/**\n * Change last argument dim from one-based to zero-based.\n */\nexport function lastDimToZeroBase(args) {\n  if (args.length === 2 && isCollection(args[0])) {\n    args = args.slice();\n    var dim = args[1];\n    if (isNumberOrBigNumber(dim)) {\n      args[1] = dimToZeroBase(dim);\n    }\n  }\n  return args;\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,sBAAsB;AACnD,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,oBAAoB;AACvE;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIL,YAAY,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9CA,IAAI,GAAGA,IAAI,CAACE,KAAK,CAAC,CAAC;IACnB,IAAIC,GAAG,GAAGH,IAAI,CAAC,CAAC,CAAC;IACjB,IAAIF,mBAAmB,CAACK,GAAG,CAAC,EAAE;MAC5BH,IAAI,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACM,GAAG,CAAC;IAC9B;EACF;EACA,OAAOH,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}