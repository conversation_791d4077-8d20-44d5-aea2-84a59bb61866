{"ast": null, "code": "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function (global, module, define) {\n  function XorGen(seed) {\n    var me = this;\n\n    // Set up generator function.\n    me.next = function () {\n      var w = me.w,\n        X = me.X,\n        i = me.i,\n        t,\n        v;\n      // Update Weyl generator.\n      me.w = w = w + 0x61c88647 | 0;\n      // Update xor generator.\n      v = X[i + 34 & 127];\n      t = X[i = i + 1 & 127];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      // Update Xor generator array state.\n      v = X[i] = v ^ t;\n      me.i = i;\n      // Result is the combination.\n      return v + (w ^ w >>> 16) | 0;\n    };\n    function init(me, seed) {\n      var t,\n        v,\n        i,\n        j,\n        w,\n        X = [],\n        limit = 128;\n      if (seed === (seed | 0)) {\n        // Numeric seeds initialize v, which is used to generates X.\n        v = seed;\n        seed = null;\n      } else {\n        // String seeds are mixed into v and X one character at a time.\n        seed = seed + '\\0';\n        v = 0;\n        limit = Math.max(limit, seed.length);\n      }\n      // Initialize circular array and weyl value.\n      for (i = 0, j = -32; j < limit; ++j) {\n        // Put the unicode characters into the array, and shuffle them.\n        if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n        // After 32 shuffles, take v as the starting w value.\n        if (j === 0) w = v;\n        v ^= v << 10;\n        v ^= v >>> 15;\n        v ^= v << 4;\n        v ^= v >>> 13;\n        if (j >= 0) {\n          w = w + 0x61c88647 | 0; // Weyl.\n          t = X[j & 127] ^= v + w; // Combine xor and weyl to init array.\n          i = 0 == t ? i + 1 : 0; // Count zeroes.\n        }\n      }\n      // We have detected all zeroes; make the key nonzero.\n      if (i >= 128) {\n        X[(seed && seed.length || 0) & 127] = -1;\n      }\n      // Run the generator 512 times to further mix the state before using it.\n      // Factoring this as a function slows the main generator, so it is just\n      // unrolled here.  The weyl generator is not advanced while warming up.\n      i = 127;\n      for (j = 4 * 128; j > 0; --j) {\n        v = X[i + 34 & 127];\n        t = X[i = i + 1 & 127];\n        v ^= v << 13;\n        t ^= t << 17;\n        v ^= v >>> 15;\n        t ^= t >>> 12;\n        X[i] = v ^ t;\n      }\n      // Storing state as object members is faster than using closure variables.\n      me.w = w;\n      me.X = X;\n      me.i = i;\n    }\n    init(me, seed);\n  }\n  function copy(f, t) {\n    t.i = f.i;\n    t.w = f.w;\n    t.X = f.X.slice();\n    return t;\n  }\n  ;\n  function impl(seed, opts) {\n    if (seed == null) seed = +new Date();\n    var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function () {\n        return (xg.next() >>> 0) / 0x100000000;\n      };\n    prng.double = function () {\n      do {\n        var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n      } while (result === 0);\n      return result;\n    };\n    prng.int32 = xg.next;\n    prng.quick = prng;\n    if (state) {\n      if (state.X) copy(state, xg);\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n    return prng;\n  }\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.xor4096 = impl;\n  }\n})(this,\n// window object or global\ntypeof module == 'object' && module,\n// present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "XorGen", "seed", "me", "next", "w", "X", "i", "t", "v", "init", "j", "limit", "Math", "max", "length", "charCodeAt", "copy", "f", "slice", "impl", "opts", "Date", "xg", "state", "prng", "double", "top", "bot", "result", "int32", "quick", "exports", "amd", "xor4096"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/seedrandom/lib/xor4096.js"], "sourcesContent": ["// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAElC,SAASC,MAAMA,CAACC,IAAI,EAAE;IACpB,IAAIC,EAAE,GAAG,IAAI;;IAEb;IACAA,EAAE,CAACC,IAAI,GAAG,YAAW;MACnB,IAAIC,CAAC,GAAGF,EAAE,CAACE,CAAC;QACRC,CAAC,GAAGH,EAAE,CAACG,CAAC;QAAEC,CAAC,GAAGJ,EAAE,CAACI,CAAC;QAAEC,CAAC;QAAEC,CAAC;MAC5B;MACAN,EAAE,CAACE,CAAC,GAAGA,CAAC,GAAIA,CAAC,GAAG,UAAU,GAAI,CAAC;MAC/B;MACAI,CAAC,GAAGH,CAAC,CAAEC,CAAC,GAAG,EAAE,GAAI,GAAG,CAAC;MACrBC,CAAC,GAAGF,CAAC,CAACC,CAAC,GAAKA,CAAC,GAAG,CAAC,GAAI,GAAI,CAAC;MAC1BE,CAAC,IAAIA,CAAC,IAAI,EAAE;MACZD,CAAC,IAAIA,CAAC,IAAI,EAAE;MACZC,CAAC,IAAIA,CAAC,KAAK,EAAE;MACbD,CAAC,IAAIA,CAAC,KAAK,EAAE;MACb;MACAC,CAAC,GAAGH,CAAC,CAACC,CAAC,CAAC,GAAGE,CAAC,GAAGD,CAAC;MAChBL,EAAE,CAACI,CAAC,GAAGA,CAAC;MACR;MACA,OAAQE,CAAC,IAAIJ,CAAC,GAAIA,CAAC,KAAK,EAAG,CAAC,GAAI,CAAC;IACnC,CAAC;IAED,SAASK,IAAIA,CAACP,EAAE,EAAED,IAAI,EAAE;MACtB,IAAIM,CAAC;QAAEC,CAAC;QAAEF,CAAC;QAAEI,CAAC;QAAEN,CAAC;QAAEC,CAAC,GAAG,EAAE;QAAEM,KAAK,GAAG,GAAG;MACtC,IAAIV,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;QACvB;QACAO,CAAC,GAAGP,IAAI;QACRA,IAAI,GAAG,IAAI;MACb,CAAC,MAAM;QACL;QACAA,IAAI,GAAGA,IAAI,GAAG,IAAI;QAClBO,CAAC,GAAG,CAAC;QACLG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAEV,IAAI,CAACa,MAAM,CAAC;MACtC;MACA;MACA,KAAKR,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,GAAGC,KAAK,EAAE,EAAED,CAAC,EAAE;QACnC;QACA,IAAIT,IAAI,EAAEO,CAAC,IAAIP,IAAI,CAACc,UAAU,CAAC,CAACL,CAAC,GAAG,EAAE,IAAIT,IAAI,CAACa,MAAM,CAAC;QACtD;QACA,IAAIJ,CAAC,KAAK,CAAC,EAAEN,CAAC,GAAGI,CAAC;QAClBA,CAAC,IAAIA,CAAC,IAAI,EAAE;QACZA,CAAC,IAAIA,CAAC,KAAK,EAAE;QACbA,CAAC,IAAIA,CAAC,IAAI,CAAC;QACXA,CAAC,IAAIA,CAAC,KAAK,EAAE;QACb,IAAIE,CAAC,IAAI,CAAC,EAAE;UACVN,CAAC,GAAIA,CAAC,GAAG,UAAU,GAAI,CAAC,CAAC,CAAK;UAC9BG,CAAC,GAAIF,CAAC,CAACK,CAAC,GAAG,GAAG,CAAC,IAAKF,CAAC,GAAGJ,CAAG,CAAC,CAAE;UAC9BE,CAAC,GAAI,CAAC,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAK;QAChC;MACF;MACA;MACA,IAAIA,CAAC,IAAI,GAAG,EAAE;QACZD,CAAC,CAAC,CAACJ,IAAI,IAAIA,IAAI,CAACa,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;MAC1C;MACA;MACA;MACA;MACAR,CAAC,GAAG,GAAG;MACP,KAAKI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC5BF,CAAC,GAAGH,CAAC,CAAEC,CAAC,GAAG,EAAE,GAAI,GAAG,CAAC;QACrBC,CAAC,GAAGF,CAAC,CAACC,CAAC,GAAKA,CAAC,GAAG,CAAC,GAAI,GAAI,CAAC;QAC1BE,CAAC,IAAIA,CAAC,IAAI,EAAE;QACZD,CAAC,IAAIA,CAAC,IAAI,EAAE;QACZC,CAAC,IAAIA,CAAC,KAAK,EAAE;QACbD,CAAC,IAAIA,CAAC,KAAK,EAAE;QACbF,CAAC,CAACC,CAAC,CAAC,GAAGE,CAAC,GAAGD,CAAC;MACd;MACA;MACAL,EAAE,CAACE,CAAC,GAAGA,CAAC;MACRF,EAAE,CAACG,CAAC,GAAGA,CAAC;MACRH,EAAE,CAACI,CAAC,GAAGA,CAAC;IACV;IAEAG,IAAI,CAACP,EAAE,EAAED,IAAI,CAAC;EAChB;EAEA,SAASe,IAAIA,CAACC,CAAC,EAAEV,CAAC,EAAE;IAClBA,CAAC,CAACD,CAAC,GAAGW,CAAC,CAACX,CAAC;IACTC,CAAC,CAACH,CAAC,GAAGa,CAAC,CAACb,CAAC;IACTG,CAAC,CAACF,CAAC,GAAGY,CAAC,CAACZ,CAAC,CAACa,KAAK,CAAC,CAAC;IACjB,OAAOX,CAAC;EACV;EAAC;EAED,SAASY,IAAIA,CAAClB,IAAI,EAAEmB,IAAI,EAAE;IACxB,IAAInB,IAAI,IAAI,IAAI,EAAEA,IAAI,GAAG,CAAE,IAAIoB,IAAI,CAAD,CAAE;IACpC,IAAIC,EAAE,GAAG,IAAItB,MAAM,CAACC,IAAI,CAAC;MACrBsB,KAAK,GAAGH,IAAI,IAAIA,IAAI,CAACG,KAAK;MAC1BC,IAAI,GAAG,SAAAA,CAAA,EAAW;QAAE,OAAO,CAACF,EAAE,CAACnB,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;MAAE,CAAC;IACjEqB,IAAI,CAACC,MAAM,GAAG,YAAW;MACvB,GAAG;QACD,IAAIC,GAAG,GAAGJ,EAAE,CAACnB,IAAI,CAAC,CAAC,KAAK,EAAE;UACtBwB,GAAG,GAAG,CAACL,EAAE,CAACnB,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;UACrCyB,MAAM,GAAG,CAACF,GAAG,GAAGC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;MACtC,CAAC,QAAQC,MAAM,KAAK,CAAC;MACrB,OAAOA,MAAM;IACf,CAAC;IACDJ,IAAI,CAACK,KAAK,GAAGP,EAAE,CAACnB,IAAI;IACpBqB,IAAI,CAACM,KAAK,GAAGN,IAAI;IACjB,IAAID,KAAK,EAAE;MACT,IAAIA,KAAK,CAAClB,CAAC,EAAEW,IAAI,CAACO,KAAK,EAAED,EAAE,CAAC;MAC5BE,IAAI,CAACD,KAAK,GAAG,YAAW;QAAE,OAAOP,IAAI,CAACM,EAAE,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC;IAClD;IACA,OAAOE,IAAI;EACb;EAEA,IAAI1B,MAAM,IAAIA,MAAM,CAACiC,OAAO,EAAE;IAC5BjC,MAAM,CAACiC,OAAO,GAAGZ,IAAI;EACvB,CAAC,MAAM,IAAIpB,MAAM,IAAIA,MAAM,CAACiC,GAAG,EAAE;IAC/BjC,MAAM,CAAC,YAAW;MAAE,OAAOoB,IAAI;IAAE,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAI,CAACc,OAAO,GAAGd,IAAI;EACrB;AAEA,CAAC,EACC,IAAI;AAAsC;AACzC,OAAOrB,MAAM,IAAK,QAAQ,IAAIA,MAAM;AAAK;AACzC,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAAG;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}