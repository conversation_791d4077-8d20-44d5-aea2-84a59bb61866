{"ast": null, "code": "export var rightArithShiftDocs = {\n  name: 'rightArithShift',\n  category: 'Bitwise',\n  syntax: ['x >> y', 'rightArithShift(x, y)'],\n  description: 'Bitwise right arithmetic shift of a value x by y number of bits.',\n  examples: ['8 >> 1', '4 << 1', '-12 >> 2'],\n  seealso: ['bitAnd', 'bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightLogShift']\n};", "map": {"version": 3, "names": ["rightArithShiftDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/bitwise/rightArithShift.js"], "sourcesContent": ["export var rightArithShiftDocs = {\n  name: 'rightArithShift',\n  category: 'Bitwise',\n  syntax: ['x >> y', 'rightArithShift(x, y)'],\n  description: 'Bitwise right arithmetic shift of a value x by y number of bits.',\n  examples: ['8 >> 1', '4 << 1', '-12 >> 2'],\n  seealso: ['bitAnd', 'bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightLogShift']\n};"], "mappings": "AAAA,OAAO,IAAIA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,QAAQ,EAAE,uBAAuB,CAAC;EAC3CC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC1CC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe;AAC/E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}