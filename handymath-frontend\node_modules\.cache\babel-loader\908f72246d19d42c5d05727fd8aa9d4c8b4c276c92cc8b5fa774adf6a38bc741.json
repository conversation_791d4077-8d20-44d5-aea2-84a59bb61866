{"ast": null, "code": "export var zetaDocs = {\n  name: 'zeta',\n  category: 'Special',\n  syntax: ['zeta(s)'],\n  description: 'Compute the Riemann Zeta Function using an infinite series and <PERSON><PERSON>mann\\'s Functional Equation for the entire complex plane',\n  examples: ['zeta(0.2)', 'zeta(-0.5)', 'zeta(4)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["zetaDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/special/zeta.js"], "sourcesContent": ["export var zetaDocs = {\n  name: 'zeta',\n  category: 'Special',\n  syntax: ['zeta(s)'],\n  description: 'Compute the Riemann Zeta Function using an infinite series and <PERSON><PERSON>mann\\'s Functional Equation for the entire complex plane',\n  examples: ['zeta(0.2)', 'zeta(-0.5)', 'zeta(4)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,4HAA4H;EACzIC,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC;EAChDC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}