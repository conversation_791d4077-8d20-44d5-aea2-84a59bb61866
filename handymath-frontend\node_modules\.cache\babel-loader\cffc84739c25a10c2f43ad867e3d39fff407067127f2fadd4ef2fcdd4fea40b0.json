{"ast": null, "code": "export var notDocs = {\n  name: 'not',\n  category: 'Logical',\n  syntax: ['not x', 'not(x)'],\n  description: 'Logical not. Flips the boolean value of given argument.',\n  examples: ['not true', 'not false', 'not 2', 'not 0'],\n  seealso: ['and', 'or', 'xor']\n};", "map": {"version": 3, "names": ["notDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/logical/not.js"], "sourcesContent": ["export var notDocs = {\n  name: 'not',\n  category: 'Logical',\n  syntax: ['not x', 'not(x)'],\n  description: 'Logical not. Flips the boolean value of given argument.',\n  examples: ['not true', 'not false', 'not 2', 'not 0'],\n  seealso: ['and', 'or', 'xor']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC3BC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;EACrDC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}