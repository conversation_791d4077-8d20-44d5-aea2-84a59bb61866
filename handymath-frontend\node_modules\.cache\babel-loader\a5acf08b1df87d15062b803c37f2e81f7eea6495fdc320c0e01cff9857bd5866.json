{"ast": null, "code": "export var distanceDocs = {\n  name: 'distance',\n  category: 'Geometry',\n  syntax: ['distance([x1, y1], [x2, y2])', 'distance([[x1, y1], [x2, y2]])'],\n  description: 'Calculates the Euclidean distance between two points.',\n  examples: ['distance([0,0], [4,4])', 'distance([[0,0], [4,4]])'],\n  seealso: []\n};", "map": {"version": 3, "names": ["distanceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/geometry/distance.js"], "sourcesContent": ["export var distanceDocs = {\n  name: 'distance',\n  category: 'Geometry',\n  syntax: ['distance([x1, y1], [x2, y2])', 'distance([[x1, y1], [x2, y2]])'],\n  description: 'Calculates the Euclidean distance between two points.',\n  examples: ['distance([0,0], [4,4])', 'distance([[0,0], [4,4]])'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC,8BAA8B,EAAE,gCAAgC,CAAC;EAC1EC,WAAW,EAAE,uDAAuD;EACpEC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,0BAA0B,CAAC;EAChEC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}