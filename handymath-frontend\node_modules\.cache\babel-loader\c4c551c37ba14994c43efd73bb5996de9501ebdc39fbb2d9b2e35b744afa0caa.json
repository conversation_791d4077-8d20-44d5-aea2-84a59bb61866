{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSum } from '../../factoriesAny.js';\nexport var sumDependencies = {\n  addDependencies,\n  numericDependencies,\n  typedDependencies,\n  createSum\n};", "map": {"version": 3, "names": ["addDependencies", "numericDependencies", "typedDependencies", "createSum", "sumDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSum.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSum } from '../../factoriesAny.js';\nexport var sumDependencies = {\n  addDependencies,\n  numericDependencies,\n  typedDependencies,\n  createSum\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BJ,eAAe;EACfC,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}