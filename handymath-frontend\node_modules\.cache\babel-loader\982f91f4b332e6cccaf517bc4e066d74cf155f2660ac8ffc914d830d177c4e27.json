{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnitFunction } from '../../factoriesAny.js';\nexport var unitDependencies = {\n  UnitDependencies,\n  typedDependencies,\n  createUnitFunction\n};", "map": {"version": 3, "names": ["UnitDependencies", "typedDependencies", "createUnitFunction", "unitDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUnitFunction.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnitFunction } from '../../factoriesAny.js';\nexport var unitDependencies = {\n  UnitDependencies,\n  typedDependencies,\n  createUnitFunction\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,gBAAgB,GAAG;EAC5BH,gBAAgB;EAChBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}