{"ast": null, "code": "import { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nimport { complexEquals } from '../../utils/complex.js';\nimport { createCompareUnits } from './compareUnits.js';\nvar name = 'equalScalar';\nvar dependencies = ['typed', 'config'];\nexport var createEqualScalar = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config\n  } = _ref;\n  var compareUnits = createCompareUnits({\n    typed\n  });\n\n  /**\n   * Test whether two scalar values are nearly equal.\n   *\n   * @param  {number | BigNumber | bigint | Fraction | boolean | Complex | Unit} x   First value to compare\n   * @param  {number | BigNumber | bigint | Fraction | boolean | Complex} y          Second value to compare\n   * @return {boolean}                                                  Returns true when the compared values are equal, else returns false\n   * @private\n   */\n  return typed(name, {\n    'boolean, boolean': function boolean_boolean(x, y) {\n      return x === y;\n    },\n    'number, number': function number_number(x, y) {\n      return nearlyEqual(x, y, config.relTol, config.absTol);\n    },\n    'BigNumber, BigNumber': function BigNumber_BigNumber(x, y) {\n      return x.eq(y) || bigNearlyEqual(x, y, config.relTol, config.absTol);\n    },\n    'bigint, bigint': function bigint_bigint(x, y) {\n      return x === y;\n    },\n    'Fraction, Fraction': function Fraction_Fraction(x, y) {\n      return x.equals(y);\n    },\n    'Complex, Complex': function Complex_Complex(x, y) {\n      return complexEquals(x, y, config.relTol, config.absTol);\n    }\n  }, compareUnits);\n});\nexport var createEqualScalarNumber = factory(name, ['typed', 'config'], _ref2 => {\n  var {\n    typed,\n    config\n  } = _ref2;\n  return typed(name, {\n    'number, number': function number_number(x, y) {\n      return nearlyEqual(x, y, config.relTol, config.absTol);\n    }\n  });\n});", "map": {"version": 3, "names": ["nearlyEqual", "bigNearlyEqual", "factory", "complexEquals", "createCompareUnits", "name", "dependencies", "createEqualScalar", "_ref", "typed", "config", "compareUnits", "boolean_boolean", "x", "y", "number_number", "relTol", "absTol", "BigNumber_BigNumber", "eq", "bigint_bigint", "Fraction_Fraction", "equals", "Complex_Complex", "createEqualScalarNumber", "_ref2"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/relational/equalScalar.js"], "sourcesContent": ["import { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nimport { complexEquals } from '../../utils/complex.js';\nimport { createCompareUnits } from './compareUnits.js';\nvar name = 'equalScalar';\nvar dependencies = ['typed', 'config'];\nexport var createEqualScalar = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config\n  } = _ref;\n  var compareUnits = createCompareUnits({\n    typed\n  });\n\n  /**\n   * Test whether two scalar values are nearly equal.\n   *\n   * @param  {number | BigNumber | bigint | Fraction | boolean | Complex | Unit} x   First value to compare\n   * @param  {number | BigNumber | bigint | Fraction | boolean | Complex} y          Second value to compare\n   * @return {boolean}                                                  Returns true when the compared values are equal, else returns false\n   * @private\n   */\n  return typed(name, {\n    'boolean, boolean': function boolean_boolean(x, y) {\n      return x === y;\n    },\n    'number, number': function number_number(x, y) {\n      return nearlyEqual(x, y, config.relTol, config.absTol);\n    },\n    'BigNumber, BigNumber': function BigNumber_BigNumber(x, y) {\n      return x.eq(y) || bigNearlyEqual(x, y, config.relTol, config.absTol);\n    },\n    'bigint, bigint': function bigint_bigint(x, y) {\n      return x === y;\n    },\n    'Fraction, Fraction': function Fraction_Fraction(x, y) {\n      return x.equals(y);\n    },\n    'Complex, Complex': function Complex_Complex(x, y) {\n      return complexEquals(x, y, config.relTol, config.absTol);\n    }\n  }, compareUnits);\n});\nexport var createEqualScalarNumber = factory(name, ['typed', 'config'], _ref2 => {\n  var {\n    typed,\n    config\n  } = _ref2;\n  return typed(name, {\n    'number, number': function number_number(x, y) {\n      return nearlyEqual(x, y, config.relTol, config.absTol);\n    }\n  });\n});"], "mappings": "AAAA,SAASA,WAAW,IAAIC,cAAc,QAAQ,sCAAsC;AACpF,SAASD,WAAW,QAAQ,uBAAuB;AACnD,SAASE,OAAO,QAAQ,wBAAwB;AAChD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,IAAIC,IAAI,GAAG,aAAa;AACxB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AACtC,OAAO,IAAIC,iBAAiB,GAAG,eAAeL,OAAO,CAACG,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAChF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR,IAAIG,YAAY,GAAGP,kBAAkB,CAAC;IACpCK;EACF,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOA,KAAK,CAACJ,IAAI,EAAE;IACjB,kBAAkB,EAAE,SAASO,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACjD,OAAOD,CAAC,KAAKC,CAAC;IAChB,CAAC;IACD,gBAAgB,EAAE,SAASC,aAAaA,CAACF,CAAC,EAAEC,CAAC,EAAE;MAC7C,OAAOd,WAAW,CAACa,CAAC,EAAEC,CAAC,EAAEJ,MAAM,CAACM,MAAM,EAAEN,MAAM,CAACO,MAAM,CAAC;IACxD,CAAC;IACD,sBAAsB,EAAE,SAASC,mBAAmBA,CAACL,CAAC,EAAEC,CAAC,EAAE;MACzD,OAAOD,CAAC,CAACM,EAAE,CAACL,CAAC,CAAC,IAAIb,cAAc,CAACY,CAAC,EAAEC,CAAC,EAAEJ,MAAM,CAACM,MAAM,EAAEN,MAAM,CAACO,MAAM,CAAC;IACtE,CAAC;IACD,gBAAgB,EAAE,SAASG,aAAaA,CAACP,CAAC,EAAEC,CAAC,EAAE;MAC7C,OAAOD,CAAC,KAAKC,CAAC;IAChB,CAAC;IACD,oBAAoB,EAAE,SAASO,iBAAiBA,CAACR,CAAC,EAAEC,CAAC,EAAE;MACrD,OAAOD,CAAC,CAACS,MAAM,CAACR,CAAC,CAAC;IACpB,CAAC;IACD,kBAAkB,EAAE,SAASS,eAAeA,CAACV,CAAC,EAAEC,CAAC,EAAE;MACjD,OAAOX,aAAa,CAACU,CAAC,EAAEC,CAAC,EAAEJ,MAAM,CAACM,MAAM,EAAEN,MAAM,CAACO,MAAM,CAAC;IAC1D;EACF,CAAC,EAAEN,YAAY,CAAC;AAClB,CAAC,CAAC;AACF,OAAO,IAAIa,uBAAuB,GAAGtB,OAAO,CAACG,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAEoB,KAAK,IAAI;EAC/E,IAAI;IACFhB,KAAK;IACLC;EACF,CAAC,GAAGe,KAAK;EACT,OAAOhB,KAAK,CAACJ,IAAI,EAAE;IACjB,gBAAgB,EAAE,SAASU,aAAaA,CAACF,CAAC,EAAEC,CAAC,EAAE;MAC7C,OAAOd,WAAW,CAACa,CAAC,EAAEC,CAAC,EAAEJ,MAAM,CAACM,MAAM,EAAEN,MAAM,CAACO,MAAM,CAAC;IACxD;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}