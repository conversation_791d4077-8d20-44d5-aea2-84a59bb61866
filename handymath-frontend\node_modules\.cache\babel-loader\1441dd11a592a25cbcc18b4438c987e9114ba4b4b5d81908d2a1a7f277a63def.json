{"ast": null, "code": "export var subsetDocs = {\n  name: 'subset',\n  category: 'Matrix',\n  syntax: ['value(index)', 'value(index) = replacement', 'subset(value, [index])', 'subset(value, [index], replacement)'],\n  description: 'Get or set a subset of the entries of a matrix or ' + 'characters of a string. ' + 'Indexes are one-based. There should be one index specification for ' + 'each dimension of the target. Each specification can be a single ' + 'index, a list of indices, or a range in colon notation `l:u`. ' + 'In a range, both the lower bound l and upper bound u are included; ' + 'and if a bound is omitted it defaults to the most extreme valid value. ' + 'The cartesian product of the indices specified in each dimension ' + 'determines the target of the operation.',\n  examples: ['d = [1, 2; 3, 4]', 'e = []', 'e[1, 1:2] = [5, 6]', 'e[2, :] = [7, 8]', 'f = d * e', 'f[2, 1]', 'f[:, 1]', 'f[[1,2], [1,3]] = [9, 10; 11, 12]', 'f'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["subsetDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/subset.js"], "sourcesContent": ["export var subsetDocs = {\n  name: 'subset',\n  category: 'Matrix',\n  syntax: ['value(index)', 'value(index) = replacement', 'subset(value, [index])', 'subset(value, [index], replacement)'],\n  description: 'Get or set a subset of the entries of a matrix or ' + 'characters of a string. ' + 'Indexes are one-based. There should be one index specification for ' + 'each dimension of the target. Each specification can be a single ' + 'index, a list of indices, or a range in colon notation `l:u`. ' + 'In a range, both the lower bound l and upper bound u are included; ' + 'and if a bound is omitted it defaults to the most extreme valid value. ' + 'The cartesian product of the indices specified in each dimension ' + 'determines the target of the operation.',\n  examples: ['d = [1, 2; 3, 4]', 'e = []', 'e[1, 1:2] = [5, 6]', 'e[2, :] = [7, 8]', 'f = d * e', 'f[2, 1]', 'f[:, 1]', 'f[[1,2], [1,3]] = [9, 10; 11, 12]', 'f'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,cAAc,EAAE,4BAA4B,EAAE,wBAAwB,EAAE,qCAAqC,CAAC;EACvHC,WAAW,EAAE,oDAAoD,GAAG,0BAA0B,GAAG,qEAAqE,GAAG,mEAAmE,GAAG,gEAAgE,GAAG,qEAAqE,GAAG,yEAAyE,GAAG,mEAAmE,GAAG,yCAAyC;EACrjBC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,mCAAmC,EAAE,GAAG,CAAC;EAC/JC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AACzH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}