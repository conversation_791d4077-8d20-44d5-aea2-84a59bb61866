import React, { useState } from 'react';
import { motion } from 'framer-motion';
import api from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../components/NotificationSystem';
import ImageUpload from '../components/ImageUpload';
import SimpleHeader from '../components/SimpleHeader';

const SolverPage: React.FC = () => {
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // Helper functions for notifications
  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message });
  };
  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };
  const showWarning = (title: string, message: string) => {
    addNotification({ type: 'warning', title, message });
  };

  // State
  const [equation, setEquation] = useState('');
  const [solution, setSolution] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [inputMethod, setInputMethod] = useState<'text' | 'image'>('text');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [ocrInfo, setOcrInfo] = useState<{rawText?: string, confidence?: number}>({});

  // Event Handlers
  const handleSolve = async () => {
    if (!equation.trim()) {
      showWarning('Équation manquante', 'Veuillez entrer une équation');
      return;
    }

    if (!user) {
      showError('Authentification requise', 'Vous devez être connecté pour résoudre des équations');
      return;
    }

    setLoading(true);
    setSolution(null);

    try {
      const response = await api.post('/equations/solve/', {
        equation: equation.trim()
      });

      if (response.data) {
        const { solution: solutionText, steps, equation_id } = response.data;

        // Debug: Afficher les données reçues
        console.log('🔍 Données reçues de l\'API:', response.data);
        console.log('📋 Étapes reçues:', steps);
        console.log('📊 Nombre d\'étapes:', steps ? steps.length : 0);

        setSolution({
          result: solutionText,
          steps: steps || [],
          equation_id: equation_id
        });
        showSuccess('Équation résolue !', `L'équation a été résolue avec succès. ${steps ? steps.length : 0} étapes générées.`);
      }
    } catch (error: any) {
      console.error('Erreur lors de la résolution:', error);
      if (error.response?.data?.error) {
        showError('Erreur de résolution', error.response.data.error);
      } else if (error.response?.status === 401) {
        showError('Session expirée', 'Veuillez vous reconnecter.');
      } else if (error.response?.status === 400) {
        showError('Équation invalide', 'Vérifiez la syntaxe de votre équation.');
      } else {
        showError('Erreur de résolution', 'Une erreur est survenue lors de la résolution.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setEquation('');
    setSolution(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !loading && user) {
      handleSolve();
    }
  };

  // Gestion de l'upload d'image
  const handleImageUpload = async (file: File) => {
    if (!user) {
      showError('Authentification requise', 'Vous devez être connecté pour utiliser la reconnaissance d\'image');
      return;
    }

    setImageLoading(true);
    setSolution(null);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await api.post('/equations/recognize/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data && response.data.recognized_text) {
        const recognizedEquation = response.data.recognized_text;
        const confidence = response.data.confidence || 0;
        const suggestions = response.data.suggestions || [];
        const rawText = response.data.ocr_raw_text || '';

        setEquation(recognizedEquation);
        setSuggestions(suggestions);
        setOcrInfo({ rawText, confidence });

        if (confidence >= 0.8) {
          showSuccess('Équation reconnue !', `Équation détectée: ${recognizedEquation} (${Math.round(confidence * 100)}%)`);
        } else if (confidence >= 0.5) {
          showWarning('Reconnaissance partielle', `Équation détectée: ${recognizedEquation} (${Math.round(confidence * 100)}%). Vérifiez le résultat.`);
        } else {
          showWarning('Reconnaissance difficile', `Texte OCR: "${rawText}". ${suggestions.length > 0 ? 'Suggestions disponibles ci-dessous.' : 'Essayez avec une image plus claire.'}`);
        }

        // Optionnel : résoudre automatiquement l'équation reconnue
        if (response.data.auto_solve && response.data.solution) {
          setSolution({
            result: response.data.solution,
            steps: response.data.steps || [],
            equation_id: response.data.equation_id
          });
        }
      } else {
        showWarning('Reconnaissance échouée', 'Impossible de reconnaître l\'équation. Essayez avec une image plus claire.');
      }
    } catch (error: any) {
      console.error('Erreur lors de la reconnaissance:', error);
      if (error.response?.data?.error) {
        showError('Erreur de reconnaissance', error.response.data.error);
      } else if (error.response?.status === 401) {
        showError('Session expirée', 'Veuillez vous reconnecter.');
      } else if (error.response?.status === 413) {
        showError('Fichier trop volumineux', 'L\'image est trop grande. Taille maximale : 5MB');
      } else {
        showError('Erreur de reconnaissance', 'Une erreur est survenue lors de la reconnaissance de l\'image.');
      }
    } finally {
      setImageLoading(false);
    }
  };

  const handleEquationRecognized = (recognizedEquation: string) => {
    setEquation(recognizedEquation);
  };

  return (
    <>
      <SimpleHeader title="Résolution d'équations" />
      <div className="container mx-auto px-4 py-8">
        <motion.h1
          className="text-4xl font-bold mb-8 text-center text-primary-600"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          Résolution d'équations
        </motion.h1>

      <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        {/* Onglets de sélection */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6">
          <button
            onClick={() => setInputMethod('text')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
              inputMethod === 'text'
                ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            }`}
          >
            ⌨️ Saisie manuelle
          </button>
          <button
            onClick={() => setInputMethod('image')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
              inputMethod === 'image'
                ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            }`}
          >
            📸 Reconnaissance manuscrite
          </button>
        </div>

        {/* Contenu selon la méthode sélectionnée */}
        <div className="mb-6">
          {inputMethod === 'text' ? (
            <div>
              <label htmlFor="equation" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Entrez votre équation
              </label>
              <div className="flex">
                <input
                  type="text"
                  id="equation"
                  value={equation}
                  onChange={(e) => setEquation(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Ex: 2x + 3 = 7, x^2 - 4 = 0"
                  className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  disabled={loading || imageLoading}
                />
                <button
                  onClick={handleSolve}
                  disabled={loading || imageLoading || !user || !equation.trim()}
                  className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-r-lg transition-colors"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Résolution...
                    </div>
                  ) : 'Résoudre'}
                </button>
                {equation && (
                  <button
                    onClick={handleClear}
                    className="ml-2 bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    Effacer
                  </button>
                )}
              </div>

              {/* Exemples d'équations */}
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Exemples d'équations :
                </p>
                <div className="flex flex-wrap gap-2">
                  {[
                    "2x + 3 = 7",
                    "x^2 - 4 = 0",
                    "3x - 7 = 2x + 8"
                  ].map((example, index) => (
                    <button
                      key={index}
                      onClick={() => setEquation(example)}
                      className="text-xs px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      disabled={loading || imageLoading}
                    >
                      {example}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <ImageUpload
                onImageUpload={handleImageUpload}
                onEquationRecognized={handleEquationRecognized}
                loading={imageLoading}
                disabled={loading || !user}
              />

              {/* Suggestions d'équations si la reconnaissance a échoué */}
              {suggestions.length > 0 && (
                <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
                  <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-3">
                    🤔 La reconnaissance n'est pas parfaite ? Essayez ces suggestions :
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          setEquation(suggestion);
                          setSuggestions([]);
                          showSuccess('Équation sélectionnée', `Équation choisie: ${suggestion}`);
                        }}
                        className="text-left p-3 bg-white dark:bg-gray-700 rounded-lg border border-yellow-300 dark:border-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-800/30 transition-colors"
                      >
                        <span className="font-mono text-sm text-gray-900 dark:text-white">
                          {suggestion}
                        </span>
                      </button>
                    ))}
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <p className="text-xs text-yellow-700 dark:text-yellow-400">
                      {ocrInfo.rawText && `Texte OCR détecté: "${ocrInfo.rawText}"`}
                    </p>
                    <button
                      onClick={() => setSuggestions([])}
                      className="text-xs text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200"
                    >
                      Masquer les suggestions
                    </button>
                  </div>
                </div>
              )}

              {equation && (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                  <h4 className="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
                    ✅ Équation reconnue :
                  </h4>
                  <p className="text-lg font-mono text-green-700 dark:text-green-400 mb-3">
                    {equation}
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSolve}
                      disabled={loading || imageLoading || !user}
                      className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
                        loading || imageLoading || !user
                          ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                          : 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl'
                      }`}
                    >
                      {loading ? 'Résolution en cours...' : 'Résoudre cette équation'}
                    </button>
                    <button
                      onClick={() => setEquation('')}
                      className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                    >
                      Effacer
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Avertissement pour utilisateurs non connectés */}
        {!user && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-200 rounded-lg"
          >
            <div className="flex items-center">
              <span className="text-xl mr-2">⚠️</span>
              <span>
                Vous devez être connecté pour résoudre des équations.{' '}
                <a href="/login" className="underline font-medium">Se connecter</a>
              </span>
            </div>
          </motion.div>
        )}

        {/* Section solution */}
        {solution && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-semibold text-primary-600">Solution</h2>
              {solution.equation_id && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  ID: #{solution.equation_id}
                </div>
              )}
            </div>

            {/* Résultat final */}
            <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 rounded-lg mb-6 border border-green-200 dark:border-green-700">
              <div className="flex items-center mb-2">
                <span className="text-lg font-semibold text-gray-700 dark:text-gray-300">Résultat final :</span>
              </div>
              <p className="text-2xl font-mono font-bold text-green-700 dark:text-green-300">
                {solution.result}
              </p>
            </div>

            {/* Étapes de résolution détaillées */}
            {solution.steps && solution.steps.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    📚 Étapes de résolution détaillées
                    <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
                      ({solution.steps.length} étapes)
                    </span>
                  </h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {solution.steps.map((step: string, index: number) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-sm font-semibold">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-900 dark:text-white font-mono text-sm leading-relaxed">
                            {step}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </div>
    </>
  );
};

export default SolverPage;
