{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnaryMinus } from '../../factoriesAny.js';\nexport var unaryMinusDependencies = {\n  typedDependencies,\n  createUnaryMinus\n};", "map": {"version": 3, "names": ["typedDependencies", "createUnaryMinus", "unaryMinusDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUnaryMinus.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnaryMinus } from '../../factoriesAny.js';\nexport var unaryMinusDependencies = {\n  typedDependencies,\n  createUnaryMinus\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,sBAAsB,GAAG;EAClCF,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}