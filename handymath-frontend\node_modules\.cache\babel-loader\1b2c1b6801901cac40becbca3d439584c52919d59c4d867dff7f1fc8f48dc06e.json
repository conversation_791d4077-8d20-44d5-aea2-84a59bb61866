{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\nimport { factory } from '../../../utils/factory.js';\nimport { createCsSpsolve } from './csSpsolve.js';\nvar name = 'csLu';\nvar dependencies = ['abs', 'divideScalar', 'multiply', 'subtract', 'larger', 'largerEq', 'SparseMatrix'];\nexport var createCsLu = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    abs,\n    divideScalar,\n    multiply,\n    subtract,\n    larger,\n    largerEq,\n    SparseMatrix\n  } = _ref;\n  var csSpsolve = createCsSpsolve({\n    divideScalar,\n    multiply,\n    subtract\n  });\n\n  /**\n   * Computes the numeric LU factorization of the sparse matrix A. Implements a Left-looking LU factorization\n   * algorithm that computes L and U one column at a tume. At the kth step, it access columns 1 to k-1 of L\n   * and column k of A. Given the fill-reducing column ordering q (see parameter s) computes L, U and pinv so\n   * L * U = A(p, q), where p is the inverse of pinv.\n   *\n   * @param {Matrix}  m               The A Matrix to factorize\n   * @param {Object}  s               The symbolic analysis from csSqr(). Provides the fill-reducing\n   *                                  column ordering q\n   * @param {Number}  tol             Partial pivoting threshold (1 for partial pivoting)\n   *\n   * @return {Number}                 The numeric LU factorization of A or null\n   */\n  return function csLu(m, s, tol) {\n    // validate input\n    if (!m) {\n      return null;\n    }\n    // m arrays\n    var size = m._size;\n    // columns\n    var n = size[1];\n    // symbolic analysis result\n    var q;\n    var lnz = 100;\n    var unz = 100;\n    // update symbolic analysis parameters\n    if (s) {\n      q = s.q;\n      lnz = s.lnz || lnz;\n      unz = s.unz || unz;\n    }\n    // L arrays\n    var lvalues = []; // (lnz)\n    var lindex = []; // (lnz)\n    var lptr = []; // (n + 1)\n    // L\n    var L = new SparseMatrix({\n      values: lvalues,\n      index: lindex,\n      ptr: lptr,\n      size: [n, n]\n    });\n    // U arrays\n    var uvalues = []; // (unz)\n    var uindex = []; // (unz)\n    var uptr = []; // (n + 1)\n    // U\n    var U = new SparseMatrix({\n      values: uvalues,\n      index: uindex,\n      ptr: uptr,\n      size: [n, n]\n    });\n    // inverse of permutation vector\n    var pinv = []; // (n)\n    // vars\n    var i, p;\n    // allocate arrays\n    var x = []; // (n)\n    var xi = []; // (2 * n)\n    // initialize variables\n    for (i = 0; i < n; i++) {\n      // clear workspace\n      x[i] = 0;\n      // no rows pivotal yet\n      pinv[i] = -1;\n      // no cols of L yet\n      lptr[i + 1] = 0;\n    }\n    // reset number of nonzero elements in L and U\n    lnz = 0;\n    unz = 0;\n    // compute L(:,k) and U(:,k)\n    for (var k = 0; k < n; k++) {\n      // update ptr\n      lptr[k] = lnz;\n      uptr[k] = unz;\n      // apply column permutations if needed\n      var col = q ? q[k] : k;\n      // solve triangular system, x = L\\A(:,col)\n      var top = csSpsolve(L, m, col, xi, x, pinv, 1);\n      // find pivot\n      var ipiv = -1;\n      var a = -1;\n      // loop xi[] from top -> n\n      for (p = top; p < n; p++) {\n        // x[i] is nonzero\n        i = xi[p];\n        // check row i is not yet pivotal\n        if (pinv[i] < 0) {\n          // absolute value of x[i]\n          var xabs = abs(x[i]);\n          // check absoulte value is greater than pivot value\n          if (larger(xabs, a)) {\n            // largest pivot candidate so far\n            a = xabs;\n            ipiv = i;\n          }\n        } else {\n          // x(i) is the entry U(pinv[i],k)\n          uindex[unz] = pinv[i];\n          uvalues[unz++] = x[i];\n        }\n      }\n      // validate we found a valid pivot\n      if (ipiv === -1 || a <= 0) {\n        return null;\n      }\n      // update actual pivot column, give preference to diagonal value\n      if (pinv[col] < 0 && largerEq(abs(x[col]), multiply(a, tol))) {\n        ipiv = col;\n      }\n      // the chosen pivot\n      var pivot = x[ipiv];\n      // last entry in U(:,k) is U(k,k)\n      uindex[unz] = k;\n      uvalues[unz++] = pivot;\n      // ipiv is the kth pivot row\n      pinv[ipiv] = k;\n      // first entry in L(:,k) is L(k,k) = 1\n      lindex[lnz] = ipiv;\n      lvalues[lnz++] = 1;\n      // L(k+1:n,k) = x / pivot\n      for (p = top; p < n; p++) {\n        // row\n        i = xi[p];\n        // check x(i) is an entry in L(:,k)\n        if (pinv[i] < 0) {\n          // save unpermuted row in L\n          lindex[lnz] = i;\n          // scale pivot column\n          lvalues[lnz++] = divideScalar(x[i], pivot);\n        }\n        // x[0..n-1] = 0 for next k\n        x[i] = 0;\n      }\n    }\n    // update ptr\n    lptr[n] = lnz;\n    uptr[n] = unz;\n    // fix row indices of L for final pinv\n    for (p = 0; p < lnz; p++) {\n      lindex[p] = pinv[lindex[p]];\n    }\n    // trim arrays\n    lvalues.splice(lnz, lvalues.length - lnz);\n    lindex.splice(lnz, lindex.length - lnz);\n    uvalues.splice(unz, uvalues.length - unz);\n    uindex.splice(unz, uindex.length - unz);\n    // return LU factor\n    return {\n      L,\n      U,\n      pinv\n    };\n  };\n});", "map": {"version": 3, "names": ["factory", "createCsSpsolve", "name", "dependencies", "createCsLu", "_ref", "abs", "divideScalar", "multiply", "subtract", "larger", "largerEq", "SparseMatrix", "csSpsolve", "csLu", "m", "s", "tol", "size", "_size", "n", "q", "lnz", "unz", "lvalues", "lindex", "lptr", "L", "values", "index", "ptr", "uvalues", "uindex", "uptr", "U", "pinv", "i", "p", "x", "xi", "k", "col", "top", "ipiv", "a", "xabs", "pivot", "splice", "length"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csLu.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\nimport { factory } from '../../../utils/factory.js';\nimport { createCsSpsolve } from './csSpsolve.js';\nvar name = 'csLu';\nvar dependencies = ['abs', 'divideScalar', 'multiply', 'subtract', 'larger', 'largerEq', 'SparseMatrix'];\nexport var createCsLu = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    abs,\n    divideScalar,\n    multiply,\n    subtract,\n    larger,\n    largerEq,\n    SparseMatrix\n  } = _ref;\n  var csSpsolve = createCsSpsolve({\n    divideScalar,\n    multiply,\n    subtract\n  });\n\n  /**\n   * Computes the numeric LU factorization of the sparse matrix A. Implements a Left-looking LU factorization\n   * algorithm that computes L and U one column at a tume. At the kth step, it access columns 1 to k-1 of L\n   * and column k of A. Given the fill-reducing column ordering q (see parameter s) computes L, U and pinv so\n   * L * U = A(p, q), where p is the inverse of pinv.\n   *\n   * @param {Matrix}  m               The A Matrix to factorize\n   * @param {Object}  s               The symbolic analysis from csSqr(). Provides the fill-reducing\n   *                                  column ordering q\n   * @param {Number}  tol             Partial pivoting threshold (1 for partial pivoting)\n   *\n   * @return {Number}                 The numeric LU factorization of A or null\n   */\n  return function csLu(m, s, tol) {\n    // validate input\n    if (!m) {\n      return null;\n    }\n    // m arrays\n    var size = m._size;\n    // columns\n    var n = size[1];\n    // symbolic analysis result\n    var q;\n    var lnz = 100;\n    var unz = 100;\n    // update symbolic analysis parameters\n    if (s) {\n      q = s.q;\n      lnz = s.lnz || lnz;\n      unz = s.unz || unz;\n    }\n    // L arrays\n    var lvalues = []; // (lnz)\n    var lindex = []; // (lnz)\n    var lptr = []; // (n + 1)\n    // L\n    var L = new SparseMatrix({\n      values: lvalues,\n      index: lindex,\n      ptr: lptr,\n      size: [n, n]\n    });\n    // U arrays\n    var uvalues = []; // (unz)\n    var uindex = []; // (unz)\n    var uptr = []; // (n + 1)\n    // U\n    var U = new SparseMatrix({\n      values: uvalues,\n      index: uindex,\n      ptr: uptr,\n      size: [n, n]\n    });\n    // inverse of permutation vector\n    var pinv = []; // (n)\n    // vars\n    var i, p;\n    // allocate arrays\n    var x = []; // (n)\n    var xi = []; // (2 * n)\n    // initialize variables\n    for (i = 0; i < n; i++) {\n      // clear workspace\n      x[i] = 0;\n      // no rows pivotal yet\n      pinv[i] = -1;\n      // no cols of L yet\n      lptr[i + 1] = 0;\n    }\n    // reset number of nonzero elements in L and U\n    lnz = 0;\n    unz = 0;\n    // compute L(:,k) and U(:,k)\n    for (var k = 0; k < n; k++) {\n      // update ptr\n      lptr[k] = lnz;\n      uptr[k] = unz;\n      // apply column permutations if needed\n      var col = q ? q[k] : k;\n      // solve triangular system, x = L\\A(:,col)\n      var top = csSpsolve(L, m, col, xi, x, pinv, 1);\n      // find pivot\n      var ipiv = -1;\n      var a = -1;\n      // loop xi[] from top -> n\n      for (p = top; p < n; p++) {\n        // x[i] is nonzero\n        i = xi[p];\n        // check row i is not yet pivotal\n        if (pinv[i] < 0) {\n          // absolute value of x[i]\n          var xabs = abs(x[i]);\n          // check absoulte value is greater than pivot value\n          if (larger(xabs, a)) {\n            // largest pivot candidate so far\n            a = xabs;\n            ipiv = i;\n          }\n        } else {\n          // x(i) is the entry U(pinv[i],k)\n          uindex[unz] = pinv[i];\n          uvalues[unz++] = x[i];\n        }\n      }\n      // validate we found a valid pivot\n      if (ipiv === -1 || a <= 0) {\n        return null;\n      }\n      // update actual pivot column, give preference to diagonal value\n      if (pinv[col] < 0 && largerEq(abs(x[col]), multiply(a, tol))) {\n        ipiv = col;\n      }\n      // the chosen pivot\n      var pivot = x[ipiv];\n      // last entry in U(:,k) is U(k,k)\n      uindex[unz] = k;\n      uvalues[unz++] = pivot;\n      // ipiv is the kth pivot row\n      pinv[ipiv] = k;\n      // first entry in L(:,k) is L(k,k) = 1\n      lindex[lnz] = ipiv;\n      lvalues[lnz++] = 1;\n      // L(k+1:n,k) = x / pivot\n      for (p = top; p < n; p++) {\n        // row\n        i = xi[p];\n        // check x(i) is an entry in L(:,k)\n        if (pinv[i] < 0) {\n          // save unpermuted row in L\n          lindex[lnz] = i;\n          // scale pivot column\n          lvalues[lnz++] = divideScalar(x[i], pivot);\n        }\n        // x[0..n-1] = 0 for next k\n        x[i] = 0;\n      }\n    }\n    // update ptr\n    lptr[n] = lnz;\n    uptr[n] = unz;\n    // fix row indices of L for final pinv\n    for (p = 0; p < lnz; p++) {\n      lindex[p] = pinv[lindex[p]];\n    }\n    // trim arrays\n    lvalues.splice(lnz, lvalues.length - lnz);\n    lindex.splice(lnz, lindex.length - lnz);\n    uvalues.splice(unz, uvalues.length - unz);\n    uindex.splice(unz, uindex.length - unz);\n    // return LU factor\n    return {\n      L,\n      U,\n      pinv\n    };\n  };\n});"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,YAAY,GAAG,CAAC,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,CAAC;AACxG,OAAO,IAAIC,UAAU,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACzE,IAAI;IACFC,GAAG;IACHC,YAAY;IACZC,QAAQ;IACRC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAGP,IAAI;EACR,IAAIQ,SAAS,GAAGZ,eAAe,CAAC;IAC9BM,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASK,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;IAC9B;IACA,IAAI,CAACF,CAAC,EAAE;MACN,OAAO,IAAI;IACb;IACA;IACA,IAAIG,IAAI,GAAGH,CAAC,CAACI,KAAK;IAClB;IACA,IAAIC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;IACf;IACA,IAAIG,CAAC;IACL,IAAIC,GAAG,GAAG,GAAG;IACb,IAAIC,GAAG,GAAG,GAAG;IACb;IACA,IAAIP,CAAC,EAAE;MACLK,CAAC,GAAGL,CAAC,CAACK,CAAC;MACPC,GAAG,GAAGN,CAAC,CAACM,GAAG,IAAIA,GAAG;MAClBC,GAAG,GAAGP,CAAC,CAACO,GAAG,IAAIA,GAAG;IACpB;IACA;IACA,IAAIC,OAAO,GAAG,EAAE,CAAC,CAAC;IAClB,IAAIC,MAAM,GAAG,EAAE,CAAC,CAAC;IACjB,IAAIC,IAAI,GAAG,EAAE,CAAC,CAAC;IACf;IACA,IAAIC,CAAC,GAAG,IAAIf,YAAY,CAAC;MACvBgB,MAAM,EAAEJ,OAAO;MACfK,KAAK,EAAEJ,MAAM;MACbK,GAAG,EAAEJ,IAAI;MACTR,IAAI,EAAE,CAACE,CAAC,EAAEA,CAAC;IACb,CAAC,CAAC;IACF;IACA,IAAIW,OAAO,GAAG,EAAE,CAAC,CAAC;IAClB,IAAIC,MAAM,GAAG,EAAE,CAAC,CAAC;IACjB,IAAIC,IAAI,GAAG,EAAE,CAAC,CAAC;IACf;IACA,IAAIC,CAAC,GAAG,IAAItB,YAAY,CAAC;MACvBgB,MAAM,EAAEG,OAAO;MACfF,KAAK,EAAEG,MAAM;MACbF,GAAG,EAAEG,IAAI;MACTf,IAAI,EAAE,CAACE,CAAC,EAAEA,CAAC;IACb,CAAC,CAAC;IACF;IACA,IAAIe,IAAI,GAAG,EAAE,CAAC,CAAC;IACf;IACA,IAAIC,CAAC,EAAEC,CAAC;IACR;IACA,IAAIC,CAAC,GAAG,EAAE,CAAC,CAAC;IACZ,IAAIC,EAAE,GAAG,EAAE,CAAC,CAAC;IACb;IACA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,CAAC,EAAEgB,CAAC,EAAE,EAAE;MACtB;MACAE,CAAC,CAACF,CAAC,CAAC,GAAG,CAAC;MACR;MACAD,IAAI,CAACC,CAAC,CAAC,GAAG,CAAC,CAAC;MACZ;MACAV,IAAI,CAACU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACjB;IACA;IACAd,GAAG,GAAG,CAAC;IACPC,GAAG,GAAG,CAAC;IACP;IACA,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,CAAC,EAAEoB,CAAC,EAAE,EAAE;MAC1B;MACAd,IAAI,CAACc,CAAC,CAAC,GAAGlB,GAAG;MACbW,IAAI,CAACO,CAAC,CAAC,GAAGjB,GAAG;MACb;MACA,IAAIkB,GAAG,GAAGpB,CAAC,GAAGA,CAAC,CAACmB,CAAC,CAAC,GAAGA,CAAC;MACtB;MACA,IAAIE,GAAG,GAAG7B,SAAS,CAACc,CAAC,EAAEZ,CAAC,EAAE0B,GAAG,EAAEF,EAAE,EAAED,CAAC,EAAEH,IAAI,EAAE,CAAC,CAAC;MAC9C;MACA,IAAIQ,IAAI,GAAG,CAAC,CAAC;MACb,IAAIC,CAAC,GAAG,CAAC,CAAC;MACV;MACA,KAAKP,CAAC,GAAGK,GAAG,EAAEL,CAAC,GAAGjB,CAAC,EAAEiB,CAAC,EAAE,EAAE;QACxB;QACAD,CAAC,GAAGG,EAAE,CAACF,CAAC,CAAC;QACT;QACA,IAAIF,IAAI,CAACC,CAAC,CAAC,GAAG,CAAC,EAAE;UACf;UACA,IAAIS,IAAI,GAAGvC,GAAG,CAACgC,CAAC,CAACF,CAAC,CAAC,CAAC;UACpB;UACA,IAAI1B,MAAM,CAACmC,IAAI,EAAED,CAAC,CAAC,EAAE;YACnB;YACAA,CAAC,GAAGC,IAAI;YACRF,IAAI,GAAGP,CAAC;UACV;QACF,CAAC,MAAM;UACL;UACAJ,MAAM,CAACT,GAAG,CAAC,GAAGY,IAAI,CAACC,CAAC,CAAC;UACrBL,OAAO,CAACR,GAAG,EAAE,CAAC,GAAGe,CAAC,CAACF,CAAC,CAAC;QACvB;MACF;MACA;MACA,IAAIO,IAAI,KAAK,CAAC,CAAC,IAAIC,CAAC,IAAI,CAAC,EAAE;QACzB,OAAO,IAAI;MACb;MACA;MACA,IAAIT,IAAI,CAACM,GAAG,CAAC,GAAG,CAAC,IAAI9B,QAAQ,CAACL,GAAG,CAACgC,CAAC,CAACG,GAAG,CAAC,CAAC,EAAEjC,QAAQ,CAACoC,CAAC,EAAE3B,GAAG,CAAC,CAAC,EAAE;QAC5D0B,IAAI,GAAGF,GAAG;MACZ;MACA;MACA,IAAIK,KAAK,GAAGR,CAAC,CAACK,IAAI,CAAC;MACnB;MACAX,MAAM,CAACT,GAAG,CAAC,GAAGiB,CAAC;MACfT,OAAO,CAACR,GAAG,EAAE,CAAC,GAAGuB,KAAK;MACtB;MACAX,IAAI,CAACQ,IAAI,CAAC,GAAGH,CAAC;MACd;MACAf,MAAM,CAACH,GAAG,CAAC,GAAGqB,IAAI;MAClBnB,OAAO,CAACF,GAAG,EAAE,CAAC,GAAG,CAAC;MAClB;MACA,KAAKe,CAAC,GAAGK,GAAG,EAAEL,CAAC,GAAGjB,CAAC,EAAEiB,CAAC,EAAE,EAAE;QACxB;QACAD,CAAC,GAAGG,EAAE,CAACF,CAAC,CAAC;QACT;QACA,IAAIF,IAAI,CAACC,CAAC,CAAC,GAAG,CAAC,EAAE;UACf;UACAX,MAAM,CAACH,GAAG,CAAC,GAAGc,CAAC;UACf;UACAZ,OAAO,CAACF,GAAG,EAAE,CAAC,GAAGf,YAAY,CAAC+B,CAAC,CAACF,CAAC,CAAC,EAAEU,KAAK,CAAC;QAC5C;QACA;QACAR,CAAC,CAACF,CAAC,CAAC,GAAG,CAAC;MACV;IACF;IACA;IACAV,IAAI,CAACN,CAAC,CAAC,GAAGE,GAAG;IACbW,IAAI,CAACb,CAAC,CAAC,GAAGG,GAAG;IACb;IACA,KAAKc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,GAAG,EAAEe,CAAC,EAAE,EAAE;MACxBZ,MAAM,CAACY,CAAC,CAAC,GAAGF,IAAI,CAACV,MAAM,CAACY,CAAC,CAAC,CAAC;IAC7B;IACA;IACAb,OAAO,CAACuB,MAAM,CAACzB,GAAG,EAAEE,OAAO,CAACwB,MAAM,GAAG1B,GAAG,CAAC;IACzCG,MAAM,CAACsB,MAAM,CAACzB,GAAG,EAAEG,MAAM,CAACuB,MAAM,GAAG1B,GAAG,CAAC;IACvCS,OAAO,CAACgB,MAAM,CAACxB,GAAG,EAAEQ,OAAO,CAACiB,MAAM,GAAGzB,GAAG,CAAC;IACzCS,MAAM,CAACe,MAAM,CAACxB,GAAG,EAAES,MAAM,CAACgB,MAAM,GAAGzB,GAAG,CAAC;IACvC;IACA,OAAO;MACLI,CAAC;MACDO,CAAC;MACDC;IACF,CAAC;EACH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}