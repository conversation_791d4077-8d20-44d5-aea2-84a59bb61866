{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createRelationalNode } from '../../factoriesAny.js';\nexport var RelationalNodeDependencies = {\n  NodeDependencies,\n  createRelationalNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "createRelationalNode", "RelationalNodeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRelationalNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createRelationalNode } from '../../factoriesAny.js';\nexport var RelationalNodeDependencies = {\n  NodeDependencies,\n  createRelationalNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,IAAIC,0BAA0B,GAAG;EACtCF,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}