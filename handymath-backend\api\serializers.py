from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Course, Equation, Exercise, ContactMessage

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'role', 'bio', 'profile_picture', 'password']
        extra_kwargs = {
            'password': {'write_only': True},
        }

    def create(self, validated_data):
        # Extraire le mot de passe des données validées
        password = validated_data.pop('password')

        # Créer l'utilisateur sans le mot de passe
        user = User.objects.create(**validated_data)

        # Hacher et définir le mot de passe
        user.set_password(password)
        user.save()

        return user

    def update(self, instance, validated_data):
        # Si le mot de passe est dans les données à mettre à jour
        password = validated_data.pop('password', None)

        # Mettre à jour les autres champs
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Si un nouveau mot de passe est fourni, le hacher
        if password:
            instance.set_password(password)

        instance.save()
        return instance

class CourseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Course
        fields = '__all__'

class EquationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Equation
        fields = '__all__'

class ExerciseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Exercise
        fields = '__all__'


class ContactMessageSerializer(serializers.ModelSerializer):
    """Serializer pour les messages de contact"""

    status_display = serializers.CharField(source='get_status_display', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    priority_color = serializers.CharField(source='get_priority_color', read_only=True)
    status_color = serializers.CharField(source='get_status_color', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)

    class Meta:
        model = ContactMessage
        fields = [
            'id', 'name', 'email', 'category', 'category_display', 'subject',
            'message', 'created_at', 'updated_at', 'status', 'status_display',
            'priority', 'priority_display', 'priority_color', 'status_color',
            'assigned_to', 'assigned_to_name', 'admin_notes', 'resolved_at',
            'user_agent', 'ip_address', 'is_overdue'
        ]
        read_only_fields = ['created_at', 'updated_at', 'resolved_at']


class ContactMessageCreateSerializer(serializers.ModelSerializer):
    """Serializer pour créer un nouveau message de contact"""

    class Meta:
        model = ContactMessage
        fields = ['name', 'email', 'category', 'subject', 'message']

    def validate_email(self, value):
        """Validation de l'email"""
        if not value:
            raise serializers.ValidationError("L'email est requis.")
        return value

    def validate_message(self, value):
        """Validation du message"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Le message doit contenir au moins 10 caractères.")
        return value


class ContactMessageUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour mettre à jour un message de contact (admin seulement)"""

    class Meta:
        model = ContactMessage
        fields = ['status', 'priority', 'assigned_to', 'admin_notes']

    def update(self, instance, validated_data):
        """Mise à jour avec gestion de la date de résolution"""
        if validated_data.get('status') == 'resolved' and instance.status != 'resolved':
            from django.utils import timezone
            validated_data['resolved_at'] = timezone.now()

        return super().update(instance, validated_data)


