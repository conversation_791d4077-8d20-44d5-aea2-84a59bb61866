{"ast": null, "code": "import { errorTransform } from '../../transform/utils/errorTransform.js';\nimport { setSafeProperty } from '../../../utils/customs.js';\nexport function assignFactory(_ref) {\n  var {\n    subset,\n    matrix\n  } = _ref;\n  /**\n   * Replace part of an object:\n   *\n   * - Assign a property to an object\n   * - Replace a part of a string\n   * - Replace a matrix subset\n   *\n   * @param {Object | Array | Matrix | string} object\n   * @param {Index} index\n   * @param {*} value\n   * @return {Object | Array | Matrix | string} Returns the original object\n   *                                            except in case of a string\n   */\n  // TODO: change assign to return the value instead of the object\n  return function assign(object, index, value) {\n    try {\n      if (Array.isArray(object)) {\n        var result = matrix(object).subset(index, value).valueOf();\n\n        // shallow copy all (updated) items into the original array\n        result.forEach((item, index) => {\n          object[index] = item;\n        });\n        return object;\n      } else if (object && typeof object.subset === 'function') {\n        // Matrix\n        return object.subset(index, value);\n      } else if (typeof object === 'string') {\n        // TODO: move setStringSubset into a separate util file, use that\n        return subset(object, index, value);\n      } else if (typeof object === 'object') {\n        if (!index.isObjectProperty()) {\n          throw TypeError('Cannot apply a numeric index as object property');\n        }\n        setSafeProperty(object, index.getObjectProperty(), value);\n        return object;\n      } else {\n        throw new TypeError('Cannot apply index: unsupported type of object');\n      }\n    } catch (err) {\n      throw errorTransform(err);\n    }\n  };\n}", "map": {"version": 3, "names": ["errorTransform", "setSafeProperty", "assignFactory", "_ref", "subset", "matrix", "assign", "object", "index", "value", "Array", "isArray", "result", "valueOf", "for<PERSON>ach", "item", "isObjectProperty", "TypeError", "getObjectProperty", "err"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/node/utils/assign.js"], "sourcesContent": ["import { errorTransform } from '../../transform/utils/errorTransform.js';\nimport { setSafeProperty } from '../../../utils/customs.js';\nexport function assignFactory(_ref) {\n  var {\n    subset,\n    matrix\n  } = _ref;\n  /**\n   * Replace part of an object:\n   *\n   * - Assign a property to an object\n   * - Replace a part of a string\n   * - Replace a matrix subset\n   *\n   * @param {Object | Array | Matrix | string} object\n   * @param {Index} index\n   * @param {*} value\n   * @return {Object | Array | Matrix | string} Returns the original object\n   *                                            except in case of a string\n   */\n  // TODO: change assign to return the value instead of the object\n  return function assign(object, index, value) {\n    try {\n      if (Array.isArray(object)) {\n        var result = matrix(object).subset(index, value).valueOf();\n\n        // shallow copy all (updated) items into the original array\n        result.forEach((item, index) => {\n          object[index] = item;\n        });\n        return object;\n      } else if (object && typeof object.subset === 'function') {\n        // Matrix\n        return object.subset(index, value);\n      } else if (typeof object === 'string') {\n        // TODO: move setStringSubset into a separate util file, use that\n        return subset(object, index, value);\n      } else if (typeof object === 'object') {\n        if (!index.isObjectProperty()) {\n          throw TypeError('Cannot apply a numeric index as object property');\n        }\n        setSafeProperty(object, index.getObjectProperty(), value);\n        return object;\n      } else {\n        throw new TypeError('Cannot apply index: unsupported type of object');\n      }\n    } catch (err) {\n      throw errorTransform(err);\n    }\n  };\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,yCAAyC;AACxE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI;IACFC,MAAM;IACNC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACA,OAAO,SAASG,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC3C,IAAI;MACF,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;QACzB,IAAIK,MAAM,GAAGP,MAAM,CAACE,MAAM,CAAC,CAACH,MAAM,CAACI,KAAK,EAAEC,KAAK,CAAC,CAACI,OAAO,CAAC,CAAC;;QAE1D;QACAD,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEP,KAAK,KAAK;UAC9BD,MAAM,CAACC,KAAK,CAAC,GAAGO,IAAI;QACtB,CAAC,CAAC;QACF,OAAOR,MAAM;MACf,CAAC,MAAM,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACH,MAAM,KAAK,UAAU,EAAE;QACxD;QACA,OAAOG,MAAM,CAACH,MAAM,CAACI,KAAK,EAAEC,KAAK,CAAC;MACpC,CAAC,MAAM,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;QACrC;QACA,OAAOH,MAAM,CAACG,MAAM,EAAEC,KAAK,EAAEC,KAAK,CAAC;MACrC,CAAC,MAAM,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;QACrC,IAAI,CAACC,KAAK,CAACQ,gBAAgB,CAAC,CAAC,EAAE;UAC7B,MAAMC,SAAS,CAAC,iDAAiD,CAAC;QACpE;QACAhB,eAAe,CAACM,MAAM,EAAEC,KAAK,CAACU,iBAAiB,CAAC,CAAC,EAAET,KAAK,CAAC;QACzD,OAAOF,MAAM;MACf,CAAC,MAAM;QACL,MAAM,IAAIU,SAAS,CAAC,gDAAgD,CAAC;MACvE;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZ,MAAMnB,cAAc,CAACmB,GAAG,CAAC;IAC3B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}