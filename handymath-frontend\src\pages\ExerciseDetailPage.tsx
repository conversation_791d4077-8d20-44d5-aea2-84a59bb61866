import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../components/NotificationSystem';
import api from '../services/api';

// Types
interface ExerciseChoice {
  id: number;
  choice_text: string;
  order: number;
}

interface Exercise {
  id: number;
  title: string;
  description: string;
  question: string;
  course: {
    id: number;
    title: string;
    level: string;
  };
  difficulty: string;
  difficulty_display: string;
  exercise_type: string;
  type_display: string;
  points: number;
  time_limit: number;
  choices: ExerciseChoice[];
  user_attempt?: {
    id: number;
    status: string;
    user_answer: string;
    is_correct: boolean;
    points_earned: number;
    time_taken: number;
    started_at: string;
    completed_at?: string;
  } | null;
  correct_answer?: string;
  explanation?: string;
  created_at: string;
}

const ExerciseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // Helper functions for notifications
  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message });
  };
  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };
  const showWarning = (title: string, message: string) => {
    addNotification({ type: 'warning', title, message });
  };

  // State
  const [exercise, setExercise] = useState<Exercise | null>(null);
  const [loading, setLoading] = useState(true);
  const [starting, setStarting] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [userAnswer, setUserAnswer] = useState('');
  const [selectedChoice, setSelectedChoice] = useState<number | null>(null);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);

  // Effects
  useEffect(() => {
    if (user && id) {
      fetchExercise();
    }
  }, [user, id]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timeLeft !== null && timeLeft > 0 && exercise?.user_attempt?.status === 'in_progress') {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev === null || prev <= 1) {
            // Temps écoulé - soumettre automatiquement
            handleSubmit(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timeLeft, exercise?.user_attempt?.status]);

  // API Functions
  const fetchExercise = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/exercises/${id}/`);
      if (response.data) {
        setExercise(response.data);
        // Si l'exercice est en cours, calculer le temps restant
        if (response.data.user_attempt?.status === 'in_progress') {
          const startedAt = new Date(response.data.user_attempt.started_at);
          const now = new Date();
          const elapsed = Math.floor((now.getTime() - startedAt.getTime()) / 1000);
          const remaining = Math.max(0, response.data.time_limit - elapsed);
          setTimeLeft(remaining);
          setStartTime(startedAt);
        }
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération de l\'exercice:', error);
      showError('Erreur', 'Impossible de charger l\'exercice');
      navigate('/exercises');
    } finally {
      setLoading(false);
    }
  };

  const handleStart = async () => {
    if (!exercise) return;
    try {
      setStarting(true);
      const response = await api.post(`/exercises/${exercise.id}/start/`);
      if (response.data) {
        showSuccess('Exercice démarré !', 'Vous pouvez maintenant répondre à la question.');
        setTimeLeft(exercise.time_limit);
        setStartTime(new Date());
        await fetchExercise(); // Recharger pour avoir la tentative
      }
    } catch (error: any) {
      console.error('Erreur lors du démarrage:', error);
      if (error.response?.data?.error) {
        showError('Erreur', error.response.data.error);
      } else {
        showError('Erreur', 'Impossible de démarrer l\'exercice');
      }
    } finally {
      setStarting(false);
    }
  };

  const handleSubmit = async (timeExpired = false) => {
    if (!exercise || !exercise.user_attempt) return;

    const answer = exercise.exercise_type === 'multiple_choice'
      ? selectedChoice?.toString() || ''
      : userAnswer.trim();

    if (!answer && !timeExpired) {
      showWarning('Réponse manquante', 'Veuillez fournir une réponse avant de soumettre.');
      return;
    }

    try {
      setSubmitting(true);
      const timeTaken = startTime
        ? Math.floor((new Date().getTime() - startTime.getTime()) / 1000)
        : exercise.time_limit;

      const response = await api.post(`/exercises/${exercise.id}/submit/`, {
        answer,
        time_taken: timeTaken
      });

      if (response.data) {
        const { is_correct, points_earned, correct_answer, explanation } = response.data;
        if (is_correct) {
          showSuccess(
            'Bravo !',
            `Réponse correcte ! Vous avez gagné ${points_earned} points.`
          );
        } else {
          showError(
            'Réponse incorrecte',
            `La bonne réponse était : ${correct_answer}`
          );
        }
        // Recharger l'exercice pour voir les résultats
        await fetchExercise();
        setTimeLeft(null);
      }
    } catch (error: any) {
      console.error('Erreur lors de la soumission:', error);
      if (error.response?.data?.error) {
        showError('Erreur', error.response.data.error);
      } else {
        showError('Erreur', 'Impossible de soumettre la réponse');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Utility Functions
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'hard':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'equation':
        return '📐';
      case 'multiple_choice':
        return '📝';
      case 'calculation':
        return '🧮';
      case 'proof':
        return '📋';
      default:
        return '📚';
    }
  };

  // Loading and Error States
  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Accès restreint</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour accéder à cet exercice.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement de l'exercice...</p>
        </div>
      </div>
    );
  }

  if (!exercise) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Exercice non trouvé</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            L'exercice demandé n'existe pas ou n'est plus disponible.
          </p>
          <button
            onClick={() => navigate('/exercises')}
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Retour aux exercices
          </button>
        </div>
      </div>
    );
  }

  const isCompleted = exercise.user_attempt?.status === 'completed';
  const isInProgress = exercise.user_attempt?.status === 'in_progress';

  // Main Render
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Bouton retour */}
      <button
        onClick={() => navigate('/exercises')}
        className="mb-6 flex items-center text-primary-600 hover:text-primary-700 transition-colors"
      >
        <span className="mr-2">←</span>
        Retour aux exercices
      </button>

      <div className="max-w-4xl mx-auto">
        {/* En-tête de l'exercice */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <span className="text-3xl mr-3">{getTypeIcon(exercise.exercise_type)}</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {exercise.title}
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-400">
                  {exercise.course.title} • {exercise.type_display}
                </p>
              </div>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>
              {exercise.difficulty_display}
            </span>
          </div>

          <p className="text-gray-700 dark:text-gray-300 mb-6">
            {exercise.description}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center">
              <span><strong>Points:</strong> {exercise.points}</span>
            </div>
            <div className="flex items-center">
              <span className="mr-2">⏱</span>
              <span><strong>Temps limite:</strong> {Math.floor(exercise.time_limit / 60)}min</span>
            </div>
            <div className="flex items-center">
              <span><strong>Statut:</strong> {
                isCompleted ? 'Terminé' : isInProgress ? 'En cours' : 'Nouveau'
              }</span>
            </div>
          </div>

          {/* Chronomètre */}
          {timeLeft !== null && isInProgress && (
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
              <div className="flex items-center justify-center">
                <span className="text-2xl mr-2">⏰</span>
                <span className="text-2xl font-mono font-bold">
                  {formatTime(timeLeft)}
                </span>
              </div>
              {timeLeft <= 60 && (
                <p className="text-center text-red-600 dark:text-red-400 mt-2">
                  Moins d'une minute restante !
                </p>
              )}
            </div>
          )}
        </motion.div>

        {/* Question et réponse */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <h2 className="text-2xl font-semibold mb-4 flex items-center">
            Question
          </h2>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <p className="text-lg whitespace-pre-wrap">{exercise.question}</p>
          </div>

          {!isCompleted && !isInProgress && (
            <div className="text-center">
              <button
                onClick={handleStart}
                disabled={starting}
                className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors"
              >
                {starting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2"></div>
                    Démarrage...
                  </>
                ) : (
                  'Commencer l\'exercice'
                )}
              </button>
            </div>
          )}

          {isInProgress && (
            <div>
              <h3 className="text-xl font-semibold mb-4">Choisissez votre réponse :</h3>
              <div className="space-y-3">
                {exercise.choices.map((choice, index) => (
                  <motion.label
                    key={choice.id}
                    className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      selectedChoice === choice.id
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <input
                      type="radio"
                      name="choice"
                      value={choice.id}
                      checked={selectedChoice === choice.id}
                      onChange={() => setSelectedChoice(choice.id)}
                      className="mr-4 w-5 h-5 text-primary-600"
                    />
                    <div className="flex items-center">
                      <span className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3">
                        {String.fromCharCode(65 + index)}
                      </span>
                      <span className="text-lg">{choice.choice_text}</span>
                    </div>
                  </motion.label>
                ))}
              </div>

              <div className="mt-8 text-center">
                <motion.button
                  onClick={() => handleSubmit()}
                  disabled={submitting || !selectedChoice}
                  className={`font-medium py-4 px-12 rounded-xl transition-all duration-200 ${
                    selectedChoice && !submitting
                      ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  }`}
                  whileHover={selectedChoice && !submitting ? { scale: 1.05 } : {}}
                  whileTap={selectedChoice && !submitting ? { scale: 0.95 } : {}}
                >
                  {submitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2"></div>
                      Soumission en cours...
                    </>
                  ) : selectedChoice ? (
                    <>
                      Valider ma réponse
                    </>
                  ) : (
                    <>
                      <span className="mr-2">📝</span>
                      Sélectionnez une réponse
                    </>
                  )}
                </motion.button>

                {selectedChoice && !submitting && (
                  <motion.p
                    className="mt-3 text-sm text-gray-600 dark:text-gray-400"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    Réponse sélectionnée : <strong>{String.fromCharCode(65 + exercise.choices.findIndex(c => c.id === selectedChoice))}</strong>
                  </motion.p>
                )}
              </div>
            </div>
          )}

          {isCompleted && (
            <div>
              <h3 className="text-xl font-semibold mb-4">Résultats :</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <span className="text-2xl mr-2">
                      {exercise.user_attempt?.is_correct ? '✅' : '❌'}
                    </span>
                    <span className="font-semibold">
                      {exercise.user_attempt?.is_correct ? 'Correct' : 'Incorrect'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Points obtenus : {exercise.user_attempt?.points_earned || 0} / {exercise.points}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <span className="text-2xl mr-2">⏱</span>
                    <span className="font-semibold">Temps pris</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatTime(exercise.user_attempt?.time_taken || 0)}
                  </p>
                </div>
              </div>

              {exercise.correct_answer && (
                <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 mb-4">
                  <h4 className="font-semibold mb-2 flex items-center">
                    💡 Réponse correcte :
                  </h4>
                  <p className="font-mono">{exercise.correct_answer}</p>
                </div>
              )}

              {exercise.explanation && (
                <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4">
                  <h4 className="font-semibold mb-2 flex items-center">
                    📚 Explication :
                  </h4>
                  <p className="whitespace-pre-wrap">{exercise.explanation}</p>
                </div>
              )}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default ExerciseDetailPage;