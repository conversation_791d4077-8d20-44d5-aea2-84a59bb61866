{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\student\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNotifications } from '../../components/NotificationSystem';\nimport StudentLayout from '../../components/StudentLayout';\nimport api from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  var _studentStats$general, _studentStats$general2, _studentStats$general3, _studentStats$general4, _studentStats$general5, _studentStats$general6;\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n\n  // State\n  const [studentStats, setStudentStats] = useState(null);\n  const [recentCourses, setRecentCourses] = useState([]);\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Helper functions for notifications\n  const showError = (title, message) => {\n    addNotification({\n      type: 'error',\n      title,\n      message\n    });\n  };\n  const showSuccess = (title, message) => {\n    addNotification({\n      type: 'success',\n      title,\n      message\n    });\n  };\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchStudentData();\n    }\n  }, [user]);\n\n  // API Functions\n  const fetchStudentData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Récupérer les statistiques de l'étudiant\n      const [statsResponse, coursesResponse, exercisesResponse] = await Promise.all([api.get('/progress/'), api.get('/courses/'), api.get('/exercises/')]);\n      setStudentStats(statsResponse.data);\n\n      // Traiter les cours (les ViewSets retournent directement un array)\n      const coursesData = Array.isArray(coursesResponse.data) ? coursesResponse.data : coursesResponse.data.results || [];\n      setRecentCourses(coursesData.slice(0, 3)); // 3 premiers cours\n\n      // Traiter les exercices\n      const exercisesData = Array.isArray(exercisesResponse.data) ? exercisesResponse.data : exercisesResponse.data.results || [];\n      setRecommendedExercises(exercisesData.slice(0, 4)); // 4 premiers exercices\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      console.error('Erreur lors du chargement des données étudiant:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || 'Erreur lors du chargement des données';\n      setError(errorMessage);\n      showError('Erreur', errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Quick actions\n  const quickActions = [{\n    title: 'Résoudre une équation',\n    description: 'Utilisez notre solveur d\\'équations',\n    link: '/solver',\n    icon: '🧮',\n    color: 'bg-blue-500'\n  }, {\n    title: 'Faire un exercice',\n    description: 'Pratiquez avec nos exercices',\n    link: '/exercises',\n    icon: '📝',\n    color: 'bg-green-500'\n  }, {\n    title: 'Voir mes cours',\n    description: 'Accédez à vos cours',\n    link: '/courses',\n    icon: '📚',\n    color: 'bg-purple-500'\n  }, {\n    title: 'Ma progression',\n    description: 'Consultez vos statistiques',\n    link: '/progress',\n    icon: '📊',\n    color: 'bg-orange-500'\n  }];\n\n  // Calculer les statistiques rapides\n  const quickStats = studentStats ? [{\n    label: 'Points totaux',\n    value: ((_studentStats$general = studentStats.general_stats) === null || _studentStats$general === void 0 ? void 0 : (_studentStats$general2 = _studentStats$general.total_points) === null || _studentStats$general2 === void 0 ? void 0 : _studentStats$general2.toString()) || '0',\n    color: 'text-blue-600',\n    icon: '🏆'\n  }, {\n    label: 'Taux de réussite',\n    value: `${Math.round(((_studentStats$general3 = studentStats.general_stats) === null || _studentStats$general3 === void 0 ? void 0 : _studentStats$general3.success_rate) || 0)}%`,\n    color: 'text-green-600',\n    icon: '✅'\n  }, {\n    label: 'Exercices complétés',\n    value: ((_studentStats$general4 = studentStats.general_stats) === null || _studentStats$general4 === void 0 ? void 0 : (_studentStats$general5 = _studentStats$general4.completed_attempts) === null || _studentStats$general5 === void 0 ? void 0 : _studentStats$general5.toString()) || '0',\n    color: 'text-purple-600',\n    icon: '📋'\n  }, {\n    label: 'Temps moyen',\n    value: `${Math.round(((_studentStats$general6 = studentStats.general_stats) === null || _studentStats$general6 === void 0 ? void 0 : _studentStats$general6.average_time) || 0)}s`,\n    color: 'text-orange-600',\n    icon: '⏱️'\n  }] : [\n  // Données par défaut si pas de statistiques\n  {\n    label: 'Points totaux',\n    value: '0',\n    color: 'text-blue-600',\n    icon: '🏆'\n  }, {\n    label: 'Taux de réussite',\n    value: '0%',\n    color: 'text-green-600',\n    icon: '✅'\n  }, {\n    label: 'Exercices complétés',\n    value: '0',\n    color: 'text-purple-600',\n    icon: '📋'\n  }, {\n    label: 'Temps moyen',\n    value: '0s',\n    color: 'text-orange-600',\n    icon: '⏱️'\n  }];\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement de votre tableau de bord...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Erreur de chargement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchStudentData,\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this);\n  }\n  const refreshAction = /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: fetchStudentData,\n    className: \"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"\\uD83D\\uDD04\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Actualiser\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(StudentLayout, {\n    title: \"Tableau de bord \\xE9tudiant\",\n    subtitle: `Bienvenue, ${user === null || user === void 0 ? void 0 : user.username} • Votre progression HandyMath`,\n    actions: refreshAction,\n    children: [quickStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: quickStats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-2xl font-bold ${stat.color}`,\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl\",\n            children: stat.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: quickActions.map((action, index) => /*#__PURE__*/_jsxDEV(Link, {\n        to: action.link,\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 group transform hover:scale-105\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-3 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform duration-300`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: action.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 dark:text-white mb-1 group-hover:text-primary-600 transition-colors\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: action.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n            children: \"\\uD83D\\uDCDA Cours disponibles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/courses\",\n            className: \"text-primary-600 hover:text-primary-700 text-sm font-medium\",\n            children: \"Voir tout \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recentCourses.length > 0 ? recentCourses.map(course => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/courses/${course.id}`,\n            className: \"block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 dark:text-white\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                  children: [course.lessons_count, \" le\\xE7ons \\u2022 Niveau \", course.level]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-2 py-1 rounded text-xs font-medium ${course.level === 'beginner' ? 'bg-green-100 text-green-800' : course.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                children: course.level === 'beginner' ? 'Débutant' : course.level === 'intermediate' ? 'Intermédiaire' : 'Avancé'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 19\n            }, this)\n          }, course.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-2\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400\",\n              children: \"Aucun cours disponible\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n            children: \"\\uD83C\\uDFAF Exercices recommand\\xE9s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/exercises\",\n            className: \"text-primary-600 hover:text-primary-700 text-sm font-medium\",\n            children: \"Voir tout \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recommendedExercises.length > 0 ? recommendedExercises.map(exercise => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/exercises/${exercise.id}`,\n            className: \"block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 dark:text-white\",\n                  children: exercise.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                  children: [exercise.exercise_type, \" \\u2022 \", exercise.points, \" points\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-2 py-1 rounded text-xs font-medium ${exercise.difficulty === 'easy' ? 'bg-green-100 text-green-800' : exercise.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                children: exercise.difficulty === 'easy' ? 'Facile' : exercise.difficulty === 'medium' ? 'Moyen' : 'Difficile'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)\n          }, exercise.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-2\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400\",\n              children: \"Aucun exercice disponible\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), studentStats && studentStats.recent_exercises && studentStats.recent_exercises.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n        children: \"\\uD83D\\uDD52 Exercices r\\xE9cents\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: studentStats.recent_exercises.slice(0, 5).map((exercise, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-3 h-3 rounded-full ${exercise.is_correct ? 'bg-green-500' : 'bg-red-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 dark:text-white\",\n                children: exercise.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: new Date(exercise.completed_at).toLocaleDateString('fr-FR')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `text-sm font-medium ${exercise.is_correct ? 'text-green-600' : 'text-red-600'}`,\n              children: exercise.is_correct ? '✅ Réussi' : '❌ Échoué'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: [\"+\", exercise.points_earned, \" points\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"WgQ/auBXxa1hYHfIINq+kGv1MO4=\", false, function () {\n  return [useAuth, useNotifications];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "motion", "useAuth", "useNotifications", "StudentLayout", "api", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "_studentStats$general", "_studentStats$general2", "_studentStats$general3", "_studentStats$general4", "_studentStats$general5", "_studentStats$general6", "user", "addNotification", "studentStats", "setStudentStats", "recentCourses", "setRecentCourses", "recommendedExercises", "setRecommendedExercises", "loading", "setLoading", "error", "setError", "showError", "title", "message", "type", "showSuccess", "fetchStudentData", "statsResponse", "coursesResponse", "exercisesResponse", "Promise", "all", "get", "data", "coursesData", "Array", "isArray", "results", "slice", "exercisesData", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "console", "errorMessage", "response", "detail", "quickActions", "description", "link", "icon", "color", "quickStats", "label", "value", "general_stats", "total_points", "toString", "Math", "round", "success_rate", "completed_attempts", "average_time", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "refreshAction", "subtitle", "username", "actions", "length", "map", "stat", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "action", "to", "x", "course", "id", "lessons_count", "level", "exercise", "exercise_type", "points", "difficulty", "recent_exercises", "is_correct", "Date", "completed_at", "toLocaleDateString", "points_earned", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/student/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { motion } from 'framer-motion';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { useNotifications } from '../../components/NotificationSystem';\r\nimport StudentLayout from '../../components/StudentLayout';\r\nimport { RefreshIcon } from '../../components/icons/NavigationIcons';\r\nimport api from '../../services/api';\r\n\r\n// Types\r\ninterface StudentStats {\r\n  general_stats: {\r\n    total_attempts: number;\r\n    completed_attempts: number;\r\n    correct_attempts: number;\r\n    success_rate: number;\r\n    total_points: number;\r\n    average_time: number;\r\n  };\r\n  recent_exercises: Array<{\r\n    id: number;\r\n    title: string;\r\n    difficulty: string;\r\n    is_correct: boolean;\r\n    points_earned: number;\r\n    completed_at: string;\r\n  }>;\r\n  difficulty_stats: {\r\n    [key: string]: {\r\n      total_attempts: number;\r\n      correct_attempts: number;\r\n      success_rate: number;\r\n      points_earned: number;\r\n    };\r\n  };\r\n}\r\n\r\ninterface Course {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  level: string;\r\n  lessons_count: number;\r\n}\r\n\r\ninterface Exercise {\r\n  id: number;\r\n  title: string;\r\n  difficulty: string;\r\n  exercise_type: string;\r\n  points: number;\r\n}\r\n\r\nconst StudentDashboard: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const { addNotification } = useNotifications();\r\n\r\n  // State\r\n  const [studentStats, setStudentStats] = useState<StudentStats | null>(null);\r\n  const [recentCourses, setRecentCourses] = useState<Course[]>([]);\r\n  const [recommendedExercises, setRecommendedExercises] = useState<Exercise[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Helper functions for notifications\r\n  const showError = (title: string, message: string) => {\r\n    addNotification({ type: 'error', title, message });\r\n  };\r\n\r\n  const showSuccess = (title: string, message: string) => {\r\n    addNotification({ type: 'success', title, message });\r\n  };\r\n\r\n  // Effects\r\n  useEffect(() => {\r\n    if (user) {\r\n      fetchStudentData();\r\n    }\r\n  }, [user]);\r\n\r\n  // API Functions\r\n  const fetchStudentData = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      // Récupérer les statistiques de l'étudiant\r\n      const [statsResponse, coursesResponse, exercisesResponse] = await Promise.all([\r\n        api.get('/progress/'),\r\n        api.get('/courses/'),\r\n        api.get('/exercises/')\r\n      ]);\r\n\r\n      setStudentStats(statsResponse.data);\r\n\r\n      // Traiter les cours (les ViewSets retournent directement un array)\r\n      const coursesData = Array.isArray(coursesResponse.data) ? coursesResponse.data : coursesResponse.data.results || [];\r\n      setRecentCourses(coursesData.slice(0, 3)); // 3 premiers cours\r\n\r\n      // Traiter les exercices\r\n      const exercisesData = Array.isArray(exercisesResponse.data) ? exercisesResponse.data : exercisesResponse.data.results || [];\r\n      setRecommendedExercises(exercisesData.slice(0, 4)); // 4 premiers exercices\r\n\r\n    } catch (err: any) {\r\n      console.error('Erreur lors du chargement des données étudiant:', err);\r\n      const errorMessage = err.response?.data?.error || err.response?.data?.detail || 'Erreur lors du chargement des données';\r\n      setError(errorMessage);\r\n      showError('Erreur', errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Quick actions\r\n  const quickActions = [\r\n    {\r\n      title: 'Résoudre une équation',\r\n      description: 'Utilisez notre solveur d\\'équations',\r\n      link: '/solver',\r\n      icon: '🧮',\r\n      color: 'bg-blue-500'\r\n    },\r\n    {\r\n      title: 'Faire un exercice',\r\n      description: 'Pratiquez avec nos exercices',\r\n      link: '/exercises',\r\n      icon: '📝',\r\n      color: 'bg-green-500'\r\n    },\r\n    {\r\n      title: 'Voir mes cours',\r\n      description: 'Accédez à vos cours',\r\n      link: '/courses',\r\n      icon: '📚',\r\n      color: 'bg-purple-500'\r\n    },\r\n    {\r\n      title: 'Ma progression',\r\n      description: 'Consultez vos statistiques',\r\n      link: '/progress',\r\n      icon: '📊',\r\n      color: 'bg-orange-500'\r\n    }\r\n  ];\r\n\r\n  // Calculer les statistiques rapides\r\n  const quickStats = studentStats ? [\r\n    {\r\n      label: 'Points totaux',\r\n      value: studentStats.general_stats?.total_points?.toString() || '0',\r\n      color: 'text-blue-600',\r\n      icon: '🏆'\r\n    },\r\n    {\r\n      label: 'Taux de réussite',\r\n      value: `${Math.round(studentStats.general_stats?.success_rate || 0)}%`,\r\n      color: 'text-green-600',\r\n      icon: '✅'\r\n    },\r\n    {\r\n      label: 'Exercices complétés',\r\n      value: studentStats.general_stats?.completed_attempts?.toString() || '0',\r\n      color: 'text-purple-600',\r\n      icon: '📋'\r\n    },\r\n    {\r\n      label: 'Temps moyen',\r\n      value: `${Math.round(studentStats.general_stats?.average_time || 0)}s`,\r\n      color: 'text-orange-600',\r\n      icon: '⏱️'\r\n    }\r\n  ] : [\r\n    // Données par défaut si pas de statistiques\r\n    {\r\n      label: 'Points totaux',\r\n      value: '0',\r\n      color: 'text-blue-600',\r\n      icon: '🏆'\r\n    },\r\n    {\r\n      label: 'Taux de réussite',\r\n      value: '0%',\r\n      color: 'text-green-600',\r\n      icon: '✅'\r\n    },\r\n    {\r\n      label: 'Exercices complétés',\r\n      value: '0',\r\n      color: 'text-purple-600',\r\n      icon: '📋'\r\n    },\r\n    {\r\n      label: 'Temps moyen',\r\n      value: '0s',\r\n      color: 'text-orange-600',\r\n      icon: '⏱️'\r\n    }\r\n  ];\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement de votre tableau de bord...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">Erreur de chargement</h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mb-6\">{error}</p>\r\n          <button\r\n            onClick={fetchStudentData}\r\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\r\n          >\r\n            Réessayer\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const refreshAction = (\r\n    <button\r\n      onClick={fetchStudentData}\r\n      className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2\"\r\n    >\r\n      <span>🔄</span>\r\n      <span>Actualiser</span>\r\n    </button>\r\n  );\r\n\r\n  return (\r\n    <StudentLayout\r\n      title=\"Tableau de bord étudiant\"\r\n      subtitle={`Bienvenue, ${user?.username} • Votre progression HandyMath`}\r\n      actions={refreshAction}\r\n    >\r\n\r\n      {/* Statistiques rapides */}\r\n      {quickStats.length > 0 && (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n          {quickStats.map((stat, index) => (\r\n            <motion.div\r\n              key={index}\r\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.1 }}\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex-1\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n                    {stat.label}\r\n                  </p>\r\n                  <p className={`text-2xl font-bold ${stat.color}`}>\r\n                    {stat.value}\r\n                  </p>\r\n                </div>\r\n                <div className=\"text-3xl\">\r\n                  {stat.icon}\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Actions rapides */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n        {quickActions.map((action, index) => (\r\n          <Link\r\n            key={index}\r\n            to={action.link}\r\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 group transform hover:scale-105\"\r\n          >\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className={`p-3 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform duration-300`}>\r\n                <span className=\"text-2xl\">{action.icon}</span>\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-1 group-hover:text-primary-600 transition-colors\">\r\n                  {action.title}\r\n                </h3>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                  {action.description}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </Link>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Cours et exercices récents */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\r\n        {/* Cours récents */}\r\n        <motion.div\r\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\r\n          initial={{ opacity: 0, x: -20 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n        >\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\r\n              📚 Cours disponibles\r\n            </h3>\r\n            <Link\r\n              to=\"/courses\"\r\n              className=\"text-primary-600 hover:text-primary-700 text-sm font-medium\"\r\n            >\r\n              Voir tout →\r\n            </Link>\r\n          </div>\r\n          <div className=\"space-y-3\">\r\n            {recentCourses.length > 0 ? (\r\n              recentCourses.map((course) => (\r\n                <Link\r\n                  key={course.id}\r\n                  to={`/courses/${course.id}`}\r\n                  className=\"block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex-1\">\r\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                        {course.title}\r\n                      </h4>\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n                        {course.lessons_count} leçons • Niveau {course.level}\r\n                      </p>\r\n                    </div>\r\n                    <div className={`px-2 py-1 rounded text-xs font-medium ${\r\n                      course.level === 'beginner' ? 'bg-green-100 text-green-800' :\r\n                      course.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :\r\n                      'bg-red-100 text-red-800'\r\n                    }`}>\r\n                      {course.level === 'beginner' ? 'Débutant' :\r\n                       course.level === 'intermediate' ? 'Intermédiaire' : 'Avancé'}\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              ))\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <div className=\"text-4xl mb-2\">📚</div>\r\n                <p className=\"text-gray-600 dark:text-gray-400\">Aucun cours disponible</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Exercices recommandés */}\r\n        <motion.div\r\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\r\n          initial={{ opacity: 0, x: 20 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n        >\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\r\n              🎯 Exercices recommandés\r\n            </h3>\r\n            <Link\r\n              to=\"/exercises\"\r\n              className=\"text-primary-600 hover:text-primary-700 text-sm font-medium\"\r\n            >\r\n              Voir tout →\r\n            </Link>\r\n          </div>\r\n          <div className=\"space-y-3\">\r\n            {recommendedExercises.length > 0 ? (\r\n              recommendedExercises.map((exercise) => (\r\n                <Link\r\n                  key={exercise.id}\r\n                  to={`/exercises/${exercise.id}`}\r\n                  className=\"block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex-1\">\r\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                        {exercise.title}\r\n                      </h4>\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n                        {exercise.exercise_type} • {exercise.points} points\r\n                      </p>\r\n                    </div>\r\n                    <div className={`px-2 py-1 rounded text-xs font-medium ${\r\n                      exercise.difficulty === 'easy' ? 'bg-green-100 text-green-800' :\r\n                      exercise.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :\r\n                      'bg-red-100 text-red-800'\r\n                    }`}>\r\n                      {exercise.difficulty === 'easy' ? 'Facile' :\r\n                       exercise.difficulty === 'medium' ? 'Moyen' : 'Difficile'}\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              ))\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <div className=\"text-4xl mb-2\">🎯</div>\r\n                <p className=\"text-gray-600 dark:text-gray-400\">Aucun exercice disponible</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Exercices récents */}\r\n      {studentStats && studentStats.recent_exercises && studentStats.recent_exercises.length > 0 && (\r\n        <motion.div\r\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n        >\r\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\r\n            🕒 Exercices récents\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            {studentStats.recent_exercises.slice(0, 5).map((exercise, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\"\r\n              >\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className={`w-3 h-3 rounded-full ${\r\n                    exercise.is_correct ? 'bg-green-500' : 'bg-red-500'\r\n                  }`}></div>\r\n                  <div>\r\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                      {exercise.title}\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      {new Date(exercise.completed_at).toLocaleDateString('fr-FR')}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <div className={`text-sm font-medium ${\r\n                    exercise.is_correct ? 'text-green-600' : 'text-red-600'\r\n                  }`}>\r\n                    {exercise.is_correct ? '✅ Réussi' : '❌ Échoué'}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-500\">\r\n                    +{exercise.points_earned} points\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </StudentLayout>\r\n  );\r\n};\r\n\r\nexport default StudentDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,OAAOC,aAAa,MAAM,gCAAgC;AAE1D,OAAOC,GAAG,MAAM,oBAAoB;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AA4CA,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEe;EAAgB,CAAC,GAAGd,gBAAgB,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzB,QAAQ,CAAa,EAAE,CAAC;EAChF,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM8B,SAAS,GAAGA,CAACC,KAAa,EAAEC,OAAe,KAAK;IACpDb,eAAe,CAAC;MAAEc,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,WAAW,GAAGA,CAACH,KAAa,EAAEC,OAAe,KAAK;IACtDb,eAAe,CAAC;MAAEc,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;;EAED;EACA/B,SAAS,CAAC,MAAM;IACd,IAAIiB,IAAI,EAAE;MACRiB,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACjB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMiB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCR,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF;MACA,MAAM,CAACO,aAAa,EAAEC,eAAe,EAAEC,iBAAiB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5EjC,GAAG,CAACkC,GAAG,CAAC,YAAY,CAAC,EACrBlC,GAAG,CAACkC,GAAG,CAAC,WAAW,CAAC,EACpBlC,GAAG,CAACkC,GAAG,CAAC,aAAa,CAAC,CACvB,CAAC;MAEFpB,eAAe,CAACe,aAAa,CAACM,IAAI,CAAC;;MAEnC;MACA,MAAMC,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACR,eAAe,CAACK,IAAI,CAAC,GAAGL,eAAe,CAACK,IAAI,GAAGL,eAAe,CAACK,IAAI,CAACI,OAAO,IAAI,EAAE;MACnHvB,gBAAgB,CAACoB,WAAW,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3C;MACA,MAAMC,aAAa,GAAGJ,KAAK,CAACC,OAAO,CAACP,iBAAiB,CAACI,IAAI,CAAC,GAAGJ,iBAAiB,CAACI,IAAI,GAAGJ,iBAAiB,CAACI,IAAI,CAACI,OAAO,IAAI,EAAE;MAC3HrB,uBAAuB,CAACuB,aAAa,CAACD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtD,CAAC,CAAC,OAAOE,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACjBC,OAAO,CAAC1B,KAAK,CAAC,iDAAiD,EAAEqB,GAAG,CAAC;MACrE,MAAMM,YAAY,GAAG,EAAAL,aAAA,GAAAD,GAAG,CAACO,QAAQ,cAAAN,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcR,IAAI,cAAAS,kBAAA,uBAAlBA,kBAAA,CAAoBvB,KAAK,OAAAwB,cAAA,GAAIH,GAAG,CAACO,QAAQ,cAAAJ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcV,IAAI,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBI,MAAM,KAAI,uCAAuC;MACvH5B,QAAQ,CAAC0B,YAAY,CAAC;MACtBzB,SAAS,CAAC,QAAQ,EAAEyB,YAAY,CAAC;IACnC,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+B,YAAY,GAAG,CACnB;IACE3B,KAAK,EAAE,uBAAuB;IAC9B4B,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACE/B,KAAK,EAAE,mBAAmB;IAC1B4B,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACE/B,KAAK,EAAE,gBAAgB;IACvB4B,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACE/B,KAAK,EAAE,gBAAgB;IACvB4B,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,UAAU,GAAG3C,YAAY,GAAG,CAChC;IACE4C,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,EAAArD,qBAAA,GAAAQ,YAAY,CAAC8C,aAAa,cAAAtD,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BuD,YAAY,cAAAtD,sBAAA,uBAAxCA,sBAAA,CAA0CuD,QAAQ,CAAC,CAAC,KAAI,GAAG;IAClEN,KAAK,EAAE,eAAe;IACtBD,IAAI,EAAE;EACR,CAAC,EACD;IACEG,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,GAAGI,IAAI,CAACC,KAAK,CAAC,EAAAxD,sBAAA,GAAAM,YAAY,CAAC8C,aAAa,cAAApD,sBAAA,uBAA1BA,sBAAA,CAA4ByD,YAAY,KAAI,CAAC,CAAC,GAAG;IACtET,KAAK,EAAE,gBAAgB;IACvBD,IAAI,EAAE;EACR,CAAC,EACD;IACEG,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAE,EAAAlD,sBAAA,GAAAK,YAAY,CAAC8C,aAAa,cAAAnD,sBAAA,wBAAAC,sBAAA,GAA1BD,sBAAA,CAA4ByD,kBAAkB,cAAAxD,sBAAA,uBAA9CA,sBAAA,CAAgDoD,QAAQ,CAAC,CAAC,KAAI,GAAG;IACxEN,KAAK,EAAE,iBAAiB;IACxBD,IAAI,EAAE;EACR,CAAC,EACD;IACEG,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,GAAGI,IAAI,CAACC,KAAK,CAAC,EAAArD,sBAAA,GAAAG,YAAY,CAAC8C,aAAa,cAAAjD,sBAAA,uBAA1BA,sBAAA,CAA4BwD,YAAY,KAAI,CAAC,CAAC,GAAG;IACtEX,KAAK,EAAE,iBAAiB;IACxBD,IAAI,EAAE;EACR,CAAC,CACF,GAAG;EACF;EACA;IACEG,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVH,KAAK,EAAE,eAAe;IACtBD,IAAI,EAAE;EACR,CAAC,EACD;IACEG,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,IAAI;IACXH,KAAK,EAAE,gBAAgB;IACvBD,IAAI,EAAE;EACR,CAAC,EACD;IACEG,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAE,GAAG;IACVH,KAAK,EAAE,iBAAiB;IACxBD,IAAI,EAAE;EACR,CAAC,EACD;IACEG,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,IAAI;IACXH,KAAK,EAAE,iBAAiB;IACxBD,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,IAAInC,OAAO,EAAE;IACX,oBACEjB,OAAA;MAAKiE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClE,OAAA;QAAKiE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClE,OAAA;UAAKiE,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGtE,OAAA;UAAGiE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAInD,KAAK,EAAE;IACT,oBACEnB,OAAA;MAAKiE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClE,OAAA;QAAKiE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClE,OAAA;UAAKiE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDtE,OAAA;UAAIiE,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/FtE,OAAA;UAAGiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAE/C;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEtE,OAAA;UACEuE,OAAO,EAAE7C,gBAAiB;UAC1BuC,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,aAAa,gBACjBxE,OAAA;IACEuE,OAAO,EAAE7C,gBAAiB;IAC1BuC,SAAS,EAAC,oJAAoJ;IAAAC,QAAA,gBAE9JlE,OAAA;MAAAkE,QAAA,EAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACftE,OAAA;MAAAkE,QAAA,EAAM;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CACT;EAED,oBACEtE,OAAA,CAACH,aAAa;IACZyB,KAAK,EAAC,6BAA0B;IAChCmD,QAAQ,EAAE,cAAchE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,QAAQ,gCAAiC;IACvEC,OAAO,EAAEH,aAAc;IAAAN,QAAA,GAItBZ,UAAU,CAACsB,MAAM,GAAG,CAAC,iBACpB5E,OAAA;MAAKiE,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACvEZ,UAAU,CAACuB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC1B/E,OAAA,CAACN,MAAM,CAACsF,GAAG;QAETf,SAAS,EAAC,oDAAoD;QAC9DgB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QAAAb,QAAA,eAEnClE,OAAA;UAAKiE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDlE,OAAA;YAAKiE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBlE,OAAA;cAAGiE,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAChEY,IAAI,CAACvB;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACJtE,OAAA;cAAGiE,SAAS,EAAE,sBAAsBa,IAAI,CAACzB,KAAK,EAAG;cAAAa,QAAA,EAC9CY,IAAI,CAACtB;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,UAAU;YAAAC,QAAA,EACtBY,IAAI,CAAC1B;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAlBDS,KAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBA,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDtE,OAAA;MAAKiE,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACvEjB,YAAY,CAAC4B,GAAG,CAAC,CAACU,MAAM,EAAER,KAAK,kBAC9B/E,OAAA,CAACP,IAAI;QAEH+F,EAAE,EAAED,MAAM,CAACpC,IAAK;QAChBc,SAAS,EAAC,gIAAgI;QAAAC,QAAA,eAE1IlE,OAAA;UAAKiE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ClE,OAAA;YAAKiE,SAAS,EAAE,kBAAkBsB,MAAM,CAAClC,KAAK,qEAAsE;YAAAa,QAAA,eAClHlE,OAAA;cAAMiE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEqB,MAAM,CAACnC;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBlE,OAAA;cAAIiE,SAAS,EAAC,yGAAyG;cAAAC,QAAA,EACpHqB,MAAM,CAACjE;YAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACLtE,OAAA;cAAGiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACpDqB,MAAM,CAACrC;YAAW;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAhBDS,KAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBN,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBAEzDlE,OAAA,CAACN,MAAM,CAACsF,GAAG;QACTf,SAAS,EAAC,oDAAoD;QAC9DgB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEO,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCL,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEO,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAApB,QAAA,gBAE3BlE,OAAA;UAAKiE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDlE,OAAA;YAAIiE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtE,OAAA,CAACP,IAAI;YACH+F,EAAE,EAAC,UAAU;YACbvB,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBrD,aAAa,CAAC+D,MAAM,GAAG,CAAC,GACvB/D,aAAa,CAACgE,GAAG,CAAEa,MAAM,iBACvB1F,OAAA,CAACP,IAAI;YAEH+F,EAAE,EAAE,YAAYE,MAAM,CAACC,EAAE,EAAG;YAC5B1B,SAAS,EAAC,6GAA6G;YAAAC,QAAA,eAEvHlE,OAAA;cAAKiE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlE,OAAA;gBAAKiE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlE,OAAA;kBAAIiE,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACtDwB,MAAM,CAACpE;gBAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACLtE,OAAA;kBAAGiE,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,GACzDwB,MAAM,CAACE,aAAa,EAAC,2BAAiB,EAACF,MAAM,CAACG,KAAK;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAE,yCACdyB,MAAM,CAACG,KAAK,KAAK,UAAU,GAAG,6BAA6B,GAC3DH,MAAM,CAACG,KAAK,KAAK,cAAc,GAAG,+BAA+B,GACjE,yBAAyB,EACxB;gBAAA3B,QAAA,EACAwB,MAAM,CAACG,KAAK,KAAK,UAAU,GAAG,UAAU,GACxCH,MAAM,CAACG,KAAK,KAAK,cAAc,GAAG,eAAe,GAAG;cAAQ;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GArBDoB,MAAM,CAACC,EAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBV,CACP,CAAC,gBAEFtE,OAAA;YAAKiE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BlE,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtE,OAAA;cAAGiE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbtE,OAAA,CAACN,MAAM,CAACsF,GAAG;QACTf,SAAS,EAAC,oDAAoD;QAC9DgB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEO,CAAC,EAAE;QAAG,CAAE;QAC/BL,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEO,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAApB,QAAA,gBAE3BlE,OAAA;UAAKiE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDlE,OAAA;YAAIiE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtE,OAAA,CAACP,IAAI;YACH+F,EAAE,EAAC,YAAY;YACfvB,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBnD,oBAAoB,CAAC6D,MAAM,GAAG,CAAC,GAC9B7D,oBAAoB,CAAC8D,GAAG,CAAEiB,QAAQ,iBAChC9F,OAAA,CAACP,IAAI;YAEH+F,EAAE,EAAE,cAAcM,QAAQ,CAACH,EAAE,EAAG;YAChC1B,SAAS,EAAC,6GAA6G;YAAAC,QAAA,eAEvHlE,OAAA;cAAKiE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlE,OAAA;gBAAKiE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlE,OAAA;kBAAIiE,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACtD4B,QAAQ,CAACxE;gBAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACLtE,OAAA;kBAAGiE,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,GACzD4B,QAAQ,CAACC,aAAa,EAAC,UAAG,EAACD,QAAQ,CAACE,MAAM,EAAC,SAC9C;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAE,yCACd6B,QAAQ,CAACG,UAAU,KAAK,MAAM,GAAG,6BAA6B,GAC9DH,QAAQ,CAACG,UAAU,KAAK,QAAQ,GAAG,+BAA+B,GAClE,yBAAyB,EACxB;gBAAA/B,QAAA,EACA4B,QAAQ,CAACG,UAAU,KAAK,MAAM,GAAG,QAAQ,GACzCH,QAAQ,CAACG,UAAU,KAAK,QAAQ,GAAG,OAAO,GAAG;cAAW;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GArBDwB,QAAQ,CAACH,EAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBZ,CACP,CAAC,gBAEFtE,OAAA;YAAKiE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BlE,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtE,OAAA;cAAGiE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGL3D,YAAY,IAAIA,YAAY,CAACuF,gBAAgB,IAAIvF,YAAY,CAACuF,gBAAgB,CAACtB,MAAM,GAAG,CAAC,iBACxF5E,OAAA,CAACN,MAAM,CAACsF,GAAG;MACTf,SAAS,EAAC,oDAAoD;MAC9DgB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAApB,QAAA,gBAE3BlE,OAAA;QAAIiE,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EAAC;MAEzE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtE,OAAA;QAAKiE,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBvD,YAAY,CAACuF,gBAAgB,CAAC5D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuC,GAAG,CAAC,CAACiB,QAAQ,EAAEf,KAAK,kBAC7D/E,OAAA;UAEEiE,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFlE,OAAA;YAAKiE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClE,OAAA;cAAKiE,SAAS,EAAE,wBACd6B,QAAQ,CAACK,UAAU,GAAG,cAAc,GAAG,YAAY;YAClD;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACVtE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAIiE,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EACtD4B,QAAQ,CAACxE;cAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACLtE,OAAA;gBAAGiE,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EACpD,IAAIkC,IAAI,CAACN,QAAQ,CAACO,YAAY,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAC;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAKiE,SAAS,EAAE,uBACd6B,QAAQ,CAACK,UAAU,GAAG,gBAAgB,GAAG,cAAc,EACtD;cAAAjC,QAAA,EACA4B,QAAQ,CAACK,UAAU,GAAG,UAAU,GAAG;YAAU;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNtE,OAAA;cAAKiE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,GACpC,EAAC4B,QAAQ,CAACS,aAAa,EAAC,SAC3B;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAzBDS,KAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0BP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEpB,CAAC;AAACpE,EAAA,CAvZID,gBAA0B;EAAA,QACbN,OAAO,EACIC,gBAAgB;AAAA;AAAA4G,EAAA,GAFxCvG,gBAA0B;AAyZhC,eAAeA,gBAAgB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}