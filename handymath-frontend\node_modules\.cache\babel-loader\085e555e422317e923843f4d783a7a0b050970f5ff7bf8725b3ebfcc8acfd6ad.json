{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isZero';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createIsZero = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Test whether a value is zero.\n   * The function can check for zero for types `number`, `BigNumber`, `Fraction`,\n   * `Complex`, and `Unit`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isZero(x)\n   *\n   * Examples:\n   *\n   *    math.isZero(0)                      // returns true\n   *    math.isZero(2)                      // returns false\n   *    math.isZero(0.5)                    // returns false\n   *    math.isZero(math.bignumber(0))      // returns true\n   *    math.isZero(math.fraction(0))       // returns true\n   *    math.isZero(math.fraction(1,3))     // returns false\n   *    math.isZero(math.complex('2 - 4i')) // returns false\n   *    math.isZero(math.complex('0i'))     // returns true\n   *    math.isZero('0')                    // returns true\n   *    math.isZero('2')                    // returns false\n   *    math.isZero([2, 0, -3])             // returns [false, true, false]\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isNegative, isInteger\n   *\n   * @param {number | BigNumber | bigint | Complex | Fraction | Unit | Array | Matrix} x       Value to be tested\n   * @return {boolean}  Returns true when `x` is zero.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    'number | BigNumber | Complex | Fraction': x => equalScalar(x, 0),\n    bigint: x => x === 0n,\n    Unit: typed.referToSelf(self => x => typed.find(self, x.valueType())(x.value)),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "factory", "name", "dependencies", "createIsZero", "_ref", "typed", "equalScalar", "x", "bigint", "Unit", "referToSelf", "self", "find", "valueType", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/isZero.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isZero';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createIsZero = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Test whether a value is zero.\n   * The function can check for zero for types `number`, `BigNumber`, `Fraction`,\n   * `Complex`, and `Unit`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isZero(x)\n   *\n   * Examples:\n   *\n   *    math.isZero(0)                      // returns true\n   *    math.isZero(2)                      // returns false\n   *    math.isZero(0.5)                    // returns false\n   *    math.isZero(math.bignumber(0))      // returns true\n   *    math.isZero(math.fraction(0))       // returns true\n   *    math.isZero(math.fraction(1,3))     // returns false\n   *    math.isZero(math.complex('2 - 4i')) // returns false\n   *    math.isZero(math.complex('0i'))     // returns true\n   *    math.isZero('0')                    // returns true\n   *    math.isZero('2')                    // returns false\n   *    math.isZero([2, 0, -3])             // returns [false, true, false]\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isNegative, isInteger\n   *\n   * @param {number | BigNumber | bigint | Complex | Fraction | Unit | Array | Matrix} x       Value to be tested\n   * @return {boolean}  Returns true when `x` is zero.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    'number | BigNumber | Complex | Fraction': x => equalScalar(x, 0),\n    bigint: x => x === 0n,\n    Unit: typed.referToSelf(self => x => typed.find(self, x.valueType())(x.value)),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,QAAQ;AACnB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,YAAY,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC3E,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,yCAAyC,EAAEM,CAAC,IAAID,WAAW,CAACC,CAAC,EAAE,CAAC,CAAC;IACjEC,MAAM,EAAED,CAAC,IAAIA,CAAC,KAAK,EAAE;IACrBE,IAAI,EAAEJ,KAAK,CAACK,WAAW,CAACC,IAAI,IAAIJ,CAAC,IAAIF,KAAK,CAACO,IAAI,CAACD,IAAI,EAAEJ,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,CAACN,CAAC,CAACO,KAAK,CAAC,CAAC;IAC9E,gBAAgB,EAAET,KAAK,CAACK,WAAW,CAACC,IAAI,IAAIJ,CAAC,IAAIR,OAAO,CAACQ,CAAC,EAAEI,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}