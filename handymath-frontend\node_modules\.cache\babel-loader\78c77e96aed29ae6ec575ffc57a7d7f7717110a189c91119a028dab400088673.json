{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\StudentNavbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentNavbar = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Composants d'icônes SVG\n  const DashboardIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n  const CoursesIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n  const ExercisesIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n  const ProgressIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n  const SolverIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n  const VisualizerIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n\n  // Navigation items pour l'espace étudiant\n  const navItems = [{\n    label: 'Tableau de bord',\n    path: '/etudiant/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 13\n    }, this),\n    description: 'Vue d\\'ensemble de votre progression'\n  }, {\n    label: 'Mes Cours',\n    path: '/courses',\n    icon: /*#__PURE__*/_jsxDEV(CoursesIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    description: 'Accédez à vos cours et leçons'\n  }, {\n    label: 'Exercices',\n    path: '/exercises',\n    icon: /*#__PURE__*/_jsxDEV(ExercisesIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this),\n    description: 'Pratiquez avec nos exercices'\n  }, {\n    label: 'Ma Progression',\n    path: '/progress',\n    icon: /*#__PURE__*/_jsxDEV(ProgressIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 13\n    }, this),\n    description: 'Suivez vos statistiques'\n  }, {\n    label: 'Résolveur',\n    path: '/solver',\n    icon: /*#__PURE__*/_jsxDEV(SolverIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this),\n    description: 'Résolvez des équations'\n  }, {\n    label: 'Visualiseur',\n    path: '/visualizer',\n    icon: /*#__PURE__*/_jsxDEV(VisualizerIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    description: 'Visualisez des fonctions'\n  }];\n  const isActivePath = path => {\n    if (path === '/etudiant/dashboard') {\n      return location.pathname === path;\n    }\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors\",\n            children: \"HandyMath\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 dark:text-gray-500\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-lg font-medium text-gray-700 dark:text-gray-300\",\n              children: \"Espace \\xC9tudiant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center space-x-1\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `group relative px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActivePath(item.path) ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'}`,\n            title: item.description,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\",\n              children: [item.description, /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: toggleTheme,\n            className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            title: theme === 'light' ? 'Mode sombre' : 'Mode clair',\n            children: theme === 'light' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 rounded-full bg-primary-600 flex items-center justify-center text-white text-sm font-semibold\",\n                children: user.username.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:block\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: user.prenom || user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-primary-600 dark:text-primary-400\",\n                  children: \"\\xC9tudiant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 9l-7 7-7-7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/profile\",\n                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 mr-3\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 25\n                    }, this), \"Mon Profil\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/settings\",\n                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 mr-3\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 25\n                    }, this), \"Param\\xE8tres\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-t border-gray-200 dark:border-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleLogout,\n                    className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 mr-3\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 25\n                    }, this), \"D\\xE9connexion\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"lg:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden border-t border-gray-200 dark:border-gray-700 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isActivePath(item.path) ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentNavbar, \"mqNeklrjssChoYLNAXV6Lg6vJL0=\", false, function () {\n  return [useLocation, useNavigate, useAuth, useTheme];\n});\n_c = StudentNavbar;\nexport default StudentNavbar;\nvar _c;\n$RefreshReg$(_c, \"StudentNavbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "useAuth", "useTheme", "jsxDEV", "_jsxDEV", "StudentNavbar", "_s", "location", "navigate", "user", "logout", "theme", "toggleTheme", "isMobileMenuOpen", "setIsMobileMenuOpen", "handleLogout", "DashboardIcon", "className", "fill", "stroke", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "CoursesIcon", "ExercisesIcon", "ProgressIcon", "SolverIcon", "VisualizerIcon", "navItems", "label", "path", "icon", "description", "isActivePath", "pathname", "startsWith", "to", "map", "item", "title", "type", "onClick", "username", "char<PERSON>t", "toUpperCase", "prenom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/StudentNavbar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface NavItem {\n  label: string;\n  path: string;\n  icon: React.ReactNode;\n  description: string;\n  badge?: string;\n}\n\nconst StudentNavbar: React.FC = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Composants d'icônes SVG\n  const DashboardIcon = () => (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  );\n\n  const CoursesIcon = () => (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n    </svg>\n  );\n\n  const ExercisesIcon = () => (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n    </svg>\n  );\n\n  const ProgressIcon = () => (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  );\n\n  const SolverIcon = () => (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n    </svg>\n  );\n\n  const VisualizerIcon = () => (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n    </svg>\n  );\n\n  // Navigation items pour l'espace étudiant\n  const navItems = [\n    {\n      label: 'Tableau de bord',\n      path: '/etudiant/dashboard',\n      icon: <DashboardIcon />,\n      description: 'Vue d\\'ensemble de votre progression'\n    },\n    {\n      label: 'Mes Cours',\n      path: '/courses',\n      icon: <CoursesIcon />,\n      description: 'Accédez à vos cours et leçons'\n    },\n    {\n      label: 'Exercices',\n      path: '/exercises',\n      icon: <ExercisesIcon />,\n      description: 'Pratiquez avec nos exercices'\n    },\n    {\n      label: 'Ma Progression',\n      path: '/progress',\n      icon: <ProgressIcon />,\n      description: 'Suivez vos statistiques'\n    },\n    {\n      label: 'Résolveur',\n      path: '/solver',\n      icon: <SolverIcon />,\n      description: 'Résolvez des équations'\n    },\n    {\n      label: 'Visualiseur',\n      path: '/visualizer',\n      icon: <VisualizerIcon />,\n      description: 'Visualisez des fonctions'\n    }\n  ];\n\n  const isActivePath = (path: string) => {\n    if (path === '/etudiant/dashboard') {\n      return location.pathname === path;\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo et titre */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              to=\"/\"\n              className=\"text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors\"\n            >\n              HandyMath\n            </Link>\n            <div className=\"hidden md:block\">\n              <span className=\"text-gray-400 dark:text-gray-500\">•</span>\n              <span className=\"ml-2 text-lg font-medium text-gray-700 dark:text-gray-300\">\n                Espace Étudiant\n              </span>\n            </div>\n          </div>\n\n          {/* Navigation principale - Desktop */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`group relative px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                  isActivePath(item.path)\n                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'\n                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'\n                }`}\n                title={item.description}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <span>{item.icon}</span>\n                  <span>{item.label}</span>\n                </div>\n                \n                {/* Tooltip */}\n                <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\">\n                  {item.description}\n                  <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700\"></div>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Actions utilisateur */}\n          <div className=\"flex items-center space-x-3\">\n            {/* Bouton thème */}\n            <button\n              type=\"button\"\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              title={theme === 'light' ? 'Mode sombre' : 'Mode clair'}\n            >\n              {theme === 'light' ? (\n                <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n                </svg>\n              ) : (\n                <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n                </svg>\n              )}\n            </button>\n\n            {/* Informations utilisateur */}\n            {user && (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"hidden sm:flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary-600 flex items-center justify-center text-white text-sm font-semibold\">\n                    {user.username.charAt(0).toUpperCase()}\n                  </div>\n                  <div className=\"hidden md:block\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {user.prenom || user.username}\n                    </div>\n                    <div className=\"text-xs text-primary-600 dark:text-primary-400\">\n                      Étudiant\n                    </div>\n                  </div>\n                </div>\n\n                {/* Menu utilisateur */}\n                <div className=\"relative group\">\n                  <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\">\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                    </svg>\n                  </button>\n                  \n                  {/* Dropdown menu */}\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto\">\n                    <div className=\"py-1\">\n                      <Link\n                        to=\"/profile\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      >\n                        <svg className=\"w-4 h-4 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                        Mon Profil\n                      </Link>\n                      <Link\n                        to=\"/settings\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      >\n                        <svg className=\"w-4 h-4 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                        Paramètres\n                      </Link>\n                      <div className=\"border-t border-gray-200 dark:border-gray-600\"></div>\n                      <button\n                        onClick={handleLogout}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20\"\n                      >\n                        <svg className=\"w-4 h-4 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        Déconnexion\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Menu mobile */}\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                {isMobileMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Menu mobile */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden border-t border-gray-200 dark:border-gray-700 py-4\">\n            <div className=\"space-y-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\n                    isActivePath(item.path)\n                      ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'\n                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <span>{item.icon}</span>\n                  <div>\n                    <div>{item.label}</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">{item.description}</div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default StudentNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUpD,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEU,KAAK;IAAEC;EAAY,CAAC,GAAGV,QAAQ,CAAC,CAAC;EACzC,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzBL,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAGA,CAAA,kBACpBZ,OAAA;IAAKa,SAAS,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAC5EjB,OAAA;MAAMkB,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAAsM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3Q,CACN;EAED,MAAMC,WAAW,GAAGA,CAAA,kBAClB1B,OAAA;IAAKa,SAAS,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAC5EjB,OAAA;MAAMkB,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAAoP;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzT,CACN;EAED,MAAME,aAAa,GAAGA,CAAA,kBACpB3B,OAAA;IAAKa,SAAS,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAC5EjB,OAAA;MAAMkB,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAAsH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3L,CACN;EAED,MAAMG,YAAY,GAAGA,CAAA,kBACnB5B,OAAA;IAAKa,SAAS,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAC5EjB,OAAA;MAAMkB,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAAsM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3Q,CACN;EAED,MAAMI,UAAU,GAAGA,CAAA,kBACjB7B,OAAA;IAAKa,SAAS,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAC5EjB,OAAA;MAAMkB,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAAoJ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzN,CACN;EAED,MAAMK,cAAc,GAAGA,CAAA,kBACrB9B,OAAA;IAAKa,SAAS,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAC5EjB,OAAA;MAAMkB,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAAyF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9J,CACN;;EAED;EACA,MAAMM,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,eAAElC,OAAA,CAACY,aAAa;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBU,WAAW,EAAE;EACf,CAAC,EACD;IACEH,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAElC,OAAA,CAAC0B,WAAW;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBU,WAAW,EAAE;EACf,CAAC,EACD;IACEH,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAElC,OAAA,CAAC2B,aAAa;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBU,WAAW,EAAE;EACf,CAAC,EACD;IACEH,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAElC,OAAA,CAAC4B,YAAY;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBU,WAAW,EAAE;EACf,CAAC,EACD;IACEH,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,SAAS;IACfC,IAAI,eAAElC,OAAA,CAAC6B,UAAU;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBU,WAAW,EAAE;EACf,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,aAAa;IACnBC,IAAI,eAAElC,OAAA,CAAC8B,cAAc;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBU,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,YAAY,GAAIH,IAAY,IAAK;IACrC,IAAIA,IAAI,KAAK,qBAAqB,EAAE;MAClC,OAAO9B,QAAQ,CAACkC,QAAQ,KAAKJ,IAAI;IACnC;IACA,OAAO9B,QAAQ,CAACkC,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEjC,OAAA;IAAKa,SAAS,EAAC,qGAAqG;IAAAI,QAAA,eAClHjB,OAAA;MAAKa,SAAS,EAAC,wCAAwC;MAAAI,QAAA,gBACrDjB,OAAA;QAAKa,SAAS,EAAC,wCAAwC;QAAAI,QAAA,gBAErDjB,OAAA;UAAKa,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAC1CjB,OAAA,CAACN,IAAI;YACH6C,EAAE,EAAC,GAAG;YACN1B,SAAS,EAAC,8EAA8E;YAAAI,QAAA,EACzF;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA;YAAKa,SAAS,EAAC,iBAAiB;YAAAI,QAAA,gBAC9BjB,OAAA;cAAMa,SAAS,EAAC,kCAAkC;cAAAI,QAAA,EAAC;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DzB,OAAA;cAAMa,SAAS,EAAC,2DAA2D;cAAAI,QAAA,EAAC;YAE5E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzB,OAAA;UAAKa,SAAS,EAAC,uCAAuC;UAAAI,QAAA,EACnDc,QAAQ,CAACS,GAAG,CAAEC,IAAI,iBACjBzC,OAAA,CAACN,IAAI;YAEH6C,EAAE,EAAEE,IAAI,CAACR,IAAK;YACdpB,SAAS,EAAE,uFACTuB,YAAY,CAACK,IAAI,CAACR,IAAI,CAAC,GACnB,2EAA2E,GAC3E,8HAA8H,EACjI;YACHS,KAAK,EAAED,IAAI,CAACN,WAAY;YAAAlB,QAAA,gBAExBjB,OAAA;cAAKa,SAAS,EAAC,6BAA6B;cAAAI,QAAA,gBAC1CjB,OAAA;gBAAAiB,QAAA,EAAOwB,IAAI,CAACP;cAAI;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBzB,OAAA;gBAAAiB,QAAA,EAAOwB,IAAI,CAACT;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGNzB,OAAA;cAAKa,SAAS,EAAC,4OAA4O;cAAAI,QAAA,GACxPwB,IAAI,CAACN,WAAW,eACjBnC,OAAA;gBAAKa,SAAS,EAAC;cAA4J;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/K,CAAC;UAAA,GAlBDgB,IAAI,CAACR,IAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzB,OAAA;UAAKa,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAE1CjB,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEpC,WAAY;YACrBK,SAAS,EAAC,yJAAyJ;YACnK6B,KAAK,EAAEnC,KAAK,KAAK,OAAO,GAAG,aAAa,GAAG,YAAa;YAAAU,QAAA,EAEvDV,KAAK,KAAK,OAAO,gBAChBP,OAAA;cAAKa,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACE,OAAO,EAAC,WAAW;cAACD,MAAM,EAAC,cAAc;cAAAE,QAAA,eAC5EjB,OAAA;gBAAMkB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAuF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5J,CAAC,gBAENzB,OAAA;cAAKa,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACE,OAAO,EAAC,WAAW;cAACD,MAAM,EAAC,cAAc;cAAAE,QAAA,eAC5EjB,OAAA;gBAAMkB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAuJ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5N;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,EAGRpB,IAAI,iBACHL,OAAA;YAAKa,SAAS,EAAC,6BAA6B;YAAAI,QAAA,gBAC1CjB,OAAA;cAAKa,SAAS,EAAC,uCAAuC;cAAAI,QAAA,gBACpDjB,OAAA;gBAAKa,SAAS,EAAC,uGAAuG;gBAAAI,QAAA,EACnHZ,IAAI,CAACwC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNzB,OAAA;gBAAKa,SAAS,EAAC,iBAAiB;gBAAAI,QAAA,gBAC9BjB,OAAA;kBAAKa,SAAS,EAAC,mDAAmD;kBAAAI,QAAA,EAC/DZ,IAAI,CAAC2C,MAAM,IAAI3C,IAAI,CAACwC;gBAAQ;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNzB,OAAA;kBAAKa,SAAS,EAAC,gDAAgD;kBAAAI,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzB,OAAA;cAAKa,SAAS,EAAC,gBAAgB;cAAAI,QAAA,gBAC7BjB,OAAA;gBAAQa,SAAS,EAAC,yJAAyJ;gBAAAI,QAAA,eACzKjB,OAAA;kBAAKa,SAAS,EAAC,SAAS;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAC,QAAA,eAC5EjB,OAAA;oBAAMkB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAGTzB,OAAA;gBAAKa,SAAS,EAAC,6OAA6O;gBAAAI,QAAA,eAC1PjB,OAAA;kBAAKa,SAAS,EAAC,MAAM;kBAAAI,QAAA,gBACnBjB,OAAA,CAACN,IAAI;oBACH6C,EAAE,EAAC,UAAU;oBACb1B,SAAS,EAAC,+GAA+G;oBAAAI,QAAA,gBAEzHjB,OAAA;sBAAKa,SAAS,EAAC,cAAc;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAC,QAAA,eACjFjB,OAAA;wBAAMkB,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAqE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1I,CAAC,cAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPzB,OAAA,CAACN,IAAI;oBACH6C,EAAE,EAAC,WAAW;oBACd1B,SAAS,EAAC,+GAA+G;oBAAAI,QAAA,gBAEzHjB,OAAA;sBAAKa,SAAS,EAAC,cAAc;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAC,QAAA,gBACjFjB,OAAA;wBAAMkB,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAqe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7iBzB,OAAA;wBAAMkB,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAkC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvG,CAAC,iBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPzB,OAAA;oBAAKa,SAAS,EAAC;kBAA+C;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrEzB,OAAA;oBACE4C,OAAO,EAAEjC,YAAa;oBACtBE,SAAS,EAAC,oHAAoH;oBAAAI,QAAA,gBAE9HjB,OAAA;sBAAKa,SAAS,EAAC,cAAc;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAC,QAAA,eACjFjB,OAAA;wBAAMkB,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA2F;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChK,CAAC,kBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDzB,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMlC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtDI,SAAS,EAAC,mKAAmK;YAAAI,QAAA,eAE7KjB,OAAA;cAAKa,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAC,QAAA,EAC3ER,gBAAgB,gBACfT,OAAA;gBAAMkB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9FzB,OAAA;gBAAMkB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLhB,gBAAgB,iBACfT,OAAA;QAAKa,SAAS,EAAC,8DAA8D;QAAAI,QAAA,eAC3EjB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAI,QAAA,EACvBc,QAAQ,CAACS,GAAG,CAAEC,IAAI,iBACjBzC,OAAA,CAACN,IAAI;YAEH6C,EAAE,EAAEE,IAAI,CAACR,IAAK;YACdW,OAAO,EAAEA,CAAA,KAAMlC,mBAAmB,CAAC,KAAK,CAAE;YAC1CG,SAAS,EAAE,0FACTuB,YAAY,CAACK,IAAI,CAACR,IAAI,CAAC,GACnB,2EAA2E,GAC3E,2EAA2E,EAC9E;YAAAhB,QAAA,gBAEHjB,OAAA;cAAAiB,QAAA,EAAOwB,IAAI,CAACP;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBzB,OAAA;cAAAiB,QAAA,gBACEjB,OAAA;gBAAAiB,QAAA,EAAMwB,IAAI,CAACT;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvBzB,OAAA;gBAAKa,SAAS,EAAC,0CAA0C;gBAAAI,QAAA,EAAEwB,IAAI,CAACN;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA,GAbDgB,IAAI,CAACR,IAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA9QID,aAAuB;EAAA,QACVN,WAAW,EACXC,WAAW,EACHC,OAAO,EACDC,QAAQ;AAAA;AAAAmD,EAAA,GAJnChD,aAAuB;AAgR7B,eAAeA,aAAa;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}