#!/usr/bin/env python
"""
Script de test pour l'API OCR
"""
import os
import sys
import django
import requests
import base64
from PIL import Image, ImageDraw, ImageFont
import io

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

def create_test_image(equation="2x + 3 = 7"):
    """Créer une image de test"""
    img = Image.new('RGB', (400, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 36)
    except:
        font = ImageFont.load_default()
    
    # Centrer le texte
    bbox = draw.textbbox((0, 0), equation, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (400 - text_width) // 2
    y = (100 - text_height) // 2
    
    draw.text((x, y), equation, fill='black', font=font)
    
    return img

def image_to_base64(image):
    """Convertir une image PIL en base64"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_data = buffer.getvalue()
    return base64.b64encode(img_data).decode('utf-8')

def test_ocr_endpoint():
    """Tester l'endpoint OCR"""
    print("=== Test de l'endpoint OCR ===")
    
    # Créer une image de test
    test_image = create_test_image("x + 5 = 10")
    
    # Sauvegarder l'image pour vérification
    test_image.save("test_api_image.png")
    print("Image de test sauvegardée: test_api_image.png")
    
    # Préparer les données pour l'API
    with open("test_api_image.png", "rb") as f:
        files = {'image': f}
        
        try:
            # Tester l'endpoint de test (sans authentification)
            response = requests.post(
                'http://localhost:8000/api/equations/recognize-test/',
                files=files
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.json()}")
            
        except requests.exceptions.ConnectionError:
            print("❌ Erreur: Impossible de se connecter au serveur Django")
            print("Assurez-vous que le serveur Django est démarré avec: python manage.py runserver 8000")
        except Exception as e:
            print(f"❌ Erreur: {e}")

def test_ocr_function_directly():
    """Tester la fonction OCR directement"""
    print("\n=== Test direct de la fonction OCR ===")
    
    try:
        from api.views import recognize_equation_from_image
        from django.test import RequestFactory
        from django.contrib.auth import get_user_model
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        # Créer une image de test
        test_image = create_test_image("2x + 1 = 9")
        
        # Convertir en fichier uploadé
        buffer = io.BytesIO()
        test_image.save(buffer, format='PNG')
        buffer.seek(0)
        
        uploaded_file = SimpleUploadedFile(
            "test.png",
            buffer.getvalue(),
            content_type="image/png"
        )
        
        # Créer une requête factice
        factory = RequestFactory()
        request = factory.post('/api/equations/recognize/', {'image': uploaded_file})
        
        # Créer un utilisateur factice
        User = get_user_model()
        request.user = User(id=1, username='test_user')
        
        # Ajouter le fichier à la requête
        request.FILES = {'image': uploaded_file}
        
        # Appeler la fonction
        response = recognize_equation_from_image(request)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Data: {response.data}")
        
    except Exception as e:
        print(f"❌ Erreur lors du test direct: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ocr_function_directly()
    test_ocr_endpoint()
