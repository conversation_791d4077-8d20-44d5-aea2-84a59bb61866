{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ResetPasswordPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport SimpleHeader from '../components/SimpleHeader';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ResetPasswordPage = () => {\n  _s();\n  const {\n    uid,\n    token\n  } = useParams();\n  const navigate = useNavigate();\n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isValidating, setIsValidating] = useState(true);\n  const [isValidLink, setIsValidLink] = useState(false);\n  const [userEmail, setUserEmail] = useState('');\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n\n  // Valider le lien au chargement de la page\n  useEffect(() => {\n    const validateLink = async () => {\n      if (!uid || !token) {\n        setError('Lien de réinitialisation invalide');\n        setIsValidating(false);\n        return;\n      }\n      try {\n        const response = await api.get(`/password-reset/validate/${uid}/${token}/`);\n        if (response.data.valid) {\n          setIsValidLink(true);\n          setUserEmail(response.data.user_email);\n        } else {\n          setError(response.data.error || 'Lien de réinitialisation invalide');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la validation du lien:', err);\n        setError('Lien de réinitialisation invalide ou expiré');\n      } finally {\n        setIsValidating(false);\n      }\n    };\n    validateLink();\n  }, [uid, token]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validation côté client\n    if (!newPassword || !confirmPassword) {\n      setError('Veuillez remplir tous les champs');\n      return;\n    }\n    if (newPassword !== confirmPassword) {\n      setError('Les mots de passe ne correspondent pas');\n      return;\n    }\n    if (newPassword.length < 8) {\n      setError('Le mot de passe doit contenir au moins 8 caractères');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await api.post('/password-reset/confirm/', {\n        uid,\n        token,\n        new_password: newPassword\n      });\n      if (response.data.success) {\n        setSuccess(true);\n        // Rediriger vers la page de connexion après 3 secondes\n        setTimeout(() => {\n          navigate('/login', {\n            state: {\n              message: 'Votre mot de passe a été réinitialisé avec succès. Veuillez vous connecter.'\n            }\n          });\n        }, 3000);\n      } else {\n        setError(response.data.error || 'Une erreur est survenue');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Erreur lors de la réinitialisation:', err);\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.error) {\n        setError(err.response.data.error);\n      } else {\n        setError('Une erreur est survenue. Veuillez réessayer.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  if (isValidating) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n        title: \"R\\xE9initialisation du mot de passe\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600 dark:text-gray-400\",\n            children: \"Validation du lien...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  if (!isValidLink) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n        title: \"Lien invalide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg text-center\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-8 w-8 text-red-600 dark:text-red-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n              children: \"Lien invalide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400 mb-6\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/forgot-password\",\n              className: \"block w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors\",\n              children: \"Demander un nouveau lien\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"block w-full px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md transition-colors\",\n              children: \"Retour \\xE0 la connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  if (success) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n        title: \"Mot de passe r\\xE9initialis\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg text-center\",\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-8 w-8 text-green-600 dark:text-green-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n              children: \"Mot de passe r\\xE9initialis\\xE9 !\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400 mb-4\",\n              children: \"Votre mot de passe a \\xE9t\\xE9 r\\xE9initialis\\xE9 avec succ\\xE8s.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 dark:text-gray-500\",\n              children: \"Redirection vers la page de connexion dans quelques secondes...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"inline-block px-6 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors\",\n            children: \"Se connecter maintenant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"Nouveau mot de passe\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white\",\n            children: \"Nouveau mot de passe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\",\n            children: [\"Pour le compte : \", userEmail]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"mt-8 space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-md\",\n            initial: {\n              opacity: 0,\n              scale: 0.95\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-red-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"newPassword\",\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Nouveau mot de passe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"newPassword\",\n                  name: \"newPassword\",\n                  type: showPassword ? \"text\" : \"password\",\n                  required: true,\n                  value: newPassword,\n                  onChange: e => setNewPassword(e.target.value),\n                  className: \"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 focus:z-10 sm:text-sm\",\n                  placeholder: \"Nouveau mot de passe (min. 8 caract\\xE8res)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                  onClick: () => setShowPassword(!showPassword),\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Confirmer le mot de passe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"confirmPassword\",\n                  name: \"confirmPassword\",\n                  type: showConfirmPassword ? \"text\" : \"password\",\n                  required: true,\n                  value: confirmPassword,\n                  onChange: e => setConfirmPassword(e.target.value),\n                  className: \"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 focus:z-10 sm:text-sm\",\n                  placeholder: \"Confirmer le nouveau mot de passe\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                  onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                  children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), \"R\\xE9initialisation...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this) : 'Réinitialiser le mot de passe'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ResetPasswordPage, \"weBsXYEuYyeGc1VkHqDMedQMB3Q=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ResetPasswordPage;\nexport default ResetPasswordPage;\nvar _c;\n$RefreshReg$(_c, \"ResetPasswordPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "motion", "api", "SimpleHeader", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ResetPasswordPage", "_s", "uid", "token", "navigate", "newPassword", "setNewPassword", "confirmPassword", "setConfirmPassword", "isLoading", "setIsLoading", "isValidating", "setIsValidating", "isValidLink", "setIsValidLink", "userEmail", "setUserEmail", "error", "setError", "success", "setSuccess", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "validateLink", "response", "get", "data", "valid", "user_email", "err", "console", "handleSubmit", "e", "preventDefault", "length", "post", "new_password", "setTimeout", "state", "message", "_err$response", "_err$response$data", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "div", "initial", "opacity", "y", "animate", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "scale", "onSubmit", "fillRule", "clipRule", "htmlFor", "id", "name", "type", "required", "value", "onChange", "target", "placeholder", "onClick", "disabled", "xmlns", "cx", "cy", "r", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/ResetPasswordPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport SimpleHeader from '../components/SimpleHeader';\n\nconst ResetPasswordPage: React.FC = () => {\n  const { uid, token } = useParams<{ uid: string; token: string }>();\n  const navigate = useNavigate();\n  \n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isValidating, setIsValidating] = useState(true);\n  const [isValidLink, setIsValidLink] = useState(false);\n  const [userEmail, setUserEmail] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n\n  // Valider le lien au chargement de la page\n  useEffect(() => {\n    const validateLink = async () => {\n      if (!uid || !token) {\n        setError('Lien de réinitialisation invalide');\n        setIsValidating(false);\n        return;\n      }\n\n      try {\n        const response = await api.get(`/password-reset/validate/${uid}/${token}/`);\n        \n        if (response.data.valid) {\n          setIsValidLink(true);\n          setUserEmail(response.data.user_email);\n        } else {\n          setError(response.data.error || 'Lien de réinitialisation invalide');\n        }\n      } catch (err: any) {\n        console.error('Erreur lors de la validation du lien:', err);\n        setError('Lien de réinitialisation invalide ou expiré');\n      } finally {\n        setIsValidating(false);\n      }\n    };\n\n    validateLink();\n  }, [uid, token]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Validation côté client\n    if (!newPassword || !confirmPassword) {\n      setError('Veuillez remplir tous les champs');\n      return;\n    }\n\n    if (newPassword !== confirmPassword) {\n      setError('Les mots de passe ne correspondent pas');\n      return;\n    }\n\n    if (newPassword.length < 8) {\n      setError('Le mot de passe doit contenir au moins 8 caractères');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await api.post('/password-reset/confirm/', {\n        uid,\n        token,\n        new_password: newPassword\n      });\n      \n      if (response.data.success) {\n        setSuccess(true);\n        // Rediriger vers la page de connexion après 3 secondes\n        setTimeout(() => {\n          navigate('/login', {\n            state: {\n              message: 'Votre mot de passe a été réinitialisé avec succès. Veuillez vous connecter.'\n            }\n          });\n        }, 3000);\n      } else {\n        setError(response.data.error || 'Une erreur est survenue');\n      }\n    } catch (err: any) {\n      console.error('Erreur lors de la réinitialisation:', err);\n      \n      if (err.response?.data?.error) {\n        setError(err.response.data.error);\n      } else {\n        setError('Une erreur est survenue. Veuillez réessayer.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (isValidating) {\n    return (\n      <>\n        <SimpleHeader title=\"Réinitialisation du mot de passe\" />\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600 dark:text-gray-400\">Validation du lien...</p>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  if (!isValidLink) {\n    return (\n      <>\n        <SimpleHeader title=\"Lien invalide\" />\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            {/* Icône d'erreur */}\n            <div className=\"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20\">\n              <svg className=\"h-8 w-8 text-red-600 dark:text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                Lien invalide\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                {error}\n              </p>\n            </div>\n\n            <div className=\"space-y-3\">\n              <Link\n                to=\"/forgot-password\"\n                className=\"block w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors\"\n              >\n                Demander un nouveau lien\n              </Link>\n              <Link\n                to=\"/login\"\n                className=\"block w-full px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md transition-colors\"\n              >\n                Retour à la connexion\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </>\n    );\n  }\n\n  if (success) {\n    return (\n      <>\n        <SimpleHeader title=\"Mot de passe réinitialisé\" />\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg text-center\"\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5 }}\n          >\n            {/* Icône de succès */}\n            <div className=\"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20\">\n              <svg className=\"h-8 w-8 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </div>\n\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                Mot de passe réinitialisé !\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                Votre mot de passe a été réinitialisé avec succès.\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n                Redirection vers la page de connexion dans quelques secondes...\n              </p>\n            </div>\n\n            <Link\n              to=\"/login\"\n              className=\"inline-block px-6 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors\"\n            >\n              Se connecter maintenant\n            </Link>\n          </motion.div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <SimpleHeader title=\"Nouveau mot de passe\" />\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          {/* En-tête */}\n          <div>\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white\">\n              Nouveau mot de passe\n            </h2>\n            <p className=\"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\">\n              Pour le compte : {userEmail}\n            </p>\n          </div>\n\n          <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n            {/* Messages d'erreur */}\n            {error && (\n              <motion.div\n                className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-md\"\n                initial={{ opacity: 0, scale: 0.95 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-3\">\n                    <p className=\"text-sm\">{error}</p>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n\n            <div className=\"space-y-4\">\n              {/* Nouveau mot de passe */}\n              <div>\n                <label htmlFor=\"newPassword\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nouveau mot de passe\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"newPassword\"\n                    name=\"newPassword\"\n                    type={showPassword ? \"text\" : \"password\"}\n                    required\n                    value={newPassword}\n                    onChange={(e) => setNewPassword(e.target.value)}\n                    className=\"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 focus:z-10 sm:text-sm\"\n                    placeholder=\"Nouveau mot de passe (min. 8 caractères)\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                      </svg>\n                    ) : (\n                      <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                      </svg>\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              {/* Confirmer le mot de passe */}\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Confirmer le mot de passe\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? \"text\" : \"password\"}\n                    required\n                    value={confirmPassword}\n                    onChange={(e) => setConfirmPassword(e.target.value)}\n                    className=\"appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 focus:z-10 sm:text-sm\"\n                    placeholder=\"Confirmer le nouveau mot de passe\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                      </svg>\n                    ) : (\n                      <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                      </svg>\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Bouton de soumission */}\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Réinitialisation...\n                  </div>\n                ) : (\n                  'Réinitialiser le mot de passe'\n                )}\n              </button>\n            </div>\n          </form>\n        </motion.div>\n      </div>\n    </>\n  );\n};\n\nexport default ResetPasswordPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,GAAGb,SAAS,CAAiC,CAAC;EAClE,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACvB,GAAG,IAAI,CAACC,KAAK,EAAE;QAClBe,QAAQ,CAAC,mCAAmC,CAAC;QAC7CN,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,IAAI;QACF,MAAMc,QAAQ,GAAG,MAAMhC,GAAG,CAACiC,GAAG,CAAC,4BAA4BzB,GAAG,IAAIC,KAAK,GAAG,CAAC;QAE3E,IAAIuB,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAE;UACvBf,cAAc,CAAC,IAAI,CAAC;UACpBE,YAAY,CAACU,QAAQ,CAACE,IAAI,CAACE,UAAU,CAAC;QACxC,CAAC,MAAM;UACLZ,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACX,KAAK,IAAI,mCAAmC,CAAC;QACtE;MACF,CAAC,CAAC,OAAOc,GAAQ,EAAE;QACjBC,OAAO,CAACf,KAAK,CAAC,uCAAuC,EAAEc,GAAG,CAAC;QAC3Db,QAAQ,CAAC,6CAA6C,CAAC;MACzD,CAAC,SAAS;QACRN,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAEDa,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACvB,GAAG,EAAEC,KAAK,CAAC,CAAC;EAEhB,MAAM8B,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAAC9B,WAAW,IAAI,CAACE,eAAe,EAAE;MACpCW,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEA,IAAIb,WAAW,KAAKE,eAAe,EAAE;MACnCW,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;IAEA,IAAIb,WAAW,CAAC+B,MAAM,GAAG,CAAC,EAAE;MAC1BlB,QAAQ,CAAC,qDAAqD,CAAC;MAC/D;IACF;IAEAR,YAAY,CAAC,IAAI,CAAC;IAClBQ,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMhC,GAAG,CAAC2C,IAAI,CAAC,0BAA0B,EAAE;QAC1DnC,GAAG;QACHC,KAAK;QACLmC,YAAY,EAAEjC;MAChB,CAAC,CAAC;MAEF,IAAIqB,QAAQ,CAACE,IAAI,CAACT,OAAO,EAAE;QACzBC,UAAU,CAAC,IAAI,CAAC;QAChB;QACAmB,UAAU,CAAC,MAAM;UACfnC,QAAQ,CAAC,QAAQ,EAAE;YACjBoC,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLvB,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACX,KAAK,IAAI,yBAAyB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOc,GAAQ,EAAE;MAAA,IAAAW,aAAA,EAAAC,kBAAA;MACjBX,OAAO,CAACf,KAAK,CAAC,qCAAqC,EAAEc,GAAG,CAAC;MAEzD,KAAAW,aAAA,GAAIX,GAAG,CAACL,QAAQ,cAAAgB,aAAA,gBAAAC,kBAAA,GAAZD,aAAA,CAAcd,IAAI,cAAAe,kBAAA,eAAlBA,kBAAA,CAAoB1B,KAAK,EAAE;QAC7BC,QAAQ,CAACa,GAAG,CAACL,QAAQ,CAACE,IAAI,CAACX,KAAK,CAAC;MACnC,CAAC,MAAM;QACLC,QAAQ,CAAC,8CAA8C,CAAC;MAC1D;IACF,CAAC,SAAS;MACRR,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,IAAIC,YAAY,EAAE;IAChB,oBACEd,OAAA,CAAAE,SAAA;MAAA6C,QAAA,gBACE/C,OAAA,CAACF,YAAY;QAACkD,KAAK,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzDpD,OAAA;QAAKqD,SAAS,EAAC,2EAA2E;QAAAN,QAAA,eACxF/C,OAAA;UAAKqD,SAAS,EAAC,aAAa;UAAAN,QAAA,gBAC1B/C,OAAA;YAAKqD,SAAS,EAAC;UAA2E;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGpD,OAAA;YAAGqD,SAAS,EAAC,uCAAuC;YAAAN,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,IAAI,CAACpC,WAAW,EAAE;IAChB,oBACEhB,OAAA,CAAAE,SAAA;MAAA6C,QAAA,gBACE/C,OAAA,CAACF,YAAY;QAACkD,KAAK,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCpD,OAAA;QAAKqD,SAAS,EAAC,sGAAsG;QAAAN,QAAA,eACnH/C,OAAA,CAACJ,MAAM,CAAC0D,GAAG;UACTD,SAAS,EAAC,0FAA0F;UACpGE,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAb,QAAA,gBAG9B/C,OAAA;YAAKqD,SAAS,EAAC,+FAA+F;YAAAN,QAAA,eAC5G/C,OAAA;cAAKqD,SAAS,EAAC,wCAAwC;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAhB,QAAA,eAC3G/C,OAAA;gBAAMgE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAA2I;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAIqD,SAAS,EAAC,uDAAuD;cAAAN,QAAA,EAAC;YAEtE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpD,OAAA;cAAGqD,SAAS,EAAC,uCAAuC;cAAAN,QAAA,EACjD3B;YAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENpD,OAAA;YAAKqD,SAAS,EAAC,WAAW;YAAAN,QAAA,gBACxB/C,OAAA,CAACL,IAAI;cACHyE,EAAE,EAAC,kBAAkB;cACrBf,SAAS,EAAC,wHAAwH;cAAAN,QAAA,EACnI;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpD,OAAA,CAACL,IAAI;cACHyE,EAAE,EAAC,QAAQ;cACXf,SAAS,EAAC,mMAAmM;cAAAN,QAAA,EAC9M;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,IAAI9B,OAAO,EAAE;IACX,oBACEtB,OAAA,CAAAE,SAAA;MAAA6C,QAAA,gBACE/C,OAAA,CAACF,YAAY;QAACkD,KAAK,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDpD,OAAA;QAAKqD,SAAS,EAAC,sGAAsG;QAAAN,QAAA,eACnH/C,OAAA,CAACJ,MAAM,CAAC0D,GAAG;UACTD,SAAS,EAAC,0FAA0F;UACpGE,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEa,KAAK,EAAE;UAAK,CAAE;UACrCX,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEa,KAAK,EAAE;UAAE,CAAE;UAClCV,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAb,QAAA,gBAG9B/C,OAAA;YAAKqD,SAAS,EAAC,mGAAmG;YAAAN,QAAA,eAChH/C,OAAA;cAAKqD,SAAS,EAAC,4CAA4C;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAhB,QAAA,eAC/G/C,OAAA;gBAAMgE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAgB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAIqD,SAAS,EAAC,uDAAuD;cAAAN,QAAA,EAAC;YAEtE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpD,OAAA;cAAGqD,SAAS,EAAC,uCAAuC;cAAAN,QAAA,EAAC;YAErD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpD,OAAA;cAAGqD,SAAS,EAAC,0CAA0C;cAAAN,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENpD,OAAA,CAACL,IAAI;YACHyE,EAAE,EAAC,QAAQ;YACXf,SAAS,EAAC,wHAAwH;YAAAN,QAAA,EACnI;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,oBACEpD,OAAA,CAAAE,SAAA;IAAA6C,QAAA,gBACE/C,OAAA,CAACF,YAAY;MAACkD,KAAK,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7CpD,OAAA;MAAKqD,SAAS,EAAC,sGAAsG;MAAAN,QAAA,eACnH/C,OAAA,CAACJ,MAAM,CAAC0D,GAAG;QACTD,SAAS,EAAC,8EAA8E;QACxFE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAG9B/C,OAAA;UAAA+C,QAAA,gBACE/C,OAAA;YAAIqD,SAAS,EAAC,wEAAwE;YAAAN,QAAA,EAAC;UAEvF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpD,OAAA;YAAGqD,SAAS,EAAC,2DAA2D;YAAAN,QAAA,GAAC,mBACtD,EAAC7B,SAAS;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENpD,OAAA;UAAMqD,SAAS,EAAC,gBAAgB;UAACiB,QAAQ,EAAElC,YAAa;UAAAW,QAAA,GAErD3B,KAAK,iBACJpB,OAAA,CAACJ,MAAM,CAAC0D,GAAG;YACTD,SAAS,EAAC,4HAA4H;YACtIE,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAK,CAAE;YACrCX,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAE,CAAE;YAClCV,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,eAE9B/C,OAAA;cAAKqD,SAAS,EAAC,MAAM;cAAAN,QAAA,gBACnB/C,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAN,QAAA,eAC5B/C,OAAA;kBAAKqD,SAAS,EAAC,sBAAsB;kBAACU,OAAO,EAAC,WAAW;kBAACF,IAAI,EAAC,cAAc;kBAAAd,QAAA,eAC3E/C,OAAA;oBAAMuE,QAAQ,EAAC,SAAS;oBAACJ,CAAC,EAAC,yNAAyN;oBAACK,QAAQ,EAAC;kBAAS;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpD,OAAA;gBAAKqD,SAAS,EAAC,MAAM;gBAAAN,QAAA,eACnB/C,OAAA;kBAAGqD,SAAS,EAAC,SAAS;kBAAAN,QAAA,EAAE3B;gBAAK;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDpD,OAAA;YAAKqD,SAAS,EAAC,WAAW;YAAAN,QAAA,gBAExB/C,OAAA;cAAA+C,QAAA,gBACE/C,OAAA;gBAAOyE,OAAO,EAAC,aAAa;gBAACpB,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEzG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBAAKqD,SAAS,EAAC,UAAU;gBAAAN,QAAA,gBACvB/C,OAAA;kBACE0E,EAAE,EAAC,aAAa;kBAChBC,IAAI,EAAC,aAAa;kBAClBC,IAAI,EAAEpD,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCqD,QAAQ;kBACRC,KAAK,EAAEtE,WAAY;kBACnBuE,QAAQ,EAAG1C,CAAC,IAAK5B,cAAc,CAAC4B,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;kBAChDzB,SAAS,EAAC,qSAAqS;kBAC/S4B,WAAW,EAAC;gBAA0C;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFpD,OAAA;kBACE4E,IAAI,EAAC,QAAQ;kBACbvB,SAAS,EAAC,mDAAmD;kBAC7D6B,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,CAACD,YAAY,CAAE;kBAAAuB,QAAA,EAE7CvB,YAAY,gBACXxB,OAAA;oBAAKqD,SAAS,EAAC,uBAAuB;oBAACQ,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,eAC1F/C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAqL;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1P,CAAC,gBAENpD,OAAA;oBAAKqD,SAAS,EAAC,uBAAuB;oBAACQ,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,gBAC1F/C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAkC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1GpD,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAyH;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9L;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpD,OAAA;cAAA+C,QAAA,gBACE/C,OAAA;gBAAOyE,OAAO,EAAC,iBAAiB;gBAACpB,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAE7G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBAAKqD,SAAS,EAAC,UAAU;gBAAAN,QAAA,gBACvB/C,OAAA;kBACE0E,EAAE,EAAC,iBAAiB;kBACpBC,IAAI,EAAC,iBAAiB;kBACtBC,IAAI,EAAElD,mBAAmB,GAAG,MAAM,GAAG,UAAW;kBAChDmD,QAAQ;kBACRC,KAAK,EAAEpE,eAAgB;kBACvBqE,QAAQ,EAAG1C,CAAC,IAAK1B,kBAAkB,CAAC0B,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;kBACpDzB,SAAS,EAAC,qSAAqS;kBAC/S4B,WAAW,EAAC;gBAAmC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACFpD,OAAA;kBACE4E,IAAI,EAAC,QAAQ;kBACbvB,SAAS,EAAC,mDAAmD;kBAC7D6B,OAAO,EAAEA,CAAA,KAAMvD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;kBAAAqB,QAAA,EAE3DrB,mBAAmB,gBAClB1B,OAAA;oBAAKqD,SAAS,EAAC,uBAAuB;oBAACQ,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,eAC1F/C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAqL;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1P,CAAC,gBAENpD,OAAA;oBAAKqD,SAAS,EAAC,uBAAuB;oBAACQ,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,gBAC1F/C,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAkC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1GpD,OAAA;sBAAMgE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAyH;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9L;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpD,OAAA;YAAA+C,QAAA,eACE/C,OAAA;cACE4E,IAAI,EAAC,QAAQ;cACbO,QAAQ,EAAEvE,SAAU;cACpByC,SAAS,EAAC,0SAA0S;cAAAN,QAAA,EAEnTnC,SAAS,gBACRZ,OAAA;gBAAKqD,SAAS,EAAC,mBAAmB;gBAAAN,QAAA,gBAChC/C,OAAA;kBAAKqD,SAAS,EAAC,4CAA4C;kBAAC+B,KAAK,EAAC,4BAA4B;kBAACvB,IAAI,EAAC,MAAM;kBAACE,OAAO,EAAC,WAAW;kBAAAhB,QAAA,gBAC5H/C,OAAA;oBAAQqD,SAAS,EAAC,YAAY;oBAACgC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACzB,MAAM,EAAC,cAAc;oBAACI,WAAW,EAAC;kBAAG;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACrGpD,OAAA;oBAAMqD,SAAS,EAAC,YAAY;oBAACQ,IAAI,EAAC,cAAc;oBAACM,CAAC,EAAC;kBAAiH;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzK,CAAC,0BAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAChD,EAAA,CArVID,iBAA2B;EAAA,QACRV,SAAS,EACfC,WAAW;AAAA;AAAA8F,EAAA,GAFxBrF,iBAA2B;AAuVjC,eAAeA,iBAAiB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}