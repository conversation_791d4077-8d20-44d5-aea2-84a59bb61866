{"ast": null, "code": "export var nthRootsDocs = {\n  name: 'nthRoots',\n  category: 'Arithmetic',\n  syntax: ['nthRoots(A)', 'nthRoots(A, root)'],\n  description: '' + 'Calculate the nth roots of a value. ' + 'An nth root of a positive real number A, ' + 'is a positive real solution of the equation \"x^root = A\". ' + 'This function returns an array of complex values.',\n  examples: ['nthRoots(1)', 'nthRoots(1, 3)'],\n  seealso: ['sqrt', 'pow', 'nthRoot']\n};", "map": {"version": 3, "names": ["nthRootsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/nthRoots.js"], "sourcesContent": ["export var nthRootsDocs = {\n  name: 'nthRoots',\n  category: 'Arithmetic',\n  syntax: ['nthRoots(A)', 'nthRoots(A, root)'],\n  description: '' + 'Calculate the nth roots of a value. ' + 'An nth root of a positive real number A, ' + 'is a positive real solution of the equation \"x^root = A\". ' + 'This function returns an array of complex values.',\n  examples: ['nthRoots(1)', 'nthRoots(1, 3)'],\n  seealso: ['sqrt', 'pow', 'nthRoot']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC;EAC5CC,WAAW,EAAE,EAAE,GAAG,sCAAsC,GAAG,2CAA2C,GAAG,4DAA4D,GAAG,mDAAmD;EAC3NC,QAAQ,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;EAC3CC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}