{"ast": null, "code": "export var trueDocs = {\n  name: 'true',\n  category: 'Constants',\n  syntax: ['true'],\n  description: 'Boolean value true',\n  examples: ['true'],\n  seealso: ['false']\n};", "map": {"version": 3, "names": ["trueDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/true.js"], "sourcesContent": ["export var trueDocs = {\n  name: 'true',\n  category: 'Constants',\n  syntax: ['true'],\n  description: 'Boolean value true',\n  examples: ['true'],\n  seealso: ['false']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,MAAM,CAAC;EAChBC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC,MAAM,CAAC;EAClBC,OAAO,EAAE,CAAC,OAAO;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}