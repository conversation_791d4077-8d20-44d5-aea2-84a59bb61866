# Generated by Django 5.2.1 on 2025-06-04 00:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0005_remove_professor_features'),
    ]

    operations = [
        migrations.AddField(
            model_name='exercise',
            name='correct_answer',
            field=models.TextField(default='x = 0'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='exercise',
            name='exercise_type',
            field=models.CharField(choices=[('equation', "Résolution d'équation"), ('multiple_choice', 'Choix multiple'), ('calculation', 'Calcul'), ('proof', 'Démonstration')], default='equation', max_length=20),
        ),
        migrations.AddField(
            model_name='exercise',
            name='explanation',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='exercise',
            name='points',
            field=models.IntegerField(default=10),
        ),
        migrations.AddField(
            model_name='exercise',
            name='question',
            field=models.TextField(default='Résolvez cette équation'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='exercise',
            name='time_limit',
            field=models.IntegerField(default=300, help_text='Temps limite en secondes'),
        ),
        migrations.CreateModel(
            name='ExerciseChoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('choice_text', models.CharField(max_length=200)),
                ('is_correct', models.BooleanField(default=False)),
                ('order', models.IntegerField(default=0)),
                ('exercise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='choices', to='api.exercise')),
            ],
            options={
                'verbose_name': "Choix d'exercice",
                'verbose_name_plural': "Choix d'exercices",
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='ExerciseAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_answer', models.TextField(blank=True)),
                ('is_correct', models.BooleanField(default=False)),
                ('points_earned', models.IntegerField(default=0)),
                ('time_taken', models.IntegerField(default=0, help_text='Temps pris en secondes')),
                ('status', models.CharField(choices=[('in_progress', 'En cours'), ('completed', 'Terminé'), ('abandoned', 'Abandonné')], default='in_progress', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('exercise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='api.exercise')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exercise_attempts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': "Tentative d'exercice",
                'verbose_name_plural': "Tentatives d'exercices",
                'unique_together': {('user', 'exercise')},
            },
        ),
    ]
