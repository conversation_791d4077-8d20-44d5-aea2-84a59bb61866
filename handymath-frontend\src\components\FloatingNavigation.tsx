import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface FloatingNavItem {
  id: string;
  label: string;
  path: string;
  icon: string;
  shortcut?: string;
}

interface FloatingNavigationProps {
  items: FloatingNavItem[];
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  className?: string;
}

const FloatingNavigation: React.FC<FloatingNavigationProps> = ({
  items,
  position = 'bottom-right',
  className = ''
}) => {
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeItem, setActiveItem] = useState<string>('');

  useEffect(() => {
    // Déterminer l'élément actif basé sur l'URL actuelle
    const currentItem = items.find(item => 
      location.pathname === item.path || 
      (item.path !== '/' && location.pathname.startsWith(item.path))
    );
    setActiveItem(currentItem?.id || '');
  }, [location.pathname, items]);

  // Gestion des raccourcis clavier
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        const item = items.find(item => item.shortcut === event.key.toLowerCase());
        if (item) {
          event.preventDefault();
          window.location.href = item.path;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [items]);

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6';
      case 'bottom-center':
        return 'bottom-6 left-1/2 transform -translate-x-1/2';
      case 'bottom-right':
      default:
        return 'bottom-6 right-6';
    }
  };

  const getExpandDirection = () => {
    switch (position) {
      case 'bottom-left':
        return 'flex-col-reverse';
      case 'bottom-center':
        return 'flex-row-reverse';
      case 'bottom-right':
      default:
        return 'flex-col-reverse';
    }
  };

  return (
    <div className={`fixed z-50 ${getPositionClasses()} ${className}`}>
      <div className={`flex ${getExpandDirection()} items-center space-y-3`}>
        {/* Menu items */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              className={`flex ${getExpandDirection()} space-y-2`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              {items.map((item, index) => {
                const isActive = activeItem === item.id;
                
                return (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Link
                      to={item.path}
                      className={`group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${
                        isActive
                          ? 'bg-primary-600 text-white scale-110'
                          : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'
                      }`}
                      onClick={() => setIsExpanded(false)}
                      title={`${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`}
                    >
                      <span className="text-lg">{item.icon}</span>
                      
                      {/* Tooltip */}
                      <div className="absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                        {item.label}
                        {item.shortcut && (
                          <span className="ml-2 text-xs opacity-75">
                            Ctrl+{item.shortcut.toUpperCase()}
                          </span>
                        )}
                        <div className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700"></div>
                      </div>
                    </Link>
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Toggle button */}
        <motion.button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`flex items-center justify-center w-14 h-14 rounded-full shadow-lg transition-all duration-200 ${
            isExpanded
              ? 'bg-red-500 hover:bg-red-600 text-white rotate-45'
              : 'bg-primary-600 hover:bg-primary-700 text-white hover:scale-105'
          }`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          title={isExpanded ? 'Fermer le menu' : 'Ouvrir le menu de navigation'}
        >
          {isExpanded ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          )}
        </motion.button>
      </div>
    </div>
  );
};

export default FloatingNavigation;
