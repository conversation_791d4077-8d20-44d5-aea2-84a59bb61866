{"ast": null, "code": "export var isNaNDocs = {\n  name: 'isNaN',\n  category: 'Utils',\n  syntax: ['isNaN(x)'],\n  description: 'Test whether a value is NaN (not a number)',\n  examples: ['isNaN(2)', 'isNaN(0 / 0)', 'isNaN(NaN)', 'isNaN(Infinity)'],\n  seealso: ['isNegative', 'isNumeric', 'isPositive', 'isZero']\n};", "map": {"version": 3, "names": ["isNaNDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isNaN.js"], "sourcesContent": ["export var isNaNDocs = {\n  name: 'isNaN',\n  category: 'Utils',\n  syntax: ['isNaN(x)'],\n  description: 'Test whether a value is NaN (not a number)',\n  examples: ['isNaN(2)', 'isNaN(0 / 0)', 'isNaN(NaN)', 'isNaN(Infinity)'],\n  seealso: ['isNegative', 'isNumeric', 'isPositive', 'isZero']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,4CAA4C;EACzDC,QAAQ,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,CAAC;EACvEC,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ;AAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}