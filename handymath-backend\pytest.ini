[tool:pytest]
DJANGO_SETTINGS_MODULE = handymath_backend.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=api
    --cov=handymath_backend
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    math: Mathematical computation tests
    auth: Authentication tests
    slow: Slow running tests
testpaths = tests
