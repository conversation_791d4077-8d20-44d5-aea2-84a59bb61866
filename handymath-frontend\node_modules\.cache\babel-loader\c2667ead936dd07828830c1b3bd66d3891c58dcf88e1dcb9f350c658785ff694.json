{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createWeakMixingAngle } from '../../factoriesAny.js';\nexport var weakMixingAngleDependencies = {\n  BigNumberDependencies,\n  createWeakMixingAngle\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "createWeakMixingAngle", "weakMixingAngleDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesWeakMixingAngle.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createWeakMixingAngle } from '../../factoriesAny.js';\nexport var weakMixingAngleDependencies = {\n  BigNumberDependencies,\n  createWeakMixingAngle\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,IAAIC,2BAA2B,GAAG;EACvCF,qBAAqB;EACrBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}