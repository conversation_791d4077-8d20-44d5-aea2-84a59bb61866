#!/usr/bin/env python3
"""
Script pour déboguer les problèmes d'inscription et d'affichage de cours
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import User, Course, Chapter, Lesson, LessonProgress, CourseEnrollment

def debug_enrollment_progression():
    """Déboguer le problème de progression à 45% lors de l'inscription"""
    print("🔍 DÉBOGAGE: Problème de progression lors de l'inscription")
    print("=" * 60)
    
    # Créer un nouvel utilisateur pour simuler l'inscription
    test_user, created = User.objects.get_or_create(
        username='enrollment_debug_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Enrollment',
            'last_name': 'Debug'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Nouvel utilisateur créé: {test_user.username}")
    else:
        # Nettoyer les données existantes
        LessonProgress.objects.filter(user=test_user).delete()
        CourseEnrollment.objects.filter(user=test_user).delete()
        print(f"🧹 Données nettoyées pour: {test_user.username}")
    
    # Tester chaque cours publié
    for course in Course.objects.filter(status='published'):
        print(f"\n📚 Test du cours: {course.title}")
        
        # 1. Vérifier la progression AVANT inscription
        progress_before = course.get_progress_for_user(test_user)
        print(f"   📊 Progression avant inscription: {progress_before}%")
        
        # 2. Simuler l'inscription (comme dans l'API)
        enrollment, enrollment_created = CourseEnrollment.objects.get_or_create(
            user=test_user,
            course=course,
            defaults={'is_active': True}
        )
        
        print(f"   📝 Inscription {'créée' if enrollment_created else 'existante'}")
        
        # 3. Vérifier la progression APRÈS inscription
        progress_after = course.get_progress_for_user(test_user)
        print(f"   📊 Progression après inscription: {progress_after}%")
        
        # 4. Analyser les leçons et progressions
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        completed_lessons = LessonProgress.objects.filter(
            user=test_user,
            lesson__chapter__course=course,
            lesson__is_published=True,
            completed=True
        ).count()
        
        all_progressions = LessonProgress.objects.filter(
            user=test_user,
            lesson__chapter__course=course
        ).count()
        
        print(f"   📖 Leçons totales: {total_lessons}")
        print(f"   ✅ Leçons complétées: {completed_lessons}")
        print(f"   📋 Progressions créées: {all_progressions}")
        
        # 5. Vérifier s'il y a des progressions automatiques incorrectes
        if progress_after > 0 and completed_lessons == 0:
            print(f"   ⚠️ PROBLÈME DÉTECTÉ: Progression {progress_after}% sans leçons complétées!")
            
            # Analyser les progressions existantes
            suspicious_progressions = LessonProgress.objects.filter(
                user=test_user,
                lesson__chapter__course=course,
                completed=True
            )
            
            if suspicious_progressions.exists():
                print(f"   🔍 Progressions suspectes trouvées:")
                for prog in suspicious_progressions:
                    print(f"      - {prog.lesson.title}: completed={prog.completed}, time_spent={prog.time_spent}")
        
        # 6. Calculer la progression attendue
        expected_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
        print(f"   🧮 Progression attendue: {expected_progress}%")
        
        if abs(progress_after - expected_progress) > 0.1:
            print(f"   ⚠️ INCOHÉRENCE: Progression calculée ≠ progression attendue")

def debug_course_content_display():
    """Déboguer le problème d'affichage du contenu du premier cours"""
    print(f"\n\n🔍 DÉBOGAGE: Problème d'affichage de contenu de cours")
    print("=" * 60)
    
    # Analyser l'ordre des cours
    courses = Course.objects.filter(status='published').order_by('order', 'id')
    
    print(f"📚 Ordre des cours dans la base de données:")
    for i, course in enumerate(courses):
        print(f"   {i+1}. ID: {course.id}, Ordre: {course.order}, Titre: {course.title}")
    
    # Vérifier s'il y a des problèmes d'ordre
    if courses.exists():
        first_course = courses.first()
        print(f"\n🎯 Premier cours par défaut: {first_course.title} (ID: {first_course.id})")
        
        # Vérifier si tous les cours ont le même ordre
        orders = courses.values_list('order', flat=True).distinct()
        if len(orders) == 1:
            print(f"   ⚠️ PROBLÈME: Tous les cours ont le même ordre ({orders[0]})")
            print(f"   💡 Solution: Définir des ordres différents pour chaque cours")
        
        # Analyser les chapitres du premier cours
        print(f"\n📖 Chapitres du premier cours:")
        for chapter in first_course.chapters.filter(is_published=True).order_by('order'):
            lessons_count = chapter.lessons.filter(is_published=True).count()
            print(f"   - {chapter.title}: {lessons_count} leçons")

def check_api_course_selection():
    """Vérifier la logique de sélection de cours dans l'API"""
    print(f"\n\n🔍 DÉBOGAGE: Logique de sélection de cours dans l'API")
    print("=" * 60)
    
    # Simuler l'appel API get_course_detail pour différents cours
    courses = Course.objects.filter(status='published')
    
    for course in courses:
        print(f"\n📚 Test API pour le cours: {course.title} (ID: {course.id})")
        
        # Simuler la récupération du cours par ID
        try:
            retrieved_course = Course.objects.prefetch_related(
                'chapters__lessons', 'prerequisites'
            ).get(id=course.id, status='published')
            
            print(f"   ✅ Cours récupéré: {retrieved_course.title}")
            
            # Vérifier les chapitres
            chapters = retrieved_course.chapters.filter(is_published=True).order_by('order')
            print(f"   📖 Chapitres: {chapters.count()}")
            
            for chapter in chapters:
                lessons = chapter.lessons.filter(is_published=True).order_by('order')
                print(f"      - {chapter.title}: {lessons.count()} leçons")
                
        except Course.DoesNotExist:
            print(f"   ❌ Cours non trouvé avec l'ID {course.id}")

def simulate_enrollment_process():
    """Simuler le processus complet d'inscription"""
    print(f"\n\n🧪 SIMULATION: Processus complet d'inscription")
    print("=" * 60)
    
    # Créer un utilisateur de test
    test_user, created = User.objects.get_or_create(
        username='enrollment_simulation_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Enrollment',
            'last_name': 'Simulation'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
    else:
        # Nettoyer les données existantes
        LessonProgress.objects.filter(user=test_user).delete()
        CourseEnrollment.objects.filter(user=test_user).delete()
    
    print(f"👤 Utilisateur de test: {test_user.username}")
    
    # Sélectionner le premier cours
    course = Course.objects.filter(status='published').first()
    if not course:
        print("❌ Aucun cours publié trouvé")
        return
    
    print(f"📚 Cours sélectionné: {course.title}")
    
    # Étape 1: Progression avant inscription
    print(f"\n1️⃣ AVANT inscription:")
    progress_before = course.get_progress_for_user(test_user)
    print(f"   📊 Progression: {progress_before}%")
    
    # Étape 2: Inscription
    print(f"\n2️⃣ INSCRIPTION:")
    enrollment, enrollment_created = CourseEnrollment.objects.get_or_create(
        user=test_user,
        course=course,
        defaults={'is_active': True}
    )
    print(f"   📝 Inscription créée: {enrollment_created}")
    
    # Étape 3: Progression après inscription
    print(f"\n3️⃣ APRÈS inscription:")
    progress_after = course.get_progress_for_user(test_user)
    print(f"   📊 Progression: {progress_after}%")
    
    # Étape 4: Accès au contenu du cours
    print(f"\n4️⃣ ACCÈS au contenu:")
    chapters = course.chapters.filter(is_published=True).order_by('order')
    
    for chapter in chapters:
        print(f"   📖 Chapitre: {chapter.title}")
        lessons = chapter.lessons.filter(is_published=True).order_by('order')
        
        for lesson in lessons:
            # Simuler l'accès à la leçon (comme dans l'API get_course_detail)
            lesson_progress = LessonProgress.objects.filter(
                user=test_user, lesson=lesson
            ).first()
            
            # Créer la progression si elle n'existe pas (comme dans l'API)
            if not lesson_progress:
                lesson_progress, created = LessonProgress.objects.get_or_create(
                    user=test_user,
                    lesson=lesson,
                    defaults={'completed': False, 'time_spent': 0}
                )
                print(f"      📋 Progression créée pour: {lesson.title}")
    
    # Étape 5: Progression finale
    print(f"\n5️⃣ PROGRESSION finale:")
    final_progress = course.get_progress_for_user(test_user)
    print(f"   📊 Progression: {final_progress}%")
    
    if final_progress != progress_before:
        print(f"   ⚠️ CHANGEMENT DE PROGRESSION DÉTECTÉ!")
        print(f"   📈 {progress_before}% → {final_progress}%")

def main():
    """Fonction principale"""
    try:
        # 1. Déboguer le problème de progression à l'inscription
        debug_enrollment_progression()
        
        # 2. Déboguer le problème d'affichage de contenu
        debug_course_content_display()
        
        # 3. Vérifier la logique API
        check_api_course_selection()
        
        # 4. Simuler le processus complet
        simulate_enrollment_process()
        
        print(f"\n\n📋 RÉSUMÉ DES PROBLÈMES POTENTIELS")
        print("=" * 60)
        print("1. Vérifiez si des progressions sont créées automatiquement lors de l'inscription")
        print("2. Vérifiez l'ordre des cours dans la base de données")
        print("3. Vérifiez la logique de sélection de cours dans le frontend")
        print("4. Vérifiez si l'API retourne le bon cours selon l'ID demandé")
        
    except Exception as e:
        print(f"❌ Erreur lors du débogage: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
