{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createResultSet } from '../../factoriesAny.js';\nexport var ResultSetDependencies = {\n  createResultSet\n};", "map": {"version": 3, "names": ["createResultSet", "ResultSetDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesResultSet.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createResultSet } from '../../factoriesAny.js';\nexport var ResultSetDependencies = {\n  createResultSet\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,qBAAqB,GAAG;EACjCD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}