{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { subtractNumber } from '../../plain/number/index.js';\nvar name = 'subtractScalar';\nvar dependencies = ['typed'];\nexport var createSubtractScalar = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Subtract two scalar values, `x - y`.\n   * This function is meant for internal use: it is used by the public function\n   * `subtract`\n   *\n   * This function does not support collections (Array or Matrix).\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit} x   First value\n   * @param  {number | BigNumber | bigint | Fraction | Complex} y          Second value to be subtracted from `x`\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit}     Difference of `x` and `y`\n   * @private\n   */\n  return typed(name, {\n    'number, number': subtractNumber,\n    'Complex, Complex': function Complex_Complex(x, y) {\n      return x.sub(y);\n    },\n    'BigNumber, BigNumber': function BigNumber_BigNumber(x, y) {\n      return x.minus(y);\n    },\n    'bigint, bigint': function bigint_bigint(x, y) {\n      return x - y;\n    },\n    'Fraction, Fraction': function Fraction_Fraction(x, y) {\n      return x.sub(y);\n    },\n    'Unit, Unit': typed.referToSelf(self => (x, y) => {\n      if (x.value === null || x.value === undefined) {\n        throw new Error('Parameter x contains a unit with undefined value');\n      }\n      if (y.value === null || y.value === undefined) {\n        throw new Error('Parameter y contains a unit with undefined value');\n      }\n      if (!x.equalBase(y)) throw new Error('Units do not match');\n      var res = x.clone();\n      res.value = typed.find(self, [res.valueType(), y.valueType()])(res.value, y.value);\n      res.fixPrefix = false;\n      return res;\n    })\n  });\n});", "map": {"version": 3, "names": ["factory", "subtractNumber", "name", "dependencies", "createSubtractScalar", "_ref", "typed", "Complex_Complex", "x", "y", "sub", "BigNumber_BigNumber", "minus", "bigint_bigint", "Fraction_Fraction", "referToSelf", "self", "value", "undefined", "Error", "equalBase", "res", "clone", "find", "valueType", "fixPrefix"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/arithmetic/subtractScalar.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { subtractNumber } from '../../plain/number/index.js';\nvar name = 'subtractScalar';\nvar dependencies = ['typed'];\nexport var createSubtractScalar = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Subtract two scalar values, `x - y`.\n   * This function is meant for internal use: it is used by the public function\n   * `subtract`\n   *\n   * This function does not support collections (Array or Matrix).\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit} x   First value\n   * @param  {number | BigNumber | bigint | Fraction | Complex} y          Second value to be subtracted from `x`\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit}     Difference of `x` and `y`\n   * @private\n   */\n  return typed(name, {\n    'number, number': subtractNumber,\n    'Complex, Complex': function Complex_Complex(x, y) {\n      return x.sub(y);\n    },\n    'BigNumber, BigNumber': function BigNumber_BigNumber(x, y) {\n      return x.minus(y);\n    },\n    'bigint, bigint': function bigint_bigint(x, y) {\n      return x - y;\n    },\n    'Fraction, Fraction': function Fraction_Fraction(x, y) {\n      return x.sub(y);\n    },\n    'Unit, Unit': typed.referToSelf(self => (x, y) => {\n      if (x.value === null || x.value === undefined) {\n        throw new Error('Parameter x contains a unit with undefined value');\n      }\n      if (y.value === null || y.value === undefined) {\n        throw new Error('Parameter y contains a unit with undefined value');\n      }\n      if (!x.equalBase(y)) throw new Error('Units do not match');\n      var res = x.clone();\n      res.value = typed.find(self, [res.valueType(), y.valueType()])(res.value, y.value);\n      res.fixPrefix = false;\n      return res;\n    })\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,oBAAoB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,gBAAgB,EAAED,cAAc;IAChC,kBAAkB,EAAE,SAASM,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACjD,OAAOD,CAAC,CAACE,GAAG,CAACD,CAAC,CAAC;IACjB,CAAC;IACD,sBAAsB,EAAE,SAASE,mBAAmBA,CAACH,CAAC,EAAEC,CAAC,EAAE;MACzD,OAAOD,CAAC,CAACI,KAAK,CAACH,CAAC,CAAC;IACnB,CAAC;IACD,gBAAgB,EAAE,SAASI,aAAaA,CAACL,CAAC,EAAEC,CAAC,EAAE;MAC7C,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC;IACD,oBAAoB,EAAE,SAASK,iBAAiBA,CAACN,CAAC,EAAEC,CAAC,EAAE;MACrD,OAAOD,CAAC,CAACE,GAAG,CAACD,CAAC,CAAC;IACjB,CAAC;IACD,YAAY,EAAEH,KAAK,CAACS,WAAW,CAACC,IAAI,IAAI,CAACR,CAAC,EAAEC,CAAC,KAAK;MAChD,IAAID,CAAC,CAACS,KAAK,KAAK,IAAI,IAAIT,CAAC,CAACS,KAAK,KAAKC,SAAS,EAAE;QAC7C,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;MACrE;MACA,IAAIV,CAAC,CAACQ,KAAK,KAAK,IAAI,IAAIR,CAAC,CAACQ,KAAK,KAAKC,SAAS,EAAE;QAC7C,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;MACrE;MACA,IAAI,CAACX,CAAC,CAACY,SAAS,CAACX,CAAC,CAAC,EAAE,MAAM,IAAIU,KAAK,CAAC,oBAAoB,CAAC;MAC1D,IAAIE,GAAG,GAAGb,CAAC,CAACc,KAAK,CAAC,CAAC;MACnBD,GAAG,CAACJ,KAAK,GAAGX,KAAK,CAACiB,IAAI,CAACP,IAAI,EAAE,CAACK,GAAG,CAACG,SAAS,CAAC,CAAC,EAAEf,CAAC,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAACH,GAAG,CAACJ,KAAK,EAAER,CAAC,CAACQ,KAAK,CAAC;MAClFI,GAAG,CAACI,SAAS,GAAG,KAAK;MACrB,OAAOJ,GAAG;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}