{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createSecondRadiation } from '../../factoriesAny.js';\nexport var secondRadiationDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createSecondRadiation\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createSecondRadiation", "secondRadiationDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSecondRadiation.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createSecondRadiation } from '../../factoriesAny.js';\nexport var secondRadiationDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createSecondRadiation\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,IAAIC,2BAA2B,GAAG;EACvCH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}