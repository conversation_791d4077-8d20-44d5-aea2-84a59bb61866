import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../components/NotificationSystem';
import StudentLayout from '../../components/StudentLayout';
import { RefreshIcon } from '../../components/icons/NavigationIcons';
import api from '../../services/api';

// Types
interface StudentStats {
  general_stats: {
    total_attempts: number;
    completed_attempts: number;
    correct_attempts: number;
    success_rate: number;
    total_points: number;
    average_time: number;
  };
  recent_exercises: Array<{
    id: number;
    title: string;
    difficulty: string;
    is_correct: boolean;
    points_earned: number;
    completed_at: string;
  }>;
  difficulty_stats: {
    [key: string]: {
      total_attempts: number;
      correct_attempts: number;
      success_rate: number;
      points_earned: number;
    };
  };
}

interface Course {
  id: number;
  title: string;
  description: string;
  level: string;
  lessons_count: number;
}

interface Exercise {
  id: number;
  title: string;
  difficulty: string;
  exercise_type: string;
  points: number;
}

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // State
  const [studentStats, setStudentStats] = useState<StudentStats | null>(null);
  const [recentCourses, setRecentCourses] = useState<Course[]>([]);
  const [recommendedExercises, setRecommendedExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper functions for notifications
  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };

  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message });
  };

  // Effects
  useEffect(() => {
    if (user) {
      fetchStudentData();
    }
  }, [user]);

  // API Functions
  const fetchStudentData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Récupérer les statistiques de l'étudiant
      const [statsResponse, coursesResponse, exercisesResponse] = await Promise.all([
        api.get('/progress/'),
        api.get('/courses/'),
        api.get('/exercises/')
      ]);

      setStudentStats(statsResponse.data);

      // Traiter les cours (les ViewSets retournent directement un array)
      const coursesData = Array.isArray(coursesResponse.data) ? coursesResponse.data : coursesResponse.data.results || [];
      setRecentCourses(coursesData.slice(0, 3)); // 3 premiers cours

      // Traiter les exercices
      const exercisesData = Array.isArray(exercisesResponse.data) ? exercisesResponse.data : exercisesResponse.data.results || [];
      setRecommendedExercises(exercisesData.slice(0, 4)); // 4 premiers exercices

    } catch (err: any) {
      console.error('Erreur lors du chargement des données étudiant:', err);
      const errorMessage = err.response?.data?.error || err.response?.data?.detail || 'Erreur lors du chargement des données';
      setError(errorMessage);
      showError('Erreur', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Quick actions
  const quickActions = [
    {
      title: 'Résoudre une équation',
      description: 'Utilisez notre solveur d\'équations',
      link: '/solver',
      icon: '🧮',
      color: 'bg-blue-500'
    },
    {
      title: 'Faire un exercice',
      description: 'Pratiquez avec nos exercices',
      link: '/exercises',
      icon: '📝',
      color: 'bg-green-500'
    },
    {
      title: 'Voir mes cours',
      description: 'Accédez à vos cours',
      link: '/courses',
      icon: '📚',
      color: 'bg-purple-500'
    },
    {
      title: 'Ma progression',
      description: 'Consultez vos statistiques',
      link: '/progress',
      icon: '📊',
      color: 'bg-orange-500'
    }
  ];

  // Calculer les statistiques rapides
  const quickStats = studentStats ? [
    {
      label: 'Points totaux',
      value: studentStats.general_stats?.total_points?.toString() || '0',
      color: 'text-blue-600',
      icon: '🏆'
    },
    {
      label: 'Taux de réussite',
      value: `${Math.round(studentStats.general_stats?.success_rate || 0)}%`,
      color: 'text-green-600',
      icon: '✅'
    },
    {
      label: 'Exercices complétés',
      value: studentStats.general_stats?.completed_attempts?.toString() || '0',
      color: 'text-purple-600',
      icon: '📋'
    },
    {
      label: 'Temps moyen',
      value: `${Math.round(studentStats.general_stats?.average_time || 0)}s`,
      color: 'text-orange-600',
      icon: '⏱️'
    }
  ] : [
    // Données par défaut si pas de statistiques
    {
      label: 'Points totaux',
      value: '0',
      color: 'text-blue-600',
      icon: '🏆'
    },
    {
      label: 'Taux de réussite',
      value: '0%',
      color: 'text-green-600',
      icon: '✅'
    },
    {
      label: 'Exercices complétés',
      value: '0',
      color: 'text-purple-600',
      icon: '📋'
    },
    {
      label: 'Temps moyen',
      value: '0s',
      color: 'text-orange-600',
      icon: '⏱️'
    }
  ];

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement de votre tableau de bord...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Erreur de chargement</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <button
            onClick={fetchStudentData}
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  const refreshAction = (
    <button
      onClick={fetchStudentData}
      className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2"
    >
      <RefreshIcon size="sm" />
      <span>Actualiser</span>
    </button>
  );

  return (
    <StudentLayout
      title="Tableau de bord étudiant"
      subtitle={`Bienvenue, ${user?.username} • Votre progression HandyMath`}
      actions={refreshAction}
    >

      {/* Statistiques rapides */}
      {quickStats.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {stat.label}
                  </p>
                  <p className={`text-2xl font-bold ${stat.color}`}>
                    {stat.value}
                  </p>
                </div>
                <div className="text-3xl">
                  {stat.icon}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Actions rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 group transform hover:scale-105"
          >
            <div className="flex items-center space-x-4">
              <div className={`p-3 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform duration-300`}>
                <span className="text-2xl">{action.icon}</span>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 group-hover:text-primary-600 transition-colors">
                  {action.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {action.description}
                </p>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Cours et exercices récents */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Cours récents */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              📚 Cours disponibles
            </h3>
            <Link
              to="/courses"
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              Voir tout →
            </Link>
          </div>
          <div className="space-y-3">
            {recentCourses.length > 0 ? (
              recentCourses.map((course) => (
                <Link
                  key={course.id}
                  to={`/courses/${course.id}`}
                  className="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {course.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {course.lessons_count} leçons • Niveau {course.level}
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      course.level === 'beginner' ? 'bg-green-100 text-green-800' :
                      course.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {course.level === 'beginner' ? 'Débutant' :
                       course.level === 'intermediate' ? 'Intermédiaire' : 'Avancé'}
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">📚</div>
                <p className="text-gray-600 dark:text-gray-400">Aucun cours disponible</p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Exercices recommandés */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              🎯 Exercices recommandés
            </h3>
            <Link
              to="/exercises"
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              Voir tout →
            </Link>
          </div>
          <div className="space-y-3">
            {recommendedExercises.length > 0 ? (
              recommendedExercises.map((exercise) => (
                <Link
                  key={exercise.id}
                  to={`/exercises/${exercise.id}`}
                  className="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {exercise.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {exercise.exercise_type} • {exercise.points} points
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      exercise.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                      exercise.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {exercise.difficulty === 'easy' ? 'Facile' :
                       exercise.difficulty === 'medium' ? 'Moyen' : 'Difficile'}
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">🎯</div>
                <p className="text-gray-600 dark:text-gray-400">Aucun exercice disponible</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Exercices récents */}
      {studentStats && studentStats.recent_exercises && studentStats.recent_exercises.length > 0 && (
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            🕒 Exercices récents
          </h3>
          <div className="space-y-3">
            {studentStats.recent_exercises.slice(0, 5).map((exercise, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    exercise.is_correct ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {exercise.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(exercise.completed_at).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${
                    exercise.is_correct ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {exercise.is_correct ? '✅ Réussi' : '❌ Échoué'}
                  </div>
                  <div className="text-xs text-gray-500">
                    +{exercise.points_earned} points
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </StudentLayout>
  );
};

export default StudentDashboard;