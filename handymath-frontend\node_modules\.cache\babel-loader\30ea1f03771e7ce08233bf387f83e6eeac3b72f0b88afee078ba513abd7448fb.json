{"ast": null, "code": "export var smallerDocs = {\n  name: 'smaller',\n  category: 'Relational',\n  syntax: ['x < y', 'smaller(x, y)'],\n  description: 'Check if value x is smaller than value y. Returns true if x is smaller than y, and false if not. Comparing a value with NaN returns false.',\n  examples: ['2 < 3', '5 < 2*2', 'a = 3.3', 'b = 6-2.8', '(a < b)', '5 cm < 2 inch'],\n  seealso: ['equal', 'unequal', 'larger', 'smallerEq', 'largerEq', 'compare']\n};", "map": {"version": 3, "names": ["smallerDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/smaller.js"], "sourcesContent": ["export var smallerDocs = {\n  name: 'smaller',\n  category: 'Relational',\n  syntax: ['x < y', 'smaller(x, y)'],\n  description: 'Check if value x is smaller than value y. Returns true if x is smaller than y, and false if not. Comparing a value with NaN returns false.',\n  examples: ['2 < 3', '5 < 2*2', 'a = 3.3', 'b = 6-2.8', '(a < b)', '5 cm < 2 inch'],\n  seealso: ['equal', 'unequal', 'larger', 'smallerEq', 'largerEq', 'compare']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;EAClCC,WAAW,EAAE,4IAA4I;EACzJC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,CAAC;EAClFC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}