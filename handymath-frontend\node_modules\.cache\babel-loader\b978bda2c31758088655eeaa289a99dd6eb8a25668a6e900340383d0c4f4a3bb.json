{"ast": null, "code": "export var cumSumDocs = {\n  name: 'cumsum',\n  category: 'Statistics',\n  syntax: ['cumsum(a, b, c, ...)', 'cumsum(A)'],\n  description: 'Compute the cumulative sum of all values.',\n  examples: ['cumsum(2, 3, 4, 1)', 'cumsum([2, 3, 4, 1])', 'cumsum([1, 2; 3, 4])', 'cumsum([1, 2; 3, 4], 1)', 'cumsum([1, 2; 3, 4], 2)'],\n  seealso: ['max', 'mean', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["cumSumDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/cumsum.js"], "sourcesContent": ["export var cumSumDocs = {\n  name: 'cumsum',\n  category: 'Statistics',\n  syntax: ['cumsum(a, b, c, ...)', 'cumsum(A)'],\n  description: 'Compute the cumulative sum of all values.',\n  examples: ['cumsum(2, 3, 4, 1)', 'cumsum([2, 3, 4, 1])', 'cumsum([1, 2; 3, 4])', 'cumsum([1, 2; 3, 4], 1)', 'cumsum([1, 2; 3, 4], 2)'],\n  seealso: ['max', 'mean', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,sBAAsB,EAAE,WAAW,CAAC;EAC7CC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;EACtIC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}