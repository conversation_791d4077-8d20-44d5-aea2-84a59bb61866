{"ast": null, "code": "import { isNumber, isBigNumber } from '../../../utils/is.js';\n/**\n * Change last argument dim from one-based to zero-based.\n */\nexport function dimToZeroBase(dim) {\n  if (isNumber(dim)) {\n    return dim - 1;\n  } else if (isBigNumber(dim)) {\n    return dim.minus(1);\n  } else {\n    return dim;\n  }\n}\nexport function isNumberOrBigNumber(n) {\n  return isNumber(n) || isBigNumber(n);\n}", "map": {"version": 3, "names": ["isNumber", "isBigNumber", "dimToZeroBase", "dim", "minus", "isNumberOrBigNumber", "n"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/transform/utils/dimToZeroBase.js"], "sourcesContent": ["import { isNumber, isBigNumber } from '../../../utils/is.js';\n/**\n * Change last argument dim from one-based to zero-based.\n */\nexport function dimToZeroBase(dim) {\n  if (isNumber(dim)) {\n    return dim - 1;\n  } else if (isBigNumber(dim)) {\n    return dim.minus(1);\n  } else {\n    return dim;\n  }\n}\nexport function isNumberOrBigNumber(n) {\n  return isNumber(n) || isBigNumber(n);\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,sBAAsB;AAC5D;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAIH,QAAQ,CAACG,GAAG,CAAC,EAAE;IACjB,OAAOA,GAAG,GAAG,CAAC;EAChB,CAAC,MAAM,IAAIF,WAAW,CAACE,GAAG,CAAC,EAAE;IAC3B,OAAOA,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC;EACrB,CAAC,MAAM;IACL,OAAOD,GAAG;EACZ;AACF;AACA,OAAO,SAASE,mBAAmBA,CAACC,CAAC,EAAE;EACrC,OAAON,QAAQ,CAACM,CAAC,CAAC,IAAIL,WAAW,CAACK,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}