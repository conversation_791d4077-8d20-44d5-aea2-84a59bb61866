{"ast": null, "code": "export var reDocs = {\n  name: 're',\n  category: 'Complex',\n  syntax: ['re(x)'],\n  description: 'Get the real part of a complex number.',\n  examples: ['re(2 + 3i)', 'im(2 + 3i)', 're(-5.2i)', 're(2.4)'],\n  seealso: ['im', 'conj', 'abs', 'arg']\n};", "map": {"version": 3, "names": ["reDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/complex/re.js"], "sourcesContent": ["export var reDocs = {\n  name: 're',\n  category: 'Complex',\n  syntax: ['re(x)'],\n  description: 'Get the real part of a complex number.',\n  examples: ['re(2 + 3i)', 'im(2 + 3i)', 're(-5.2i)', 're(2.4)'],\n  seealso: ['im', 'conj', 'abs', 'arg']\n};"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,WAAW,EAAE,wCAAwC;EACrDC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC;EAC9DC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}