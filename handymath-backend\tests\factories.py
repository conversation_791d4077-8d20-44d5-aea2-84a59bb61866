"""
Factories pour générer des données de test avec Factory Boy
"""
import factory
from django.contrib.auth.models import User
from faker import Faker

fake = Faker('fr_FR')  # Utilisation du français


class UserFactory(factory.django.DjangoModelFactory):
    """Factory pour créer des utilisateurs de test"""
    
    class Meta:
        model = User
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    first_name = factory.LazyFunction(lambda: fake.first_name())
    last_name = factory.LazyFunction(lambda: fake.last_name())
    is_active = True
    is_staff = False
    is_superuser = False


class AdminUserFactory(UserFactory):
    """Factory pour créer des administrateurs de test"""
    
    username = factory.Sequence(lambda n: f"admin{n}")
    is_staff = True
    is_superuser = True


class StudentUserFactory(UserFactory):
    """Factory pour créer des étudiants de test"""
    
    username = factory.Sequence(lambda n: f"student{n}")
    is_staff = False
    is_superuser = False


# Factory pour les équations (si vous avez un modèle Equation)
class EquationFactory(factory.django.DjangoModelFactory):
    """Factory pour créer des équations de test"""
    
    class Meta:
        model = 'api.Equation'  # Remplacez par votre modèle réel
        django_get_or_create = ('equation_text',)
    
    equation_text = factory.Iterator([
        "2x + 3 = 11",
        "x^2 - 4 = 0", 
        "3x - 7 = 14",
        "x^2 + 5x + 6 = 0",
        "2x^2 - 8x + 6 = 0",
        "x + 5 = 12",
        "4x - 3 = 17",
        "x^2 = 16"
    ])
    solution = factory.LazyAttribute(lambda obj: f"Solution pour {obj.equation_text}")
    steps = factory.LazyAttribute(lambda obj: f"Étapes pour résoudre {obj.equation_text}")
    created_by = factory.SubFactory(UserFactory)


# Factory pour les profils utilisateur (si vous en avez)
class UserProfileFactory(factory.django.DjangoModelFactory):
    """Factory pour créer des profils utilisateur de test"""
    
    class Meta:
        model = 'api.UserProfile'  # Remplacez par votre modèle réel
    
    user = factory.SubFactory(UserFactory)
    bio = factory.LazyFunction(lambda: fake.text(max_nb_chars=200))
    niveau = factory.Iterator(['debutant', 'intermediaire', 'avance'])
    points = factory.LazyFunction(lambda: fake.random_int(min=0, max=1000))


# Factory pour les exercices (si vous en avez)
class ExerciseFactory(factory.django.DjangoModelFactory):
    """Factory pour créer des exercices de test"""
    
    class Meta:
        model = 'api.Exercise'  # Remplacez par votre modèle réel
    
    title = factory.LazyFunction(lambda: fake.sentence(nb_words=4))
    description = factory.LazyFunction(lambda: fake.text(max_nb_chars=300))
    difficulty = factory.Iterator(['facile', 'moyen', 'difficile'])
    points = factory.LazyFunction(lambda: fake.random_int(min=5, max=50))
    created_by = factory.SubFactory(AdminUserFactory)


# Factory pour les cours (si vous en avez)
class CourseFactory(factory.django.DjangoModelFactory):
    """Factory pour créer des cours de test"""
    
    class Meta:
        model = 'api.Course'  # Remplacez par votre modèle réel
    
    title = factory.LazyFunction(lambda: f"Cours de {fake.word()}")
    description = factory.LazyFunction(lambda: fake.text(max_nb_chars=500))
    level = factory.Iterator(['debutant', 'intermediaire', 'avance'])
    created_by = factory.SubFactory(AdminUserFactory)


# Traits pour des variations
class Traits:
    """Traits pour modifier les factories"""
    
    @factory.trait
    def with_profile(self):
        profile = factory.RelatedFactory(UserProfileFactory, 'user')
    
    @factory.trait
    def active(self):
        is_active = True
    
    @factory.trait
    def inactive(self):
        is_active = False
    
    @factory.trait
    def with_equations(self):
        equations = factory.RelatedFactoryBoy(EquationFactory, 'created_by', size=3)


# Fonctions utilitaires pour les tests
def create_test_user(username="testuser", email="<EMAIL>", **kwargs):
    """Créer un utilisateur de test simple"""
    return UserFactory(username=username, email=email, **kwargs)


def create_admin_user(username="admin", **kwargs):
    """Créer un administrateur de test"""
    return AdminUserFactory(username=username, **kwargs)


def create_student_user(username="student", **kwargs):
    """Créer un étudiant de test"""
    return StudentUserFactory(username=username, **kwargs)


def create_test_equation(equation_text="x + 1 = 2", **kwargs):
    """Créer une équation de test"""
    return EquationFactory(equation_text=equation_text, **kwargs)


# Batch creation helpers
def create_multiple_users(count=5, **kwargs):
    """Créer plusieurs utilisateurs de test"""
    return UserFactory.create_batch(count, **kwargs)


def create_multiple_equations(count=10, **kwargs):
    """Créer plusieurs équations de test"""
    return EquationFactory.create_batch(count, **kwargs)


def create_test_dataset():
    """Créer un jeu de données complet pour les tests"""
    # Créer des utilisateurs
    admin = create_admin_user()
    students = create_multiple_users(5)
    
    # Créer des équations
    equations = create_multiple_equations(10, created_by=admin)
    
    # Créer des cours et exercices
    courses = CourseFactory.create_batch(3, created_by=admin)
    exercises = ExerciseFactory.create_batch(8, created_by=admin)
    
    return {
        'admin': admin,
        'students': students,
        'equations': equations,
        'courses': courses,
        'exercises': exercises
    }
