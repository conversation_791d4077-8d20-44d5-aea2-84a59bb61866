{"ast": null, "code": "import { isBig<PERSON><PERSON>ber, isComplex, isFraction, isMatrix, isObject, isUnit } from '../../utils/is.js';\nimport { isFactory, stripOptionalNotation } from '../../utils/factory.js';\nimport { hasOwnProperty, lazy } from '../../utils/object.js';\nimport { ArgumentsError } from '../../error/ArgumentsError.js';\nexport function importFactory(typed, load, math, importedFactories) {\n  /**\n   * Import functions from an object or a module.\n   *\n   * This function is only available on a mathjs instance created using `create`.\n   *\n   * Syntax:\n   *\n   *    math.import(functions)\n   *    math.import(functions, options)\n   *\n   * Where:\n   *\n   * - `functions: Object`\n   *   An object with functions or factories to be imported.\n   * - `options: Object` An object with import options. Available options:\n   *   - `override: boolean`\n   *     If true, existing functions will be overwritten. False by default.\n   *   - `silent: boolean`\n   *     If true, the function will not throw errors on duplicates or invalid\n   *     types. False by default.\n   *   - `wrap: boolean`\n   *     If true, the functions will be wrapped in a wrapper function\n   *     which converts data types like Matrix to primitive data types like Array.\n   *     The wrapper is needed when extending math.js with libraries which do not\n   *     support these data type. False by default.\n   *\n   * Examples:\n   *\n   *    import { create, all } from 'mathjs'\n   *    import * as numbers from 'numbers'\n   *\n   *    // create a mathjs instance\n   *    const math = create(all)\n   *\n   *    // define new functions and variables\n   *    math.import({\n   *      myvalue: 42,\n   *      hello: function (name) {\n   *        return 'hello, ' + name + '!'\n   *      }\n   *    })\n   *\n   *    // use the imported function and variable\n   *    math.myvalue * 2               // 84\n   *    math.hello('user')             // 'hello, user!'\n   *\n   *    // import the npm module 'numbers'\n   *    // (must be installed first with `npm install numbers`)\n   *    math.import(numbers, {wrap: true})\n   *\n   *    math.fibonacci(7) // returns 13\n   *\n   * @param {Object | Array} functions  Object with functions to be imported.\n   * @param {Object} [options]          Import options.\n   */\n  function mathImport(functions, options) {\n    var num = arguments.length;\n    if (num !== 1 && num !== 2) {\n      throw new ArgumentsError('import', num, 1, 2);\n    }\n    if (!options) {\n      options = {};\n    }\n    function flattenImports(flatValues, value, name) {\n      if (Array.isArray(value)) {\n        value.forEach(item => flattenImports(flatValues, item));\n      } else if (isObject(value) || isModule(value)) {\n        for (var _name in value) {\n          if (hasOwnProperty(value, _name)) {\n            flattenImports(flatValues, value[_name], _name);\n          }\n        }\n      } else if (isFactory(value) || name !== undefined) {\n        var flatName = isFactory(value) ? isTransformFunctionFactory(value) ? value.fn + '.transform' // TODO: this is ugly\n        : value.fn : name;\n\n        // we allow importing the same function twice if it points to the same implementation\n        if (hasOwnProperty(flatValues, flatName) && flatValues[flatName] !== value && !options.silent) {\n          throw new Error('Cannot import \"' + flatName + '\" twice');\n        }\n        flatValues[flatName] = value;\n      } else {\n        if (!options.silent) {\n          throw new TypeError('Factory, Object, or Array expected');\n        }\n      }\n    }\n    var flatValues = {};\n    flattenImports(flatValues, functions);\n    for (var name in flatValues) {\n      if (hasOwnProperty(flatValues, name)) {\n        // console.log('import', name)\n        var value = flatValues[name];\n        if (isFactory(value)) {\n          // we ignore name here and enforce the name of the factory\n          // maybe at some point we do want to allow overriding it\n          // in that case we can implement an option overrideFactoryNames: true\n          _importFactory(value, options);\n        } else if (isSupportedType(value)) {\n          _import(name, value, options);\n        } else {\n          if (!options.silent) {\n            throw new TypeError('Factory, Object, or Array expected');\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Add a property to the math namespace\n   * @param {string} name\n   * @param {*} value\n   * @param {Object} options  See import for a description of the options\n   * @private\n   */\n  function _import(name, value, options) {\n    var _math$Unit;\n    // TODO: refactor this function, it's to complicated and contains duplicate code\n    if (options.wrap && typeof value === 'function') {\n      // create a wrapper around the function\n      value = _wrap(value);\n    }\n\n    // turn a plain function with a typed-function signature into a typed-function\n    if (hasTypedFunctionSignature(value)) {\n      value = typed(name, {\n        [value.signature]: value\n      });\n    }\n    if (typed.isTypedFunction(math[name]) && typed.isTypedFunction(value)) {\n      if (options.override) {\n        // give the typed function the right name\n        value = typed(name, value.signatures);\n      } else {\n        // merge the existing and typed function\n        value = typed(math[name], value);\n      }\n      math[name] = value;\n      delete importedFactories[name];\n      _importTransform(name, value);\n      math.emit('import', name, function resolver() {\n        return value;\n      });\n      return;\n    }\n    var isDefined = math[name] !== undefined;\n    var isValuelessUnit = (_math$Unit = math.Unit) === null || _math$Unit === void 0 ? void 0 : _math$Unit.isValuelessUnit(name);\n    if (!isDefined && !isValuelessUnit || options.override) {\n      math[name] = value;\n      delete importedFactories[name];\n      _importTransform(name, value);\n      math.emit('import', name, function resolver() {\n        return value;\n      });\n      return;\n    }\n    if (!options.silent) {\n      throw new Error('Cannot import \"' + name + '\": already exists');\n    }\n  }\n  function _importTransform(name, value) {\n    if (value && typeof value.transform === 'function') {\n      math.expression.transform[name] = value.transform;\n      if (allowedInExpressions(name)) {\n        math.expression.mathWithTransform[name] = value.transform;\n      }\n    } else {\n      // remove existing transform\n      delete math.expression.transform[name];\n      if (allowedInExpressions(name)) {\n        math.expression.mathWithTransform[name] = value;\n      }\n    }\n  }\n  function _deleteTransform(name) {\n    delete math.expression.transform[name];\n    if (allowedInExpressions(name)) {\n      math.expression.mathWithTransform[name] = math[name];\n    } else {\n      delete math.expression.mathWithTransform[name];\n    }\n  }\n\n  /**\n   * Create a wrapper a round an function which converts the arguments\n   * to their primitive values (like convert a Matrix to Array)\n   * @param {Function} fn\n   * @return {Function} Returns the wrapped function\n   * @private\n   */\n  function _wrap(fn) {\n    var wrapper = function wrapper() {\n      var args = [];\n      for (var i = 0, len = arguments.length; i < len; i++) {\n        var arg = arguments[i];\n        args[i] = arg && arg.valueOf();\n      }\n      return fn.apply(math, args);\n    };\n    if (fn.transform) {\n      wrapper.transform = fn.transform;\n    }\n    return wrapper;\n  }\n\n  /**\n   * Import an instance of a factory into math.js\n   * @param {function(scope: object)} factory\n   * @param {Object} options  See import for a description of the options\n   * @param {string} [name=factory.name] Optional custom name\n   * @private\n   */\n  function _importFactory(factory, options) {\n    var _factory$meta$formerl, _factory$meta;\n    var name = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : factory.fn;\n    if (name.includes('.')) {\n      throw new Error('Factory name should not contain a nested path. ' + 'Name: ' + JSON.stringify(name));\n    }\n    var namespace = isTransformFunctionFactory(factory) ? math.expression.transform : math;\n    var existingTransform = name in math.expression.transform;\n    var existing = hasOwnProperty(namespace, name) ? namespace[name] : undefined;\n    var resolver = function resolver() {\n      // collect all dependencies, handle finding both functions and classes and other special cases\n      var dependencies = {};\n      factory.dependencies.map(stripOptionalNotation).forEach(dependency => {\n        if (dependency.includes('.')) {\n          throw new Error('Factory dependency should not contain a nested path. ' + 'Name: ' + JSON.stringify(dependency));\n        }\n        if (dependency === 'math') {\n          dependencies.math = math;\n        } else if (dependency === 'mathWithTransform') {\n          dependencies.mathWithTransform = math.expression.mathWithTransform;\n        } else if (dependency === 'classes') {\n          // special case for json reviver\n          dependencies.classes = math;\n        } else {\n          dependencies[dependency] = math[dependency];\n        }\n      });\n      var instance = /* #__PURE__ */factory(dependencies);\n      if (instance && typeof instance.transform === 'function') {\n        throw new Error('Transforms cannot be attached to factory functions. ' + 'Please create a separate function for it with export const path = \"expression.transform\"');\n      }\n      if (existing === undefined || options.override) {\n        return instance;\n      }\n      if (typed.isTypedFunction(existing) && typed.isTypedFunction(instance)) {\n        // merge the existing and new typed function\n        return typed(existing, instance);\n      }\n      if (options.silent) {\n        // keep existing, ignore imported function\n        return existing;\n      } else {\n        throw new Error('Cannot import \"' + name + '\": already exists');\n      }\n    };\n    var former = (_factory$meta$formerl = (_factory$meta = factory.meta) === null || _factory$meta === void 0 ? void 0 : _factory$meta.formerly) !== null && _factory$meta$formerl !== void 0 ? _factory$meta$formerl : '';\n    var needsTransform = isTransformFunctionFactory(factory) || factoryAllowedInExpressions(factory);\n    var withTransform = math.expression.mathWithTransform;\n\n    // TODO: add unit test with non-lazy factory\n    if (!factory.meta || factory.meta.lazy !== false) {\n      lazy(namespace, name, resolver);\n      if (former) lazy(namespace, former, resolver);\n\n      // FIXME: remove the `if (existing &&` condition again. Can we make sure subset is loaded before subset.transform? (Name collision, and no dependencies between the two)\n      if (existing && existingTransform) {\n        _deleteTransform(name);\n        if (former) _deleteTransform(former);\n      } else {\n        if (needsTransform) {\n          lazy(withTransform, name, () => namespace[name]);\n          if (former) lazy(withTransform, former, () => namespace[name]);\n        }\n      }\n    } else {\n      namespace[name] = resolver();\n      if (former) namespace[former] = namespace[name];\n\n      // FIXME: remove the `if (existing &&` condition again. Can we make sure subset is loaded before subset.transform? (Name collision, and no dependencies between the two)\n      if (existing && existingTransform) {\n        _deleteTransform(name);\n        if (former) _deleteTransform(former);\n      } else {\n        if (needsTransform) {\n          lazy(withTransform, name, () => namespace[name]);\n          if (former) lazy(withTransform, former, () => namespace[name]);\n        }\n      }\n    }\n\n    // TODO: improve factories, store a list with imports instead which can be re-played\n    importedFactories[name] = factory;\n    math.emit('import', name, resolver);\n  }\n\n  /**\n   * Check whether given object is a type which can be imported\n   * @param {Function | number | string | boolean | null | Unit | Complex} object\n   * @return {boolean}\n   * @private\n   */\n  function isSupportedType(object) {\n    return typeof object === 'function' || typeof object === 'number' || typeof object === 'string' || typeof object === 'boolean' || object === null || isUnit(object) || isComplex(object) || isBigNumber(object) || isFraction(object) || isMatrix(object) || Array.isArray(object);\n  }\n  function isModule(object) {\n    return typeof object === 'object' && object[Symbol.toStringTag] === 'Module';\n  }\n  function hasTypedFunctionSignature(fn) {\n    return typeof fn === 'function' && typeof fn.signature === 'string';\n  }\n  function allowedInExpressions(name) {\n    return !hasOwnProperty(unsafe, name);\n  }\n  function factoryAllowedInExpressions(factory) {\n    return !factory.fn.includes('.') &&\n    // FIXME: make checking on path redundant, check on meta data instead\n    !hasOwnProperty(unsafe, factory.fn) && (!factory.meta || !factory.meta.isClass);\n  }\n  function isTransformFunctionFactory(factory) {\n    return factory !== undefined && factory.meta !== undefined && factory.meta.isTransformFunction === true || false;\n  }\n\n  // namespaces and functions not available in the parser for safety reasons\n  var unsafe = {\n    expression: true,\n    type: true,\n    docs: true,\n    error: true,\n    json: true,\n    chain: true // chain method not supported. Note that there is a unit chain too.\n  };\n  return mathImport;\n}", "map": {"version": 3, "names": ["isBigNumber", "isComplex", "isFraction", "isMatrix", "isObject", "isUnit", "isFactory", "stripOptionalNotation", "hasOwnProperty", "lazy", "ArgumentsError", "importFactory", "typed", "load", "math", "importedFactories", "mathImport", "functions", "options", "num", "arguments", "length", "flattenImports", "flatValues", "value", "name", "Array", "isArray", "for<PERSON>ach", "item", "isModule", "_name", "undefined", "flatName", "isTransformFunctionFactory", "fn", "silent", "Error", "TypeError", "_importFactory", "isSupportedType", "_import", "_math$Unit", "wrap", "_wrap", "hasTypedFunctionSignature", "signature", "isTypedFunction", "override", "signatures", "_importTransform", "emit", "resolver", "isDefined", "isValuelessUnit", "Unit", "transform", "expression", "allowedInExpressions", "mathWithTransform", "_deleteTransform", "wrapper", "args", "i", "len", "arg", "valueOf", "apply", "factory", "_factory$meta$formerl", "_factory$meta", "includes", "JSON", "stringify", "namespace", "existingTransform", "existing", "dependencies", "map", "dependency", "classes", "instance", "former", "meta", "formerly", "needsTransform", "factoryAllowedInExpressions", "withTransform", "object", "Symbol", "toStringTag", "unsafe", "isClass", "isTransformFunction", "type", "docs", "error", "json", "chain"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/core/function/import.js"], "sourcesContent": ["import { isBig<PERSON><PERSON>ber, isComplex, isFraction, isMatrix, isObject, isUnit } from '../../utils/is.js';\nimport { isFactory, stripOptionalNotation } from '../../utils/factory.js';\nimport { hasOwnProperty, lazy } from '../../utils/object.js';\nimport { ArgumentsError } from '../../error/ArgumentsError.js';\nexport function importFactory(typed, load, math, importedFactories) {\n  /**\n   * Import functions from an object or a module.\n   *\n   * This function is only available on a mathjs instance created using `create`.\n   *\n   * Syntax:\n   *\n   *    math.import(functions)\n   *    math.import(functions, options)\n   *\n   * Where:\n   *\n   * - `functions: Object`\n   *   An object with functions or factories to be imported.\n   * - `options: Object` An object with import options. Available options:\n   *   - `override: boolean`\n   *     If true, existing functions will be overwritten. False by default.\n   *   - `silent: boolean`\n   *     If true, the function will not throw errors on duplicates or invalid\n   *     types. False by default.\n   *   - `wrap: boolean`\n   *     If true, the functions will be wrapped in a wrapper function\n   *     which converts data types like Matrix to primitive data types like Array.\n   *     The wrapper is needed when extending math.js with libraries which do not\n   *     support these data type. False by default.\n   *\n   * Examples:\n   *\n   *    import { create, all } from 'mathjs'\n   *    import * as numbers from 'numbers'\n   *\n   *    // create a mathjs instance\n   *    const math = create(all)\n   *\n   *    // define new functions and variables\n   *    math.import({\n   *      myvalue: 42,\n   *      hello: function (name) {\n   *        return 'hello, ' + name + '!'\n   *      }\n   *    })\n   *\n   *    // use the imported function and variable\n   *    math.myvalue * 2               // 84\n   *    math.hello('user')             // 'hello, user!'\n   *\n   *    // import the npm module 'numbers'\n   *    // (must be installed first with `npm install numbers`)\n   *    math.import(numbers, {wrap: true})\n   *\n   *    math.fibonacci(7) // returns 13\n   *\n   * @param {Object | Array} functions  Object with functions to be imported.\n   * @param {Object} [options]          Import options.\n   */\n  function mathImport(functions, options) {\n    var num = arguments.length;\n    if (num !== 1 && num !== 2) {\n      throw new ArgumentsError('import', num, 1, 2);\n    }\n    if (!options) {\n      options = {};\n    }\n    function flattenImports(flatValues, value, name) {\n      if (Array.isArray(value)) {\n        value.forEach(item => flattenImports(flatValues, item));\n      } else if (isObject(value) || isModule(value)) {\n        for (var _name in value) {\n          if (hasOwnProperty(value, _name)) {\n            flattenImports(flatValues, value[_name], _name);\n          }\n        }\n      } else if (isFactory(value) || name !== undefined) {\n        var flatName = isFactory(value) ? isTransformFunctionFactory(value) ? value.fn + '.transform' // TODO: this is ugly\n        : value.fn : name;\n\n        // we allow importing the same function twice if it points to the same implementation\n        if (hasOwnProperty(flatValues, flatName) && flatValues[flatName] !== value && !options.silent) {\n          throw new Error('Cannot import \"' + flatName + '\" twice');\n        }\n        flatValues[flatName] = value;\n      } else {\n        if (!options.silent) {\n          throw new TypeError('Factory, Object, or Array expected');\n        }\n      }\n    }\n    var flatValues = {};\n    flattenImports(flatValues, functions);\n    for (var name in flatValues) {\n      if (hasOwnProperty(flatValues, name)) {\n        // console.log('import', name)\n        var value = flatValues[name];\n        if (isFactory(value)) {\n          // we ignore name here and enforce the name of the factory\n          // maybe at some point we do want to allow overriding it\n          // in that case we can implement an option overrideFactoryNames: true\n          _importFactory(value, options);\n        } else if (isSupportedType(value)) {\n          _import(name, value, options);\n        } else {\n          if (!options.silent) {\n            throw new TypeError('Factory, Object, or Array expected');\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Add a property to the math namespace\n   * @param {string} name\n   * @param {*} value\n   * @param {Object} options  See import for a description of the options\n   * @private\n   */\n  function _import(name, value, options) {\n    var _math$Unit;\n    // TODO: refactor this function, it's to complicated and contains duplicate code\n    if (options.wrap && typeof value === 'function') {\n      // create a wrapper around the function\n      value = _wrap(value);\n    }\n\n    // turn a plain function with a typed-function signature into a typed-function\n    if (hasTypedFunctionSignature(value)) {\n      value = typed(name, {\n        [value.signature]: value\n      });\n    }\n    if (typed.isTypedFunction(math[name]) && typed.isTypedFunction(value)) {\n      if (options.override) {\n        // give the typed function the right name\n        value = typed(name, value.signatures);\n      } else {\n        // merge the existing and typed function\n        value = typed(math[name], value);\n      }\n      math[name] = value;\n      delete importedFactories[name];\n      _importTransform(name, value);\n      math.emit('import', name, function resolver() {\n        return value;\n      });\n      return;\n    }\n    var isDefined = math[name] !== undefined;\n    var isValuelessUnit = (_math$Unit = math.Unit) === null || _math$Unit === void 0 ? void 0 : _math$Unit.isValuelessUnit(name);\n    if (!isDefined && !isValuelessUnit || options.override) {\n      math[name] = value;\n      delete importedFactories[name];\n      _importTransform(name, value);\n      math.emit('import', name, function resolver() {\n        return value;\n      });\n      return;\n    }\n    if (!options.silent) {\n      throw new Error('Cannot import \"' + name + '\": already exists');\n    }\n  }\n  function _importTransform(name, value) {\n    if (value && typeof value.transform === 'function') {\n      math.expression.transform[name] = value.transform;\n      if (allowedInExpressions(name)) {\n        math.expression.mathWithTransform[name] = value.transform;\n      }\n    } else {\n      // remove existing transform\n      delete math.expression.transform[name];\n      if (allowedInExpressions(name)) {\n        math.expression.mathWithTransform[name] = value;\n      }\n    }\n  }\n  function _deleteTransform(name) {\n    delete math.expression.transform[name];\n    if (allowedInExpressions(name)) {\n      math.expression.mathWithTransform[name] = math[name];\n    } else {\n      delete math.expression.mathWithTransform[name];\n    }\n  }\n\n  /**\n   * Create a wrapper a round an function which converts the arguments\n   * to their primitive values (like convert a Matrix to Array)\n   * @param {Function} fn\n   * @return {Function} Returns the wrapped function\n   * @private\n   */\n  function _wrap(fn) {\n    var wrapper = function wrapper() {\n      var args = [];\n      for (var i = 0, len = arguments.length; i < len; i++) {\n        var arg = arguments[i];\n        args[i] = arg && arg.valueOf();\n      }\n      return fn.apply(math, args);\n    };\n    if (fn.transform) {\n      wrapper.transform = fn.transform;\n    }\n    return wrapper;\n  }\n\n  /**\n   * Import an instance of a factory into math.js\n   * @param {function(scope: object)} factory\n   * @param {Object} options  See import for a description of the options\n   * @param {string} [name=factory.name] Optional custom name\n   * @private\n   */\n  function _importFactory(factory, options) {\n    var _factory$meta$formerl, _factory$meta;\n    var name = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : factory.fn;\n    if (name.includes('.')) {\n      throw new Error('Factory name should not contain a nested path. ' + 'Name: ' + JSON.stringify(name));\n    }\n    var namespace = isTransformFunctionFactory(factory) ? math.expression.transform : math;\n    var existingTransform = name in math.expression.transform;\n    var existing = hasOwnProperty(namespace, name) ? namespace[name] : undefined;\n    var resolver = function resolver() {\n      // collect all dependencies, handle finding both functions and classes and other special cases\n      var dependencies = {};\n      factory.dependencies.map(stripOptionalNotation).forEach(dependency => {\n        if (dependency.includes('.')) {\n          throw new Error('Factory dependency should not contain a nested path. ' + 'Name: ' + JSON.stringify(dependency));\n        }\n        if (dependency === 'math') {\n          dependencies.math = math;\n        } else if (dependency === 'mathWithTransform') {\n          dependencies.mathWithTransform = math.expression.mathWithTransform;\n        } else if (dependency === 'classes') {\n          // special case for json reviver\n          dependencies.classes = math;\n        } else {\n          dependencies[dependency] = math[dependency];\n        }\n      });\n      var instance = /* #__PURE__ */factory(dependencies);\n      if (instance && typeof instance.transform === 'function') {\n        throw new Error('Transforms cannot be attached to factory functions. ' + 'Please create a separate function for it with export const path = \"expression.transform\"');\n      }\n      if (existing === undefined || options.override) {\n        return instance;\n      }\n      if (typed.isTypedFunction(existing) && typed.isTypedFunction(instance)) {\n        // merge the existing and new typed function\n        return typed(existing, instance);\n      }\n      if (options.silent) {\n        // keep existing, ignore imported function\n        return existing;\n      } else {\n        throw new Error('Cannot import \"' + name + '\": already exists');\n      }\n    };\n    var former = (_factory$meta$formerl = (_factory$meta = factory.meta) === null || _factory$meta === void 0 ? void 0 : _factory$meta.formerly) !== null && _factory$meta$formerl !== void 0 ? _factory$meta$formerl : '';\n    var needsTransform = isTransformFunctionFactory(factory) || factoryAllowedInExpressions(factory);\n    var withTransform = math.expression.mathWithTransform;\n\n    // TODO: add unit test with non-lazy factory\n    if (!factory.meta || factory.meta.lazy !== false) {\n      lazy(namespace, name, resolver);\n      if (former) lazy(namespace, former, resolver);\n\n      // FIXME: remove the `if (existing &&` condition again. Can we make sure subset is loaded before subset.transform? (Name collision, and no dependencies between the two)\n      if (existing && existingTransform) {\n        _deleteTransform(name);\n        if (former) _deleteTransform(former);\n      } else {\n        if (needsTransform) {\n          lazy(withTransform, name, () => namespace[name]);\n          if (former) lazy(withTransform, former, () => namespace[name]);\n        }\n      }\n    } else {\n      namespace[name] = resolver();\n      if (former) namespace[former] = namespace[name];\n\n      // FIXME: remove the `if (existing &&` condition again. Can we make sure subset is loaded before subset.transform? (Name collision, and no dependencies between the two)\n      if (existing && existingTransform) {\n        _deleteTransform(name);\n        if (former) _deleteTransform(former);\n      } else {\n        if (needsTransform) {\n          lazy(withTransform, name, () => namespace[name]);\n          if (former) lazy(withTransform, former, () => namespace[name]);\n        }\n      }\n    }\n\n    // TODO: improve factories, store a list with imports instead which can be re-played\n    importedFactories[name] = factory;\n    math.emit('import', name, resolver);\n  }\n\n  /**\n   * Check whether given object is a type which can be imported\n   * @param {Function | number | string | boolean | null | Unit | Complex} object\n   * @return {boolean}\n   * @private\n   */\n  function isSupportedType(object) {\n    return typeof object === 'function' || typeof object === 'number' || typeof object === 'string' || typeof object === 'boolean' || object === null || isUnit(object) || isComplex(object) || isBigNumber(object) || isFraction(object) || isMatrix(object) || Array.isArray(object);\n  }\n  function isModule(object) {\n    return typeof object === 'object' && object[Symbol.toStringTag] === 'Module';\n  }\n  function hasTypedFunctionSignature(fn) {\n    return typeof fn === 'function' && typeof fn.signature === 'string';\n  }\n  function allowedInExpressions(name) {\n    return !hasOwnProperty(unsafe, name);\n  }\n  function factoryAllowedInExpressions(factory) {\n    return !factory.fn.includes('.') &&\n    // FIXME: make checking on path redundant, check on meta data instead\n    !hasOwnProperty(unsafe, factory.fn) && (!factory.meta || !factory.meta.isClass);\n  }\n  function isTransformFunctionFactory(factory) {\n    return factory !== undefined && factory.meta !== undefined && factory.meta.isTransformFunction === true || false;\n  }\n\n  // namespaces and functions not available in the parser for safety reasons\n  var unsafe = {\n    expression: true,\n    type: true,\n    docs: true,\n    error: true,\n    json: true,\n    chain: true // chain method not supported. Note that there is a unit chain too.\n  };\n  return mathImport;\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,mBAAmB;AAClG,SAASC,SAAS,EAAEC,qBAAqB,QAAQ,wBAAwB;AACzE,SAASC,cAAc,EAAEC,IAAI,QAAQ,uBAAuB;AAC5D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,iBAAiB,EAAE;EAClE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAACC,SAAS,EAAEC,OAAO,EAAE;IACtC,IAAIC,GAAG,GAAGC,SAAS,CAACC,MAAM;IAC1B,IAAIF,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAC1B,MAAM,IAAIT,cAAc,CAAC,QAAQ,EAAES,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C;IACA,IAAI,CAACD,OAAO,EAAE;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,SAASI,cAAcA,CAACC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAAE;MAC/C,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;QACxBA,KAAK,CAACI,OAAO,CAACC,IAAI,IAAIP,cAAc,CAACC,UAAU,EAAEM,IAAI,CAAC,CAAC;MACzD,CAAC,MAAM,IAAIzB,QAAQ,CAACoB,KAAK,CAAC,IAAIM,QAAQ,CAACN,KAAK,CAAC,EAAE;QAC7C,KAAK,IAAIO,KAAK,IAAIP,KAAK,EAAE;UACvB,IAAIhB,cAAc,CAACgB,KAAK,EAAEO,KAAK,CAAC,EAAE;YAChCT,cAAc,CAACC,UAAU,EAAEC,KAAK,CAACO,KAAK,CAAC,EAAEA,KAAK,CAAC;UACjD;QACF;MACF,CAAC,MAAM,IAAIzB,SAAS,CAACkB,KAAK,CAAC,IAAIC,IAAI,KAAKO,SAAS,EAAE;QACjD,IAAIC,QAAQ,GAAG3B,SAAS,CAACkB,KAAK,CAAC,GAAGU,0BAA0B,CAACV,KAAK,CAAC,GAAGA,KAAK,CAACW,EAAE,GAAG,YAAY,CAAC;QAAA,EAC5FX,KAAK,CAACW,EAAE,GAAGV,IAAI;;QAEjB;QACA,IAAIjB,cAAc,CAACe,UAAU,EAAEU,QAAQ,CAAC,IAAIV,UAAU,CAACU,QAAQ,CAAC,KAAKT,KAAK,IAAI,CAACN,OAAO,CAACkB,MAAM,EAAE;UAC7F,MAAM,IAAIC,KAAK,CAAC,iBAAiB,GAAGJ,QAAQ,GAAG,SAAS,CAAC;QAC3D;QACAV,UAAU,CAACU,QAAQ,CAAC,GAAGT,KAAK;MAC9B,CAAC,MAAM;QACL,IAAI,CAACN,OAAO,CAACkB,MAAM,EAAE;UACnB,MAAM,IAAIE,SAAS,CAAC,oCAAoC,CAAC;QAC3D;MACF;IACF;IACA,IAAIf,UAAU,GAAG,CAAC,CAAC;IACnBD,cAAc,CAACC,UAAU,EAAEN,SAAS,CAAC;IACrC,KAAK,IAAIQ,IAAI,IAAIF,UAAU,EAAE;MAC3B,IAAIf,cAAc,CAACe,UAAU,EAAEE,IAAI,CAAC,EAAE;QACpC;QACA,IAAID,KAAK,GAAGD,UAAU,CAACE,IAAI,CAAC;QAC5B,IAAInB,SAAS,CAACkB,KAAK,CAAC,EAAE;UACpB;UACA;UACA;UACAe,cAAc,CAACf,KAAK,EAAEN,OAAO,CAAC;QAChC,CAAC,MAAM,IAAIsB,eAAe,CAAChB,KAAK,CAAC,EAAE;UACjCiB,OAAO,CAAChB,IAAI,EAAED,KAAK,EAAEN,OAAO,CAAC;QAC/B,CAAC,MAAM;UACL,IAAI,CAACA,OAAO,CAACkB,MAAM,EAAE;YACnB,MAAM,IAAIE,SAAS,CAAC,oCAAoC,CAAC;UAC3D;QACF;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,OAAOA,CAAChB,IAAI,EAAED,KAAK,EAAEN,OAAO,EAAE;IACrC,IAAIwB,UAAU;IACd;IACA,IAAIxB,OAAO,CAACyB,IAAI,IAAI,OAAOnB,KAAK,KAAK,UAAU,EAAE;MAC/C;MACAA,KAAK,GAAGoB,KAAK,CAACpB,KAAK,CAAC;IACtB;;IAEA;IACA,IAAIqB,yBAAyB,CAACrB,KAAK,CAAC,EAAE;MACpCA,KAAK,GAAGZ,KAAK,CAACa,IAAI,EAAE;QAClB,CAACD,KAAK,CAACsB,SAAS,GAAGtB;MACrB,CAAC,CAAC;IACJ;IACA,IAAIZ,KAAK,CAACmC,eAAe,CAACjC,IAAI,CAACW,IAAI,CAAC,CAAC,IAAIb,KAAK,CAACmC,eAAe,CAACvB,KAAK,CAAC,EAAE;MACrE,IAAIN,OAAO,CAAC8B,QAAQ,EAAE;QACpB;QACAxB,KAAK,GAAGZ,KAAK,CAACa,IAAI,EAAED,KAAK,CAACyB,UAAU,CAAC;MACvC,CAAC,MAAM;QACL;QACAzB,KAAK,GAAGZ,KAAK,CAACE,IAAI,CAACW,IAAI,CAAC,EAAED,KAAK,CAAC;MAClC;MACAV,IAAI,CAACW,IAAI,CAAC,GAAGD,KAAK;MAClB,OAAOT,iBAAiB,CAACU,IAAI,CAAC;MAC9ByB,gBAAgB,CAACzB,IAAI,EAAED,KAAK,CAAC;MAC7BV,IAAI,CAACqC,IAAI,CAAC,QAAQ,EAAE1B,IAAI,EAAE,SAAS2B,QAAQA,CAAA,EAAG;QAC5C,OAAO5B,KAAK;MACd,CAAC,CAAC;MACF;IACF;IACA,IAAI6B,SAAS,GAAGvC,IAAI,CAACW,IAAI,CAAC,KAAKO,SAAS;IACxC,IAAIsB,eAAe,GAAG,CAACZ,UAAU,GAAG5B,IAAI,CAACyC,IAAI,MAAM,IAAI,IAAIb,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACY,eAAe,CAAC7B,IAAI,CAAC;IAC5H,IAAI,CAAC4B,SAAS,IAAI,CAACC,eAAe,IAAIpC,OAAO,CAAC8B,QAAQ,EAAE;MACtDlC,IAAI,CAACW,IAAI,CAAC,GAAGD,KAAK;MAClB,OAAOT,iBAAiB,CAACU,IAAI,CAAC;MAC9ByB,gBAAgB,CAACzB,IAAI,EAAED,KAAK,CAAC;MAC7BV,IAAI,CAACqC,IAAI,CAAC,QAAQ,EAAE1B,IAAI,EAAE,SAAS2B,QAAQA,CAAA,EAAG;QAC5C,OAAO5B,KAAK;MACd,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACN,OAAO,CAACkB,MAAM,EAAE;MACnB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,GAAGZ,IAAI,GAAG,mBAAmB,CAAC;IACjE;EACF;EACA,SAASyB,gBAAgBA,CAACzB,IAAI,EAAED,KAAK,EAAE;IACrC,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACgC,SAAS,KAAK,UAAU,EAAE;MAClD1C,IAAI,CAAC2C,UAAU,CAACD,SAAS,CAAC/B,IAAI,CAAC,GAAGD,KAAK,CAACgC,SAAS;MACjD,IAAIE,oBAAoB,CAACjC,IAAI,CAAC,EAAE;QAC9BX,IAAI,CAAC2C,UAAU,CAACE,iBAAiB,CAAClC,IAAI,CAAC,GAAGD,KAAK,CAACgC,SAAS;MAC3D;IACF,CAAC,MAAM;MACL;MACA,OAAO1C,IAAI,CAAC2C,UAAU,CAACD,SAAS,CAAC/B,IAAI,CAAC;MACtC,IAAIiC,oBAAoB,CAACjC,IAAI,CAAC,EAAE;QAC9BX,IAAI,CAAC2C,UAAU,CAACE,iBAAiB,CAAClC,IAAI,CAAC,GAAGD,KAAK;MACjD;IACF;EACF;EACA,SAASoC,gBAAgBA,CAACnC,IAAI,EAAE;IAC9B,OAAOX,IAAI,CAAC2C,UAAU,CAACD,SAAS,CAAC/B,IAAI,CAAC;IACtC,IAAIiC,oBAAoB,CAACjC,IAAI,CAAC,EAAE;MAC9BX,IAAI,CAAC2C,UAAU,CAACE,iBAAiB,CAAClC,IAAI,CAAC,GAAGX,IAAI,CAACW,IAAI,CAAC;IACtD,CAAC,MAAM;MACL,OAAOX,IAAI,CAAC2C,UAAU,CAACE,iBAAiB,CAAClC,IAAI,CAAC;IAChD;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASmB,KAAKA,CAACT,EAAE,EAAE;IACjB,IAAI0B,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;MAC/B,IAAIC,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG5C,SAAS,CAACC,MAAM,EAAE0C,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACpD,IAAIE,GAAG,GAAG7C,SAAS,CAAC2C,CAAC,CAAC;QACtBD,IAAI,CAACC,CAAC,CAAC,GAAGE,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,CAAC;MAChC;MACA,OAAO/B,EAAE,CAACgC,KAAK,CAACrD,IAAI,EAAEgD,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI3B,EAAE,CAACqB,SAAS,EAAE;MAChBK,OAAO,CAACL,SAAS,GAAGrB,EAAE,CAACqB,SAAS;IAClC;IACA,OAAOK,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAAStB,cAAcA,CAAC6B,OAAO,EAAElD,OAAO,EAAE;IACxC,IAAImD,qBAAqB,EAAEC,aAAa;IACxC,IAAI7C,IAAI,GAAGL,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKY,SAAS,GAAGZ,SAAS,CAAC,CAAC,CAAC,GAAGgD,OAAO,CAACjC,EAAE;IACzF,IAAIV,IAAI,CAAC8C,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtB,MAAM,IAAIlC,KAAK,CAAC,iDAAiD,GAAG,QAAQ,GAAGmC,IAAI,CAACC,SAAS,CAAChD,IAAI,CAAC,CAAC;IACtG;IACA,IAAIiD,SAAS,GAAGxC,0BAA0B,CAACkC,OAAO,CAAC,GAAGtD,IAAI,CAAC2C,UAAU,CAACD,SAAS,GAAG1C,IAAI;IACtF,IAAI6D,iBAAiB,GAAGlD,IAAI,IAAIX,IAAI,CAAC2C,UAAU,CAACD,SAAS;IACzD,IAAIoB,QAAQ,GAAGpE,cAAc,CAACkE,SAAS,EAAEjD,IAAI,CAAC,GAAGiD,SAAS,CAACjD,IAAI,CAAC,GAAGO,SAAS;IAC5E,IAAIoB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;MACjC;MACA,IAAIyB,YAAY,GAAG,CAAC,CAAC;MACrBT,OAAO,CAACS,YAAY,CAACC,GAAG,CAACvE,qBAAqB,CAAC,CAACqB,OAAO,CAACmD,UAAU,IAAI;QACpE,IAAIA,UAAU,CAACR,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC5B,MAAM,IAAIlC,KAAK,CAAC,uDAAuD,GAAG,QAAQ,GAAGmC,IAAI,CAACC,SAAS,CAACM,UAAU,CAAC,CAAC;QAClH;QACA,IAAIA,UAAU,KAAK,MAAM,EAAE;UACzBF,YAAY,CAAC/D,IAAI,GAAGA,IAAI;QAC1B,CAAC,MAAM,IAAIiE,UAAU,KAAK,mBAAmB,EAAE;UAC7CF,YAAY,CAAClB,iBAAiB,GAAG7C,IAAI,CAAC2C,UAAU,CAACE,iBAAiB;QACpE,CAAC,MAAM,IAAIoB,UAAU,KAAK,SAAS,EAAE;UACnC;UACAF,YAAY,CAACG,OAAO,GAAGlE,IAAI;QAC7B,CAAC,MAAM;UACL+D,YAAY,CAACE,UAAU,CAAC,GAAGjE,IAAI,CAACiE,UAAU,CAAC;QAC7C;MACF,CAAC,CAAC;MACF,IAAIE,QAAQ,GAAG,eAAeb,OAAO,CAACS,YAAY,CAAC;MACnD,IAAII,QAAQ,IAAI,OAAOA,QAAQ,CAACzB,SAAS,KAAK,UAAU,EAAE;QACxD,MAAM,IAAInB,KAAK,CAAC,sDAAsD,GAAG,0FAA0F,CAAC;MACtK;MACA,IAAIuC,QAAQ,KAAK5C,SAAS,IAAId,OAAO,CAAC8B,QAAQ,EAAE;QAC9C,OAAOiC,QAAQ;MACjB;MACA,IAAIrE,KAAK,CAACmC,eAAe,CAAC6B,QAAQ,CAAC,IAAIhE,KAAK,CAACmC,eAAe,CAACkC,QAAQ,CAAC,EAAE;QACtE;QACA,OAAOrE,KAAK,CAACgE,QAAQ,EAAEK,QAAQ,CAAC;MAClC;MACA,IAAI/D,OAAO,CAACkB,MAAM,EAAE;QAClB;QACA,OAAOwC,QAAQ;MACjB,CAAC,MAAM;QACL,MAAM,IAAIvC,KAAK,CAAC,iBAAiB,GAAGZ,IAAI,GAAG,mBAAmB,CAAC;MACjE;IACF,CAAC;IACD,IAAIyD,MAAM,GAAG,CAACb,qBAAqB,GAAG,CAACC,aAAa,GAAGF,OAAO,CAACe,IAAI,MAAM,IAAI,IAAIb,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACc,QAAQ,MAAM,IAAI,IAAIf,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE;IACtN,IAAIgB,cAAc,GAAGnD,0BAA0B,CAACkC,OAAO,CAAC,IAAIkB,2BAA2B,CAAClB,OAAO,CAAC;IAChG,IAAImB,aAAa,GAAGzE,IAAI,CAAC2C,UAAU,CAACE,iBAAiB;;IAErD;IACA,IAAI,CAACS,OAAO,CAACe,IAAI,IAAIf,OAAO,CAACe,IAAI,CAAC1E,IAAI,KAAK,KAAK,EAAE;MAChDA,IAAI,CAACiE,SAAS,EAAEjD,IAAI,EAAE2B,QAAQ,CAAC;MAC/B,IAAI8B,MAAM,EAAEzE,IAAI,CAACiE,SAAS,EAAEQ,MAAM,EAAE9B,QAAQ,CAAC;;MAE7C;MACA,IAAIwB,QAAQ,IAAID,iBAAiB,EAAE;QACjCf,gBAAgB,CAACnC,IAAI,CAAC;QACtB,IAAIyD,MAAM,EAAEtB,gBAAgB,CAACsB,MAAM,CAAC;MACtC,CAAC,MAAM;QACL,IAAIG,cAAc,EAAE;UAClB5E,IAAI,CAAC8E,aAAa,EAAE9D,IAAI,EAAE,MAAMiD,SAAS,CAACjD,IAAI,CAAC,CAAC;UAChD,IAAIyD,MAAM,EAAEzE,IAAI,CAAC8E,aAAa,EAAEL,MAAM,EAAE,MAAMR,SAAS,CAACjD,IAAI,CAAC,CAAC;QAChE;MACF;IACF,CAAC,MAAM;MACLiD,SAAS,CAACjD,IAAI,CAAC,GAAG2B,QAAQ,CAAC,CAAC;MAC5B,IAAI8B,MAAM,EAAER,SAAS,CAACQ,MAAM,CAAC,GAAGR,SAAS,CAACjD,IAAI,CAAC;;MAE/C;MACA,IAAImD,QAAQ,IAAID,iBAAiB,EAAE;QACjCf,gBAAgB,CAACnC,IAAI,CAAC;QACtB,IAAIyD,MAAM,EAAEtB,gBAAgB,CAACsB,MAAM,CAAC;MACtC,CAAC,MAAM;QACL,IAAIG,cAAc,EAAE;UAClB5E,IAAI,CAAC8E,aAAa,EAAE9D,IAAI,EAAE,MAAMiD,SAAS,CAACjD,IAAI,CAAC,CAAC;UAChD,IAAIyD,MAAM,EAAEzE,IAAI,CAAC8E,aAAa,EAAEL,MAAM,EAAE,MAAMR,SAAS,CAACjD,IAAI,CAAC,CAAC;QAChE;MACF;IACF;;IAEA;IACAV,iBAAiB,CAACU,IAAI,CAAC,GAAG2C,OAAO;IACjCtD,IAAI,CAACqC,IAAI,CAAC,QAAQ,EAAE1B,IAAI,EAAE2B,QAAQ,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASZ,eAAeA,CAACgD,MAAM,EAAE;IAC/B,OAAO,OAAOA,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,IAAI,IAAInF,MAAM,CAACmF,MAAM,CAAC,IAAIvF,SAAS,CAACuF,MAAM,CAAC,IAAIxF,WAAW,CAACwF,MAAM,CAAC,IAAItF,UAAU,CAACsF,MAAM,CAAC,IAAIrF,QAAQ,CAACqF,MAAM,CAAC,IAAI9D,KAAK,CAACC,OAAO,CAAC6D,MAAM,CAAC;EACpR;EACA,SAAS1D,QAAQA,CAAC0D,MAAM,EAAE;IACxB,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,MAAM,CAACC,WAAW,CAAC,KAAK,QAAQ;EAC9E;EACA,SAAS7C,yBAAyBA,CAACV,EAAE,EAAE;IACrC,OAAO,OAAOA,EAAE,KAAK,UAAU,IAAI,OAAOA,EAAE,CAACW,SAAS,KAAK,QAAQ;EACrE;EACA,SAASY,oBAAoBA,CAACjC,IAAI,EAAE;IAClC,OAAO,CAACjB,cAAc,CAACmF,MAAM,EAAElE,IAAI,CAAC;EACtC;EACA,SAAS6D,2BAA2BA,CAAClB,OAAO,EAAE;IAC5C,OAAO,CAACA,OAAO,CAACjC,EAAE,CAACoC,QAAQ,CAAC,GAAG,CAAC;IAChC;IACA,CAAC/D,cAAc,CAACmF,MAAM,EAAEvB,OAAO,CAACjC,EAAE,CAAC,KAAK,CAACiC,OAAO,CAACe,IAAI,IAAI,CAACf,OAAO,CAACe,IAAI,CAACS,OAAO,CAAC;EACjF;EACA,SAAS1D,0BAA0BA,CAACkC,OAAO,EAAE;IAC3C,OAAOA,OAAO,KAAKpC,SAAS,IAAIoC,OAAO,CAACe,IAAI,KAAKnD,SAAS,IAAIoC,OAAO,CAACe,IAAI,CAACU,mBAAmB,KAAK,IAAI,IAAI,KAAK;EAClH;;EAEA;EACA,IAAIF,MAAM,GAAG;IACXlC,UAAU,EAAE,IAAI;IAChBqC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI,CAAC;EACd,CAAC;EACD,OAAOlF,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}