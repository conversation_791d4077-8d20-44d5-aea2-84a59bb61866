<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Contact - HandyMath</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Contact HandyMath</h1>
        
        <form id="contactForm">
            <div class="form-group">
                <label for="name">Nom :</label>
                <input type="text" id="name" name="name" value="Test User" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email :</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="category">Catégorie :</label>
                <select id="category" name="category" required>
                    <option value="question">Question générale</option>
                    <option value="bug">Signaler un bug</option>
                    <option value="feature">Demande de fonctionnalité</option>
                    <option value="help">Aide technique</option>
                    <option value="other">Autre</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="subject">Sujet :</label>
                <input type="text" id="subject" name="subject" value="Test de l'API de contact" required>
            </div>
            
            <div class="form-group">
                <label for="message">Message :</label>
                <textarea id="message" name="message" required>Ceci est un message de test pour vérifier que l'API de contact fonctionne correctement. Le serveur Django devrait recevoir ce message et le sauvegarder en base de données.</textarea>
            </div>
            
            <button type="submit" id="submitBtn">Envoyer le message</button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const result = document.getElementById('result');
            
            // Désactiver le bouton et afficher le loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'Envoi en cours...';
            result.style.display = 'block';
            result.className = 'result loading';
            result.innerHTML = '⏳ Envoi du message en cours...';
            
            // Récupérer les données du formulaire
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                category: document.getElementById('category').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value
            };
            
            try {
                console.log('📤 Envoi des données:', formData);
                
                const response = await fetch('http://127.0.0.1:8000/api/contact/send/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                console.log('📥 Status Code:', response.status);
                console.log('📥 Headers:', Object.fromEntries(response.headers.entries()));
                
                const responseData = await response.text();
                console.log('📥 Response:', responseData);
                
                if (response.ok) {
                    const data = JSON.parse(responseData);
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ Message envoyé avec succès !</h3>
                        <p><strong>Message :</strong> ${data.message}</p>
                        <p><strong>ID du contact :</strong> #${data.contact_id}</p>
                        <p><strong>Status Code :</strong> ${response.status}</p>
                    `;
                } else {
                    let errorMessage = 'Erreur inconnue';
                    try {
                        const errorData = JSON.parse(responseData);
                        if (errorData.errors) {
                            errorMessage = Object.values(errorData.errors).flat().join(', ');
                        } else if (errorData.detail) {
                            errorMessage = errorData.detail;
                        } else if (errorData.message) {
                            errorMessage = errorData.message;
                        }
                    } catch (e) {
                        errorMessage = responseData;
                    }
                    
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ Erreur lors de l'envoi</h3>
                        <p><strong>Status Code :</strong> ${response.status}</p>
                        <p><strong>Erreur :</strong> ${errorMessage}</p>
                        <p><strong>Réponse brute :</strong> ${responseData}</p>
                    `;
                }
            } catch (error) {
                console.error('💥 Erreur:', error);
                result.className = 'result error';
                result.innerHTML = `
                    <h3>💥 Erreur de connexion</h3>
                    <p><strong>Erreur :</strong> ${error.message}</p>
                    <p>Assurez-vous que le serveur Django fonctionne sur http://127.0.0.1:8000/</p>
                `;
            } finally {
                // Réactiver le bouton
                submitBtn.disabled = false;
                submitBtn.textContent = 'Envoyer le message';
            }
        });
        
        // Afficher les informations de debug
        console.log('🚀 Page de test API Contact chargée');
        console.log('🎯 URL de l\'API:', 'http://127.0.0.1:8000/api/contact/send/');
    </script>
</body>
</html>
