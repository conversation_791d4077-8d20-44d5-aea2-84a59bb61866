{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { evaluateDependencies } from './dependenciesEvaluate.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { createParserClass } from '../../factoriesAny.js';\nexport var ParserDependencies = {\n  evaluateDependencies,\n  parseDependencies,\n  createParserClass\n};", "map": {"version": 3, "names": ["evaluateDependencies", "parseDependencies", "createParserClass", "ParserDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesParserClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { evaluateDependencies } from './dependenciesEvaluate.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { createParserClass } from '../../factoriesAny.js';\nexport var ParserDependencies = {\n  evaluateDependencies,\n  parseDependencies,\n  createParserClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BH,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}