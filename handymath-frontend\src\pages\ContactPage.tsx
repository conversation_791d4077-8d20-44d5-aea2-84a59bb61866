import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNotifications } from '../components/NotificationSystem';
import SimpleHeader from '../components/SimpleHeader';
import api from '../services/api';

interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
  category: string;
}

const ContactPage: React.FC = () => {
  const { addNotification } = useNotifications();

  const [formData, setFormData] = useState<ContactForm>({
    name: '',
    email: '',
    subject: '',
    message: '',
    category: 'question'
  });

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Envoyer le message à l'API
      const response = await api.post('/contact/send/', {
        name: formData.name,
        email: formData.email,
        category: formData.category,
        subject: formData.subject,
        message: formData.message
      });

      if (response.data.success) {
        addNotification({
          type: 'success',
          title: 'Message envoyé avec succès !',
          message: 'Nous vous répondrons dans les plus brefs délais. Numéro de référence : #' + response.data.contact_id
        });

        // Réinitialiser le formulaire
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
          category: 'question'
        });
      } else {
        throw new Error('Erreur lors de l\'envoi');
      }
    } catch (error: any) {
      console.error('Erreur envoi message:', error);

      let errorMessage = 'Impossible d\'envoyer le message. Veuillez réessayer.';

      if (error.response?.data?.errors) {
        const errors = error.response.data.errors;
        errorMessage = Object.values(errors).flat().join(', ');
      }

      addNotification({
        type: 'error',
        title: 'Erreur d\'envoi',
        message: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <>
      <SimpleHeader title="Contact & Support" showBackButton />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">

          {/* En-tête */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Contactez-nous
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              Une question ? Un problème ? Nous sommes là pour vous aider !
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

            {/* Formulaire de contact */}
            <motion.div
              className="lg:col-span-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Envoyez-nous un message
                </h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Nom complet *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500"
                        placeholder="Votre nom"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Catégorie
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="question">Question générale</option>
                      <option value="bug">Signaler un bug</option>
                      <option value="feature">Demande de fonctionnalité</option>
                      <option value="help">Aide technique</option>
                      <option value="other">Autre</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Sujet *
                    </label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500"
                      placeholder="Résumé de votre demande"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Message *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500"
                      placeholder="Décrivez votre demande en détail..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center justify-center"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Envoi en cours...
                      </>
                    ) : (
                      <>
                        <span className="mr-2">📧</span>
                        Envoyer le message
                      </>
                    )}
                  </button>
                </form>
              </div>
            </motion.div>

            {/* Informations de contact */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >

              {/* FAQ rapide */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <span className="text-xl mr-2">❓</span>
                  FAQ Rapide
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      Comment résoudre une équation ?
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Utilisez notre solveur d'équations ou prenez une photo de votre équation.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      Problème de connexion ?
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Vérifiez vos identifiants ou réinitialisez votre mot de passe.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      Comment suivre ma progression ?
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Consultez votre tableau de bord ou la page de progression.
                    </p>
                  </div>
                </div>
              </div>

              {/* Informations de contact */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <span className="text-xl mr-2">📞</span>
                  Autres moyens de contact
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <span className="text-lg mr-3">📧</span>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">Email</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="text-lg mr-3">💬</span>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">Chat en direct</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Lun-Ven 9h-18h</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="text-lg mr-3">📱</span>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">Téléphone</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">+212 664 470 516</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Temps de réponse */}
              <div className="bg-primary-50 dark:bg-primary-900/20 rounded-xl p-6">
                <h3 className="text-lg font-bold text-primary-900 dark:text-primary-100 mb-2 flex items-center">
                  <span className="text-xl mr-2">⏱️</span>
                  Temps de réponse
                </h3>
                <p className="text-sm text-primary-700 dark:text-primary-300">
                  Nous nous engageons à répondre à votre message dans les <strong>24 heures</strong>
                  pendant les jours ouvrables.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactPage;
