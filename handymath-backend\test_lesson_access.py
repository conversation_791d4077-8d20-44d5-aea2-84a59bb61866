#!/usr/bin/env python
"""
Script pour tester l'accès aux leçons après les corrections
"""
import os
import sys
import django
import requests

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import Course, Chapter, Lesson, LessonProgress
from django.contrib.auth import get_user_model

User = get_user_model()

def test_lesson_access():
    """Tester l'accès aux leçons via l'API"""
    print("🧪 Test d'accès aux leçons via l'API...\n")
    
    # Obtenir un token d'authentification pour un utilisateur de test
    login_data = {
        'username': 'ayoub',  # Utilisateur qui a des leçons terminées
        'password': 'testpass123'  # Mot de passe par défaut
    }
    
    try:
        # Connexion
        response = requests.post('http://localhost:8000/api/token/', json=login_data)
        if response.status_code != 200:
            print(f"❌ Échec de la connexion: {response.status_code}")
            print(f"Réponse: {response.text}")
            return
            
        token = response.json()['access']
        headers = {'Authorization': f'Bearer {token}'}
        print(f"✅ Connexion réussie pour {login_data['username']}")
        
        # Tester l'accès aux cours
        response = requests.get('http://localhost:8000/api/courses/', headers=headers)
        if response.status_code == 200:
            courses = response.json()
            print(f"✅ Récupération des cours réussie: {len(courses)} cours trouvés")
            
            # Tester l'accès à un cours spécifique
            if courses:
                course_id = courses[0]['id']
                response = requests.get(f'http://localhost:8000/api/courses/{course_id}/', headers=headers)
                if response.status_code == 200:
                    course_detail = response.json()
                    print(f"✅ Détails du cours '{course_detail['title']}' récupérés")
                    
                    # Tester l'accès aux leçons
                    for chapter in course_detail['chapters']:
                        print(f"  📖 Chapitre: {chapter['title']}")
                        for lesson in chapter['lessons']:
                            lesson_id = lesson['id']
                            lesson_response = requests.get(f'http://localhost:8000/api/lessons/{lesson_id}/', headers=headers)
                            
                            if lesson_response.status_code == 200:
                                print(f"    ✅ Leçon accessible: {lesson['title']}")
                            elif lesson_response.status_code == 403:
                                print(f"    🔒 Leçon verrouillée: {lesson['title']}")
                            else:
                                print(f"    ❌ Erreur {lesson_response.status_code}: {lesson['title']}")
                else:
                    print(f"❌ Erreur lors de la récupération du cours: {response.status_code}")
        else:
            print(f"❌ Erreur lors de la récupération des cours: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur. Assurez-vous que le serveur Django fonctionne.")
    except Exception as e:
        print(f"❌ Erreur inattendue: {str(e)}")

def test_lesson_completion():
    """Tester la completion d'une leçon"""
    print("\n🧪 Test de completion de leçon...\n")
    
    # Utiliser l'utilisateur ayoub qui a déjà des progressions
    user = User.objects.get(username='ayoub')
    
    # Trouver une leçon accessible mais non terminée
    for course in Course.objects.filter(status='published'):
        if course.is_accessible_for_user(user):
            for chapter in course.chapters.all():
                for lesson in chapter.lessons.filter(is_published=True).order_by('order'):
                    if lesson.is_accessible_for_user(user):
                        progress = LessonProgress.objects.filter(user=user, lesson=lesson).first()
                        if not progress or not progress.completed:
                            print(f"📚 Test avec la leçon: {lesson.title}")
                            print(f"   Cours: {course.title}")
                            print(f"   Chapitre: {chapter.title}")
                            print(f"   Accessible: {lesson.is_accessible_for_user(user)}")
                            
                            # Créer ou mettre à jour la progression
                            progress, created = LessonProgress.objects.get_or_create(
                                user=user,
                                lesson=lesson,
                                defaults={'completed': False, 'time_spent': 0}
                            )
                            
                            if created:
                                print(f"   ✅ Progression créée")
                            else:
                                print(f"   ✅ Progression existante trouvée")
                            
                            return lesson, user
    
    print("❌ Aucune leçon accessible non terminée trouvée")
    return None, None

def main():
    print("🚀 Tests du système de leçons après corrections...\n")
    
    # Test 1: Accès via API
    test_lesson_access()
    
    # Test 2: Logique de completion
    lesson, user = test_lesson_completion()
    
    if lesson and user:
        print(f"\n✅ Tests terminés avec succès!")
        print(f"   Leçon de test: {lesson.title}")
        print(f"   Utilisateur de test: {user.username}")
    else:
        print(f"\n⚠️ Tests partiellement réussis")
    
    print(f"\n📋 Résumé des corrections apportées:")
    print(f"   ✅ Ordre des leçons corrigé (commence à 0)")
    print(f"   ✅ Progressions créées pour tous les utilisateurs")
    print(f"   ✅ Logique d'accessibilité améliorée")
    print(f"   ✅ Gestion d'erreur frontend améliorée")
    print(f"   ✅ API de completion de leçon corrigée")

if __name__ == '__main__':
    main()
