{"ast": null, "code": "export var helpDocs = {\n  name: 'help',\n  category: 'Expression',\n  syntax: ['help(object)', 'help(string)'],\n  description: 'Display documentation on a function or data type.',\n  examples: ['help(sqrt)', 'help(\"complex\")'],\n  seealso: []\n};", "map": {"version": 3, "names": ["helpDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/expression/help.js"], "sourcesContent": ["export var helpDocs = {\n  name: 'help',\n  category: 'Expression',\n  syntax: ['help(object)', 'help(string)'],\n  description: 'Display documentation on a function or data type.',\n  examples: ['help(sqrt)', 'help(\"complex\")'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;EACxCC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,CAAC,YAAY,EAAE,iBAAiB,CAAC;EAC3CC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}