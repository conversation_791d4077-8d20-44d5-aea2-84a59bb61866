from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from .models import Equation

class EquationAPITest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser', 
            password='testpassword'
        )
        self.client.force_authenticate(user=self.user)
        
    def test_create_equation(self):
        data = {'equation_text': 'x^2 + 2x + 1 = 0'}
        response = self.client.post('/api/equations/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Equation.objects.count(), 1)
        self.assertEqual(Equation.objects.get().equation_text, 'x^2 + 2x + 1 = 0')