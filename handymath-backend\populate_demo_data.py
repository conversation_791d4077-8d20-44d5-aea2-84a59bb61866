#!/usr/bin/env python3
"""
Script pour ajouter des données de démonstration à la base de données
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from api.models import Course, Exercise, Equation

User = get_user_model()

def create_demo_courses():
    """Créer des cours de démonstration"""
    courses_data = [
        {
            'title': 'Algèbre de base',
            'description': 'Introduction aux concepts fondamentaux de l\'algèbre : variables, équations du premier degré, et résolution de problèmes simples.',
            'level': 'beginner',
        },
        {
            'title': 'Équations du second degré',
            'description': 'Maîtrisez les équations quadratiques, le discriminant, et les différentes méthodes de résolution.',
            'level': 'intermediate',
        },
        {
            'title': 'Systèmes d\'équations',
            'description': 'Résolution de systèmes d\'équations linéaires par substitution, élimination et méthodes matricielles.',
            'level': 'intermediate',
        },
        {
            'title': 'Fonctions exponentielles',
            'description': 'Étude des fonctions exponentielles et logarithmiques, leurs propriétés et applications.',
            'level': 'advanced',
        },
        {
            'title': 'Trigonométrie',
            'description': 'Les fonctions trigonométriques, identités remarquables et résolution d\'équations trigonométriques.',
            'level': 'intermediate',
        },
    ]
    
    created_courses = []
    for course_data in courses_data:
        course, created = Course.objects.get_or_create(
            title=course_data['title'],
            defaults=course_data
        )
        if created:
            print(f"✅ Cours créé: {course.title}")
        else:
            print(f"📚 Cours existant: {course.title}")
        created_courses.append(course)
    
    return created_courses

def create_demo_exercises(courses):
    """Créer des exercices de démonstration"""
    exercises_data = [
        {
            'title': 'Résoudre x + 5 = 12',
            'description': 'Trouvez la valeur de x dans cette équation simple.',
            'difficulty': 'easy',
            'course': courses[0],  # Algèbre de base
        },
        {
            'title': 'Équation du premier degré',
            'description': 'Résolvez 3x - 7 = 2x + 8',
            'difficulty': 'easy',
            'course': courses[0],
        },
        {
            'title': 'Équation quadratique simple',
            'description': 'Résolvez x² - 5x + 6 = 0',
            'difficulty': 'medium',
            'course': courses[1],  # Équations du second degré
        },
        {
            'title': 'Discriminant négatif',
            'description': 'Analysez l\'équation x² + x + 1 = 0',
            'difficulty': 'medium',
            'course': courses[1],
        },
        {
            'title': 'Système 2x2',
            'description': 'Résolvez le système: x + y = 5 et 2x - y = 1',
            'difficulty': 'medium',
            'course': courses[2],  # Systèmes d\'équations
        },
        {
            'title': 'Équation exponentielle',
            'description': 'Résolvez 2^x = 16',
            'difficulty': 'hard',
            'course': courses[3],  # Fonctions exponentielles
        },
        {
            'title': 'Identité trigonométrique',
            'description': 'Simplifiez sin²(x) + cos²(x)',
            'difficulty': 'medium',
            'course': courses[4],  # Trigonométrie
        },
        {
            'title': 'Équation trigonométrique',
            'description': 'Résolvez sin(x) = 1/2 sur [0, 2π]',
            'difficulty': 'hard',
            'course': courses[4],
        },
    ]
    
    created_exercises = []
    for exercise_data in exercises_data:
        exercise, created = Exercise.objects.get_or_create(
            title=exercise_data['title'],
            defaults=exercise_data
        )
        if created:
            print(f"✅ Exercice créé: {exercise.title}")
        else:
            print(f"📝 Exercice existant: {exercise.title}")
        created_exercises.append(exercise)
    
    return created_exercises

def create_demo_equations():
    """Créer quelques équations d'exemple"""
    equations_data = [
        {
            'equation_text': '2x + 3 = 11',
            'solution_text': 'x = 4',
        },
        {
            'equation_text': 'x² - 4 = 0',
            'solution_text': 'x = ±2',
        },
        {
            'equation_text': '3x - 7 = 2x + 8',
            'solution_text': 'x = 15',
        },
    ]
    
    # Obtenir un utilisateur étudiant pour associer les équations
    student_user = User.objects.filter(role='student').first()
    if not student_user:
        print("⚠️ Aucun utilisateur étudiant trouvé, création d'équations ignorée")
        return []
    
    created_equations = []
    for eq_data in equations_data:
        equation, created = Equation.objects.get_or_create(
            equation_text=eq_data['equation_text'],
            defaults={
                **eq_data,
                'user': student_user,
                'validated': True
            }
        )
        if created:
            print(f"✅ Équation créée: {equation.equation_text}")
        else:
            print(f"🧮 Équation existante: {equation.equation_text}")
        created_equations.append(equation)
    
    return created_equations

def main():
    """Fonction principale"""
    print("🚀 Création des données de démonstration pour HandyMath\n")
    
    # Créer les cours
    print("📚 Création des cours...")
    courses = create_demo_courses()
    print(f"Total: {len(courses)} cours\n")
    
    # Créer les exercices
    print("📝 Création des exercices...")
    exercises = create_demo_exercises(courses)
    print(f"Total: {len(exercises)} exercices\n")
    
    # Créer quelques équations d'exemple
    print("🧮 Création des équations d'exemple...")
    equations = create_demo_equations()
    print(f"Total: {len(equations)} équations\n")
    
    print("🎉 Données de démonstration créées avec succès !")
    print("\n📊 Résumé:")
    print(f"   Cours: {Course.objects.count()}")
    print(f"   Exercices: {Exercise.objects.count()}")
    print(f"   Équations: {Equation.objects.count()}")
    print(f"   Utilisateurs: {User.objects.count()}")

if __name__ == "__main__":
    main()
