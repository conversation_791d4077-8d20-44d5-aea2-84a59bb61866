{"ast": null, "code": "import { broadcastSizes, broadcastTo } from '../../../utils/array.js';\nimport { deepStrictEqual } from '../../../utils/object.js';\n\n/**\n* Broadcasts two matrices, and return both in an array\n* It checks if it's possible with broadcasting rules\n*\n* @param {Matrix}   A      First Matrix\n* @param {Matrix}   B      Second Matrix\n*\n* @return {Matrix[]}      [ broadcastedA, broadcastedB ]\n*/\n\nexport function broadcast(A, B) {\n  if (deepStrictEqual(A.size(), B.size())) {\n    // If matrices have the same size return them\n    return [A, B];\n  }\n\n  // calculate the broadcasted sizes\n  var newSize = broadcastSizes(A.size(), B.size());\n\n  // return the array with the two broadcasted matrices\n  return [A, B].map(M => _broadcastTo(M, newSize));\n}\n\n/**\n * Broadcasts a matrix to the given size.\n *\n * @param {Matrix} M - The matrix to be broadcasted.\n * @param {number[]} size - The desired size of the broadcasted matrix.\n * @returns {Matrix} The broadcasted matrix.\n * @throws {Error} If the size parameter is not an array of numbers.\n */\nfunction _broadcastTo(M, size) {\n  if (deepStrictEqual(M.size(), size)) {\n    return M;\n  }\n  return M.create(broadcastTo(M.valueOf(), size), M.datatype());\n}", "map": {"version": 3, "names": ["broadcastSizes", "broadcastTo", "deepStrictEqual", "broadcast", "A", "B", "size", "newSize", "map", "M", "_broadcastTo", "create", "valueOf", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/broadcast.js"], "sourcesContent": ["import { broadcastSizes, broadcastTo } from '../../../utils/array.js';\nimport { deepStrictEqual } from '../../../utils/object.js';\n\n/**\n* Broadcasts two matrices, and return both in an array\n* It checks if it's possible with broadcasting rules\n*\n* @param {Matrix}   A      First Matrix\n* @param {Matrix}   B      Second Matrix\n*\n* @return {Matrix[]}      [ broadcastedA, broadcastedB ]\n*/\n\nexport function broadcast(A, B) {\n  if (deepStrictEqual(A.size(), B.size())) {\n    // If matrices have the same size return them\n    return [A, B];\n  }\n\n  // calculate the broadcasted sizes\n  var newSize = broadcastSizes(A.size(), B.size());\n\n  // return the array with the two broadcasted matrices\n  return [A, B].map(M => _broadcastTo(M, newSize));\n}\n\n/**\n * Broadcasts a matrix to the given size.\n *\n * @param {Matrix} M - The matrix to be broadcasted.\n * @param {number[]} size - The desired size of the broadcasted matrix.\n * @returns {Matrix} The broadcasted matrix.\n * @throws {Error} If the size parameter is not an array of numbers.\n */\nfunction _broadcastTo(M, size) {\n  if (deepStrictEqual(M.size(), size)) {\n    return M;\n  }\n  return M.create(broadcastTo(M.valueOf(), size), M.datatype());\n}"], "mappings": "AAAA,SAASA,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AACrE,SAASC,eAAe,QAAQ,0BAA0B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC9B,IAAIH,eAAe,CAACE,CAAC,CAACE,IAAI,CAAC,CAAC,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE;IACvC;IACA,OAAO,CAACF,CAAC,EAAEC,CAAC,CAAC;EACf;;EAEA;EACA,IAAIE,OAAO,GAAGP,cAAc,CAACI,CAAC,CAACE,IAAI,CAAC,CAAC,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;;EAEhD;EACA,OAAO,CAACF,CAAC,EAAEC,CAAC,CAAC,CAACG,GAAG,CAACC,CAAC,IAAIC,YAAY,CAACD,CAAC,EAAEF,OAAO,CAAC,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACD,CAAC,EAAEH,IAAI,EAAE;EAC7B,IAAIJ,eAAe,CAACO,CAAC,CAACH,IAAI,CAAC,CAAC,EAAEA,IAAI,CAAC,EAAE;IACnC,OAAOG,CAAC;EACV;EACA,OAAOA,CAAC,CAACE,MAAM,CAACV,WAAW,CAACQ,CAAC,CAACG,OAAO,CAAC,CAAC,EAAEN,IAAI,CAAC,EAAEG,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}