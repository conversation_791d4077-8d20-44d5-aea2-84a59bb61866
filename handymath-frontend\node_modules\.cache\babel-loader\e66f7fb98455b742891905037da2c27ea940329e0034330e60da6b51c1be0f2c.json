{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CoursesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CoursesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    level: '',\n    featured: false\n  });\n  const [pagination, setPagination] = useState({\n    currentPage: 1,\n    itemsPerPage: 6,\n    totalItems: 0,\n    totalPages: 1\n  });\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters]);\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        const allCourses = [{\n          id: 1,\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          is_featured: true,\n          chapters_count: 4,\n          lessons_count: 12,\n          progress_percentage: 45,\n          is_accessible: true,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        }, {\n          id: 2,\n          title: 'Géométrie euclidienne',\n          description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 240,\n          is_featured: false,\n          chapters_count: 6,\n          lessons_count: 18,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-16T14:20:00Z'\n        }, {\n          id: 3,\n          title: 'Calcul différentiel',\n          description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '∫',\n          estimated_duration: 360,\n          is_featured: true,\n          chapters_count: 8,\n          lessons_count: 24,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [{\n            id: 1,\n            title: 'Algèbre de base',\n            progress: 45\n          }],\n          created_at: '2024-01-17T09:45:00Z'\n        }, {\n          id: 4,\n          title: 'Statistiques descriptives',\n          description: 'Apprenez les bases des statistiques et de l\\'analyse de données.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📊',\n          estimated_duration: 200,\n          is_featured: false,\n          chapters_count: 5,\n          lessons_count: 15,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-18T11:30:00Z'\n        }, {\n          id: 5,\n          title: 'Trigonométrie',\n          description: 'Maîtrisez les fonctions trigonométriques et leurs applications.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 280,\n          is_featured: true,\n          chapters_count: 6,\n          lessons_count: 20,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-19T14:15:00Z'\n        }, {\n          id: 6,\n          title: 'Analyse complexe',\n          description: 'Explorez les nombres complexes et leurs propriétés.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '🔢',\n          estimated_duration: 400,\n          is_featured: false,\n          chapters_count: 10,\n          lessons_count: 30,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [{\n            id: 3,\n            title: 'Calcul différentiel',\n            progress: 0\n          }],\n          created_at: '2024-01-20T16:45:00Z'\n        }];\n        setCourses(allCourses);\n        setPagination(prev => ({\n          ...prev,\n          totalItems: allCourses.length,\n          totalPages: Math.ceil(allCourses.length / prev.itemsPerPage)\n        }));\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async (courseId, courseTitle) => {\n    try {\n      setCourses(courses.map(course => course.id === courseId ? {\n        ...course,\n        is_enrolled: true,\n        enrollment_date: new Date().toISOString()\n      } : course));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  const filteredCourses = courses.filter(course => {\n    if (filters.level && course.level !== filters.level) return false;\n    if (filters.featured && !course.is_featured) return false;\n    return true;\n  });\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours Structur\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n      className: \"text-4xl font-bold mb-8 text-center text-primary-600\",\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: \"Cours Structur\\xE9s\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Filtres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Niveau\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.level,\n            onChange: e => setFilters(prev => ({\n              ...prev,\n              level: e.target.value\n            })),\n            className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Tous les niveaux\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"beginner\",\n              children: \"D\\xE9butant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"intermediate\",\n              children: \"Interm\\xE9diaire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"advanced\",\n              children: \"Avanc\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: filters.featured,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                featured: e.target.checked\n              })),\n              className: \"mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: \"Cours vedettes uniquement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setFilters({\n              level: '',\n              featured: false\n            }),\n            className: \"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n            children: \"Effacer filtres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600 dark:text-gray-400\",\n        children: [filteredCourses.length, \" cours trouv\\xE9\", filteredCourses.length !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 dark:text-gray-400\",\n        children: \"Chargement des cours...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this) : filteredCourses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-2\",\n        children: \"Aucun cours trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n        children: \"Essayez de modifier vos filtres ou revenez plus tard.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setFilters({\n          level: '',\n          featured: false\n        }),\n        className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n        children: \"Voir tous les cours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: filteredCourses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl mr-3\",\n                children: course.thumbnail\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-lg text-gray-900 dark:text-white\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 23\n                }, this), course.is_featured && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\",\n                  children: \"Vedette\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`,\n              children: course.level_display\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2\",\n            children: course.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.chapters_count, \" chapitres\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.lessons_count, \" le\\xE7ons\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: \"\\u23F1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatDuration(course.estimated_duration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.progress_percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 17\n          }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Progression\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.progress_percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${course.progress_percentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 19\n          }, this), course.prerequisites.length > 0 && !course.is_accessible && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\",\n              children: \"Pr\\xE9requis requis :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 21\n            }, this), course.prerequisites.map(prereq => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-yellow-700 dark:text-yellow-300\",\n              children: [\"\\u2022 \", prereq.title, \" (\", prereq.progress, \"% termin\\xE9)\"]\n            }, prereq.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 23\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [!course.is_accessible ? /*#__PURE__*/_jsxDEV(\"button\", {\n              disabled: true,\n              className: \"w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed\",\n              children: \"Non accessible\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 21\n            }, this) : !course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleEnroll(course.id, course.title),\n              className: \"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n              children: \"S'inscrire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/courses/${course.id}`,\n              className: \"block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors\",\n              children: course.progress_percentage > 0 ? 'Continuer' : 'Commencer'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/courses/${course.id}`,\n              className: \"block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors\",\n              children: \"Voir les d\\xE9tails\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 15\n        }, this)\n      }, course.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 5\n  }, this);\n};\n_s(CoursesPage, \"EskPkxP2WZaMlEQMiXPuIT75mHw=\", false, function () {\n  return [useAuth];\n});\n_c = CoursesPage;\nexport default CoursesPage;\nvar _c;\n$RefreshReg$(_c, \"CoursesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "jsxDEV", "_jsxDEV", "CoursesPage", "_s", "user", "courses", "setCourses", "loading", "setLoading", "filters", "setFilters", "level", "featured", "pagination", "setPagination", "currentPage", "itemsPerPage", "totalItems", "totalPages", "fetchCourses", "setTimeout", "allCourses", "id", "title", "description", "level_display", "thumbnail", "estimated_duration", "is_featured", "chapters_count", "lessons_count", "progress_percentage", "is_accessible", "is_enrolled", "enrollment_date", "prerequisites", "created_at", "progress", "prev", "length", "Math", "ceil", "error", "console", "handleEnroll", "courseId", "courseTitle", "map", "course", "Date", "toISOString", "alert", "getLevelColor", "formatDuration", "minutes", "hours", "floor", "mins", "filteredCourses", "filter", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "h1", "initial", "opacity", "y", "animate", "div", "transition", "delay", "value", "onChange", "e", "target", "type", "checked", "onClick", "index", "style", "width", "prereq", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/CoursesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentLayout from '../components/StudentLayout';\nimport Pagination from '../components/Pagination';\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  is_featured: boolean;\n  chapters_count: number;\n  lessons_count: number;\n  progress_percentage: number;\n  is_accessible: boolean;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\ninterface Filters {\n  level: string;\n  featured: boolean;\n}\n\ninterface PaginationState {\n  currentPage: number;\n  itemsPerPage: number;\n  totalItems: number;\n  totalPages: number;\n}\n\nconst CoursesPage: React.FC = () => {\n  const { user } = useAuth();\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    level: '',\n    featured: false\n  });\n  const [pagination, setPagination] = useState<PaginationState>({\n    currentPage: 1,\n    itemsPerPage: 6,\n    totalItems: 0,\n    totalPages: 1\n  });\n\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters]);\n\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        const allCourses = [\n          {\n            id: 1,\n            title: 'Algèbre de base',\n            description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n            level: 'beginner',\n            level_display: 'Débutant',\n            thumbnail: '📚',\n            estimated_duration: 180,\n            is_featured: true,\n            chapters_count: 4,\n            lessons_count: 12,\n            progress_percentage: 45,\n            is_accessible: true,\n            is_enrolled: true,\n            enrollment_date: '2024-01-15T10:30:00Z',\n            prerequisites: [],\n            created_at: '2024-01-15T10:30:00Z'\n          },\n          {\n            id: 2,\n            title: 'Géométrie euclidienne',\n            description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n            level: 'intermediate',\n            level_display: 'Intermédiaire',\n            thumbnail: '📐',\n            estimated_duration: 240,\n            is_featured: false,\n            chapters_count: 6,\n            lessons_count: 18,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-16T14:20:00Z'\n          },\n          {\n            id: 3,\n            title: 'Calcul différentiel',\n            description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n            level: 'advanced',\n            level_display: 'Avancé',\n            thumbnail: '∫',\n            estimated_duration: 360,\n            is_featured: true,\n            chapters_count: 8,\n            lessons_count: 24,\n            progress_percentage: 0,\n            is_accessible: false,\n            is_enrolled: false,\n            prerequisites: [\n              { id: 1, title: 'Algèbre de base', progress: 45 }\n            ],\n            created_at: '2024-01-17T09:45:00Z'\n          },\n          {\n            id: 4,\n            title: 'Statistiques descriptives',\n            description: 'Apprenez les bases des statistiques et de l\\'analyse de données.',\n            level: 'beginner',\n            level_display: 'Débutant',\n            thumbnail: '📊',\n            estimated_duration: 200,\n            is_featured: false,\n            chapters_count: 5,\n            lessons_count: 15,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-18T11:30:00Z'\n          },\n          {\n            id: 5,\n            title: 'Trigonométrie',\n            description: 'Maîtrisez les fonctions trigonométriques et leurs applications.',\n            level: 'intermediate',\n            level_display: 'Intermédiaire',\n            thumbnail: '📐',\n            estimated_duration: 280,\n            is_featured: true,\n            chapters_count: 6,\n            lessons_count: 20,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-19T14:15:00Z'\n          },\n          {\n            id: 6,\n            title: 'Analyse complexe',\n            description: 'Explorez les nombres complexes et leurs propriétés.',\n            level: 'advanced',\n            level_display: 'Avancé',\n            thumbnail: '🔢',\n            estimated_duration: 400,\n            is_featured: false,\n            chapters_count: 10,\n            lessons_count: 30,\n            progress_percentage: 0,\n            is_accessible: false,\n            is_enrolled: false,\n            prerequisites: [\n              { id: 3, title: 'Calcul différentiel', progress: 0 }\n            ],\n            created_at: '2024-01-20T16:45:00Z'\n          }\n        ];\n\n        setCourses(allCourses);\n        setPagination(prev => ({\n          ...prev,\n          totalItems: allCourses.length,\n          totalPages: Math.ceil(allCourses.length / prev.itemsPerPage)\n        }));\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async (courseId: number, courseTitle: string) => {\n    try {\n      setCourses(courses.map(course => \n        course.id === courseId \n          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }\n          : course\n      ));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  const filteredCourses = courses.filter(course => {\n    if (filters.level && course.level !== filters.level) return false;\n    if (filters.featured && !course.is_featured) return false;\n    return true;\n  });\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours Structurés</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <motion.h1\n        className=\"text-4xl font-bold mb-8 text-center text-primary-600\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        Cours Structurés\n      </motion.h1>\n\n      <motion.div\n        className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <h2 className=\"text-xl font-semibold mb-4\">\n          Filtres\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Niveau\n            </label>\n            <select\n              value={filters.level}\n              onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Tous les niveaux</option>\n              <option value=\"beginner\">Débutant</option>\n              <option value=\"intermediate\">Intermédiaire</option>\n              <option value=\"advanced\">Avancé</option>\n            </select>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Type\n            </label>\n            <label className=\"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700\">\n              <input\n                type=\"checkbox\"\n                checked={filters.featured}\n                onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}\n                className=\"mr-3\"\n              />\n              <span className=\"text-gray-900 dark:text-white\">Cours vedettes uniquement</span>\n            </label>\n          </div>\n          \n          <div className=\"flex items-end\">\n            <button\n              onClick={() => setFilters({ level: '', featured: false })}\n              className=\"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n            >\n              Effacer filtres\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n          {filteredCourses.length} cours trouvé{filteredCourses.length !== 1 ? 's' : ''}\n        </div>\n      </motion.div>\n\n      {loading ? (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement des cours...</p>\n        </div>\n      ) : filteredCourses.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-xl font-semibold mb-2\">Aucun cours trouvé</h3>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            Essayez de modifier vos filtres ou revenez plus tard.\n          </p>\n          <button\n            onClick={() => setFilters({ level: '', featured: false })}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n          >\n            Voir tous les cours\n          </button>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredCourses.map((course, index) => (\n            <motion.div\n              key={course.id}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-3xl mr-3\">{course.thumbnail}</span>\n                    <div>\n                      <h3 className=\"font-semibold text-lg text-gray-900 dark:text-white\">\n                        {course.title}\n                      </h3>\n                      {course.is_featured && (\n                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                          Vedette\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>\n                    {course.level_display}\n                  </span>\n                </div>\n\n                <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2\">\n                  {course.description}\n                </p>\n\n                <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  <div className=\"flex items-center\">\n                    <span>{course.chapters_count} chapitres</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span>{course.lessons_count} leçons</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">⏱</span>\n                    <span>{formatDuration(course.estimated_duration)}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span>{course.progress_percentage}%</span>\n                  </div>\n                </div>\n\n                {course.is_enrolled && (\n                  <div className=\"mb-4\">\n                    <div className=\"flex justify-between text-sm mb-1\">\n                      <span>Progression</span>\n                      <span>{course.progress_percentage}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div\n                        className=\"bg-primary-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${course.progress_percentage}%` }}\n                      />\n                    </div>\n                  </div>\n                )}\n\n                {course.prerequisites.length > 0 && !course.is_accessible && (\n                  <div className=\"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg\">\n                    <p className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                      Prérequis requis :\n                    </p>\n                    {course.prerequisites.map(prereq => (\n                      <div key={prereq.id} className=\"text-xs text-yellow-700 dark:text-yellow-300\">\n                        • {prereq.title} ({prereq.progress}% terminé)\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                <div className=\"space-y-2\">\n                  {!course.is_accessible ? (\n                    <button\n                      disabled\n                      className=\"w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed\"\n                    >\n                      Non accessible\n                    </button>\n                  ) : !course.is_enrolled ? (\n                    <button\n                      onClick={() => handleEnroll(course.id, course.title)}\n                      className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n                    >\n                      S'inscrire\n                    </button>\n                  ) : (\n                    <a\n                      href={`/courses/${course.id}`}\n                      className=\"block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors\"\n                    >\n                      {course.progress_percentage > 0 ? 'Continuer' : 'Commencer'}\n                    </a>\n                  )}\n                  \n                  <a\n                    href={`/courses/${course.id}`}\n                    className=\"block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors\"\n                  >\n                    Voir les détails\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CoursesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuClD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAU;IAC9Ce,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAkB;IAC5DmB,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEFrB,SAAS,CAAC,MAAM;IACd,IAAIO,IAAI,EAAE;MACRe,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,IAAI,EAAEK,OAAO,CAAC,CAAC;EAEnB,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChBY,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG,CACjB;UACEC,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,iBAAiB;UACxBC,WAAW,EAAE,yGAAyG;UACtHb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,UAAU;UACzBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,EAAE;UACvBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE,sBAAsB;UACvCC,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,uBAAuB;UAC9BC,WAAW,EAAE,oFAAoF;UACjGb,KAAK,EAAE,cAAc;UACrBc,aAAa,EAAE,eAAe;UAC9BC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,qBAAqB;UAC5BC,WAAW,EAAE,4DAA4D;UACzEb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,QAAQ;UACvBC,SAAS,EAAE,GAAG;UACdC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,KAAK;UACpBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,CACb;YAAEb,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE,iBAAiB;YAAEc,QAAQ,EAAE;UAAG,CAAC,CAClD;UACDD,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,2BAA2B;UAClCC,WAAW,EAAE,kEAAkE;UAC/Eb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,UAAU;UACzBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE,iEAAiE;UAC9Eb,KAAK,EAAE,cAAc;UACrBc,aAAa,EAAE,eAAe;UAC9BC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,kBAAkB;UACzBC,WAAW,EAAE,qDAAqD;UAClEb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,QAAQ;UACvBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,EAAE;UAClBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,KAAK;UACpBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,CACb;YAAEb,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE,qBAAqB;YAAEc,QAAQ,EAAE;UAAE,CAAC,CACrD;UACDD,UAAU,EAAE;QACd,CAAC,CACF;QAED9B,UAAU,CAACe,UAAU,CAAC;QACtBP,aAAa,CAACwB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPrB,UAAU,EAAEI,UAAU,CAACkB,MAAM;UAC7BrB,UAAU,EAAEsB,IAAI,CAACC,IAAI,CAACpB,UAAU,CAACkB,MAAM,GAAGD,IAAI,CAACtB,YAAY;QAC7D,CAAC,CAAC,CAAC;QACHR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjElC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,WAAmB,KAAK;IACpE,IAAI;MACFxC,UAAU,CAACD,OAAO,CAAC0C,GAAG,CAACC,MAAM,IAC3BA,MAAM,CAAC1B,EAAE,KAAKuB,QAAQ,GAClB;QAAE,GAAGG,MAAM;QAAEf,WAAW,EAAE,IAAI;QAAEC,eAAe,EAAE,IAAIe,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,GAC3EF,MACN,CAAC,CAAC;MACFG,KAAK,CAAC,iCAAiCL,WAAW,KAAK,CAAC;IAC1D,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDS,KAAK,CAAC,+BAA+B,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,aAAa,GAAIzC,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAM0C,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGf,IAAI,CAACgB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIE,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,MAAMC,eAAe,GAAGrD,OAAO,CAACsD,MAAM,CAACX,MAAM,IAAI;IAC/C,IAAIvC,OAAO,CAACE,KAAK,IAAIqC,MAAM,CAACrC,KAAK,KAAKF,OAAO,CAACE,KAAK,EAAE,OAAO,KAAK;IACjE,IAAIF,OAAO,CAACG,QAAQ,IAAI,CAACoC,MAAM,CAACpB,WAAW,EAAE,OAAO,KAAK;IACzD,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,IAAI,CAACxB,IAAI,EAAE;IACT,oBACEH,OAAA;MAAK2D,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5D,OAAA;UAAI2D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DhE,OAAA;UAAG2D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhE,OAAA;UACEiE,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhE,OAAA;IAAK2D,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C5D,OAAA,CAACH,MAAM,CAACqE,EAAE;MACRP,SAAS,EAAC,sDAAsD;MAChEQ,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC/B;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAEZhE,OAAA,CAACH,MAAM,CAAC0E,GAAG;MACTZ,SAAS,EAAC,yDAAyD;MACnEQ,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BG,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,gBAE3B5D,OAAA;QAAI2D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELhE,OAAA;QAAK2D,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzD5D,OAAA;UAAA4D,QAAA,gBACE5D,OAAA;YAAO2D,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRhE,OAAA;YACE0E,KAAK,EAAElE,OAAO,CAACE,KAAM;YACrBiE,QAAQ,EAAGC,CAAC,IAAKnE,UAAU,CAAC4B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE3B,KAAK,EAAEkE,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC1Ef,SAAS,EAAC,2HAA2H;YAAAC,QAAA,gBAErI5D,OAAA;cAAQ0E,KAAK,EAAC,EAAE;cAAAd,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ChE,OAAA;cAAQ0E,KAAK,EAAC,UAAU;cAAAd,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ChE,OAAA;cAAQ0E,KAAK,EAAC,cAAc;cAAAd,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnDhE,OAAA;cAAQ0E,KAAK,EAAC,UAAU;cAAAd,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhE,OAAA;UAAA4D,QAAA,gBACE5D,OAAA;YAAO2D,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRhE,OAAA;YAAO2D,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBACvH5D,OAAA;cACE8E,IAAI,EAAC,UAAU;cACfC,OAAO,EAAEvE,OAAO,CAACG,QAAS;cAC1BgE,QAAQ,EAAGC,CAAC,IAAKnE,UAAU,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,QAAQ,EAAEiE,CAAC,CAACC,MAAM,CAACE;cAAQ,CAAC,CAAC,CAAE;cAC/EpB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFhE,OAAA;cAAM2D,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhE,OAAA;UAAK2D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B5D,OAAA;YACEgF,OAAO,EAAEA,CAAA,KAAMvE,UAAU,CAAC;cAAEC,KAAK,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAE;YAC1DgD,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAC/G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,0CAA0C;QAAAC,QAAA,GACtDH,eAAe,CAACnB,MAAM,EAAC,kBAAa,EAACmB,eAAe,CAACnB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAEZ1D,OAAO,gBACNN,OAAA;MAAK2D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5D,OAAA;QAAK2D,SAAS,EAAC;MAAgF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtGhE,OAAA;QAAG2D,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,GACJP,eAAe,CAACnB,MAAM,KAAK,CAAC,gBAC9BtC,OAAA;MAAK2D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5D,OAAA;QAAI2D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClEhE,OAAA;QAAG2D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJhE,OAAA;QACEgF,OAAO,EAAEA,CAAA,KAAMvE,UAAU,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAE;QAC1DgD,SAAS,EAAC,mGAAmG;QAAAC,QAAA,EAC9G;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENhE,OAAA;MAAK2D,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEH,eAAe,CAACX,GAAG,CAAC,CAACC,MAAM,EAAEkC,KAAK,kBACjCjF,OAAA,CAACH,MAAM,CAAC0E,GAAG;QAETZ,SAAS,EAAC,kGAAkG;QAC5GQ,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAEQ,KAAK,GAAG;QAAI,CAAE;QAAArB,QAAA,eAEnC5D,OAAA;UAAK2D,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5D,OAAA;YAAK2D,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5D,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5D,OAAA;gBAAM2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEb,MAAM,CAACtB;cAAS;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDhE,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAI2D,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAChEb,MAAM,CAACzB;gBAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EACJjB,MAAM,CAACpB,WAAW,iBACjB3B,OAAA;kBAAM2D,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EAAC;gBAE5J;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhE,OAAA;cAAM2D,SAAS,EAAE,8CAA8CR,aAAa,CAACJ,MAAM,CAACrC,KAAK,CAAC,EAAG;cAAAkD,QAAA,EAC1Fb,MAAM,CAACvB;YAAa;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENhE,OAAA;YAAG2D,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EACtEb,MAAM,CAACxB;UAAW;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEJhE,OAAA;YAAK2D,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF5D,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC5D,OAAA;gBAAA4D,QAAA,GAAOb,MAAM,CAACnB,cAAc,EAAC,YAAU;cAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC5D,OAAA;gBAAA4D,QAAA,GAAOb,MAAM,CAAClB,aAAa,EAAC,YAAO;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5D,OAAA;gBAAM2D,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BhE,OAAA;gBAAA4D,QAAA,EAAOR,cAAc,CAACL,MAAM,CAACrB,kBAAkB;cAAC;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC5D,OAAA;gBAAA4D,QAAA,GAAOb,MAAM,CAACjB,mBAAmB,EAAC,GAAC;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELjB,MAAM,CAACf,WAAW,iBACjBhC,OAAA;YAAK2D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5D,OAAA;cAAK2D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5D,OAAA;gBAAA4D,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxBhE,OAAA;gBAAA4D,QAAA,GAAOb,MAAM,CAACjB,mBAAmB,EAAC,GAAC;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,sDAAsD;cAAAC,QAAA,eACnE5D,OAAA;gBACE2D,SAAS,EAAC,6DAA6D;gBACvEuB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGpC,MAAM,CAACjB,mBAAmB;gBAAI;cAAE;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAjB,MAAM,CAACb,aAAa,CAACI,MAAM,GAAG,CAAC,IAAI,CAACS,MAAM,CAAChB,aAAa,iBACvD/B,OAAA;YAAK2D,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClE5D,OAAA;cAAG2D,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACHjB,MAAM,CAACb,aAAa,CAACY,GAAG,CAACsC,MAAM,iBAC9BpF,OAAA;cAAqB2D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,SAC1E,EAACwB,MAAM,CAAC9D,KAAK,EAAC,IAAE,EAAC8D,MAAM,CAAChD,QAAQ,EAAC,eACrC;YAAA,GAFUgD,MAAM,CAAC/D,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDhE,OAAA;YAAK2D,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvB,CAACb,MAAM,CAAChB,aAAa,gBACpB/B,OAAA;cACEqF,QAAQ;cACR1B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAC9F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GACP,CAACjB,MAAM,CAACf,WAAW,gBACrBhC,OAAA;cACEgF,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAACI,MAAM,CAAC1B,EAAE,EAAE0B,MAAM,CAACzB,KAAK,CAAE;cACrDqC,SAAS,EAAC,0GAA0G;cAAAC,QAAA,EACrH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAEThE,OAAA;cACEiE,IAAI,EAAE,YAAYlB,MAAM,CAAC1B,EAAE,EAAG;cAC9BsC,SAAS,EAAC,wHAAwH;cAAAC,QAAA,EAEjIb,MAAM,CAACjB,mBAAmB,GAAG,CAAC,GAAG,WAAW,GAAG;YAAW;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CACJ,eAEDhE,OAAA;cACEiE,IAAI,EAAE,YAAYlB,MAAM,CAAC1B,EAAE,EAAG;cAC9BsC,SAAS,EAAC,oLAAoL;cAAAC,QAAA,EAC/L;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAzGDjB,MAAM,CAAC1B,EAAE;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0GJ,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9D,EAAA,CA3ZID,WAAqB;EAAA,QACRH,OAAO;AAAA;AAAAwF,EAAA,GADpBrF,WAAqB;AA6Z3B,eAAeA,WAAW;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}