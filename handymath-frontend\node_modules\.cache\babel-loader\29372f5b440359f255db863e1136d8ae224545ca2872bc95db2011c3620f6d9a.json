{"ast": null, "code": "export var sparseDocs = {\n  name: 'sparse',\n  category: 'Construction',\n  syntax: ['sparse()', 'sparse([a1, b1, ...; a1, b2, ...])', 'sparse([a1, b1, ...; a1, b2, ...], \"number\")'],\n  description: 'Create a sparse matrix.',\n  examples: ['sparse()', 'sparse([3, 4; 5, 6])', 'sparse([3, 0; 5, 0], \"number\")'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'number', 'string', 'unit', 'matrix']\n};", "map": {"version": 3, "names": ["sparseDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/sparse.js"], "sourcesContent": ["export var sparseDocs = {\n  name: 'sparse',\n  category: 'Construction',\n  syntax: ['sparse()', 'sparse([a1, b1, ...; a1, b2, ...])', 'sparse([a1, b1, ...; a1, b2, ...], \"number\")'],\n  description: 'Create a sparse matrix.',\n  examples: ['sparse()', 'sparse([3, 4; 5, 6])', 'sparse([3, 0; 5, 0], \"number\")'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'number', 'string', 'unit', 'matrix']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,EAAE,oCAAoC,EAAE,8CAA8C,CAAC;EAC1GC,WAAW,EAAE,yBAAyB;EACtCC,QAAQ,EAAE,CAAC,UAAU,EAAE,sBAAsB,EAAE,gCAAgC,CAAC;EAChFC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ;AAC5F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}