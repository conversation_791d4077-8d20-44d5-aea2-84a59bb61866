{"ast": null, "code": "export var modDocs = {\n  name: 'mod',\n  category: 'Operators',\n  syntax: ['x % y', 'x mod y', 'mod(x, y)'],\n  description: 'Calculates the modulus, the remainder of an integer division.',\n  examples: ['7 % 3', '11 % 2', '10 mod 4', 'isOdd(x) = x % 2', 'isOdd(2)', 'isOdd(3)'],\n  seealso: ['divide']\n};", "map": {"version": 3, "names": ["modDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/mod.js"], "sourcesContent": ["export var modDocs = {\n  name: 'mod',\n  category: 'Operators',\n  syntax: ['x % y', 'x mod y', 'mod(x, y)'],\n  description: 'Calculates the modulus, the remainder of an integer division.',\n  examples: ['7 % 3', '11 % 2', '10 mod 4', 'isOdd(x) = x % 2', 'isOdd(2)', 'isOdd(3)'],\n  seealso: ['divide']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC;EACzCC,WAAW,EAAE,+DAA+D;EAC5EC,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,kBAAkB,EAAE,UAAU,EAAE,UAAU,CAAC;EACrFC,OAAO,EAAE,CAAC,QAAQ;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}