{"ast": null, "code": "export var equalDocs = {\n  name: 'equal',\n  category: 'Relational',\n  syntax: ['x == y', 'equal(x, y)'],\n  description: 'Check equality of two values. Returns true if the values are equal, and false if not.',\n  examples: ['2+2 == 3', '2+2 == 4', 'a = 3.2', 'b = 6-2.8', 'a == b', '50cm == 0.5m'],\n  seealso: ['unequal', 'smaller', 'larger', 'smallerEq', 'largerEq', 'compare', 'deepEqual', 'equalText']\n};", "map": {"version": 3, "names": ["equalDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/equal.js"], "sourcesContent": ["export var equalDocs = {\n  name: 'equal',\n  category: 'Relational',\n  syntax: ['x == y', 'equal(x, y)'],\n  description: 'Check equality of two values. Returns true if the values are equal, and false if not.',\n  examples: ['2+2 == 3', '2+2 == 4', 'a = 3.2', 'b = 6-2.8', 'a == b', '50cm == 0.5m'],\n  seealso: ['unequal', 'smaller', 'larger', 'smallerEq', 'largerEq', 'compare', 'deepEqual', 'equalText']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;EACjCC,WAAW,EAAE,uFAAuF;EACpGC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,CAAC;EACpFC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW;AACxG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}