{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'matAlgo11xS0s';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo11xS0s = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix S nonzero items and invokes the callback function f(Sij, b).\n   * Callback function invoked NZ times (number of nonzero items in S).\n   *\n   *\n   *          ┌  f(Sij, b)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0          ; otherwise\n   *\n   *\n   * @param {Matrix}   s                 The SparseMatrix instance (S)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Sij)\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97626813\n   */\n  return function matAlgo11xS0s(s, b, callback, inverse) {\n    // sparse matrix arrays\n    var avalues = s._values;\n    var aindex = s._index;\n    var aptr = s._ptr;\n    var asize = s._size;\n    var adt = s._datatype;\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!avalues) {\n      throw new Error('Cannot perform operation on Pattern Sparse Matrix and Scalar value');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // convert b to the same datatype\n      b = typed.convert(b, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = [];\n    var cindex = [];\n    var cptr = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // initialize ptr\n      cptr[j] = cindex.length;\n      // values in j\n      for (var k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var i = aindex[k];\n        // invoke callback\n        var v = inverse ? cf(b, avalues[k]) : cf(avalues[k], b);\n        // check value is zero\n        if (!eq(v, zero)) {\n          // push index & value\n          cindex.push(i);\n          cvalues.push(v);\n        }\n      }\n    }\n    // update ptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return s.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: dt\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatAlgo11xS0s", "_ref", "typed", "equalScalar", "matAlgo11xS0s", "s", "b", "callback", "inverse", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "Error", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "j", "length", "k0", "k1", "k", "i", "v", "push", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo11xS0s.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nvar name = 'matAlgo11xS0s';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo11xS0s = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix S nonzero items and invokes the callback function f(Sij, b).\n   * Callback function invoked NZ times (number of nonzero items in S).\n   *\n   *\n   *          ┌  f(Sij, b)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0          ; otherwise\n   *\n   *\n   * @param {Matrix}   s                 The SparseMatrix instance (S)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Sij)\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97626813\n   */\n  return function matAlgo11xS0s(s, b, callback, inverse) {\n    // sparse matrix arrays\n    var avalues = s._values;\n    var aindex = s._index;\n    var aptr = s._ptr;\n    var asize = s._size;\n    var adt = s._datatype;\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!avalues) {\n      throw new Error('Cannot perform operation on Pattern Sparse Matrix and Scalar value');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // convert b to the same datatype\n      b = typed.convert(b, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = [];\n    var cindex = [];\n    var cptr = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // initialize ptr\n      cptr[j] = cindex.length;\n      // values in j\n      for (var k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var i = aindex[k];\n        // invoke callback\n        var v = inverse ? cf(b, avalues[k]) : cf(avalues[k], b);\n        // check value is zero\n        if (!eq(v, zero)) {\n          // push index & value\n          cindex.push(i);\n          cvalues.push(v);\n        }\n      }\n    }\n    // update ptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return s.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: dt\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,IAAI,GAAG,eAAe;AAC1B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,mBAAmB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAClF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACrD;IACA,IAAIC,OAAO,GAAGJ,CAAC,CAACK,OAAO;IACvB,IAAIC,MAAM,GAAGN,CAAC,CAACO,MAAM;IACrB,IAAIC,IAAI,GAAGR,CAAC,CAACS,IAAI;IACjB,IAAIC,KAAK,GAAGV,CAAC,CAACW,KAAK;IACnB,IAAIC,GAAG,GAAGZ,CAAC,CAACa,SAAS;;IAErB;IACA,IAAI,CAACT,OAAO,EAAE;MACZ,MAAM,IAAIU,KAAK,CAAC,oEAAoE,CAAC;IACvF;;IAEA;IACA,IAAIC,IAAI,GAAGL,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIM,OAAO,GAAGN,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIO,EAAE;IACN;IACA,IAAIC,EAAE,GAAGpB,WAAW;IACpB;IACA,IAAIqB,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAGlB,QAAQ;;IAEjB;IACA,IAAI,OAAOU,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACAK,EAAE,GAAGL,GAAG;MACR;MACAM,EAAE,GAAGrB,KAAK,CAACwB,IAAI,CAACvB,WAAW,EAAE,CAACmB,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAGtB,KAAK,CAACyB,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAhB,CAAC,GAAGJ,KAAK,CAACyB,OAAO,CAACrB,CAAC,EAAEgB,EAAE,CAAC;MACxB;MACAG,EAAE,GAAGvB,KAAK,CAACwB,IAAI,CAACnB,QAAQ,EAAE,CAACe,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,EAAEU,CAAC,EAAE,EAAE;MAChC;MACAD,IAAI,CAACC,CAAC,CAAC,GAAGF,MAAM,CAACG,MAAM;MACvB;MACA,KAAK,IAAIC,EAAE,GAAGpB,IAAI,CAACkB,CAAC,CAAC,EAAEG,EAAE,GAAGrB,IAAI,CAACkB,CAAC,GAAG,CAAC,CAAC,EAAEI,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;QAC5D;QACA,IAAIC,CAAC,GAAGzB,MAAM,CAACwB,CAAC,CAAC;QACjB;QACA,IAAIE,CAAC,GAAG7B,OAAO,GAAGiB,EAAE,CAACnB,CAAC,EAAEG,OAAO,CAAC0B,CAAC,CAAC,CAAC,GAAGV,EAAE,CAAChB,OAAO,CAAC0B,CAAC,CAAC,EAAE7B,CAAC,CAAC;QACvD;QACA,IAAI,CAACiB,EAAE,CAACc,CAAC,EAAEb,IAAI,CAAC,EAAE;UAChB;UACAK,MAAM,CAACS,IAAI,CAACF,CAAC,CAAC;UACdR,OAAO,CAACU,IAAI,CAACD,CAAC,CAAC;QACjB;MACF;IACF;IACA;IACAP,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACG,MAAM;;IAE7B;IACA,OAAO3B,CAAC,CAACkC,kBAAkB,CAAC;MAC1BC,MAAM,EAAEZ,OAAO;MACfa,KAAK,EAAEZ,MAAM;MACba,GAAG,EAAEZ,IAAI;MACTa,IAAI,EAAE,CAACvB,IAAI,EAAEC,OAAO,CAAC;MACrBuB,QAAQ,EAAEtB;IACZ,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}