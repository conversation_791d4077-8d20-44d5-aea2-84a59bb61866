{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\StudentBreadcrumbs.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentBreadcrumbs = ({\n  items,\n  className = '',\n  showHome = true\n}) => {\n  _s();\n  const location = useLocation();\n\n  // Génération automatique des breadcrumbs basée sur l'URL si aucun item n'est fourni\n  const generateBreadcrumbs = () => {\n    const pathSegments = location.pathname.split('/').filter(segment => segment !== '');\n    const breadcrumbs = [];\n\n    // Ajouter l'accueil si demandé\n    if (showHome) {\n      breadcrumbs.push({\n        label: 'Accueil',\n        path: '/',\n        icon: '🏠'\n      });\n    }\n\n    // Mapping spécialisé pour l'espace étudiant\n    const segmentLabels = {\n      'etudiant': 'Espace Étudiant',\n      'dashboard': 'Tableau de bord',\n      'courses': 'Mes Cours',\n      'lessons': 'Leçon',\n      'exercises': 'Exercices',\n      'progress': 'Ma Progression',\n      'solver': 'Résolveur',\n      'visualizer': 'Visualiseur',\n      'profile': 'Mon Profil',\n      'settings': 'Paramètres'\n    };\n    const segmentIcons = {\n      'etudiant': '🎓',\n      'dashboard': '📊',\n      'courses': '📚',\n      'lessons': '📖',\n      'exercises': '📝',\n      'progress': '📈',\n      'solver': '🧮',\n      'visualizer': '📊',\n      'profile': '👤',\n      'settings': '⚙️'\n    };\n    let currentPath = '';\n    pathSegments.forEach((segment, index) => {\n      currentPath += `/${segment}`;\n\n      // Ignorer les IDs numériques dans les breadcrumbs\n      if (/^\\d+$/.test(segment)) {\n        return;\n      }\n      const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);\n      const icon = segmentIcons[segment];\n\n      // Ne pas ajouter de lien pour le dernier segment (page actuelle)\n      const isLast = index === pathSegments.length - 1;\n      breadcrumbs.push({\n        label,\n        path: isLast ? undefined : currentPath,\n        icon,\n        isActive: isLast\n      });\n    });\n    return breadcrumbs;\n  };\n  const breadcrumbItems = items || generateBreadcrumbs();\n  if (breadcrumbItems.length <= 1) {\n    return null; // Ne pas afficher les breadcrumbs s'il n'y a qu'un seul élément\n  }\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: `flex items-center space-x-1 text-sm bg-gray-50 dark:bg-gray-800/50 px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`,\n    \"aria-label\": \"Fil d'Ariane\",\n    children: /*#__PURE__*/_jsxDEV(\"ol\", {\n      className: \"flex items-center space-x-1\",\n      children: breadcrumbItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"flex items-center\",\n        children: [index > 0 && /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4 text-gray-400 mx-2 flex-shrink-0\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 5l7 7-7 7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 15\n        }, this), item.path ? /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: \"flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 hover:underline\",\n          children: [item.icon && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-1.5 text-base\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `flex items-center font-semibold ${item.isActive ? 'text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white'}`,\n          children: [item.icon && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-1.5 text-base\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 19\n          }, this), item.label]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentBreadcrumbs, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = StudentBreadcrumbs;\nexport default StudentBreadcrumbs;\nvar _c;\n$RefreshReg$(_c, \"StudentBreadcrumbs\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "StudentBreadcrumbs", "items", "className", "showHome", "_s", "location", "generateBreadcrumbs", "pathSegments", "pathname", "split", "filter", "segment", "breadcrumbs", "push", "label", "path", "icon", "segmentLabels", "segmentIcons", "currentPath", "for<PERSON>ach", "index", "test", "char<PERSON>t", "toUpperCase", "slice", "isLast", "length", "undefined", "isActive", "breadcrumbItems", "children", "map", "item", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/StudentBreadcrumbs.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\ninterface BreadcrumbItem {\n  label: string;\n  path?: string;\n  icon?: string;\n  isActive?: boolean;\n}\n\ninterface StudentBreadcrumbsProps {\n  items?: BreadcrumbItem[];\n  className?: string;\n  showHome?: boolean;\n}\n\nconst StudentBreadcrumbs: React.FC<StudentBreadcrumbsProps> = ({ \n  items, \n  className = '',\n  showHome = true \n}) => {\n  const location = useLocation();\n  \n  // Génération automatique des breadcrumbs basée sur l'URL si aucun item n'est fourni\n  const generateBreadcrumbs = (): BreadcrumbItem[] => {\n    const pathSegments = location.pathname.split('/').filter(segment => segment !== '');\n    const breadcrumbs: BreadcrumbItem[] = [];\n    \n    // Ajouter l'accueil si demandé\n    if (showHome) {\n      breadcrumbs.push({ \n        label: 'Accueil', \n        path: '/', \n        icon: '🏠' \n      });\n    }\n\n    // Mapping spécialisé pour l'espace étudiant\n    const segmentLabels: { [key: string]: string } = {\n      'etudiant': 'Espace Étudiant',\n      'dashboard': 'Tableau de bord',\n      'courses': 'Mes Cours',\n      'lessons': 'Leçon',\n      'exercises': 'Exercices',\n      'progress': 'Ma Progression',\n      'solver': 'Résolveur',\n      'visualizer': 'Visualiseur',\n      'profile': 'Mon Profil',\n      'settings': 'Paramètres'\n    };\n\n    const segmentIcons: { [key: string]: string } = {\n      'etudiant': '🎓',\n      'dashboard': '📊',\n      'courses': '📚',\n      'lessons': '📖',\n      'exercises': '📝',\n      'progress': '📈',\n      'solver': '🧮',\n      'visualizer': '📊',\n      'profile': '👤',\n      'settings': '⚙️'\n    };\n\n    let currentPath = '';\n    pathSegments.forEach((segment, index) => {\n      currentPath += `/${segment}`;\n      \n      // Ignorer les IDs numériques dans les breadcrumbs\n      if (/^\\d+$/.test(segment)) {\n        return;\n      }\n      \n      const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);\n      const icon = segmentIcons[segment];\n      \n      // Ne pas ajouter de lien pour le dernier segment (page actuelle)\n      const isLast = index === pathSegments.length - 1;\n      \n      breadcrumbs.push({\n        label,\n        path: isLast ? undefined : currentPath,\n        icon,\n        isActive: isLast\n      });\n    });\n\n    return breadcrumbs;\n  };\n\n  const breadcrumbItems = items || generateBreadcrumbs();\n\n  if (breadcrumbItems.length <= 1) {\n    return null; // Ne pas afficher les breadcrumbs s'il n'y a qu'un seul élément\n  }\n\n  return (\n    <nav \n      className={`flex items-center space-x-1 text-sm bg-gray-50 dark:bg-gray-800/50 px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`} \n      aria-label=\"Fil d'Ariane\"\n    >\n      <ol className=\"flex items-center space-x-1\">\n        {breadcrumbItems.map((item, index) => (\n          <li key={index} className=\"flex items-center\">\n            {index > 0 && (\n              <svg\n                className=\"w-4 h-4 text-gray-400 mx-2 flex-shrink-0\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M9 5l7 7-7 7\"\n                />\n              </svg>\n            )}\n            \n            {item.path ? (\n              <Link\n                to={item.path}\n                className=\"flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 hover:underline\"\n              >\n                {item.icon && (\n                  <span className=\"mr-1.5 text-base\">{item.icon}</span>\n                )}\n                <span className=\"font-medium\">{item.label}</span>\n              </Link>\n            ) : (\n              <span className={`flex items-center font-semibold ${\n                item.isActive \n                  ? 'text-primary-600 dark:text-primary-400' \n                  : 'text-gray-900 dark:text-white'\n              }`}>\n                {item.icon && (\n                  <span className=\"mr-1.5 text-base\">{item.icon}</span>\n                )}\n                {item.label}\n              </span>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n};\n\nexport default StudentBreadcrumbs;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAerD,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,KAAK;EACLC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMS,mBAAmB,GAAGA,CAAA,KAAwB;IAClD,MAAMC,YAAY,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAK,EAAE,CAAC;IACnF,MAAMC,WAA6B,GAAG,EAAE;;IAExC;IACA,IAAIT,QAAQ,EAAE;MACZS,WAAW,CAACC,IAAI,CAAC;QACfC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,GAAG;QACTC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,aAAwC,GAAG;MAC/C,UAAU,EAAE,iBAAiB;MAC7B,WAAW,EAAE,iBAAiB;MAC9B,SAAS,EAAE,WAAW;MACtB,SAAS,EAAE,OAAO;MAClB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,gBAAgB;MAC5B,QAAQ,EAAE,WAAW;MACrB,YAAY,EAAE,aAAa;MAC3B,SAAS,EAAE,YAAY;MACvB,UAAU,EAAE;IACd,CAAC;IAED,MAAMC,YAAuC,GAAG;MAC9C,UAAU,EAAE,IAAI;MAChB,WAAW,EAAE,IAAI;MACjB,SAAS,EAAE,IAAI;MACf,SAAS,EAAE,IAAI;MACf,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE,IAAI;MAChB,QAAQ,EAAE,IAAI;MACd,YAAY,EAAE,IAAI;MAClB,SAAS,EAAE,IAAI;MACf,UAAU,EAAE;IACd,CAAC;IAED,IAAIC,WAAW,GAAG,EAAE;IACpBZ,YAAY,CAACa,OAAO,CAAC,CAACT,OAAO,EAAEU,KAAK,KAAK;MACvCF,WAAW,IAAI,IAAIR,OAAO,EAAE;;MAE5B;MACA,IAAI,OAAO,CAACW,IAAI,CAACX,OAAO,CAAC,EAAE;QACzB;MACF;MAEA,MAAMG,KAAK,GAAGG,aAAa,CAACN,OAAO,CAAC,IAAIA,OAAO,CAACY,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGb,OAAO,CAACc,KAAK,CAAC,CAAC,CAAC;MAC1F,MAAMT,IAAI,GAAGE,YAAY,CAACP,OAAO,CAAC;;MAElC;MACA,MAAMe,MAAM,GAAGL,KAAK,KAAKd,YAAY,CAACoB,MAAM,GAAG,CAAC;MAEhDf,WAAW,CAACC,IAAI,CAAC;QACfC,KAAK;QACLC,IAAI,EAAEW,MAAM,GAAGE,SAAS,GAAGT,WAAW;QACtCH,IAAI;QACJa,QAAQ,EAAEH;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOd,WAAW;EACpB,CAAC;EAED,MAAMkB,eAAe,GAAG7B,KAAK,IAAIK,mBAAmB,CAAC,CAAC;EAEtD,IAAIwB,eAAe,CAACH,MAAM,IAAI,CAAC,EAAE;IAC/B,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,oBACE5B,OAAA;IACEG,SAAS,EAAE,uIAAuIA,SAAS,EAAG;IAC9J,cAAW,cAAc;IAAA6B,QAAA,eAEzBhC,OAAA;MAAIG,SAAS,EAAC,6BAA6B;MAAA6B,QAAA,EACxCD,eAAe,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEZ,KAAK,kBAC/BtB,OAAA;QAAgBG,SAAS,EAAC,mBAAmB;QAAA6B,QAAA,GAC1CV,KAAK,GAAG,CAAC,iBACRtB,OAAA;UACEG,SAAS,EAAC,0CAA0C;UACpDgC,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,cAAc;UACrBC,OAAO,EAAC,WAAW;UAAAL,QAAA,eAEnBhC,OAAA;YACEsC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBC,WAAW,EAAE,CAAE;YACfC,CAAC,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAX,IAAI,CAAClB,IAAI,gBACRhB,OAAA,CAACH,IAAI;UACHiD,EAAE,EAAEZ,IAAI,CAAClB,IAAK;UACdb,SAAS,EAAC,sJAAsJ;UAAA6B,QAAA,GAE/JE,IAAI,CAACjB,IAAI,iBACRjB,OAAA;YAAMG,SAAS,EAAC,kBAAkB;YAAA6B,QAAA,EAAEE,IAAI,CAACjB;UAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACrD,eACD7C,OAAA;YAAMG,SAAS,EAAC,aAAa;YAAA6B,QAAA,EAAEE,IAAI,CAACnB;UAAK;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,gBAEP7C,OAAA;UAAMG,SAAS,EAAE,mCACf+B,IAAI,CAACJ,QAAQ,GACT,wCAAwC,GACxC,+BAA+B,EAClC;UAAAE,QAAA,GACAE,IAAI,CAACjB,IAAI,iBACRjB,OAAA;YAAMG,SAAS,EAAC,kBAAkB;YAAA6B,QAAA,EAAEE,IAAI,CAACjB;UAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACrD,EACAX,IAAI,CAACnB,KAAK;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACP;MAAA,GAtCMvB,KAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuCV,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACxC,EAAA,CAnIIJ,kBAAqD;EAAA,QAKxCH,WAAW;AAAA;AAAAiD,EAAA,GALxB9C,kBAAqD;AAqI3D,eAAeA,kBAAkB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}