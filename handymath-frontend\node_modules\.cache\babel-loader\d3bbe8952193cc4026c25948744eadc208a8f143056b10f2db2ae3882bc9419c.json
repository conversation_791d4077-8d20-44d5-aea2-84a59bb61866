{"ast": null, "code": "export var splitUnitDocs = {\n  name: 'splitUnit',\n  category: 'Construction',\n  syntax: ['splitUnit(unit: Unit, parts: Unit[])'],\n  description: 'Split a unit in an array of units whose sum is equal to the original unit.',\n  examples: ['splitUnit(1 m, [\"feet\", \"inch\"])'],\n  seealso: ['unit', 'createUnit']\n};", "map": {"version": 3, "names": ["splitUnitDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/splitUnit.js"], "sourcesContent": ["export var splitUnitDocs = {\n  name: 'splitUnit',\n  category: 'Construction',\n  syntax: ['splitUnit(unit: Unit, parts: Unit[])'],\n  description: 'Split a unit in an array of units whose sum is equal to the original unit.',\n  examples: ['splitUnit(1 m, [\"feet\", \"inch\"])'],\n  seealso: ['unit', 'createUnit']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,sCAAsC,CAAC;EAChDC,WAAW,EAAE,4EAA4E;EACzFC,QAAQ,EAAE,CAAC,kCAAkC,CAAC;EAC9CC,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}