import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../components/NotificationSystem';
import StudentLayout from '../components/StudentLayout';
import TabNavigation from '../components/TabNavigation';
import api from '../services/api';

// Types
interface GeneralStats {
  total_attempts: number;
  completed_attempts: number;
  correct_attempts: number;
  success_rate: number;
  total_points: number;
  average_time: number;
}

interface ProgressData {
  general_stats: GeneralStats;
}

const ProgressPage: React.FC = () => {
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // Helper functions for notifications
  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };

  // State
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [loading, setLoading] = useState(true);

  // Effects
  useEffect(() => {
    if (user) {
      fetchProgressData();
    }
  }, [user]);

  // API Functions
  const fetchProgressData = async () => {
    try {
      const response = await api.get('/progress/');
      if (response.data) {
        setProgressData(response.data);
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      showError('Erreur', 'Impossible de charger les statistiques de progression');
    } finally {
      setLoading(false);
    }
  };

  // Loading and Error States
  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Progression</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour voir vos statistiques de progression.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement des statistiques...</p>
        </div>
      </div>
    );
  }

  return (
    <StudentLayout
      title="Ma Progression"
      subtitle="Suivez vos statistiques et votre évolution"
    >
      <div className="space-y-6">

      {progressData && (
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h2 className="text-2xl font-semibold mb-6 flex items-center">
            📊 Statistiques Générales
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {progressData.general_stats.total_points}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Points Total</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {Math.round(progressData.general_stats.success_rate)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Taux de Réussite</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {progressData.general_stats.completed_attempts}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Exercices Terminés</div>
            </div>
          </div>
        </motion.div>
      )}

      {!progressData && !loading && (
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="text-6xl mb-4">📊</div>
          <h2 className="text-2xl font-semibold mb-4">Aucune donnée disponible</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Commencez à faire des exercices pour voir vos statistiques de progression !
          </p>
          <a
            href="/exercises"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Commencer les exercices
          </a>
        </motion.div>
      )}
    </div>
    </StudentLayout>
  );
};

export default ProgressPage;