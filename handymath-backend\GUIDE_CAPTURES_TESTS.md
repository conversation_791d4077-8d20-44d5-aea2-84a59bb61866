# 📸 GUIDE POUR CAPTURER LES TESTS PYTEST - RAPPORT PFA

## 🎯 **OBJECTIF**
<PERSON><PERSON><PERSON> des captures d'écran des tests pytest pour le rapport PFA HandyMath

## 📋 **PRÉPARATION**

### **1. Ouvrir PowerShell**
- Clic droit sur le bureau → "Ouvrir PowerShell ici"
- Ou : Windows + R → `powershell` → Entrée

### **2. Naviguer vers le projet**
```bash
cd C:\Users\<USER>\OneDrive\Desktop\HandyMath2\handymath-backend
```

### **3. Activer l'environnement virtuel**
```bash
venv_new\Scripts\activate
```

## 📸 **CAPTURES À RÉALISER**

### **CAPTURE 1 : Tests Mathématiques ✅**
**Commande :**
```bash
python -m pytest tests/test_math_basic.py -v
```

**Ce que ça montre :**
- 12 tests mathématiques qui passent
- Tests de résolution d'équations
- Tests SymPy
- Temps d'exécution

**📸 À capturer :** Toute la sortie avec les ✅ verts

---

### **CAPTURE 2 : Tests API ✅**
**Commande :**
```bash
python -m pytest tests/test_api_basic.py -v
```

**Ce que ça montre :**
- 11 tests API qui passent
- Tests de validation
- Tests de formatage
- Tests d'intégration

**📸 À capturer :** Toute la sortie avec les résultats

---

### **CAPTURE 3 : Couverture de Code 📊**
**Commande :**
```bash
python -m pytest tests/test_math_basic.py tests/test_api_basic.py --cov=api --cov-report=term
```

**Ce que ça montre :**
- 23 tests au total
- Pourcentage de couverture par fichier
- Statistiques détaillées

**📸 À capturer :** Le tableau de couverture complet

---

### **CAPTURE 4 : Test Spécifique 🔍**
**Commande :**
```bash
python -m pytest tests/test_math_basic.py::TestEquationSolver::test_linear_equation_solver -v -s
```

**Ce que ça montre :**
- Exécution d'un test particulier
- Détails du test de résolution d'équation

**📸 À capturer :** Résultat du test individuel

---

### **CAPTURE 5 : Résumé Global 📈**
**Commande :**
```bash
python -m pytest tests/test_math_basic.py tests/test_api_basic.py -v --tb=short
```

**Ce que ça montre :**
- Vue d'ensemble de tous les tests
- Temps total d'exécution
- Résumé final

**📸 À capturer :** Résumé complet avec statistiques

---

### **CAPTURE 6 : Configuration Pytest ⚙️**
**Commande :**
```bash
python -m pytest --version
```

**Ce que ça montre :**
- Version de pytest
- Plugins installés
- Configuration

**📸 À capturer :** Informations de version

---

### **CAPTURE 7 : Structure des Tests 📁**
**Commande :**
```bash
dir tests
```

**Ce que ça montre :**
- Liste des fichiers de test
- Organisation du code de test

**📸 À capturer :** Liste des fichiers

---

### **CAPTURE 8 : Test avec Échec (Optionnel) ❌**
**Commande :**
```bash
python -m pytest tests/test_simple.py::TestBasicFunctionality::test_user_creation -v
```

**Ce que ça montre :**
- Exemple de test qui échoue
- Message d'erreur détaillé
- Diagnostic du problème

**📸 À capturer :** Sortie d'erreur pour montrer la robustesse

## 🎨 **CONSEILS POUR LES CAPTURES**

### **1. Qualité de l'image**
- **Résolution :** Plein écran ou fenêtre maximisée
- **Police :** Taille lisible (Ctrl + molette pour agrandir)
- **Contraste :** Fond sombre avec texte clair (optionnel)

### **2. Contenu à inclure**
- **En-tête :** Ligne de commande visible
- **Corps :** Tous les résultats de tests
- **Pied :** Résumé final avec statistiques

### **3. Format recommandé**
- **Format :** PNG ou JPG
- **Noms de fichiers :**
  - `pytest_tests_math.png`
  - `pytest_tests_api.png`
  - `pytest_coverage.png`
  - `pytest_summary.png`

## 📝 **LÉGENDES POUR LE RAPPORT**

### **Pour chaque capture, ajoutez :**

**Figure X.1 : Tests mathématiques avec pytest**
> "Exécution des 12 tests mathématiques montrant la validation des fonctions de résolution d'équations linéaires et quadratiques avec SymPy."

**Figure X.2 : Tests API et validation**
> "Tests des 11 fonctions API incluant la validation des données, le formatage des réponses et l'intégration des composants."

**Figure X.3 : Couverture de code**
> "Rapport de couverture de code montrant 31% de couverture sur 23 tests exécutés avec succès."

**Figure X.4 : Résumé global des tests**
> "Vue d'ensemble des 23 tests pytest avec un taux de réussite de 100% pour les modules mathématiques et API."

## 🎯 **UTILISATION DANS LE RAPPORT**

### **Section "Tests et Validation"**
1. **Introduction :** "Pour assurer la qualité du code, nous avons implémenté une suite de tests automatisés avec pytest..."

2. **Méthodologie :** "Les tests sont organisés en plusieurs catégories : tests unitaires, tests d'intégration, et tests API..."

3. **Résultats :** "L'exécution des tests montre..." [INSÉRER CAPTURES]

4. **Analyse :** "La couverture de code atteint 31% avec..." [INSÉRER CAPTURE COUVERTURE]

5. **Conclusion :** "Les tests valident le bon fonctionnement des fonctionnalités critiques..."

## ⚡ **COMMANDES RAPIDES**

```bash
# Activation environnement
venv_new\Scripts\activate

# Tests principaux (pour captures)
python -m pytest tests/test_math_basic.py -v
python -m pytest tests/test_api_basic.py -v
python -m pytest tests/test_math_basic.py tests/test_api_basic.py --cov=api --cov-report=term

# Version et info
python -m pytest --version
dir tests
```

## 📊 **STATISTIQUES À MENTIONNER**

- **Framework :** pytest 8.4.0
- **Tests réussis :** 23/23 (100% pour les modules fonctionnels)
- **Couverture :** 31% du code API
- **Temps d'exécution :** ~3 secondes
- **Modules testés :** Mathématiques, API, Configuration
- **Technologies :** Django 5.2.2, SymPy, Python 3.12

---

**🎯 Avec ces captures, vous aurez une documentation visuelle complète de vos tests pytest pour le rapport PFA !**
