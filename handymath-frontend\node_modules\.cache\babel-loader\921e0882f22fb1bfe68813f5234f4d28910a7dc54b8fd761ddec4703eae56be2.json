{"ast": null, "code": "export var booleanDocs = {\n  name: 'boolean',\n  category: 'Construction',\n  syntax: ['x', 'boolean(x)'],\n  description: 'Convert a string or number into a boolean.',\n  examples: ['boolean(0)', 'boolean(1)', 'boolean(3)', 'boolean(\"true\")', 'boolean(\"false\")', 'boolean([1, 0, 1, 1])'],\n  seealso: ['bignumber', 'complex', 'index', 'matrix', 'number', 'string', 'unit']\n};", "map": {"version": 3, "names": ["booleanDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/boolean.js"], "sourcesContent": ["export var booleanDocs = {\n  name: 'boolean',\n  category: 'Construction',\n  syntax: ['x', 'boolean(x)'],\n  description: 'Convert a string or number into a boolean.',\n  examples: ['boolean(0)', 'boolean(1)', 'boolean(3)', 'boolean(\"true\")', 'boolean(\"false\")', 'boolean([1, 0, 1, 1])'],\n  seealso: ['bignumber', 'complex', 'index', 'matrix', 'number', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,GAAG,EAAE,YAAY,CAAC;EAC3BC,WAAW,EAAE,4CAA4C;EACzDC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,CAAC;EACpHC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AACjF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}