"""
Tests pour l'authentification et la gestion des utilisateurs
"""
import pytest
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from .factories import UserFactory, AdminUserFactory, StudentUserFactory


@pytest.mark.auth
@pytest.mark.django_db
class TestUserAuthentication:
    """Tests pour l'authentification des utilisateurs"""
    
    def test_user_registration_success(self, api_client, user_registration_data):
        """Test d'inscription réussie"""
        url = reverse('user-list')  # Ajustez selon vos URLs
        response = api_client.post(url, user_registration_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert User.objects.filter(username=user_registration_data['username']).exists()
        
        # Vérifier les données retournées
        assert 'id' in response.data
        assert response.data['username'] == user_registration_data['username']
        assert response.data['email'] == user_registration_data['email']
    
    def test_user_registration_duplicate_username(self, api_client, user):
        """Test d'inscription avec nom d'utilisateur existant"""
        registration_data = {
            "username": user.username,  # Username déjà existant
            "email": "<EMAIL>",
            "password": "testpassword123",
            "password_confirm": "testpassword123"
        }
        
        url = reverse('user-list')
        response = api_client.post(url, registration_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'username' in response.data
    
    def test_user_registration_invalid_email(self, api_client):
        """Test d'inscription avec email invalide"""
        registration_data = {
            "username": "testuser",
            "email": "invalid-email",
            "password": "testpassword123",
            "password_confirm": "testpassword123"
        }
        
        url = reverse('user-list')
        response = api_client.post(url, registration_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'email' in response.data
    
    def test_user_registration_password_mismatch(self, api_client):
        """Test d'inscription avec mots de passe différents"""
        registration_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "password_confirm": "differentpassword"
        }
        
        url = reverse('user-list')
        response = api_client.post(url, registration_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.auth
@pytest.mark.django_db
class TestJWTAuthentication:
    """Tests pour l'authentification JWT"""
    
    def test_obtain_jwt_token_success(self, api_client, user):
        """Test d'obtention de token JWT réussie"""
        user.set_password('testpassword123')
        user.save()
        
        login_data = {
            "username": user.username,
            "password": "testpassword123"
        }
        
        url = reverse('token_obtain_pair')
        response = api_client.post(url, login_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
        assert 'refresh' in response.data
    
    def test_obtain_jwt_token_invalid_credentials(self, api_client, user):
        """Test d'obtention de token avec identifiants invalides"""
        login_data = {
            "username": user.username,
            "password": "wrongpassword"
        }
        
        url = reverse('token_obtain_pair')
        response = api_client.post(url, login_data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_refresh_jwt_token(self, api_client, user):
        """Test de rafraîchissement du token JWT"""
        refresh = RefreshToken.for_user(user)
        
        refresh_data = {"refresh": str(refresh)}
        
        url = reverse('token_refresh')
        response = api_client.post(url, refresh_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
    
    def test_access_protected_endpoint_with_token(self, authenticated_client):
        """Test d'accès à un endpoint protégé avec token"""
        url = reverse('user-me')  # Endpoint pour obtenir les infos utilisateur
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'username' in response.data
    
    def test_access_protected_endpoint_without_token(self, api_client):
        """Test d'accès à un endpoint protégé sans token"""
        url = reverse('user-me')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.auth
@pytest.mark.django_db
class TestUserPermissions:
    """Tests pour les permissions utilisateur"""
    
    def test_admin_access_admin_endpoints(self, admin_client):
        """Test d'accès admin aux endpoints administrateur"""
        url = reverse('admin-users-list')  # Endpoint admin
        response = admin_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_student_denied_admin_endpoints(self, student_client):
        """Test de refus d'accès étudiant aux endpoints admin"""
        url = reverse('admin-users-list')
        response = student_client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_unauthenticated_denied_protected_endpoints(self, api_client):
        """Test de refus d'accès non authentifié aux endpoints protégés"""
        protected_urls = [
            reverse('user-me'),
            reverse('equations-list'),
            reverse('admin-users-list')
        ]
        
        for url in protected_urls:
            response = api_client.get(url)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.auth
@pytest.mark.django_db
class TestUserProfile:
    """Tests pour les profils utilisateur"""
    
    def test_get_current_user_profile(self, authenticated_client, user):
        """Test de récupération du profil utilisateur actuel"""
        url = reverse('user-me')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == user.id
        assert response.data['username'] == user.username
        assert response.data['email'] == user.email
    
    def test_update_user_profile(self, authenticated_client, user):
        """Test de mise à jour du profil utilisateur"""
        update_data = {
            "first_name": "Updated",
            "last_name": "Name",
            "email": "<EMAIL>"
        }
        
        url = reverse('user-me')
        response = authenticated_client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['first_name'] == update_data['first_name']
        assert response.data['last_name'] == update_data['last_name']
        assert response.data['email'] == update_data['email']
        
        # Vérifier en base de données
        user.refresh_from_db()
        assert user.first_name == update_data['first_name']
        assert user.last_name == update_data['last_name']
        assert user.email == update_data['email']
    
    def test_change_password(self, authenticated_client, user):
        """Test de changement de mot de passe"""
        user.set_password('oldpassword')
        user.save()
        
        password_data = {
            "old_password": "oldpassword",
            "new_password": "newpassword123",
            "new_password_confirm": "newpassword123"
        }
        
        url = reverse('user-change-password')
        response = authenticated_client.post(url, password_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        
        # Vérifier que le nouveau mot de passe fonctionne
        user.refresh_from_db()
        assert user.check_password('newpassword123')
    
    def test_change_password_wrong_old_password(self, authenticated_client, user):
        """Test de changement de mot de passe avec ancien mot de passe incorrect"""
        user.set_password('oldpassword')
        user.save()
        
        password_data = {
            "old_password": "wrongpassword",
            "new_password": "newpassword123",
            "new_password_confirm": "newpassword123"
        }
        
        url = reverse('user-change-password')
        response = authenticated_client.post(url, password_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'old_password' in response.data


@pytest.mark.auth
@pytest.mark.django_db
class TestUserFactory:
    """Tests pour les factories d'utilisateurs"""
    
    def test_user_factory_creates_valid_user(self):
        """Test que UserFactory crée un utilisateur valide"""
        user = UserFactory()
        
        assert user.id is not None
        assert user.username is not None
        assert user.email is not None
        assert '@' in user.email
        assert user.is_active is True
        assert user.is_staff is False
        assert user.is_superuser is False
    
    def test_admin_user_factory_creates_admin(self):
        """Test que AdminUserFactory crée un administrateur"""
        admin = AdminUserFactory()
        
        assert admin.is_staff is True
        assert admin.is_superuser is True
        assert 'admin' in admin.username
    
    def test_student_user_factory_creates_student(self):
        """Test que StudentUserFactory crée un étudiant"""
        student = StudentUserFactory()
        
        assert student.is_staff is False
        assert student.is_superuser is False
        assert 'student' in student.username
    
    def test_create_multiple_users(self):
        """Test de création de plusieurs utilisateurs"""
        users = UserFactory.create_batch(5)
        
        assert len(users) == 5
        assert all(user.id is not None for user in users)
        assert len(set(user.username for user in users)) == 5  # Tous uniques
