{"ast": null, "code": "export var crossDocs = {\n  name: 'cross',\n  category: 'Matrix',\n  syntax: ['cross(A, B)'],\n  description: 'Calculate the cross product for two vectors in three dimensional space.',\n  examples: ['cross([1, 1, 0],  [0, 1, 1])', 'cross([3, -3, 1], [4, 9, 2])', 'cross([2, 3, 4],  [5, 6, 7])'],\n  seealso: ['multiply', 'dot']\n};", "map": {"version": 3, "names": ["crossDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/cross.js"], "sourcesContent": ["export var crossDocs = {\n  name: 'cross',\n  category: 'Matrix',\n  syntax: ['cross(A, B)'],\n  description: 'Calculate the cross product for two vectors in three dimensional space.',\n  examples: ['cross([1, 1, 0],  [0, 1, 1])', 'cross([3, -3, 1], [4, 9, 2])', 'cross([2, 3, 4],  [5, 6, 7])'],\n  seealso: ['multiply', 'dot']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,aAAa,CAAC;EACvBC,WAAW,EAAE,yEAAyE;EACtFC,QAAQ,EAAE,CAAC,8BAA8B,EAAE,8BAA8B,EAAE,8BAA8B,CAAC;EAC1GC,OAAO,EAAE,CAAC,UAAU,EAAE,KAAK;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}