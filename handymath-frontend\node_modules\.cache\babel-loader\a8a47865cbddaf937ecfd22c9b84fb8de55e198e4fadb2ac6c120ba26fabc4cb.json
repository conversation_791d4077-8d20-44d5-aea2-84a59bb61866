{"ast": null, "code": "export var imDocs = {\n  name: 'im',\n  category: 'Complex',\n  syntax: ['im(x)'],\n  description: 'Get the imaginary part of a complex number.',\n  examples: ['im(2 + 3i)', 're(2 + 3i)', 'im(-5.2i)', 'im(2.4)'],\n  seealso: ['re', 'conj', 'abs', 'arg']\n};", "map": {"version": 3, "names": ["imDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/complex/im.js"], "sourcesContent": ["export var imDocs = {\n  name: 'im',\n  category: 'Complex',\n  syntax: ['im(x)'],\n  description: 'Get the imaginary part of a complex number.',\n  examples: ['im(2 + 3i)', 're(2 + 3i)', 'im(-5.2i)', 'im(2.4)'],\n  seealso: ['re', 'conj', 'abs', 'arg']\n};"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC;EAC9DC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}