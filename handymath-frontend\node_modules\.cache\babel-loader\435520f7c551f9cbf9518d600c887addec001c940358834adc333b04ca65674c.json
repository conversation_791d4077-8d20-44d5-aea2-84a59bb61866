{"ast": null, "code": "export var catalanDocs = {\n  name: 'catalan',\n  category: 'Combinatorics',\n  syntax: ['catalan(n)'],\n  description: 'The Catalan Numbers enumerate combinatorial structures of many different types. catalan only takes integer arguments. The following condition must be enforced: n >= 0.',\n  examples: ['catalan(3)', 'catalan(8)'],\n  seealso: ['bellNumbers']\n};", "map": {"version": 3, "names": ["catalanDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/combinatorics/catalan.js"], "sourcesContent": ["export var catalanDocs = {\n  name: 'catalan',\n  category: 'Combinatorics',\n  syntax: ['catalan(n)'],\n  description: 'The Catalan Numbers enumerate combinatorial structures of many different types. catalan only takes integer arguments. The following condition must be enforced: n >= 0.',\n  examples: ['catalan(3)', 'catalan(8)'],\n  seealso: ['bellNumbers']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,yKAAyK;EACtLC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;EACtCC,OAAO,EAAE,CAAC,aAAa;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}