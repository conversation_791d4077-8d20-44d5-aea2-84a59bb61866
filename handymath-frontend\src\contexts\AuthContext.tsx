import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

interface User {
  id: number;
  username: string;
  email: string;
  nom: string;
  prenom: string;
  role: string;
  niveau: string | undefined;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  register: (userData: any) => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      // OPTION 1: Désactiver complètement la persistance
      // setIsLoading(false);
      // return;

      // OPTION 2: Persistance avec expiration (recommandé)
      const storedUser = localStorage.getItem('user');
      const token = localStorage.getItem('authToken');
      const loginTime = localStorage.getItem('loginTime');

      // Vérifier si la session a expiré (ex: 24h)
      const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 heures en ms
      const now = new Date().getTime();

      if (loginTime && (now - parseInt(loginTime)) > SESSION_DURATION) {
        // Session expirée, nettoyer
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        localStorage.removeItem('loginTime');
        setIsLoading(false);
        return;
      }

      if (storedUser && token) {
        try {
          setUser(JSON.parse(storedUser));
        } catch (error) {
          localStorage.removeItem('authToken');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          localStorage.removeItem('loginTime');
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/token/`, {
        username,
        password
      });

      const { access, refresh } = response.data;

      localStorage.setItem('authToken', access);
      localStorage.setItem('refreshToken', refresh);
      localStorage.setItem('loginTime', new Date().getTime().toString());

      const userResponse = await axios.get(`${API_BASE_URL}/api/users/me/`, {
        headers: {
          'Authorization': `Bearer ${access}`
        }
      });

      setUser(userResponse.data);
      localStorage.setItem('user', JSON.stringify(userResponse.data));

      return true;
    } catch (error: any) {
      console.error('Erreur de connexion:', error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    localStorage.removeItem('loginTime');
    setUser(null);
  };

  const register = async (userData: any) => {
    setIsLoading(true);
    try {
      await axios.post(`${API_BASE_URL}/api/users/`, userData);
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
        register,
        updateUser
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};