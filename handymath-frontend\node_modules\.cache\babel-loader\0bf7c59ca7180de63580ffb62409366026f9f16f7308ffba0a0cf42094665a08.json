{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { mix } from './mix.mjs';\nimport { invariant } from './errors.mjs';\nimport { hslaToRgba } from './hsla-to-rgba.mjs';\nimport { hex } from '../value/types/color/hex.mjs';\nimport { rgba } from '../value/types/color/rgba.mjs';\nimport { hsla } from '../value/types/color/hsla.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n  const fromExpo = from * from;\n  return Math.sqrt(Math.max(0, v * (to * to - fromExpo) + fromExpo));\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = v => colorTypes.find(type => type.test(v));\nfunction asRGBA(color) {\n  const type = getColorType(color);\n  invariant(Boolean(type), \"'\".concat(color, \"' is not an animatable color. Use the equivalent color code instead.\"));\n  let model = type.parse(color);\n  if (type === hsla) {\n    // TODO Remove this cast - needed since Framer Motion's stricter typing\n    model = hslaToRgba(model);\n  }\n  return model;\n}\nconst mixColor = (from, to) => {\n  const fromRGBA = asRGBA(from);\n  const toRGBA = asRGBA(to);\n  const blended = _objectSpread({}, fromRGBA);\n  return v => {\n    blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n    blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n    blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n    blended.alpha = mix(fromRGBA.alpha, toRGBA.alpha, v);\n    return rgba.transform(blended);\n  };\n};\nexport { mixColor, mixLinearColor };", "map": {"version": 3, "names": ["mix", "invariant", "hslaToRgba", "hex", "rgba", "hsla", "mixLinearColor", "from", "to", "v", "fromExpo", "Math", "sqrt", "max", "colorTypes", "getColorType", "find", "type", "test", "asRGBA", "color", "Boolean", "concat", "model", "parse", "mixColor", "fromRGBA", "toRGBA", "blended", "_objectSpread", "red", "green", "blue", "alpha", "transform"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/framer-motion/dist/es/utils/mix-color.mjs"], "sourcesContent": ["import { mix } from './mix.mjs';\nimport { invariant } from './errors.mjs';\nimport { hslaToRgba } from './hsla-to-rgba.mjs';\nimport { hex } from '../value/types/color/hex.mjs';\nimport { rgba } from '../value/types/color/rgba.mjs';\nimport { hsla } from '../value/types/color/hsla.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    return Math.sqrt(Math.max(0, v * (to * to - fromExpo) + fromExpo));\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    invariant(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n    let model = type.parse(color);\n    if (type === hsla) {\n        // TODO Remove this cast - needed since Framer Motion's stricter typing\n        model = hslaToRgba(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = mix(fromRGBA.alpha, toRGBA.alpha, v);\n        return rgba.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n"], "mappings": ";AAAA,SAASA,GAAG,QAAQ,WAAW;AAC/B,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,GAAG,QAAQ,8BAA8B;AAClD,SAASC,IAAI,QAAQ,+BAA+B;AACpD,SAASC,IAAI,QAAQ,+BAA+B;;AAEpD;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,CAAC,KAAK;EACpC,MAAMC,QAAQ,GAAGH,IAAI,GAAGA,IAAI;EAC5B,OAAOI,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,CAAC,IAAID,EAAE,GAAGA,EAAE,GAAGE,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC;AACtE,CAAC;AACD,MAAMI,UAAU,GAAG,CAACX,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;AACpC,MAAMU,YAAY,GAAIN,CAAC,IAAKK,UAAU,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAACT,CAAC,CAAC,CAAC;AACnE,SAASU,MAAMA,CAACC,KAAK,EAAE;EACnB,MAAMH,IAAI,GAAGF,YAAY,CAACK,KAAK,CAAC;EAChCnB,SAAS,CAACoB,OAAO,CAACJ,IAAI,CAAC,MAAAK,MAAA,CAAMF,KAAK,yEAAsE,CAAC;EACzG,IAAIG,KAAK,GAAGN,IAAI,CAACO,KAAK,CAACJ,KAAK,CAAC;EAC7B,IAAIH,IAAI,KAAKZ,IAAI,EAAE;IACf;IACAkB,KAAK,GAAGrB,UAAU,CAACqB,KAAK,CAAC;EAC7B;EACA,OAAOA,KAAK;AAChB;AACA,MAAME,QAAQ,GAAGA,CAAClB,IAAI,EAAEC,EAAE,KAAK;EAC3B,MAAMkB,QAAQ,GAAGP,MAAM,CAACZ,IAAI,CAAC;EAC7B,MAAMoB,MAAM,GAAGR,MAAM,CAACX,EAAE,CAAC;EACzB,MAAMoB,OAAO,GAAAC,aAAA,KAAQH,QAAQ,CAAE;EAC/B,OAAQjB,CAAC,IAAK;IACVmB,OAAO,CAACE,GAAG,GAAGxB,cAAc,CAACoB,QAAQ,CAACI,GAAG,EAAEH,MAAM,CAACG,GAAG,EAAErB,CAAC,CAAC;IACzDmB,OAAO,CAACG,KAAK,GAAGzB,cAAc,CAACoB,QAAQ,CAACK,KAAK,EAAEJ,MAAM,CAACI,KAAK,EAAEtB,CAAC,CAAC;IAC/DmB,OAAO,CAACI,IAAI,GAAG1B,cAAc,CAACoB,QAAQ,CAACM,IAAI,EAAEL,MAAM,CAACK,IAAI,EAAEvB,CAAC,CAAC;IAC5DmB,OAAO,CAACK,KAAK,GAAGjC,GAAG,CAAC0B,QAAQ,CAACO,KAAK,EAAEN,MAAM,CAACM,KAAK,EAAExB,CAAC,CAAC;IACpD,OAAOL,IAAI,CAAC8B,SAAS,CAACN,OAAO,CAAC;EAClC,CAAC;AACL,CAAC;AAED,SAASH,QAAQ,EAAEnB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}