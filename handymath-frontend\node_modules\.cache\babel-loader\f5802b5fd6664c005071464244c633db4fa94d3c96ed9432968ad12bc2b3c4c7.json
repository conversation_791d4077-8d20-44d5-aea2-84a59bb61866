{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSumTransform } from '../../factoriesAny.js';\nexport var sumTransformDependencies = {\n  addDependencies,\n  numericDependencies,\n  typedDependencies,\n  createSumTransform\n};", "map": {"version": 3, "names": ["addDependencies", "numericDependencies", "typedDependencies", "createSumTransform", "sumTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSumTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSumTransform } from '../../factoriesAny.js';\nexport var sumTransformDependencies = {\n  addDependencies,\n  numericDependencies,\n  typedDependencies,\n  createSumTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCJ,eAAe;EACfC,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}