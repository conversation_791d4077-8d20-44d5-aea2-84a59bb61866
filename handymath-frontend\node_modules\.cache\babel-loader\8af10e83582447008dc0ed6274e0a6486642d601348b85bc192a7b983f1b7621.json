{"ast": null, "code": "export var acoshDocs = {\n  name: 'acosh',\n  category: 'Trigonometry',\n  syntax: ['acosh(x)'],\n  description: 'Calculate the hyperbolic arccos of a value, defined as `acosh(x) = ln(sqrt(x^2 - 1) + x)`.',\n  examples: ['acosh(1.5)'],\n  seealso: ['cosh', 'asinh', 'atanh']\n};", "map": {"version": 3, "names": ["acoshDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/acosh.js"], "sourcesContent": ["export var acoshDocs = {\n  name: 'acosh',\n  category: 'Trigonometry',\n  syntax: ['acosh(x)'],\n  description: 'Calculate the hyperbolic arccos of a value, defined as `acosh(x) = ln(sqrt(x^2 - 1) + x)`.',\n  examples: ['acosh(1.5)'],\n  seealso: ['cosh', 'asinh', 'atanh']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,4FAA4F;EACzGC,QAAQ,EAAE,CAAC,YAAY,CAAC;EACxBC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}