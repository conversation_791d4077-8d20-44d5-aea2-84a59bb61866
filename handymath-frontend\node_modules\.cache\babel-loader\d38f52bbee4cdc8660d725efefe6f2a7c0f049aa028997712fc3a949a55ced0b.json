{"ast": null, "code": "export var lsolveDocs = {\n  name: 'lsolve',\n  category: 'Algebra',\n  syntax: ['x=lsolve(L, b)'],\n  description: 'Finds one solution of the linear system L * x = b where L is an [n x n] lower triangular matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lsolve(a, b)'],\n  seealso: ['lsolveAll', 'lup', 'lusolve', 'usolve', 'matrix', 'sparse']\n};", "map": {"version": 3, "names": ["lsolveDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/lsolve.js"], "sourcesContent": ["export var lsolveDocs = {\n  name: 'lsolve',\n  category: 'Algebra',\n  syntax: ['x=lsolve(L, b)'],\n  description: 'Finds one solution of the linear system L * x = b where L is an [n x n] lower triangular matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lsolve(a, b)'],\n  seealso: ['lsolveAll', 'lup', 'lusolve', 'usolve', 'matrix', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,gBAAgB,CAAC;EAC1BC,WAAW,EAAE,+HAA+H;EAC5IC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,aAAa,EAAE,kBAAkB,CAAC;EAClEC,OAAO,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}