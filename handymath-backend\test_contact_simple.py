#!/usr/bin/env python
"""
Test simple de l'API de contact
"""
import requests
import json

def test_contact_api():
    """Test simple de l'API de contact"""
    print("🧪 Test de l'API de contact...")
    
    # URL de l'API
    url = "http://127.0.0.1:8000/api/contact/send/"
    
    # Données de test
    data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "category": "question",
        "subject": "Test Subject",
        "message": "Ceci est un message de test pour vérifier que l'API fonctionne correctement."
    }
    
    print(f"📤 Envoi vers: {url}")
    print(f"📤 Données: {json.dumps(data, indent=2)}")
    
    try:
        # Envoyer la requête POST
        response = requests.post(url, json=data)
        
        print(f"📥 Status Code: {response.status_code}")
        print(f"📥 Headers: {dict(response.headers)}")
        print(f"📥 Response: {response.text}")
        
        if response.status_code == 201:
            response_data = response.json()
            print("✅ Message envoyé avec succès!")
            print(f"🆔 ID du message: {response_data.get('contact_id')}")
            return True
        else:
            print("❌ Erreur lors de l'envoi du message")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur Django")
        print("   Assurez-vous que le serveur Django fonctionne sur http://127.0.0.1:8000/")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Test simple de l'API de contact\n")
    success = test_contact_api()
    
    if success:
        print("\n🎉 Test réussi! L'API de contact fonctionne correctement.")
    else:
        print("\n⚠️ Test échoué. Vérifiez les erreurs ci-dessus.")
