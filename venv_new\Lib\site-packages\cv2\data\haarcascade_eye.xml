<?xml version="1.0"?>
<!--
    Stump-based 20x20 frontal eye detector.
    Created by <PERSON><PERSON><PERSON> (http://umich.edu/~shameem)

////////////////////////////////////////////////////////////////////////////////////////

  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<cascade type_id="opencv-cascade-classifier"><stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>20</height>
  <width>20</width>
  <stageParams>
    <maxWeakCount>93</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount></featureParams>
  <stageNum>24</stageNum>
  <stages>
    <_>
      <maxWeakCount>6</maxWeakCount>
      <stageThreshold>-1.4562760591506958e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 0 1.2963959574699402e-01</internalNodes>
          <leafValues>
            -7.7304208278656006e-01 6.8350148200988770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -4.6326808631420135e-02</internalNodes>
          <leafValues>
            5.7352751493453979e-01 -4.9097689986228943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -1.6173090785741806e-02</internalNodes>
          <leafValues>
            6.0254341363906860e-01 -3.1610709428787231e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 -4.5828841626644135e-02</internalNodes>
          <leafValues>
            6.4177548885345459e-01 -1.5545040369033813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 -5.3759619593620300e-02</internalNodes>
          <leafValues>
            5.4219317436218262e-01 -2.0480829477310181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 3.4171190112829208e-02</internalNodes>
          <leafValues>
            -2.3388190567493439e-01 4.8410901427268982e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>12</maxWeakCount>
      <stageThreshold>-1.2550230026245117e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 6 -2.1727620065212250e-01</internalNodes>
          <leafValues>
            7.1098899841308594e-01 -5.9360730648040771e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 1.2071969918906689e-02</internalNodes>
          <leafValues>
            -2.8240481019020081e-01 5.9013551473617554e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 -1.7854139208793640e-02</internalNodes>
          <leafValues>
            5.3137522935867310e-01 -2.2758960723876953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 2.2333610802888870e-02</internalNodes>
          <leafValues>
            -1.7556099593639374e-01 6.3356137275695801e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 -9.1420017182826996e-02</internalNodes>
          <leafValues>
            6.1563092470169067e-01 -1.6899530589580536e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 2.8973650187253952e-02</internalNodes>
          <leafValues>
            -1.2250079959630966e-01 7.4401170015335083e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 7.8203463926911354e-03</internalNodes>
          <leafValues>
            1.6974370181560516e-01 -6.5441650152206421e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 2.0340489223599434e-02</internalNodes>
          <leafValues>
            -1.2556649744510651e-01 8.2710450887680054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -1.1926149949431419e-02</internalNodes>
          <leafValues>
            3.8605681061744690e-01 -2.0992340147495270e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -9.7281101625412703e-04</internalNodes>
          <leafValues>
            -6.3761192560195923e-01 1.2952390313148499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 1.8322050891583785e-05</internalNodes>
          <leafValues>
            -3.4631478786468506e-01 2.2924269735813141e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 -8.0854417756199837e-03</internalNodes>
          <leafValues>
            -6.3665801286697388e-01 1.3078659772872925e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-1.3728189468383789e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 18 -1.1812269687652588e-01</internalNodes>
          <leafValues>
            6.7844521999359131e-01 -5.0045782327651978e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 -3.4332759678363800e-02</internalNodes>
          <leafValues>
            6.7186361551284790e-01 -3.5744878649711609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 -2.1530799567699432e-02</internalNodes>
          <leafValues>
            7.2220700979232788e-01 -1.8192419409751892e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 -2.1909970790147781e-02</internalNodes>
          <leafValues>
            6.6529387235641479e-01 -2.7510228753089905e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 -2.8713539242744446e-02</internalNodes>
          <leafValues>
            6.9955700635910034e-01 -1.9615580141544342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 -1.1467480100691319e-02</internalNodes>
          <leafValues>
            5.9267348051071167e-01 -2.2097350656986237e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 -2.2611169144511223e-02</internalNodes>
          <leafValues>
            3.4483069181442261e-01 -3.8379558920860291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -1.9308089977130294e-03</internalNodes>
          <leafValues>
            -7.9445719718933105e-01 1.5628659725189209e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 5.6419910833938047e-05</internalNodes>
          <leafValues>
            -3.0896010994911194e-01 3.5431089997291565e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>16</maxWeakCount>
      <stageThreshold>-1.2879480123519897e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 27 1.9886520504951477e-01</internalNodes>
          <leafValues>
            -5.2860701084136963e-01 3.5536721348762512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -3.6008939146995544e-02</internalNodes>
          <leafValues>
            4.2109689116477966e-01 -3.9348980784416199e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 -7.7569849789142609e-02</internalNodes>
          <leafValues>
            4.7991541028022766e-01 -2.5122168660163879e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 8.2630853285081685e-05</internalNodes>
          <leafValues>
            -3.8475489616394043e-01 3.1849220395088196e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 3.2773229759186506e-04</internalNodes>
          <leafValues>
            -2.6427319645881653e-01 3.2547241449356079e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 -1.8574850633740425e-02</internalNodes>
          <leafValues>
            4.6736589074134827e-01 -1.5067270398139954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 -7.0008762122597545e-05</internalNodes>
          <leafValues>
            2.9313150048255920e-01 -2.5365099310874939e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -1.8552130088210106e-02</internalNodes>
          <leafValues>
            4.6273660659790039e-01 -1.3148050010204315e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 -1.3030420057475567e-02</internalNodes>
          <leafValues>
            4.1627219319343567e-01 -1.7751489579677582e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 6.5694141085259616e-05</internalNodes>
          <leafValues>
            -2.8035101294517517e-01 2.6680740714073181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 1.7005260451696813e-04</internalNodes>
          <leafValues>
            -2.7027249336242676e-01 2.3981650173664093e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -3.3129199873656034e-03</internalNodes>
          <leafValues>
            4.4411438703536987e-01 -1.4428889751434326e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 1.7583490116521716e-03</internalNodes>
          <leafValues>
            -1.6126190125942230e-01 4.2940768599510193e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 -2.5194749236106873e-02</internalNodes>
          <leafValues>
            4.0687298774719238e-01 -1.8202580511569977e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 1.4031709870323539e-03</internalNodes>
          <leafValues>
            8.4759786725044250e-02 -8.0018568038940430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 -7.3991729877889156e-03</internalNodes>
          <leafValues>
            5.5766099691390991e-01 -1.1843159794807434e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>23</maxWeakCount>
      <stageThreshold>-1.2179850339889526e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 43 -2.9943080618977547e-02</internalNodes>
          <leafValues>
            3.5810810327529907e-01 -3.8487631082534790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 -1.2567380070686340e-01</internalNodes>
          <leafValues>
            3.9316931366920471e-01 -3.0012258887290955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 5.3635272197425365e-03</internalNodes>
          <leafValues>
            -4.3908619880676270e-01 1.9257010519504547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 -8.0971820279955864e-03</internalNodes>
          <leafValues>
            3.9906668663024902e-01 -2.3407870531082153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -1.6597909852862358e-02</internalNodes>
          <leafValues>
            4.2095288634300232e-01 -2.2674840688705444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 -2.0199299324303865e-03</internalNodes>
          <leafValues>
            -7.4156731367111206e-01 1.2601189315319061e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 -1.5202340437099338e-03</internalNodes>
          <leafValues>
            -7.6154601573944092e-01 8.6373612284660339e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -4.9663940444588661e-03</internalNodes>
          <leafValues>
            4.2182239890098572e-01 -1.7904919385910034e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 -1.9207600504159927e-02</internalNodes>
          <leafValues>
            4.6894899010658264e-01 -1.4378750324249268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 -1.2222680263221264e-02</internalNodes>
          <leafValues>
            3.2842078804969788e-01 -2.1802149713039398e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 5.7548668235540390e-02</internalNodes>
          <leafValues>
            -3.6768808960914612e-01 2.4357110261917114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 -9.5794079825282097e-03</internalNodes>
          <leafValues>
            -7.2245067358016968e-01 6.3664563000202179e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 -2.9545740690082312e-03</internalNodes>
          <leafValues>
            3.5846439003944397e-01 -1.6696329414844513e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 -4.2017991654574871e-03</internalNodes>
          <leafValues>
            3.9094808697700500e-01 -1.2041790038347244e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 -1.3624990358948708e-02</internalNodes>
          <leafValues>
            -5.8767718076705933e-01 8.8404729962348938e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 6.2853112467564642e-05</internalNodes>
          <leafValues>
            -2.6348459720611572e-01 2.1419279277324677e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 -2.6782939676195383e-03</internalNodes>
          <leafValues>
            -7.8390169143676758e-01 8.0526962876319885e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 -7.0597179234027863e-02</internalNodes>
          <leafValues>
            4.1469261050224304e-01 -1.3989959657192230e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 9.2093646526336670e-02</internalNodes>
          <leafValues>
            -1.3055180013179779e-01 5.0435781478881836e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 -8.8004386052489281e-03</internalNodes>
          <leafValues>
            3.6609750986099243e-01 -1.4036649465560913e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 7.5080977694597095e-05</internalNodes>
          <leafValues>
            -2.9704439640045166e-01 2.0702940225601196e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 -2.9870450962334871e-03</internalNodes>
          <leafValues>
            3.5615700483322144e-01 -1.5445969998836517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 -2.6441509835422039e-03</internalNodes>
          <leafValues>
            -5.4353517293930054e-01 1.0295110195875168e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>27</maxWeakCount>
      <stageThreshold>-1.2905240058898926e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 66 -4.7862470149993896e-02</internalNodes>
          <leafValues>
            4.1528239846229553e-01 -3.4185820817947388e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 8.7350532412528992e-02</internalNodes>
          <leafValues>
            -3.8749781250953674e-01 2.4204200506210327e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -1.6849499195814133e-02</internalNodes>
          <leafValues>
            5.3082478046417236e-01 -1.7282910645008087e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 -2.8870029374957085e-02</internalNodes>
          <leafValues>
            3.5843509435653687e-01 -2.2402590513229370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 2.5679389946162701e-03</internalNodes>
          <leafValues>
            1.4990499615669250e-01 -6.5609407424926758e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -2.4116659536957741e-02</internalNodes>
          <leafValues>
            5.5889678001403809e-01 -1.4810280501842499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 -3.2826658338308334e-02</internalNodes>
          <leafValues>
            4.6468681097030640e-01 -1.0785529762506485e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 -1.5233060345053673e-02</internalNodes>
          <leafValues>
            -7.3954427242279053e-01 5.6236881762742996e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 -3.0209511169232428e-04</internalNodes>
          <leafValues>
            -4.5548820495605469e-01 9.7069837152957916e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 7.5365108205005527e-04</internalNodes>
          <leafValues>
            9.5147296786308289e-02 -5.4895019531250000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 -1.0638950392603874e-02</internalNodes>
          <leafValues>
            4.0912970900535583e-01 -1.2308409810066223e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 -7.5217830017209053e-03</internalNodes>
          <leafValues>
            4.0289148688316345e-01 -1.6048780083656311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 -1.0677099972963333e-01</internalNodes>
          <leafValues>
            6.1759322881698608e-01 -7.3091186583042145e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 1.6256919130682945e-02</internalNodes>
          <leafValues>
            -1.3103680312633514e-01 3.7453651428222656e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 -2.0679360255599022e-02</internalNodes>
          <leafValues>
            -7.1402907371520996e-01 5.2390009164810181e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 1.7052369192242622e-02</internalNodes>
          <leafValues>
            1.2822860479354858e-01 -3.1080681085586548e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 -5.7122060097754002e-03</internalNodes>
          <leafValues>
            -6.0556507110595703e-01 8.1884756684303284e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 2.0851430235779844e-05</internalNodes>
          <leafValues>
            -2.6812988519668579e-01 1.4453840255737305e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 7.9284431412816048e-03</internalNodes>
          <leafValues>
            -7.8795351088047028e-02 5.6762582063674927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 -2.5217379443347454e-03</internalNodes>
          <leafValues>
            3.7068629264831543e-01 -1.3620570302009583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 -2.2426199167966843e-02</internalNodes>
          <leafValues>
            -6.8704998493194580e-01 5.1062859594821930e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 -7.6451441273093224e-03</internalNodes>
          <leafValues>
            2.3492220044136047e-01 -1.7905959486961365e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 -1.1175329564139247e-03</internalNodes>
          <leafValues>
            -5.9869050979614258e-01 7.4324436485767365e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 1.9212789833545685e-02</internalNodes>
          <leafValues>
            -1.5702550113201141e-01 2.9737469553947449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 5.6293429806828499e-03</internalNodes>
          <leafValues>
            -9.9769018590450287e-02 4.2130270600318909e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 -9.5671862363815308e-03</internalNodes>
          <leafValues>
            -6.0858798027038574e-01 7.3506258428096771e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 1.1217960156500340e-02</internalNodes>
          <leafValues>
            -1.0320810228586197e-01 4.1909849643707275e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>28</maxWeakCount>
      <stageThreshold>-1.1600480079650879e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 93 -1.7486440017819405e-02</internalNodes>
          <leafValues>
            3.1307280063629150e-01 -3.3681181073188782e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 3.0714649707078934e-02</internalNodes>
          <leafValues>
            -1.8766190111637115e-01 5.3780800104141235e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 -2.2188719362020493e-02</internalNodes>
          <leafValues>
            3.6637881398200989e-01 -1.6124810278415680e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 -5.0700771680567414e-05</internalNodes>
          <leafValues>
            2.1245710551738739e-01 -2.8444620966911316e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 -7.0170420221984386e-03</internalNodes>
          <leafValues>
            3.9543110132217407e-01 -1.3173590600490570e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 -6.8563609384000301e-03</internalNodes>
          <leafValues>
            3.0373859405517578e-01 -2.0657819509506226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 -1.4129259623587132e-02</internalNodes>
          <leafValues>
            -7.6503008604049683e-01 9.8213188350200653e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -4.7915481030941010e-02</internalNodes>
          <leafValues>
            4.8307389020919800e-01 -1.3006809353828430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 4.7032979637151584e-05</internalNodes>
          <leafValues>
            -2.5216570496559143e-01 2.4386680126190186e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 1.0221180273219943e-03</internalNodes>
          <leafValues>
            6.8857602775096893e-02 -6.5861141681671143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 -2.6056109927594662e-03</internalNodes>
          <leafValues>
            4.2942029237747192e-01 -1.3022460043430328e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 5.4505340813193470e-05</internalNodes>
          <leafValues>
            -1.9288620352745056e-01 2.8958499431610107e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 -6.6721157054416835e-05</internalNodes>
          <leafValues>
            3.0290710926055908e-01 -1.9854369759559631e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 2.6281431317329407e-01</internalNodes>
          <leafValues>
            -2.3293940722942352e-01 2.3692460358142853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 -2.3569669574499130e-02</internalNodes>
          <leafValues>
            1.9401040673255920e-01 -2.8484618663787842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 -3.9120172150433064e-03</internalNodes>
          <leafValues>
            5.5378979444503784e-01 -9.5665678381919861e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 5.0788799853762612e-05</internalNodes>
          <leafValues>
            -2.3912659287452698e-01 2.1799489855766296e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 -7.8732017427682877e-03</internalNodes>
          <leafValues>
            4.0697428584098816e-01 -1.2768040597438812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 -1.6778609715402126e-03</internalNodes>
          <leafValues>
            -5.7744657993316650e-01 9.7324788570404053e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 -2.6832430739887059e-04</internalNodes>
          <leafValues>
            2.9021880030632019e-01 -1.6831269860267639e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 7.8687182394787669e-05</internalNodes>
          <leafValues>
            -1.9551570713520050e-01 2.7720969915390015e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 1.2953500263392925e-02</internalNodes>
          <leafValues>
            -9.6838317811489105e-02 4.0323871374130249e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 -1.3043959625065327e-02</internalNodes>
          <leafValues>
            4.7198569774627686e-01 -8.9287549257278442e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 3.0261781066656113e-03</internalNodes>
          <leafValues>
            -1.3623380661010742e-01 3.0686271190643311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 -6.0438038781285286e-03</internalNodes>
          <leafValues>
            -7.7954101562500000e-01 5.7316310703754425e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 -2.2507249377667904e-03</internalNodes>
          <leafValues>
            3.0877059698104858e-01 -1.5006309747695923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 1.5826810151338577e-02</internalNodes>
          <leafValues>
            6.4551889896392822e-02 -7.2455567121505737e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 6.5864507632795721e-05</internalNodes>
          <leafValues>
            -1.7598840594291687e-01 2.3210389912128448e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>36</maxWeakCount>
      <stageThreshold>-1.2257250547409058e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 121 -2.7854869142174721e-02</internalNodes>
          <leafValues>
            4.5518448948860168e-01 -1.8099910020828247e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 1.2895040214061737e-01</internalNodes>
          <leafValues>
            -5.2565532922744751e-01 1.6188900172710419e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 2.4403180927038193e-02</internalNodes>
          <leafValues>
            -1.4974960684776306e-01 4.2357379198074341e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -2.4458570405840874e-03</internalNodes>
          <leafValues>
            3.2948669791221619e-01 -1.7447690665721893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 -3.5336529836058617e-03</internalNodes>
          <leafValues>
            4.7426640987396240e-01 -7.3618359863758087e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 5.1358150813030079e-05</internalNodes>
          <leafValues>
            -3.0421930551528931e-01 1.5633270144462585e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 -1.6225680708885193e-02</internalNodes>
          <leafValues>
            2.3002180457115173e-01 -2.0359820127487183e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 -4.6007009223103523e-03</internalNodes>
          <leafValues>
            4.0459269285202026e-01 -1.3485440611839294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 -2.1928999572992325e-02</internalNodes>
          <leafValues>
            -6.8724489212036133e-01 8.0684266984462738e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 -2.8971210122108459e-03</internalNodes>
          <leafValues>
            -6.9619607925415039e-01 4.8545219004154205e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 -4.4074649922549725e-03</internalNodes>
          <leafValues>
            2.5166261196136475e-01 -1.6236649453639984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 2.8437169268727303e-02</internalNodes>
          <leafValues>
            6.0394261032342911e-02 -6.6744458675384521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 8.3212882280349731e-02</internalNodes>
          <leafValues>
            6.4357921481132507e-02 -5.3626042604446411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 -1.2419329956173897e-02</internalNodes>
          <leafValues>
            -7.0816862583160400e-01 5.7526610791683197e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 -4.6992599964141846e-03</internalNodes>
          <leafValues>
            5.1254332065582275e-01 -8.7350800633430481e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 -7.8025809489190578e-04</internalNodes>
          <leafValues>
            2.6687660813331604e-01 -1.7961509525775909e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 -1.9724339246749878e-02</internalNodes>
          <leafValues>
            -6.7563730478286743e-01 7.2941906750202179e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 1.0269250487908721e-03</internalNodes>
          <leafValues>
            5.3919319063425064e-02 -5.5540180206298828e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 -2.5957189500331879e-02</internalNodes>
          <leafValues>
            5.6362527608871460e-01 -7.1898393332958221e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 -1.2552699772641063e-03</internalNodes>
          <leafValues>
            -5.0346630811691284e-01 8.9691452682018280e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 -4.9970578402280807e-02</internalNodes>
          <leafValues>
            1.7685119807720184e-01 -2.2301959991455078e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -2.9899610672146082e-03</internalNodes>
          <leafValues>
            3.9122420549392700e-01 -1.0149750113487244e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 4.8546842299401760e-03</internalNodes>
          <leafValues>
            -1.1770179867744446e-01 4.2190939188003540e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 1.0448860120959580e-04</internalNodes>
          <leafValues>
            -1.7333979904651642e-01 2.2344440221786499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 5.9689260524464771e-05</internalNodes>
          <leafValues>
            -2.3409630358219147e-01 1.6558240354061127e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 -1.3423919677734375e-02</internalNodes>
          <leafValues>
            4.3023818731307983e-01 -9.9723652005195618e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 2.2581999655812979e-03</internalNodes>
          <leafValues>
            7.2720989584922791e-02 -5.7501018047332764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 -1.2546280398964882e-02</internalNodes>
          <leafValues>
            3.6184579133987427e-01 -1.1457010358572006e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 -2.8705769218504429e-03</internalNodes>
          <leafValues>
            2.8210538625717163e-01 -1.2367550283670425e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 1.9785640761256218e-02</internalNodes>
          <leafValues>
            4.7876749187707901e-02 -8.0666238069534302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 4.7588930465281010e-03</internalNodes>
          <leafValues>
            -1.0925389826297760e-01 3.3746978640556335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 -6.9974269717931747e-03</internalNodes>
          <leafValues>
            -8.0295938253402710e-01 4.5706700533628464e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 -1.3033480383455753e-02</internalNodes>
          <leafValues>
            1.8680439889431000e-01 -1.7688910663127899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 -1.3742579612880945e-03</internalNodes>
          <leafValues>
            2.7725479006767273e-01 -1.2809009850025177e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 2.7657810132950544e-03</internalNodes>
          <leafValues>
            9.0758942067623138e-02 -4.2594739794731140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 2.8941841446794569e-04</internalNodes>
          <leafValues>
            -3.8816329836845398e-01 8.9267797768115997e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>47</maxWeakCount>
      <stageThreshold>-1.2863140106201172e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 157 -1.4469229616224766e-02</internalNodes>
          <leafValues>
            3.7507829070091248e-01 -2.4928289651870728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 -1.3317629694938660e-01</internalNodes>
          <leafValues>
            3.0166378617286682e-01 -2.2414070367813110e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 -1.0132160037755966e-02</internalNodes>
          <leafValues>
            3.6985591053962708e-01 -1.7850010097026825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 -7.8511182218790054e-03</internalNodes>
          <leafValues>
            4.6086761355400085e-01 -1.2931390106678009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 -1.4295839704573154e-02</internalNodes>
          <leafValues>
            4.4841429591178894e-01 -1.0226240009069443e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 -5.9606940485537052e-03</internalNodes>
          <leafValues>
            2.7927988767623901e-01 -1.5323829650878906e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 1.0932769626379013e-02</internalNodes>
          <leafValues>
            -1.5141740441322327e-01 3.9889648556709290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 5.0430990086169913e-05</internalNodes>
          <leafValues>
            -2.2681570053100586e-01 2.1644389629364014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 -5.8431681245565414e-03</internalNodes>
          <leafValues>
            4.5420148968696594e-01 -1.2587159872055054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 -2.2346209734678268e-02</internalNodes>
          <leafValues>
            -6.2690192461013794e-01 8.2403123378753662e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 -4.8836669884622097e-03</internalNodes>
          <leafValues>
            2.6359251141548157e-01 -1.4686630666255951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 7.5506002758629620e-05</internalNodes>
          <leafValues>
            -2.4507020413875580e-01 1.6678880155086517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 -4.9026997294276953e-04</internalNodes>
          <leafValues>
            -4.2649960517883301e-01 8.9973561465740204e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 1.4861579984426498e-03</internalNodes>
          <leafValues>
            -1.2040250003337860e-01 3.0097651481628418e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 -1.1988339945673943e-02</internalNodes>
          <leafValues>
            2.7852478623390198e-01 -1.2244340032339096e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 1.0502239689230919e-02</internalNodes>
          <leafValues>
            4.0452759712934494e-02 -7.4050408601760864e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 173 -3.0963009223341942e-02</internalNodes>
          <leafValues>
            -6.2842690944671631e-01 4.8013761639595032e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 1.1414520442485809e-02</internalNodes>
          <leafValues>
            3.9405211806297302e-02 -7.1674120426177979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 -1.2337000109255314e-02</internalNodes>
          <leafValues>
            1.9941329956054688e-01 -1.9274300336837769e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 -5.9942267835140228e-03</internalNodes>
          <leafValues>
            5.1318162679672241e-01 -6.1658058315515518e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 -1.1923230485990644e-03</internalNodes>
          <leafValues>
            -7.2605299949645996e-01 5.0652720034122467e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 -7.4582789093255997e-03</internalNodes>
          <leafValues>
            2.9603078961372375e-01 -1.1754789948463440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 2.7877509128302336e-03</internalNodes>
          <leafValues>
            4.5068711042404175e-02 -6.9535410404205322e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 -2.2503209766000509e-04</internalNodes>
          <leafValues>
            2.0047250390052795e-01 -1.5775249898433685e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 -5.0367889925837517e-03</internalNodes>
          <leafValues>
            2.9299819469451904e-01 -1.1700499802827835e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 7.4742160737514496e-02</internalNodes>
          <leafValues>
            -1.1392319947481155e-01 3.0256620049476624e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 2.0255519077181816e-02</internalNodes>
          <leafValues>
            -1.0515890270471573e-01 4.0670460462570190e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 4.4214509427547455e-02</internalNodes>
          <leafValues>
            -2.7631640434265137e-01 1.2363869696855545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 -8.7259558495134115e-04</internalNodes>
          <leafValues>
            2.4355030059814453e-01 -1.3300949335098267e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 -2.4453739169985056e-03</internalNodes>
          <leafValues>
            -5.3866171836853027e-01 6.2510646879673004e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 8.2725353422574699e-05</internalNodes>
          <leafValues>
            -2.0772209763526917e-01 1.6270439326763153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 -3.6627110093832016e-02</internalNodes>
          <leafValues>
            3.6568409204483032e-01 -9.0330280363559723e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 3.0996399000287056e-03</internalNodes>
          <leafValues>
            -1.3183020055294037e-01 2.5354298949241638e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 -2.4709280114620924e-03</internalNodes>
          <leafValues>
            -5.6853497028350830e-01 5.3505431860685349e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 -1.4114670455455780e-02</internalNodes>
          <leafValues>
            -4.8599010705947876e-01 5.8485250920057297e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 8.4537261864170432e-04</internalNodes>
          <leafValues>
            -8.0093637108802795e-02 4.0265649557113647e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 -7.1098632179200649e-03</internalNodes>
          <leafValues>
            4.4703239202499390e-01 -6.2947437167167664e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 -1.9125960767269135e-02</internalNodes>
          <leafValues>
            -6.6422867774963379e-01 4.9822770059108734e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 -5.0773010589182377e-03</internalNodes>
          <leafValues>
            1.7379400134086609e-01 -1.6850599646568298e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 -2.9198289848864079e-03</internalNodes>
          <leafValues>
            -6.0110282897949219e-01 5.7427939027547836e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 -2.4902150034904480e-02</internalNodes>
          <leafValues>
            2.3397980630397797e-01 -1.1818459630012512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 2.0147779956459999e-02</internalNodes>
          <leafValues>
            -8.9459821581840515e-02 3.6024400591850281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 1.7597640398889780e-03</internalNodes>
          <leafValues>
            4.9458440393209457e-02 -6.3102620840072632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 1.3812039978802204e-03</internalNodes>
          <leafValues>
            -1.5218059718608856e-01 1.8971739709377289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 -1.0904540307819843e-02</internalNodes>
          <leafValues>
            -5.8097380399703979e-01 4.4862728565931320e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 7.5157178798690438e-05</internalNodes>
          <leafValues>
            -1.3777349889278412e-01 1.9543160498142242e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 3.8649770431220531e-03</internalNodes>
          <leafValues>
            -1.0302229970693588e-01 2.5374969840049744e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>48</maxWeakCount>
      <stageThreshold>-1.1189440488815308e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 204 -1.0215889662504196e-01</internalNodes>
          <leafValues>
            4.1681259870529175e-01 -1.6655629873275757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 -5.1939819008111954e-02</internalNodes>
          <leafValues>
            3.3023950457572937e-01 -2.0715710520744324e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 -4.2717780917882919e-02</internalNodes>
          <leafValues>
            2.6093730330467224e-01 -1.6013890504837036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 4.3890418601222336e-04</internalNodes>
          <leafValues>
            -3.4750530123710632e-01 1.3918919861316681e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 2.4264389649033546e-02</internalNodes>
          <leafValues>
            -4.2552059888839722e-01 1.3578380644321442e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 -2.3820599541068077e-02</internalNodes>
          <leafValues>
            3.1749808788299561e-01 -1.6652040183544159e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 -7.0518180727958679e-03</internalNodes>
          <leafValues>
            3.0947178602218628e-01 -1.3338300585746765e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 -6.8517157342284918e-04</internalNodes>
          <leafValues>
            -6.0082262754440308e-01 8.7747000157833099e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 5.3705149330198765e-03</internalNodes>
          <leafValues>
            -1.2311449646949768e-01 3.8333550095558167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 -1.3403539545834064e-02</internalNodes>
          <leafValues>
            3.3877369761466980e-01 -1.0140489786863327e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 -6.6856360062956810e-03</internalNodes>
          <leafValues>
            -6.1193597316741943e-01 4.7740221023559570e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 -4.2887418530881405e-03</internalNodes>
          <leafValues>
            2.5275790691375732e-01 -1.4434510469436646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 -1.0876749642193317e-02</internalNodes>
          <leafValues>
            5.4775732755661011e-01 -5.9455480426549911e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 3.7882640026509762e-04</internalNodes>
          <leafValues>
            8.3410300314426422e-02 -4.4226369261741638e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 -2.4550149682909250e-03</internalNodes>
          <leafValues>
            2.3330999910831451e-01 -1.3964480161666870e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 1.2721839593723416e-03</internalNodes>
          <leafValues>
            6.0480289161205292e-02 -4.9456089735031128e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 -4.8933159559965134e-03</internalNodes>
          <leafValues>
            -6.6833269596099854e-01 4.6218499541282654e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 2.6449989527463913e-02</internalNodes>
          <leafValues>
            -7.3235362768173218e-02 4.4425961375236511e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 -3.3706070389598608e-03</internalNodes>
          <leafValues>
            -4.2464339733123779e-01 6.8676561117172241e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 -2.9559480026364326e-03</internalNodes>
          <leafValues>
            1.6218039393424988e-01 -1.8222999572753906e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 3.0619909986853600e-02</internalNodes>
          <leafValues>
            -5.8643341064453125e-02 5.3263628482818604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 -9.5765907317399979e-03</internalNodes>
          <leafValues>
            -6.0562682151794434e-01 5.3345989435911179e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 6.6372493165545166e-05</internalNodes>
          <leafValues>
            -1.6680839657783508e-01 1.9284160435199738e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 5.0975950434803963e-03</internalNodes>
          <leafValues>
            4.4119510799646378e-02 -5.7458841800689697e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 3.7112718564458191e-04</internalNodes>
          <leafValues>
            -1.1086399853229523e-01 2.3105390369892120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 -8.6607588455080986e-03</internalNodes>
          <leafValues>
            4.0456289052963257e-01 -6.2446091324090958e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 8.7489158613607287e-04</internalNodes>
          <leafValues>
            6.4875148236751556e-02 -4.4871041178703308e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 1.1120870476588607e-03</internalNodes>
          <leafValues>
            -9.3861460685729980e-02 3.0453911423683167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 -2.3837819695472717e-02</internalNodes>
          <leafValues>
            -5.8887428045272827e-01 4.6659421175718307e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 2.2272899514064193e-04</internalNodes>
          <leafValues>
            -1.4898599684238434e-01 1.7701950669288635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 2.4467470124363899e-02</internalNodes>
          <leafValues>
            -5.5789601057767868e-02 4.9208301305770874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 -1.4239320158958435e-01</internalNodes>
          <leafValues>
            1.5192000567913055e-01 -1.8778899312019348e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 -2.0123120397329330e-02</internalNodes>
          <leafValues>
            2.1780100464820862e-01 -1.2081900238990784e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 1.1513679783092812e-04</internalNodes>
          <leafValues>
            -1.6856589913368225e-01 1.6451929509639740e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 -2.7556740678846836e-03</internalNodes>
          <leafValues>
            -6.9442039728164673e-01 3.9449468255043030e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 -7.5843912782147527e-05</internalNodes>
          <leafValues>
            1.8941369652748108e-01 -1.5183840692043304e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 -7.0697711780667305e-03</internalNodes>
          <leafValues>
            4.7064599394798279e-01 -5.7927619665861130e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 -3.7393178790807724e-02</internalNodes>
          <leafValues>
            -7.5892448425292969e-01 3.4116048365831375e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 -1.5995610505342484e-02</internalNodes>
          <leafValues>
            3.0670469999313354e-01 -8.7525576353073120e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 -3.1183990649878979e-03</internalNodes>
          <leafValues>
            2.6195371150970459e-01 -9.1214887797832489e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 1.0651360498741269e-03</internalNodes>
          <leafValues>
            -1.7427560687065125e-01 1.5277640521526337e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 -1.6029420075938106e-03</internalNodes>
          <leafValues>
            3.5612630844116211e-01 -7.6629996299743652e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 4.3619908392429352e-03</internalNodes>
          <leafValues>
            4.9356970936059952e-02 -5.9228771924972534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 -1.0779909789562225e-02</internalNodes>
          <leafValues>
            -6.3922178745269775e-01 3.3204540610313416e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 -4.3590869754552841e-03</internalNodes>
          <leafValues>
            1.6107389330863953e-01 -1.5221320092678070e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 7.4596069753170013e-03</internalNodes>
          <leafValues>
            3.3172961324453354e-02 -7.5007742643356323e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 250 8.1385448575019836e-03</internalNodes>
          <leafValues>
            2.6325279846787453e-02 -7.1731162071228027e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 -3.3338490873575211e-02</internalNodes>
          <leafValues>
            3.3536610007286072e-01 -7.0803590118885040e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>55</maxWeakCount>
      <stageThreshold>-1.1418989896774292e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 252 1.9553979858756065e-02</internalNodes>
          <leafValues>
            -1.0439720004796982e-01 5.3128951787948608e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 2.2122919559478760e-02</internalNodes>
          <leafValues>
            -2.4747270345687866e-01 2.0847250521183014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 -4.1829389519989491e-03</internalNodes>
          <leafValues>
            3.8289439678192139e-01 -1.4711579680442810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 -8.6381728760898113e-04</internalNodes>
          <leafValues>
            -6.2632888555526733e-01 1.1993259936571121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 7.9958612332120538e-04</internalNodes>
          <leafValues>
            9.2573471367359161e-02 -5.5168831348419189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 9.1527570039033890e-03</internalNodes>
          <leafValues>
            -7.2929807007312775e-02 5.5512511730194092e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 -3.9388681761920452e-03</internalNodes>
          <leafValues>
            2.0196039974689484e-01 -2.0912039279937744e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 1.4613410166930407e-04</internalNodes>
          <leafValues>
            -2.7861818671226501e-01 1.3817410171031952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -3.1691689509898424e-03</internalNodes>
          <leafValues>
            3.6685898900032043e-01 -7.6308242976665497e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 -2.2189389914274216e-02</internalNodes>
          <leafValues>
            3.9096599817276001e-01 -1.0971540212631226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 -7.4523608200252056e-03</internalNodes>
          <leafValues>
            1.2838590145111084e-01 -2.4159869551658630e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 7.7997002517804503e-04</internalNodes>
          <leafValues>
            7.1978069841861725e-02 -4.3976500630378723e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 -4.6783639118075371e-03</internalNodes>
          <leafValues>
            2.1569849550724030e-01 -1.4205920696258545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 -1.5188639983534813e-02</internalNodes>
          <leafValues>
            3.6458781361579895e-01 -8.2675926387310028e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 5.0619798712432384e-03</internalNodes>
          <leafValues>
            -3.4380409121513367e-01 9.2068232595920563e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 -1.7351920250803232e-03</internalNodes>
          <leafValues>
            -6.1725497245788574e-01 4.9214478582143784e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 -1.2423450127243996e-02</internalNodes>
          <leafValues>
            -5.8558952808380127e-01 4.6112600713968277e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 -1.3031429611146450e-02</internalNodes>
          <leafValues>
            -5.9710788726806641e-01 4.0672458708286285e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 -1.2369629694148898e-03</internalNodes>
          <leafValues>
            -6.8334168195724487e-01 3.3156178891658783e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 6.1022108420729637e-03</internalNodes>
          <leafValues>
            -9.4729237258434296e-02 3.0102241039276123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 6.6952849738299847e-04</internalNodes>
          <leafValues>
            8.1816866993904114e-02 -3.5196030139923096e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 -1.7970580374822021e-03</internalNodes>
          <leafValues>
            2.3718979954719543e-01 -1.1768709868192673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 -7.1074528386816382e-04</internalNodes>
          <leafValues>
            -4.4763788580894470e-01 5.7682480663061142e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 -5.9126471169292927e-03</internalNodes>
          <leafValues>
            4.3425410985946655e-01 -6.6868573427200317e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 -3.3132149837911129e-03</internalNodes>
          <leafValues>
            1.8150010704994202e-01 -1.4180320501327515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 -6.0814660042524338e-02</internalNodes>
          <leafValues>
            4.7221711277961731e-01 -6.1410639435052872e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 -9.6714183688163757e-02</internalNodes>
          <leafValues>
            2.7683168649673462e-01 -9.4490036368370056e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 3.9073550142347813e-03</internalNodes>
          <leafValues>
            -1.2278530001640320e-01 2.1057400107383728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 -9.0431869029998779e-03</internalNodes>
          <leafValues>
            3.5641568899154663e-01 -7.7806226909160614e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 -4.8800031654536724e-03</internalNodes>
          <leafValues>
            -4.1034790873527527e-01 6.9694377481937408e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 -4.3547428213059902e-03</internalNodes>
          <leafValues>
            -7.3017889261245728e-01 3.6655150353908539e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 -9.6500627696514130e-03</internalNodes>
          <leafValues>
            5.5181127786636353e-01 -5.3168080747127533e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 -1.7397310584783554e-02</internalNodes>
          <leafValues>
            -5.7084232568740845e-01 5.0214089453220367e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 -6.8304329179227352e-03</internalNodes>
          <leafValues>
            -4.6180281043052673e-01 5.0202690064907074e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 3.3255619928240776e-04</internalNodes>
          <leafValues>
            -9.5362730324268341e-02 2.5983759760856628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -2.3100529797375202e-03</internalNodes>
          <leafValues>
            2.2872470319271088e-01 -1.0533530265092850e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 -7.5426651164889336e-03</internalNodes>
          <leafValues>
            -5.6990510225296021e-01 4.8863459378480911e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 -5.2723060362040997e-03</internalNodes>
          <leafValues>
            3.5145181417465210e-01 -8.2390107214450836e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 -4.8578968271613121e-03</internalNodes>
          <leafValues>
            -6.0417622327804565e-01 4.4539440423250198e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 1.5867310576140881e-03</internalNodes>
          <leafValues>
            -1.0340909659862518e-01 2.3282019793987274e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 -4.7427811659872532e-03</internalNodes>
          <leafValues>
            2.8490281105041504e-01 -9.8090499639511108e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 -1.3515240279957652e-03</internalNodes>
          <leafValues>
            2.3096430301666260e-01 -1.1361840367317200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 2.2526069078594446e-03</internalNodes>
          <leafValues>
            6.4478322863578796e-02 -4.2205891013145447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 -3.8038659840822220e-04</internalNodes>
          <leafValues>
            -3.8076201081275940e-01 6.0043290257453918e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 4.9043921753764153e-03</internalNodes>
          <leafValues>
            -7.6104998588562012e-02 3.3232170343399048e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 -9.0969670563936234e-03</internalNodes>
          <leafValues>
            1.4287790656089783e-01 -1.6887800395488739e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 -6.9317929446697235e-03</internalNodes>
          <leafValues>
            2.7255409955978394e-01 -9.2879563570022583e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 1.1471060570329428e-03</internalNodes>
          <leafValues>
            -1.5273059904575348e-01 1.9702400267124176e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 -3.7662889808416367e-02</internalNodes>
          <leafValues>
            -5.9320437908172607e-01 4.0738601237535477e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 -6.8165571428835392e-03</internalNodes>
          <leafValues>
            2.5494089722633362e-01 -9.4081960618495941e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 6.6205562325194478e-04</internalNodes>
          <leafValues>
            4.6795718371868134e-02 -4.8454371094703674e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 -4.2202551849186420e-03</internalNodes>
          <leafValues>
            2.4682149291038513e-01 -9.4673976302146912e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 -6.8986512720584869e-02</internalNodes>
          <leafValues>
            -6.6514801979064941e-01 3.5926390439271927e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 6.1707608401775360e-03</internalNodes>
          <leafValues>
            2.5833319872617722e-02 -7.2686272859573364e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 1.0536249727010727e-02</internalNodes>
          <leafValues>
            -8.1828996539115906e-02 2.9760798811912537e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>32</maxWeakCount>
      <stageThreshold>-1.1255199909210205e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 307 -6.2758728861808777e-02</internalNodes>
          <leafValues>
            2.7899080514907837e-01 -2.9656109213829041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 3.4516479354351759e-03</internalNodes>
          <leafValues>
            -3.4635880589485168e-01 2.0903840661048889e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 -7.8699486330151558e-03</internalNodes>
          <leafValues>
            2.4144889414310455e-01 -1.9205570220947266e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 -3.4624869003891945e-03</internalNodes>
          <leafValues>
            -5.9151780605316162e-01 1.2486449629068375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 -9.4818761572241783e-03</internalNodes>
          <leafValues>
            1.8391540646553040e-01 -2.4858260154724121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 2.3226840130519122e-04</internalNodes>
          <leafValues>
            -3.3047258853912354e-01 1.0999260097742081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 1.8101120367646217e-03</internalNodes>
          <leafValues>
            9.8744012415409088e-02 -4.9634781479835510e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 -5.4422430694103241e-03</internalNodes>
          <leafValues>
            2.9344418644905090e-01 -1.3094750046730042e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 7.4148122221231461e-03</internalNodes>
          <leafValues>
            -1.4762699604034424e-01 3.3277168869972229e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 -1.5565140172839165e-02</internalNodes>
          <leafValues>
            -6.8404901027679443e-01 9.9872693419456482e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 2.8720520436763763e-02</internalNodes>
          <leafValues>
            -1.4833280444145203e-01 3.0902579426765442e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 9.6687392215244472e-05</internalNodes>
          <leafValues>
            -1.7431040108203888e-01 2.1402959525585175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 5.2371058613061905e-02</internalNodes>
          <leafValues>
            -7.0156857371330261e-02 4.9222990870475769e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 -8.6485691368579865e-02</internalNodes>
          <leafValues>
            5.0757247209548950e-01 -7.5294211506843567e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 -4.2169868946075439e-02</internalNodes>
          <leafValues>
            4.5680961012840271e-01 -9.0219900012016296e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 4.5369830331765115e-05</internalNodes>
          <leafValues>
            -2.6538279652595520e-01 1.6189539432525635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 5.2918000146746635e-03</internalNodes>
          <leafValues>
            7.4890151619911194e-02 -5.4054671525955200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -7.5511651812121272e-04</internalNodes>
          <leafValues>
            -4.9261990189552307e-01 5.8723948895931244e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 7.5108138844370842e-05</internalNodes>
          <leafValues>
            -2.1432100236415863e-01 1.4077760279178619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 4.9981209449470043e-03</internalNodes>
          <leafValues>
            -9.0547338128089905e-02 3.5716068744659424e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 -1.4929979806765914e-03</internalNodes>
          <leafValues>
            2.5623458623886108e-01 -1.4229069650173187e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 2.7239411137998104e-03</internalNodes>
          <leafValues>
            -1.5649250149726868e-01 2.1088710427284241e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 2.2218320518732071e-03</internalNodes>
          <leafValues>
            -1.5072989463806152e-01 2.6801869273185730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 -7.3993072146549821e-04</internalNodes>
          <leafValues>
            2.9546990990638733e-01 -1.0692390054464340e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 2.0113459322601557e-03</internalNodes>
          <leafValues>
            5.0614349544048309e-02 -7.1683371067047119e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 1.1452870443463326e-02</internalNodes>
          <leafValues>
            -1.2719069421291351e-01 2.4152779579162598e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 -1.0782170575112104e-03</internalNodes>
          <leafValues>
            2.4813009798526764e-01 -1.3461199402809143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 3.3417691010981798e-03</internalNodes>
          <leafValues>
            5.3578309714794159e-02 -5.2274167537689209e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 6.9398651248775423e-05</internalNodes>
          <leafValues>
            -2.1698740124702454e-01 1.2812179327011108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 -4.0982551872730255e-03</internalNodes>
          <leafValues>
            2.4401889741420746e-01 -1.1570589989423752e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 337 -1.6289720078930259e-03</internalNodes>
          <leafValues>
            2.8261470794677734e-01 -1.0659469664096832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 1.3984859921038151e-02</internalNodes>
          <leafValues>
            4.2715899646282196e-02 -7.3646312952041626e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>30</maxWeakCount>
      <stageThreshold>-1.1729990243911743e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 339 1.6416519880294800e-01</internalNodes>
          <leafValues>
            -4.8960301280021667e-01 1.7607709765434265e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 8.3413062384352088e-04</internalNodes>
          <leafValues>
            -2.8220430016517639e-01 2.4199579656124115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 -1.7193210078403354e-03</internalNodes>
          <leafValues>
            -7.1485888957977295e-01 8.6162216961383820e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 -1.5654950402677059e-03</internalNodes>
          <leafValues>
            -7.2972381114959717e-01 9.4070672988891602e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 1.9124479731544852e-03</internalNodes>
          <leafValues>
            -3.1187158823013306e-01 1.8143390119075775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 -1.3512369990348816e-01</internalNodes>
          <leafValues>
            2.9577299952507019e-01 -2.2179250419139862e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 -4.0300549007952213e-03</internalNodes>
          <leafValues>
            -6.6595137119293213e-01 8.5431016981601715e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 -2.8640460222959518e-03</internalNodes>
          <leafValues>
            -6.2086361646652222e-01 5.3106021136045456e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 -1.4065420255064964e-03</internalNodes>
          <leafValues>
            2.2346289455890656e-01 -2.0211009681224823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 -3.5820449702441692e-03</internalNodes>
          <leafValues>
            -5.4030400514602661e-01 6.8213619291782379e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 4.1544470936059952e-02</internalNodes>
          <leafValues>
            -6.5215840935707092e-02 6.2109231948852539e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 -9.1709550470113754e-03</internalNodes>
          <leafValues>
            -7.5553297996520996e-01 5.2640449255704880e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 6.1552738770842552e-03</internalNodes>
          <leafValues>
            9.0939402580261230e-02 -4.4246131181716919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 -1.0043520014733076e-03</internalNodes>
          <leafValues>
            2.4292330443859100e-01 -1.8669790029525757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 1.1519829742610455e-02</internalNodes>
          <leafValues>
            -1.1763150244951248e-01 3.6723458766937256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 -8.9040733873844147e-03</internalNodes>
          <leafValues>
            -4.8931330442428589e-01 1.0897020250558853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 5.3973670583218336e-04</internalNodes>
          <leafValues>
            -2.1850399672985077e-01 1.8489989638328552e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 1.3727260520681739e-03</internalNodes>
          <leafValues>
            -1.5072910487651825e-01 2.9173129796981812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 -1.0807390324771404e-02</internalNodes>
          <leafValues>
            4.2897450923919678e-01 -1.0280139744281769e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 1.2670770520344377e-03</internalNodes>
          <leafValues>
            7.4192158877849579e-02 -6.4208251237869263e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 2.2991129662841558e-03</internalNodes>
          <leafValues>
            4.7100279480218887e-02 -7.2335231304168701e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 360 2.7187510859221220e-03</internalNodes>
          <leafValues>
            -1.7086869478225708e-01 2.3513509333133698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 -6.6619180142879486e-03</internalNodes>
          <leafValues>
            -7.8975427150726318e-01 4.5084670186042786e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 -4.8266649246215820e-02</internalNodes>
          <leafValues>
            -6.9579917192459106e-01 4.1976079344749451e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 1.5214690007269382e-02</internalNodes>
          <leafValues>
            -1.0818280279636383e-01 3.6460620164871216e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 -6.0080131515860558e-03</internalNodes>
          <leafValues>
            3.0970990657806396e-01 -1.1359210312366486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 6.6127157770097256e-03</internalNodes>
          <leafValues>
            8.0665342509746552e-02 -4.6658530831336975e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 -7.9607013612985611e-03</internalNodes>
          <leafValues>
            -8.7201941013336182e-01 3.6774590611457825e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 3.8847199175506830e-03</internalNodes>
          <leafValues>
            -1.1666289716959000e-01 3.3070269227027893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 -1.0988810099661350e-03</internalNodes>
          <leafValues>
            2.3872570693492889e-01 -1.7656759917736053e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>44</maxWeakCount>
      <stageThreshold>-1.0368299484252930e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 369 3.5903379321098328e-03</internalNodes>
          <leafValues>
            -2.3688079416751862e-01 2.4631640315055847e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 6.4815930090844631e-03</internalNodes>
          <leafValues>
            -3.1373620033264160e-01 1.8675759434700012e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 7.3048402555286884e-05</internalNodes>
          <leafValues>
            -2.7644351124763489e-01 1.6496239602565765e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 -3.8514640182256699e-03</internalNodes>
          <leafValues>
            -5.6014508008956909e-01 1.1294739693403244e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 3.8588210009038448e-03</internalNodes>
          <leafValues>
            3.9848998188972473e-02 -5.8071857690811157e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 -2.4651220068335533e-02</internalNodes>
          <leafValues>
            1.6755010187625885e-01 -2.5343671441078186e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 4.7245521098375320e-02</internalNodes>
          <leafValues>
            -1.0662080347537994e-01 3.9451980590820312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 6.5964651294052601e-03</internalNodes>
          <leafValues>
            -1.7744250595569611e-01 2.7280190587043762e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 -1.3177490327507257e-03</internalNodes>
          <leafValues>
            -5.4272651672363281e-01 4.8606589436531067e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 -5.0261709839105606e-03</internalNodes>
          <leafValues>
            2.4394249916076660e-01 -1.3143649697303772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 3.4632768947631121e-03</internalNodes>
          <leafValues>
            6.9049343466758728e-02 -7.0336240530014038e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 2.1692588925361633e-03</internalNodes>
          <leafValues>
            -1.3289460539817810e-01 2.2098529338836670e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 2.9395870864391327e-02</internalNodes>
          <leafValues>
            -2.8530520200729370e-01 1.3543990254402161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 -9.6181448316201568e-04</internalNodes>
          <leafValues>
            -5.8041381835937500e-01 3.7450648844242096e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 383 -1.0820999741554260e-01</internalNodes>
          <leafValues>
            3.9467281103134155e-01 -7.8655943274497986e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 -1.8024869263172150e-02</internalNodes>
          <leafValues>
            2.7355629205703735e-01 -1.3415299355983734e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 6.2509840354323387e-03</internalNodes>
          <leafValues>
            2.3388059809803963e-02 -8.0088591575622559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 -1.6088379779830575e-03</internalNodes>
          <leafValues>
            -5.6762522459030151e-01 4.1215669363737106e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 7.7564752427861094e-04</internalNodes>
          <leafValues>
            -1.4891269803047180e-01 1.9086180627346039e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 8.7122338300105184e-05</internalNodes>
          <leafValues>
            -1.5557530522346497e-01 1.9428220391273499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 -2.0755320787429810e-02</internalNodes>
          <leafValues>
            -6.3006532192230225e-01 3.6134380847215652e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -6.2931738793849945e-03</internalNodes>
          <leafValues>
            2.5609248876571655e-01 -1.0588269680738449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 1.0844149626791477e-02</internalNodes>
          <leafValues>
            -1.0124850273132324e-01 3.0322128534317017e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 -6.3752777350600809e-05</internalNodes>
          <leafValues>
            1.9111579656600952e-01 -1.3849230110645294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 6.6480963141657412e-05</internalNodes>
          <leafValues>
            -1.5205250680446625e-01 2.1706309914588928e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 1.3560829684138298e-03</internalNodes>
          <leafValues>
            4.9431789666414261e-02 -6.4279842376708984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -9.0662558795884252e-04</internalNodes>
          <leafValues>
            1.7982010543346405e-01 -1.4044609665870667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 1.0473709553480148e-03</internalNodes>
          <leafValues>
            -1.0933549702167511e-01 2.4265940487384796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -1.0243969736620784e-03</internalNodes>
          <leafValues>
            2.7162680029869080e-01 -1.1820919811725616e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 398 -1.2024149764329195e-03</internalNodes>
          <leafValues>
            -7.0151102542877197e-01 3.9489898830652237e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 7.6911649666726589e-03</internalNodes>
          <leafValues>
            -9.2218913137912750e-02 3.1046289205551147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 -1.3966549932956696e-01</internalNodes>
          <leafValues>
            6.8979388475418091e-01 -3.9706118404865265e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 2.1276050247251987e-03</internalNodes>
          <leafValues>
            9.7277611494064331e-02 -2.8841799497604370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 -2.7594310231506824e-03</internalNodes>
          <leafValues>
            2.4168670177459717e-01 -1.1277820169925690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 5.2236132323741913e-03</internalNodes>
          <leafValues>
            -1.1430279910564423e-01 2.4256780743598938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 -1.2590440455824137e-03</internalNodes>
          <leafValues>
            -5.9679388999938965e-01 4.7663960605859756e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 -3.7192099262028933e-03</internalNodes>
          <leafValues>
            -4.6414130926132202e-01 5.2847690880298615e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 5.9696151874959469e-03</internalNodes>
          <leafValues>
            -7.3244288563728333e-02 3.8743090629577637e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 -5.1776720210909843e-03</internalNodes>
          <leafValues>
            -7.4193227291107178e-01 4.0496710687875748e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 5.0035100430250168e-03</internalNodes>
          <leafValues>
            -1.3888800144195557e-01 1.8767620623111725e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 -5.2013457752764225e-04</internalNodes>
          <leafValues>
            -5.4940617084503174e-01 4.9417849630117416e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 410 5.3168768063187599e-03</internalNodes>
          <leafValues>
            -8.2482978701591492e-02 3.1740561127662659e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 -1.4774589799344540e-02</internalNodes>
          <leafValues>
            2.0816099643707275e-01 -1.2115559726953506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 -4.1416451334953308e-02</internalNodes>
          <leafValues>
            -8.2437807321548462e-01 3.3329188823699951e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>53</maxWeakCount>
      <stageThreshold>-1.0492420196533203e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 413 9.0962520334869623e-04</internalNodes>
          <leafValues>
            8.4579966962337494e-02 -5.6118410825729370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 -5.6139789521694183e-02</internalNodes>
          <leafValues>
            1.5341749787330627e-01 -2.6967319846153259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 1.0292009683325887e-03</internalNodes>
          <leafValues>
            -2.0489980280399323e-01 2.0153179764747620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 2.8783010784536600e-03</internalNodes>
          <leafValues>
            -1.7351140081882477e-01 2.1297949552536011e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 417 -7.4144392274320126e-03</internalNodes>
          <leafValues>
            -5.9624868631362915e-01 4.7077950090169907e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 418 -1.4831849839538336e-03</internalNodes>
          <leafValues>
            1.9024610519409180e-01 -1.5986390411853790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 419 4.5968941412866116e-03</internalNodes>
          <leafValues>
            3.1447131186723709e-02 -6.8694341182708740e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 420 2.4255330208688974e-03</internalNodes>
          <leafValues>
            -2.3609359562397003e-01 1.1036109924316406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 421 -8.4950566291809082e-02</internalNodes>
          <leafValues>
            2.3107160627841949e-01 -1.3776530325412750e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 -5.0145681016147137e-03</internalNodes>
          <leafValues>
            3.8676109910011292e-01 -5.6217379868030548e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 -2.1482061129063368e-03</internalNodes>
          <leafValues>
            1.8191599845886230e-01 -1.7615699768066406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 -1.0396770201623440e-02</internalNodes>
          <leafValues>
            -7.5351381301879883e-01 2.4091970175504684e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 425 -1.3466750271618366e-02</internalNodes>
          <leafValues>
            -7.2118860483169556e-01 3.4949369728565216e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 -8.4435477852821350e-02</internalNodes>
          <leafValues>
            -3.3792638778686523e-01 7.1113817393779755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 2.4771490134298801e-03</internalNodes>
          <leafValues>
            -1.1765109747648239e-01 2.2541989386081696e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 1.5828050673007965e-02</internalNodes>
          <leafValues>
            -6.9536216557025909e-02 3.1395369768142700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 6.4916983246803284e-02</internalNodes>
          <leafValues>
            -7.5043588876724243e-02 4.0677338838577271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 430 2.9652469675056636e-04</internalNodes>
          <leafValues>
            7.3953360319137573e-02 -3.4544008970260620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 431 1.3129520229995251e-03</internalNodes>
          <leafValues>
            -1.6909439861774445e-01 1.5258370339870453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 432 -5.8032129891216755e-03</internalNodes>
          <leafValues>
            3.5260149836540222e-01 -8.3444066345691681e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 -1.4791679382324219e-01</internalNodes>
          <leafValues>
            4.3004658818244934e-01 -5.7309929281473160e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 -1.6584150493144989e-02</internalNodes>
          <leafValues>
            2.3432689905166626e-01 -1.0907640308141708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 435 3.0183270573616028e-03</internalNodes>
          <leafValues>
            -1.3600939512252808e-01 2.6409289240837097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 436 -3.6471918225288391e-02</internalNodes>
          <leafValues>
            -6.2809741497039795e-01 4.3545108288526535e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 437 -7.3119226726703346e-05</internalNodes>
          <leafValues>
            1.6470630466938019e-01 -1.6463780403137207e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 -3.6719450727105141e-03</internalNodes>
          <leafValues>
            -4.7421360015869141e-01 4.8586919903755188e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 439 -4.0151178836822510e-03</internalNodes>
          <leafValues>
            1.8222180008888245e-01 -1.4097510278224945e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 1.9948020577430725e-02</internalNodes>
          <leafValues>
            -6.9787658751010895e-02 3.6707460880279541e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 441 7.6699437340721488e-04</internalNodes>
          <leafValues>
            5.5729299783706665e-02 -4.4585430622100830e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 442 -1.1806039838120341e-03</internalNodes>
          <leafValues>
            -4.6876621246337891e-01 4.8902221024036407e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 1.5847349539399147e-02</internalNodes>
          <leafValues>
            -1.2120209634304047e-01 2.0566530525684357e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 444 -1.1985700111836195e-03</internalNodes>
          <leafValues>
            2.0262099802494049e-01 -1.2823820114135742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 445 -1.0964959859848022e-01</internalNodes>
          <leafValues>
            -8.6619192361831665e-01 3.0351849272847176e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 -9.2532606795430183e-03</internalNodes>
          <leafValues>
            2.9343119263648987e-01 -8.5361950099468231e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 447 1.4686530455946922e-02</internalNodes>
          <leafValues>
            3.2798621803522110e-02 -7.7556562423706055e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 -1.3514430029317737e-03</internalNodes>
          <leafValues>
            2.4426999688148499e-01 -1.1503250151872635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 -4.3728090822696686e-03</internalNodes>
          <leafValues>
            2.1687670052051544e-01 -1.3984480500221252e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 3.4263390116393566e-03</internalNodes>
          <leafValues>
            4.5614220201969147e-02 -5.4567712545394897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 -3.8404068909585476e-03</internalNodes>
          <leafValues>
            1.4949500560760498e-01 -1.5062509477138519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 3.7988980766385794e-03</internalNodes>
          <leafValues>
            -8.7301626801490784e-02 2.5481531023979187e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 453 -2.0094281062483788e-03</internalNodes>
          <leafValues>
            1.7259070277214050e-01 -1.4288470149040222e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 454 -2.4370709434151649e-03</internalNodes>
          <leafValues>
            2.6848098635673523e-01 -8.1898219883441925e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 1.0485399980098009e-03</internalNodes>
          <leafValues>
            4.6113260090351105e-02 -4.7243279218673706e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 456 1.7460780218243599e-03</internalNodes>
          <leafValues>
            -1.1030430346727371e-01 2.0379729568958282e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 5.8608627878129482e-03</internalNodes>
          <leafValues>
            -1.5619659423828125e-01 1.5927439928054810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 458 -2.7724979445338249e-02</internalNodes>
          <leafValues>
            1.1349119991064072e-01 -2.1885140240192413e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 459 4.7080639749765396e-02</internalNodes>
          <leafValues>
            -4.1688729077577591e-02 5.3630048036575317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 460 -7.9283770173788071e-03</internalNodes>
          <leafValues>
            -5.3595131635665894e-01 4.4237509369850159e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 -1.2880540452897549e-02</internalNodes>
          <leafValues>
            2.3237949609756470e-01 -1.0246250033378601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 2.3604769259691238e-02</internalNodes>
          <leafValues>
            -8.8291436433792114e-02 3.0561059713363647e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 1.5902200713753700e-02</internalNodes>
          <leafValues>
            -1.2238109856843948e-01 1.7849120497703552e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 7.9939495772123337e-03</internalNodes>
          <leafValues>
            -8.3729006350040436e-02 3.2319590449333191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 5.7100867852568626e-03</internalNodes>
          <leafValues>
            3.8479208946228027e-02 -6.8138152360916138e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>51</maxWeakCount>
      <stageThreshold>-1.1122100353240967e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 466 2.2480720654129982e-03</internalNodes>
          <leafValues>
            -1.6416870057582855e-01 4.1648530960083008e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 4.5813550241291523e-03</internalNodes>
          <leafValues>
            -1.2465959787368774e-01 4.0385121107101440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 468 -1.6073239967226982e-03</internalNodes>
          <leafValues>
            2.6082459092140198e-01 -2.0282520353794098e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 469 2.5205370038747787e-03</internalNodes>
          <leafValues>
            -1.0557229816913605e-01 3.6669111251831055e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 470 2.4119189474731684e-03</internalNodes>
          <leafValues>
            -1.3877600431442261e-01 2.9959911108016968e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 471 5.7156179100275040e-03</internalNodes>
          <leafValues>
            -7.7683463692665100e-02 4.8481920361518860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 472 3.1093840952962637e-03</internalNodes>
          <leafValues>
            -1.1229000240564346e-01 2.9215508699417114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 473 -8.6836628615856171e-02</internalNodes>
          <leafValues>
            -3.6779600381851196e-01 7.2597242891788483e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 5.2652182057499886e-03</internalNodes>
          <leafValues>
            -1.0890290141105652e-01 3.1791260838508606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 475 -1.9913529977202415e-02</internalNodes>
          <leafValues>
            -5.3373438119888306e-01 7.0585712790489197e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 3.8297839928418398e-03</internalNodes>
          <leafValues>
            -1.3575910031795502e-01 2.2788879275321960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 1.0431859642267227e-02</internalNodes>
          <leafValues>
            8.8797912001609802e-02 -4.7958970069885254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 -2.0040439441800117e-02</internalNodes>
          <leafValues>
            1.5745539963245392e-01 -1.7771570384502411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 479 -5.2967290394008160e-03</internalNodes>
          <leafValues>
            -6.8434917926788330e-01 3.5671461373567581e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 -2.1624139044433832e-03</internalNodes>
          <leafValues>
            2.8318038582801819e-01 -9.8511278629302979e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 481 -3.5464888787828386e-04</internalNodes>
          <leafValues>
            -3.7077340483665466e-01 8.0932952463626862e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 482 -1.8152060511056334e-04</internalNodes>
          <leafValues>
            -3.2207030057907104e-01 7.7551059424877167e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 483 -2.7563021285459399e-04</internalNodes>
          <leafValues>
            -3.2441279292106628e-01 8.7949477136135101e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 6.3823810778558254e-03</internalNodes>
          <leafValues>
            -8.8924713432788849e-02 3.1727218627929688e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 485 1.1150909587740898e-02</internalNodes>
          <leafValues>
            7.1019843220710754e-02 -4.0494039654731750e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 486 -1.0593760525807738e-03</internalNodes>
          <leafValues>
            2.6050668954849243e-01 -1.1765640228986740e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 2.3906480055302382e-03</internalNodes>
          <leafValues>
            -8.4388621151447296e-02 3.1230551004409790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 488 -1.1000749655067921e-02</internalNodes>
          <leafValues>
            1.9152249395847321e-01 -1.5210020542144775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 -2.4643228971399367e-04</internalNodes>
          <leafValues>
            -3.1765159964561462e-01 8.6582258343696594e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 490 2.3053269833326340e-02</internalNodes>
          <leafValues>
            -1.0089760273694992e-01 2.5769290328025818e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 -2.2135660983622074e-03</internalNodes>
          <leafValues>
            4.5689210295677185e-01 -5.2404791116714478e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 492 -9.7139709396287799e-04</internalNodes>
          <leafValues>
            -3.5518380999565125e-01 8.0094382166862488e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 493 1.5676229959353805e-03</internalNodes>
          <leafValues>
            1.0091420263051987e-01 -2.1603040397167206e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 494 7.5460801599547267e-04</internalNodes>
          <leafValues>
            5.7896178215742111e-02 -4.0461111068725586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 495 -2.0698970183730125e-02</internalNodes>
          <leafValues>
            3.1543630361557007e-01 -8.0713048577308655e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 -2.0619940012693405e-02</internalNodes>
          <leafValues>
            2.7181661128997803e-01 -7.6358616352081299e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 497 2.1611129865050316e-02</internalNodes>
          <leafValues>
            3.9493449032306671e-02 -5.9429651498794556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 498 6.5676742233335972e-03</internalNodes>
          <leafValues>
            -9.8353669047355652e-02 2.3649279773235321e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 499 -8.8434796780347824e-03</internalNodes>
          <leafValues>
            -5.2523428201675415e-01 4.3099921196699142e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 500 -9.4260741025209427e-03</internalNodes>
          <leafValues>
            2.4665130674839020e-01 -9.4130717217922211e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 501 -1.9830230157822371e-03</internalNodes>
          <leafValues>
            2.6743701100349426e-01 -9.0069316327571869e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 502 -1.7358399927616119e-03</internalNodes>
          <leafValues>
            1.5940019488334656e-01 -1.5789410471916199e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -1.3513869605958462e-02</internalNodes>
          <leafValues>
            4.0792331099510193e-01 -6.4223118126392365e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 504 -1.9394010305404663e-02</internalNodes>
          <leafValues>
            1.8015649914741516e-01 -1.3731400668621063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 505 -3.2684770412743092e-03</internalNodes>
          <leafValues>
            2.9080390930175781e-01 -8.0161906778812408e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 506 4.1773589327931404e-04</internalNodes>
          <leafValues>
            -2.1412980556488037e-01 1.1273439973592758e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 507 -7.6351119205355644e-03</internalNodes>
          <leafValues>
            -4.5365959405899048e-01 5.4625060409307480e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -8.3652976900339127e-03</internalNodes>
          <leafValues>
            2.6472920179367065e-01 -9.4334110617637634e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 509 2.7768449857831001e-02</internalNodes>
          <leafValues>
            -1.0136710107326508e-01 2.0743979513645172e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 510 -5.4891228675842285e-02</internalNodes>
          <leafValues>
            2.8840309381484985e-01 -7.5312040746212006e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 2.5793339591473341e-03</internalNodes>
          <leafValues>
            -1.1088529974222183e-01 2.1724960207939148e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 512 6.6196516854688525e-05</internalNodes>
          <leafValues>
            -1.8872100114822388e-01 1.4440689980983734e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 513 5.0907251425087452e-03</internalNodes>
          <leafValues>
            -7.7601231634616852e-02 2.9398378729820251e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 -1.0444259643554688e-01</internalNodes>
          <leafValues>
            2.0133109390735626e-01 -1.0903970152139664e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 -6.7273090826347470e-04</internalNodes>
          <leafValues>
            1.7945900559425354e-01 -1.2023670226335526e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 516 3.2412849832326174e-03</internalNodes>
          <leafValues>
            4.0688131004571915e-02 -5.4600572586059570e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>44</maxWeakCount>
      <stageThreshold>-1.2529590129852295e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 517 5.2965320646762848e-03</internalNodes>
          <leafValues>
            -1.2154529988765717e-01 6.4420372247695923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 518 -2.5326260365545750e-03</internalNodes>
          <leafValues>
            5.1233220100402832e-01 -1.1108259856700897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 -2.9183230362832546e-03</internalNodes>
          <leafValues>
            -5.0615429878234863e-01 1.1501979827880859e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 520 -2.3692339658737183e-02</internalNodes>
          <leafValues>
            3.7167280912399292e-01 -1.4672680199146271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 521 2.0177470520138741e-02</internalNodes>
          <leafValues>
            -1.7388840019702911e-01 4.7759491205215454e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 522 -2.1723210811614990e-02</internalNodes>
          <leafValues>
            -4.3880090117454529e-01 1.3576899468898773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 523 2.8369780629873276e-03</internalNodes>
          <leafValues>
            -1.2512069940567017e-01 4.6789029240608215e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 524 2.7148420922458172e-03</internalNodes>
          <leafValues>
            -8.8018856942653656e-02 3.6866518855094910e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 525 3.2625689636915922e-03</internalNodes>
          <leafValues>
            -8.5335306823253632e-02 5.1644730567932129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 526 -3.5618850961327553e-03</internalNodes>
          <leafValues>
            -4.4503930211067200e-01 9.1738171875476837e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 1.9227749435231090e-03</internalNodes>
          <leafValues>
            -1.1077310144901276e-01 3.9416998624801636e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 -3.5111969918943942e-04</internalNodes>
          <leafValues>
            -3.7775701284408569e-01 1.2166170030832291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 529 1.9121779769193381e-04</internalNodes>
          <leafValues>
            7.4816018342971802e-02 -4.0767100453376770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 530 -2.6525629800744355e-04</internalNodes>
          <leafValues>
            -3.3151718974113464e-01 1.1291120201349258e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 2.0086700096726418e-02</internalNodes>
          <leafValues>
            -6.1598118394613266e-02 5.6128817796707153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 3.6783248186111450e-02</internalNodes>
          <leafValues>
            -6.0251388698816299e-02 5.2192491292953491e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 533 1.3941619545221329e-03</internalNodes>
          <leafValues>
            -3.5503050684928894e-01 1.0863020271062851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 534 -1.5181669965386391e-02</internalNodes>
          <leafValues>
            2.2739650309085846e-01 -1.6252990067005157e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 535 4.6796840615570545e-03</internalNodes>
          <leafValues>
            -5.7535041123628616e-02 4.8124238848686218e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 536 -1.7988319450523704e-04</internalNodes>
          <leafValues>
            -3.0587670207023621e-01 1.0868159681558609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 537 -3.5850999411195517e-03</internalNodes>
          <leafValues>
            3.8596940040588379e-01 -9.2194072902202606e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 538 1.0793360415846109e-03</internalNodes>
          <leafValues>
            -1.1190389841794968e-01 3.1125208735466003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 539 7.3285802500322461e-05</internalNodes>
          <leafValues>
            -2.0239910483360291e-01 1.5586680173873901e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 540 1.3678739964962006e-01</internalNodes>
          <leafValues>
            -2.1672859787940979e-01 1.4420390129089355e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 541 -1.1729259975254536e-02</internalNodes>
          <leafValues>
            4.3503770232200623e-01 -7.4886530637741089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 3.9230841211974621e-03</internalNodes>
          <leafValues>
            -5.0289329141378403e-02 5.8831161260604858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 543 -2.9819121118634939e-04</internalNodes>
          <leafValues>
            -3.8232401013374329e-01 9.2451132833957672e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 -4.7992770560085773e-03</internalNodes>
          <leafValues>
            4.8488789796829224e-01 -7.3136523365974426e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 -3.0155890271998942e-04</internalNodes>
          <leafValues>
            -3.5757359862327576e-01 1.0581880062818527e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 546 1.0390769690275192e-02</internalNodes>
          <leafValues>
            5.2920468151569366e-02 -5.7249659299850464e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 547 -9.4488041941076517e-04</internalNodes>
          <leafValues>
            4.4966828823089600e-01 -8.3075523376464844e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 548 1.2651870492845774e-03</internalNodes>
          <leafValues>
            -9.6695438027381897e-02 3.1302270293235779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 549 1.7094539478421211e-02</internalNodes>
          <leafValues>
            -8.1248976290225983e-02 3.6113831400871277e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 550 2.5973359588533640e-03</internalNodes>
          <leafValues>
            -1.1338350176811218e-01 2.2233949601650238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 1.4527440071105957e-03</internalNodes>
          <leafValues>
            6.9750443100929260e-02 -3.6720710992813110e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 552 4.7638658434152603e-03</internalNodes>
          <leafValues>
            -6.5788961946964264e-02 3.8328540325164795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 553 -6.2501081265509129e-03</internalNodes>
          <leafValues>
            -7.0754468441009521e-01 3.8350198417901993e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 -3.1765329185873270e-03</internalNodes>
          <leafValues>
            1.3755400478839874e-01 -2.3240029811859131e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 555 3.2191169448196888e-03</internalNodes>
          <leafValues>
            -1.2935450673103333e-01 2.2737880051136017e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 -5.6365579366683960e-03</internalNodes>
          <leafValues>
            3.8067150115966797e-01 -6.7246839404106140e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 557 -2.3844049428589642e-04</internalNodes>
          <leafValues>
            -3.1122380495071411e-01 8.3838358521461487e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 -4.1017560288310051e-03</internalNodes>
          <leafValues>
            2.6067280769348145e-01 -1.0449740290641785e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 1.3336989795789123e-03</internalNodes>
          <leafValues>
            -5.8250140398740768e-02 4.7682440280914307e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 560 -1.2090239906683564e-03</internalNodes>
          <leafValues>
            1.4834509789943695e-01 -1.7329469323158264e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>72</maxWeakCount>
      <stageThreshold>-1.1188739538192749e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 561 -3.1760931015014648e-03</internalNodes>
          <leafValues>
            3.3333331346511841e-01 -1.6642349958419800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 562 2.4858079850673676e-02</internalNodes>
          <leafValues>
            -7.2728872299194336e-02 5.6674581766128540e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 563 -7.7597280032932758e-03</internalNodes>
          <leafValues>
            4.6258568763732910e-01 -9.3112178146839142e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 564 7.8239021822810173e-03</internalNodes>
          <leafValues>
            -2.7414610981941223e-01 1.3243049383163452e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 -1.0948839597404003e-02</internalNodes>
          <leafValues>
            2.2345480322837830e-01 -1.4965449273586273e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 566 -3.4349008928984404e-03</internalNodes>
          <leafValues>
            3.8724988698959351e-01 -6.6121727228164673e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 567 -3.1156290322542191e-02</internalNodes>
          <leafValues>
            2.4078279733657837e-01 -1.1406909674406052e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 1.1100519914180040e-03</internalNodes>
          <leafValues>
            -2.8207978606224060e-01 1.3275429606437683e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 3.1762740109115839e-03</internalNodes>
          <leafValues>
            3.4585930407047272e-02 -5.1374310255050659e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 570 -2.7977459132671356e-02</internalNodes>
          <leafValues>
            2.3926779627799988e-01 -1.3255919516086578e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 571 -2.3097939789295197e-02</internalNodes>
          <leafValues>
            3.9019620418548584e-01 -7.8478008508682251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 572 -3.9731930010020733e-03</internalNodes>
          <leafValues>
            3.0691069364547729e-01 -7.0601403713226318e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 573 3.0335749033838511e-03</internalNodes>
          <leafValues>
            -1.4002190530300140e-01 1.9134859740734100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 574 -1.0844370350241661e-02</internalNodes>
          <leafValues>
            1.6548730432987213e-01 -1.5657779574394226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 575 -1.8150510266423225e-02</internalNodes>
          <leafValues>
            -6.3243591785430908e-01 3.9561819285154343e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 576 7.1052298881113529e-04</internalNodes>
          <leafValues>
            -1.8515570461750031e-01 1.3408809900283813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 577 1.0893340222537518e-02</internalNodes>
          <leafValues>
            -2.6730230078101158e-02 6.0971802473068237e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 578 -2.8780900174751878e-04</internalNodes>
          <leafValues>
            -3.0065140128135681e-01 7.3171459138393402e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 579 -3.5855069290846586e-03</internalNodes>
          <leafValues>
            2.6217609643936157e-01 -7.9714097082614899e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 580 -1.9759280607104301e-02</internalNodes>
          <leafValues>
            -5.9039229154586792e-01 4.0698971599340439e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 -1.0845210403203964e-02</internalNodes>
          <leafValues>
            1.6364559531211853e-01 -1.2586060166358948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 582 -4.3183090165257454e-03</internalNodes>
          <leafValues>
            -5.7474881410598755e-01 3.7644311785697937e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 583 1.4913700288161635e-03</internalNodes>
          <leafValues>
            6.0913469642400742e-02 -3.0222928524017334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 584 1.5675699338316917e-02</internalNodes>
          <leafValues>
            -7.3145911097526550e-02 2.9379451274871826e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 585 -1.1033560149371624e-02</internalNodes>
          <leafValues>
            3.9318808913230896e-01 -4.7084320336580276e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 586 8.8555756956338882e-03</internalNodes>
          <leafValues>
            3.7601381540298462e-02 -4.9108490347862244e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 587 -8.9665671112015843e-04</internalNodes>
          <leafValues>
            1.7952020466327667e-01 -1.1086239665746689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 588 -3.0592409893870354e-03</internalNodes>
          <leafValues>
            -4.4429460167884827e-01 5.1005430519580841e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 589 6.3201179727911949e-03</internalNodes>
          <leafValues>
            -5.2841089665889740e-02 3.7197101116180420e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 590 2.0682830363512039e-02</internalNodes>
          <leafValues>
            5.7667169719934464e-02 -3.6901599168777466e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 9.9822662770748138e-02</internalNodes>
          <leafValues>
            -3.7377018481492996e-02 5.8165591955184937e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 592 -6.5854229032993317e-03</internalNodes>
          <leafValues>
            2.8509441018104553e-01 -6.0978069901466370e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 -6.0900300741195679e-02</internalNodes>
          <leafValues>
            -5.1031768321990967e-01 3.7787400186061859e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 594 -2.9991709161549807e-03</internalNodes>
          <leafValues>
            -4.7943010926246643e-01 3.8833890110254288e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 595 -9.8906438797712326e-03</internalNodes>
          <leafValues>
            4.0609079599380493e-01 -4.7869648784399033e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 596 -8.2688927650451660e-02</internalNodes>
          <leafValues>
            -7.0671182870864868e-01 2.7487749233841896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 597 5.0060399807989597e-03</internalNodes>
          <leafValues>
            2.8208440169692039e-02 -5.2909690141677856e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 598 6.1695030890405178e-03</internalNodes>
          <leafValues>
            -5.4554861038923264e-02 3.2837980985641479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 -3.3914761152118444e-03</internalNodes>
          <leafValues>
            9.2117667198181152e-02 -2.1637110412120819e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 -2.6131230406463146e-03</internalNodes>
          <leafValues>
            1.3651019334793091e-01 -1.3781130313873291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 8.0490659456700087e-04</internalNodes>
          <leafValues>
            -6.8637110292911530e-02 3.3581069111824036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 602 -3.8106508553028107e-02</internalNodes>
          <leafValues>
            2.9445430636405945e-01 -6.8239226937294006e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 603 7.2450799052603543e-05</internalNodes>
          <leafValues>
            -1.6750130057334900e-01 1.2178230285644531e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 604 1.5837959945201874e-03</internalNodes>
          <leafValues>
            -9.2042848467826843e-02 2.1348990499973297e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 1.2924340553581715e-03</internalNodes>
          <leafValues>
            6.2917232513427734e-02 -3.6174508929252625e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 9.9146775901317596e-03</internalNodes>
          <leafValues>
            1.9534060731530190e-02 -8.1015038490295410e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 607 -1.7086310544982553e-03</internalNodes>
          <leafValues>
            2.5525239109992981e-01 -6.8229459226131439e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 2.1844399161636829e-03</internalNodes>
          <leafValues>
            2.3314049467444420e-02 -8.4296780824661255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 -3.4244330599904060e-03</internalNodes>
          <leafValues>
            2.7213689684867859e-01 -7.6395228505134583e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 610 2.7591470279730856e-04</internalNodes>
          <leafValues>
            -1.0742840170860291e-01 2.2888970375061035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 611 -6.0005177510902286e-04</internalNodes>
          <leafValues>
            -2.9854211211204529e-01 6.3479736447334290e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 612 -2.5001438916660845e-04</internalNodes>
          <leafValues>
            -2.7178969979286194e-01 6.9615006446838379e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 613 6.8751391954720020e-03</internalNodes>
          <leafValues>
            -5.7185899466276169e-02 3.6695951223373413e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 614 1.2761900201439857e-02</internalNodes>
          <leafValues>
            6.7955687642097473e-02 -2.8534150123596191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 -1.4752789866179228e-03</internalNodes>
          <leafValues>
            2.0680660009384155e-01 -1.0059390217065811e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 1.2138819694519043e-01</internalNodes>
          <leafValues>
            -9.7126796841621399e-02 1.9789619743824005e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 617 -5.0081279128789902e-02</internalNodes>
          <leafValues>
            2.8417178988456726e-01 -6.7879997193813324e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 618 3.1454950571060181e-02</internalNodes>
          <leafValues>
            -8.9468672871589661e-02 2.1298420429229736e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 619 1.8878319533541799e-03</internalNodes>
          <leafValues>
            -1.1656440049409866e-01 1.6663520038127899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 620 -5.7211960665881634e-03</internalNodes>
          <leafValues>
            2.3702140152454376e-01 -9.0776607394218445e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 621 -1.8076719425152987e-04</internalNodes>
          <leafValues>
            1.7951929569244385e-01 -1.0793480277061462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 -1.9761849939823151e-01</internalNodes>
          <leafValues>
            4.5674291253089905e-01 -4.0480159223079681e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 623 -2.3846809926908463e-04</internalNodes>
          <leafValues>
            -2.3733009397983551e-01 7.5922161340713501e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 2.1540730085689574e-04</internalNodes>
          <leafValues>
            8.1688016653060913e-02 -2.8685030341148376e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 1.0163090191781521e-02</internalNodes>
          <leafValues>
            -4.1250020265579224e-02 4.8038348555564880e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 626 -7.2184870950877666e-03</internalNodes>
          <leafValues>
            1.7458580434322357e-01 -1.0146500170230865e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 627 2.4263170361518860e-01</internalNodes>
          <leafValues>
            5.3426481783390045e-02 -3.2318529486656189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 628 6.9304101634770632e-04</internalNodes>
          <leafValues>
            -1.1499179899692535e-01 1.4793939888477325e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 629 3.5475199110805988e-03</internalNodes>
          <leafValues>
            -3.9424978196620941e-02 5.3126180171966553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 630 2.1403690334409475e-04</internalNodes>
          <leafValues>
            6.9753833115100861e-02 -2.7319580316543579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 631 -5.7119462871924043e-04</internalNodes>
          <leafValues>
            3.4369900822639465e-01 -5.7699009776115417e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 632 -6.6290069371461868e-03</internalNodes>
          <leafValues>
            1.1758489906787872e-01 -1.5020139515399933e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>66</maxWeakCount>
      <stageThreshold>-1.0888810157775879e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 633 -2.6513449847698212e-02</internalNodes>
          <leafValues>
            2.0568640530109406e-01 -2.6473900675773621e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 634 9.7727458924055099e-03</internalNodes>
          <leafValues>
            -1.1192840337753296e-01 3.2570549845695496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 635 3.2290350645780563e-02</internalNodes>
          <leafValues>
            -9.8574757575988770e-02 3.1779170036315918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 -2.8103240765631199e-03</internalNodes>
          <leafValues>
            1.5213899314403534e-01 -1.9686409831047058e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 637 -1.0991429910063744e-02</internalNodes>
          <leafValues>
            5.1407659053802490e-01 -4.3707210570573807e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 638 6.3133831135928631e-03</internalNodes>
          <leafValues>
            -9.2781022191047668e-02 3.4702470898628235e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 8.7105982005596161e-02</internalNodes>
          <leafValues>
            3.0053649097681046e-02 -8.2814818620681763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 640 1.1799359926953912e-03</internalNodes>
          <leafValues>
            -1.2928420305252075e-01 2.0646120607852936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 -9.3056890182197094e-04</internalNodes>
          <leafValues>
            -5.0021439790725708e-01 9.3666993081569672e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 642 -1.3687170110642910e-02</internalNodes>
          <leafValues>
            -7.9358148574829102e-01 -6.6733639687299728e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 -7.5917452573776245e-02</internalNodes>
          <leafValues>
            3.0469641089439392e-01 -7.9655893146991730e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 -2.8559709899127483e-03</internalNodes>
          <leafValues>
            2.0961460471153259e-01 -1.2732550501823425e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 -4.0231510065495968e-03</internalNodes>
          <leafValues>
            -6.5817278623580933e-01 5.0683639943599701e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 1.7558040097355843e-02</internalNodes>
          <leafValues>
            -8.5382692515850067e-02 3.6174559593200684e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 647 2.1988239139318466e-02</internalNodes>
          <leafValues>
            6.2943696975708008e-02 -7.0896339416503906e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 -2.8599589131772518e-03</internalNodes>
          <leafValues>
            1.4683780074119568e-01 -1.6465979814529419e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 -1.0030849836766720e-02</internalNodes>
          <leafValues>
            4.9579939246177673e-01 -2.7188340201973915e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 -6.9560329429805279e-03</internalNodes>
          <leafValues>
            2.7977779507637024e-01 -7.7953331172466278e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 651 -3.8356808945536613e-03</internalNodes>
          <leafValues>
            -5.8163982629776001e-01 3.5739939659833908e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 -3.2647319603711367e-03</internalNodes>
          <leafValues>
            -4.9945080280303955e-01 4.6986490488052368e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 653 -7.8412350267171860e-03</internalNodes>
          <leafValues>
            3.4532830119132996e-01 -6.8810403347015381e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 654 -8.1718113506212831e-05</internalNodes>
          <leafValues>
            1.5041710436344147e-01 -1.4146679639816284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 655 -3.2448628917336464e-03</internalNodes>
          <leafValues>
            2.2724510729312897e-01 -9.2860206961631775e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 -7.8561151167377830e-04</internalNodes>
          <leafValues>
            -4.4319018721580505e-01 5.7812441140413284e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 -6.2474247533828020e-04</internalNodes>
          <leafValues>
            1.3952389359474182e-01 -1.4668719470500946e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 658 -3.2942948746494949e-04</internalNodes>
          <leafValues>
            -2.9901570081710815e-01 7.6066739857196808e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 1.2605739757418633e-03</internalNodes>
          <leafValues>
            -1.6125600039958954e-01 1.3953800499439240e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 660 -5.1667019724845886e-02</internalNodes>
          <leafValues>
            -5.3142839670181274e-01 4.0719520300626755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 -1.5285619534552097e-02</internalNodes>
          <leafValues>
            -7.8206378221511841e-01 2.7183769270777702e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 662 6.9029822945594788e-02</internalNodes>
          <leafValues>
            -3.6427021026611328e-02 7.1102517843246460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 663 1.4522749697789550e-03</internalNodes>
          <leafValues>
            -9.6890516579151154e-02 2.1668420732021332e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 664 -2.4765590205788612e-03</internalNodes>
          <leafValues>
            1.1645310372114182e-01 -1.8227979540824890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 665 -1.5134819550439715e-03</internalNodes>
          <leafValues>
            1.7863979935646057e-01 -1.2214969843626022e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 666 -1.5099470037966967e-03</internalNodes>
          <leafValues>
            1.8086239695549011e-01 -1.1446069926023483e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 -6.7054620012640953e-03</internalNodes>
          <leafValues>
            2.5106599926948547e-01 -9.1871462762355804e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 668 -1.4075200073421001e-02</internalNodes>
          <leafValues>
            1.3707509636878967e-01 -1.7333500087261200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 -2.2400720044970512e-03</internalNodes>
          <leafValues>
            4.0092980861663818e-01 -4.7576878219842911e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 1.9782369956374168e-02</internalNodes>
          <leafValues>
            -1.9040350615978241e-01 1.4923410117626190e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 671 2.6002870872616768e-03</internalNodes>
          <leafValues>
            4.6971768140792847e-02 -4.3307659029960632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 -5.3445628145709634e-04</internalNodes>
          <leafValues>
            -4.3744230270385742e-01 4.1520189493894577e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 673 -1.7466509714722633e-02</internalNodes>
          <leafValues>
            6.5818172693252563e-01 -3.4447491168975830e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 -2.0425589755177498e-03</internalNodes>
          <leafValues>
            3.9657929539680481e-01 -4.4052429497241974e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 675 2.6661779265850782e-03</internalNodes>
          <leafValues>
            5.8770958334207535e-02 -3.2806369662284851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 -5.5982369929552078e-02</internalNodes>
          <leafValues>
            -5.1735472679138184e-01 3.5791840404272079e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 -1.5066330088302493e-03</internalNodes>
          <leafValues>
            1.5123869478702545e-01 -1.2520180642604828e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 678 -1.1472369544208050e-02</internalNodes>
          <leafValues>
            -6.2930530309677124e-01 3.4704331308603287e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 2.3409629240632057e-02</internalNodes>
          <leafValues>
            -5.8063350617885590e-02 3.8668221235275269e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 680 -2.3243729956448078e-03</internalNodes>
          <leafValues>
            1.8754099309444427e-01 -9.8394669592380524e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 681 -2.9039299115538597e-02</internalNodes>
          <leafValues>
            -5.4486900568008423e-01 4.0926340967416763e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 682 -1.4474649913609028e-02</internalNodes>
          <leafValues>
            -6.7248392105102539e-01 2.3128850385546684e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 683 -5.2086091600358486e-03</internalNodes>
          <leafValues>
            -4.3271440267562866e-01 4.3780650943517685e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 684 4.9382899887859821e-03</internalNodes>
          <leafValues>
            -1.0878620296716690e-01 1.9342589378356934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 685 -4.3193930760025978e-03</internalNodes>
          <leafValues>
            2.4080930650234222e-01 -1.0380800068378448e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 2.3705669445917010e-04</internalNodes>
          <leafValues>
            -8.7349072098731995e-02 2.0466239750385284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 687 4.7858079778961837e-04</internalNodes>
          <leafValues>
            4.5624580234289169e-02 -3.8854670524597168e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 -8.5342838428914547e-04</internalNodes>
          <leafValues>
            -5.5077940225601196e-01 3.5825889557600021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 689 5.4772121075075120e-05</internalNodes>
          <leafValues>
            -1.1225239932537079e-01 1.7503519356250763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 690 -3.8445889949798584e-03</internalNodes>
          <leafValues>
            2.4526700377464294e-01 -8.1132568418979645e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 691 -4.0128458291292191e-02</internalNodes>
          <leafValues>
            -6.3122707605361938e-01 2.6972670108079910e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 692 -1.7886360001284629e-04</internalNodes>
          <leafValues>
            1.9855099916458130e-01 -1.0333680361509323e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 693 1.7668239888735116e-04</internalNodes>
          <leafValues>
            -9.1359011828899384e-02 1.9848720729351044e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 694 7.2763383388519287e-02</internalNodes>
          <leafValues>
            5.0075579434633255e-02 -3.3852630853652954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 695 1.0181630030274391e-02</internalNodes>
          <leafValues>
            -9.3229979276657104e-02 2.0059590041637421e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 696 2.4409969337284565e-03</internalNodes>
          <leafValues>
            6.4636632800102234e-02 -2.6921740174293518e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 -3.6227488890290260e-03</internalNodes>
          <leafValues>
            1.3169890642166138e-01 -1.2514840066432953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 698 -1.3635610230267048e-03</internalNodes>
          <leafValues>
            1.6350460052490234e-01 -1.0665939748287201e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>69</maxWeakCount>
      <stageThreshold>-1.0408929586410522e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 699 -9.6991164609789848e-03</internalNodes>
          <leafValues>
            6.1125320196151733e-01 -6.6225312650203705e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 700 -9.6426531672477722e-03</internalNodes>
          <leafValues>
            -1. 2.7699959464371204e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 701 -9.6381865441799164e-03</internalNodes>
          <leafValues>
            1. -2.9904270195402205e-04</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 -4.2553939856588840e-03</internalNodes>
          <leafValues>
            2.8464388847351074e-01 -1.5540120005607605e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 703 -9.6223521977663040e-03</internalNodes>
          <leafValues>
            -1. 4.3999180197715759e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 704 -9.1231241822242737e-03</internalNodes>
          <leafValues>
            8.6869341135025024e-01 -2.7267890982329845e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 705 -8.6240433156490326e-03</internalNodes>
          <leafValues>
            4.5352488756179810e-01 -8.6071379482746124e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 706 -8.9324144646525383e-03</internalNodes>
          <leafValues>
            1.3375559449195862e-01 -2.6012519001960754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 707 -1.4207810163497925e-02</internalNodes>
          <leafValues>
            3.2077640295028687e-01 -9.7226411104202271e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 708 2.5911010801792145e-02</internalNodes>
          <leafValues>
            -1.2964080274105072e-01 2.6218649744987488e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 709 2.0531509653665125e-04</internalNodes>
          <leafValues>
            -1.2404280155897141e-01 2.1062959730625153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 710 -5.4795680625829846e-05</internalNodes>
          <leafValues>
            1.1974299699068069e-01 -2.3201279342174530e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 711 6.8555199541151524e-03</internalNodes>
          <leafValues>
            -6.3276126980781555e-02 4.1044250130653381e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 -1.2253040447831154e-02</internalNodes>
          <leafValues>
            5.4883331060409546e-01 -3.9731100201606750e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -3.9058770053088665e-03</internalNodes>
          <leafValues>
            2.4190980195999146e-01 -9.7096011042594910e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 2.7560980524867773e-03</internalNodes>
          <leafValues>
            -1.2569679319858551e-01 1.9456650316715240e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 -7.7662160620093346e-03</internalNodes>
          <leafValues>
            2.9765701293945312e-01 -9.6818156540393829e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 716 3.8997188676148653e-04</internalNodes>
          <leafValues>
            6.2188401818275452e-02 -4.2040899395942688e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 717 3.3579880837351084e-03</internalNodes>
          <leafValues>
            4.7498140484094620e-02 -6.3216882944107056e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 -1.6745539382100105e-02</internalNodes>
          <leafValues>
            7.1098130941390991e-01 -3.9157349616289139e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 719 -6.5409899689257145e-03</internalNodes>
          <leafValues>
            -3.5043171048164368e-01 7.0616953074932098e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 3.0016340315341949e-04</internalNodes>
          <leafValues>
            9.1902457177639008e-02 -2.4618670344352722e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 721 1.4918990433216095e-02</internalNodes>
          <leafValues>
            -5.1909450441598892e-02 5.6636041402816772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 722 4.8153079114854336e-04</internalNodes>
          <leafValues>
            6.4659558236598969e-02 -3.6590608954429626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 -3.0211321427486837e-04</internalNodes>
          <leafValues>
            1.7926569283008575e-01 -1.1410660296678543e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 724 3.8521419628523290e-04</internalNodes>
          <leafValues>
            1.0345619916915894e-01 -2.0072460174560547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 725 8.0837132409214973e-03</internalNodes>
          <leafValues>
            -6.6073462367057800e-02 3.0284249782562256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 726 -2.2804969921708107e-02</internalNodes>
          <leafValues>
            5.2962350845336914e-01 -4.0118999779224396e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 727 1.9440450705587864e-04</internalNodes>
          <leafValues>
            8.1854820251464844e-02 -2.4663360416889191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 728 -1.2848090380430222e-02</internalNodes>
          <leafValues>
            -3.4973311424255371e-01 5.6916229426860809e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 729 -1.0937290498986840e-03</internalNodes>
          <leafValues>
            2.3368680477142334e-01 -9.1604806482791901e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 730 1.0032650316134095e-03</internalNodes>
          <leafValues>
            1.1852180212736130e-01 -1.8469190597534180e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 -4.4688429683446884e-02</internalNodes>
          <leafValues>
            -6.4362460374832153e-01 3.0363269150257111e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 732 8.1657543778419495e-03</internalNodes>
          <leafValues>
            4.3674658983945847e-02 -4.3002089858055115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 733 -1.1717810295522213e-02</internalNodes>
          <leafValues>
            4.1781479120254517e-01 -4.8233699053525925e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 8.4277130663394928e-02</internalNodes>
          <leafValues>
            5.3461279720067978e-02 -3.7952190637588501e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 1.4211839996278286e-02</internalNodes>
          <leafValues>
            4.4900938868522644e-02 -4.2981499433517456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 736 1.5028340276330709e-03</internalNodes>
          <leafValues>
            8.2227639853954315e-02 -2.4706399440765381e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 737 1.0003579780459404e-02</internalNodes>
          <leafValues>
            -5.7221669703722000e-02 3.4609371423721313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 -9.0706320479512215e-03</internalNodes>
          <leafValues>
            4.5058089494705200e-01 -4.2795319110155106e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 739 -3.3141620224341750e-04</internalNodes>
          <leafValues>
            1.8336910009384155e-01 -1.0759949684143066e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 740 1.9723279774188995e-01</internalNodes>
          <leafValues>
            -3.0363829806447029e-02 6.6423428058624268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 -7.1258801035583019e-03</internalNodes>
          <leafValues>
            -8.9225047826766968e-01 2.5669990107417107e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 742 8.6921341717243195e-03</internalNodes>
          <leafValues>
            -7.0764370262622833e-02 2.8210529685020447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 743 8.9262127876281738e-03</internalNodes>
          <leafValues>
            7.1078233420848846e-02 -3.0232560634613037e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 5.7286009192466736e-02</internalNodes>
          <leafValues>
            5.0974130630493164e-02 -3.9196950197219849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 745 3.7920880131423473e-03</internalNodes>
          <leafValues>
            3.3841941505670547e-02 -5.1016288995742798e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 -1.4508679741993546e-03</internalNodes>
          <leafValues>
            3.0879148840904236e-01 -6.3845083117485046e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 9.8390132188796997e-04</internalNodes>
          <leafValues>
            -1.3029569387435913e-01 1.4604410529136658e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 748 -1.7221809830516577e-03</internalNodes>
          <leafValues>
            2.9157009720802307e-01 -6.8549558520317078e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 749 1.0948250070214272e-02</internalNodes>
          <leafValues>
            3.4351408481597900e-02 -4.7702258825302124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 750 -1.7176309484057128e-05</internalNodes>
          <leafValues>
            1.6055269539356232e-01 -1.1690840125083923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 -5.4884208366274834e-03</internalNodes>
          <leafValues>
            -4.3415889143943787e-01 4.6106241643428802e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 -3.0975250992923975e-03</internalNodes>
          <leafValues>
            3.7943339347839355e-01 -5.6860551238059998e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 753 6.4182081259787083e-03</internalNodes>
          <leafValues>
            -1.5858210623264313e-01 1.2335419654846191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 1.1831239797174931e-02</internalNodes>
          <leafValues>
            -4.0929291397333145e-02 4.5878958702087402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 1.3540499843657017e-02</internalNodes>
          <leafValues>
            -5.3725559264421463e-02 3.5056120157241821e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 756 -2.5932150892913342e-03</internalNodes>
          <leafValues>
            1.1010520160198212e-01 -1.6752210259437561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 757 1.6856270376592875e-03</internalNodes>
          <leafValues>
            6.6574357450008392e-02 -3.0835020542144775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 758 2.6524690911173820e-03</internalNodes>
          <leafValues>
            6.6318482160568237e-02 -2.7861338853836060e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 -7.7341729775071144e-03</internalNodes>
          <leafValues>
            1.9718359410762787e-01 -1.0782919824123383e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 760 5.0944271497428417e-03</internalNodes>
          <leafValues>
            8.5337489843368530e-02 -2.4847009778022766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 761 -2.9162371065467596e-03</internalNodes>
          <leafValues>
            -4.7476351261138916e-01 3.3566489815711975e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 762 3.0121419113129377e-03</internalNodes>
          <leafValues>
            -4.7575380653142929e-02 4.2586800456047058e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 3.1694869976490736e-03</internalNodes>
          <leafValues>
            -1.0519450157880783e-01 1.7163459956645966e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 764 2.2327560186386108e-01</internalNodes>
          <leafValues>
            -1.4370209537446499e-02 9.2483651638031006e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 -9.5585048198699951e-02</internalNodes>
          <leafValues>
            -7.4206638336181641e-01 2.7818970382213593e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 3.4773729566950351e-05</internalNodes>
          <leafValues>
            -1.2765780091285706e-01 1.2926669418811798e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 767 7.2459770308341831e-05</internalNodes>
          <leafValues>
            -1.6518579423427582e-01 1.0036809742450714e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>59</maxWeakCount>
      <stageThreshold>-1.0566600561141968e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 768 -6.5778270363807678e-03</internalNodes>
          <leafValues>
            3.3815258741378784e-01 -1.5281909704208374e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 769 -1.0922809597104788e-03</internalNodes>
          <leafValues>
            2.2282369434833527e-01 -1.9308499991893768e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 -2.9759589582681656e-02</internalNodes>
          <leafValues>
            2.5959870219230652e-01 -1.5409409999847412e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 -1.3147540390491486e-02</internalNodes>
          <leafValues>
            1.9033810496330261e-01 -1.6543999314308167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 772 -1.4396329643204808e-03</internalNodes>
          <leafValues>
            2.0071710646152496e-01 -1.2338940054178238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 -3.5928250290453434e-03</internalNodes>
          <leafValues>
            2.3985520005226135e-01 -1.2922149896621704e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 774 -1.5314699849113822e-03</internalNodes>
          <leafValues>
            -4.9014899134635925e-01 1.0275030136108398e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 -6.2372139655053616e-03</internalNodes>
          <leafValues>
            3.1214639544487000e-01 -1.1405629664659500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 -3.3364649862051010e-02</internalNodes>
          <leafValues>
            -4.9520879983901978e-01 5.1328450441360474e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 777 -2.2827699780464172e-02</internalNodes>
          <leafValues>
            3.2558828592300415e-01 -6.5089307725429535e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 778 -8.6199097335338593e-02</internalNodes>
          <leafValues>
            -6.7646330595016479e-01 2.6985699310898781e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 779 -2.1065981127321720e-03</internalNodes>
          <leafValues>
            2.2452430427074432e-01 -1.2610229849815369e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 780 3.9120148867368698e-02</internalNodes>
          <leafValues>
            1.1329399794340134e-01 -2.6860630512237549e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 781 3.5082739777863026e-03</internalNodes>
          <leafValues>
            -1.1359959840774536e-01 2.5649771094322205e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 5.9289898490533233e-04</internalNodes>
          <leafValues>
            -1.4942969381809235e-01 1.6409839689731598e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 783 7.1766850305721164e-04</internalNodes>
          <leafValues>
            9.9905692040920258e-02 -2.1967969834804535e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 784 -2.1803600713610649e-02</internalNodes>
          <leafValues>
            -3.1711721420288086e-01 8.2889586687088013e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 785 -3.2962779514491558e-03</internalNodes>
          <leafValues>
            -3.8048729300498962e-01 6.0819379985332489e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 786 2.4196270387619734e-03</internalNodes>
          <leafValues>
            -9.6013016998767853e-02 2.8540581464767456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 -4.4187481398694217e-04</internalNodes>
          <leafValues>
            2.2127939760684967e-01 -9.7434908151626587e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 788 3.4523929934948683e-03</internalNodes>
          <leafValues>
            3.7553120404481888e-02 -5.7969051599502563e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 -2.1834600716829300e-02</internalNodes>
          <leafValues>
            2.9562139511108398e-01 -8.0048300325870514e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 790 -2.1309500152710825e-04</internalNodes>
          <leafValues>
            2.2814509272575378e-01 -1.0114189982414246e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -1.6166249988600612e-03</internalNodes>
          <leafValues>
            -5.0541198253631592e-01 4.4764541089534760e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 792 7.5959609821438789e-03</internalNodes>
          <leafValues>
            4.5986540615558624e-02 -4.1197681427001953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 793 3.8601809646934271e-03</internalNodes>
          <leafValues>
            -8.6563169956207275e-02 2.4809999763965607e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 794 6.0622231103479862e-03</internalNodes>
          <leafValues>
            -7.5557373464107513e-02 2.8433260321617126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -1.7097420059144497e-03</internalNodes>
          <leafValues>
            -3.5295820236206055e-01 5.8410499244928360e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 1.6515579074621201e-02</internalNodes>
          <leafValues>
            -8.0486953258514404e-02 2.3537430167198181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 4.8465100117027760e-03</internalNodes>
          <leafValues>
            4.1895218193531036e-02 -4.8443049192428589e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 798 -3.1167170032858849e-02</internalNodes>
          <leafValues>
            1.9192309677600861e-01 -1.0268159955739975e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 6.1892281519249082e-04</internalNodes>
          <leafValues>
            -2.1085770428180695e-01 9.3886926770210266e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 1.1946310289204121e-02</internalNodes>
          <leafValues>
            3.9096169173717499e-02 -6.2248629331588745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 801 -7.5677200220525265e-03</internalNodes>
          <leafValues>
            1.5936839580535889e-01 -1.2250780314207077e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 -5.3747411817312241e-02</internalNodes>
          <leafValues>
            -5.5622178316116333e-01 4.1190009564161301e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 803 1.5513530001044273e-02</internalNodes>
          <leafValues>
            -3.9826881140470505e-02 6.2400728464126587e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 804 1.5246650436893106e-03</internalNodes>
          <leafValues>
            7.0138677954673767e-02 -3.0789071321487427e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 805 -4.8315100139006972e-04</internalNodes>
          <leafValues>
            1.7887659370899200e-01 -1.0958620160818100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 806 2.7374739293009043e-03</internalNodes>
          <leafValues>
            2.7478590607643127e-02 -8.8489568233489990e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 807 -6.5787717700004578e-02</internalNodes>
          <leafValues>
            -4.6432140469551086e-01 3.5037148743867874e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 808 1.2409730115905404e-03</internalNodes>
          <leafValues>
            -9.6479237079620361e-02 2.8779220581054688e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 8.1398809561505914e-04</internalNodes>
          <leafValues>
            1.1511719971895218e-01 -1.6766160726547241e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 2.3901820182800293e-02</internalNodes>
          <leafValues>
            -3.2603189349174500e-02 6.0017347335815430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 2.7556600049138069e-02</internalNodes>
          <leafValues>
            -6.6137343645095825e-02 2.9994478821754456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 -3.8070970913395286e-04</internalNodes>
          <leafValues>
            -3.3881181478500366e-01 6.4450770616531372e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 -1.3335429830476642e-03</internalNodes>
          <leafValues>
            1.4588660001754761e-01 -1.3217620551586151e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 -9.3507990241050720e-03</internalNodes>
          <leafValues>
            -5.1177829504013062e-01 3.4969471395015717e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 7.6215229928493500e-03</internalNodes>
          <leafValues>
            2.3249529302120209e-02 -6.9619411230087280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 816 -5.3407860832521692e-05</internalNodes>
          <leafValues>
            2.3727379739284515e-01 -8.6910709738731384e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 817 -1.5332329785451293e-03</internalNodes>
          <leafValues>
            1.9228410720825195e-01 -1.0422399640083313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 4.3135890737175941e-03</internalNodes>
          <leafValues>
            -9.6219547092914581e-02 2.5601211190223694e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 -2.3042880638968199e-04</internalNodes>
          <leafValues>
            -3.1564751267433167e-01 5.8838598430156708e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 820 -7.8411828726530075e-03</internalNodes>
          <leafValues>
            -6.6340929269790649e-01 2.4500999599695206e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 821 1.7103740572929382e-01</internalNodes>
          <leafValues>
            3.3831499516963959e-02 -4.5615941286087036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 822 -1.6011140542104840e-03</internalNodes>
          <leafValues>
            2.1574890613555908e-01 -8.3622530102729797e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 823 -1.0535780340433121e-02</internalNodes>
          <leafValues>
            2.4552319943904877e-01 -8.2384489476680756e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 -5.8351638726890087e-03</internalNodes>
          <leafValues>
            -4.7807329893112183e-01 4.4086221605539322e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 -1.8706109374761581e-02</internalNodes>
          <leafValues>
            -6.0024029016494751e-01 2.1410040557384491e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 826 -9.3307439237833023e-04</internalNodes>
          <leafValues>
            2.4323590099811554e-01 -7.4165716767311096e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>88</maxWeakCount>
      <stageThreshold>-9.7693431377410889e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 827 1.0646229609847069e-02</internalNodes>
          <leafValues>
            -1.3861389458179474e-01 2.6494070887565613e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 3.5298269242048264e-02</internalNodes>
          <leafValues>
            -7.5821727514266968e-02 3.9021068811416626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 829 7.5638387352228165e-04</internalNodes>
          <leafValues>
            -9.5521442592144012e-02 2.9061999917030334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 9.2497706413269043e-02</internalNodes>
          <leafValues>
            -2.7704238891601562e-01 7.9474702477455139e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 831 -2.9340879991650581e-03</internalNodes>
          <leafValues>
            2.2989539802074432e-01 -7.8550010919570923e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 832 -8.6535848677158356e-02</internalNodes>
          <leafValues>
            4.7744810581207275e-01 -6.8231220357120037e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 833 5.4699288739357144e-05</internalNodes>
          <leafValues>
            -2.2642609477043152e-01 8.8192112743854523e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 -3.6592520773410797e-02</internalNodes>
          <leafValues>
            2.7353870868682861e-01 -9.8606742918491364e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 2.6469118893146515e-03</internalNodes>
          <leafValues>
            -4.4083978980779648e-02 3.1445288658142090e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 836 -4.4271810911595821e-03</internalNodes>
          <leafValues>
            2.3822729289531708e-01 -8.6784273386001587e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -5.1882481202483177e-03</internalNodes>
          <leafValues>
            1.5042769908905029e-01 -1.2672109901905060e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 838 4.5530400238931179e-03</internalNodes>
          <leafValues>
            -5.5945020169019699e-02 3.6501631140708923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 839 1.4562410302460194e-02</internalNodes>
          <leafValues>
            3.6397770047187805e-02 -5.3559190034866333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 840 6.8677567469421774e-05</internalNodes>
          <leafValues>
            -1.7479629814624786e-01 1.1068709939718246e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 841 -5.9744901955127716e-03</internalNodes>
          <leafValues>
            3.1077870726585388e-01 -6.6530227661132812e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 -5.8691250160336494e-03</internalNodes>
          <leafValues>
            -3.1901490688323975e-01 6.3931830227375031e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 843 -1.1140310205519199e-02</internalNodes>
          <leafValues>
            2.4364790320396423e-01 -8.0935180187225342e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 -5.8643531054258347e-02</internalNodes>
          <leafValues>
            -7.6083260774612427e-01 3.0809629708528519e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 -4.6097282320261002e-03</internalNodes>
          <leafValues>
            -4.5315021276473999e-01 2.9879059642553329e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 846 -9.3032103031873703e-03</internalNodes>
          <leafValues>
            1.4513379335403442e-01 -1.1033169925212860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 1.3253629440441728e-03</internalNodes>
          <leafValues>
            -9.7698956727981567e-02 1.9646440446376801e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 848 4.9800761044025421e-03</internalNodes>
          <leafValues>
            3.3648081123828888e-02 -3.9792209863662720e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 -7.6542161405086517e-03</internalNodes>
          <leafValues>
            9.0841993689537048e-02 -1.5967549383640289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 850 -3.8920590281486511e-01</internalNodes>
          <leafValues>
            -6.6571092605590820e-01 1.9028829410672188e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 -1.0019669681787491e-01</internalNodes>
          <leafValues>
            -5.7559269666671753e-01 2.4282779544591904e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 852 7.3541211895644665e-04</internalNodes>
          <leafValues>
            8.7919801473617554e-02 -1.6195340454578400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 853 -3.4802639856934547e-03</internalNodes>
          <leafValues>
            2.6064491271972656e-01 -6.0200810432434082e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 854 8.4000425413250923e-03</internalNodes>
          <leafValues>
            -1.0979729890823364e-01 1.5707309544086456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 855 2.3786011151969433e-03</internalNodes>
          <leafValues>
            3.6058239638805389e-02 -4.7277191281318665e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 856 7.3831682093441486e-03</internalNodes>
          <leafValues>
            -3.5756360739469528e-02 4.9498590826988220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 857 3.2115620560944080e-03</internalNodes>
          <leafValues>
            -1.0125560313463211e-01 1.5747989714145660e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 858 -7.8209668397903442e-02</internalNodes>
          <leafValues>
            -7.6627081632614136e-01 2.2965829819440842e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 859 5.3303989261621609e-05</internalNodes>
          <leafValues>
            -1.3414350152015686e-01 1.1114919930696487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 860 -9.6419155597686768e-03</internalNodes>
          <leafValues>
            2.5068029761314392e-01 -6.6608138382434845e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 861 -7.1092672646045685e-02</internalNodes>
          <leafValues>
            -4.0056818723678589e-01 4.0297791361808777e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 862 3.5171560011804104e-04</internalNodes>
          <leafValues>
            4.1861180216073990e-02 -3.2961198687553406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 863 -3.3458150574006140e-04</internalNodes>
          <leafValues>
            -2.6029831171035767e-01 6.7892737686634064e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 864 -4.1451421566307545e-03</internalNodes>
          <leafValues>
            2.3967699706554413e-01 -7.2093337774276733e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 865 3.1754500232636929e-03</internalNodes>
          <leafValues>
            -7.1235269308090210e-02 2.4128450453281403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 -5.5184490047395229e-03</internalNodes>
          <leafValues>
            5.0320237874984741e-01 -2.9686680063605309e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 867 -3.0242869979701936e-04</internalNodes>
          <leafValues>
            2.4879050254821777e-01 -5.6758578866720200e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 868 -1.3125919504091144e-03</internalNodes>
          <leafValues>
            3.1747800111770630e-01 -4.1845861822366714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 869 -2.7123570907860994e-04</internalNodes>
          <leafValues>
            -2.7042070031166077e-01 5.6828990578651428e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 -7.3241777718067169e-03</internalNodes>
          <leafValues>
            2.7556678652763367e-01 -5.4252970963716507e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 -1.6851710155606270e-02</internalNodes>
          <leafValues>
            -3.4852910041809082e-01 4.5368999242782593e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 2.9902100563049316e-02</internalNodes>
          <leafValues>
            3.1621079891920090e-02 -4.3114370107650757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 873 2.8902660124003887e-03</internalNodes>
          <leafValues>
            3.8029961287975311e-02 -3.7027099728584290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 -1.9242949783802032e-03</internalNodes>
          <leafValues>
            2.4800279736518860e-01 -5.9333298355340958e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 4.9354149959981441e-03</internalNodes>
          <leafValues>
            -8.3068400621414185e-02 2.2043809294700623e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 8.2075603306293488e-02</internalNodes>
          <leafValues>
            -1.9413439556956291e-02 6.9089287519454956e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 877 -2.4699489586055279e-04</internalNodes>
          <leafValues>
            -2.4660569429397583e-01 6.4776450395584106e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 -1.8365769647061825e-03</internalNodes>
          <leafValues>
            2.8836160898208618e-01 -5.3390458226203918e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 879 -4.9553811550140381e-03</internalNodes>
          <leafValues>
            1.2740829586982727e-01 -1.2559419870376587e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 880 -8.3086621016263962e-03</internalNodes>
          <leafValues>
            2.3478110134601593e-01 -7.1676492691040039e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 -1.0879919677972794e-01</internalNodes>
          <leafValues>
            -2.5992238521575928e-01 5.8689739555120468e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 882 -9.6786450594663620e-03</internalNodes>
          <leafValues>
            -7.0720428228378296e-01 1.8749259412288666e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 883 -2.7136830613017082e-02</internalNodes>
          <leafValues>
            -5.8384227752685547e-01 2.1684130653738976e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 -6.5389778465032578e-03</internalNodes>
          <leafValues>
            -5.9748911857604980e-01 2.1480310708284378e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 885 -1.2095630168914795e-02</internalNodes>
          <leafValues>
            1.3269039988517761e-01 -9.9722720682621002e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 -1.6776099801063538e-01</internalNodes>
          <leafValues>
            -5.6655067205429077e-01 3.2123088836669922e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 -1.3262550346553326e-02</internalNodes>
          <leafValues>
            1.1495590209960938e-01 -1.1738389730453491e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 7.6744519174098969e-02</internalNodes>
          <leafValues>
            -3.1413231045007706e-02 5.9935492277145386e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 889 5.0785229541361332e-03</internalNodes>
          <leafValues>
            -5.2911940962076187e-02 2.3342399299144745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 3.1800279393792152e-03</internalNodes>
          <leafValues>
            -7.7734388411045074e-02 1.7652909457683563e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 -1.7729829996824265e-03</internalNodes>
          <leafValues>
            1.9591629505157471e-01 -7.9752199351787567e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 -4.8560940194875002e-04</internalNodes>
          <leafValues>
            -2.8800371289253235e-01 4.9047119915485382e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 3.6554320831783116e-04</internalNodes>
          <leafValues>
            6.7922897636890411e-02 -2.2499430179595947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 -2.6938671362586319e-04</internalNodes>
          <leafValues>
            1.6582170128822327e-01 -8.9744098484516144e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 895 7.8684233129024506e-02</internalNodes>
          <leafValues>
            2.6081679388880730e-02 -5.5693739652633667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 -7.3774810880422592e-04</internalNodes>
          <leafValues>
            1.4036870002746582e-01 -1.1800300329923630e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 2.3957829922437668e-02</internalNodes>
          <leafValues>
            3.0470740050077438e-02 -4.6159979701042175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 -1.6239080578088760e-03</internalNodes>
          <leafValues>
            2.6327079534530640e-01 -5.6765370070934296e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 899 -9.0819748584181070e-04</internalNodes>
          <leafValues>
            1.5462459623813629e-01 -1.1087069660425186e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 900 3.9806248969398439e-04</internalNodes>
          <leafValues>
            5.5630370974540710e-02 -2.8331959247589111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 2.0506449509412050e-03</internalNodes>
          <leafValues>
            -9.1604836285114288e-02 1.7585539817810059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 902 2.6742549613118172e-02</internalNodes>
          <leafValues>
            6.2003031373023987e-02 -2.4487000703811646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 903 -2.1497008856385946e-03</internalNodes>
          <leafValues>
            2.9449298977851868e-01 -5.3218148648738861e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 904 5.6671658530831337e-03</internalNodes>
          <leafValues>
            -6.4298242330551147e-02 2.4905680119991302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 905 6.8317902332637459e-05</internalNodes>
          <leafValues>
            -1.6819630563259125e-01 9.6548579633235931e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 906 1.7600439605303109e-04</internalNodes>
          <leafValues>
            6.5308012068271637e-02 -2.4267880618572235e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 907 4.1861608624458313e-03</internalNodes>
          <leafValues>
            -9.7988583147525787e-02 1.8052889406681061e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 908 -2.1808340679854155e-03</internalNodes>
          <leafValues>
            1.9231270253658295e-01 -9.4123929738998413e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 909 2.1730400621891022e-02</internalNodes>
          <leafValues>
            3.5578511655330658e-02 -4.5088538527488708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 -1.4780269935727119e-02</internalNodes>
          <leafValues>
            -4.3927010893821716e-01 3.1735591590404510e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 911 -3.6145891062915325e-03</internalNodes>
          <leafValues>
            1.9811479747295380e-01 -7.7701419591903687e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 1.8892709631472826e-03</internalNodes>
          <leafValues>
            1.9962439313530922e-02 -7.2041720151901245e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 -1.3822480104863644e-03</internalNodes>
          <leafValues>
            9.8466947674751282e-02 -1.4881080389022827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 -3.9505911991000175e-03</internalNodes>
          <leafValues>
            1.1593230068683624e-01 -1.2791970372200012e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>58</maxWeakCount>
      <stageThreshold>-1.0129359960556030e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 915 -1.9395539537072182e-02</internalNodes>
          <leafValues>
            4.7474750876426697e-01 -1.1721090227365494e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 916 1.3118919916450977e-02</internalNodes>
          <leafValues>
            -2.5552129745483398e-01 1.6378800570964813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 917 -5.1606801571324468e-04</internalNodes>
          <leafValues>
            1.9452619552612305e-01 -1.7448890209197998e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 918 -1.3184159994125366e-02</internalNodes>
          <leafValues>
            4.4181451201438904e-01 -9.0048752725124359e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 919 3.4657081123441458e-03</internalNodes>
          <leafValues>
            -1.3477090001106262e-01 1.8056340515613556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 920 6.2980200164020061e-03</internalNodes>
          <leafValues>
            -5.4164979606866837e-02 3.6033380031585693e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 1.6879989998415112e-03</internalNodes>
          <leafValues>
            -1.9997949898242950e-01 1.2021599709987640e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 922 3.6039709812030196e-04</internalNodes>
          <leafValues>
            1.0524140298366547e-01 -2.4116060137748718e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 923 -1.5276849735528231e-03</internalNodes>
          <leafValues>
            2.8135529160499573e-01 -6.8964816629886627e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 924 3.5033570602536201e-03</internalNodes>
          <leafValues>
            -8.2519583404064178e-02 4.0713590383529663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 -4.7337161377072334e-03</internalNodes>
          <leafValues>
            1.9727009534835815e-01 -1.1710140109062195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 -1.1557149700820446e-02</internalNodes>
          <leafValues>
            -5.6061112880706787e-01 6.8170957267284393e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 -2.7445720508694649e-02</internalNodes>
          <leafValues>
            4.9718621373176575e-01 -6.2380149960517883e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 928 -5.2825778722763062e-02</internalNodes>
          <leafValues>
            1.6921220719814301e-01 -1.3093550503253937e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 -2.9849699139595032e-01</internalNodes>
          <leafValues>
            -6.4649671316146851e-01 4.0076818317174911e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 -2.6307269581593573e-04</internalNodes>
          <leafValues>
            2.5127941370010376e-01 -8.9494839310646057e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 931 2.3261709429789335e-04</internalNodes>
          <leafValues>
            -8.6843989789485931e-02 2.3831979930400848e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 932 2.3631360090803355e-04</internalNodes>
          <leafValues>
            1.1554460227489471e-01 -1.8936349451541901e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 933 2.0742209162563086e-03</internalNodes>
          <leafValues>
            -4.8594851046800613e-02 5.7485991716384888e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 934 -7.0308889262378216e-03</internalNodes>
          <leafValues>
            -5.4120808839797974e-01 4.8743750900030136e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 8.2652270793914795e-03</internalNodes>
          <leafValues>
            2.6494519785046577e-02 -6.1728459596633911e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 2.0042760297656059e-04</internalNodes>
          <leafValues>
            -1.1768630146980286e-01 1.6333860158920288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 1.6470040427520871e-03</internalNodes>
          <leafValues>
            -5.9954918920993805e-02 3.5179701447486877e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 938 -3.5642538568936288e-04</internalNodes>
          <leafValues>
            -3.4420299530029297e-01 6.4948253333568573e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 -3.0935870483517647e-02</internalNodes>
          <leafValues>
            1.9979700446128845e-01 -9.7693696618080139e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 -6.3578772824257612e-04</internalNodes>
          <leafValues>
            -3.1481391191482544e-01 5.9425041079521179e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 -1.1862180195748806e-02</internalNodes>
          <leafValues>
            2.0043690502643585e-01 -8.9447543025016785e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 942 7.1508930996060371e-03</internalNodes>
          <leafValues>
            -3.9006061851978302e-02 5.3327161073684692e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 943 -2.0059191156178713e-03</internalNodes>
          <leafValues>
            -2.8469720482826233e-01 7.0723608136177063e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 944 3.6412389017641544e-03</internalNodes>
          <leafValues>
            -1.0660319775342941e-01 2.4944800138473511e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 945 -1.3467429578304291e-01</internalNodes>
          <leafValues>
            4.9910080432891846e-01 -4.0332220494747162e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 -2.2547659464180470e-03</internalNodes>
          <leafValues>
            1.6851690411567688e-01 -1.1119280010461807e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 4.3842289596796036e-03</internalNodes>
          <leafValues>
            8.6139492690563202e-02 -2.7431771159172058e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 948 -7.3361168615520000e-03</internalNodes>
          <leafValues>
            2.4875210225582123e-01 -9.5919162034988403e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 949 6.4666912658140063e-04</internalNodes>
          <leafValues>
            6.7431576550006866e-02 -3.3754080533981323e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 2.2983769304119051e-04</internalNodes>
          <leafValues>
            -8.3903051912784576e-02 2.4584099650382996e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 951 6.7039071582257748e-03</internalNodes>
          <leafValues>
            2.9079329222440720e-02 -6.9055938720703125e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 952 5.0734888645820320e-05</internalNodes>
          <leafValues>
            -1.5696719288825989e-01 1.1965429782867432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 953 -2.0335559546947479e-01</internalNodes>
          <leafValues>
            -6.9506347179412842e-01 2.7507519349455833e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 954 9.4939414411783218e-03</internalNodes>
          <leafValues>
            -8.7449371814727783e-02 2.3968330025672913e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 955 -2.4055240210145712e-03</internalNodes>
          <leafValues>
            2.1150960028171539e-01 -1.3148930668830872e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 -1.1342419747961685e-04</internalNodes>
          <leafValues>
            1.5233789384365082e-01 -1.2725900113582611e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 957 1.4992210082709789e-02</internalNodes>
          <leafValues>
            -3.4127969294786453e-02 5.0624072551727295e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 958 7.4068200774490833e-04</internalNodes>
          <leafValues>
            4.8764750361442566e-02 -4.0225321054458618e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 959 -4.2459447868168354e-03</internalNodes>
          <leafValues>
            2.1554760634899139e-01 -8.7126992642879486e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 6.8655109498649836e-04</internalNodes>
          <leafValues>
            -7.5418718159198761e-02 2.6405909657478333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 961 -1.6751460731029510e-02</internalNodes>
          <leafValues>
            -6.7729032039642334e-01 3.2918728888034821e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 962 -2.6301678735762835e-04</internalNodes>
          <leafValues>
            2.2725869715213776e-01 -9.0534873306751251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 963 4.3398610432632267e-04</internalNodes>
          <leafValues>
            5.5894378572702408e-02 -3.5592669248580933e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 964 -2.0150149241089821e-02</internalNodes>
          <leafValues>
            1.9162760674953461e-01 -9.4929970800876617e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 965 -1.4452129602432251e-02</internalNodes>
          <leafValues>
            -6.8510341644287109e-01 2.5422170758247375e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 966 -2.1149739623069763e-02</internalNodes>
          <leafValues>
            3.7533190846443176e-01 -5.1496580243110657e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 967 2.1137770265340805e-02</internalNodes>
          <leafValues>
            2.9083080589771271e-02 -8.9430367946624756e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 968 1.1524349683895707e-03</internalNodes>
          <leafValues>
            -6.9694936275482178e-02 2.7299800515174866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 969 -1.9070580310653895e-04</internalNodes>
          <leafValues>
            1.8228119611740112e-01 -9.8367072641849518e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 -3.6349631845951080e-02</internalNodes>
          <leafValues>
            -8.3693099021911621e-01 2.5055760517716408e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 971 -9.0632075443863869e-03</internalNodes>
          <leafValues>
            4.1463500261306763e-01 -5.4413449019193649e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 972 -2.0535490475594997e-03</internalNodes>
          <leafValues>
            -1.9750310480594635e-01 1.0506899654865265e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>93</maxWeakCount>
      <stageThreshold>-9.7747492790222168e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 973 -2.2717019543051720e-02</internalNodes>
          <leafValues>
            2.4288550019264221e-01 -1.4745520055294037e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 974 2.5505950674414635e-02</internalNodes>
          <leafValues>
            -2.8551739454269409e-01 1.0837209969758987e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 -2.6640091091394424e-03</internalNodes>
          <leafValues>
            2.9275730252265930e-01 -1.0372710227966309e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 976 -3.8115289062261581e-03</internalNodes>
          <leafValues>
            2.1426899731159210e-01 -1.3811139762401581e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 977 -1.6732690855860710e-02</internalNodes>
          <leafValues>
            2.6550260186195374e-01 -4.3911330401897430e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 978 4.9277010839432478e-04</internalNodes>
          <leafValues>
            2.1104559302330017e-02 -4.2971360683441162e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 -3.6691110581159592e-02</internalNodes>
          <leafValues>
            5.3992420434951782e-01 -4.3648801743984222e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 980 1.2615970335900784e-03</internalNodes>
          <leafValues>
            -1.2933869659900665e-01 1.6638770699501038e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 981 -8.4106856957077980e-03</internalNodes>
          <leafValues>
            -9.4698411226272583e-01 2.1465849131345749e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 982 6.4902722835540771e-02</internalNodes>
          <leafValues>
            -7.1727760136127472e-02 2.6613479852676392e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 983 3.0305000022053719e-02</internalNodes>
          <leafValues>
            -8.2782492041587830e-02 2.7694320678710938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 984 2.5875340215861797e-03</internalNodes>
          <leafValues>
            -1.2966169416904449e-01 1.7756630480289459e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 985 -7.0240451022982597e-03</internalNodes>
          <leafValues>
            -6.4243179559707642e-01 3.9943210780620575e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 986 -1.0099769569933414e-03</internalNodes>
          <leafValues>
            1.4176610112190247e-01 -1.1659970134496689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 987 -4.1179071558872238e-05</internalNodes>
          <leafValues>
            1.5687669813632965e-01 -1.1127340048551559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 -4.7293151146732271e-04</internalNodes>
          <leafValues>
            -3.3554559946060181e-01 4.5977730304002762e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 989 -1.7178079579025507e-03</internalNodes>
          <leafValues>
            1.6952909529209137e-01 -1.0578069835901260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 990 -1.3333169743418694e-02</internalNodes>
          <leafValues>
            -5.8257812261581421e-01 3.0978430062532425e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 991 -1.8783430568873882e-03</internalNodes>
          <leafValues>
            1.4266879856586456e-01 -1.1131259799003601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 992 -6.5765981562435627e-03</internalNodes>
          <leafValues>
            2.7561360597610474e-01 -5.3100328892469406e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 993 -7.7210381277836859e-05</internalNodes>
          <leafValues>
            1.3240240514278412e-01 -1.1167799681425095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 994 2.1968539804220200e-02</internalNodes>
          <leafValues>
            -2.6968160644173622e-02 5.0067168474197388e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 995 -2.7445750311017036e-02</internalNodes>
          <leafValues>
            -2.4086740612983704e-01 6.0478270053863525e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 996 7.8305849456228316e-05</internalNodes>
          <leafValues>
            -1.3334889709949493e-01 1.0123469680547714e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 997 7.0190683007240295e-02</internalNodes>
          <leafValues>
            -5.4863780736923218e-02 2.4809940159320831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 998 -7.1902133524417877e-02</internalNodes>
          <leafValues>
            -3.7846690416336060e-01 4.2210999876260757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 999 -1.0780979692935944e-01</internalNodes>
          <leafValues>
            -3.7486588954925537e-01 4.2833440005779266e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1000 1.4364200178533792e-03</internalNodes>
          <leafValues>
            8.0476358532905579e-02 -1.7263789474964142e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1001 6.8289190530776978e-02</internalNodes>
          <leafValues>
            -3.5595789551734924e-02 4.0761318802833557e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1002 -6.8037179298698902e-03</internalNodes>
          <leafValues>
            1.9233790040016174e-01 -8.2368023693561554e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1003 -5.6193489581346512e-04</internalNodes>
          <leafValues>
            1.3057120144367218e-01 -1.4355149865150452e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1004 -5.8276649564504623e-02</internalNodes>
          <leafValues>
            -3.0125439167022705e-01 5.2819650620222092e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1005 -6.1205718666315079e-03</internalNodes>
          <leafValues>
            2.2043900191783905e-01 -7.5691752135753632e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1006 -1.3594309799373150e-02</internalNodes>
          <leafValues>
            -3.9049360156059265e-01 4.1857108473777771e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1007 1.3626200379803777e-03</internalNodes>
          <leafValues>
            -9.5363423228263855e-02 1.4970320463180542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1008 -1.5074219845701009e-04</internalNodes>
          <leafValues>
            -2.3945580422878265e-01 6.4798332750797272e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1009 -7.7414259314537048e-02</internalNodes>
          <leafValues>
            5.5941981077194214e-01 -2.4516880512237549e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1010 9.2117872554808855e-04</internalNodes>
          <leafValues>
            5.4928861558437347e-02 -2.7934810519218445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1011 1.0250780032947659e-03</internalNodes>
          <leafValues>
            -6.2167309224605560e-02 2.4976369738578796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1012 -8.1174750812351704e-04</internalNodes>
          <leafValues>
            2.3437939584255219e-01 -6.5725810825824738e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1013 8.3431020379066467e-02</internalNodes>
          <leafValues>
            5.0954800099134445e-02 -3.1020981073379517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1014 -9.2014456167817116e-03</internalNodes>
          <leafValues>
            -3.9242538809776306e-01 3.2926950603723526e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1015 -2.9086650465615094e-04</internalNodes>
          <leafValues>
            -3.1039750576019287e-01 4.9711819738149643e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1016 7.7576898038387299e-03</internalNodes>
          <leafValues>
            -4.4040750712156296e-02 3.6431351304054260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1017 -1.2466090172529221e-01</internalNodes>
          <leafValues>
            -8.1957077980041504e-01 1.9150640815496445e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1018 1.3242550194263458e-02</internalNodes>
          <leafValues>
            3.8988839834928513e-02 -3.3230680227279663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1019 -6.6770128905773163e-03</internalNodes>
          <leafValues>
            -3.5790139436721802e-01 4.0460210293531418e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1020 -2.7479929849505424e-03</internalNodes>
          <leafValues>
            2.5253900885581970e-01 -5.6427821516990662e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1021 8.2659651525318623e-04</internalNodes>
          <leafValues>
            -7.1988657116889954e-02 2.2780479490756989e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1022 -5.0153400748968124e-02</internalNodes>
          <leafValues>
            -6.3036471605300903e-01 2.7462050318717957e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1023 7.4203149415552616e-03</internalNodes>
          <leafValues>
            -6.6610716283321381e-02 2.7787339687347412e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1024 -6.7951780511066318e-04</internalNodes>
          <leafValues>
            -3.6327061057090759e-01 4.2795430868864059e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1025 -1.9305750029161572e-03</internalNodes>
          <leafValues>
            1.4196230471134186e-01 -1.0759980231523514e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1026 -3.8132671033963561e-04</internalNodes>
          <leafValues>
            2.1591760218143463e-01 -7.0202663540840149e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1027 -7.0990346372127533e-02</internalNodes>
          <leafValues>
            4.5266601443290710e-01 -4.0750481188297272e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1028 -5.3368080407381058e-02</internalNodes>
          <leafValues>
            -6.7674058675765991e-01 1.9288340583443642e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1029 -2.0064849406480789e-02</internalNodes>
          <leafValues>
            -4.3365430831909180e-01 3.1853288412094116e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1030 1.1976360110566020e-03</internalNodes>
          <leafValues>
            -2.6559870690107346e-02 5.0797182321548462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1031 -2.2697300300933421e-04</internalNodes>
          <leafValues>
            1.8012599647045135e-01 -8.3606548607349396e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1032 1.5262699685990810e-02</internalNodes>
          <leafValues>
            -2.0238929986953735e-01 6.7422017455101013e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1033 -2.0811769366264343e-01</internalNodes>
          <leafValues>
            6.6943860054016113e-01 -2.2452110424637794e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1034 1.5514369588345289e-03</internalNodes>
          <leafValues>
            -7.5121842324733734e-02 1.7326919734477997e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1035 -5.2924010902643204e-02</internalNodes>
          <leafValues>
            2.4992519617080688e-01 -6.2879167497158051e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1036 -2.1648850291967392e-02</internalNodes>
          <leafValues>
            -2.9194280505180359e-01 5.2614491432905197e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1037 -2.2905069636180997e-04</internalNodes>
          <leafValues>
            -2.2117300331592560e-01 6.3168339431285858e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1038 5.0170070608146489e-05</internalNodes>
          <leafValues>
            -1.1510709673166275e-01 1.1611440032720566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1039 -1.6416069411206990e-04</internalNodes>
          <leafValues>
            1.5871520340442657e-01 -8.2600601017475128e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1040 -1.2003289535641670e-02</internalNodes>
          <leafValues>
            1.2218090146780014e-01 -1.1229699850082397e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1041 -1.7784100025892258e-02</internalNodes>
          <leafValues>
            -3.5072788596153259e-01 3.1341921538114548e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1042 -6.3457582145929337e-03</internalNodes>
          <leafValues>
            1.3078069686889648e-01 -1.0574410110712051e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1043 -7.9523242311552167e-04</internalNodes>
          <leafValues>
            1.7204670608043671e-01 -8.6001992225646973e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1044 -3.1029590172693133e-04</internalNodes>
          <leafValues>
            -2.8433170914649963e-01 5.1817119121551514e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1045 -1.7053710296750069e-02</internalNodes>
          <leafValues>
            3.9242428541183472e-01 -4.0143270045518875e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1046 4.6504959464073181e-03</internalNodes>
          <leafValues>
            -3.1837560236454010e-02 4.1237699985504150e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1047 -1.0358760133385658e-02</internalNodes>
          <leafValues>
            -5.6993198394775391e-01 2.9248379170894623e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1048 -2.2196240723133087e-02</internalNodes>
          <leafValues>
            -4.5605289936065674e-01 2.6285989210009575e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1049 -7.0536029525101185e-03</internalNodes>
          <leafValues>
            1.5998320281505585e-01 -9.1594859957695007e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1050 -5.7094299700111151e-04</internalNodes>
          <leafValues>
            -1.4076329767704010e-01 1.0287419706583023e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1051 -2.2152599412947893e-03</internalNodes>
          <leafValues>
            1.6593599319458008e-01 -8.5273988544940948e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1052 -2.8084890916943550e-02</internalNodes>
          <leafValues>
            2.7022340893745422e-01 -5.5873811244964600e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1053 2.1515151020139456e-03</internalNodes>
          <leafValues>
            4.2472891509532928e-02 -3.2005849480628967e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1054 -2.9733829433098435e-04</internalNodes>
          <leafValues>
            1.6177169978618622e-01 -8.5115589201450348e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1055 -1.6694780439138412e-02</internalNodes>
          <leafValues>
            -4.2858770489692688e-01 3.0541609972715378e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1056 1.1982990056276321e-01</internalNodes>
          <leafValues>
            -1.6277290880680084e-02 7.9846781492233276e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1057 -3.5499420482665300e-04</internalNodes>
          <leafValues>
            1.5935939550399780e-01 -8.3272881805896759e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1058 -1.8226269632577896e-02</internalNodes>
          <leafValues>
            1.9527280330657959e-01 -7.3939889669418335e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1059 -4.0238600922748446e-04</internalNodes>
          <leafValues>
            7.9101808369159698e-02 -2.0806129276752472e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1060 4.0892060496844351e-04</internalNodes>
          <leafValues>
            1.0036630183458328e-01 -1.5128210186958313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1061 9.5368112670257688e-04</internalNodes>
          <leafValues>
            -7.3011666536331177e-02 2.1752020716667175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1062 4.3081799149513245e-01</internalNodes>
          <leafValues>
            -2.7450699359178543e-02 5.7061582803726196e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1063 5.3564831614494324e-04</internalNodes>
          <leafValues>
            1.1587540060281754e-01 -1.2790560722351074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1064 2.4430730263702571e-05</internalNodes>
          <leafValues>
            -1.6816629469394684e-01 8.0449983477592468e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1065 -5.5345650762319565e-02</internalNodes>
          <leafValues>
            4.5338949561119080e-01 -3.1222779303789139e-02</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          0 8 20 12 -1.</_>
        <_>
          0 14 20 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 4 15 -1.</_>
        <_>
          9 6 4 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 9 2 -1.</_>
        <_>
          9 10 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 10 9 -1.</_>
        <_>
          7 3 10 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 2 2 18 -1.</_>
        <_>
          12 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 8 6 -1.</_>
        <_>
          8 9 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 17 18 -1.</_>
        <_>
          2 6 17 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 1 8 -1.</_>
        <_>
          10 14 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 10 9 2 -1.</_>
        <_>
          10 10 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 6 6 -1.</_>
        <_>
          5 3 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 15 9 -1.</_>
        <_>
          3 4 15 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 3 9 6 -1.</_>
        <_>
          6 5 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 6 3 -1.</_>
        <_>
          10 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 10 9 1 -1.</_>
        <_>
          12 10 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 6 11 -1.</_>
        <_>
          3 7 2 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 3 1 -1.</_>
        <_>
          10 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 1 2 -1.</_>
        <_>
          16 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 6 3 -1.</_>
        <_>
          11 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 5 18 -1.</_>
        <_>
          8 6 5 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 9 7 -1.</_>
        <_>
          9 7 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 6 6 10 -1.</_>
        <_>
          16 6 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 9 5 -1.</_>
        <_>
          12 8 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 9 6 -1.</_>
        <_>
          6 7 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 6 6 -1.</_>
        <_>
          3 7 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 4 18 -1.</_>
        <_>
          16 6 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 3 3 -1.</_>
        <_>
          0 18 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 2 1 -1.</_>
        <_>
          17 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 20 12 -1.</_>
        <_>
          0 14 20 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 9 8 -1.</_>
        <_>
          9 6 3 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 3 12 9 -1.</_>
        <_>
          5 6 12 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 16 1 2 -1.</_>
        <_>
          4 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 10 2 1 -1.</_>
        <_>
          19 10 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 6 5 -1.</_>
        <_>
          11 8 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 1 -1.</_>
        <_>
          1 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 6 6 -1.</_>
        <_>
          8 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 6 7 -1.</_>
        <_>
          13 7 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 14 1 2 -1.</_>
        <_>
          19 15 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 1 2 -1.</_>
        <_>
          6 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 7 2 7 -1.</_>
        <_>
          15 7 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 2 4 -1.</_>
        <_>
          7 8 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 6 -1.</_>
        <_>
          5 10 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 1 3 -1.</_>
        <_>
          2 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 3 6 -1.</_>
        <_>
          7 7 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 9 12 -1.</_>
        <_>
          9 7 3 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 11 12 -1.</_>
        <_>
          6 6 11 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 5 8 -1.</_>
        <_>
          1 16 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 7 6 7 -1.</_>
        <_>
          16 7 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 6 6 -1.</_>
        <_>
          12 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 4 2 -1.</_>
        <_>
          16 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 2 3 -1.</_>
        <_>
          18 18 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 7 -1.</_>
        <_>
          10 7 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 6 8 -1.</_>
        <_>
          7 6 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 6 11 -1.</_>
        <_>
          4 6 2 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 10 12 8 -1.</_>
        <_>
          8 14 12 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 6 3 -1.</_>
        <_>
          9 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 3 3 -1.</_>
        <_>
          11 9 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 3 6 -1.</_>
        <_>
          9 8 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 5 -1.</_>
        <_>
          9 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 1 3 -1.</_>
        <_>
          6 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 4 2 -1.</_>
        <_>
          0 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 11 9 -1.</_>
        <_>
          4 4 11 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 14 9 -1.</_>
        <_>
          3 4 14 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 6 4 -1.</_>
        <_>
          2 9 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 13 1 2 -1.</_>
        <_>
          18 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 5 3 11 -1.</_>
        <_>
          14 5 1 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 8 2 -1.</_>
        <_>
          0 18 4 1 2.</_>
        <_>
          4 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 5 -1.</_>
        <_>
          9 8 4 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 11 10 -1.</_>
        <_>
          4 12 11 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 9 6 4 -1.</_>
        <_>
          16 9 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 6 8 -1.</_>
        <_>
          3 7 3 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 3 3 -1.</_>
        <_>
          0 17 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 11 12 1 -1.</_>
        <_>
          11 11 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 9 4 -1.</_>
        <_>
          7 8 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 6 4 -1.</_>
        <_>
          7 16 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 4 10 -1.</_>
        <_>
          4 9 2 5 2.</_>
        <_>
          6 14 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 6 4 -1.</_>
        <_>
          6 8 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 2 18 -1.</_>
        <_>
          10 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 8 6 -1.</_>
        <_>
          0 5 4 3 2.</_>
        <_>
          4 8 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 5 -1.</_>
        <_>
          8 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 14 -1.</_>
        <_>
          18 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 4 2 -1.</_>
        <_>
          10 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 6 3 -1.</_>
        <_>
          1 18 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 3 5 -1.</_>
        <_>
          12 8 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 3 4 -1.</_>
        <_>
          12 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 6 5 -1.</_>
        <_>
          13 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 6 7 -1.</_>
        <_>
          3 7 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 1 3 -1.</_>
        <_>
          0 14 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 9 6 -1.</_>
        <_>
          3 4 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 9 2 -1.</_>
        <_>
          8 7 9 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 3 6 -1.</_>
        <_>
          0 16 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 6 4 -1.</_>
        <_>
          3 11 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 9 3 -1.</_>
        <_>
          9 9 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 9 6 -1.</_>
        <_>
          6 2 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 6 6 -1.</_>
        <_>
          8 7 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 2 1 -1.</_>
        <_>
          2 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 6 2 -1.</_>
        <_>
          12 10 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 6 6 -1.</_>
        <_>
          15 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 16 6 4 -1.</_>
        <_>
          8 16 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 9 9 -1.</_>
        <_>
          8 3 9 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 10 3 3 -1.</_>
        <_>
          8 10 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 14 2 2 -1.</_>
        <_>
          9 14 1 1 2.</_>
        <_>
          10 15 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 14 2 2 -1.</_>
        <_>
          9 14 1 1 2.</_>
        <_>
          10 15 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 19 12 -1.</_>
        <_>
          0 14 19 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 9 14 -1.</_>
        <_>
          10 6 3 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 3 4 -1.</_>
        <_>
          14 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 1 3 -1.</_>
        <_>
          4 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 6 3 -1.</_>
        <_>
          6 9 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 5 2 -1.</_>
        <_>
          2 19 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 1 2.</_>
        <_>
          8 9 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 1 2.</_>
        <_>
          8 9 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 13 2 -1.</_>
        <_>
          5 11 13 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 1 9 -1.</_>
        <_>
          10 11 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 2 12 -1.</_>
        <_>
          15 8 1 6 2.</_>
        <_>
          16 14 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 3 5 -1.</_>
        <_>
          5 0 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 3 7 -1.</_>
        <_>
          13 6 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 16 6 4 -1.</_>
        <_>
          9 16 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 16 2 1 -1.</_>
        <_>
          10 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 9 2 -1.</_>
        <_>
          9 10 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 15 14 -1.</_>
        <_>
          0 13 15 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 5 6 -1.</_>
        <_>
          9 3 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 3 4 -1.</_>
        <_>
          4 9 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 3 6 -1.</_>
        <_>
          6 7 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 1 2 -1.</_>
        <_>
          17 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 6 12 -1.</_>
        <_>
          11 8 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 6 1 -1.</_>
        <_>
          8 10 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 9 3 -1.</_>
        <_>
          10 17 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 18 6 2 -1.</_>
        <_>
          14 19 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 5 3 14 -1.</_>
        <_>
          10 5 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 16 9 4 -1.</_>
        <_>
          11 16 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 4 14 -1.</_>
        <_>
          0 7 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 6 3 -1.</_>
        <_>
          10 1 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 3 4 -1.</_>
        <_>
          7 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 3 4 -1.</_>
        <_>
          5 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 6 5 -1.</_>
        <_>
          7 1 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 1 2 -1.</_>
        <_>
          1 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 6 -1.</_>
        <_>
          7 2 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 4 2 -1.</_>
        <_>
          0 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 8 12 -1.</_>
        <_>
          12 7 8 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 3 4 -1.</_>
        <_>
          13 9 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 3 5 -1.</_>
        <_>
          13 8 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 2 1 -1.</_>
        <_>
          17 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 1 3 -1.</_>
        <_>
          5 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 3 6 -1.</_>
        <_>
          10 4 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 2 3 -1.</_>
        <_>
          4 18 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 1 9 -1.</_>
        <_>
          12 10 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 3 9 -1.</_>
        <_>
          8 6 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 13 3 6 -1.</_>
        <_>
          17 15 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 3 8 -1.</_>
        <_>
          8 7 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 3 5 -1.</_>
        <_>
          6 0 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 9 8 -1.</_>
        <_>
          7 6 3 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 3 3 -1.</_>
        <_>
          3 9 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 4 2 -1.</_>
        <_>
          16 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 10 3 10 -1.</_>
        <_>
          17 15 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 6 4 -1.</_>
        <_>
          10 9 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 10 12 -1.</_>
        <_>
          5 6 10 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 6 3 -1.</_>
        <_>
          8 9 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 3 7 -1.</_>
        <_>
          12 7 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 6 4 -1.</_>
        <_>
          14 8 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 8 6 5 -1.</_>
        <_>
          16 8 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 2 4 -1.</_>
        <_>
          12 14 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 1 2 -1.</_>
        <_>
          3 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 3 4 -1.</_>
        <_>
          13 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 6 6 -1.</_>
        <_>
          12 0 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 3 8 -1.</_>
        <_>
          11 6 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 17 1 2 -1.</_>
        <_>
          16 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 1 3 -1.</_>
        <_>
          16 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 11 1 2 -1.</_>
        <_>
          11 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 6 9 -1.</_>
        <_>
          5 7 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 9 1 -1.</_>
        <_>
          7 18 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 4 9 -1.</_>
        <_>
          0 14 4 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 6 3 -1.</_>
        <_>
          11 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 6 12 -1.</_>
        <_>
          9 8 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 3 4 -1.</_>
        <_>
          7 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 1 3 -1.</_>
        <_>
          3 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 9 6 4 -1.</_>
        <_>
          13 9 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 3 2 -1.</_>
        <_>
          7 1 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 2 1 -1.</_>
        <_>
          2 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 2 14 -1.</_>
        <_>
          1 0 1 7 2.</_>
        <_>
          2 7 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 11 8 -1.</_>
        <_>
          5 9 11 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 5 6 -1.</_>
        <_>
          9 5 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 5 10 -1.</_>
        <_>
          7 14 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 2 2 -1.</_>
        <_>
          16 10 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 8 2 -1.</_>
        <_>
          0 19 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 1 3 -1.</_>
        <_>
          7 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 11 6 -1.</_>
        <_>
          7 4 11 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 9 3 -1.</_>
        <_>
          8 4 9 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 2 2 -1.</_>
        <_>
          0 10 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 3 6 -1.</_>
        <_>
          0 7 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 2 2 -1.</_>
        <_>
          6 7 1 1 2.</_>
        <_>
          7 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 3 6 -1.</_>
        <_>
          8 6 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 1 6 4 -1.</_>
        <_>
          14 1 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 6 8 -1.</_>
        <_>
          11 11 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 15 3 3 -1.</_>
        <_>
          17 16 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 3 9 -1.</_>
        <_>
          6 9 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 8 6 -1.</_>
        <_>
          0 5 4 3 2.</_>
        <_>
          4 8 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 1 3 -1.</_>
        <_>
          0 7 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 2 6 -1.</_>
        <_>
          18 0 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 6 3 -1.</_>
        <_>
          12 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 15 2 2 -1.</_>
        <_>
          13 15 1 1 2.</_>
        <_>
          14 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 12 3 -1.</_>
        <_>
          4 1 12 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 3 10 9 -1.</_>
        <_>
          5 6 10 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 9 7 -1.</_>
        <_>
          10 7 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 9 6 -1.</_>
        <_>
          8 8 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 6 2 -1.</_>
        <_>
          0 17 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 7 14 -1.</_>
        <_>
          12 13 7 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 7 6 8 -1.</_>
        <_>
          15 7 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 6 3 -1.</_>
        <_>
          4 10 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 6 2 -1.</_>
        <_>
          7 2 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 4 -1.</_>
        <_>
          6 2 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 6 2 -1.</_>
        <_>
          10 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 5 2 -1.</_>
        <_>
          7 7 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 3 6 -1.</_>
        <_>
          7 7 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 18 2 2 -1.</_>
        <_>
          18 18 1 1 2.</_>
        <_>
          19 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 8 3 7 -1.</_>
        <_>
          17 8 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 2 3 -1.</_>
        <_>
          0 17 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 19 6 1 -1.</_>
        <_>
          7 19 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 5 6 6 -1.</_>
        <_>
          9 7 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 10 2 4 -1.</_>
        <_>
          0 12 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 4 3 -1.</_>
        <_>
          2 9 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 10 6 9 -1.</_>
        <_>
          3 10 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 6 2 -1.</_>
        <_>
          11 0 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 2 1 -1.</_>
        <_>
          15 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 1 4 -1.</_>
        <_>
          0 10 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 2 2 -1.</_>
        <_>
          15 6 1 1 2.</_>
        <_>
          16 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 3 6 -1.</_>
        <_>
          8 5 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 17 1 3 -1.</_>
        <_>
          19 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 10 3 1 -1.</_>
        <_>
          8 10 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 1 6 6 -1.</_>
        <_>
          14 1 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 5 2 1 -1.</_>
        <_>
          16 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 7 4 -1.</_>
        <_>
          8 4 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 14 15 -1.</_>
        <_>
          4 5 14 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 6 6 -1.</_>
        <_>
          9 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 17 1 3 -1.</_>
        <_>
          11 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 2 4 -1.</_>
        <_>
          12 16 1 2 2.</_>
        <_>
          13 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 13 2 1 -1.</_>
        <_>
          11 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 3 3 -1.</_>
        <_>
          12 8 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 6 8 -1.</_>
        <_>
          4 0 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 6 6 -1.</_>
        <_>
          3 5 3 3 2.</_>
        <_>
          6 8 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 3 3 -1.</_>
        <_>
          11 8 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 4 2 -1.</_>
        <_>
          5 18 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 16 5 2 -1.</_>
        <_>
          8 17 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 3 3 -1.</_>
        <_>
          0 5 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 3 6 2 -1.</_>
        <_>
          8 3 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 4 9 3 -1.</_>
        <_>
          7 4 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 1 4 -1.</_>
        <_>
          0 15 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 8 3 -1.</_>
        <_>
          0 18 8 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 11 6 -1.</_>
        <_>
          6 3 11 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 6 2 -1.</_>
        <_>
          6 10 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 1 12 -1.</_>
        <_>
          10 14 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 3 4 -1.</_>
        <_>
          6 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 1 3 -1.</_>
        <_>
          0 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 1 3 -1.</_>
        <_>
          0 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 3 4 -1.</_>
        <_>
          14 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 5 4 -1.</_>
        <_>
          1 7 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 14 1 2 -1.</_>
        <_>
          18 15 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 2 4 -1.</_>
        <_>
          14 8 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 6 8 -1.</_>
        <_>
          12 6 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 6 10 -1.</_>
        <_>
          10 6 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 1 3 -1.</_>
        <_>
          17 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 2 10 -1.</_>
        <_>
          2 7 1 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 6 3 -1.</_>
        <_>
          7 9 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 5 12 -1.</_>
        <_>
          0 14 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 1 3 -1.</_>
        <_>
          0 12 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 16 6 4 -1.</_>
        <_>
          8 16 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 2 6 -1.</_>
        <_>
          0 8 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 2 1 -1.</_>
        <_>
          12 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 9 2 -1.</_>
        <_>
          5 2 9 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 2 -1.</_>
        <_>
          0 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 9 3 3 -1.</_>
        <_>
          16 9 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 1 3 -1.</_>
        <_>
          18 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 10 6 1 -1.</_>
        <_>
          13 10 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 4 4 -1.</_>
        <_>
          3 3 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 2 1 18 -1.</_>
        <_>
          11 8 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 5 12 -1.</_>
        <_>
          9 5 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 8 1 -1.</_>
        <_>
          16 0 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 3 10 -1.</_>
        <_>
          9 6 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 6 -1.</_>
        <_>
          19 4 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 6 2 2 -1.</_>
        <_>
          18 7 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 3 4 -1.</_>
        <_>
          8 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 6 5 -1.</_>
        <_>
          7 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 7 3 -1.</_>
        <_>
          0 4 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 2 1 -1.</_>
        <_>
          2 6 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 2 10 -1.</_>
        <_>
          4 8 1 5 2.</_>
        <_>
          5 13 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 18 2 -1.</_>
        <_>
          2 18 9 1 2.</_>
        <_>
          11 19 9 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 7 4 4 -1.</_>
        <_>
          2 7 2 2 2.</_>
        <_>
          4 9 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 3 3 4 -1.</_>
        <_>
          18 3 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 9 2 8 -1.</_>
        <_>
          16 9 1 4 2.</_>
        <_>
          17 13 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 7 1 6 -1.</_>
        <_>
          15 9 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 2 2 -1.</_>
        <_>
          14 3 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 2 3 -1.</_>
        <_>
          17 1 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 2 2 -1.</_>
        <_>
          16 18 1 1 2.</_>
        <_>
          17 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 4 3 -1.</_>
        <_>
          10 5 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 8 6 -1.</_>
        <_>
          4 2 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 14 6 6 -1.</_>
        <_>
          7 16 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 15 2 2 -1.</_>
        <_>
          11 16 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 9 4 -1.</_>
        <_>
          10 1 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 7 -1.</_>
        <_>
          10 7 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 2 2 -1.</_>
        <_>
          6 17 1 1 2.</_>
        <_>
          7 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 3 9 -1.</_>
        <_>
          5 6 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 10 19 10 -1.</_>
        <_>
          0 15 19 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 6 1 -1.</_>
        <_>
          7 17 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 3 -1.</_>
        <_>
          3 12 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 5 18 5 -1.</_>
        <_>
          8 5 6 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 6 4 -1.</_>
        <_>
          1 17 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 6 6 -1.</_>
        <_>
          16 10 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 4 3 -1.</_>
        <_>
          0 15 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 6 11 -1.</_>
        <_>
          3 7 2 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 17 7 2 -1.</_>
        <_>
          13 18 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 2 3 -1.</_>
        <_>
          0 15 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 2 -1.</_>
        <_>
          3 0 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 6 3 -1.</_>
        <_>
          3 1 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 2 6 -1.</_>
        <_>
          0 10 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 6 14 -1.</_>
        <_>
          1 2 3 7 2.</_>
        <_>
          4 9 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 5 2 2 -1.</_>
        <_>
          17 5 1 1 2.</_>
        <_>
          18 6 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 10 9 4 -1.</_>
        <_>
          14 10 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 12 4 -1.</_>
        <_>
          6 9 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 10 12 2 -1.</_>
        <_>
          11 10 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 13 1 2 -1.</_>
        <_>
          2 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 4 3 -1.</_>
        <_>
          16 8 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 16 1 3 -1.</_>
        <_>
          19 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 11 1 2 -1.</_>
        <_>
          18 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 8 2 -1.</_>
        <_>
          12 7 4 1 2.</_>
        <_>
          16 8 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 9 2 4 -1.</_>
        <_>
          15 9 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 6 4 -1.</_>
        <_>
          14 2 3 2 2.</_>
        <_>
          17 4 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 1 -1.</_>
        <_>
          17 0 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 2 1 -1.</_>
        <_>
          4 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 3 1 -1.</_>
        <_>
          18 2 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 18 2 -1.</_>
        <_>
          7 16 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 19 8 1 -1.</_>
        <_>
          6 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 4 3 -1.</_>
        <_>
          1 18 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 13 1 2 -1.</_>
        <_>
          19 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 16 10 4 -1.</_>
        <_>
          9 16 5 2 2.</_>
        <_>
          14 18 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 2 4 -1.</_>
        <_>
          12 9 1 2 2.</_>
        <_>
          13 11 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 11 1 9 -1.</_>
        <_>
          19 14 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 14 14 -1.</_>
        <_>
          6 13 14 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 4 2 -1.</_>
        <_>
          2 18 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 1 3 -1.</_>
        <_>
          0 3 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 1 3 -1.</_>
        <_>
          0 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 15 4 4 -1.</_>
        <_>
          15 17 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 5 18 7 -1.</_>
        <_>
          8 5 6 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 5 3 -1.</_>
        <_>
          1 17 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 2 3 -1.</_>
        <_>
          0 5 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 2 6 -1.</_>
        <_>
          1 6 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 14 4 3 -1.</_>
        <_>
          16 15 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 10 6 -1.</_>
        <_>
          0 0 5 3 2.</_>
        <_>
          5 3 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 3 6 -1.</_>
        <_>
          3 2 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 3 10 -1.</_>
        <_>
          3 0 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 2 2 -1.</_>
        <_>
          5 6 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 4 4 -1.</_>
        <_>
          12 8 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 5 7 3 -1.</_>
        <_>
          13 6 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 13 1 2 -1.</_>
        <_>
          10 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 4 2 -1.</_>
        <_>
          18 16 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 12 4 7 -1.</_>
        <_>
          18 12 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 17 1 3 -1.</_>
        <_>
          16 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 9 1 3 -1.</_>
        <_>
          19 10 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 7 2 6 -1.</_>
        <_>
          19 7 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 3 4 -1.</_>
        <_>
          9 1 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 9 -1.</_>
        <_>
          16 0 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 2 10 2 -1.</_>
        <_>
          9 2 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 8 4 -1.</_>
        <_>
          2 12 4 2 2.</_>
        <_>
          6 14 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 7 3 -1.</_>
        <_>
          0 5 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 14 3 3 -1.</_>
        <_>
          15 14 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 4 3 -1.</_>
        <_>
          2 3 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 2 7 -1.</_>
        <_>
          2 0 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 16 4 4 -1.</_>
        <_>
          15 18 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 4 -1.</_>
        <_>
          5 10 12 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 1 2 -1.</_>
        <_>
          3 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 3 4 -1.</_>
        <_>
          7 1 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 3 4 -1.</_>
        <_>
          7 2 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 9 12 -1.</_>
        <_>
          9 8 3 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 8 6 -1.</_>
        <_>
          8 3 8 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 6 3 -1.</_>
        <_>
          17 2 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 1 3 -1.</_>
        <_>
          0 7 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 10 2 -1.</_>
        <_>
          15 0 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 3 2 -1.</_>
        <_>
          12 0 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 19 10 1 -1.</_>
        <_>
          8 19 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 7 16 -1.</_>
        <_>
          0 12 7 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 1 3 -1.</_>
        <_>
          2 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 12 6 -1.</_>
        <_>
          11 8 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 9 6 7 -1.</_>
        <_>
          16 9 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 17 6 1 -1.</_>
        <_>
          14 17 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 1 3 1 -1.</_>
        <_>
          17 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 8 2 -1.</_>
        <_>
          0 17 4 1 2.</_>
        <_>
          4 18 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 2 1 -1.</_>
        <_>
          18 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 15 6 5 -1.</_>
        <_>
          6 15 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 8 2 -1.</_>
        <_>
          7 3 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 8 4 -1.</_>
        <_>
          4 3 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 19 2 1 -1.</_>
        <_>
          6 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 19 2 1 -1.</_>
        <_>
          6 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 17 1 3 -1.</_>
        <_>
          16 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 2 3 -1.</_>
        <_>
          1 11 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 4 1 -1.</_>
        <_>
          2 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 4 2 -1.</_>
        <_>
          2 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 1 3 -1.</_>
        <_>
          2 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 11 2 -1.</_>
        <_>
          5 8 11 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 2 4 10 -1.</_>
        <_>
          9 7 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 4 3 -1.</_>
        <_>
          0 3 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 19 10 1 -1.</_>
        <_>
          15 19 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 17 8 3 -1.</_>
        <_>
          15 17 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 19 3 1 -1.</_>
        <_>
          9 19 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 3 4 -1.</_>
        <_>
          15 0 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 4 3 -1.</_>
        <_>
          10 7 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 3 2 -1.</_>
        <_>
          0 9 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 3 6 -1.</_>
        <_>
          7 14 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 1 2 -1.</_>
        <_>
          1 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 4 -1.</_>
        <_>
          2 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 6 7 -1.</_>
        <_>
          3 8 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 4 5 -1.</_>
        <_>
          2 8 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 16 1 3 -1.</_>
        <_>
          19 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 18 6 -1.</_>
        <_>
          7 5 6 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 4 2 -1.</_>
        <_>
          2 16 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 6 2 11 -1.</_>
        <_>
          19 6 1 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 2 6 -1.</_>
        <_>
          0 14 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 5 3 2 -1.</_>
        <_>
          12 6 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 2 3 -1.</_>
        <_>
          1 4 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 14 4 4 -1.</_>
        <_>
          16 16 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 12 5 -1.</_>
        <_>
          10 8 4 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 7 2 7 -1.</_>
        <_>
          14 7 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 2 6 -1.</_>
        <_>
          2 8 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 3 7 -1.</_>
        <_>
          16 0 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 2 6 2 -1.</_>
        <_>
          6 2 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 20 9 -1.</_>
        <_>
          0 12 20 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 14 2 2 -1.</_>
        <_>
          10 15 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 10 4 -1.</_>
        <_>
          6 7 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 5 9 -1.</_>
        <_>
          6 4 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 2 2 -1.</_>
        <_>
          16 18 1 1 2.</_>
        <_>
          17 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 2 4 -1.</_>
        <_>
          0 16 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 2 5 -1.</_>
        <_>
          11 8 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 12 7 -1.</_>
        <_>
          7 7 4 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 6 -1.</_>
        <_>
          3 0 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 4 4 -1.</_>
        <_>
          3 0 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 8 -1.</_>
        <_>
          2 0 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 1 -1.</_>
        <_>
          1 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 3 3 -1.</_>
        <_>
          0 1 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 2 4 -1.</_>
        <_>
          5 6 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 9 1 -1.</_>
        <_>
          5 10 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 1 3 -1.</_>
        <_>
          1 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 2 3 -1.</_>
        <_>
          0 18 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 16 3 -1.</_>
        <_>
          8 15 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 4 1 -1.</_>
        <_>
          2 5 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 20 -1.</_>
        <_>
          3 0 2 20 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 5 4 6 -1.</_>
        <_>
          2 5 2 3 2.</_>
        <_>
          4 8 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 16 6 3 -1.</_>
        <_>
          11 16 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 17 6 1 -1.</_>
        <_>
          14 17 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 15 2 -1.</_>
        <_>
          8 17 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 3 -1.</_>
        <_>
          18 1 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 1 7 4 -1.</_>
        <_>
          13 3 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 6 4 4 -1.</_>
        <_>
          13 6 2 2 2.</_>
        <_>
          15 8 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 6 3 4 -1.</_>
        <_>
          17 8 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 9 2 2 -1.</_>
        <_>
          15 9 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 17 1 3 -1.</_>
        <_>
          17 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 19 8 1 -1.</_>
        <_>
          7 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 3 6 -1.</_>
        <_>
          0 12 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 15 5 -1.</_>
        <_>
          9 7 5 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 9 5 -1.</_>
        <_>
          9 9 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 6 2 -1.</_>
        <_>
          10 1 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 12 2 -1.</_>
        <_>
          10 0 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 10 3 -1.</_>
        <_>
          12 0 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 6 -1.</_>
        <_>
          5 2 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 6 4 -1.</_>
        <_>
          8 5 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 4 2 3 -1.</_>
        <_>
          17 5 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 4 3 -1.</_>
        <_>
          5 3 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 2 6 -1.</_>
        <_>
          6 9 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 2 6 -1.</_>
        <_>
          15 10 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 3 3 -1.</_>
        <_>
          7 5 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 4 8 2 -1.</_>
        <_>
          12 4 4 1 2.</_>
        <_>
          16 5 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 1 6 -1.</_>
        <_>
          15 10 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 11 3 -1.</_>
        <_>
          4 18 11 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 16 20 -1.</_>
        <_>
          3 10 16 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 4 4 6 -1.</_>
        <_>
          12 6 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 6 6 -1.</_>
        <_>
          13 0 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 1 6 4 -1.</_>
        <_>
          13 1 3 2 2.</_>
        <_>
          16 3 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 6 4 -1.</_>
        <_>
          13 0 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 6 9 -1.</_>
        <_>
          10 6 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 3 4 -1.</_>
        <_>
          8 0 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 14 2 -1.</_>
        <_>
          0 17 7 1 2.</_>
        <_>
          7 18 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 2 2 -1.</_>
        <_>
          6 18 1 1 2.</_>
        <_>
          7 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 18 2 2 -1.</_>
        <_>
          17 18 1 1 2.</_>
        <_>
          18 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 1 9 -1.</_>
        <_>
          5 10 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 3 6 4 -1.</_>
        <_>
          7 3 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 6 2 -1.</_>
        <_>
          1 9 3 1 2.</_>
        <_>
          4 10 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 2 3 -1.</_>
        <_>
          7 9 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 6 12 -1.</_>
        <_>
          8 8 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 2 2 -1.</_>
        <_>
          4 18 1 1 2.</_>
        <_>
          5 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 6 6 -1.</_>
        <_>
          9 3 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 6 2 -1.</_>
        <_>
          6 18 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 18 16 2 -1.</_>
        <_>
          3 19 16 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 3 11 -1.</_>
        <_>
          4 0 1 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 18 3 1 -1.</_>
        <_>
          14 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 9 6 -1.</_>
        <_>
          6 2 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 12 4 -1.</_>
        <_>
          1 2 6 2 2.</_>
        <_>
          7 4 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 6 4 -1.</_>
        <_>
          5 3 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 8 1 -1.</_>
        <_>
          16 0 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 6 2 -1.</_>
        <_>
          11 0 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 12 1 -1.</_>
        <_>
          9 3 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 7 6 2 -1.</_>
        <_>
          2 7 3 1 2.</_>
        <_>
          5 8 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 4 6 -1.</_>
        <_>
          0 10 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 6 3 7 -1.</_>
        <_>
          10 6 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 6 6 13 -1.</_>
        <_>
          11 6 2 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 6 1 -1.</_>
        <_>
          13 12 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 9 2 6 -1.</_>
        <_>
          18 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 3 9 -1.</_>
        <_>
          18 2 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 4 6 -1.</_>
        <_>
          13 8 2 3 2.</_>
        <_>
          15 11 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 2 12 6 -1.</_>
        <_>
          10 2 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 14 16 6 -1.</_>
        <_>
          12 14 8 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 19 10 1 -1.</_>
        <_>
          11 19 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 1 3 -1.</_>
        <_>
          6 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 14 10 3 -1.</_>
        <_>
          4 15 10 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 12 12 -1.</_>
        <_>
          6 4 12 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 4 2 -1.</_>
        <_>
          5 7 2 1 2.</_>
        <_>
          7 8 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 5 3 2 -1.</_>
        <_>
          18 5 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 13 6 3 -1.</_>
        <_>
          8 14 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 13 5 3 -1.</_>
        <_>
          8 14 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 1 18 -1.</_>
        <_>
          13 11 1 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 9 2 -1.</_>
        <_>
          9 10 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 7 4 -1.</_>
        <_>
          11 2 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 8 -1.</_>
        <_>
          3 0 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 15 3 3 -1.</_>
        <_>
          9 16 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 3 -1.</_>
        <_>
          9 18 9 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 3 3 -1.</_>
        <_>
          12 13 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 3 5 -1.</_>
        <_>
          5 1 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 14 2 3 -1.</_>
        <_>
          10 15 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 2 2 -1.</_>
        <_>
          18 17 1 1 2.</_>
        <_>
          19 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 18 2 2 -1.</_>
        <_>
          18 18 1 1 2.</_>
        <_>
          19 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 18 2 2 -1.</_>
        <_>
          18 18 1 1 2.</_>
        <_>
          19 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 9 1 -1.</_>
        <_>
          7 10 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 6 5 -1.</_>
        <_>
          5 9 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 8 1 12 -1.</_>
        <_>
          18 14 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 8 6 -1.</_>
        <_>
          0 2 4 3 2.</_>
        <_>
          4 5 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 4 3 3 -1.</_>
        <_>
          9 5 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 18 2 2 -1.</_>
        <_>
          3 18 1 1 2.</_>
        <_>
          4 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 4 4 3 -1.</_>
        <_>
          6 5 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 4 2 -1.</_>
        <_>
          16 7 2 1 2.</_>
        <_>
          18 8 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 1 3 -1.</_>
        <_>
          5 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 15 20 -1.</_>
        <_>
          2 10 15 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 6 4 -1.</_>
        <_>
          8 11 3 2 2.</_>
        <_>
          11 13 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 16 4 3 -1.</_>
        <_>
          8 17 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 2 2 -1.</_>
        <_>
          8 18 1 1 2.</_>
        <_>
          9 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 13 3 -1.</_>
        <_>
          2 17 13 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 2 2 -1.</_>
        <_>
          16 16 1 1 2.</_>
        <_>
          17 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 6 3 -1.</_>
        <_>
          10 1 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 2 2 -1.</_>
        <_>
          16 7 1 1 2.</_>
        <_>
          17 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 7 4 2 -1.</_>
        <_>
          14 7 2 1 2.</_>
        <_>
          16 8 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 14 1 -1.</_>
        <_>
          11 0 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 8 2 -1.</_>
        <_>
          10 4 4 1 2.</_>
        <_>
          14 5 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 3 2 -1.</_>
        <_>
          9 2 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 6 3 -1.</_>
        <_>
          12 12 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 1 4 -1.</_>
        <_>
          1 7 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 1 18 -1.</_>
        <_>
          1 7 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 3 2 -1.</_>
        <_>
          11 14 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 12 2 -1.</_>
        <_>
          0 1 6 1 2.</_>
        <_>
          6 2 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 18 2 2 -1.</_>
        <_>
          10 18 1 1 2.</_>
        <_>
          11 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 4 4 -1.</_>
        <_>
          4 5 2 2 2.</_>
        <_>
          6 7 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 1 3 -1.</_>
        <_>
          6 8 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 6 2 -1.</_>
        <_>
          16 10 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 8 3 6 -1.</_>
        <_>
          17 8 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 6 2 -1.</_>
        <_>
          6 10 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 3 7 -1.</_>
        <_>
          7 5 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 6 6 -1.</_>
        <_>
          0 16 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 5 1 9 -1.</_>
        <_>
          12 8 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 3 3 -1.</_>
        <_>
          6 9 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 6 13 -1.</_>
        <_>
          9 5 2 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 8 1 10 -1.</_>
        <_>
          19 13 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 6 1 -1.</_>
        <_>
          13 18 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 6 12 -1.</_>
        <_>
          11 7 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 6 6 -1.</_>
        <_>
          14 7 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 3 4 -1.</_>
        <_>
          16 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 4 2 -1.</_>
        <_>
          6 12 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 6 8 -1.</_>
        <_>
          3 6 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 15 6 5 -1.</_>
        <_>
          13 15 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 17 4 2 -1.</_>
        <_>
          15 18 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 6 1 -1.</_>
        <_>
          15 11 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 18 2 2 -1.</_>
        <_>
          5 18 1 1 2.</_>
        <_>
          6 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 4 4 -1.</_>
        <_>
          4 8 2 2 2.</_>
        <_>
          6 10 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 9 3 -1.</_>
        <_>
          11 8 9 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 10 4 -1.</_>
        <_>
          0 3 5 2 2.</_>
        <_>
          5 5 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 6 1 -1.</_>
        <_>
          9 18 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 3 3 -1.</_>
        <_>
          0 9 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 8 -1.</_>
        <_>
          0 0 3 4 2.</_>
        <_>
          3 4 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 3 8 -1.</_>
        <_>
          8 6 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 7 7 3 -1.</_>
        <_>
          13 8 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 2 2 -1.</_>
        <_>
          3 4 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 3 3 -1.</_>
        <_>
          0 4 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 5 2 -1.</_>
        <_>
          9 4 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 9 4 -1.</_>
        <_>
          9 5 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 12 3 -1.</_>
        <_>
          7 10 4 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 3 6 -1.</_>
        <_>
          9 7 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 6 5 -1.</_>
        <_>
          8 5 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 2 3 -1.</_>
        <_>
          0 6 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 4 -1.</_>
        <_>
          10 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 15 -1.</_>
        <_>
          3 0 2 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 1 3 5 -1.</_>
        <_>
          16 1 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 2 3 10 -1.</_>
        <_>
          10 2 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 6 12 -1.</_>
        <_>
          10 8 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 4 3 4 -1.</_>
        <_>
          16 6 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 2 2 -1.</_>
        <_>
          16 7 1 1 2.</_>
        <_>
          17 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 9 -1.</_>
        <_>
          13 3 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 1 3 -1.</_>
        <_>
          7 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 1 4 2 -1.</_>
        <_>
          12 2 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 3 1 3 -1.</_>
        <_>
          17 4 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 9 3 -1.</_>
        <_>
          0 17 9 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 2 4 -1.</_>
        <_>
          3 6 1 2 2.</_>
        <_>
          4 8 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 18 3 1 -1.</_>
        <_>
          14 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 4 2 -1.</_>
        <_>
          2 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 2 1 -1.</_>
        <_>
          2 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 4 2 -1.</_>
        <_>
          0 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 1 3 -1.</_>
        <_>
          2 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 3 5 -1.</_>
        <_>
          5 8 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 6 7 -1.</_>
        <_>
          4 1 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 2 8 -1.</_>
        <_>
          3 6 1 4 2.</_>
        <_>
          4 10 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 11 10 -1.</_>
        <_>
          4 10 11 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 20 2 -1.</_>
        <_>
          10 13 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 13 16 3 -1.</_>
        <_>
          9 13 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 4 4 4 -1.</_>
        <_>
          16 4 2 2 2.</_>
        <_>
          18 6 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 4 12 -1.</_>
        <_>
          16 0 2 6 2.</_>
        <_>
          18 6 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 15 3 1 -1.</_>
        <_>
          15 15 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 12 10 -1.</_>
        <_>
          3 9 12 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 2 2 -1.</_>
        <_>
          9 18 1 1 2.</_>
        <_>
          10 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 2 2 -1.</_>
        <_>
          9 18 1 1 2.</_>
        <_>
          10 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 4 2 14 -1.</_>
        <_>
          13 4 1 7 2.</_>
        <_>
          14 11 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 2 6 4 -1.</_>
        <_>
          7 2 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 18 20 -1.</_>
        <_>
          0 0 9 10 2.</_>
        <_>
          9 10 9 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 1 2 -1.</_>
        <_>
          15 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 10 2 4 -1.</_>
        <_>
          16 10 1 2 2.</_>
        <_>
          17 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 2 2 -1.</_>
        <_>
          18 17 1 1 2.</_>
        <_>
          19 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 1 2 -1.</_>
        <_>
          9 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 9 6 -1.</_>
        <_>
          11 4 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 9 10 -1.</_>
        <_>
          9 9 3 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 5 4 -1.</_>
        <_>
          5 2 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 11 4 -1.</_>
        <_>
          5 9 11 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 2 14 -1.</_>
        <_>
          3 4 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 3 5 -1.</_>
        <_>
          9 6 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 3 9 -1.</_>
        <_>
          9 4 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 20 6 -1.</_>
        <_>
          0 10 20 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 16 6 1 -1.</_>
        <_>
          17 16 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 18 2 2 -1.</_>
        <_>
          17 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 6 3 -1.</_>
        <_>
          10 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 9 15 -1.</_>
        <_>
          7 1 3 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 3 12 -1.</_>
        <_>
          12 5 1 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 4 3 -1.</_>
        <_>
          0 16 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 15 1 -1.</_>
        <_>
          5 0 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 4 -1.</_>
        <_>
          8 0 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 9 3 -1.</_>
        <_>
          5 0 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 6 3 7 -1.</_>
        <_>
          14 6 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 4 2 -1.</_>
        <_>
          7 7 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 6 1 -1.</_>
        <_>
          8 18 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 6 2 2 -1.</_>
        <_>
          18 7 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 4 7 3 -1.</_>
        <_>
          6 5 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 3 1 -1.</_>
        <_>
          13 7 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 1 2 10 -1.</_>
        <_>
          15 1 1 5 2.</_>
        <_>
          16 6 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 2 2 -1.</_>
        <_>
          0 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 4 1 8 -1.</_>
        <_>
          19 8 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 1 3 -1.</_>
        <_>
          1 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 6 4 -1.</_>
        <_>
          0 15 3 2 2.</_>
        <_>
          3 17 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 18 -1.</_>
        <_>
          19 6 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 6 2 -1.</_>
        <_>
          12 2 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 12 2 -1.</_>
        <_>
          6 8 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 4 1 -1.</_>
        <_>
          18 0 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 2 6 -1.</_>
        <_>
          8 7 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 5 2 10 -1.</_>
        <_>
          15 5 1 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 4 2 2 -1.</_>
        <_>
          13 5 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 3 6 -1.</_>
        <_>
          11 3 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          10 9 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 16 4 2 -1.</_>
        <_>
          9 17 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 14 15 4 -1.</_>
        <_>
          5 16 15 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 2 -1.</_>
        <_>
          18 17 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 2 2 -1.</_>
        <_>
          16 18 1 1 2.</_>
        <_>
          17 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 4 3 8 -1.</_>
        <_>
          7 4 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 3 1 -1.</_>
        <_>
          6 9 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 1 6 -1.</_>
        <_>
          0 10 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 2 9 6 -1.</_>
        <_>
          14 2 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 2 6 4 -1.</_>
        <_>
          14 2 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 2 4 -1.</_>
        <_>
          1 9 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 1 6 4 -1.</_>
        <_>
          13 3 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 2 10 -1.</_>
        <_>
          4 10 1 5 2.</_>
        <_>
          5 15 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 9 3 -1.</_>
        <_>
          5 16 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 3 9 -1.</_>
        <_>
          2 2 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 7 1 4 -1.</_>
        <_>
          19 9 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 11 6 8 -1.</_>
        <_>
          14 11 3 4 2.</_>
        <_>
          17 15 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 12 4 6 -1.</_>
        <_>
          15 12 2 3 2.</_>
        <_>
          17 15 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 15 2 2 -1.</_>
        <_>
          16 15 1 1 2.</_>
        <_>
          17 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 1 1 2.</_>
        <_>
          18 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 1 1 2.</_>
        <_>
          18 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 2 2 -1.</_>
        <_>
          2 3 1 1 2.</_>
        <_>
          3 4 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 3 3 -1.</_>
        <_>
          11 10 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 7 8 -1.</_>
        <_>
          5 13 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 16 2 2 -1.</_>
        <_>
          7 16 1 1 2.</_>
        <_>
          8 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 16 2 2 -1.</_>
        <_>
          7 16 1 1 2.</_>
        <_>
          8 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 10 3 -1.</_>
        <_>
          14 8 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 4 8 -1.</_>
        <_>
          6 7 2 4 2.</_>
        <_>
          8 11 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 4 3 -1.</_>
        <_>
          1 7 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 6 10 -1.</_>
        <_>
          8 10 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 3 6 -1.</_>
        <_>
          5 6 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 4 -1.</_>
        <_>
          3 10 2 2 2.</_>
        <_>
          5 12 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 4 -1.</_>
        <_>
          3 10 2 2 2.</_>
        <_>
          5 12 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 4 -1.</_>
        <_>
          3 10 2 2 2.</_>
        <_>
          5 12 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 8 2 6 -1.</_>
        <_>
          15 8 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 4 -1.</_>
        <_>
          3 10 2 2 2.</_>
        <_>
          5 12 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 4 -1.</_>
        <_>
          3 10 2 2 2.</_>
        <_>
          5 12 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 4 3 9 -1.</_>
        <_>
          13 4 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 1 12 -1.</_>
        <_>
          12 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 18 1 -1.</_>
        <_>
          8 0 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 10 6 -1.</_>
        <_>
          10 0 5 3 2.</_>
        <_>
          15 3 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 2 -1.</_>
        <_>
          18 17 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 4 2 -1.</_>
        <_>
          3 5 2 1 2.</_>
        <_>
          5 6 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 3 3 -1.</_>
        <_>
          12 8 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 3 5 -1.</_>
        <_>
          12 7 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 19 15 1 -1.</_>
        <_>
          8 19 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 13 3 2 -1.</_>
        <_>
          8 14 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 8 4 -1.</_>
        <_>
          2 12 4 2 2.</_>
        <_>
          6 14 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 2 2 -1.</_>
        <_>
          16 16 1 1 2.</_>
        <_>
          17 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 3 2 -1.</_>
        <_>
          8 0 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 2 5 -1.</_>
        <_>
          7 7 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 17 -1.</_>
        <_>
          19 0 1 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 1 3 -1.</_>
        <_>
          16 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 8 3 7 -1.</_>
        <_>
          15 8 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 2 2 -1.</_>
        <_>
          10 17 1 1 2.</_>
        <_>
          11 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 1 3 -1.</_>
        <_>
          4 10 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 10 2 3 -1.</_>
        <_>
          18 11 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 1 3 10 -1.</_>
        <_>
          13 1 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 9 1 -1.</_>
        <_>
          11 12 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 18 2 2 -1.</_>
        <_>
          5 18 1 1 2.</_>
        <_>
          6 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 6 1 9 -1.</_>
        <_>
          19 9 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 2 4 -1.</_>
        <_>
          4 7 1 2 2.</_>
        <_>
          5 9 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 6 14 -1.</_>
        <_>
          3 4 2 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 9 3 -1.</_>
        <_>
          13 5 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 7 2 6 -1.</_>
        <_>
          18 9 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 2 7 -1.</_>
        <_>
          6 6 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 6 8 -1.</_>
        <_>
          13 4 3 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 2 9 -1.</_>
        <_>
          0 11 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 5 3 -1.</_>
        <_>
          0 8 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 7 2 -1.</_>
        <_>
          8 2 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 3 5 -1.</_>
        <_>
          8 5 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 2 -1.</_>
        <_>
          19 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 10 11 -1.</_>
        <_>
          11 7 5 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 6 1 -1.</_>
        <_>
          11 19 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 12 1 -1.</_>
        <_>
          7 0 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 6 5 -1.</_>
        <_>
          6 1 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 12 6 -1.</_>
        <_>
          10 12 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 13 2 3 -1.</_>
        <_>
          16 14 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 14 4 2 -1.</_>
        <_>
          7 15 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 14 2 2 -1.</_>
        <_>
          7 15 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 2 4 -1.</_>
        <_>
          3 10 1 2 2.</_>
        <_>
          4 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 2 6 -1.</_>
        <_>
          0 5 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 10 2 2 -1.</_>
        <_>
          1 10 1 1 2.</_>
        <_>
          2 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 4 4 3 -1.</_>
        <_>
          16 5 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 2 4 -1.</_>
        <_>
          5 10 1 2 2.</_>
        <_>
          6 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 13 2 -1.</_>
        <_>
          5 12 13 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 3 11 -1.</_>
        <_>
          11 2 1 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 4 4 -1.</_>
        <_>
          10 4 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 6 2 -1.</_>
        <_>
          10 8 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 2 3 3 -1.</_>
        <_>
          12 2 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 14 2 -1.</_>
        <_>
          6 18 7 1 2.</_>
        <_>
          13 19 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 7 1 12 -1.</_>
        <_>
          17 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 10 3 -1.</_>
        <_>
          10 6 10 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 3 3 -1.</_>
        <_>
          7 1 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 3 1 -1.</_>
        <_>
          14 8 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 14 2 6 -1.</_>
        <_>
          10 16 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 12 14 -1.</_>
        <_>
          8 1 4 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 6 14 -1.</_>
        <_>
          16 1 2 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 16 2 2 -1.</_>
        <_>
          3 16 1 1 2.</_>
        <_>
          4 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 2 2 -1.</_>
        <_>
          0 17 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 4 6 -1.</_>
        <_>
          15 6 2 3 2.</_>
        <_>
          17 9 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 5 2 2 -1.</_>
        <_>
          12 6 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 6 13 -1.</_>
        <_>
          9 6 2 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 6 5 -1.</_>
        <_>
          3 9 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 3 4 -1.</_>
        <_>
          0 7 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 16 2 -1.</_>
        <_>
          4 1 8 1 2.</_>
        <_>
          12 2 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 4 2 -1.</_>
        <_>
          1 18 2 1 2.</_>
        <_>
          3 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 3 4 -1.</_>
        <_>
          8 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 9 3 -1.</_>
        <_>
          6 4 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 6 10 -1.</_>
        <_>
          6 6 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 8 10 -1.</_>
        <_>
          13 0 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 8 1 -1.</_>
        <_>
          12 0 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 8 16 -1.</_>
        <_>
          6 2 4 8 2.</_>
        <_>
          10 10 4 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 2 10 -1.</_>
        <_>
          14 10 1 5 2.</_>
        <_>
          15 15 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 1 2 -1.</_>
        <_>
          12 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 3 8 -1.</_>
        <_>
          17 0 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 10 -1.</_>
        <_>
          17 0 3 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 3 5 -1.</_>
        <_>
          17 0 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 11 2 -1.</_>
        <_>
          4 6 11 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 2 1 -1.</_>
        <_>
          2 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 3 -1.</_>
        <_>
          0 1 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 6 11 -1.</_>
        <_>
          13 6 2 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 3 1 -1.</_>
        <_>
          15 0 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 7 1 2 -1.</_>
        <_>
          19 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 9 -1.</_>
        <_>
          18 0 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 3 4 -1.</_>
        <_>
          13 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 14 2 -1.</_>
        <_>
          0 1 7 1 2.</_>
        <_>
          7 2 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 3 2 -1.</_>
        <_>
          4 1 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 15 2 -1.</_>
        <_>
          9 0 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 6 1 -1.</_>
        <_>
          12 2 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 4 6 11 -1.</_>
        <_>
          11 4 2 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 2 4 -1.</_>
        <_>
          2 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 6 3 -1.</_>
        <_>
          8 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 6 2 -1.</_>
        <_>
          9 9 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 9 2 -1.</_>
        <_>
          9 8 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 2 10 -1.</_>
        <_>
          6 6 1 5 2.</_>
        <_>
          7 11 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 2 3 -1.</_>
        <_>
          0 12 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 15 4 1 -1.</_>
        <_>
          13 15 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 1 2 -1.</_>
        <_>
          6 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 20 -1.</_>
        <_>
          2 0 2 20 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 2 2 -1.</_>
        <_>
          4 10 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 3 5 -1.</_>
        <_>
          5 7 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 6 2 -1.</_>
        <_>
          5 12 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 15 7 4 -1.</_>
        <_>
          6 17 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 1 1 2.</_>
        <_>
          18 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 1 3 16 -1.</_>
        <_>
          16 1 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 16 6 3 -1.</_>
        <_>
          8 16 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 14 3 2 -1.</_>
        <_>
          15 15 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 1 2 -1.</_>
        <_>
          12 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 4 4 -1.</_>
        <_>
          0 2 2 2 2.</_>
        <_>
          2 4 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 6 4 -1.</_>
        <_>
          1 1 3 2 2.</_>
        <_>
          4 3 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 1 2 -1.</_>
        <_>
          1 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 2 3 -1.</_>
        <_>
          4 8 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 9 14 -1.</_>
        <_>
          1 7 9 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 6 -1.</_>
        <_>
          4 9 1 3 2.</_>
        <_>
          5 12 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 4 3 -1.</_>
        <_>
          5 9 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 2 4 -1.</_>
        <_>
          0 11 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 6 3 10 -1.</_>
        <_>
          17 6 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 11 2 1 -1.</_>
        <_>
          17 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 4 4 -1.</_>
        <_>
          5 9 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 11 9 2 -1.</_>
        <_>
          13 11 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 2 2 -1.</_>
        <_>
          15 10 1 1 2.</_>
        <_>
          16 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 6 14 -1.</_>
        <_>
          10 13 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 7 3 5 -1.</_>
        <_>
          15 7 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 12 3 -1.</_>
        <_>
          10 11 4 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 1 2 -1.</_>
        <_>
          17 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 5 4 -1.</_>
        <_>
          8 7 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 4 2 -1.</_>
        <_>
          11 7 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 8 2 -1.</_>
        <_>
          3 4 4 1 2.</_>
        <_>
          7 5 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 6 6 -1.</_>
        <_>
          2 8 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 6 2 -1.</_>
        <_>
          7 5 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 3 6 3 -1.</_>
        <_>
          9 3 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 3 3 -1.</_>
        <_>
          2 18 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 6 1 -1.</_>
        <_>
          5 10 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 6 2 -1.</_>
        <_>
          9 2 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 9 1 -1.</_>
        <_>
          7 11 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 11 12 -1.</_>
        <_>
          7 13 11 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 3 4 -1.</_>
        <_>
          4 2 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 9 3 -1.</_>
        <_>
          12 7 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 2 6 -1.</_>
        <_>
          15 11 1 3 2.</_>
        <_>
          16 14 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 5 3 -1.</_>
        <_>
          0 6 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 6 12 -1.</_>
        <_>
          10 1 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 15 13 -1.</_>
        <_>
          8 7 5 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 9 9 -1.</_>
        <_>
          0 12 9 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 3 8 -1.</_>
        <_>
          17 0 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 2 4 2 -1.</_>
        <_>
          18 2 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 5 -1.</_>
        <_>
          16 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 1 3 2 -1.</_>
        <_>
          16 1 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 3 2 -1.</_>
        <_>
          12 8 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 2 12 -1.</_>
        <_>
          1 8 1 6 2.</_>
        <_>
          2 14 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 6 12 -1.</_>
        <_>
          2 1 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 17 1 3 -1.</_>
        <_>
          19 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 3 3 10 -1.</_>
        <_>
          12 3 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 9 8 -1.</_>
        <_>
          11 1 3 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 2 -1.</_>
        <_>
          18 16 1 1 2.</_>
        <_>
          19 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 2 -1.</_>
        <_>
          18 16 1 1 2.</_>
        <_>
          19 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 2 6 -1.</_>
        <_>
          6 15 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 14 2 2 -1.</_>
        <_>
          9 15 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 2 4 -1.</_>
        <_>
          14 10 1 2 2.</_>
        <_>
          15 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 2 2 -1.</_>
        <_>
          0 15 1 1 2.</_>
        <_>
          1 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 2 2 -1.</_>
        <_>
          6 7 1 1 2.</_>
        <_>
          7 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 2 2 -1.</_>
        <_>
          11 18 1 1 2.</_>
        <_>
          12 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 4 -1.</_>
        <_>
          0 0 3 2 2.</_>
        <_>
          3 2 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 6 6 -1.</_>
        <_>
          6 1 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 13 5 4 -1.</_>
        <_>
          15 15 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 6 1 -1.</_>
        <_>
          9 17 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 19 4 1 -1.</_>
        <_>
          18 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 4 4 -1.</_>
        <_>
          18 16 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 9 4 -1.</_>
        <_>
          10 8 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 2 2 -1.</_>
        <_>
          16 18 1 1 2.</_>
        <_>
          17 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 2 4 -1.</_>
        <_>
          2 9 1 2 2.</_>
        <_>
          3 11 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 8 4 -1.</_>
        <_>
          0 3 4 2 2.</_>
        <_>
          4 5 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 8 1 -1.</_>
        <_>
          4 1 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 8 9 -1.</_>
        <_>
          4 5 4 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 6 2 -1.</_>
        <_>
          9 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 1 12 -1.</_>
        <_>
          0 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 13 1 6 -1.</_>
        <_>
          19 15 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 6 8 -1.</_>
        <_>
          4 8 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 9 17 -1.</_>
        <_>
          3 0 3 17 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 6 8 -1.</_>
        <_>
          9 9 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 9 4 -1.</_>
        <_>
          8 10 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 8 3 -1.</_>
        <_>
          5 1 8 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 6 4 4 -1.</_>
        <_>
          16 6 2 2 2.</_>
        <_>
          18 8 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 4 2 8 -1.</_>
        <_>
          17 4 1 4 2.</_>
        <_>
          18 8 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 1 3 -1.</_>
        <_>
          2 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 1 3 -1.</_>
        <_>
          2 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 1 3 -1.</_>
        <_>
          11 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 2 9 7 -1.</_>
        <_>
          14 2 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 3 6 -1.</_>
        <_>
          11 2 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 15 2 -1.</_>
        <_>
          5 10 15 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 16 6 2 -1.</_>
        <_>
          8 17 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 16 10 2 -1.</_>
        <_>
          9 16 5 1 2.</_>
        <_>
          14 17 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 2 2 -1.</_>
        <_>
          9 17 1 1 2.</_>
        <_>
          10 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 15 6 4 -1.</_>
        <_>
          10 15 3 2 2.</_>
        <_>
          13 17 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 15 12 -1.</_>
        <_>
          9 5 5 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 2 3 -1.</_>
        <_>
          11 14 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 13 7 3 -1.</_>
        <_>
          8 14 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 1 2 -1.</_>
        <_>
          1 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 2 2 -1.</_>
        <_>
          16 18 1 1 2.</_>
        <_>
          17 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 18 1 -1.</_>
        <_>
          7 19 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 6 1 -1.</_>
        <_>
          4 17 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 1 12 -1.</_>
        <_>
          1 9 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 3 6 -1.</_>
        <_>
          0 11 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 3 10 -1.</_>
        <_>
          6 4 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 2 1 -1.</_>
        <_>
          7 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 12 -1.</_>
        <_>
          3 0 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 9 2 -1.</_>
        <_>
          7 7 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 9 1 -1.</_>
        <_>
          9 11 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 10 2 10 -1.</_>
        <_>
          17 15 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 2 10 -1.</_>
        <_>
          4 10 1 5 2.</_>
        <_>
          5 15 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 3 12 -1.</_>
        <_>
          13 3 1 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 3 4 6 -1.</_>
        <_>
          15 3 2 3 2.</_>
        <_>
          17 6 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 3 3 -1.</_>
        <_>
          13 8 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 14 2 4 -1.</_>
        <_>
          4 16 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 16 1 3 -1.</_>
        <_>
          6 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 2 3 -1.</_>
        <_>
          2 1 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 4 1 -1.</_>
        <_>
          2 2 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 12 3 -1.</_>
        <_>
          12 17 4 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 16 6 4 -1.</_>
        <_>
          11 16 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 3 6 -1.</_>
        <_>
          4 9 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 12 9 -1.</_>
        <_>
          6 5 12 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 14 20 -1.</_>
        <_>
          6 0 7 10 2.</_>
        <_>
          13 10 7 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 16 2 2 -1.</_>
        <_>
          15 16 1 1 2.</_>
        <_>
          16 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 16 2 2 -1.</_>
        <_>
          15 16 1 1 2.</_>
        <_>
          16 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 8 1 3 -1.</_>
        <_>
          19 9 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 4 1 2 -1.</_>
        <_>
          13 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 4 2 -1.</_>
        <_>
          0 5 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 5 1 6 -1.</_>
        <_>
          19 7 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 2 1 -1.</_>
        <_>
          17 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 1 1 3 -1.</_>
        <_>
          13 2 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 17 1 3 -1.</_>
        <_>
          17 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 8 8 -1.</_>
        <_>
          5 4 4 4 2.</_>
        <_>
          9 8 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 2 2 -1.</_>
        <_>
          1 2 1 1 2.</_>
        <_>
          2 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 8 6 -1.</_>
        <_>
          0 0 4 3 2.</_>
        <_>
          4 3 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 3 4 2 -1.</_>
        <_>
          6 4 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 3 3 -1.</_>
        <_>
          1 1 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 7 2 -1.</_>
        <_>
          6 2 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 12 6 -1.</_>
        <_>
          6 6 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 9 2 -1.</_>
        <_>
          4 16 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 15 6 4 -1.</_>
        <_>
          9 15 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 15 12 1 -1.</_>
        <_>
          12 15 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 17 1 3 -1.</_>
        <_>
          17 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 15 2 2 -1.</_>
        <_>
          17 15 1 1 2.</_>
        <_>
          18 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 13 3 3 -1.</_>
        <_>
          3 14 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 1 3 -1.</_>
        <_>
          10 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 14 8 -1.</_>
        <_>
          11 0 7 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 12 2 -1.</_>
        <_>
          6 0 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 4 3 -1.</_>
        <_>
          4 0 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 1 1 2 -1.</_>
        <_>
          13 2 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 3 6 -1.</_>
        <_>
          8 5 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 2 2 2 -1.</_>
        <_>
          18 2 1 1 2.</_>
        <_>
          19 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 1 2 14 -1.</_>
        <_>
          16 1 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 2 2 -1.</_>
        <_>
          15 6 1 1 2.</_>
        <_>
          16 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 6 3 -1.</_>
        <_>
          5 1 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 16 2 2 -1.</_>
        <_>
          7 16 1 1 2.</_>
        <_>
          8 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 2 2 -1.</_>
        <_>
          5 17 1 1 2.</_>
        <_>
          6 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 10 6 10 -1.</_>
        <_>
          11 10 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 6 3 -1.</_>
        <_>
          12 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 5 2 10 -1.</_>
        <_>
          14 10 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 6 2 -1.</_>
        <_>
          11 13 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 1 3 -1.</_>
        <_>
          8 2 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 15 2 2 -1.</_>
        <_>
          12 15 1 1 2.</_>
        <_>
          13 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 6 4 -1.</_>
        <_>
          6 8 3 2 2.</_>
        <_>
          9 10 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 3 5 -1.</_>
        <_>
          8 5 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 7 3 -1.</_>
        <_>
          0 6 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 6 6 -1.</_>
        <_>
          9 9 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 8 8 -1.</_>
        <_>
          5 11 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 6 -1.</_>
        <_>
          4 9 1 3 2.</_>
        <_>
          5 12 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 11 6 1 -1.</_>
        <_>
          12 11 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 6 6 11 -1.</_>
        <_>
          15 6 2 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 2 2 -1.</_>
        <_>
          8 17 1 1 2.</_>
        <_>
          9 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 12 12 1 -1.</_>
        <_>
          8 12 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 17 3 2 -1.</_>
        <_>
          11 18 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 6 1 -1.</_>
        <_>
          10 17 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 14 6 -1.</_>
        <_>
          4 3 14 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 2 12 -1.</_>
        <_>
          14 8 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 3 2 -1.</_>
        <_>
          12 14 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 6 1 -1.</_>
        <_>
          8 1 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 6 1 -1.</_>
        <_>
          12 6 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 19 2 1 -1.</_>
        <_>
          4 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 2 -1.</_>
        <_>
          18 16 1 1 2.</_>
        <_>
          19 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 11 3 7 -1.</_>
        <_>
          17 11 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 5 1 6 -1.</_>
        <_>
          19 8 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 4 3 -1.</_>
        <_>
          9 9 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 8 4 4 -1.</_>
        <_>
          16 8 2 2 2.</_>
        <_>
          18 10 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 2 2 -1.</_>
        <_>
          2 8 1 1 2.</_>
        <_>
          3 9 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 6 4 -1.</_>
        <_>
          3 5 3 2 2.</_>
        <_>
          6 7 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 8 16 -1.</_>
        <_>
          2 3 4 8 2.</_>
        <_>
          6 11 4 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 17 1 3 -1.</_>
        <_>
          17 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 8 11 -1.</_>
        <_>
          11 2 4 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 3 6 14 -1.</_>
        <_>
          16 3 3 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 18 2 -1.</_>
        <_>
          6 9 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 14 3 -1.</_>
        <_>
          6 11 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 9 3 -1.</_>
        <_>
          13 9 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 4 6 -1.</_>
        <_>
          3 5 2 3 2.</_>
        <_>
          5 8 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 3 7 -1.</_>
        <_>
          4 7 1 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 11 6 -1.</_>
        <_>
          2 10 11 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 6 3 -1.</_>
        <_>
          8 10 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 3 11 -1.</_>
        <_>
          4 3 1 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 6 1 -1.</_>
        <_>
          3 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 18 1 2 -1.</_>
        <_>
          18 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 12 6 -1.</_>
        <_>
          8 0 6 3 2.</_>
        <_>
          14 3 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 5 1 3 -1.</_>
        <_>
          19 6 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 2 1 -1.</_>
        <_>
          6 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 2 1 -1.</_>
        <_>
          14 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 15 13 -1.</_>
        <_>
          8 6 5 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 3 6 2 -1.</_>
        <_>
          6 3 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 1 2 -1.</_>
        <_>
          0 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 2 6 -1.</_>
        <_>
          8 8 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 6 19 -1.</_>
        <_>
          5 0 2 19 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 6 5 -1.</_>
        <_>
          5 1 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 14 3 6 -1.</_>
        <_>
          17 16 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 13 2 6 -1.</_>
        <_>
          18 13 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 18 2 2 -1.</_>
        <_>
          18 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 14 9 4 -1.</_>
        <_>
          14 14 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 4 6 -1.</_>
        <_>
          15 8 2 3 2.</_>
        <_>
          17 11 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 1 3 -1.</_>
        <_>
          1 17 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 3 14 -1.</_>
        <_>
          8 0 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 2 1 -1.</_>
        <_>
          13 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 6 5 -1.</_>
        <_>
          10 9 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 5 4 9 -1.</_>
        <_>
          17 5 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 6 6 -1.</_>
        <_>
          13 0 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 15 2 2 -1.</_>
        <_>
          16 15 1 1 2.</_>
        <_>
          17 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 15 2 2 -1.</_>
        <_>
          16 15 1 1 2.</_>
        <_>
          17 16 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 2 18 -1.</_>
        <_>
          13 11 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 8 10 -1.</_>
        <_>
          8 9 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 2 3 -1.</_>
        <_>
          8 4 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 6 9 -1.</_>
        <_>
          11 4 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 4 5 6 -1.</_>
        <_>
          15 6 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 2 2 -1.</_>
        <_>
          12 18 1 1 2.</_>
        <_>
          13 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 1 3 -1.</_>
        <_>
          1 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 19 2 1 -1.</_>
        <_>
          13 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 10 6 6 -1.</_>
        <_>
          10 10 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 6 5 -1.</_>
        <_>
          16 2 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 5 2 6 -1.</_>
        <_>
          9 7 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 2 2 -1.</_>
        <_>
          2 15 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 14 4 6 -1.</_>
        <_>
          10 16 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 2 -1.</_>
        <_>
          10 7 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 6 2 -1.</_>
        <_>
          6 9 3 1 2.</_>
        <_>
          9 10 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 1 12 -1.</_>
        <_>
          0 6 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 15 1 -1.</_>
        <_>
          9 0 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 8 2 -1.</_>
        <_>
          9 0 4 1 2.</_>
        <_>
          13 1 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 2 8 1 -1.</_>
        <_>
          16 2 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 10 6 -1.</_>
        <_>
          7 3 10 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 6 2 3 -1.</_>
        <_>
          18 7 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 12 2 2 -1.</_>
        <_>
          4 12 1 1 2.</_>
        <_>
          5 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 6 2 -1.</_>
        <_>
          8 6 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 9 6 -1.</_>
        <_>
          3 9 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 18 2 2 -1.</_>
        <_>
          18 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 2 6 16 -1.</_>
        <_>
          13 2 2 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 15 13 -1.</_>
        <_>
          7 4 5 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 2 3 10 -1.</_>
        <_>
          17 2 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 2 1 -1.</_>
        <_>
          7 10 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 18 16 -1.</_>
        <_>
          10 1 9 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 4 3 15 -1.</_>
        <_>
          15 4 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 13 1 2 -1.</_>
        <_>
          19 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 5 8 -1.</_>
        <_>
          2 10 5 4 2.</_></rects></_></features></cascade>
</opencv_storage>
