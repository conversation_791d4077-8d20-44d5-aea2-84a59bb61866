import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../components/NotificationSystem';
import api from '../services/api';

// Types
interface Lesson {
  id: number;
  title: string;
  content: string;
  lesson_type: string;
  type_display: string;
  estimated_duration: number;
  chapter: {
    id: number;
    title: string;
    course: {
      id: number;
      title: string;
    };
  };
  exercise?: {
    id: number;
    title: string;
  };
  progress: {
    is_completed: boolean;
    time_spent: number;
    started_at?: string;
    completed_at?: string;
  };
}

const LessonDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // Helper functions for notifications
  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message });
  };
  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };

  // State
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [loading, setLoading] = useState(true);
  const [completing, setCompleting] = useState(false);
  const [startTime] = useState(Date.now());

  // Effects
  useEffect(() => {
    if (user && id) {
      fetchLesson();
    }
  }, [user, id]);

  // API Functions
  const fetchLesson = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/lessons/${id}/`);
      if (response.data) {
        setLesson(response.data);
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération de la leçon:', error);
      if (error.response?.status === 404) {
        showError('Leçon non trouvée', 'La leçon demandée n\'existe pas.');
      } else if (error.response?.status === 403) {
        showError(
          'Accès refusé',
          error.response.data.error || 'Vous n\'avez pas accès à cette leçon.'
        );
      } else {
        showError('Erreur', 'Impossible de charger la leçon');
      }
      navigate('/courses');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteLesson = async () => {
    if (!lesson) return;

    try {
      setCompleting(true);
      const timeSpent = Math.floor((Date.now() - startTime) / 1000); // en secondes

      const response = await api.post(`/lessons/${lesson.id}/complete/`, {
        time_spent: timeSpent
      });

      if (response.data) {
        showSuccess('Leçon terminée !', response.data.message);
        // Rediriger vers le cours
        setTimeout(() => {
          navigate(`/courses/${lesson.chapter.course.id}`);
        }, 1500);
      }
    } catch (error: any) {
      console.error('Erreur lors de la completion:', error);
      showError(
        'Erreur',
        error.response?.data?.error || 'Impossible de marquer la leçon comme terminée'
      );
    } finally {
      setCompleting(false);
    }
  };

  // Utility Functions
  const getLessonTypeIcon = (type: string) => {
    switch (type) {
      case 'theory':
        return '📚';
      case 'example':
        return '💡';
      case 'exercise':
        return '✏️';
      case 'quiz':
        return '❓';
      case 'video':
        return '🎥';
      default:
        return '📖';
    }
  };

  const getLessonTypeColor = (type: string) => {
    switch (type) {
      case 'theory':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'example':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'exercise':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'quiz':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';
      case 'video':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatContent = (content: string) => {
    // Convertir le contenu en HTML simple
    return content
      .split('\n\n')
      .map((paragraph, index) => {
        if (paragraph.trim() === '') return null;

        // Détecter les listes
        if (paragraph.includes('- ') || paragraph.includes('• ')) {
          const items = paragraph
            .split('\n')
            .filter(line => line.trim().startsWith('- ') || line.trim().startsWith('• '));
          return (
            <ul key={index} className="list-disc list-inside mb-4 space-y-1">
              {items.map((item, itemIndex) => (
                <li key={itemIndex} className="text-gray-700 dark:text-gray-300">
                  {item.replace(/^[- •]\s*/, '')}
                </li>
              ))}
            </ul>
          );
        }

        // Détecter les titres
        if (paragraph.endsWith(':') && paragraph.length < 100) {
          return (
            <h3 key={index} className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
              {paragraph}
            </h3>
          );
        }

        // Paragraphe normal
        return (
          <p key={index} className="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">
            {paragraph}
          </p>
        );
      })
      .filter(Boolean);
  };

  // Loading and Error States
  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Accès restreint</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour accéder à cette leçon.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement de la leçon...</p>
        </div>
      </div>
    );
  }

  if (!lesson) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Leçon non trouvée</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            La leçon demandée n'existe pas ou n'est plus disponible.
          </p>
          <button
            onClick={() => navigate('/courses')}
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Retour aux cours
          </button>
        </div>
      </div>
    );
  }

  // Main Render
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation */}
      <div className="mb-6 flex items-center text-sm text-gray-600 dark:text-gray-400">
        <button
          onClick={() => navigate('/courses')}
          className="hover:text-primary-600 transition-colors"
        >
          Cours
        </button>
        <span className="mx-2">›</span>
        <button
          onClick={() => navigate(`/courses/${lesson.chapter.course.id}`)}
          className="hover:text-primary-600 transition-colors"
        >
          {lesson.chapter.course.title}
        </button>
        <span className="mx-2">›</span>
        <span className="text-gray-900 dark:text-white font-medium">
          {lesson.title}
        </span>
      </div>

      <div className="max-w-4xl mx-auto">
        {/* En-tête de la leçon */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <span className="text-3xl mr-4">{getLessonTypeIcon(lesson.lesson_type)}</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {lesson.title}
                </h1>
                <div className="flex items-center mt-2 space-x-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getLessonTypeColor(lesson.lesson_type)}`}>
                    {lesson.type_display}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ⏱ {lesson.estimated_duration} minutes
                  </span>
                  {lesson.progress.is_completed && (
                    <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                      ✅ Terminée
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            <span className="font-medium">Chapitre:</span> {lesson.chapter.title}
          </div>

          {lesson.progress.time_spent > 0 && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <span className="font-medium">Temps déjà passé:</span>{' '}
              {Math.floor(lesson.progress.time_spent / 60)} minutes
            </div>
          )}
        </motion.div>

        {/* Contenu de la leçon */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="prose prose-lg max-w-none dark:prose-invert">
            {formatContent(lesson.content)}
          </div>
        </motion.div>

        {/* Actions */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(`/courses/${lesson.chapter.course.id}`)}
                className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                ← Retour au cours
              </button>
              {lesson.exercise && (
                <a
                  href={`/exercises/${lesson.exercise.id}`}
                  className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                >
                  📝 Faire l'exercice
                </a>
              )}
            </div>

            {!lesson.progress.is_completed && (
              <button
                onClick={handleCompleteLesson}
                disabled={completing}
                className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {completing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2"></div>
                    Finalisation...
                  </>
                ) : (
                  '✅ Marquer comme terminée'
                )}
              </button>
            )}
          </div>

          {lesson.progress.is_completed && (
            <div className="mt-4 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
              <div className="flex items-center">
                <span className="text-green-800 dark:text-green-200 font-medium">
                  ✅ Leçon terminée le{' '}
                  {new Date(lesson.progress.completed_at!).toLocaleDateString('fr-FR')}
                </span>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default LessonDetailPage;