{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { setIntersectDependencies } from './dependenciesSetIntersect.generated.js';\nimport { setSymDifferenceDependencies } from './dependenciesSetSymDifference.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSetUnion } from '../../factoriesAny.js';\nexport var setUnionDependencies = {\n  IndexDependencies,\n  concatDependencies,\n  setIntersectDependencies,\n  setSymDifferenceDependencies,\n  sizeDependencies,\n  subsetDependencies,\n  typedDependencies,\n  createSetUnion\n};", "map": {"version": 3, "names": ["IndexDependencies", "concatDependencies", "setIntersectDependencies", "setSymDifferenceDependencies", "sizeDependencies", "subsetDependencies", "typedDependencies", "createSetUnion", "setUnionDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSetUnion.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { setIntersectDependencies } from './dependenciesSetIntersect.generated.js';\nimport { setSymDifferenceDependencies } from './dependenciesSetSymDifference.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSetUnion } from '../../factoriesAny.js';\nexport var setUnionDependencies = {\n  IndexDependencies,\n  concatDependencies,\n  setIntersectDependencies,\n  setSymDifferenceDependencies,\n  sizeDependencies,\n  subsetDependencies,\n  typedDependencies,\n  createSetUnion\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,4BAA4B,QAAQ,6CAA6C;AAC1F,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,oBAAoB,GAAG;EAChCR,iBAAiB;EACjBC,kBAAkB;EAClBC,wBAAwB;EACxBC,4BAA4B;EAC5BC,gBAAgB;EAChBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}