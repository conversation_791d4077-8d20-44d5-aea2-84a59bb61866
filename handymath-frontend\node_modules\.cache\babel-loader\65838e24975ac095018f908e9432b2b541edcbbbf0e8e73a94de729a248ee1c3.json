{"ast": null, "code": "export var acscDocs = {\n  name: 'acsc',\n  category: 'Trigonometry',\n  syntax: ['acsc(x)'],\n  description: 'Calculate the inverse cotangent of a value.',\n  examples: ['acsc(2)', 'acsc(csc(0.5))', 'acsc(0.5)'],\n  seealso: ['csc', 'asin', 'asec']\n};", "map": {"version": 3, "names": ["acscDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/acsc.js"], "sourcesContent": ["export var acscDocs = {\n  name: 'acsc',\n  category: 'Trigonometry',\n  syntax: ['acsc(x)'],\n  description: 'Calculate the inverse cotangent of a value.',\n  examples: ['acsc(2)', 'acsc(csc(0.5))', 'acsc(0.5)'],\n  seealso: ['csc', 'asin', 'asec']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,WAAW,CAAC;EACpDC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}