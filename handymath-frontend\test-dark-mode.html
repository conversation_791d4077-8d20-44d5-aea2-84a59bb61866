<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mode Sombre - HandyMath</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        html {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        html {
            background-color: #ffffff;
            color: #1f2937;
        }
        
        html.dark {
            background-color: #111827;
            color: #f9fafb;
        }
        
        body {
            background-color: inherit;
            color: inherit;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
    </style>
</head>
<body class="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header avec bouton de test -->
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    🧪 Test du Mode Sombre
                </h1>
                <button 
                    id="themeToggle"
                    class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                    🌙 Mode Sombre
                </button>
            </div>

            <!-- Statut actuel -->
            <div class="mb-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <h2 class="text-xl font-semibold mb-2">📊 Statut Actuel</h2>
                <p><strong>Thème :</strong> <span id="currentTheme">Clair</span></p>
                <p><strong>Classe HTML :</strong> <span id="htmlClass">Aucune</span></p>
                <p><strong>localStorage :</strong> <span id="localStorageTheme">Aucun</span></p>
            </div>

            <!-- Exemples de composants -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Carte 1 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        📚 Carte d'exemple
                    </h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Cette carte devrait changer de couleur selon le thème.
                    </p>
                    <button class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded transition-colors">
                        Bouton Test
                    </button>
                </div>

                <!-- Carte 2 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        🎨 Palette de couleurs
                    </h3>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-primary-500 rounded"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-300">Primary</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-gray-500 dark:bg-gray-400 rounded"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-300">Gray</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-green-500 rounded"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-300">Success</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formulaire de test -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                    📝 Formulaire de test
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Nom
                        </label>
                        <input 
                            type="text" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            placeholder="Votre nom"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Message
                        </label>
                        <textarea 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            rows="3"
                            placeholder="Votre message"
                        ></textarea>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2 text-blue-900 dark:text-blue-100">
                    📋 Instructions de test
                </h3>
                <ul class="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-200">
                    <li>Cliquez sur le bouton "Mode Sombre" pour basculer</li>
                    <li>Vérifiez que tous les éléments changent de couleur</li>
                    <li>Le statut doit se mettre à jour automatiquement</li>
                    <li>Rafraîchissez la page pour tester la persistance</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Variables
        const themeToggle = document.getElementById('themeToggle');
        const currentThemeSpan = document.getElementById('currentTheme');
        const htmlClassSpan = document.getElementById('htmlClass');
        const localStorageThemeSpan = document.getElementById('localStorageTheme');

        // Fonction pour mettre à jour l'affichage du statut
        function updateStatus() {
            const isDark = document.documentElement.classList.contains('dark');
            const storedTheme = localStorage.getItem('theme');
            
            currentThemeSpan.textContent = isDark ? 'Sombre' : 'Clair';
            htmlClassSpan.textContent = document.documentElement.className || 'Aucune';
            localStorageThemeSpan.textContent = storedTheme || 'Aucun';
            
            themeToggle.textContent = isDark ? '☀️ Mode Clair' : '🌙 Mode Sombre';
        }

        // Fonction pour basculer le thème
        function toggleTheme() {
            const isDark = document.documentElement.classList.contains('dark');
            
            if (isDark) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
            
            updateStatus();
        }

        // Initialisation
        function initTheme() {
            const storedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            if (storedTheme === 'dark' || (!storedTheme && prefersDark)) {
                document.documentElement.classList.add('dark');
            }
            
            updateStatus();
        }

        // Event listeners
        themeToggle.addEventListener('click', toggleTheme);

        // Initialiser au chargement
        initTheme();

        // Mettre à jour le statut toutes les secondes pour le debug
        setInterval(updateStatus, 1000);

        console.log('🧪 Page de test du mode sombre chargée');
        console.log('📊 Statut initial:', {
            isDark: document.documentElement.classList.contains('dark'),
            storedTheme: localStorage.getItem('theme'),
            prefersDark: window.matchMedia('(prefers-color-scheme: dark)').matches
        });
    </script>
</body>
</html>
