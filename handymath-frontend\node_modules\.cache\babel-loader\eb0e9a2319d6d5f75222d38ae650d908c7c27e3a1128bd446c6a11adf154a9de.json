{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { mapSlicesDependencies } from './dependenciesMapSlices.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';\nimport { smallerDependencies } from './dependenciesSmaller.generated.js';\nimport { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createQuantileSeq } from '../../factoriesAny.js';\nexport var quantileSeqDependencies = {\n  bignumberDependencies,\n  addDependencies,\n  compareDependencies,\n  divideDependencies,\n  isIntegerDependencies,\n  largerDependencies,\n  mapSlicesDependencies,\n  multiplyDependencies,\n  partitionSelectDependencies,\n  smallerDependencies,\n  smallerEqDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createQuantileSeq\n};", "map": {"version": 3, "names": ["bignumberDependencies", "addDependencies", "compareDependencies", "divideDependencies", "isIntegerDependencies", "largerDependencies", "mapSlicesDependencies", "multiplyDependencies", "partitionSelectDependencies", "smallerDependencies", "smallerEqDependencies", "subtractDependencies", "typedDependencies", "createQuantileSeq", "quantileSeqDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesQuantileSeq.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { mapSlicesDependencies } from './dependenciesMapSlices.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';\nimport { smallerDependencies } from './dependenciesSmaller.generated.js';\nimport { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createQuantileSeq } from '../../factoriesAny.js';\nexport var quantileSeqDependencies = {\n  bignumberDependencies,\n  addDependencies,\n  compareDependencies,\n  divideDependencies,\n  isIntegerDependencies,\n  largerDependencies,\n  mapSlicesDependencies,\n  multiplyDependencies,\n  partitionSelectDependencies,\n  smallerDependencies,\n  smallerEqDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createQuantileSeq\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,OAAO,IAAIC,uBAAuB,GAAG;EACnCd,qBAAqB;EACrBC,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,qBAAqB;EACrBC,kBAAkB;EAClBC,qBAAqB;EACrBC,oBAAoB;EACpBC,2BAA2B;EAC3BC,mBAAmB;EACnBC,qBAAqB;EACrBC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}