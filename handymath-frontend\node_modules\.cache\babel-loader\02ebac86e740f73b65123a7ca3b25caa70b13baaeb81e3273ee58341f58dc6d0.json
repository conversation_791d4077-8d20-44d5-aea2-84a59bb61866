{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromRows';\nvar dependencies = ['typed', 'matrix', 'flatten', 'size'];\nexport var createMatrixFromRows = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    flatten,\n    size\n  } = _ref;\n  /**\n   * Create a dense matrix from vectors as individual rows.\n   * If you pass column vectors, they will be transposed (but not conjugated!)\n   *\n   * Syntax:\n   *\n   *    math.matrixFromRows(...arr)\n   *    math.matrixFromRows(row1, row2)\n   *    math.matrixFromRows(row1, row2, row3)\n   *\n   * Examples:\n   *\n   *    math.matrixFromRows([1, 2, 3], [[4],[5],[6]])\n   *    math.matrixFromRows(...vectors)\n   *\n   * See also:\n   *\n   *    matrix, matrixFromColumns, matrixFromFunction, zeros\n   *\n   * @param {... Array | Matrix} rows  Multiple rows\n   * @return { number[][] | Matrix } if at least one of the arguments is an array, an array will be returned\n   */\n  return typed(name, {\n    '...Array': function Array(arr) {\n      return _createArray(arr);\n    },\n    '...Matrix': function Matrix(arr) {\n      return matrix(_createArray(arr.map(m => m.toArray())));\n    }\n\n    // TODO implement this properly for SparseMatrix\n  });\n  function _createArray(arr) {\n    if (arr.length === 0) throw new TypeError('At least one row is needed to construct a matrix.');\n    var N = checkVectorTypeAndReturnLength(arr[0]);\n    var result = [];\n    for (var row of arr) {\n      var rowLength = checkVectorTypeAndReturnLength(row);\n      if (rowLength !== N) {\n        throw new TypeError('The vectors had different length: ' + (N | 0) + ' ≠ ' + (rowLength | 0));\n      }\n      result.push(flatten(row));\n    }\n    return result;\n  }\n  function checkVectorTypeAndReturnLength(vec) {\n    var s = size(vec);\n    if (s.length === 1) {\n      // 1D vector\n      return s[0];\n    } else if (s.length === 2) {\n      // 2D vector\n      if (s[0] === 1) {\n        // row vector\n        return s[1];\n      } else if (s[1] === 1) {\n        // col vector\n        return s[0];\n      } else {\n        throw new TypeError('At least one of the arguments is not a vector.');\n      }\n    } else {\n      throw new TypeError('Only one- or two-dimensional vectors are supported.');\n    }\n  }\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatrixFromRows", "_ref", "typed", "matrix", "flatten", "size", "Array", "arr", "_createArray", "Matrix", "map", "m", "toArray", "length", "TypeError", "N", "checkVectorTypeAndReturnLength", "result", "row", "<PERSON><PERSON><PERSON><PERSON>", "push", "vec", "s"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/matrix/matrixFromRows.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromRows';\nvar dependencies = ['typed', 'matrix', 'flatten', 'size'];\nexport var createMatrixFromRows = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    flatten,\n    size\n  } = _ref;\n  /**\n   * Create a dense matrix from vectors as individual rows.\n   * If you pass column vectors, they will be transposed (but not conjugated!)\n   *\n   * Syntax:\n   *\n   *    math.matrixFromRows(...arr)\n   *    math.matrixFromRows(row1, row2)\n   *    math.matrixFromRows(row1, row2, row3)\n   *\n   * Examples:\n   *\n   *    math.matrixFromRows([1, 2, 3], [[4],[5],[6]])\n   *    math.matrixFromRows(...vectors)\n   *\n   * See also:\n   *\n   *    matrix, matrixFromColumns, matrixFromFunction, zeros\n   *\n   * @param {... Array | Matrix} rows  Multiple rows\n   * @return { number[][] | Matrix } if at least one of the arguments is an array, an array will be returned\n   */\n  return typed(name, {\n    '...Array': function Array(arr) {\n      return _createArray(arr);\n    },\n    '...Matrix': function Matrix(arr) {\n      return matrix(_createArray(arr.map(m => m.toArray())));\n    }\n\n    // TODO implement this properly for SparseMatrix\n  });\n  function _createArray(arr) {\n    if (arr.length === 0) throw new TypeError('At least one row is needed to construct a matrix.');\n    var N = checkVectorTypeAndReturnLength(arr[0]);\n    var result = [];\n    for (var row of arr) {\n      var rowLength = checkVectorTypeAndReturnLength(row);\n      if (rowLength !== N) {\n        throw new TypeError('The vectors had different length: ' + (N | 0) + ' ≠ ' + (rowLength | 0));\n      }\n      result.push(flatten(row));\n    }\n    return result;\n  }\n  function checkVectorTypeAndReturnLength(vec) {\n    var s = size(vec);\n    if (s.length === 1) {\n      // 1D vector\n      return s[0];\n    } else if (s.length === 2) {\n      // 2D vector\n      if (s[0] === 1) {\n        // row vector\n        return s[1];\n      } else if (s[1] === 1) {\n        // col vector\n        return s[0];\n      } else {\n        throw new TypeError('At least one of the arguments is not a vector.');\n      }\n    } else {\n      throw new TypeError('Only one- or two-dimensional vectors are supported.');\n    }\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;AACzD,OAAO,IAAIC,oBAAoB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGJ,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,UAAU,EAAE,SAASQ,KAAKA,CAACC,GAAG,EAAE;MAC9B,OAAOC,YAAY,CAACD,GAAG,CAAC;IAC1B,CAAC;IACD,WAAW,EAAE,SAASE,MAAMA,CAACF,GAAG,EAAE;MAChC,OAAOJ,MAAM,CAACK,YAAY,CAACD,GAAG,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD;;IAEA;EACF,CAAC,CAAC;EACF,SAASJ,YAAYA,CAACD,GAAG,EAAE;IACzB,IAAIA,GAAG,CAACM,MAAM,KAAK,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mDAAmD,CAAC;IAC9F,IAAIC,CAAC,GAAGC,8BAA8B,CAACT,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIU,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,GAAG,IAAIX,GAAG,EAAE;MACnB,IAAIY,SAAS,GAAGH,8BAA8B,CAACE,GAAG,CAAC;MACnD,IAAIC,SAAS,KAAKJ,CAAC,EAAE;QACnB,MAAM,IAAID,SAAS,CAAC,oCAAoC,IAAIC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,IAAII,SAAS,GAAG,CAAC,CAAC,CAAC;MAC/F;MACAF,MAAM,CAACG,IAAI,CAAChB,OAAO,CAACc,GAAG,CAAC,CAAC;IAC3B;IACA,OAAOD,MAAM;EACf;EACA,SAASD,8BAA8BA,CAACK,GAAG,EAAE;IAC3C,IAAIC,CAAC,GAAGjB,IAAI,CAACgB,GAAG,CAAC;IACjB,IAAIC,CAAC,CAACT,MAAM,KAAK,CAAC,EAAE;MAClB;MACA,OAAOS,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,MAAM,IAAIA,CAAC,CAACT,MAAM,KAAK,CAAC,EAAE;MACzB;MACA,IAAIS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACd;QACA,OAAOA,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,MAAM,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACrB;QACA,OAAOA,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,MAAM;QACL,MAAM,IAAIR,SAAS,CAAC,gDAAgD,CAAC;MACvE;IACF,CAAC,MAAM;MACL,MAAM,IAAIA,SAAS,CAAC,qDAAqD,CAAC;IAC5E;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}