# Generated by Django 5.2.1 on 2025-06-03 11:27

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='course',
            old_name='created_by',
            new_name='instructor',
        ),
        migrations.RenameField(
            model_name='exercise',
            old_name='problem',
            new_name='description',
        ),
        migrations.RemoveField(
            model_name='course',
            name='level',
        ),
        migrations.RemoveField(
            model_name='exercise',
            name='solution',
        ),
        migrations.RemoveField(
            model_name='user',
            name='niveau',
        ),
        migrations.RemoveField(
            model_name='user',
            name='nom',
        ),
        migrations.RemoveField(
            model_name='user',
            name='prenom',
        ),
        migrations.AddField(
            model_name='exercise',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='bio',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='profile_pics/'),
        ),
        migrations.AlterField(
            model_name='course',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='equation',
            name='solution_text',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='exercise',
            name='difficulty',
            field=models.CharField(choices=[('easy', 'Facile'), ('medium', 'Moyen'), ('hard', 'Difficile')], default='medium', max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, max_length=254, verbose_name='email address'),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('student', 'Étudiant'), ('teacher', 'Professeur'), ('admin', 'Administrateur')], default='student', max_length=20),
        ),
        migrations.CreateModel(
            name='EquationImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='equation_images/')),
                ('recognized_text', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equation_images', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
