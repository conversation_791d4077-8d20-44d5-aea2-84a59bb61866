{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createSackurTetrode } from '../../factoriesAny.js';\nexport var sackurTetrodeDependencies = {\n  BigNumberDependencies,\n  createSackurTetrode\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "createSackurTetrode", "sackurTetrodeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSackurTetrode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createSackurTetrode } from '../../factoriesAny.js';\nexport var sackurTetrodeDependencies = {\n  BigNumberDependencies,\n  createSackurTetrode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,OAAO,IAAIC,yBAAyB,GAAG;EACrCF,qBAAqB;EACrBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}