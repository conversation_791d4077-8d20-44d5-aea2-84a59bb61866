{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  const {\n    user,\n    updateUser\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n  const [profile, setProfile] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    prenom: '',\n    nom: '',\n    email: '',\n    bio: ''\n  });\n  useEffect(() => {\n    if (user) {\n      fetchProfile();\n    }\n  }, [user]);\n  const fetchProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/users/me/');\n      setProfile(response.data);\n      setFormData({\n        prenom: response.data.prenom || '',\n        nom: response.data.nom || '',\n        email: response.data.email || '',\n        bio: response.data.bio || ''\n      });\n    } catch (error) {\n      console.error('Erreur lors du chargement du profil:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de charger le profil'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSave = async () => {\n    try {\n      const response = await api.patch('/users/me/', formData);\n      setProfile(response.data);\n      setEditing(false);\n      updateUser(response.data);\n      addNotification({\n        type: 'success',\n        title: 'Profil mis à jour',\n        message: 'Vos informations ont été sauvegardées'\n      });\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder le profil'\n      });\n    }\n  };\n  const handleCancel = () => {\n    setFormData({\n      prenom: (profile === null || profile === void 0 ? void 0 : profile.prenom) || '',\n      nom: (profile === null || profile === void 0 ? void 0 : profile.nom) || '',\n      email: (profile === null || profile === void 0 ? void 0 : profile.email) || '',\n      bio: (profile === null || profile === void 0 ? void 0 : profile.bio) || ''\n    });\n    setEditing(false);\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(StudentLayout, {\n      title: \"Profil\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s refus\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour voir votre profil.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(StudentLayout, {\n      title: \"Profil\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement du profil...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StudentLayout, {\n    title: \"Mon Profil\",\n    subtitle: \"G\\xE9rez vos informations personnelles et vos pr\\xE9f\\xE9rences\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold ${user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'}`,\n              children: ((profile === null || profile === void 0 ? void 0 : profile.prenom) || user.username).charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                children: profile !== null && profile !== void 0 && profile.prenom && profile !== null && profile !== void 0 && profile.nom ? `${profile.prenom} ${profile.nom}` : profile === null || profile === void 0 ? void 0 : profile.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400\",\n                children: [\"@\", profile === null || profile === void 0 ? void 0 : profile.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`,\n                children: user.role === 'admin' ? 'Administrateur' : 'Étudiant'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setEditing(!editing),\n            className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\",\n            children: editing ? 'Annuler' : 'Modifier'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n              children: \"Pr\\xE9nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), editing ? /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.prenom,\n              onChange: e => setFormData({\n                ...formData,\n                prenom: e.target.value\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: (profile === null || profile === void 0 ? void 0 : profile.prenom) || 'Non renseigné'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), editing ? /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.nom,\n              onChange: e => setFormData({\n                ...formData,\n                nom: e.target.value\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: (profile === null || profile === void 0 ? void 0 : profile.nom) || 'Non renseigné'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), editing ? /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: formData.email,\n              onChange: e => setFormData({\n                ...formData,\n                email: e.target.value\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: profile === null || profile === void 0 ? void 0 : profile.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n              children: \"Membre depuis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: profile !== null && profile !== void 0 && profile.date_joined ? new Date(profile.date_joined).toLocaleDateString('fr-FR') : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n            children: \"Biographie\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), editing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: formData.bio,\n            onChange: e => setFormData({\n              ...formData,\n              bio: e.target.value\n            }),\n            rows: 3,\n            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n            placeholder: \"Parlez-nous de vous...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-900 dark:text-white\",\n            children: (profile === null || profile === void 0 ? void 0 : profile.bio) || 'Aucune biographie renseignée.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), editing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCancel,\n            className: \"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\",\n            children: \"Sauvegarder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this), user.role === 'student' && (profile === null || profile === void 0 ? void 0 : profile.stats) && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 dark:text-white mb-6\",\n          children: \"Mes statistiques\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-blue-600\",\n              children: profile.stats.total_equations\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: \"\\xC9quations r\\xE9solues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-green-600\",\n              children: profile.stats.total_exercises\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: \"Exercices compl\\xE9t\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-purple-600\",\n              children: profile.stats.total_points\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: \"Points gagn\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-orange-600\",\n              children: [Math.round(profile.stats.success_rate), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: \"Taux de r\\xE9ussite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePage, \"gfut+MOPdADQZZcncvhMbvtoL9o=\", false, function () {\n  return [useAuth, useNotifications];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "useNotifications", "StudentLayout", "api", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "user", "updateUser", "addNotification", "profile", "setProfile", "loading", "setLoading", "editing", "setEditing", "formData", "setFormData", "prenom", "nom", "email", "bio", "fetchProfile", "response", "get", "data", "error", "console", "type", "title", "message", "handleSave", "patch", "handleCancel", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subtitle", "div", "initial", "opacity", "y", "animate", "role", "username", "char<PERSON>t", "toUpperCase", "onClick", "value", "onChange", "e", "target", "date_joined", "Date", "toLocaleDateString", "rows", "placeholder", "stats", "transition", "delay", "total_equations", "total_exercises", "total_points", "Math", "round", "success_rate", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/ProfilePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\nimport api from '../services/api';\n\ninterface UserProfile {\n  id: number;\n  username: string;\n  email: string;\n  nom: string;\n  prenom: string;\n  role: string;\n  niveau?: string;\n  date_joined?: string;\n  bio?: string;\n  profile_picture?: string;\n  stats?: {\n    total_equations: number;\n    total_exercises: number;\n    total_points: number;\n    success_rate: number;\n  };\n}\n\nconst ProfilePage: React.FC = () => {\n  const { user, updateUser } = useAuth();\n  const { addNotification } = useNotifications();\n\n  const [profile, setProfile] = useState<UserProfile | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    prenom: '',\n    nom: '',\n    email: '',\n    bio: ''\n  });\n\n  useEffect(() => {\n    if (user) {\n      fetchProfile();\n    }\n  }, [user]);\n\n  const fetchProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/users/me/');\n      setProfile(response.data);\n      setFormData({\n        prenom: response.data.prenom || '',\n        nom: response.data.nom || '',\n        email: response.data.email || '',\n        bio: response.data.bio || ''\n      });\n    } catch (error) {\n      console.error('Erreur lors du chargement du profil:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de charger le profil'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      const response = await api.patch('/users/me/', formData);\n      setProfile(response.data);\n      setEditing(false);\n      updateUser(response.data);\n      addNotification({\n        type: 'success',\n        title: 'Profil mis à jour',\n        message: 'Vos informations ont été sauvegardées'\n      });\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder le profil'\n      });\n    }\n  };\n\n  const handleCancel = () => {\n    setFormData({\n      prenom: profile?.prenom || '',\n      nom: profile?.nom || '',\n      email: profile?.email || '',\n      bio: profile?.bio || ''\n    });\n    setEditing(false);\n  };\n\n  if (!user) {\n    return (\n      <StudentLayout title=\"Profil\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès refusé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Vous devez être connecté pour voir votre profil.\n          </p>\n        </div>\n      </StudentLayout>\n    );\n  }\n\n  if (loading) {\n    return (\n      <StudentLayout title=\"Profil\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement du profil...</p>\n        </div>\n      </StudentLayout>\n    );\n  }\n\n  return (\n    <StudentLayout\n      title=\"Mon Profil\"\n      subtitle=\"Gérez vos informations personnelles et vos préférences\"\n    >\n      <div className=\"max-w-4xl mx-auto\">\n          {/* En-tête du profil */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <div className=\"flex items-center space-x-4\">\n                <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold ${\n                  user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'\n                }`}>\n                  {(profile?.prenom || user.username).charAt(0).toUpperCase()}\n                </div>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {profile?.prenom && profile?.nom\n                      ? `${profile.prenom} ${profile.nom}`\n                      : profile?.username\n                    }\n                  </h1>\n                  <p className=\"text-gray-600 dark:text-gray-400\">@{profile?.username}</p>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                    user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'\n                  }`}>\n                    {user.role === 'admin' ? 'Administrateur' : 'Étudiant'}\n                  </span>\n                </div>\n              </div>\n              <button\n                onClick={() => setEditing(!editing)}\n                className=\"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\"\n              >\n                {editing ? 'Annuler' : 'Modifier'}\n              </button>\n            </div>\n\n            {/* Informations du profil */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Prénom\n                </label>\n                {editing ? (\n                  <input\n                    type=\"text\"\n                    value={formData.prenom}\n                    onChange={(e) => setFormData({...formData, prenom: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-white\">{profile?.prenom || 'Non renseigné'}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nom\n                </label>\n                {editing ? (\n                  <input\n                    type=\"text\"\n                    value={formData.nom}\n                    onChange={(e) => setFormData({...formData, nom: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-white\">{profile?.nom || 'Non renseigné'}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Email\n                </label>\n                {editing ? (\n                  <input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => setFormData({...formData, email: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-white\">{profile?.email}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Membre depuis\n                </label>\n                <p className=\"text-gray-900 dark:text-white\">\n                  {profile?.date_joined ? new Date(profile.date_joined).toLocaleDateString('fr-FR') : 'N/A'}\n                </p>\n              </div>\n            </div>\n\n            {/* Bio */}\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Biographie\n              </label>\n              {editing ? (\n                <textarea\n                  value={formData.bio}\n                  onChange={(e) => setFormData({...formData, bio: e.target.value})}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder=\"Parlez-nous de vous...\"\n                />\n              ) : (\n                <p className=\"text-gray-900 dark:text-white\">\n                  {profile?.bio || 'Aucune biographie renseignée.'}\n                </p>\n              )}\n            </div>\n\n            {/* Boutons de sauvegarde */}\n            {editing && (\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={handleCancel}\n                  className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n                >\n                  Annuler\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\"\n                >\n                  Sauvegarder\n                </button>\n              </div>\n            )}\n          </motion.div>\n\n          {/* Statistiques (pour les étudiants) */}\n          {user.role === 'student' && profile?.stats && (\n            <motion.div\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6\">\n                Mes statistiques\n              </h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">{profile.stats.total_equations}</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Équations résolues</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">{profile.stats.total_exercises}</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Exercices complétés</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">{profile.stats.total_points}</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Points gagnés</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-600\">{Math.round(profile.stats.success_rate)}%</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Taux de réussite</div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n    </StudentLayout>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAqBlC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM;IAAEU;EAAgB,CAAC,GAAGT,gBAAgB,CAAC,CAAC;EAE9C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAqB,IAAI,CAAC;EAChE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE;EACP,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACd,IAAIU,IAAI,EAAE;MACRe,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;EAEV,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMU,QAAQ,GAAG,MAAMrB,GAAG,CAACsB,GAAG,CAAC,YAAY,CAAC;MAC5Cb,UAAU,CAACY,QAAQ,CAACE,IAAI,CAAC;MACzBR,WAAW,CAAC;QACVC,MAAM,EAAEK,QAAQ,CAACE,IAAI,CAACP,MAAM,IAAI,EAAE;QAClCC,GAAG,EAAEI,QAAQ,CAACE,IAAI,CAACN,GAAG,IAAI,EAAE;QAC5BC,KAAK,EAAEG,QAAQ,CAACE,IAAI,CAACL,KAAK,IAAI,EAAE;QAChCC,GAAG,EAAEE,QAAQ,CAACE,IAAI,CAACJ,GAAG,IAAI;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DjB,eAAe,CAAC;QACdmB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMrB,GAAG,CAAC8B,KAAK,CAAC,YAAY,EAAEhB,QAAQ,CAAC;MACxDL,UAAU,CAACY,QAAQ,CAACE,IAAI,CAAC;MACzBV,UAAU,CAAC,KAAK,CAAC;MACjBP,UAAU,CAACe,QAAQ,CAACE,IAAI,CAAC;MACzBhB,eAAe,CAAC;QACdmB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDjB,eAAe,CAAC;QACdmB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzBhB,WAAW,CAAC;MACVC,MAAM,EAAE,CAAAR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAI,EAAE;MAC7BC,GAAG,EAAE,CAAAT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,GAAG,KAAI,EAAE;MACvBC,KAAK,EAAE,CAAAV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAI,EAAE;MAC3BC,GAAG,EAAE,CAAAX,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEW,GAAG,KAAI;IACvB,CAAC,CAAC;IACFN,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,IAAI,CAACR,IAAI,EAAE;IACT,oBACEH,OAAA,CAACH,aAAa;MAAC4B,KAAK,EAAC,QAAQ;MAAAK,QAAA,eAC3B9B,OAAA;QAAK+B,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1B9B,OAAA;UAAI+B,SAAS,EAAC,yBAAyB;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDnC,OAAA;UAAG+B,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAEhD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEA,IAAI3B,OAAO,EAAE;IACX,oBACER,OAAA,CAACH,aAAa;MAAC4B,KAAK,EAAC,QAAQ;MAAAK,QAAA,eAC3B9B,OAAA;QAAK+B,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChC9B,OAAA;UAAK+B,SAAS,EAAC;QAAgF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGnC,OAAA;UAAG+B,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEA,oBACEnC,OAAA,CAACH,aAAa;IACZ4B,KAAK,EAAC,YAAY;IAClBW,QAAQ,EAAC,iEAAwD;IAAAN,QAAA,eAEjE9B,OAAA;MAAK+B,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAE9B9B,OAAA,CAACN,MAAM,CAAC2C,GAAG;QACTN,SAAS,EAAC,yDAAyD;QACnEO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAE9B9B,OAAA;UAAK+B,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBACrD9B,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C9B,OAAA;cAAK+B,SAAS,EAAE,yFACd5B,IAAI,CAACuC,IAAI,KAAK,OAAO,GAAG,YAAY,GAAG,aAAa,EACnD;cAAAZ,QAAA,EACA,CAAC,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAIX,IAAI,CAACwC,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNnC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAI+B,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,EAC7DxB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEQ,MAAM,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,GAAG,GAC5B,GAAGT,OAAO,CAACQ,MAAM,IAAIR,OAAO,CAACS,GAAG,EAAE,GAClCT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC;cAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CAAC,eACLnC,OAAA;gBAAG+B,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,GAAC,GAAC,EAACxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEnC,OAAA;gBAAM+B,SAAS,EAAE,4DACf5B,IAAI,CAACuC,IAAI,KAAK,OAAO,GAAG,yBAAyB,GAAG,2BAA2B,EAC9E;gBAAAZ,QAAA,EACA3B,IAAI,CAACuC,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YACE8C,OAAO,EAAEA,CAAA,KAAMnC,UAAU,CAAC,CAACD,OAAO,CAAE;YACpCqB,SAAS,EAAC,uFAAuF;YAAAD,QAAA,EAEhGpB,OAAO,GAAG,SAAS,GAAG;UAAU;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnC,OAAA;UAAK+B,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBACpD9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO+B,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACPzB,OAAO,gBACNV,OAAA;cACEwB,IAAI,EAAC,MAAM;cACXuB,KAAK,EAAEnC,QAAQ,CAACE,MAAO;cACvBkC,QAAQ,EAAGC,CAAC,IAAKpC,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEE,MAAM,EAAEmC,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACpEhB,SAAS,EAAC;YAAiI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5I,CAAC,gBAEFnC,OAAA;cAAG+B,SAAS,EAAC,+BAA+B;cAAAD,QAAA,EAAE,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAI;YAAe;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENnC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO+B,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACPzB,OAAO,gBACNV,OAAA;cACEwB,IAAI,EAAC,MAAM;cACXuB,KAAK,EAAEnC,QAAQ,CAACG,GAAI;cACpBiC,QAAQ,EAAGC,CAAC,IAAKpC,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEG,GAAG,EAAEkC,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACjEhB,SAAS,EAAC;YAAiI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5I,CAAC,gBAEFnC,OAAA;cAAG+B,SAAS,EAAC,+BAA+B;cAAAD,QAAA,EAAE,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,GAAG,KAAI;YAAe;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENnC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO+B,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACPzB,OAAO,gBACNV,OAAA;cACEwB,IAAI,EAAC,OAAO;cACZuB,KAAK,EAAEnC,QAAQ,CAACI,KAAM;cACtBgC,QAAQ,EAAGC,CAAC,IAAKpC,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEI,KAAK,EAAEiC,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACnEhB,SAAS,EAAC;YAAiI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5I,CAAC,gBAEFnC,OAAA;cAAG+B,SAAS,EAAC,+BAA+B;cAAAD,QAAA,EAAExB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU;YAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACjE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENnC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO+B,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cAAG+B,SAAS,EAAC,+BAA+B;cAAAD,QAAA,EACzCxB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE6C,WAAW,GAAG,IAAIC,IAAI,CAAC9C,OAAO,CAAC6C,WAAW,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC,GAAG;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACnB9B,OAAA;YAAO+B,SAAS,EAAC,iEAAiE;YAAAD,QAAA,EAAC;UAEnF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACPzB,OAAO,gBACNV,OAAA;YACE+C,KAAK,EAAEnC,QAAQ,CAACK,GAAI;YACpB+B,QAAQ,EAAGC,CAAC,IAAKpC,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEK,GAAG,EAAEgC,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACjEO,IAAI,EAAE,CAAE;YACRvB,SAAS,EAAC,iIAAiI;YAC3IwB,WAAW,EAAC;UAAwB;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,gBAEFnC,OAAA;YAAG+B,SAAS,EAAC,+BAA+B;YAAAD,QAAA,EACzC,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEW,GAAG,KAAI;UAA+B;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLzB,OAAO,iBACNV,OAAA;UAAK+B,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9C9B,OAAA;YACE8C,OAAO,EAAEjB,YAAa;YACtBE,SAAS,EAAC,2GAA2G;YAAAD,QAAA,EACtH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnC,OAAA;YACE8C,OAAO,EAAEnB,UAAW;YACpBI,SAAS,EAAC,uFAAuF;YAAAD,QAAA,EAClG;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZhC,IAAI,CAACuC,IAAI,KAAK,SAAS,KAAIpC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkD,KAAK,kBACxCxD,OAAA,CAACN,MAAM,CAAC2C,GAAG;QACTN,SAAS,EAAC,oDAAoD;QAC9DO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BiB,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAA5B,QAAA,gBAE3B9B,OAAA;UAAI+B,SAAS,EAAC,sDAAsD;UAAAD,QAAA,EAAC;QAErE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnC,OAAA;UAAK+B,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBACpD9B,OAAA;YAAK+B,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B9B,OAAA;cAAK+B,SAAS,EAAC,kCAAkC;cAAAD,QAAA,EAAExB,OAAO,CAACkD,KAAK,CAACG;YAAe;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvFnC,OAAA;cAAK+B,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNnC,OAAA;YAAK+B,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B9B,OAAA;cAAK+B,SAAS,EAAC,mCAAmC;cAAAD,QAAA,EAAExB,OAAO,CAACkD,KAAK,CAACI;YAAe;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxFnC,OAAA;cAAK+B,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACNnC,OAAA;YAAK+B,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B9B,OAAA;cAAK+B,SAAS,EAAC,oCAAoC;cAAAD,QAAA,EAAExB,OAAO,CAACkD,KAAK,CAACK;YAAY;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtFnC,OAAA;cAAK+B,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNnC,OAAA;YAAK+B,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B9B,OAAA;cAAK+B,SAAS,EAAC,oCAAoC;cAAAD,QAAA,GAAEgC,IAAI,CAACC,KAAK,CAACzD,OAAO,CAACkD,KAAK,CAACQ,YAAY,CAAC,EAAC,GAAC;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnGnC,OAAA;cAAK+B,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACjC,EAAA,CAjRID,WAAqB;EAAA,QACIN,OAAO,EACRC,gBAAgB;AAAA;AAAAqE,EAAA,GAFxChE,WAAqB;AAmR3B,eAAeA,WAAW;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}