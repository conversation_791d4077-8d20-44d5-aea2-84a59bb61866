#!/usr/bin/env python
"""
Script de test pour l'API de contact
"""
import os
import sys
import django
from django.conf import settings

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configurer Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

# Maintenant on peut importer les modules Django
from django.test import Client
from django.contrib.auth import get_user_model
from api.models import ContactMessage
import json

def test_contact_api():
    """Test de l'API de contact"""
    print("🧪 Test de l'API de contact...")
    
    # Créer un client de test
    client = Client()
    
    # Données de test
    data = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'category': 'question',
        'subject': 'Test Subject',
        'message': 'Ceci est un message de test pour vérifier que l\'API fonctionne correctement.'
    }
    
    print(f"📤 Envoi des données: {data}")
    
    # Envoyer la requête POST
    response = client.post(
        '/api/contact/send/',
        data=json.dumps(data),
        content_type='application/json'
    )
    
    print(f"📥 Status Code: {response.status_code}")
    print(f"📥 Response: {response.content.decode()}")
    
    if response.status_code == 201:
        print("✅ Message envoyé avec succès!")
        
        # Vérifier que le message a été créé en base
        messages = ContactMessage.objects.all()
        print(f"📊 Nombre de messages en base: {messages.count()}")
        
        if messages.exists():
            last_message = messages.last()
            print(f"📧 Dernier message: {last_message.subject} de {last_message.name}")
            return True
    else:
        print("❌ Erreur lors de l'envoi du message")
        return False

def test_admin_api():
    """Test de l'API admin"""
    print("\n👑 Test de l'API admin...")
    
    # Créer un utilisateur admin
    User = get_user_model()
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
            'role': 'admin'
        }
    )

    # S'assurer que l'utilisateur a les bonnes permissions
    admin_user.is_staff = True
    admin_user.is_superuser = True
    admin_user.role = 'admin'
    admin_user.save()
    
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print("👤 Utilisateur admin créé")
    else:
        print("👤 Utilisateur admin existant")
    
    # Créer un client authentifié
    client = Client()
    client.force_login(admin_user)
    
    # Tester la liste des messages
    response = client.get('/api/admin/contact/messages/')
    print(f"📥 Status Code (liste): {response.status_code}")
    
    if response.status_code == 200:
        data = json.loads(response.content.decode())
        print(f"📊 Nombre de messages: {len(data.get('results', []))}")
        print("✅ API admin fonctionne!")
        return True
    else:
        print(f"❌ Erreur API admin: {response.content.decode()}")
        return False

def test_stats_api():
    """Test de l'API des statistiques"""
    print("\n📊 Test de l'API des statistiques...")
    
    # Utiliser l'utilisateur admin existant
    User = get_user_model()
    admin_user = User.objects.filter(is_staff=True).first()
    
    if not admin_user:
        print("❌ Aucun utilisateur admin trouvé")
        return False
    
    client = Client()
    client.force_login(admin_user)
    
    # Tester les statistiques
    response = client.get('/api/admin/contact/stats/')
    print(f"📥 Status Code (stats): {response.status_code}")
    
    if response.status_code == 200:
        data = json.loads(response.content.decode())
        print(f"📊 Total messages: {data.get('total_messages', 0)}")
        print(f"📊 Nouveaux: {data.get('status_breakdown', {}).get('new', 0)}")
        print("✅ API statistiques fonctionne!")
        return True
    else:
        print(f"❌ Erreur API stats: {response.content.decode()}")
        return False

if __name__ == '__main__':
    print("🚀 Démarrage des tests de l'API de contact\n")
    
    try:
        # Test 1: Envoi de message
        test1_success = test_contact_api()
        
        # Test 2: API admin
        test2_success = test_admin_api()
        
        # Test 3: API statistiques
        test3_success = test_stats_api()
        
        print(f"\n📋 Résultats des tests:")
        print(f"   📤 Envoi de message: {'✅' if test1_success else '❌'}")
        print(f"   👑 API admin: {'✅' if test2_success else '❌'}")
        print(f"   📊 API stats: {'✅' if test3_success else '❌'}")
        
        if all([test1_success, test2_success, test3_success]):
            print("\n🎉 Tous les tests sont passés! L'API de contact fonctionne correctement.")
        else:
            print("\n⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
            
    except Exception as e:
        print(f"\n💥 Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
