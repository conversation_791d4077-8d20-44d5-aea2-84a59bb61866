{"ast": null, "code": "export var lgammaDocs = {\n  name: 'lgamma',\n  category: 'Probability',\n  syntax: ['lgamma(n)'],\n  description: 'Logarithm of the gamma function for real, positive numbers and complex numbers, ' + 'using <PERSON><PERSON><PERSON><PERSON> approximation for numbers and Stirling series for complex numbers.',\n  examples: ['lgamma(4)', 'lgamma(1/2)', 'lgamma(i)', 'lgamma(complex(1.1, 2))'],\n  seealso: ['gamma']\n};", "map": {"version": 3, "names": ["lgammaDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/lgamma.js"], "sourcesContent": ["export var lgammaDocs = {\n  name: 'lgamma',\n  category: 'Probability',\n  syntax: ['lgamma(n)'],\n  description: 'Logarithm of the gamma function for real, positive numbers and complex numbers, ' + 'using <PERSON><PERSON><PERSON><PERSON> approximation for numbers and Stirling series for complex numbers.',\n  examples: ['lgamma(4)', 'lgamma(1/2)', 'lgamma(i)', 'lgamma(complex(1.1, 2))'],\n  seealso: ['gamma']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,kFAAkF,GAAG,kFAAkF;EACpLC,QAAQ,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,yBAAyB,CAAC;EAC9EC,OAAO,EAAE,CAAC,OAAO;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}