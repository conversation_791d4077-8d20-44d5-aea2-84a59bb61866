{"ast": null, "code": "export var numericDocs = {\n  name: 'numeric',\n  category: 'Utils',\n  syntax: ['numeric(x)'],\n  description: 'Convert a numeric input to a specific numeric type: number, BigNumber, bigint, or Fraction.',\n  examples: ['numeric(\"4\")', 'numeric(\"4\", \"number\")', 'numeric(\"4\", \"bigint\")', 'numeric(\"4\", \"BigNumber\")', 'numeric(\"4\", \"Fraction\")', 'numeric(4, \"Fraction\")', 'numeric(fraction(2, 5), \"number\")'],\n  seealso: ['number', 'bigint', 'fraction', 'bignumber', 'string', 'format']\n};", "map": {"version": 3, "names": ["numericDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/numeric.js"], "sourcesContent": ["export var numericDocs = {\n  name: 'numeric',\n  category: 'Utils',\n  syntax: ['numeric(x)'],\n  description: 'Convert a numeric input to a specific numeric type: number, BigNumber, bigint, or Fraction.',\n  examples: ['numeric(\"4\")', 'numeric(\"4\", \"number\")', 'numeric(\"4\", \"bigint\")', 'numeric(\"4\", \"BigNumber\")', 'numeric(\"4\", \"Fraction\")', 'numeric(4, \"Fraction\")', 'numeric(fraction(2, 5), \"number\")'],\n  seealso: ['number', 'bigint', 'fraction', 'bignumber', 'string', 'format']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,6FAA6F;EAC1GC,QAAQ,EAAE,CAAC,cAAc,EAAE,wBAAwB,EAAE,wBAAwB,EAAE,2BAA2B,EAAE,0BAA0B,EAAE,wBAAwB,EAAE,mCAAmC,CAAC;EACtMC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ;AAC3E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}