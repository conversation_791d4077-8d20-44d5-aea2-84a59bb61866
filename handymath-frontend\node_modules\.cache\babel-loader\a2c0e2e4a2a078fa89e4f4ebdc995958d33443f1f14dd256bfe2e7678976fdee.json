{"ast": null, "code": "export var log10Docs = {\n  name: 'log10',\n  category: 'Arithmetic',\n  syntax: ['log10(x)'],\n  description: 'Compute the 10-base logarithm of a value.',\n  examples: ['log10(0.00001)', 'log10(10000)', '10 ^ 4', 'log(10000) / log(10)', 'log(10000, 10)'],\n  seealso: ['exp', 'log']\n};", "map": {"version": 3, "names": ["log10Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/log10.js"], "sourcesContent": ["export var log10Docs = {\n  name: 'log10',\n  category: 'Arithmetic',\n  syntax: ['log10(x)'],\n  description: 'Compute the 10-base logarithm of a value.',\n  examples: ['log10(0.00001)', 'log10(10000)', '10 ^ 4', 'log(10000) / log(10)', 'log(10000, 10)'],\n  seealso: ['exp', 'log']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,QAAQ,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;EAChGC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}