{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createWienDisplacement } from '../../factoriesAny.js';\nexport var wienDisplacementDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createWienDisplacement\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createWienDisplacement", "wienDisplacementDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesWienDisplacement.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createWienDisplacement } from '../../factoriesAny.js';\nexport var wienDisplacementDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createWienDisplacement\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,OAAO,IAAIC,4BAA4B,GAAG;EACxCH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}