{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ExerciseDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\nimport api from '../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExerciseDetailPage = () => {\n  _s();\n  var _exercise$user_attemp2, _exercise$user_attemp3, _exercise$user_attemp4, _exercise$user_attemp5, _exercise$user_attemp6, _exercise$user_attemp7, _exercise$user_attemp8;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title, message) => {\n    addNotification({\n      type: 'success',\n      title,\n      message\n    });\n  };\n  const showError = (title, message) => {\n    addNotification({\n      type: 'error',\n      title,\n      message\n    });\n  };\n  const showWarning = (title, message) => {\n    addNotification({\n      type: 'warning',\n      title,\n      message\n    });\n  };\n\n  // State\n  const [exercise, setExercise] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [starting, setStarting] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [userAnswer, setUserAnswer] = useState('');\n  const [selectedChoice, setSelectedChoice] = useState(null);\n  const [timeLeft, setTimeLeft] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n\n  // Effects\n  useEffect(() => {\n    if (user && id) {\n      fetchExercise();\n    }\n  }, [user, id]);\n  useEffect(() => {\n    var _exercise$user_attemp;\n    let interval;\n    if (timeLeft !== null && timeLeft > 0 && (exercise === null || exercise === void 0 ? void 0 : (_exercise$user_attemp = exercise.user_attempt) === null || _exercise$user_attemp === void 0 ? void 0 : _exercise$user_attemp.status) === 'in_progress') {\n      interval = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev === null || prev <= 1) {\n            // Temps écoulé - soumettre automatiquement\n            handleSubmit(true);\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [timeLeft, exercise === null || exercise === void 0 ? void 0 : (_exercise$user_attemp2 = exercise.user_attempt) === null || _exercise$user_attemp2 === void 0 ? void 0 : _exercise$user_attemp2.status]);\n\n  // API Functions\n  const fetchExercise = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get(`/exercises/${id}/`);\n      if (response.data) {\n        var _response$data$user_a;\n        setExercise(response.data);\n        // Si l'exercice est en cours, calculer le temps restant\n        if (((_response$data$user_a = response.data.user_attempt) === null || _response$data$user_a === void 0 ? void 0 : _response$data$user_a.status) === 'in_progress') {\n          const startedAt = new Date(response.data.user_attempt.started_at);\n          const now = new Date();\n          const elapsed = Math.floor((now.getTime() - startedAt.getTime()) / 1000);\n          const remaining = Math.max(0, response.data.time_limit - elapsed);\n          setTimeLeft(remaining);\n          setStartTime(startedAt);\n        }\n      }\n    } catch (error) {\n      console.error('Erreur lors de la récupération de l\\'exercice:', error);\n      showError('Erreur', 'Impossible de charger l\\'exercice');\n      navigate('/exercises');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleStart = async () => {\n    if (!exercise) return;\n    try {\n      setStarting(true);\n      const response = await api.post(`/exercises/${exercise.id}/start/`);\n      if (response.data) {\n        showSuccess('Exercice démarré !', 'Vous pouvez maintenant répondre à la question.');\n        setTimeLeft(exercise.time_limit);\n        setStartTime(new Date());\n        await fetchExercise(); // Recharger pour avoir la tentative\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur lors du démarrage:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.error) {\n        showError('Erreur', error.response.data.error);\n      } else {\n        showError('Erreur', 'Impossible de démarrer l\\'exercice');\n      }\n    } finally {\n      setStarting(false);\n    }\n  };\n  const handleSubmit = async (timeExpired = false) => {\n    if (!exercise || !exercise.user_attempt) return;\n    const answer = exercise.exercise_type === 'multiple_choice' ? (selectedChoice === null || selectedChoice === void 0 ? void 0 : selectedChoice.toString()) || '' : userAnswer.trim();\n    if (!answer && !timeExpired) {\n      showWarning('Réponse manquante', 'Veuillez fournir une réponse avant de soumettre.');\n      return;\n    }\n    try {\n      setSubmitting(true);\n      const timeTaken = startTime ? Math.floor((new Date().getTime() - startTime.getTime()) / 1000) : exercise.time_limit;\n      const response = await api.post(`/exercises/${exercise.id}/submit/`, {\n        answer,\n        time_taken: timeTaken\n      });\n      if (response.data) {\n        const {\n          is_correct,\n          points_earned,\n          correct_answer,\n          explanation\n        } = response.data;\n        if (is_correct) {\n          showSuccess('Bravo !', `Réponse correcte ! Vous avez gagné ${points_earned} points.`);\n        } else {\n          showError('Réponse incorrecte', `La bonne réponse était : ${correct_answer}`);\n        }\n        // Recharger l'exercice pour voir les résultats\n        await fetchExercise();\n        setTimeLeft(null);\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Erreur lors de la soumission:', error);\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.error) {\n        showError('Erreur', error.response.data.error);\n      } else {\n        showError('Erreur', 'Impossible de soumettre la réponse');\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Utility Functions\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'easy':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'hard':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'equation':\n        return '📐';\n      case 'multiple_choice':\n        return '📝';\n      case 'calculation':\n        return '🧮';\n      case 'proof':\n        return '📋';\n      default:\n        return '📚';\n    }\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s restreint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der \\xE0 cet exercice.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement de l'exercice...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this);\n  }\n  if (!exercise) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Exercice non trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"L'exercice demand\\xE9 n'existe pas ou n'est plus disponible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/exercises'),\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Retour aux exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this);\n  }\n  const isCompleted = ((_exercise$user_attemp3 = exercise.user_attempt) === null || _exercise$user_attemp3 === void 0 ? void 0 : _exercise$user_attemp3.status) === 'completed';\n  const isInProgress = ((_exercise$user_attemp4 = exercise.user_attempt) === null || _exercise$user_attemp4 === void 0 ? void 0 : _exercise$user_attemp4.status) === 'in_progress';\n\n  // Main Render\n  return /*#__PURE__*/_jsxDEV(StudentLayout, {\n    title: exercise.title,\n    subtitle: `${exercise.type_display} • ${exercise.difficulty_display} • ${exercise.points} points`,\n    actions: /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => navigate('/exercises'),\n      className: \"flex items-center text-primary-600 hover:text-primary-700 transition-colors\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mr-2\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this), \"Retour aux exercices\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 9\n    }, this),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl mr-3\",\n                children: getTypeIcon(exercise.exercise_type)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                  children: exercise.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg text-gray-600 dark:text-gray-400\",\n                  children: [exercise.course.title, \" \\u2022 \", exercise.type_display]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`,\n              children: exercise.difficulty_display\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 dark:text-gray-300 mb-6\",\n            children: exercise.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Points:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), \" \", exercise.points]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: \"\\u23F1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Temps limite:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), \" \", Math.floor(exercise.time_limit / 60), \"min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Statut:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), \" \", isCompleted ? 'Terminé' : isInProgress ? 'En cours' : 'Nouveau']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 11\n          }, this), timeLeft !== null && isInProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl mr-2\",\n                children: \"\\u23F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-mono font-bold\",\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), timeLeft <= 60 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-center text-red-600 dark:text-red-400 mt-2\",\n              children: \"Moins d'une minute restante !\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold mb-4 flex items-center\",\n            children: \"Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg whitespace-pre-wrap\",\n              children: exercise.question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 11\n          }, this), !isCompleted && !isInProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStart,\n              disabled: starting,\n              className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\",\n              children: starting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), \"D\\xE9marrage...\"]\n              }, void 0, true) : 'Commencer l\\'exercice'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), isInProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Choisissez votre r\\xE9ponse :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: exercise.choices.map((choice, index) => /*#__PURE__*/_jsxDEV(motion.label, {\n                className: `flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${selectedChoice === choice.id ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 hover:bg-gray-50 dark:hover:bg-gray-700'}`,\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"choice\",\n                  value: choice.id,\n                  checked: selectedChoice === choice.id,\n                  onChange: () => setSelectedChoice(choice.id),\n                  className: \"mr-4 w-5 h-5 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3\",\n                    children: String.fromCharCode(65 + index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: choice.choice_text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)]\n              }, choice.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: () => handleSubmit(),\n                disabled: submitting || !selectedChoice,\n                className: `font-medium py-4 px-12 rounded-xl transition-all duration-200 ${selectedChoice && !submitting ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105' : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'}`,\n                whileHover: selectedChoice && !submitting ? {\n                  scale: 1.05\n                } : {},\n                whileTap: selectedChoice && !submitting ? {\n                  scale: 0.95\n                } : {},\n                children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this), \"Soumission en cours...\"]\n                }, void 0, true) : selectedChoice ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: \"Valider ma r\\xE9ponse\"\n                }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2\",\n                    children: \"\\uD83D\\uDCDD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this), \"S\\xE9lectionnez une r\\xE9ponse\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), selectedChoice && !submitting && /*#__PURE__*/_jsxDEV(motion.p, {\n                className: \"mt-3 text-sm text-gray-600 dark:text-gray-400\",\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                children: [\"R\\xE9ponse s\\xE9lectionn\\xE9e : \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: String.fromCharCode(65 + exercise.choices.findIndex(c => c.id === selectedChoice))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 44\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), isCompleted && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"R\\xE9sultats :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl mr-2\",\n                    children: (_exercise$user_attemp5 = exercise.user_attempt) !== null && _exercise$user_attemp5 !== void 0 && _exercise$user_attemp5.is_correct ? '✅' : '❌'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: (_exercise$user_attemp6 = exercise.user_attempt) !== null && _exercise$user_attemp6 !== void 0 && _exercise$user_attemp6.is_correct ? 'Correct' : 'Incorrect'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 dark:text-gray-400\",\n                  children: [\"Points obtenus : \", ((_exercise$user_attemp7 = exercise.user_attempt) === null || _exercise$user_attemp7 === void 0 ? void 0 : _exercise$user_attemp7.points_earned) || 0, \" / \", exercise.points]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl mr-2\",\n                    children: \"\\u23F1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"Temps pris\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 dark:text-gray-400\",\n                  children: formatTime(((_exercise$user_attemp8 = exercise.user_attempt) === null || _exercise$user_attemp8 === void 0 ? void 0 : _exercise$user_attemp8.time_taken) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), exercise.correct_answer && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 dark:bg-blue-900 rounded-lg p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-2 flex items-center\",\n                children: \"\\uD83D\\uDCA1 R\\xE9ponse correcte :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-mono\",\n                children: exercise.correct_answer\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), exercise.explanation && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 dark:bg-green-900 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-2 flex items-center\",\n                children: \"\\uD83D\\uDCDA Explication :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"whitespace-pre-wrap\",\n                children: exercise.explanation\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 292,\n    columnNumber: 5\n  }, this);\n};\n_s(ExerciseDetailPage, \"Qlq0inWs/VFCpH8Hac2xME6S48E=\", false, function () {\n  return [useParams, useNavigate, useAuth, useNotifications];\n});\n_c = ExerciseDetailPage;\nexport default ExerciseDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ExerciseDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "useAuth", "useNotifications", "StudentLayout", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExerciseDetailPage", "_s", "_exercise$user_attemp2", "_exercise$user_attemp3", "_exercise$user_attemp4", "_exercise$user_attemp5", "_exercise$user_attemp6", "_exercise$user_attemp7", "_exercise$user_attemp8", "id", "navigate", "user", "addNotification", "showSuccess", "title", "message", "type", "showError", "showWarning", "exercise", "setExercise", "loading", "setLoading", "starting", "setStarting", "submitting", "setSubmitting", "userAnswer", "setUserAnswer", "selectedChoice", "setSelectedChoice", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "fetchExercise", "_exercise$user_attemp", "interval", "user_attempt", "status", "setInterval", "prev", "handleSubmit", "clearInterval", "response", "get", "data", "_response$data$user_a", "startedAt", "Date", "started_at", "now", "elapsed", "Math", "floor", "getTime", "remaining", "max", "time_limit", "error", "console", "handleStart", "post", "_error$response", "_error$response$data", "timeExpired", "answer", "exercise_type", "toString", "trim", "timeTaken", "time_taken", "is_correct", "points_earned", "correct_answer", "explanation", "_error$response2", "_error$response2$data", "formatTime", "seconds", "mins", "secs", "padStart", "getDifficultyColor", "difficulty", "getTypeIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "isCompleted", "isInProgress", "subtitle", "type_display", "difficulty_display", "points", "actions", "div", "initial", "opacity", "y", "animate", "course", "description", "transition", "delay", "question", "disabled", "choices", "map", "choice", "index", "label", "x", "whileHover", "scale", "whileTap", "name", "value", "checked", "onChange", "String", "fromCharCode", "choice_text", "button", "p", "findIndex", "c", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/ExerciseDetailPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\nimport api from '../services/api';\n\n// Types\ninterface ExerciseChoice {\n  id: number;\n  choice_text: string;\n  order: number;\n}\n\ninterface Exercise {\n  id: number;\n  title: string;\n  description: string;\n  question: string;\n  course: {\n    id: number;\n    title: string;\n    level: string;\n  };\n  difficulty: string;\n  difficulty_display: string;\n  exercise_type: string;\n  type_display: string;\n  points: number;\n  time_limit: number;\n  choices: ExerciseChoice[];\n  user_attempt?: {\n    id: number;\n    status: string;\n    user_answer: string;\n    is_correct: boolean;\n    points_earned: number;\n    time_taken: number;\n    started_at: string;\n    completed_at?: string;\n  } | null;\n  correct_answer?: string;\n  explanation?: string;\n  created_at: string;\n}\n\nconst ExerciseDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const { addNotification } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message });\n  };\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message });\n  };\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message });\n  };\n\n  // State\n  const [exercise, setExercise] = useState<Exercise | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [starting, setStarting] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [userAnswer, setUserAnswer] = useState('');\n  const [selectedChoice, setSelectedChoice] = useState<number | null>(null);\n  const [timeLeft, setTimeLeft] = useState<number | null>(null);\n  const [startTime, setStartTime] = useState<Date | null>(null);\n\n  // Effects\n  useEffect(() => {\n    if (user && id) {\n      fetchExercise();\n    }\n  }, [user, id]);\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    if (timeLeft !== null && timeLeft > 0 && exercise?.user_attempt?.status === 'in_progress') {\n      interval = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev === null || prev <= 1) {\n            // Temps écoulé - soumettre automatiquement\n            handleSubmit(true);\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [timeLeft, exercise?.user_attempt?.status]);\n\n  // API Functions\n  const fetchExercise = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get(`/exercises/${id}/`);\n      if (response.data) {\n        setExercise(response.data);\n        // Si l'exercice est en cours, calculer le temps restant\n        if (response.data.user_attempt?.status === 'in_progress') {\n          const startedAt = new Date(response.data.user_attempt.started_at);\n          const now = new Date();\n          const elapsed = Math.floor((now.getTime() - startedAt.getTime()) / 1000);\n          const remaining = Math.max(0, response.data.time_limit - elapsed);\n          setTimeLeft(remaining);\n          setStartTime(startedAt);\n        }\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la récupération de l\\'exercice:', error);\n      showError('Erreur', 'Impossible de charger l\\'exercice');\n      navigate('/exercises');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStart = async () => {\n    if (!exercise) return;\n    try {\n      setStarting(true);\n      const response = await api.post(`/exercises/${exercise.id}/start/`);\n      if (response.data) {\n        showSuccess('Exercice démarré !', 'Vous pouvez maintenant répondre à la question.');\n        setTimeLeft(exercise.time_limit);\n        setStartTime(new Date());\n        await fetchExercise(); // Recharger pour avoir la tentative\n      }\n    } catch (error: any) {\n      console.error('Erreur lors du démarrage:', error);\n      if (error.response?.data?.error) {\n        showError('Erreur', error.response.data.error);\n      } else {\n        showError('Erreur', 'Impossible de démarrer l\\'exercice');\n      }\n    } finally {\n      setStarting(false);\n    }\n  };\n\n  const handleSubmit = async (timeExpired = false) => {\n    if (!exercise || !exercise.user_attempt) return;\n\n    const answer = exercise.exercise_type === 'multiple_choice'\n      ? selectedChoice?.toString() || ''\n      : userAnswer.trim();\n\n    if (!answer && !timeExpired) {\n      showWarning('Réponse manquante', 'Veuillez fournir une réponse avant de soumettre.');\n      return;\n    }\n\n    try {\n      setSubmitting(true);\n      const timeTaken = startTime\n        ? Math.floor((new Date().getTime() - startTime.getTime()) / 1000)\n        : exercise.time_limit;\n\n      const response = await api.post(`/exercises/${exercise.id}/submit/`, {\n        answer,\n        time_taken: timeTaken\n      });\n\n      if (response.data) {\n        const { is_correct, points_earned, correct_answer, explanation } = response.data;\n        if (is_correct) {\n          showSuccess(\n            'Bravo !',\n            `Réponse correcte ! Vous avez gagné ${points_earned} points.`\n          );\n        } else {\n          showError(\n            'Réponse incorrecte',\n            `La bonne réponse était : ${correct_answer}`\n          );\n        }\n        // Recharger l'exercice pour voir les résultats\n        await fetchExercise();\n        setTimeLeft(null);\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la soumission:', error);\n      if (error.response?.data?.error) {\n        showError('Erreur', error.response.data.error);\n      } else {\n        showError('Erreur', 'Impossible de soumettre la réponse');\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Utility Functions\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'hard':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'equation':\n        return '📐';\n      case 'multiple_choice':\n        return '📝';\n      case 'calculation':\n        return '🧮';\n      case 'proof':\n        return '📋';\n      default:\n        return '📚';\n    }\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès restreint</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder à cet exercice.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement de l'exercice...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!exercise) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Exercice non trouvé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            L'exercice demandé n'existe pas ou n'est plus disponible.\n          </p>\n          <button\n            onClick={() => navigate('/exercises')}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Retour aux exercices\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const isCompleted = exercise.user_attempt?.status === 'completed';\n  const isInProgress = exercise.user_attempt?.status === 'in_progress';\n\n  // Main Render\n  return (\n    <StudentLayout\n      title={exercise.title}\n      subtitle={`${exercise.type_display} • ${exercise.difficulty_display} • ${exercise.points} points`}\n      actions={\n        <button\n          onClick={() => navigate('/exercises')}\n          className=\"flex items-center text-primary-600 hover:text-primary-700 transition-colors\"\n        >\n          <span className=\"mr-2\">←</span>\n          Retour aux exercices\n        </button>\n      }\n    >\n      <div className=\"space-y-6\">\n\n      <div className=\"max-w-4xl mx-auto\">\n        {/* En-tête de l'exercice */}\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-3xl mr-3\">{getTypeIcon(exercise.exercise_type)}</span>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  {exercise.title}\n                </h1>\n                <p className=\"text-lg text-gray-600 dark:text-gray-400\">\n                  {exercise.course.title} • {exercise.type_display}\n                </p>\n              </div>\n            </div>\n            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n              {exercise.difficulty_display}\n            </span>\n          </div>\n\n          <p className=\"text-gray-700 dark:text-gray-300 mb-6\">\n            {exercise.description}\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div className=\"flex items-center\">\n              <span><strong>Points:</strong> {exercise.points}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"mr-2\">⏱</span>\n              <span><strong>Temps limite:</strong> {Math.floor(exercise.time_limit / 60)}min</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Statut:</strong> {\n                isCompleted ? 'Terminé' : isInProgress ? 'En cours' : 'Nouveau'\n              }</span>\n            </div>\n          </div>\n\n          {/* Chronomètre */}\n          {timeLeft !== null && isInProgress && (\n            <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg\">\n              <div className=\"flex items-center justify-center\">\n                <span className=\"text-2xl mr-2\">⏰</span>\n                <span className=\"text-2xl font-mono font-bold\">\n                  {formatTime(timeLeft)}\n                </span>\n              </div>\n              {timeLeft <= 60 && (\n                <p className=\"text-center text-red-600 dark:text-red-400 mt-2\">\n                  Moins d'une minute restante !\n                </p>\n              )}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Question et réponse */}\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <h2 className=\"text-2xl font-semibold mb-4 flex items-center\">\n            Question\n          </h2>\n          <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6\">\n            <p className=\"text-lg whitespace-pre-wrap\">{exercise.question}</p>\n          </div>\n\n          {!isCompleted && !isInProgress && (\n            <div className=\"text-center\">\n              <button\n                onClick={handleStart}\n                disabled={starting}\n                className=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\"\n              >\n                {starting ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"></div>\n                    Démarrage...\n                  </>\n                ) : (\n                  'Commencer l\\'exercice'\n                )}\n              </button>\n            </div>\n          )}\n\n          {isInProgress && (\n            <div>\n              <h3 className=\"text-xl font-semibold mb-4\">Choisissez votre réponse :</h3>\n              <div className=\"space-y-3\">\n                {exercise.choices.map((choice, index) => (\n                  <motion.label\n                    key={choice.id}\n                    className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${\n                      selectedChoice === choice.id\n                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'\n                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 hover:bg-gray-50 dark:hover:bg-gray-700'\n                    }`}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"choice\"\n                      value={choice.id}\n                      checked={selectedChoice === choice.id}\n                      onChange={() => setSelectedChoice(choice.id)}\n                      className=\"mr-4 w-5 h-5 text-primary-600\"\n                    />\n                    <div className=\"flex items-center\">\n                      <span className=\"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3\">\n                        {String.fromCharCode(65 + index)}\n                      </span>\n                      <span className=\"text-lg\">{choice.choice_text}</span>\n                    </div>\n                  </motion.label>\n                ))}\n              </div>\n\n              <div className=\"mt-8 text-center\">\n                <motion.button\n                  onClick={() => handleSubmit()}\n                  disabled={submitting || !selectedChoice}\n                  className={`font-medium py-4 px-12 rounded-xl transition-all duration-200 ${\n                    selectedChoice && !submitting\n                      ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'\n                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'\n                  }`}\n                  whileHover={selectedChoice && !submitting ? { scale: 1.05 } : {}}\n                  whileTap={selectedChoice && !submitting ? { scale: 0.95 } : {}}\n                >\n                  {submitting ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"></div>\n                      Soumission en cours...\n                    </>\n                  ) : selectedChoice ? (\n                    <>\n                      Valider ma réponse\n                    </>\n                  ) : (\n                    <>\n                      <span className=\"mr-2\">📝</span>\n                      Sélectionnez une réponse\n                    </>\n                  )}\n                </motion.button>\n\n                {selectedChoice && !submitting && (\n                  <motion.p\n                    className=\"mt-3 text-sm text-gray-600 dark:text-gray-400\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    Réponse sélectionnée : <strong>{String.fromCharCode(65 + exercise.choices.findIndex(c => c.id === selectedChoice))}</strong>\n                  </motion.p>\n                )}\n              </div>\n            </div>\n          )}\n\n          {isCompleted && (\n            <div>\n              <h3 className=\"text-xl font-semibold mb-4\">Résultats :</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <div className=\"flex items-center mb-2\">\n                    <span className=\"text-2xl mr-2\">\n                      {exercise.user_attempt?.is_correct ? '✅' : '❌'}\n                    </span>\n                    <span className=\"font-semibold\">\n                      {exercise.user_attempt?.is_correct ? 'Correct' : 'Incorrect'}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Points obtenus : {exercise.user_attempt?.points_earned || 0} / {exercise.points}\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <div className=\"flex items-center mb-2\">\n                    <span className=\"text-2xl mr-2\">⏱</span>\n                    <span className=\"font-semibold\">Temps pris</span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {formatTime(exercise.user_attempt?.time_taken || 0)}\n                  </p>\n                </div>\n              </div>\n\n              {exercise.correct_answer && (\n                <div className=\"bg-blue-50 dark:bg-blue-900 rounded-lg p-4 mb-4\">\n                  <h4 className=\"font-semibold mb-2 flex items-center\">\n                    💡 Réponse correcte :\n                  </h4>\n                  <p className=\"font-mono\">{exercise.correct_answer}</p>\n                </div>\n              )}\n\n              {exercise.explanation && (\n                <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\n                  <h4 className=\"font-semibold mb-2 flex items-center\">\n                    📚 Explication :\n                  </h4>\n                  <p className=\"whitespace-pre-wrap\">{exercise.explanation}</p>\n                </div>\n              )}\n            </div>\n          )}\n        </motion.div>\n      </div>\n      </div>\n    </StudentLayout>\n  );\n};\n\nexport default ExerciseDetailPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,GAAG,MAAM,iBAAiB;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAuCA,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzC,MAAM;IAAEC;EAAG,CAAC,GAAGpB,SAAS,CAAiB,CAAC;EAC1C,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAK,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEoB;EAAgB,CAAC,GAAGnB,gBAAgB,CAAC,CAAC;;EAE9C;EACA,MAAMoB,WAAW,GAAGA,CAACC,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;EACD,MAAME,SAAS,GAAGA,CAACH,KAAa,EAAEC,OAAe,KAAK;IACpDH,eAAe,CAAC;MAAEI,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACpD,CAAC;EACD,MAAMG,WAAW,GAAGA,CAACJ,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAc,IAAI,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIuB,IAAI,IAAIF,EAAE,EAAE;MACd0B,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACxB,IAAI,EAAEF,EAAE,CAAC,CAAC;EAEdrB,SAAS,CAAC,MAAM;IAAA,IAAAgD,qBAAA;IACd,IAAIC,QAAwB;IAC5B,IAAIN,QAAQ,KAAK,IAAI,IAAIA,QAAQ,GAAG,CAAC,IAAI,CAAAZ,QAAQ,aAARA,QAAQ,wBAAAiB,qBAAA,GAARjB,QAAQ,CAAEmB,YAAY,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBG,MAAM,MAAK,aAAa,EAAE;MACzFF,QAAQ,GAAGG,WAAW,CAAC,MAAM;QAC3BR,WAAW,CAACS,IAAI,IAAI;UAClB,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,IAAI,CAAC,EAAE;YAC9B;YACAC,YAAY,CAAC,IAAI,CAAC;YAClB,OAAO,CAAC;UACV;UACA,OAAOD,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV;IACA,OAAO,MAAM;MACX,IAAIJ,QAAQ,EAAEM,aAAa,CAACN,QAAQ,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACN,QAAQ,EAAEZ,QAAQ,aAARA,QAAQ,wBAAAjB,sBAAA,GAARiB,QAAQ,CAAEmB,YAAY,cAAApC,sBAAA,uBAAtBA,sBAAA,CAAwBqC,MAAM,CAAC,CAAC;;EAE9C;EACA,MAAMJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMjD,GAAG,CAACkD,GAAG,CAAC,cAAcpC,EAAE,GAAG,CAAC;MACnD,IAAImC,QAAQ,CAACE,IAAI,EAAE;QAAA,IAAAC,qBAAA;QACjB3B,WAAW,CAACwB,QAAQ,CAACE,IAAI,CAAC;QAC1B;QACA,IAAI,EAAAC,qBAAA,GAAAH,QAAQ,CAACE,IAAI,CAACR,YAAY,cAAAS,qBAAA,uBAA1BA,qBAAA,CAA4BR,MAAM,MAAK,aAAa,EAAE;UACxD,MAAMS,SAAS,GAAG,IAAIC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACR,YAAY,CAACY,UAAU,CAAC;UACjE,MAAMC,GAAG,GAAG,IAAIF,IAAI,CAAC,CAAC;UACtB,MAAMG,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGP,SAAS,CAACO,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACxE,MAAMC,SAAS,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACE,IAAI,CAACY,UAAU,GAAGN,OAAO,CAAC;UACjEpB,WAAW,CAACwB,SAAS,CAAC;UACtBtB,YAAY,CAACc,SAAS,CAAC;QACzB;MACF;IACF,CAAC,CAAC,OAAOW,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE1C,SAAS,CAAC,QAAQ,EAAE,mCAAmC,CAAC;MACxDP,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC1C,QAAQ,EAAE;IACf,IAAI;MACFK,WAAW,CAAC,IAAI,CAAC;MACjB,MAAMoB,QAAQ,GAAG,MAAMjD,GAAG,CAACmE,IAAI,CAAC,cAAc3C,QAAQ,CAACV,EAAE,SAAS,CAAC;MACnE,IAAImC,QAAQ,CAACE,IAAI,EAAE;QACjBjC,WAAW,CAAC,oBAAoB,EAAE,gDAAgD,CAAC;QACnFmB,WAAW,CAACb,QAAQ,CAACuC,UAAU,CAAC;QAChCxB,YAAY,CAAC,IAAIe,IAAI,CAAC,CAAC,CAAC;QACxB,MAAMd,aAAa,CAAC,CAAC,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOwB,KAAU,EAAE;MAAA,IAAAI,eAAA,EAAAC,oBAAA;MACnBJ,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,KAAAI,eAAA,GAAIJ,KAAK,CAACf,QAAQ,cAAAmB,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBjB,IAAI,cAAAkB,oBAAA,eAApBA,oBAAA,CAAsBL,KAAK,EAAE;QAC/B1C,SAAS,CAAC,QAAQ,EAAE0C,KAAK,CAACf,QAAQ,CAACE,IAAI,CAACa,KAAK,CAAC;MAChD,CAAC,MAAM;QACL1C,SAAS,CAAC,QAAQ,EAAE,oCAAoC,CAAC;MAC3D;IACF,CAAC,SAAS;MACRO,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAAA,CAAOuB,WAAW,GAAG,KAAK,KAAK;IAClD,IAAI,CAAC9C,QAAQ,IAAI,CAACA,QAAQ,CAACmB,YAAY,EAAE;IAEzC,MAAM4B,MAAM,GAAG/C,QAAQ,CAACgD,aAAa,KAAK,iBAAiB,GACvD,CAAAtC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuC,QAAQ,CAAC,CAAC,KAAI,EAAE,GAChCzC,UAAU,CAAC0C,IAAI,CAAC,CAAC;IAErB,IAAI,CAACH,MAAM,IAAI,CAACD,WAAW,EAAE;MAC3B/C,WAAW,CAAC,mBAAmB,EAAE,kDAAkD,CAAC;MACpF;IACF;IAEA,IAAI;MACFQ,aAAa,CAAC,IAAI,CAAC;MACnB,MAAM4C,SAAS,GAAGrC,SAAS,GACvBoB,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIL,IAAI,CAAC,CAAC,CAACM,OAAO,CAAC,CAAC,GAAGtB,SAAS,CAACsB,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,GAC/DpC,QAAQ,CAACuC,UAAU;MAEvB,MAAMd,QAAQ,GAAG,MAAMjD,GAAG,CAACmE,IAAI,CAAC,cAAc3C,QAAQ,CAACV,EAAE,UAAU,EAAE;QACnEyD,MAAM;QACNK,UAAU,EAAED;MACd,CAAC,CAAC;MAEF,IAAI1B,QAAQ,CAACE,IAAI,EAAE;QACjB,MAAM;UAAE0B,UAAU;UAAEC,aAAa;UAAEC,cAAc;UAAEC;QAAY,CAAC,GAAG/B,QAAQ,CAACE,IAAI;QAChF,IAAI0B,UAAU,EAAE;UACd3D,WAAW,CACT,SAAS,EACT,sCAAsC4D,aAAa,UACrD,CAAC;QACH,CAAC,MAAM;UACLxD,SAAS,CACP,oBAAoB,EACpB,4BAA4ByD,cAAc,EAC5C,CAAC;QACH;QACA;QACA,MAAMvC,aAAa,CAAC,CAAC;QACrBH,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,CAAC,OAAO2B,KAAU,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACnBjB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,KAAAiB,gBAAA,GAAIjB,KAAK,CAACf,QAAQ,cAAAgC,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,eAApBA,qBAAA,CAAsBlB,KAAK,EAAE;QAC/B1C,SAAS,CAAC,QAAQ,EAAE0C,KAAK,CAACf,QAAQ,CAACE,IAAI,CAACa,KAAK,CAAC;MAChD,CAAC,MAAM;QACL1C,SAAS,CAAC,QAAQ,EAAE,oCAAoC,CAAC;MAC3D;IACF,CAAC,SAAS;MACRS,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMoD,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAG3B,IAAI,CAACC,KAAK,CAACyB,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACb,QAAQ,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,MAAMC,kBAAkB,GAAIC,UAAkB,IAAK;IACjD,QAAQA,UAAU;MAChB,KAAK,MAAM;QACT,OAAO,mEAAmE;MAC5E,KAAK,QAAQ;QACX,OAAO,uEAAuE;MAChF,KAAK,MAAM;QACT,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMC,WAAW,GAAIrE,IAAY,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAAO,IAAI;MACb,KAAK,aAAa;QAChB,OAAO,IAAI;MACb,KAAK,OAAO;QACV,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,IAAI,CAACL,IAAI,EAAE;IACT,oBACEd,OAAA;MAAKyF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1F,OAAA;QAAKyF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1F,OAAA;UAAIyF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5D9F,OAAA;UAAGyF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9F,OAAA;UACE+F,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItE,OAAO,EAAE;IACX,oBACExB,OAAA;MAAKyF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1F,OAAA;QAAKyF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1F,OAAA;UAAKyF,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtG9F,OAAA;UAAGyF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACxE,QAAQ,EAAE;IACb,oBACEtB,OAAA;MAAKyF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1F,OAAA;QAAKyF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1F,OAAA;UAAIyF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE9F,OAAA;UAAGyF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9F,OAAA;UACEgG,OAAO,EAAEA,CAAA,KAAMnF,QAAQ,CAAC,YAAY,CAAE;UACtC4E,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMG,WAAW,GAAG,EAAA3F,sBAAA,GAAAgB,QAAQ,CAACmB,YAAY,cAAAnC,sBAAA,uBAArBA,sBAAA,CAAuBoC,MAAM,MAAK,WAAW;EACjE,MAAMwD,YAAY,GAAG,EAAA3F,sBAAA,GAAAe,QAAQ,CAACmB,YAAY,cAAAlC,sBAAA,uBAArBA,sBAAA,CAAuBmC,MAAM,MAAK,aAAa;;EAEpE;EACA,oBACE1C,OAAA,CAACH,aAAa;IACZoB,KAAK,EAAEK,QAAQ,CAACL,KAAM;IACtBkF,QAAQ,EAAE,GAAG7E,QAAQ,CAAC8E,YAAY,MAAM9E,QAAQ,CAAC+E,kBAAkB,MAAM/E,QAAQ,CAACgF,MAAM,SAAU;IAClGC,OAAO,eACLvG,OAAA;MACEgG,OAAO,EAAEA,CAAA,KAAMnF,QAAQ,CAAC,YAAY,CAAE;MACtC4E,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAEvF1F,OAAA;QAAMyF,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,wBAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;IAAAJ,QAAA,eAED1F,OAAA;MAAKyF,SAAS,EAAC,WAAW;MAAAC,QAAA,eAE1B1F,OAAA;QAAKyF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhC1F,OAAA,CAACN,MAAM,CAAC8G,GAAG;UACTf,SAAS,EAAC,yDAAyD;UACnEgB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBAE9B1F,OAAA;YAAKyF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1F,OAAA;cAAKyF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1F,OAAA;gBAAMyF,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEF,WAAW,CAAClE,QAAQ,CAACgD,aAAa;cAAC;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5E9F,OAAA;gBAAA0F,QAAA,gBACE1F,OAAA;kBAAIyF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAC7DpE,QAAQ,CAACL;gBAAK;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACL9F,OAAA;kBAAGyF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GACpDpE,QAAQ,CAACuF,MAAM,CAAC5F,KAAK,EAAC,UAAG,EAACK,QAAQ,CAAC8E,YAAY;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9F,OAAA;cAAMyF,SAAS,EAAE,8CAA8CH,kBAAkB,CAAChE,QAAQ,CAACiE,UAAU,CAAC,EAAG;cAAAG,QAAA,EACtGpE,QAAQ,CAAC+E;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN9F,OAAA;YAAGyF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACjDpE,QAAQ,CAACwF;UAAW;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEJ9F,OAAA;YAAKyF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5D1F,OAAA;cAAKyF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC1F,OAAA;gBAAA0F,QAAA,gBAAM1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxE,QAAQ,CAACgF,MAAM;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACN9F,OAAA;cAAKyF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1F,OAAA;gBAAMyF,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/B9F,OAAA;gBAAA0F,QAAA,gBAAM1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtC,IAAI,CAACC,KAAK,CAACnC,QAAQ,CAACuC,UAAU,GAAG,EAAE,CAAC,EAAC,KAAG;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACN9F,OAAA;cAAKyF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC1F,OAAA;gBAAA0F,QAAA,gBAAM1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAC7BG,WAAW,GAAG,SAAS,GAAGC,YAAY,GAAG,UAAU,GAAG,SAAS;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL5D,QAAQ,KAAK,IAAI,IAAIgE,YAAY,iBAChClG,OAAA;YAAKyF,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClE1F,OAAA;cAAKyF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C1F,OAAA;gBAAMyF,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC9F,OAAA;gBAAMyF,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAC3CT,UAAU,CAAC/C,QAAQ;cAAC;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACL5D,QAAQ,IAAI,EAAE,iBACblC,OAAA;cAAGyF,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAGb9F,OAAA,CAACN,MAAM,CAAC8G,GAAG;UACTf,SAAS,EAAC,yDAAyD;UACnEgB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAtB,QAAA,gBAE3B1F,OAAA;YAAIyF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9F,OAAA;YAAKyF,SAAS,EAAC,iDAAiD;YAAAC,QAAA,eAC9D1F,OAAA;cAAGyF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAEpE,QAAQ,CAAC2F;YAAQ;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EAEL,CAACG,WAAW,IAAI,CAACC,YAAY,iBAC5BlG,OAAA;YAAKyF,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B1F,OAAA;cACEgG,OAAO,EAAEhC,WAAY;cACrBkD,QAAQ,EAAExF,QAAS;cACnB+D,SAAS,EAAC,2HAA2H;cAAAC,QAAA,EAEpIhE,QAAQ,gBACP1B,OAAA,CAAAE,SAAA;gBAAAwF,QAAA,gBACE1F,OAAA;kBAAKyF,SAAS,EAAC;gBAA6E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,mBAErG;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAI,YAAY,iBACXlG,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAIyF,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E9F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpE,QAAQ,CAAC6F,OAAO,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAClCtH,OAAA,CAACN,MAAM,CAAC6H,KAAK;gBAEX9B,SAAS,EAAE,wFACTzD,cAAc,KAAKqF,MAAM,CAACzG,EAAE,GACxB,yDAAyD,GACzD,uGAAuG,EAC1G;gBACH6F,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCZ,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAE,CAAE;gBAC9BT,UAAU,EAAE;kBAAEC,KAAK,EAAEM,KAAK,GAAG;gBAAI,CAAE;gBACnCG,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhC,QAAA,gBAE1B1F,OAAA;kBACEmB,IAAI,EAAC,OAAO;kBACZyG,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAER,MAAM,CAACzG,EAAG;kBACjBkH,OAAO,EAAE9F,cAAc,KAAKqF,MAAM,CAACzG,EAAG;kBACtCmH,QAAQ,EAAEA,CAAA,KAAM9F,iBAAiB,CAACoF,MAAM,CAACzG,EAAE,CAAE;kBAC7C6E,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACF9F,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1F,OAAA;oBAAMyF,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,EAC5HsC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGX,KAAK;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACP9F,OAAA;oBAAMyF,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAE2B,MAAM,CAACa;kBAAW;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA,GAzBDuB,MAAM,CAACzG,EAAE;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BF,CACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9F,OAAA;cAAKyF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B1F,OAAA,CAACN,MAAM,CAACyI,MAAM;gBACZnC,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAC,CAAE;gBAC9BqE,QAAQ,EAAEtF,UAAU,IAAI,CAACI,cAAe;gBACxCyD,SAAS,EAAE,iEACTzD,cAAc,IAAI,CAACJ,UAAU,GACzB,qJAAqJ,GACrJ,kFAAkF,EACrF;gBACH6F,UAAU,EAAEzF,cAAc,IAAI,CAACJ,UAAU,GAAG;kBAAE8F,KAAK,EAAE;gBAAK,CAAC,GAAG,CAAC,CAAE;gBACjEC,QAAQ,EAAE3F,cAAc,IAAI,CAACJ,UAAU,GAAG;kBAAE8F,KAAK,EAAE;gBAAK,CAAC,GAAG,CAAC,CAAE;gBAAAhC,QAAA,EAE9D9D,UAAU,gBACT5B,OAAA,CAAAE,SAAA;kBAAAwF,QAAA,gBACE1F,OAAA;oBAAKyF,SAAS,EAAC;kBAA6E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,0BAErG;gBAAA,eAAE,CAAC,GACD9D,cAAc,gBAChBhC,OAAA,CAAAE,SAAA;kBAAAwF,QAAA,EAAE;gBAEF,gBAAE,CAAC,gBAEH1F,OAAA,CAAAE,SAAA;kBAAAwF,QAAA,gBACE1F,OAAA;oBAAMyF,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,kCAElC;gBAAA,eAAE;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC,EAEf9D,cAAc,IAAI,CAACJ,UAAU,iBAC5B5B,OAAA,CAACN,MAAM,CAAC0I,CAAC;gBACP3C,SAAS,EAAC,+CAA+C;gBACzDgB,OAAO,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAE;gBACxBE,OAAO,EAAE;kBAAEF,OAAO,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,GACzB,kCACwB,eAAA1F,OAAA;kBAAA0F,QAAA,EAASsC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG3G,QAAQ,CAAC6F,OAAO,CAACkB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1H,EAAE,KAAKoB,cAAc,CAAC;gBAAC;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAG,WAAW,iBACVjG,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAIyF,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D9F,OAAA;cAAKyF,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD1F,OAAA;gBAAKyF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzD1F,OAAA;kBAAKyF,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC1F,OAAA;oBAAMyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC5B,CAAAlF,sBAAA,GAAAc,QAAQ,CAACmB,YAAY,cAAAjC,sBAAA,eAArBA,sBAAA,CAAuBmE,UAAU,GAAG,GAAG,GAAG;kBAAG;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACP9F,OAAA;oBAAMyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC5B,CAAAjF,sBAAA,GAAAa,QAAQ,CAACmB,YAAY,cAAAhC,sBAAA,eAArBA,sBAAA,CAAuBkE,UAAU,GAAG,SAAS,GAAG;kBAAW;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN9F,OAAA;kBAAGyF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,mBACrC,EAAC,EAAAhF,sBAAA,GAAAY,QAAQ,CAACmB,YAAY,cAAA/B,sBAAA,uBAArBA,sBAAA,CAAuBkE,aAAa,KAAI,CAAC,EAAC,KAAG,EAACtD,QAAQ,CAACgF,MAAM;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN9F,OAAA;gBAAKyF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzD1F,OAAA;kBAAKyF,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC1F,OAAA;oBAAMyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxC9F,OAAA;oBAAMyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACN9F,OAAA;kBAAGyF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACpDT,UAAU,CAAC,EAAAtE,sBAAA,GAAAW,QAAQ,CAACmB,YAAY,cAAA9B,sBAAA,uBAArBA,sBAAA,CAAuB+D,UAAU,KAAI,CAAC;gBAAC;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELxE,QAAQ,CAACuD,cAAc,iBACtB7E,OAAA;cAAKyF,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAC9D1F,OAAA;gBAAIyF,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAGyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEpE,QAAQ,CAACuD;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAEAxE,QAAQ,CAACwD,WAAW,iBACnB9E,OAAA;cAAKyF,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAC3D1F,OAAA;gBAAIyF,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAGyF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEpE,QAAQ,CAACwD;cAAW;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAAC1F,EAAA,CAneID,kBAA4B;EAAA,QACjBX,SAAS,EACPC,WAAW,EACXE,OAAO,EACIC,gBAAgB;AAAA;AAAA2I,EAAA,GAJxCpI,kBAA4B;AAqelC,eAAeA,kBAAkB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}