{"ast": null, "code": "export var addDocs = {\n  name: 'add',\n  category: 'Operators',\n  syntax: ['x + y', 'add(x, y)'],\n  description: 'Add two values.',\n  examples: ['a = 2.1 + 3.6', 'a - 3.6', '3 + 2i', '3 cm + 2 inch', '\"2.3\" + \"4\"'],\n  seealso: ['subtract']\n};", "map": {"version": 3, "names": ["addDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/add.js"], "sourcesContent": ["export var addDocs = {\n  name: 'add',\n  category: 'Operators',\n  syntax: ['x + y', 'add(x, y)'],\n  description: 'Add two values.',\n  examples: ['a = 2.1 + 3.6', 'a - 3.6', '3 + 2i', '3 cm + 2 inch', '\"2.3\" + \"4\"'],\n  seealso: ['subtract']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;EAC9BC,WAAW,EAAE,iBAAiB;EAC9BC,QAAQ,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,CAAC;EAChFC,OAAO,EAAE,CAAC,UAAU;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}