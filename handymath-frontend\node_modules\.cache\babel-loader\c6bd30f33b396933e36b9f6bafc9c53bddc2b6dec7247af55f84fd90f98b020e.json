{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const {\n    addNotification\n  } = useNotifications();\n  const [settings, setSettings] = useState({\n    notifications: {\n      email: true,\n      push: false,\n      exercise_reminders: true,\n      progress_updates: true\n    },\n    preferences: {\n      language: 'fr',\n      timezone: 'Europe/Paris',\n      difficulty_level: 'intermediate',\n      auto_save: true\n    },\n    privacy: {\n      profile_visibility: 'public',\n      show_progress: true,\n      show_statistics: true\n    }\n  });\n  const [loading, setLoading] = useState(false);\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // Simuler une sauvegarde\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      addNotification({\n        type: 'success',\n        title: 'Paramètres sauvegardés',\n        message: 'Vos préférences ont été mises à jour'\n      });\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder les paramètres'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateSetting = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n        title: \"Param\\xE8tres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold mb-4\",\n            children: \"Acc\\xE8s refus\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux param\\xE8tres.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"Param\\xE8tres\",\n      showBackButton: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\uD83C\\uDFA8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), \"Apparence\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: \"Th\\xE8me\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 dark:text-gray-400\",\n                  children: \"Choisir entre le mode clair et sombre\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: toggleTheme,\n                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${theme === 'dark' ? 'bg-primary-600' : 'bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${theme === 'dark' ? 'translate-x-6' : 'translate-x-1'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\uD83D\\uDD14\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"Notifications\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: Object.entries(settings.notifications).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: [key === 'email' && 'Notifications par email', key === 'push' && 'Notifications push', key === 'exercise_reminders' && 'Rappels d\\'exercices', key === 'progress_updates' && 'Mises à jour de progression']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateSetting('notifications', key, !value),\n                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${value ? 'bg-primary-600' : 'bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${value ? 'translate-x-6' : 'translate-x-1'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), \"Pr\\xE9f\\xE9rences\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Langue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: settings.preferences.language,\n                onChange: e => updateSetting('preferences', 'language', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fr\",\n                  children: \"Fran\\xE7ais\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"en\",\n                  children: \"English\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"es\",\n                  children: \"Espa\\xF1ol\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Niveau de difficult\\xE9 par d\\xE9faut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: settings.preferences.difficulty_level,\n                onChange: e => updateSetting('preferences', 'difficulty_level', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"beginner\",\n                  children: \"D\\xE9butant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"intermediate\",\n                  children: \"Interm\\xE9diaire\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"advanced\",\n                  children: \"Avanc\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: \"Sauvegarde automatique\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 dark:text-gray-400\",\n                  children: \"Sauvegarder automatiquement votre progression\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateSetting('preferences', 'auto_save', !settings.preferences.auto_save),\n                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.preferences.auto_save ? 'bg-primary-600' : 'bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.preferences.auto_save ? 'translate-x-6' : 'translate-x-1'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), \"Confidentialit\\xE9\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Visibilit\\xE9 du profil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: settings.privacy.profile_visibility,\n                onChange: e => updateSetting('privacy', 'profile_visibility', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"public\",\n                  children: \"Public\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"friends\",\n                  children: \"Amis seulement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"private\",\n                  children: \"Priv\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: \"Afficher ma progression\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => updateSetting('privacy', 'show_progress', !settings.privacy.show_progress),\n                  className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.privacy.show_progress ? 'bg-primary-600' : 'bg-gray-200'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.privacy.show_progress ? 'translate-x-6' : 'translate-x-1'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: \"Afficher mes statistiques\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => updateSetting('privacy', 'show_statistics', !settings.privacy.show_statistics),\n                  className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.privacy.show_statistics ? 'bg-primary-600' : 'bg-gray-200'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.privacy.show_statistics ? 'translate-x-6' : 'translate-x-1'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"flex justify-end\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            delay: 0.4\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            disabled: loading,\n            className: \"px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center\",\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), \"Sauvegarde...\"]\n            }, void 0, true) : 'Sauvegarder les paramètres'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SettingsPage, \"XL9v8Nzk1aR8fIvsxYuOBhK8Xo0=\", false, function () {\n  return [useAuth, useTheme, useNotifications];\n});\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "useAuth", "useTheme", "useNotifications", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsPage", "_s", "user", "theme", "toggleTheme", "addNotification", "settings", "setSettings", "notifications", "email", "push", "exercise_reminders", "progress_updates", "preferences", "language", "timezone", "difficulty_level", "auto_save", "privacy", "profile_visibility", "show_progress", "show_statistics", "loading", "setLoading", "handleSave", "Promise", "resolve", "setTimeout", "type", "title", "message", "error", "updateSetting", "category", "key", "value", "prev", "children", "SimpleHeader", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "showBackButton", "div", "initial", "opacity", "y", "animate", "onClick", "transition", "delay", "Object", "entries", "map", "onChange", "e", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/SettingsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\n\ninterface Settings {\n  notifications: {\n    email: boolean;\n    push: boolean;\n    exercise_reminders: boolean;\n    progress_updates: boolean;\n  };\n  preferences: {\n    language: string;\n    timezone: string;\n    difficulty_level: string;\n    auto_save: boolean;\n  };\n  privacy: {\n    profile_visibility: string;\n    show_progress: boolean;\n    show_statistics: boolean;\n  };\n}\n\nconst SettingsPage: React.FC = () => {\n  const { user } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const { addNotification } = useNotifications();\n\n  const [settings, setSettings] = useState<Settings>({\n    notifications: {\n      email: true,\n      push: false,\n      exercise_reminders: true,\n      progress_updates: true\n    },\n    preferences: {\n      language: 'fr',\n      timezone: 'Europe/Paris',\n      difficulty_level: 'intermediate',\n      auto_save: true\n    },\n    privacy: {\n      profile_visibility: 'public',\n      show_progress: true,\n      show_statistics: true\n    }\n  });\n\n  const [loading, setLoading] = useState(false);\n\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // Simuler une sauvegarde\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      addNotification({\n        type: 'success',\n        title: 'Paramètres sauvegardés',\n        message: 'Vos préférences ont été mises à jour'\n      });\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder les paramètres'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateSetting = (category: keyof Settings, key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n\n  if (!user) {\n    return (\n      <>\n        <SimpleHeader title=\"Paramètres\" />\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold mb-4\">Accès refusé</h1>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Vous devez être connecté pour accéder aux paramètres.\n            </p>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <SimpleHeader title=\"Paramètres\" showBackButton />\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-4xl mx-auto space-y-8\">\n          \n          {/* Apparence */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">🎨</span>\n              Apparence\n            </h2>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Thème\n                  </label>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Choisir entre le mode clair et sombre\n                  </p>\n                </div>\n                <button\n                  onClick={toggleTheme}\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                    theme === 'dark' ? 'bg-primary-600' : 'bg-gray-200'\n                  }`}\n                >\n                  <span\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                      theme === 'dark' ? 'translate-x-6' : 'translate-x-1'\n                    }`}\n                  />\n                </button>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Notifications */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.1 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">🔔</span>\n              Notifications\n            </h2>\n            \n            <div className=\"space-y-4\">\n              {Object.entries(settings.notifications).map(([key, value]) => (\n                <div key={key} className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      {key === 'email' && 'Notifications par email'}\n                      {key === 'push' && 'Notifications push'}\n                      {key === 'exercise_reminders' && 'Rappels d\\'exercices'}\n                      {key === 'progress_updates' && 'Mises à jour de progression'}\n                    </label>\n                  </div>\n                  <button\n                    onClick={() => updateSetting('notifications', key, !value)}\n                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                      value ? 'bg-primary-600' : 'bg-gray-200'\n                    }`}\n                  >\n                    <span\n                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                        value ? 'translate-x-6' : 'translate-x-1'\n                      }`}\n                    />\n                  </button>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Préférences */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">⚙️</span>\n              Préférences\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Langue\n                </label>\n                <select\n                  value={settings.preferences.language}\n                  onChange={(e) => updateSetting('preferences', 'language', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"fr\">Français</option>\n                  <option value=\"en\">English</option>\n                  <option value=\"es\">Español</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Niveau de difficulté par défaut\n                </label>\n                <select\n                  value={settings.preferences.difficulty_level}\n                  onChange={(e) => updateSetting('preferences', 'difficulty_level', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"beginner\">Débutant</option>\n                  <option value=\"intermediate\">Intermédiaire</option>\n                  <option value=\"advanced\">Avancé</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Sauvegarde automatique\n                  </label>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Sauvegarder automatiquement votre progression\n                  </p>\n                </div>\n                <button\n                  onClick={() => updateSetting('preferences', 'auto_save', !settings.preferences.auto_save)}\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                    settings.preferences.auto_save ? 'bg-primary-600' : 'bg-gray-200'\n                  }`}\n                >\n                  <span\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                      settings.preferences.auto_save ? 'translate-x-6' : 'translate-x-1'\n                    }`}\n                  />\n                </button>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Confidentialité */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">🔒</span>\n              Confidentialité\n            </h2>\n            \n            <div className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Visibilité du profil\n                </label>\n                <select\n                  value={settings.privacy.profile_visibility}\n                  onChange={(e) => updateSetting('privacy', 'profile_visibility', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"public\">Public</option>\n                  <option value=\"friends\">Amis seulement</option>\n                  <option value=\"private\">Privé</option>\n                </select>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Afficher ma progression\n                  </label>\n                  <button\n                    onClick={() => updateSetting('privacy', 'show_progress', !settings.privacy.show_progress)}\n                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                      settings.privacy.show_progress ? 'bg-primary-600' : 'bg-gray-200'\n                    }`}\n                  >\n                    <span\n                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                        settings.privacy.show_progress ? 'translate-x-6' : 'translate-x-1'\n                      }`}\n                    />\n                  </button>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Afficher mes statistiques\n                  </label>\n                  <button\n                    onClick={() => updateSetting('privacy', 'show_statistics', !settings.privacy.show_statistics)}\n                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                      settings.privacy.show_statistics ? 'bg-primary-600' : 'bg-gray-200'\n                    }`}\n                  >\n                    <span\n                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                        settings.privacy.show_statistics ? 'translate-x-6' : 'translate-x-1'\n                      }`}\n                    />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Bouton de sauvegarde */}\n          <motion.div\n            className=\"flex justify-end\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.4 }}\n          >\n            <button\n              onClick={handleSave}\n              disabled={loading}\n              className=\"px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center\"\n            >\n              {loading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Sauvegarde...\n                </>\n              ) : (\n                'Sauvegarder les paramètres'\n              )}\n            </button>\n          </motion.div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,gBAAgB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAuBpE,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEU,KAAK;IAAEC;EAAY,CAAC,GAAGV,QAAQ,CAAC,CAAC;EACzC,MAAM;IAAEW;EAAgB,CAAC,GAAGV,gBAAgB,CAAC,CAAC;EAE9C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAW;IACjDiB,aAAa,EAAE;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,KAAK;MACXC,kBAAkB,EAAE,IAAI;MACxBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,WAAW,EAAE;MACXC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,cAAc;MACxBC,gBAAgB,EAAE,cAAc;MAChCC,SAAS,EAAE;IACb,CAAC;IACDC,OAAO,EAAE;MACPC,kBAAkB,EAAE,QAAQ;MAC5BC,aAAa,EAAE,IAAI;MACnBC,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMiC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDrB,eAAe,CAAC;QACduB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1B,eAAe,CAAC;QACduB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,aAAa,GAAGA,CAACC,QAAwB,EAAEC,GAAW,EAAEC,KAAU,KAAK;IAC3E5B,WAAW,CAAC6B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,QAAQ,GAAG;QACV,GAAGG,IAAI,CAACH,QAAQ,CAAC;QACjB,CAACC,GAAG,GAAGC;MACT;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAACjC,IAAI,EAAE;IACT,oBACEL,OAAA,CAAAE,SAAA;MAAAsC,QAAA,gBACExC,OAAA,CAACyC,YAAY;QAACT,KAAK,EAAC;MAAY;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnC7C,OAAA;QAAK8C,SAAS,EAAC,6BAA6B;QAAAN,QAAA,eAC1CxC,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAN,QAAA,gBAC1BxC,OAAA;YAAI8C,SAAS,EAAC,yBAAyB;YAAAN,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzD7C,OAAA;YAAG8C,SAAS,EAAC,kCAAkC;YAAAN,QAAA,EAAC;UAEhD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,oBACE7C,OAAA,CAAAE,SAAA;IAAAsC,QAAA,gBACExC,OAAA,CAACyC,YAAY;MAACT,KAAK,EAAC,eAAY;MAACe,cAAc;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClD7C,OAAA;MAAK8C,SAAS,EAAC,6BAA6B;MAAAN,QAAA,eAC1CxC,OAAA;QAAK8C,SAAS,EAAC,6BAA6B;QAAAN,QAAA,gBAG1CxC,OAAA,CAACL,MAAM,CAACqD,GAAG;UACTF,SAAS,EAAC,oDAAoD;UAC9DG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAE9BxC,OAAA;YAAI8C,SAAS,EAAC,wEAAwE;YAAAN,QAAA,gBACpFxC,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAN,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,aAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAN,QAAA,eACxBxC,OAAA;cAAK8C,SAAS,EAAC,mCAAmC;cAAAN,QAAA,gBAChDxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAO8C,SAAS,EAAC,sDAAsD;kBAAAN,QAAA,EAAC;gBAExE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBAAG8C,SAAS,EAAC,0CAA0C;kBAAAN,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN7C,OAAA;gBACEqD,OAAO,EAAE9C,WAAY;gBACrBuC,SAAS,EAAE,6EACTxC,KAAK,KAAK,MAAM,GAAG,gBAAgB,GAAG,aAAa,EAClD;gBAAAkC,QAAA,eAEHxC,OAAA;kBACE8C,SAAS,EAAE,6EACTxC,KAAK,KAAK,MAAM,GAAG,eAAe,GAAG,eAAe;gBACnD;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb7C,OAAA,CAACL,MAAM,CAACqD,GAAG;UACTF,SAAS,EAAC,oDAAoD;UAC9DG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAf,QAAA,gBAE3BxC,OAAA;YAAI8C,SAAS,EAAC,wEAAwE;YAAAN,QAAA,gBACpFxC,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAN,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAN,QAAA,EACvBgB,MAAM,CAACC,OAAO,CAAChD,QAAQ,CAACE,aAAa,CAAC,CAAC+C,GAAG,CAAC,CAAC,CAACrB,GAAG,EAAEC,KAAK,CAAC,kBACvDtC,OAAA;cAAe8C,SAAS,EAAC,mCAAmC;cAAAN,QAAA,gBAC1DxC,OAAA;gBAAAwC,QAAA,eACExC,OAAA;kBAAO8C,SAAS,EAAC,sDAAsD;kBAAAN,QAAA,GACpEH,GAAG,KAAK,OAAO,IAAI,yBAAyB,EAC5CA,GAAG,KAAK,MAAM,IAAI,oBAAoB,EACtCA,GAAG,KAAK,oBAAoB,IAAI,sBAAsB,EACtDA,GAAG,KAAK,kBAAkB,IAAI,6BAA6B;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN7C,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,eAAe,EAAEE,GAAG,EAAE,CAACC,KAAK,CAAE;gBAC3DQ,SAAS,EAAE,6EACTR,KAAK,GAAG,gBAAgB,GAAG,aAAa,EACvC;gBAAAE,QAAA,eAEHxC,OAAA;kBACE8C,SAAS,EAAE,6EACTR,KAAK,GAAG,eAAe,GAAG,eAAe;gBACxC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA,GApBDR,GAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBR,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb7C,OAAA,CAACL,MAAM,CAACqD,GAAG;UACTF,SAAS,EAAC,oDAAoD;UAC9DG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAf,QAAA,gBAE3BxC,OAAA;YAAI8C,SAAS,EAAC,wEAAwE;YAAAN,QAAA,gBACpFxC,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAN,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,qBAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL7C,OAAA;YAAK8C,SAAS,EAAC,uCAAuC;YAAAN,QAAA,gBACpDxC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAO8C,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEsC,KAAK,EAAE7B,QAAQ,CAACO,WAAW,CAACC,QAAS;gBACrC0C,QAAQ,EAAGC,CAAC,IAAKzB,aAAa,CAAC,aAAa,EAAE,UAAU,EAAEyB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;gBAC1EQ,SAAS,EAAC,iIAAiI;gBAAAN,QAAA,gBAE3IxC,OAAA;kBAAQsC,KAAK,EAAC,IAAI;kBAAAE,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC7C,OAAA;kBAAQsC,KAAK,EAAC,IAAI;kBAAAE,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC7C,OAAA;kBAAQsC,KAAK,EAAC,IAAI;kBAAAE,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7C,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAO8C,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEsC,KAAK,EAAE7B,QAAQ,CAACO,WAAW,CAACG,gBAAiB;gBAC7CwC,QAAQ,EAAGC,CAAC,IAAKzB,aAAa,CAAC,aAAa,EAAE,kBAAkB,EAAEyB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;gBAClFQ,SAAS,EAAC,iIAAiI;gBAAAN,QAAA,gBAE3IxC,OAAA;kBAAQsC,KAAK,EAAC,UAAU;kBAAAE,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C7C,OAAA;kBAAQsC,KAAK,EAAC,cAAc;kBAAAE,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnD7C,OAAA;kBAAQsC,KAAK,EAAC,UAAU;kBAAAE,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7C,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAN,QAAA,eACnBxC,OAAA;cAAK8C,SAAS,EAAC,mCAAmC;cAAAN,QAAA,gBAChDxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAO8C,SAAS,EAAC,sDAAsD;kBAAAN,QAAA,EAAC;gBAExE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBAAG8C,SAAS,EAAC,0CAA0C;kBAAAN,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN7C,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC1B,QAAQ,CAACO,WAAW,CAACI,SAAS,CAAE;gBAC1F0B,SAAS,EAAE,6EACTrC,QAAQ,CAACO,WAAW,CAACI,SAAS,GAAG,gBAAgB,GAAG,aAAa,EAChE;gBAAAoB,QAAA,eAEHxC,OAAA;kBACE8C,SAAS,EAAE,6EACTrC,QAAQ,CAACO,WAAW,CAACI,SAAS,GAAG,eAAe,GAAG,eAAe;gBACjE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb7C,OAAA,CAACL,MAAM,CAACqD,GAAG;UACTF,SAAS,EAAC,oDAAoD;UAC9DG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAf,QAAA,gBAE3BxC,OAAA;YAAI8C,SAAS,EAAC,wEAAwE;YAAAN,QAAA,gBACpFxC,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAN,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,sBAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAN,QAAA,gBACxBxC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAO8C,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEsC,KAAK,EAAE7B,QAAQ,CAACY,OAAO,CAACC,kBAAmB;gBAC3CqC,QAAQ,EAAGC,CAAC,IAAKzB,aAAa,CAAC,SAAS,EAAE,oBAAoB,EAAEyB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;gBAChFQ,SAAS,EAAC,iIAAiI;gBAAAN,QAAA,gBAE3IxC,OAAA;kBAAQsC,KAAK,EAAC,QAAQ;kBAAAE,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC7C,OAAA;kBAAQsC,KAAK,EAAC,SAAS;kBAAAE,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C7C,OAAA;kBAAQsC,KAAK,EAAC,SAAS;kBAAAE,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7C,OAAA;cAAK8C,SAAS,EAAC,WAAW;cAAAN,QAAA,gBACxBxC,OAAA;gBAAK8C,SAAS,EAAC,mCAAmC;gBAAAN,QAAA,gBAChDxC,OAAA;kBAAO8C,SAAS,EAAC,sDAAsD;kBAAAN,QAAA,EAAC;gBAExE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACEqD,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC1B,QAAQ,CAACY,OAAO,CAACE,aAAa,CAAE;kBAC1FuB,SAAS,EAAE,6EACTrC,QAAQ,CAACY,OAAO,CAACE,aAAa,GAAG,gBAAgB,GAAG,aAAa,EAChE;kBAAAiB,QAAA,eAEHxC,OAAA;oBACE8C,SAAS,EAAE,6EACTrC,QAAQ,CAACY,OAAO,CAACE,aAAa,GAAG,eAAe,GAAG,eAAe;kBACjE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN7C,OAAA;gBAAK8C,SAAS,EAAC,mCAAmC;gBAAAN,QAAA,gBAChDxC,OAAA;kBAAO8C,SAAS,EAAC,sDAAsD;kBAAAN,QAAA,EAAC;gBAExE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACEqD,OAAO,EAAEA,CAAA,KAAMlB,aAAa,CAAC,SAAS,EAAE,iBAAiB,EAAE,CAAC1B,QAAQ,CAACY,OAAO,CAACG,eAAe,CAAE;kBAC9FsB,SAAS,EAAE,6EACTrC,QAAQ,CAACY,OAAO,CAACG,eAAe,GAAG,gBAAgB,GAAG,aAAa,EAClE;kBAAAgB,QAAA,eAEHxC,OAAA;oBACE8C,SAAS,EAAE,6EACTrC,QAAQ,CAACY,OAAO,CAACG,eAAe,GAAG,eAAe,GAAG,eAAe;kBACnE;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb7C,OAAA,CAACL,MAAM,CAACqD,GAAG;UACTF,SAAS,EAAC,kBAAkB;UAC5BG,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAf,QAAA,eAE3BxC,OAAA;YACEqD,OAAO,EAAE1B,UAAW;YACpBmC,QAAQ,EAAErC,OAAQ;YAClBqB,SAAS,EAAC,0IAA0I;YAAAN,QAAA,EAEnJf,OAAO,gBACNzB,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA;gBAAK8C,SAAS,EAAC;cAAgE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAExF;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACzC,EAAA,CAlUID,YAAsB;EAAA,QACTP,OAAO,EACOC,QAAQ,EACXC,gBAAgB;AAAA;AAAAiE,EAAA,GAHxC5D,YAAsB;AAoU5B,eAAeA,YAAY;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}