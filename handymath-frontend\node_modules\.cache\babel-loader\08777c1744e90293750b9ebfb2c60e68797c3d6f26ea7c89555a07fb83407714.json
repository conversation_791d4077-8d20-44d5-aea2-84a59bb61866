{"ast": null, "code": "export var versionDocs = {\n  name: 'version',\n  category: 'Constants',\n  syntax: ['version'],\n  description: 'A string with the version number of math.js',\n  examples: ['version'],\n  seealso: []\n};", "map": {"version": 3, "names": ["versionDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/version.js"], "sourcesContent": ["export var versionDocs = {\n  name: 'version',\n  category: 'Constants',\n  syntax: ['version'],\n  description: 'A string with the version number of math.js',\n  examples: ['version'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE,CAAC,SAAS,CAAC;EACrBC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}