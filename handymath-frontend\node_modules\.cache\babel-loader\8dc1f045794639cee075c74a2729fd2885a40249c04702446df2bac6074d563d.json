{"ast": null, "code": "export var cotDocs = {\n  name: 'cot',\n  category: 'Trigonometry',\n  syntax: ['cot(x)'],\n  description: 'Compute the cotangent of x in radians. Defined as 1/tan(x)',\n  examples: ['cot(2)', '1 / tan(2)'],\n  seealso: ['sec', 'csc', 'tan']\n};", "map": {"version": 3, "names": ["cotDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/cot.js"], "sourcesContent": ["export var cotDocs = {\n  name: 'cot',\n  category: 'Trigonometry',\n  syntax: ['cot(x)'],\n  description: 'Compute the cotangent of x in radians. Defined as 1/tan(x)',\n  examples: ['cot(2)', '1 / tan(2)'],\n  seealso: ['sec', 'csc', 'tan']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EAClCC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}