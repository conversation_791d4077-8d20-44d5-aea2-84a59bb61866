{"ast": null, "code": "export var onesDocs = {\n  name: 'ones',\n  category: 'Matrix',\n  syntax: ['ones(m)', 'ones(m, n)', 'ones(m, n, p, ...)', 'ones([m])', 'ones([m, n])', 'ones([m, n, p, ...])'],\n  description: 'Create a matrix containing ones.',\n  examples: ['ones(3)', 'ones(3, 5)', 'ones([2,3]) * 4.5', 'a = [1, 2, 3; 4, 5, 6]', 'ones(size(a))'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["onesDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/ones.js"], "sourcesContent": ["export var onesDocs = {\n  name: 'ones',\n  category: 'Matrix',\n  syntax: ['ones(m)', 'ones(m, n)', 'ones(m, n, p, ...)', 'ones([m])', 'ones([m, n])', 'ones([m, n, p, ...])'],\n  description: 'Create a matrix containing ones.',\n  examples: ['ones(3)', 'ones(3, 5)', 'ones([2,3]) * 4.5', 'a = [1, 2, 3; 4, 5, 6]', 'ones(size(a))'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,oBAAoB,EAAE,WAAW,EAAE,cAAc,EAAE,sBAAsB,CAAC;EAC5GC,WAAW,EAAE,kCAAkC;EAC/CC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,eAAe,CAAC;EACnGC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AAC3H,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}