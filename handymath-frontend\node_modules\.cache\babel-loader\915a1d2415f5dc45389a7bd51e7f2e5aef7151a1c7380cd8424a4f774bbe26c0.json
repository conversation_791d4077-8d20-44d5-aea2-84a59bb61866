{"ast": null, "code": "export var quantileSeqDocs = {\n  name: 'quantileSeq',\n  category: 'Statistics',\n  syntax: ['quantileSeq(A, prob[, sorted])', 'quantileSeq(A, [prob1, prob2, ...][, sorted])', 'quantileSeq(A, N[, sorted])'],\n  description: 'Compute the prob order quantile of a matrix or a list with values. The sequence is sorted and the middle value is returned. Supported types of sequence values are: Number, BigNumber, Unit Supported types of probability are: Number, BigNumber. \\n\\nIn case of a (multi dimensional) array or matrix, the prob order quantile of all elements will be calculated.',\n  examples: ['quantileSeq([3, -1, 5, 7], 0.5)', 'quantileSeq([3, -1, 5, 7], [1/3, 2/3])', 'quantileSeq([3, -1, 5, 7], 2)', 'quantileSeq([-1, 3, 5, 7], 0.5, true)'],\n  seealso: ['mean', 'median', 'min', 'max', 'prod', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["quantileSeqDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/quantileSeq.js"], "sourcesContent": ["export var quantileSeqDocs = {\n  name: 'quantileSeq',\n  category: 'Statistics',\n  syntax: ['quantileSeq(A, prob[, sorted])', 'quantileSeq(A, [prob1, prob2, ...][, sorted])', 'quantileSeq(A, N[, sorted])'],\n  description: 'Compute the prob order quantile of a matrix or a list with values. The sequence is sorted and the middle value is returned. Supported types of sequence values are: Number, BigNumber, Unit Supported types of probability are: Number, BigNumber. \\n\\nIn case of a (multi dimensional) array or matrix, the prob order quantile of all elements will be calculated.',\n  examples: ['quantileSeq([3, -1, 5, 7], 0.5)', 'quantileSeq([3, -1, 5, 7], [1/3, 2/3])', 'quantileSeq([3, -1, 5, 7], 2)', 'quantileSeq([-1, 3, 5, 7], 0.5, true)'],\n  seealso: ['mean', 'median', 'min', 'max', 'prod', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,gCAAgC,EAAE,+CAA+C,EAAE,6BAA6B,CAAC;EAC1HC,WAAW,EAAE,sWAAsW;EACnXC,QAAQ,EAAE,CAAC,iCAAiC,EAAE,wCAAwC,EAAE,+BAA+B,EAAE,uCAAuC,CAAC;EACjKC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}