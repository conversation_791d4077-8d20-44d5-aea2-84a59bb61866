{"ast": null, "code": "export var dotDocs = {\n  name: 'dot',\n  category: 'Matrix',\n  syntax: ['dot(A, B)', 'A * B'],\n  description: 'Calculate the dot product of two vectors. ' + 'The dot product of A = [a1, a2, a3, ..., an] and B = [b1, b2, b3, ..., bn] ' + 'is defined as dot(A, B) = a1 * b1 + a2 * b2 + a3 * b3 + ... + an * bn',\n  examples: ['dot([2, 4, 1], [2, 2, 3])', '[2, 4, 1] * [2, 2, 3]'],\n  seealso: ['multiply', 'cross']\n};", "map": {"version": 3, "names": ["dotDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/dot.js"], "sourcesContent": ["export var dotDocs = {\n  name: 'dot',\n  category: 'Matrix',\n  syntax: ['dot(A, B)', 'A * B'],\n  description: 'Calculate the dot product of two vectors. ' + 'The dot product of A = [a1, a2, a3, ..., an] and B = [b1, b2, b3, ..., bn] ' + 'is defined as dot(A, B) = a1 * b1 + a2 * b2 + a3 * b3 + ... + an * bn',\n  examples: ['dot([2, 4, 1], [2, 2, 3])', '[2, 4, 1] * [2, 2, 3]'],\n  seealso: ['multiply', 'cross']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC;EAC9BC,WAAW,EAAE,4CAA4C,GAAG,6EAA6E,GAAG,uEAAuE;EACnNC,QAAQ,EAAE,CAAC,2BAA2B,EAAE,uBAAuB,CAAC;EAChEC,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}