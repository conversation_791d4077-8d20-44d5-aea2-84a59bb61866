{"ast": null, "code": "export var hexDocs = {\n  name: 'hex',\n  category: 'Utils',\n  syntax: ['hex(value)'],\n  description: 'Format a number as hexadecimal',\n  examples: ['hex(240)'],\n  seealso: ['bin', 'oct']\n};", "map": {"version": 3, "names": ["hexDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/hex.js"], "sourcesContent": ["export var hexDocs = {\n  name: 'hex',\n  category: 'Utils',\n  syntax: ['hex(value)'],\n  description: 'Format a number as hexadecimal',\n  examples: ['hex(240)'],\n  seealso: ['bin', 'oct']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,gCAAgC;EAC7CC,QAAQ,EAAE,CAAC,UAAU,CAAC;EACtBC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}