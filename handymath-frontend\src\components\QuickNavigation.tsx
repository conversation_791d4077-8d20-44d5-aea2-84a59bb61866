import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

interface QuickNavItem {
  id: string;
  label: string;
  path: string;
  icon: string;
  badge?: string | number;
  isActive?: boolean;
}

interface QuickNavigationProps {
  items: QuickNavItem[];
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  showLabels?: boolean;
}

const QuickNavigation: React.FC<QuickNavigationProps> = ({
  items,
  className = '',
  orientation = 'horizontal',
  showLabels = true
}) => {
  const location = useLocation();
  const [activeItem, setActiveItem] = useState<string>('');

  useEffect(() => {
    // Déterminer l'élément actif basé sur l'URL actuelle
    const currentItem = items.find(item => 
      location.pathname === item.path || 
      (item.path !== '/' && location.pathname.startsWith(item.path))
    );
    setActiveItem(currentItem?.id || '');
  }, [location.pathname, items]);

  const isHorizontal = orientation === 'horizontal';

  return (
    <nav className={`quick-navigation ${className}`}>
      <div className={`flex ${isHorizontal ? 'flex-row space-x-2' : 'flex-col space-y-2'}`}>
        {items.map((item) => {
          const isActive = activeItem === item.id || item.isActive;
          
          return (
            <Link
              key={item.id}
              to={item.path}
              className={`group relative flex items-center ${
                isHorizontal ? 'justify-center' : 'justify-start'
              } px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'
              }`}
              title={item.label}
            >
              {/* Icône */}
              <span className={`text-lg ${showLabels && !isHorizontal ? 'mr-3' : ''}`}>
                {item.icon}
              </span>
              
              {/* Label */}
              {showLabels && (
                <span className={isHorizontal ? 'ml-2' : ''}>
                  {item.label}
                </span>
              )}
              
              {/* Badge */}
              {item.badge && (
                <span className={`absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center text-xs font-bold text-white bg-red-500 rounded-full ${
                  typeof item.badge === 'number' && item.badge > 99 ? 'px-1' : ''
                }`}>
                  {typeof item.badge === 'number' && item.badge > 99 ? '99+' : item.badge}
                </span>
              )}
              
              {/* Indicateur actif pour orientation horizontale */}
              {isActive && isHorizontal && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></div>
              )}
              
              {/* Indicateur actif pour orientation verticale */}
              {isActive && !isHorizontal && (
                <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-primary-600 rounded-r-full"></div>
              )}
              
              {/* Tooltip pour mode sans labels */}
              {!showLabels && (
                <div className="absolute z-10 px-2 py-1 text-xs text-white bg-gray-900 dark:bg-gray-700 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap left-full ml-2 top-1/2 transform -translate-y-1/2">
                  {item.label}
                  <div className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900 dark:border-r-gray-700"></div>
                </div>
              )}
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

export default QuickNavigation;
