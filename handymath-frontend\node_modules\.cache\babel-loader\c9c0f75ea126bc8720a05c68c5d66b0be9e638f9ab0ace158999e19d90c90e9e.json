{"ast": null, "code": "export var acschDocs = {\n  name: 'acsch',\n  category: 'Trigonometry',\n  syntax: ['acsch(x)'],\n  description: 'Calculate the inverse hyperbolic cosecant of a value, defined as `acsch(x) = ln(1/x + sqrt(1/x^2 + 1))`.',\n  examples: ['acsch(0.5)'],\n  seealso: ['asech', 'acoth']\n};", "map": {"version": 3, "names": ["acschDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/acsch.js"], "sourcesContent": ["export var acschDocs = {\n  name: 'acsch',\n  category: 'Trigonometry',\n  syntax: ['acsch(x)'],\n  description: 'Calculate the inverse hyperbolic cosecant of a value, defined as `acsch(x) = ln(1/x + sqrt(1/x^2 + 1))`.',\n  examples: ['acsch(0.5)'],\n  seealso: ['asech', 'acoth']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,0GAA0G;EACvHC,QAAQ,EAAE,CAAC,YAAY,CAAC;EACxBC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}