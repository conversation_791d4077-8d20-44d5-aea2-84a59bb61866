{"ast": null, "code": "import { nearlyEqual } from './number.js';\n\n/**\n * Test whether two complex values are equal provided a given relTol and absTol.\n * Does not use or change the global Complex.EPSILON setting\n * @param {Complex} x - The first complex number for comparison.\n * @param {Complex} y - The second complex number for comparison.\n * @param {number} relTol - The relative tolerance for comparison.\n * @param {number} absTol - The absolute tolerance for comparison.\n * @returns {boolean} - Returns true if the two complex numbers are equal within the given tolerances, otherwise returns false.\n */\nexport function complexEquals(x, y, relTol, absTol) {\n  return nearlyEqual(x.re, y.re, relTol, absTol) && nearlyEqual(x.im, y.im, relTol, absTol);\n}", "map": {"version": 3, "names": ["nearlyEqual", "complexEquals", "x", "y", "relTol", "absTol", "re", "im"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/complex.js"], "sourcesContent": ["import { nearlyEqual } from './number.js';\n\n/**\n * Test whether two complex values are equal provided a given relTol and absTol.\n * Does not use or change the global Complex.EPSILON setting\n * @param {Complex} x - The first complex number for comparison.\n * @param {Complex} y - The second complex number for comparison.\n * @param {number} relTol - The relative tolerance for comparison.\n * @param {number} absTol - The absolute tolerance for comparison.\n * @returns {boolean} - Returns true if the two complex numbers are equal within the given tolerances, otherwise returns false.\n */\nexport function complexEquals(x, y, relTol, absTol) {\n  return nearlyEqual(x.re, y.re, relTol, absTol) && nearlyEqual(x.im, y.im, relTol, absTol);\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAClD,OAAOL,WAAW,CAACE,CAAC,CAACI,EAAE,EAAEH,CAAC,CAACG,EAAE,EAAEF,MAAM,EAAEC,MAAM,CAAC,IAAIL,WAAW,CAACE,CAAC,CAACK,EAAE,EAAEJ,CAAC,CAACI,EAAE,EAAEH,MAAM,EAAEC,MAAM,CAAC;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}