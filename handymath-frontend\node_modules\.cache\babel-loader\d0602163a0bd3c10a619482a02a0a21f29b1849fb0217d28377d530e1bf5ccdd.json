{"ast": null, "code": "export var matrixFromRowsDocs = {\n  name: 'matrixFromRows',\n  category: 'Matrix',\n  syntax: ['matrixFromRows(...arr)', 'matrixFromRows(row1, row2)', 'matrixFromRows(row1, row2, row3)'],\n  description: 'Create a dense matrix from vectors as individual rows.',\n  examples: ['matrixFromRows([1, 2, 3], [[4],[5],[6]])'],\n  seealso: ['matrix', 'matrixFromColumns', 'matrixFromFunction', 'zeros']\n};", "map": {"version": 3, "names": ["matrixFromRowsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/matrixFromRows.js"], "sourcesContent": ["export var matrixFromRowsDocs = {\n  name: 'matrixFromRows',\n  category: 'Matrix',\n  syntax: ['matrixFromRows(...arr)', 'matrixFromRows(row1, row2)', 'matrixFromRows(row1, row2, row3)'],\n  description: 'Create a dense matrix from vectors as individual rows.',\n  examples: ['matrixFromRows([1, 2, 3], [[4],[5],[6]])'],\n  seealso: ['matrix', 'matrixFromColumns', 'matrixFromFunction', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG;EAC9BC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,wBAAwB,EAAE,4BAA4B,EAAE,kCAAkC,CAAC;EACpGC,WAAW,EAAE,wDAAwD;EACrEC,QAAQ,EAAE,CAAC,0CAA0C,CAAC;EACtDC,OAAO,EAAE,CAAC,QAAQ,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,OAAO;AACxE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}