import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: 'page' | 'exercise' | 'course' | 'equation';
  path: string;
  icon: string;
}

interface GlobalSearchProps {
  className?: string;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ className = '' }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // Données de recherche simulées
  const searchData: SearchResult[] = [
    // Pages
    { id: '1', title: 'Résolveur d\'équations', description: 'Résoudre des équations mathématiques', type: 'page', path: '/solver', icon: '🧮' },
    { id: '2', title: 'Exercices', description: 'Pratiquer avec des exercices', type: 'page', path: '/exercises', icon: '📚' },
    { id: '3', title: 'Cours', description: 'Apprendre les concepts', type: 'page', path: '/courses', icon: '📖' },
    { id: '4', title: 'Progression', description: 'Suivre vos progrès', type: 'page', path: '/progress', icon: '📈' },
    { id: '5', title: 'Profil', description: 'Gérer votre profil', type: 'page', path: '/profile', icon: '👤' },
    { id: '6', title: 'Paramètres', description: 'Configurer l\'application', type: 'page', path: '/settings', icon: '⚙️' },
    { id: '7', title: 'Contact', description: 'Nous contacter', type: 'page', path: '/contact', icon: '📞' },
    { id: '8', title: 'À propos', description: 'En savoir plus sur HandyMath', type: 'page', path: '/about', icon: 'ℹ️' },
    
    // Exercices simulés
    { id: '9', title: 'Équations du premier degré', description: 'Résoudre ax + b = 0', type: 'exercise', path: '/exercises/1', icon: '📝' },
    { id: '10', title: 'Équations du second degré', description: 'Résoudre ax² + bx + c = 0', type: 'exercise', path: '/exercises/2', icon: '📝' },
    { id: '11', title: 'Systèmes d\'équations', description: 'Résoudre des systèmes linéaires', type: 'exercise', path: '/exercises/3', icon: '📝' },
    
    // Cours simulés
    { id: '12', title: 'Algèbre de base', description: 'Introduction à l\'algèbre', type: 'course', path: '/courses/1', icon: '📖' },
    { id: '13', title: 'Fonctions', description: 'Étude des fonctions mathématiques', type: 'course', path: '/courses/2', icon: '📖' },
    
    // Équations courantes
    { id: '14', title: 'x + 5 = 10', description: 'Équation linéaire simple', type: 'equation', path: '/solver?eq=x+5=10', icon: '🧮' },
    { id: '15', title: 'x² - 4 = 0', description: 'Équation quadratique', type: 'equation', path: '/solver?eq=x²-4=0', icon: '🧮' }
  ];

  // Recherche
  useEffect(() => {
    if (query.trim() === '') {
      setResults([]);
      setSelectedIndex(-1);
      return;
    }

    const filtered = searchData.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 8); // Limiter à 8 résultats

    setResults(filtered);
    setSelectedIndex(-1);
  }, [query]);

  // Gestion du clavier
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            handleResultClick(results[selectedIndex]);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setQuery('');
          inputRef.current?.blur();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex]);

  // Fermer lors du clic à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleResultClick = (result: SearchResult) => {
    navigate(result.path);
    setIsOpen(false);
    setQuery('');
    inputRef.current?.blur();
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      page: 'Page',
      exercise: 'Exercice',
      course: 'Cours',
      equation: 'Équation'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      page: 'bg-blue-100 text-blue-800',
      exercise: 'bg-green-100 text-green-800',
      course: 'bg-purple-100 text-purple-800',
      equation: 'bg-orange-100 text-orange-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Champ de recherche */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          placeholder="Rechercher..."
          className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* Résultats de recherche */}
      <AnimatePresence>
        {isOpen && (query.trim() !== '' || results.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-y-auto"
          >
            {results.length > 0 ? (
              <div className="py-2">
                {results.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      index === selectedIndex ? 'bg-gray-50 dark:bg-gray-700' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{result.icon}</span>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {result.title}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {result.description}
                          </div>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(result.type)}`}>
                        {getTypeLabel(result.type)}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            ) : query.trim() !== '' ? (
              <div className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                <div className="text-4xl mb-2">🔍</div>
                <div className="font-medium">Aucun résultat trouvé</div>
                <div className="text-sm">Essayez avec d'autres mots-clés</div>
              </div>
            ) : null}

            {/* Raccourcis clavier */}
            {results.length > 0 && (
              <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2 bg-gray-50 dark:bg-gray-700/50">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>↑↓ pour naviguer</span>
                  <span>↵ pour sélectionner</span>
                  <span>Esc pour fermer</span>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GlobalSearch;
