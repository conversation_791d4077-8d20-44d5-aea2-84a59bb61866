{"ast": null, "code": "export var stringDocs = {\n  name: 'string',\n  category: 'Construction',\n  syntax: ['\"text\"', 'string(x)'],\n  description: 'Create a string or convert a value to a string',\n  examples: ['\"Hello World!\"', 'string(4.2)', 'string(3 + 2i)'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'number', 'unit']\n};", "map": {"version": 3, "names": ["stringDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/string.js"], "sourcesContent": ["export var stringDocs = {\n  name: 'string',\n  category: 'Construction',\n  syntax: ['\"text\"', 'string(x)'],\n  description: 'Create a string or convert a value to a string',\n  examples: ['\"Hello World!\"', 'string(4.2)', 'string(3 + 2i)'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'number', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;EAC/BC,WAAW,EAAE,gDAAgD;EAC7DC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,aAAa,EAAE,gBAAgB,CAAC;EAC7DC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AAClF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}