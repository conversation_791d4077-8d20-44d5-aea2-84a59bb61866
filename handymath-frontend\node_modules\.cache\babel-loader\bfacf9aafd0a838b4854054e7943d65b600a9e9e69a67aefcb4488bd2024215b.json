{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createSQRT1_2 } from '../../factoriesAny.js'; // eslint-disable-line camelcase\n\nexport var SQRT1_2Dependencies = {\n  // eslint-disable-line camelcase\n  BigNumberDependencies,\n  createSQRT1_2\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "createSQRT1_2", "SQRT1_2Dependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSQRT1_2.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createSQRT1_2 } from '../../factoriesAny.js'; // eslint-disable-line camelcase\n\nexport var SQRT1_2Dependencies = {\n  // eslint-disable-line camelcase\n  BigNumberDependencies,\n  createSQRT1_2\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,aAAa,QAAQ,uBAAuB,CAAC,CAAC;;AAEvD,OAAO,IAAIC,mBAAmB,GAAG;EAC/B;EACAF,qBAAqB;EACrBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}