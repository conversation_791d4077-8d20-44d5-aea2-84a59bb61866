import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import SimpleHeader from '../../components/SimpleHeader';

const NotFoundPage: React.FC = () => {
  return (
    <>
      <SimpleHeader title="Page non trouvée" />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
        <div className="max-w-lg w-full text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Illustration 404 */}
            <div className="mb-8">
              <motion.div
                className="text-9xl font-bold text-primary-600 dark:text-primary-400"
                initial={{ scale: 0.5 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                404
              </motion.div>
              <motion.div
                className="text-6xl mb-4"
                initial={{ rotate: -10 }}
                animate={{ rotate: 0 }}
                transition={{ delay: 0.4, type: "spring" }}
              >
                🔍
              </motion.div>
            </div>

            {/* Message d'erreur */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Page non trouvée
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Oups ! La page que vous recherchez semble avoir disparu dans l'espace mathématique.
              </p>
            </motion.div>

            {/* Suggestions */}
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Que souhaitez-vous faire ?
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Link
                  to="/"
                  className="p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🏠</div>
                  <div className="font-medium text-primary-700 dark:text-primary-300">Accueil</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Retour à la page principale</div>
                </Link>

                <Link
                  to="/solver"
                  className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🧮</div>
                  <div className="font-medium text-green-700 dark:text-green-300">Résoudre</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Résoudre une équation</div>
                </Link>

                <Link
                  to="/exercises"
                  className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">📚</div>
                  <div className="font-medium text-blue-700 dark:text-blue-300">Exercices</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Pratiquer les maths</div>
                </Link>

                <Link
                  to="/courses"
                  className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">📖</div>
                  <div className="font-medium text-purple-700 dark:text-purple-300">Cours</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Apprendre les concepts</div>
                </Link>
              </div>
            </motion.div>

            {/* Boutons d'action */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
            >
              <button
                onClick={() => window.history.back()}
                className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
              >
                ← Retour
              </button>
              <Link
                to="/"
                className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors"
              >
                🏠 Accueil
              </Link>
            </motion.div>

            {/* Message d'aide */}
            <motion.p
              className="text-sm text-gray-500 dark:text-gray-400 mt-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
            >
              Si vous pensez qu'il s'agit d'une erreur, veuillez{' '}
              <Link to="/contact" className="text-primary-600 hover:text-primary-700 underline">
                nous contacter
              </Link>
              .
            </motion.p>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default NotFoundPage;
