"""
Tests mathématiques de base pour HandyMath
"""
import pytest
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()


@pytest.mark.unit
class TestMathematicalFunctions:
    """Tests pour les fonctions mathématiques"""
    
    def test_basic_arithmetic(self):
        """Test d'arithmétique de base"""
        assert 2 + 2 == 4
        assert 5 * 3 == 15
        assert 10 / 2 == 5
        assert 2 ** 3 == 8
    
    def test_sympy_basic_operations(self):
        """Test des opérations de base avec SymPy"""
        import sympy as sp
        
        # Test de création de symbole
        x = sp.Symbol('x')
        assert str(x) == 'x'
        
        # Test d'expression simple
        expr = 2*x + 3
        assert str(expr) == '2*x + 3'
        
        # Test de substitution
        result = expr.subs(x, 5)
        assert result == 13
    
    def test_sympy_equation_solving(self):
        """Test de résolution d'équations avec SymPy"""
        import sympy as sp
        
        x = sp.Symbol('x')
        
        # Équation linéaire simple: x + 5 = 10
        equation1 = sp.Eq(x + 5, 10)
        solution1 = sp.solve(equation1, x)
        assert solution1 == [5]
        
        # Équation linéaire: 2x + 3 = 11
        equation2 = sp.Eq(2*x + 3, 11)
        solution2 = sp.solve(equation2, x)
        assert solution2 == [4]
        
        # Équation quadratique: x^2 - 4 = 0
        equation3 = sp.Eq(x**2 - 4, 0)
        solution3 = sp.solve(equation3, x)
        assert set(solution3) == {-2, 2}
    
    def test_equation_parsing(self):
        """Test de parsing d'équations"""
        
        def parse_simple_equation(equation_str):
            """Fonction simple de parsing d'équation"""
            if '=' not in equation_str:
                return None, None
            
            left, right = equation_str.split('=')
            return left.strip(), right.strip()
        
        # Test de parsing
        left, right = parse_simple_equation("2x + 3 = 11")
        assert left == "2x + 3"
        assert right == "11"
        
        left, right = parse_simple_equation("x^2 - 4 = 0")
        assert left == "x^2 - 4"
        assert right == "0"
    
    def test_equation_validation(self):
        """Test de validation d'équations"""
        
        def is_valid_equation(equation_str):
            """Fonction simple de validation"""
            if not equation_str or not equation_str.strip():
                return False
            
            if '=' not in equation_str:
                return False
            
            parts = equation_str.split('=')
            if len(parts) != 2:
                return False
            
            return all(part.strip() for part in parts)
        
        # Tests de validation
        assert is_valid_equation("x + 1 = 5") is True
        assert is_valid_equation("2x + 3 = 11") is True
        assert is_valid_equation("x^2 - 4 = 0") is True
        
        # Tests d'invalidité
        assert is_valid_equation("") is False
        assert is_valid_equation("x + 1") is False
        assert is_valid_equation("= 5") is False
        assert is_valid_equation("x + 1 = = 5") is False


@pytest.mark.unit
class TestEquationSolver:
    """Tests pour le solveur d'équations"""
    
    def test_linear_equation_solver(self):
        """Test de résolution d'équations linéaires"""
        import sympy as sp
        
        def solve_linear_equation(equation_str):
            """Solveur simple d'équations linéaires"""
            try:
                x = sp.Symbol('x')
                left, right = equation_str.split('=')
                
                # Convertir en expressions SymPy
                left_expr = sp.sympify(left.strip())
                right_expr = sp.sympify(right.strip())
                
                # Créer et résoudre l'équation
                equation = sp.Eq(left_expr, right_expr)
                solution = sp.solve(equation, x)
                
                return {
                    'solution': solution,
                    'steps': [
                        f"Équation originale: {equation_str}",
                        f"Équation SymPy: {equation}",
                        f"Solution: x = {solution[0] if solution else 'Aucune solution'}"
                    ]
                }
            except Exception as e:
                return {'error': str(e)}
        
        # Test d'équations linéaires
        result1 = solve_linear_equation("x + 5 = 10")
        assert result1['solution'] == [5]
        assert 'steps' in result1
        
        result2 = solve_linear_equation("2*x + 3 = 11")
        assert result2['solution'] == [4]
        
        result3 = solve_linear_equation("3*x - 7 = 14")
        assert result3['solution'] == [7]
    
    def test_quadratic_equation_solver(self):
        """Test de résolution d'équations quadratiques"""
        import sympy as sp
        
        def solve_quadratic_equation(equation_str):
            """Solveur d'équations quadratiques"""
            try:
                x = sp.Symbol('x')
                left, right = equation_str.split('=')
                
                left_expr = sp.sympify(left.strip())
                right_expr = sp.sympify(right.strip())
                
                equation = sp.Eq(left_expr, right_expr)
                solution = sp.solve(equation, x)
                
                return {
                    'solution': solution,
                    'type': 'quadratic',
                    'discriminant': None  # Pourrait être calculé
                }
            except Exception as e:
                return {'error': str(e)}
        
        # Test d'équations quadratiques
        result1 = solve_quadratic_equation("x**2 - 4 = 0")
        assert set(result1['solution']) == {-2, 2}
        
        result2 = solve_quadratic_equation("x**2 + 5*x + 6 = 0")
        assert set(result2['solution']) == {-2, -3}
    
    def test_equation_steps_generation(self):
        """Test de génération d'étapes de résolution"""
        
        def generate_solution_steps(equation_str, solution):
            """Générer les étapes de résolution"""
            steps = [
                f"1. Équation donnée: {equation_str}",
                f"2. Isoler la variable x",
                f"3. Solution: x = {solution}"
            ]
            return steps
        
        steps = generate_solution_steps("2x + 3 = 11", 4)
        assert len(steps) == 3
        assert "2x + 3 = 11" in steps[0]
        assert "x = 4" in steps[2]


@pytest.mark.unit
class TestMathUtilities:
    """Tests pour les utilitaires mathématiques"""
    
    def test_expression_simplification(self):
        """Test de simplification d'expressions"""
        import sympy as sp
        
        x = sp.Symbol('x')
        
        # Test de simplification
        expr1 = 2*x + 3*x
        simplified1 = sp.simplify(expr1)
        assert simplified1 == 5*x
        
        # Test d'expansion
        expr2 = (x + 2)**2
        expanded2 = sp.expand(expr2)
        assert expanded2 == x**2 + 4*x + 4
        
        # Test de factorisation
        expr3 = x**2 + 5*x + 6
        factored3 = sp.factor(expr3)
        assert factored3 == (x + 2)*(x + 3)
    
    def test_expression_evaluation(self):
        """Test d'évaluation d'expressions"""
        import sympy as sp
        
        x = sp.Symbol('x')
        
        # Test d'évaluation numérique
        expr = 2*x + 3
        
        result1 = expr.subs(x, 1)
        assert result1 == 5
        
        result2 = expr.subs(x, 5)
        assert result2 == 13
        
        result3 = expr.subs(x, -2)
        assert result3 == -1
    
    def test_equation_type_detection(self):
        """Test de détection du type d'équation"""
        
        def detect_equation_type(equation_str):
            """Détecter le type d'équation"""
            if '^2' in equation_str or '**2' in equation_str:
                return 'quadratic'
            elif '^3' in equation_str or '**3' in equation_str:
                return 'cubic'
            elif 'x' in equation_str:
                return 'linear'
            else:
                return 'constant'
        
        # Tests de détection
        assert detect_equation_type("x + 5 = 10") == 'linear'
        assert detect_equation_type("2x + 3 = 11") == 'linear'
        assert detect_equation_type("x^2 - 4 = 0") == 'quadratic'
        assert detect_equation_type("x**2 + 5*x + 6 = 0") == 'quadratic'
        assert detect_equation_type("5 = 10") == 'constant'


@pytest.mark.integration
class TestMathIntegration:
    """Tests d'intégration pour les fonctions mathématiques"""
    
    def test_complete_equation_solving_pipeline(self):
        """Test du pipeline complet de résolution"""
        
        def solve_equation_complete(equation_str):
            """Pipeline complet de résolution d'équation"""
            import sympy as sp
            
            # 1. Validation
            if '=' not in equation_str:
                return {'error': 'Équation invalide'}
            
            # 2. Parsing
            left, right = equation_str.split('=')
            
            # 3. Résolution
            try:
                x = sp.Symbol('x')
                left_expr = sp.sympify(left.strip())
                right_expr = sp.sympify(right.strip())
                equation = sp.Eq(left_expr, right_expr)
                solution = sp.solve(equation, x)
                
                # 4. Génération des étapes
                steps = [
                    f"Équation: {equation_str}",
                    f"Forme standard: {equation}",
                    f"Solution: {solution}"
                ]
                
                return {
                    'original_equation': equation_str,
                    'solution': solution,
                    'steps': steps,
                    'success': True
                }
            except Exception as e:
                return {'error': str(e), 'success': False}
        
        # Test du pipeline complet
        test_equations = [
            "x + 1 = 5",
            "2*x + 3 = 11",
            "x**2 - 4 = 0"
        ]
        
        for eq in test_equations:
            result = solve_equation_complete(eq)
            assert result['success'] is True
            assert 'solution' in result
            assert 'steps' in result
            assert len(result['solution']) > 0
