{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csReach } from './csReach.js';\nimport { factory } from '../../../utils/factory.js';\nvar name = 'csSpsolve';\nvar dependencies = ['divideScalar', 'multiply', 'subtract'];\nexport var createCsSpsolve = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    divideScalar,\n    multiply,\n    subtract\n  } = _ref;\n  /**\n   * The function csSpsolve() computes the solution to G * x = bk, where bk is the\n   * kth column of B. When lo is true, the function assumes G = L is lower triangular with the\n   * diagonal entry as the first entry in each column. When lo is true, the function assumes G = U\n   * is upper triangular with the diagonal entry as the last entry in each column.\n   *\n   * @param {Matrix}  g               The G matrix\n   * @param {Matrix}  b               The B matrix\n   * @param {Number}  k               The kth column in B\n   * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n   *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n   * @param {Array}   x               The soluton to the linear system G * x = b\n   * @param {Array}   pinv            The inverse row permutation vector, must be null for L * x = b\n   * @param {boolean} lo              The lower (true) upper triangular (false) flag\n   *\n   * @return {Number}                 The index for the nonzero pattern\n   */\n  return function csSpsolve(g, b, k, xi, x, pinv, lo) {\n    // g arrays\n    var gvalues = g._values;\n    var gindex = g._index;\n    var gptr = g._ptr;\n    var gsize = g._size;\n    // columns\n    var n = gsize[1];\n    // b arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    // vars\n    var p, p0, p1, q;\n    // xi[top..n-1] = csReach(B(:,k))\n    var top = csReach(g, b, k, xi, pinv);\n    // clear x\n    for (p = top; p < n; p++) {\n      x[xi[p]] = 0;\n    }\n    // scatter b\n    for (p0 = bptr[k], p1 = bptr[k + 1], p = p0; p < p1; p++) {\n      x[bindex[p]] = bvalues[p];\n    }\n    // loop columns\n    for (var px = top; px < n; px++) {\n      // x array index for px\n      var j = xi[px];\n      // apply permutation vector (U x = b), j maps to column J of G\n      var J = pinv ? pinv[j] : j;\n      // check column J is empty\n      if (J < 0) {\n        continue;\n      }\n      // column value indeces in G, p0 <= p < p1\n      p0 = gptr[J];\n      p1 = gptr[J + 1];\n      // x(j) /= G(j,j)\n      x[j] = divideScalar(x[j], gvalues[lo ? p0 : p1 - 1]);\n      // first entry L(j,j)\n      p = lo ? p0 + 1 : p0;\n      q = lo ? p1 : p1 - 1;\n      // loop\n      for (; p < q; p++) {\n        // row\n        var i = gindex[p];\n        // x(i) -= G(i,j) * x(j)\n        x[i] = subtract(x[i], multiply(gvalues[p], x[j]));\n      }\n    }\n    // return top of stack\n    return top;\n  };\n});", "map": {"version": 3, "names": ["csReach", "factory", "name", "dependencies", "createCsSpsolve", "_ref", "divideScalar", "multiply", "subtract", "csSpsolve", "g", "b", "k", "xi", "x", "pinv", "lo", "gvalues", "_values", "gindex", "_index", "gptr", "_ptr", "gsize", "_size", "n", "bvalues", "bindex", "bptr", "p", "p0", "p1", "q", "top", "px", "j", "J", "i"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csSpsolve.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csReach } from './csReach.js';\nimport { factory } from '../../../utils/factory.js';\nvar name = 'csSpsolve';\nvar dependencies = ['divideScalar', 'multiply', 'subtract'];\nexport var createCsSpsolve = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    divideScalar,\n    multiply,\n    subtract\n  } = _ref;\n  /**\n   * The function csSpsolve() computes the solution to G * x = bk, where bk is the\n   * kth column of B. When lo is true, the function assumes G = L is lower triangular with the\n   * diagonal entry as the first entry in each column. When lo is true, the function assumes G = U\n   * is upper triangular with the diagonal entry as the last entry in each column.\n   *\n   * @param {Matrix}  g               The G matrix\n   * @param {Matrix}  b               The B matrix\n   * @param {Number}  k               The kth column in B\n   * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n   *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n   * @param {Array}   x               The soluton to the linear system G * x = b\n   * @param {Array}   pinv            The inverse row permutation vector, must be null for L * x = b\n   * @param {boolean} lo              The lower (true) upper triangular (false) flag\n   *\n   * @return {Number}                 The index for the nonzero pattern\n   */\n  return function csSpsolve(g, b, k, xi, x, pinv, lo) {\n    // g arrays\n    var gvalues = g._values;\n    var gindex = g._index;\n    var gptr = g._ptr;\n    var gsize = g._size;\n    // columns\n    var n = gsize[1];\n    // b arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    // vars\n    var p, p0, p1, q;\n    // xi[top..n-1] = csReach(B(:,k))\n    var top = csReach(g, b, k, xi, pinv);\n    // clear x\n    for (p = top; p < n; p++) {\n      x[xi[p]] = 0;\n    }\n    // scatter b\n    for (p0 = bptr[k], p1 = bptr[k + 1], p = p0; p < p1; p++) {\n      x[bindex[p]] = bvalues[p];\n    }\n    // loop columns\n    for (var px = top; px < n; px++) {\n      // x array index for px\n      var j = xi[px];\n      // apply permutation vector (U x = b), j maps to column J of G\n      var J = pinv ? pinv[j] : j;\n      // check column J is empty\n      if (J < 0) {\n        continue;\n      }\n      // column value indeces in G, p0 <= p < p1\n      p0 = gptr[J];\n      p1 = gptr[J + 1];\n      // x(j) /= G(j,j)\n      x[j] = divideScalar(x[j], gvalues[lo ? p0 : p1 - 1]);\n      // first entry L(j,j)\n      p = lo ? p0 + 1 : p0;\n      q = lo ? p1 : p1 - 1;\n      // loop\n      for (; p < q; p++) {\n        // row\n        var i = gindex[p];\n        // x(i) -= G(i,j) * x(j)\n        x[i] = subtract(x[i], multiply(gvalues[p], x[j]));\n      }\n    }\n    // return top of stack\n    return top;\n  };\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,IAAI,GAAG,WAAW;AACtB,IAAIC,YAAY,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;AAC3D,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC9E,IAAI;IACFC,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAGH,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASI,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,CAAC,EAAEC,IAAI,EAAEC,EAAE,EAAE;IAClD;IACA,IAAIC,OAAO,GAAGP,CAAC,CAACQ,OAAO;IACvB,IAAIC,MAAM,GAAGT,CAAC,CAACU,MAAM;IACrB,IAAIC,IAAI,GAAGX,CAAC,CAACY,IAAI;IACjB,IAAIC,KAAK,GAAGb,CAAC,CAACc,KAAK;IACnB;IACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAChB;IACA,IAAIG,OAAO,GAAGf,CAAC,CAACO,OAAO;IACvB,IAAIS,MAAM,GAAGhB,CAAC,CAACS,MAAM;IACrB,IAAIQ,IAAI,GAAGjB,CAAC,CAACW,IAAI;IACjB;IACA,IAAIO,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC;IAChB;IACA,IAAIC,GAAG,GAAGjC,OAAO,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEE,IAAI,CAAC;IACpC;IACA,KAAKc,CAAC,GAAGI,GAAG,EAAEJ,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MACxBf,CAAC,CAACD,EAAE,CAACgB,CAAC,CAAC,CAAC,GAAG,CAAC;IACd;IACA;IACA,KAAKC,EAAE,GAAGF,IAAI,CAAChB,CAAC,CAAC,EAAEmB,EAAE,GAAGH,IAAI,CAAChB,CAAC,GAAG,CAAC,CAAC,EAAEiB,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;MACxDf,CAAC,CAACa,MAAM,CAACE,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACG,CAAC,CAAC;IAC3B;IACA;IACA,KAAK,IAAIK,EAAE,GAAGD,GAAG,EAAEC,EAAE,GAAGT,CAAC,EAAES,EAAE,EAAE,EAAE;MAC/B;MACA,IAAIC,CAAC,GAAGtB,EAAE,CAACqB,EAAE,CAAC;MACd;MACA,IAAIE,CAAC,GAAGrB,IAAI,GAAGA,IAAI,CAACoB,CAAC,CAAC,GAAGA,CAAC;MAC1B;MACA,IAAIC,CAAC,GAAG,CAAC,EAAE;QACT;MACF;MACA;MACAN,EAAE,GAAGT,IAAI,CAACe,CAAC,CAAC;MACZL,EAAE,GAAGV,IAAI,CAACe,CAAC,GAAG,CAAC,CAAC;MAChB;MACAtB,CAAC,CAACqB,CAAC,CAAC,GAAG7B,YAAY,CAACQ,CAAC,CAACqB,CAAC,CAAC,EAAElB,OAAO,CAACD,EAAE,GAAGc,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,CAAC;MACpD;MACAF,CAAC,GAAGb,EAAE,GAAGc,EAAE,GAAG,CAAC,GAAGA,EAAE;MACpBE,CAAC,GAAGhB,EAAE,GAAGe,EAAE,GAAGA,EAAE,GAAG,CAAC;MACpB;MACA,OAAOF,CAAC,GAAGG,CAAC,EAAEH,CAAC,EAAE,EAAE;QACjB;QACA,IAAIQ,CAAC,GAAGlB,MAAM,CAACU,CAAC,CAAC;QACjB;QACAf,CAAC,CAACuB,CAAC,CAAC,GAAG7B,QAAQ,CAACM,CAAC,CAACuB,CAAC,CAAC,EAAE9B,QAAQ,CAACU,OAAO,CAACY,CAAC,CAAC,EAAEf,CAAC,CAACqB,CAAC,CAAC,CAAC,CAAC;MACnD;IACF;IACA;IACA,OAAOF,GAAG;EACZ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}