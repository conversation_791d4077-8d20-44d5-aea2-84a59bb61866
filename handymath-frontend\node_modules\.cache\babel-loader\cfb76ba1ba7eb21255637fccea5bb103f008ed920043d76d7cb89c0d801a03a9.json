{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { deepMap } from '../../../utils/collection.js';\nvar name = 'complex';\nvar dependencies = ['typed', 'Complex'];\nexport var createComplex = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Complex\n  } = _ref;\n  /**\n   * Create a complex value or convert a value to a complex value.\n   *\n   * Syntax:\n   *\n   *     math.complex()                           // creates a complex value with zero\n   *                                              // as real and imaginary part.\n   *     math.complex(re : number, im : string)   // creates a complex value with provided\n   *                                              // values for real and imaginary part.\n   *     math.complex(re : number)                // creates a complex value with provided\n   *                                              // real value and zero imaginary part.\n   *     math.complex(complex : Complex)          // clones the provided complex value.\n   *     math.complex(arg : string)               // parses a string into a complex value.\n   *     math.complex(array : Array)              // converts the elements of the array\n   *                                              // or matrix element wise into a\n   *                                              // complex value.\n   *     math.complex({re: number, im: number})   // creates a complex value with provided\n   *                                              // values for real an imaginary part.\n   *     math.complex({r: number, phi: number})   // creates a complex value with provided\n   *                                              // polar coordinates\n   *\n   * Examples:\n   *\n   *    const a = math.complex(3, -4)     // a = Complex 3 - 4i\n   *    a.re = 5                          // a = Complex 5 - 4i\n   *    const i = a.im                    // Number -4\n   *    const b = math.complex('2 + 6i')  // Complex 2 + 6i\n   *    const c = math.complex()          // Complex 0 + 0i\n   *    const d = math.add(a, b)          // Complex 5 + 2i\n   *\n   * See also:\n   *\n   *    bignumber, boolean, index, matrix, number, string, unit\n   *\n   * @param {* | Array | Matrix} [args]\n   *            Arguments specifying the real and imaginary part of the complex number\n   * @return {Complex | Array | Matrix} Returns a complex value\n   */\n  return typed('complex', {\n    '': function _() {\n      return Complex.ZERO;\n    },\n    number: function number(x) {\n      return new Complex(x, 0);\n    },\n    'number, number': function number_number(re, im) {\n      return new Complex(re, im);\n    },\n    // TODO: this signature should be redundant\n    'BigNumber, BigNumber': function BigNumber_BigNumber(re, im) {\n      return new Complex(re.toNumber(), im.toNumber());\n    },\n    Fraction: function Fraction(x) {\n      return new Complex(x.valueOf(), 0);\n    },\n    Complex: function Complex(x) {\n      return x.clone();\n    },\n    string: function string(x) {\n      return Complex(x); // for example '2 + 3i'\n    },\n    null: function _null(x) {\n      return Complex(0);\n    },\n    Object: function Object(x) {\n      if ('re' in x && 'im' in x) {\n        return new Complex(x.re, x.im);\n      }\n      if ('r' in x && 'phi' in x || 'abs' in x && 'arg' in x) {\n        return new Complex(x);\n      }\n      throw new Error('Expected object with properties (re and im) or (r and phi) or (abs and arg)');\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["factory", "deepMap", "name", "dependencies", "createComplex", "_ref", "typed", "Complex", "_", "ZERO", "number", "x", "number_number", "re", "im", "BigNumber_BigNumber", "toNumber", "Fraction", "valueOf", "clone", "string", "null", "_null", "Object", "Error", "referToSelf", "self"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/complex/function/complex.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { deepMap } from '../../../utils/collection.js';\nvar name = 'complex';\nvar dependencies = ['typed', 'Complex'];\nexport var createComplex = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Complex\n  } = _ref;\n  /**\n   * Create a complex value or convert a value to a complex value.\n   *\n   * Syntax:\n   *\n   *     math.complex()                           // creates a complex value with zero\n   *                                              // as real and imaginary part.\n   *     math.complex(re : number, im : string)   // creates a complex value with provided\n   *                                              // values for real and imaginary part.\n   *     math.complex(re : number)                // creates a complex value with provided\n   *                                              // real value and zero imaginary part.\n   *     math.complex(complex : Complex)          // clones the provided complex value.\n   *     math.complex(arg : string)               // parses a string into a complex value.\n   *     math.complex(array : Array)              // converts the elements of the array\n   *                                              // or matrix element wise into a\n   *                                              // complex value.\n   *     math.complex({re: number, im: number})   // creates a complex value with provided\n   *                                              // values for real an imaginary part.\n   *     math.complex({r: number, phi: number})   // creates a complex value with provided\n   *                                              // polar coordinates\n   *\n   * Examples:\n   *\n   *    const a = math.complex(3, -4)     // a = Complex 3 - 4i\n   *    a.re = 5                          // a = Complex 5 - 4i\n   *    const i = a.im                    // Number -4\n   *    const b = math.complex('2 + 6i')  // Complex 2 + 6i\n   *    const c = math.complex()          // Complex 0 + 0i\n   *    const d = math.add(a, b)          // Complex 5 + 2i\n   *\n   * See also:\n   *\n   *    bignumber, boolean, index, matrix, number, string, unit\n   *\n   * @param {* | Array | Matrix} [args]\n   *            Arguments specifying the real and imaginary part of the complex number\n   * @return {Complex | Array | Matrix} Returns a complex value\n   */\n  return typed('complex', {\n    '': function _() {\n      return Complex.ZERO;\n    },\n    number: function number(x) {\n      return new Complex(x, 0);\n    },\n    'number, number': function number_number(re, im) {\n      return new Complex(re, im);\n    },\n    // TODO: this signature should be redundant\n    'BigNumber, BigNumber': function BigNumber_BigNumber(re, im) {\n      return new Complex(re.toNumber(), im.toNumber());\n    },\n    Fraction: function Fraction(x) {\n      return new Complex(x.valueOf(), 0);\n    },\n    Complex: function Complex(x) {\n      return x.clone();\n    },\n    string: function string(x) {\n      return Complex(x); // for example '2 + 3i'\n    },\n    null: function _null(x) {\n      return Complex(0);\n    },\n    Object: function Object(x) {\n      if ('re' in x && 'im' in x) {\n        return new Complex(x.re, x.im);\n      }\n      if ('r' in x && 'phi' in x || 'abs' in x && 'arg' in x) {\n        return new Complex(x);\n      }\n      throw new Error('Expected object with properties (re and im) or (r and phi) or (abs and arg)');\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,8BAA8B;AACtD,IAAIC,IAAI,GAAG,SAAS;AACpB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;AACvC,OAAO,IAAIC,aAAa,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC5E,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAAC,SAAS,EAAE;IACtB,EAAE,EAAE,SAASE,CAACA,CAAA,EAAG;MACf,OAAOD,OAAO,CAACE,IAAI;IACrB,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACC,CAAC,EAAE;MACzB,OAAO,IAAIJ,OAAO,CAACI,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IACD,gBAAgB,EAAE,SAASC,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAE;MAC/C,OAAO,IAAIP,OAAO,CAACM,EAAE,EAAEC,EAAE,CAAC;IAC5B,CAAC;IACD;IACA,sBAAsB,EAAE,SAASC,mBAAmBA,CAACF,EAAE,EAAEC,EAAE,EAAE;MAC3D,OAAO,IAAIP,OAAO,CAACM,EAAE,CAACG,QAAQ,CAAC,CAAC,EAAEF,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC;IAClD,CAAC;IACDC,QAAQ,EAAE,SAASA,QAAQA,CAACN,CAAC,EAAE;MAC7B,OAAO,IAAIJ,OAAO,CAACI,CAAC,CAACO,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACDX,OAAO,EAAE,SAASA,OAAOA,CAACI,CAAC,EAAE;MAC3B,OAAOA,CAAC,CAACQ,KAAK,CAAC,CAAC;IAClB,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACT,CAAC,EAAE;MACzB,OAAOJ,OAAO,CAACI,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACDU,IAAI,EAAE,SAASC,KAAKA,CAACX,CAAC,EAAE;MACtB,OAAOJ,OAAO,CAAC,CAAC,CAAC;IACnB,CAAC;IACDgB,MAAM,EAAE,SAASA,MAAMA,CAACZ,CAAC,EAAE;MACzB,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,IAAIA,CAAC,EAAE;QAC1B,OAAO,IAAIJ,OAAO,CAACI,CAAC,CAACE,EAAE,EAAEF,CAAC,CAACG,EAAE,CAAC;MAChC;MACA,IAAI,GAAG,IAAIH,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,IAAIA,CAAC,EAAE;QACtD,OAAO,IAAIJ,OAAO,CAACI,CAAC,CAAC;MACvB;MACA,MAAM,IAAIa,KAAK,CAAC,6EAA6E,CAAC;IAChG,CAAC;IACD,gBAAgB,EAAElB,KAAK,CAACmB,WAAW,CAACC,IAAI,IAAIf,CAAC,IAAIV,OAAO,CAACU,CAAC,EAAEe,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}