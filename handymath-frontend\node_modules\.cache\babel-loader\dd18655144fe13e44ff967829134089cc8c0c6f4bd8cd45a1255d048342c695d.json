{"ast": null, "code": "'use strict';\n\n/**\n *\n * This class allows the manipulation of complex numbers.\n * You can pass a complex number in different formats. Either as object, double, string or two integer parameters.\n *\n * Object form\n * { re: <real>, im: <imaginary> }\n * { arg: <angle>, abs: <radius> }\n * { phi: <angle>, r: <radius> }\n *\n * Array / Vector form\n * [ real, imaginary ]\n *\n * Double form\n * 99.3 - Single double value\n *\n * String form\n * '23.1337' - Simple real number\n * '15+3i' - a simple complex number\n * '3-i' - a simple complex number\n *\n * Example:\n *\n * const c = new Complex('99.3+8i');\n * c.mul({r: 3, i: 9}).div(4.9).sub(3, 2);\n *\n */\nconst cosh = Math.cosh || function (x) {\n  return Math.abs(x) < 1e-9 ? 1 - x : (Math.exp(x) + Math.exp(-x)) * 0.5;\n};\nconst sinh = Math.sinh || function (x) {\n  return Math.abs(x) < 1e-9 ? x : (Math.exp(x) - Math.exp(-x)) * 0.5;\n};\n\n/**\n * Calculates cos(x) - 1 using Taylor series if x is small (-¼π ≤ x ≤ ¼π).\n *\n * @param {number} x\n * @returns {number} cos(x) - 1\n */\nconst cosm1 = function (x) {\n  const b = Math.PI / 4;\n  if (-b > x || x > b) {\n    return Math.cos(x) - 1.0;\n  }\n\n  /* Calculate horner form of polynomial of taylor series in Q\n  let fac = 1, alt = 1, pol = {};\n  for (let i = 0; i <= 16; i++) {\n    fac*= i || 1;\n    if (i % 2 == 0) {\n      pol[i] = new Fraction(1, alt * fac);\n      alt = -alt;\n    }\n  }\n  console.log(new Polynomial(pol).toHorner()); // (((((((1/20922789888000x^2-1/87178291200)x^2+1/*********)x^2-1/3628800)x^2+1/40320)x^2-1/720)x^2+1/24)x^2-1/2)x^2+1\n  */\n\n  const xx = x * x;\n  return xx * (xx * (xx * (xx * (xx * (xx * (xx * (xx / 20922789888000 - 1 / 87178291200) + 1 / *********) - 1 / 3628800) + 1 / 40320) - 1 / 720) + 1 / 24) - 1 / 2);\n};\nconst hypot = function (x, y) {\n  x = Math.abs(x);\n  y = Math.abs(y);\n\n  // Ensure `x` is the larger value\n  if (x < y) [x, y] = [y, x];\n\n  // If both are below the threshold, use straightforward Pythagoras\n  if (x < 1e8) return Math.sqrt(x * x + y * y);\n\n  // For larger values, scale to avoid overflow\n  y /= x;\n  return x * Math.sqrt(1 + y * y);\n};\nconst parser_exit = function () {\n  throw SyntaxError('Invalid Param');\n};\n\n/**\n * Calculates log(sqrt(a^2+b^2)) in a way to avoid overflows\n *\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction logHypot(a, b) {\n  const _a = Math.abs(a);\n  const _b = Math.abs(b);\n  if (a === 0) {\n    return Math.log(_b);\n  }\n  if (b === 0) {\n    return Math.log(_a);\n  }\n  if (_a < 3000 && _b < 3000) {\n    return Math.log(a * a + b * b) * 0.5;\n  }\n\n  /* I got 4 ideas to compute this property without overflow:\n   *\n   * Testing 1000000 times with random samples for a,b ∈ [1, 1000000000] against a big decimal library to get an error estimate\n   *\n   * 1. Only eliminate the square root: (OVERALL ERROR: 3.9122483030951116e-11)\n    Math.log(a * a + b * b) / 2\n    *\n   *\n   * 2. Try to use the non-overflowing pythagoras: (OVERALL ERROR: 8.889760039210159e-10)\n    const fn = function(a, b) {\n   a = Math.abs(a);\n   b = Math.abs(b);\n   let t = Math.min(a, b);\n   a = Math.max(a, b);\n   t = t / a;\n    return Math.log(a) + Math.log(1 + t * t) / 2;\n   };\n    * 3. Abuse the identity cos(atan(y/x) = x / sqrt(x^2+y^2): (OVERALL ERROR: 3.4780178737037204e-10)\n    Math.log(a / Math.cos(Math.atan2(b, a)))\n    * 4. Use 3. and apply log rules: (OVERALL ERROR: 1.2014087502620896e-9)\n    Math.log(a) - Math.log(Math.cos(Math.atan2(b, a)))\n    */\n\n  a = a * 0.5;\n  b = b * 0.5;\n  return 0.5 * Math.log(a * a + b * b) + Math.LN2;\n}\nconst P = {\n  're': 0,\n  'im': 0\n};\nconst parse = function (a, b) {\n  const z = P;\n  if (a === undefined || a === null) {\n    z['re'] = z['im'] = 0;\n  } else if (b !== undefined) {\n    z['re'] = a;\n    z['im'] = b;\n  } else switch (typeof a) {\n    case 'object':\n      if ('im' in a && 're' in a) {\n        z['re'] = a['re'];\n        z['im'] = a['im'];\n      } else if ('abs' in a && 'arg' in a) {\n        if (!isFinite(a['abs']) && isFinite(a['arg'])) {\n          return Complex['INFINITY'];\n        }\n        z['re'] = a['abs'] * Math.cos(a['arg']);\n        z['im'] = a['abs'] * Math.sin(a['arg']);\n      } else if ('r' in a && 'phi' in a) {\n        if (!isFinite(a['r']) && isFinite(a['phi'])) {\n          return Complex['INFINITY'];\n        }\n        z['re'] = a['r'] * Math.cos(a['phi']);\n        z['im'] = a['r'] * Math.sin(a['phi']);\n      } else if (a.length === 2) {\n        // Quick array check\n        z['re'] = a[0];\n        z['im'] = a[1];\n      } else {\n        parser_exit();\n      }\n      break;\n    case 'string':\n      z['im'] = /* void */\n      z['re'] = 0;\n      const tokens = a.replace(/_/g, '').match(/\\d+\\.?\\d*e[+-]?\\d+|\\d+\\.?\\d*|\\.\\d+|./g);\n      let plus = 1;\n      let minus = 0;\n      if (tokens === null) {\n        parser_exit();\n      }\n      for (let i = 0; i < tokens.length; i++) {\n        const c = tokens[i];\n        if (c === ' ' || c === '\\t' || c === '\\n') {\n          /* void */\n        } else if (c === '+') {\n          plus++;\n        } else if (c === '-') {\n          minus++;\n        } else if (c === 'i' || c === 'I') {\n          if (plus + minus === 0) {\n            parser_exit();\n          }\n          if (tokens[i + 1] !== ' ' && !isNaN(tokens[i + 1])) {\n            z['im'] += parseFloat((minus % 2 ? '-' : '') + tokens[i + 1]);\n            i++;\n          } else {\n            z['im'] += parseFloat((minus % 2 ? '-' : '') + '1');\n          }\n          plus = minus = 0;\n        } else {\n          if (plus + minus === 0 || isNaN(c)) {\n            parser_exit();\n          }\n          if (tokens[i + 1] === 'i' || tokens[i + 1] === 'I') {\n            z['im'] += parseFloat((minus % 2 ? '-' : '') + c);\n            i++;\n          } else {\n            z['re'] += parseFloat((minus % 2 ? '-' : '') + c);\n          }\n          plus = minus = 0;\n        }\n      }\n\n      // Still something on the stack\n      if (plus + minus > 0) {\n        parser_exit();\n      }\n      break;\n    case 'number':\n      z['im'] = 0;\n      z['re'] = a;\n      break;\n    default:\n      parser_exit();\n  }\n  if (isNaN(z['re']) || isNaN(z['im'])) {\n    // If a calculation is NaN, we treat it as NaN and don't throw\n    //parser_exit();\n  }\n  return z;\n};\n\n/**\n * @constructor\n * @returns {Complex}\n */\nfunction Complex(a, b) {\n  if (!(this instanceof Complex)) {\n    return new Complex(a, b);\n  }\n  const z = parse(a, b);\n  this['re'] = z['re'];\n  this['im'] = z['im'];\n}\nComplex.prototype = {\n  're': 0,\n  'im': 0,\n  /**\n   * Calculates the sign of a complex number, which is a normalized complex\n   *\n   * @returns {Complex}\n   */\n  'sign': function () {\n    const abs = hypot(this['re'], this['im']);\n    return new Complex(this['re'] / abs, this['im'] / abs);\n  },\n  /**\n   * Adds two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'add': function (a, b) {\n    const z = parse(a, b);\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n    if (tInfin || zInfin) {\n      if (tInfin && zInfin) {\n        // Infinity + Infinity = NaN\n        return Complex['NAN'];\n      }\n      // Infinity + z = Infinity { where z != Infinity }\n      return Complex['INFINITY'];\n    }\n    return new Complex(this['re'] + z['re'], this['im'] + z['im']);\n  },\n  /**\n   * Subtracts two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'sub': function (a, b) {\n    const z = parse(a, b);\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n    if (tInfin || zInfin) {\n      if (tInfin && zInfin) {\n        // Infinity - Infinity = NaN\n        return Complex['NAN'];\n      }\n      // Infinity - z = Infinity { where z != Infinity }\n      return Complex['INFINITY'];\n    }\n    return new Complex(this['re'] - z['re'], this['im'] - z['im']);\n  },\n  /**\n   * Multiplies two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'mul': function (a, b) {\n    const z = parse(a, b);\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n    const tIsZero = this['re'] === 0 && this['im'] === 0;\n    const zIsZero = z['re'] === 0 && z['im'] === 0;\n\n    // Infinity * 0 = NaN\n    if (tInfin && zIsZero || zInfin && tIsZero) {\n      return Complex['NAN'];\n    }\n\n    // Infinity * z = Infinity { where z != 0 }\n    if (tInfin || zInfin) {\n      return Complex['INFINITY'];\n    }\n\n    // Shortcut for real values\n    if (z['im'] === 0 && this['im'] === 0) {\n      return new Complex(this['re'] * z['re'], 0);\n    }\n    return new Complex(this['re'] * z['re'] - this['im'] * z['im'], this['re'] * z['im'] + this['im'] * z['re']);\n  },\n  /**\n   * Divides two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'div': function (a, b) {\n    const z = parse(a, b);\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n    const tIsZero = this['re'] === 0 && this['im'] === 0;\n    const zIsZero = z['re'] === 0 && z['im'] === 0;\n\n    // 0 / 0 = NaN and Infinity / Infinity = NaN\n    if (tIsZero && zIsZero || tInfin && zInfin) {\n      return Complex['NAN'];\n    }\n\n    // Infinity / 0 = Infinity\n    if (zIsZero || tInfin) {\n      return Complex['INFINITY'];\n    }\n\n    // 0 / Infinity = 0\n    if (tIsZero || zInfin) {\n      return Complex['ZERO'];\n    }\n    if (0 === z['im']) {\n      // Divisor is real\n      return new Complex(this['re'] / z['re'], this['im'] / z['re']);\n    }\n    if (Math.abs(z['re']) < Math.abs(z['im'])) {\n      const x = z['re'] / z['im'];\n      const t = z['re'] * x + z['im'];\n      return new Complex((this['re'] * x + this['im']) / t, (this['im'] * x - this['re']) / t);\n    } else {\n      const x = z['im'] / z['re'];\n      const t = z['im'] * x + z['re'];\n      return new Complex((this['re'] + this['im'] * x) / t, (this['im'] - this['re'] * x) / t);\n    }\n  },\n  /**\n   * Calculate the power of two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'pow': function (a, b) {\n    const z = parse(a, b);\n    const tIsZero = this['re'] === 0 && this['im'] === 0;\n    const zIsZero = z['re'] === 0 && z['im'] === 0;\n    if (zIsZero) {\n      return Complex['ONE'];\n    }\n\n    // If the exponent is real\n    if (z['im'] === 0) {\n      if (this['im'] === 0 && this['re'] > 0) {\n        return new Complex(Math.pow(this['re'], z['re']), 0);\n      } else if (this['re'] === 0) {\n        // If base is fully imaginary\n\n        switch ((z['re'] % 4 + 4) % 4) {\n          case 0:\n            return new Complex(Math.pow(this['im'], z['re']), 0);\n          case 1:\n            return new Complex(0, Math.pow(this['im'], z['re']));\n          case 2:\n            return new Complex(-Math.pow(this['im'], z['re']), 0);\n          case 3:\n            return new Complex(0, -Math.pow(this['im'], z['re']));\n        }\n      }\n    }\n\n    /* I couldn't find a good formula, so here is a derivation and optimization\n     *\n     * z_1^z_2 = (a + bi)^(c + di)\n     *         = exp((c + di) * log(a + bi)\n     *         = pow(a^2 + b^2, (c + di) / 2) * exp(i(c + di)atan2(b, a))\n     * =>...\n     * Re = (pow(a^2 + b^2, c / 2) * exp(-d * atan2(b, a))) * cos(d * log(a^2 + b^2) / 2 + c * atan2(b, a))\n     * Im = (pow(a^2 + b^2, c / 2) * exp(-d * atan2(b, a))) * sin(d * log(a^2 + b^2) / 2 + c * atan2(b, a))\n     *\n     * =>...\n     * Re = exp(c * log(sqrt(a^2 + b^2)) - d * atan2(b, a)) * cos(d * log(sqrt(a^2 + b^2)) + c * atan2(b, a))\n     * Im = exp(c * log(sqrt(a^2 + b^2)) - d * atan2(b, a)) * sin(d * log(sqrt(a^2 + b^2)) + c * atan2(b, a))\n     *\n     * =>\n     * Re = exp(c * logsq2 - d * arg(z_1)) * cos(d * logsq2 + c * arg(z_1))\n     * Im = exp(c * logsq2 - d * arg(z_1)) * sin(d * logsq2 + c * arg(z_1))\n     *\n     */\n\n    if (tIsZero && z['re'] > 0) {\n      // Same behavior as Wolframalpha, Zero if real part is zero\n      return Complex['ZERO'];\n    }\n    const arg = Math.atan2(this['im'], this['re']);\n    const loh = logHypot(this['re'], this['im']);\n    let re = Math.exp(z['re'] * loh - z['im'] * arg);\n    let im = z['im'] * loh + z['re'] * arg;\n    return new Complex(re * Math.cos(im), re * Math.sin(im));\n  },\n  /**\n   * Calculate the complex square root\n   *\n   * @returns {Complex}\n   */\n  'sqrt': function () {\n    const a = this['re'];\n    const b = this['im'];\n    if (b === 0) {\n      // Real number case\n      if (a >= 0) {\n        return new Complex(Math.sqrt(a), 0);\n      } else {\n        return new Complex(0, Math.sqrt(-a));\n      }\n    }\n    const r = hypot(a, b);\n    let re = Math.sqrt(0.5 * (r + Math.abs(a))); // sqrt(2x) / 2 = sqrt(x / 2)\n    let im = Math.abs(b) / (2 * re);\n    if (a >= 0) {\n      return new Complex(re, b < 0 ? -im : im);\n    } else {\n      return new Complex(im, b < 0 ? -re : re);\n    }\n  },\n  /**\n   * Calculate the complex exponent\n   *\n   * @returns {Complex}\n   */\n  'exp': function () {\n    const er = Math.exp(this['re']);\n    if (this['im'] === 0) {\n      return new Complex(er, 0);\n    }\n    return new Complex(er * Math.cos(this['im']), er * Math.sin(this['im']));\n  },\n  /**\n   * Calculate the complex exponent and subtracts one.\n   *\n   * This may be more accurate than `Complex(x).exp().sub(1)` if\n   * `x` is small.\n   *\n   * @returns {Complex}\n   */\n  'expm1': function () {\n    /**\n     * exp(a + i*b) - 1\n     = exp(a) * (cos(b) + j*sin(b)) - 1\n     = expm1(a)*cos(b) + cosm1(b) + j*exp(a)*sin(b)\n     */\n\n    const a = this['re'];\n    const b = this['im'];\n    return new Complex(Math.expm1(a) * Math.cos(b) + cosm1(b), Math.exp(a) * Math.sin(b));\n  },\n  /**\n   * Calculate the natural log\n   *\n   * @returns {Complex}\n   */\n  'log': function () {\n    const a = this['re'];\n    const b = this['im'];\n    if (b === 0 && a > 0) {\n      return new Complex(Math.log(a), 0);\n    }\n    return new Complex(logHypot(a, b), Math.atan2(b, a));\n  },\n  /**\n   * Calculate the magnitude of the complex number\n   *\n   * @returns {number}\n   */\n  'abs': function () {\n    return hypot(this['re'], this['im']);\n  },\n  /**\n   * Calculate the angle of the complex number\n   *\n   * @returns {number}\n   */\n  'arg': function () {\n    return Math.atan2(this['im'], this['re']);\n  },\n  /**\n   * Calculate the sine of the complex number\n   *\n   * @returns {Complex}\n   */\n  'sin': function () {\n    // sin(z) = ( e^iz - e^-iz ) / 2i \n    //        = sin(a)cosh(b) + i cos(a)sinh(b)\n\n    const a = this['re'];\n    const b = this['im'];\n    return new Complex(Math.sin(a) * cosh(b), Math.cos(a) * sinh(b));\n  },\n  /**\n   * Calculate the cosine\n   *\n   * @returns {Complex}\n   */\n  'cos': function () {\n    // cos(z) = ( e^iz + e^-iz ) / 2 \n    //        = cos(a)cosh(b) - i sin(a)sinh(b)\n\n    const a = this['re'];\n    const b = this['im'];\n    return new Complex(Math.cos(a) * cosh(b), -Math.sin(a) * sinh(b));\n  },\n  /**\n   * Calculate the tangent\n   *\n   * @returns {Complex}\n   */\n  'tan': function () {\n    // tan(z) = sin(z) / cos(z) \n    //        = ( e^iz - e^-iz ) / ( i( e^iz + e^-iz ) )\n    //        = ( e^2iz - 1 ) / i( e^2iz + 1 )\n    //        = ( sin(2a) + i sinh(2b) ) / ( cos(2a) + cosh(2b) )\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = Math.cos(a) + cosh(b);\n    return new Complex(Math.sin(a) / d, sinh(b) / d);\n  },\n  /**\n   * Calculate the cotangent\n   *\n   * @returns {Complex}\n   */\n  'cot': function () {\n    // cot(c) = i(e^(ci) + e^(-ci)) / (e^(ci) - e^(-ci))\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = Math.cos(a) - cosh(b);\n    return new Complex(-Math.sin(a) / d, sinh(b) / d);\n  },\n  /**\n   * Calculate the secant\n   *\n   * @returns {Complex}\n   */\n  'sec': function () {\n    // sec(c) = 2 / (e^(ci) + e^(-ci))\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = 0.5 * cosh(2 * b) + 0.5 * Math.cos(2 * a);\n    return new Complex(Math.cos(a) * cosh(b) / d, Math.sin(a) * sinh(b) / d);\n  },\n  /**\n   * Calculate the cosecans\n   *\n   * @returns {Complex}\n   */\n  'csc': function () {\n    // csc(c) = 2i / (e^(ci) - e^(-ci))\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = 0.5 * cosh(2 * b) - 0.5 * Math.cos(2 * a);\n    return new Complex(Math.sin(a) * cosh(b) / d, -Math.cos(a) * sinh(b) / d);\n  },\n  /**\n   * Calculate the complex arcus sinus\n   *\n   * @returns {Complex}\n   */\n  'asin': function () {\n    // asin(c) = -i * log(ci + sqrt(1 - c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n    const t1 = new Complex(b * b - a * a + 1, -2 * a * b)['sqrt']();\n    const t2 = new Complex(t1['re'] - b, t1['im'] + a)['log']();\n    return new Complex(t2['im'], -t2['re']);\n  },\n  /**\n   * Calculate the complex arcus cosinus\n   *\n   * @returns {Complex}\n   */\n  'acos': function () {\n    // acos(c) = i * log(c - i * sqrt(1 - c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n    const t1 = new Complex(b * b - a * a + 1, -2 * a * b)['sqrt']();\n    const t2 = new Complex(t1['re'] - b, t1['im'] + a)['log']();\n    return new Complex(Math.PI / 2 - t2['im'], t2['re']);\n  },\n  /**\n   * Calculate the complex arcus tangent\n   *\n   * @returns {Complex}\n   */\n  'atan': function () {\n    // atan(c) = i / 2 log((i + x) / (i - x))\n\n    const a = this['re'];\n    const b = this['im'];\n    if (a === 0) {\n      if (b === 1) {\n        return new Complex(0, Infinity);\n      }\n      if (b === -1) {\n        return new Complex(0, -Infinity);\n      }\n    }\n    const d = a * a + (1.0 - b) * (1.0 - b);\n    const t1 = new Complex((1 - b * b - a * a) / d, -2 * a / d).log();\n    return new Complex(-0.5 * t1['im'], 0.5 * t1['re']);\n  },\n  /**\n   * Calculate the complex arcus cotangent\n   *\n   * @returns {Complex}\n   */\n  'acot': function () {\n    // acot(c) = i / 2 log((c - i) / (c + i))\n\n    const a = this['re'];\n    const b = this['im'];\n    if (b === 0) {\n      return new Complex(Math.atan2(1, a), 0);\n    }\n    const d = a * a + b * b;\n    return d !== 0 ? new Complex(a / d, -b / d).atan() : new Complex(a !== 0 ? a / 0 : 0, b !== 0 ? -b / 0 : 0).atan();\n  },\n  /**\n   * Calculate the complex arcus secant\n   *\n   * @returns {Complex}\n   */\n  'asec': function () {\n    // asec(c) = -i * log(1 / c + sqrt(1 - i / c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n    if (a === 0 && b === 0) {\n      return new Complex(0, Infinity);\n    }\n    const d = a * a + b * b;\n    return d !== 0 ? new Complex(a / d, -b / d).acos() : new Complex(a !== 0 ? a / 0 : 0, b !== 0 ? -b / 0 : 0).acos();\n  },\n  /**\n   * Calculate the complex arcus cosecans\n   *\n   * @returns {Complex}\n   */\n  'acsc': function () {\n    // acsc(c) = -i * log(i / c + sqrt(1 - 1 / c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n    if (a === 0 && b === 0) {\n      return new Complex(Math.PI / 2, Infinity);\n    }\n    const d = a * a + b * b;\n    return d !== 0 ? new Complex(a / d, -b / d).asin() : new Complex(a !== 0 ? a / 0 : 0, b !== 0 ? -b / 0 : 0).asin();\n  },\n  /**\n   * Calculate the complex sinh\n   *\n   * @returns {Complex}\n   */\n  'sinh': function () {\n    // sinh(c) = (e^c - e^-c) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n    return new Complex(sinh(a) * Math.cos(b), cosh(a) * Math.sin(b));\n  },\n  /**\n   * Calculate the complex cosh\n   *\n   * @returns {Complex}\n   */\n  'cosh': function () {\n    // cosh(c) = (e^c + e^-c) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n    return new Complex(cosh(a) * Math.cos(b), sinh(a) * Math.sin(b));\n  },\n  /**\n   * Calculate the complex tanh\n   *\n   * @returns {Complex}\n   */\n  'tanh': function () {\n    // tanh(c) = (e^c - e^-c) / (e^c + e^-c)\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = cosh(a) + Math.cos(b);\n    return new Complex(sinh(a) / d, Math.sin(b) / d);\n  },\n  /**\n   * Calculate the complex coth\n   *\n   * @returns {Complex}\n   */\n  'coth': function () {\n    // coth(c) = (e^c + e^-c) / (e^c - e^-c)\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = cosh(a) - Math.cos(b);\n    return new Complex(sinh(a) / d, -Math.sin(b) / d);\n  },\n  /**\n   * Calculate the complex coth\n   *\n   * @returns {Complex}\n   */\n  'csch': function () {\n    // csch(c) = 2 / (e^c - e^-c)\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = Math.cos(2 * b) - cosh(2 * a);\n    return new Complex(-2 * sinh(a) * Math.cos(b) / d, 2 * cosh(a) * Math.sin(b) / d);\n  },\n  /**\n   * Calculate the complex sech\n   *\n   * @returns {Complex}\n   */\n  'sech': function () {\n    // sech(c) = 2 / (e^c + e^-c)\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = Math.cos(2 * b) + cosh(2 * a);\n    return new Complex(2 * cosh(a) * Math.cos(b) / d, -2 * sinh(a) * Math.sin(b) / d);\n  },\n  /**\n   * Calculate the complex asinh\n   *\n   * @returns {Complex}\n   */\n  'asinh': function () {\n    // asinh(c) = log(c + sqrt(c^2 + 1))\n\n    let tmp = this['im'];\n    this['im'] = -this['re'];\n    this['re'] = tmp;\n    const res = this['asin']();\n    this['re'] = -this['im'];\n    this['im'] = tmp;\n    tmp = res['re'];\n    res['re'] = -res['im'];\n    res['im'] = tmp;\n    return res;\n  },\n  /**\n   * Calculate the complex acosh\n   *\n   * @returns {Complex}\n   */\n  'acosh': function () {\n    // acosh(c) = log(c + sqrt(c^2 - 1))\n\n    const res = this['acos']();\n    if (res['im'] <= 0) {\n      const tmp = res['re'];\n      res['re'] = -res['im'];\n      res['im'] = tmp;\n    } else {\n      const tmp = res['im'];\n      res['im'] = -res['re'];\n      res['re'] = tmp;\n    }\n    return res;\n  },\n  /**\n   * Calculate the complex atanh\n   *\n   * @returns {Complex}\n   */\n  'atanh': function () {\n    // atanh(c) = log((1+c) / (1-c)) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n    const noIM = a > 1 && b === 0;\n    const oneMinus = 1 - a;\n    const onePlus = 1 + a;\n    const d = oneMinus * oneMinus + b * b;\n    const x = d !== 0 ? new Complex((onePlus * oneMinus - b * b) / d, (b * oneMinus + onePlus * b) / d) : new Complex(a !== -1 ? a / 0 : 0, b !== 0 ? b / 0 : 0);\n    const temp = x['re'];\n    x['re'] = logHypot(x['re'], x['im']) / 2;\n    x['im'] = Math.atan2(x['im'], temp) / 2;\n    if (noIM) {\n      x['im'] = -x['im'];\n    }\n    return x;\n  },\n  /**\n   * Calculate the complex acoth\n   *\n   * @returns {Complex}\n   */\n  'acoth': function () {\n    // acoth(c) = log((c+1) / (c-1)) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n    if (a === 0 && b === 0) {\n      return new Complex(0, Math.PI / 2);\n    }\n    const d = a * a + b * b;\n    return d !== 0 ? new Complex(a / d, -b / d).atanh() : new Complex(a !== 0 ? a / 0 : 0, b !== 0 ? -b / 0 : 0).atanh();\n  },\n  /**\n   * Calculate the complex acsch\n   *\n   * @returns {Complex}\n   */\n  'acsch': function () {\n    // acsch(c) = log((1+sqrt(1+c^2))/c)\n\n    const a = this['re'];\n    const b = this['im'];\n    if (b === 0) {\n      return new Complex(a !== 0 ? Math.log(a + Math.sqrt(a * a + 1)) : Infinity, 0);\n    }\n    const d = a * a + b * b;\n    return d !== 0 ? new Complex(a / d, -b / d).asinh() : new Complex(a !== 0 ? a / 0 : 0, b !== 0 ? -b / 0 : 0).asinh();\n  },\n  /**\n   * Calculate the complex asech\n   *\n   * @returns {Complex}\n   */\n  'asech': function () {\n    // asech(c) = log((1+sqrt(1-c^2))/c)\n\n    const a = this['re'];\n    const b = this['im'];\n    if (this['isZero']()) {\n      return Complex['INFINITY'];\n    }\n    const d = a * a + b * b;\n    return d !== 0 ? new Complex(a / d, -b / d).acosh() : new Complex(a !== 0 ? a / 0 : 0, b !== 0 ? -b / 0 : 0).acosh();\n  },\n  /**\n   * Calculate the complex inverse 1/z\n   *\n   * @returns {Complex}\n   */\n  'inverse': function () {\n    // 1 / 0 = Infinity and 1 / Infinity = 0\n    if (this['isZero']()) {\n      return Complex['INFINITY'];\n    }\n    if (this['isInfinite']()) {\n      return Complex['ZERO'];\n    }\n    const a = this['re'];\n    const b = this['im'];\n    const d = a * a + b * b;\n    return new Complex(a / d, -b / d);\n  },\n  /**\n   * Returns the complex conjugate\n   *\n   * @returns {Complex}\n   */\n  'conjugate': function () {\n    return new Complex(this['re'], -this['im']);\n  },\n  /**\n   * Gets the negated complex number\n   *\n   * @returns {Complex}\n   */\n  'neg': function () {\n    return new Complex(-this['re'], -this['im']);\n  },\n  /**\n   * Ceils the actual complex number\n   *\n   * @returns {Complex}\n   */\n  'ceil': function (places) {\n    places = Math.pow(10, places || 0);\n    return new Complex(Math.ceil(this['re'] * places) / places, Math.ceil(this['im'] * places) / places);\n  },\n  /**\n   * Floors the actual complex number\n   *\n   * @returns {Complex}\n   */\n  'floor': function (places) {\n    places = Math.pow(10, places || 0);\n    return new Complex(Math.floor(this['re'] * places) / places, Math.floor(this['im'] * places) / places);\n  },\n  /**\n   * Ceils the actual complex number\n   *\n   * @returns {Complex}\n   */\n  'round': function (places) {\n    places = Math.pow(10, places || 0);\n    return new Complex(Math.round(this['re'] * places) / places, Math.round(this['im'] * places) / places);\n  },\n  /**\n   * Compares two complex numbers\n   *\n   * **Note:** new Complex(Infinity).equals(Infinity) === false\n   *\n   * @returns {boolean}\n   */\n  'equals': function (a, b) {\n    const z = parse(a, b);\n    return Math.abs(z['re'] - this['re']) <= Complex['EPSILON'] && Math.abs(z['im'] - this['im']) <= Complex['EPSILON'];\n  },\n  /**\n   * Clones the actual object\n   *\n   * @returns {Complex}\n   */\n  'clone': function () {\n    return new Complex(this['re'], this['im']);\n  },\n  /**\n   * Gets a string of the actual complex number\n   *\n   * @returns {string}\n   */\n  'toString': function () {\n    let a = this['re'];\n    let b = this['im'];\n    let ret = \"\";\n    if (this['isNaN']()) {\n      return 'NaN';\n    }\n    if (this['isInfinite']()) {\n      return 'Infinity';\n    }\n    if (Math.abs(a) < Complex['EPSILON']) {\n      a = 0;\n    }\n    if (Math.abs(b) < Complex['EPSILON']) {\n      b = 0;\n    }\n\n    // If is real number\n    if (b === 0) {\n      return ret + a;\n    }\n    if (a !== 0) {\n      ret += a;\n      ret += \" \";\n      if (b < 0) {\n        b = -b;\n        ret += \"-\";\n      } else {\n        ret += \"+\";\n      }\n      ret += \" \";\n    } else if (b < 0) {\n      b = -b;\n      ret += \"-\";\n    }\n    if (1 !== b) {\n      // b is the absolute imaginary part\n      ret += b;\n    }\n    return ret + \"i\";\n  },\n  /**\n   * Returns the actual number as a vector\n   *\n   * @returns {Array}\n   */\n  'toVector': function () {\n    return [this['re'], this['im']];\n  },\n  /**\n   * Returns the actual real value of the current object\n   *\n   * @returns {number|null}\n   */\n  'valueOf': function () {\n    if (this['im'] === 0) {\n      return this['re'];\n    }\n    return null;\n  },\n  /**\n   * Determines whether a complex number is not on the Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isNaN': function () {\n    return isNaN(this['re']) || isNaN(this['im']);\n  },\n  /**\n   * Determines whether or not a complex number is at the zero pole of the\n   * Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isZero': function () {\n    return this['im'] === 0 && this['re'] === 0;\n  },\n  /**\n   * Determines whether a complex number is not at the infinity pole of the\n   * Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isFinite': function () {\n    return isFinite(this['re']) && isFinite(this['im']);\n  },\n  /**\n   * Determines whether or not a complex number is at the infinity pole of the\n   * Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isInfinite': function () {\n    return !this['isFinite']();\n  }\n};\nComplex['ZERO'] = new Complex(0, 0);\nComplex['ONE'] = new Complex(1, 0);\nComplex['I'] = new Complex(0, 1);\nComplex['PI'] = new Complex(Math.PI, 0);\nComplex['E'] = new Complex(Math.E, 0);\nComplex['INFINITY'] = new Complex(Infinity, Infinity);\nComplex['NAN'] = new Complex(NaN, NaN);\nComplex['EPSILON'] = 1e-15;\nexport { Complex as default, Complex };", "map": {"version": 3, "names": ["cosh", "Math", "x", "abs", "exp", "sinh", "cosm1", "b", "PI", "cos", "xx", "hypot", "y", "sqrt", "parser_exit", "SyntaxError", "logHypot", "a", "_a", "_b", "log", "LN2", "P", "parse", "z", "undefined", "isFinite", "Complex", "sin", "length", "tokens", "replace", "match", "plus", "minus", "i", "c", "isNaN", "parseFloat", "prototype", "sign", "add", "tInfin", "zInfin", "sub", "mul", "tIsZero", "zIsZero", "div", "t", "pow", "arg", "atan2", "loh", "re", "im", "r", "er", "expm1", "tan", "d", "cot", "sec", "csc", "asin", "t1", "t2", "acos", "atan", "Infinity", "acot", "asec", "acsc", "tanh", "coth", "csch", "sech", "asinh", "tmp", "res", "acosh", "atanh", "noIM", "oneMinus", "onePlus", "temp", "acoth", "acsch", "asech", "inverse", "conjugate", "neg", "ceil", "places", "floor", "round", "equals", "clone", "toString", "ret", "toVector", "valueOf", "isZero", "isInfinite", "E", "NaN", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/complex.js/dist/complex.mjs"], "sourcesContent": ["'use strict';\n\n/**\n *\n * This class allows the manipulation of complex numbers.\n * You can pass a complex number in different formats. Either as object, double, string or two integer parameters.\n *\n * Object form\n * { re: <real>, im: <imaginary> }\n * { arg: <angle>, abs: <radius> }\n * { phi: <angle>, r: <radius> }\n *\n * Array / Vector form\n * [ real, imaginary ]\n *\n * Double form\n * 99.3 - Single double value\n *\n * String form\n * '23.1337' - Simple real number\n * '15+3i' - a simple complex number\n * '3-i' - a simple complex number\n *\n * Example:\n *\n * const c = new Complex('99.3+8i');\n * c.mul({r: 3, i: 9}).div(4.9).sub(3, 2);\n *\n */\n\n\nconst cosh = Math.cosh || function (x) {\n  return Math.abs(x) < 1e-9 ? 1 - x : (Math.exp(x) + Math.exp(-x)) * 0.5;\n};\n\nconst sinh = Math.sinh || function (x) {\n  return Math.abs(x) < 1e-9 ? x : (Math.exp(x) - Math.exp(-x)) * 0.5;\n};\n\n/**\n * Calculates cos(x) - 1 using Taylor series if x is small (-¼π ≤ x ≤ ¼π).\n *\n * @param {number} x\n * @returns {number} cos(x) - 1\n */\nconst cosm1 = function (x) {\n\n  const b = Math.PI / 4;\n  if (-b > x || x > b) {\n    return Math.cos(x) - 1.0;\n  }\n\n  /* Calculate horner form of polynomial of taylor series in Q\n  let fac = 1, alt = 1, pol = {};\n  for (let i = 0; i <= 16; i++) {\n    fac*= i || 1;\n    if (i % 2 == 0) {\n      pol[i] = new Fraction(1, alt * fac);\n      alt = -alt;\n    }\n  }\n  console.log(new Polynomial(pol).toHorner()); // (((((((1/20922789888000x^2-1/87178291200)x^2+1/*********)x^2-1/3628800)x^2+1/40320)x^2-1/720)x^2+1/24)x^2-1/2)x^2+1\n  */\n\n  const xx = x * x;\n  return xx * (\n    xx * (\n      xx * (\n        xx * (\n          xx * (\n            xx * (\n              xx * (\n                xx / 20922789888000\n                - 1 / 87178291200)\n              + 1 / *********)\n            - 1 / 3628800)\n          + 1 / 40320)\n        - 1 / 720)\n      + 1 / 24)\n    - 1 / 2);\n};\n\nconst hypot = function (x, y) {\n\n  x = Math.abs(x);\n  y = Math.abs(y);\n\n  // Ensure `x` is the larger value\n  if (x < y) [x, y] = [y, x];\n\n  // If both are below the threshold, use straightforward Pythagoras\n  if (x < 1e8) return Math.sqrt(x * x + y * y);\n\n  // For larger values, scale to avoid overflow\n  y /= x;\n  return x * Math.sqrt(1 + y * y);\n};\n\nconst parser_exit = function () {\n  throw SyntaxError('Invalid Param');\n};\n\n/**\n * Calculates log(sqrt(a^2+b^2)) in a way to avoid overflows\n *\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction logHypot(a, b) {\n\n  const _a = Math.abs(a);\n  const _b = Math.abs(b);\n\n  if (a === 0) {\n    return Math.log(_b);\n  }\n\n  if (b === 0) {\n    return Math.log(_a);\n  }\n\n  if (_a < 3000 && _b < 3000) {\n    return Math.log(a * a + b * b) * 0.5;\n  }\n\n  /* I got 4 ideas to compute this property without overflow:\n   *\n   * Testing 1000000 times with random samples for a,b ∈ [1, 1000000000] against a big decimal library to get an error estimate\n   *\n   * 1. Only eliminate the square root: (OVERALL ERROR: 3.9122483030951116e-11)\n\n   Math.log(a * a + b * b) / 2\n\n   *\n   *\n   * 2. Try to use the non-overflowing pythagoras: (OVERALL ERROR: 8.889760039210159e-10)\n\n   const fn = function(a, b) {\n   a = Math.abs(a);\n   b = Math.abs(b);\n   let t = Math.min(a, b);\n   a = Math.max(a, b);\n   t = t / a;\n\n   return Math.log(a) + Math.log(1 + t * t) / 2;\n   };\n\n   * 3. Abuse the identity cos(atan(y/x) = x / sqrt(x^2+y^2): (OVERALL ERROR: 3.4780178737037204e-10)\n\n   Math.log(a / Math.cos(Math.atan2(b, a)))\n\n   * 4. Use 3. and apply log rules: (OVERALL ERROR: 1.2014087502620896e-9)\n\n   Math.log(a) - Math.log(Math.cos(Math.atan2(b, a)))\n\n   */\n\n  a = a * 0.5;\n  b = b * 0.5;\n\n  return 0.5 * Math.log(a * a + b * b) + Math.LN2;\n}\n\nconst P = { 're': 0, 'im': 0 };\nconst parse = function (a, b) {\n\n  const z = P;\n\n  if (a === undefined || a === null) {\n    z['re'] =\n      z['im'] = 0;\n  } else if (b !== undefined) {\n    z['re'] = a;\n    z['im'] = b;\n  } else\n    switch (typeof a) {\n\n      case 'object':\n\n        if ('im' in a && 're' in a) {\n          z['re'] = a['re'];\n          z['im'] = a['im'];\n        } else if ('abs' in a && 'arg' in a) {\n          if (!isFinite(a['abs']) && isFinite(a['arg'])) {\n            return Complex['INFINITY'];\n          }\n          z['re'] = a['abs'] * Math.cos(a['arg']);\n          z['im'] = a['abs'] * Math.sin(a['arg']);\n        } else if ('r' in a && 'phi' in a) {\n          if (!isFinite(a['r']) && isFinite(a['phi'])) {\n            return Complex['INFINITY'];\n          }\n          z['re'] = a['r'] * Math.cos(a['phi']);\n          z['im'] = a['r'] * Math.sin(a['phi']);\n        } else if (a.length === 2) { // Quick array check\n          z['re'] = a[0];\n          z['im'] = a[1];\n        } else {\n          parser_exit();\n        }\n        break;\n\n      case 'string':\n\n        z['im'] = /* void */\n        z['re'] = 0;\n\n        const tokens = a.replace(/_/g, '')\n          .match(/\\d+\\.?\\d*e[+-]?\\d+|\\d+\\.?\\d*|\\.\\d+|./g);\n        let plus = 1;\n        let minus = 0;\n\n        if (tokens === null) {\n          parser_exit();\n        }\n\n        for (let i = 0; i < tokens.length; i++) {\n\n          const c = tokens[i];\n\n          if (c === ' ' || c === '\\t' || c === '\\n') {\n            /* void */\n          } else if (c === '+') {\n            plus++;\n          } else if (c === '-') {\n            minus++;\n          } else if (c === 'i' || c === 'I') {\n\n            if (plus + minus === 0) {\n              parser_exit();\n            }\n\n            if (tokens[i + 1] !== ' ' && !isNaN(tokens[i + 1])) {\n              z['im'] += parseFloat((minus % 2 ? '-' : '') + tokens[i + 1]);\n              i++;\n            } else {\n              z['im'] += parseFloat((minus % 2 ? '-' : '') + '1');\n            }\n            plus = minus = 0;\n\n          } else {\n\n            if (plus + minus === 0 || isNaN(c)) {\n              parser_exit();\n            }\n\n            if (tokens[i + 1] === 'i' || tokens[i + 1] === 'I') {\n              z['im'] += parseFloat((minus % 2 ? '-' : '') + c);\n              i++;\n            } else {\n              z['re'] += parseFloat((minus % 2 ? '-' : '') + c);\n            }\n            plus = minus = 0;\n          }\n        }\n\n        // Still something on the stack\n        if (plus + minus > 0) {\n          parser_exit();\n        }\n        break;\n\n      case 'number':\n        z['im'] = 0;\n        z['re'] = a;\n        break;\n\n      default:\n        parser_exit();\n    }\n\n  if (isNaN(z['re']) || isNaN(z['im'])) {\n    // If a calculation is NaN, we treat it as NaN and don't throw\n    //parser_exit();\n  }\n\n  return z;\n};\n\n/**\n * @constructor\n * @returns {Complex}\n */\nfunction Complex(a, b) {\n\n  if (!(this instanceof Complex)) {\n    return new Complex(a, b);\n  }\n\n  const z = parse(a, b);\n\n  this['re'] = z['re'];\n  this['im'] = z['im'];\n}\n\nComplex.prototype = {\n\n  're': 0,\n  'im': 0,\n\n  /**\n   * Calculates the sign of a complex number, which is a normalized complex\n   *\n   * @returns {Complex}\n   */\n  'sign': function () {\n\n    const abs = hypot(this['re'], this['im']);\n\n    return new Complex(\n      this['re'] / abs,\n      this['im'] / abs);\n  },\n\n  /**\n   * Adds two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'add': function (a, b) {\n\n    const z = parse(a, b);\n\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n\n    if (tInfin || zInfin) {\n\n      if (tInfin && zInfin) {\n        // Infinity + Infinity = NaN\n        return Complex['NAN'];\n      }\n      // Infinity + z = Infinity { where z != Infinity }\n      return Complex['INFINITY'];\n    }\n\n    return new Complex(\n      this['re'] + z['re'],\n      this['im'] + z['im']);\n  },\n\n  /**\n   * Subtracts two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'sub': function (a, b) {\n\n    const z = parse(a, b);\n\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n\n    if (tInfin || zInfin) {\n\n      if (tInfin && zInfin) {\n        // Infinity - Infinity = NaN\n        return Complex['NAN'];\n      }\n      // Infinity - z = Infinity { where z != Infinity }\n      return Complex['INFINITY'];\n    }\n\n    return new Complex(\n      this['re'] - z['re'],\n      this['im'] - z['im']);\n  },\n\n  /**\n   * Multiplies two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'mul': function (a, b) {\n\n    const z = parse(a, b);\n\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n    const tIsZero = this['re'] === 0 && this['im'] === 0;\n    const zIsZero = z['re'] === 0 && z['im'] === 0;\n\n    // Infinity * 0 = NaN\n    if (tInfin && zIsZero || zInfin && tIsZero) {\n      return Complex['NAN'];\n    }\n\n    // Infinity * z = Infinity { where z != 0 }\n    if (tInfin || zInfin) {\n      return Complex['INFINITY'];\n    }\n\n    // Shortcut for real values\n    if (z['im'] === 0 && this['im'] === 0) {\n      return new Complex(this['re'] * z['re'], 0);\n    }\n\n    return new Complex(\n      this['re'] * z['re'] - this['im'] * z['im'],\n      this['re'] * z['im'] + this['im'] * z['re']);\n  },\n\n  /**\n   * Divides two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'div': function (a, b) {\n\n    const z = parse(a, b);\n\n    const tInfin = this['isInfinite']();\n    const zInfin = !(isFinite(z['re']) && isFinite(z['im']));\n    const tIsZero = this['re'] === 0 && this['im'] === 0;\n    const zIsZero = z['re'] === 0 && z['im'] === 0;\n\n    // 0 / 0 = NaN and Infinity / Infinity = NaN\n    if (tIsZero && zIsZero || tInfin && zInfin) {\n      return Complex['NAN'];\n    }\n\n    // Infinity / 0 = Infinity\n    if (zIsZero || tInfin) {\n      return Complex['INFINITY'];\n    }\n\n    // 0 / Infinity = 0\n    if (tIsZero || zInfin) {\n      return Complex['ZERO'];\n    }\n\n    if (0 === z['im']) {\n      // Divisor is real\n      return new Complex(this['re'] / z['re'], this['im'] / z['re']);\n    }\n\n    if (Math.abs(z['re']) < Math.abs(z['im'])) {\n\n      const x = z['re'] / z['im'];\n      const t = z['re'] * x + z['im'];\n\n      return new Complex(\n        (this['re'] * x + this['im']) / t,\n        (this['im'] * x - this['re']) / t);\n\n    } else {\n\n      const x = z['im'] / z['re'];\n      const t = z['im'] * x + z['re'];\n\n      return new Complex(\n        (this['re'] + this['im'] * x) / t,\n        (this['im'] - this['re'] * x) / t);\n    }\n  },\n\n  /**\n   * Calculate the power of two complex numbers\n   *\n   * @returns {Complex}\n   */\n  'pow': function (a, b) {\n\n    const z = parse(a, b);\n\n    const tIsZero = this['re'] === 0 && this['im'] === 0;\n    const zIsZero = z['re'] === 0 && z['im'] === 0;\n\n    if (zIsZero) {\n      return Complex['ONE'];\n    }\n\n    // If the exponent is real\n    if (z['im'] === 0) {\n\n      if (this['im'] === 0 && this['re'] > 0) {\n\n        return new Complex(Math.pow(this['re'], z['re']), 0);\n\n      } else if (this['re'] === 0) { // If base is fully imaginary\n\n        switch ((z['re'] % 4 + 4) % 4) {\n          case 0:\n            return new Complex(Math.pow(this['im'], z['re']), 0);\n          case 1:\n            return new Complex(0, Math.pow(this['im'], z['re']));\n          case 2:\n            return new Complex(-Math.pow(this['im'], z['re']), 0);\n          case 3:\n            return new Complex(0, -Math.pow(this['im'], z['re']));\n        }\n      }\n    }\n\n    /* I couldn't find a good formula, so here is a derivation and optimization\n     *\n     * z_1^z_2 = (a + bi)^(c + di)\n     *         = exp((c + di) * log(a + bi)\n     *         = pow(a^2 + b^2, (c + di) / 2) * exp(i(c + di)atan2(b, a))\n     * =>...\n     * Re = (pow(a^2 + b^2, c / 2) * exp(-d * atan2(b, a))) * cos(d * log(a^2 + b^2) / 2 + c * atan2(b, a))\n     * Im = (pow(a^2 + b^2, c / 2) * exp(-d * atan2(b, a))) * sin(d * log(a^2 + b^2) / 2 + c * atan2(b, a))\n     *\n     * =>...\n     * Re = exp(c * log(sqrt(a^2 + b^2)) - d * atan2(b, a)) * cos(d * log(sqrt(a^2 + b^2)) + c * atan2(b, a))\n     * Im = exp(c * log(sqrt(a^2 + b^2)) - d * atan2(b, a)) * sin(d * log(sqrt(a^2 + b^2)) + c * atan2(b, a))\n     *\n     * =>\n     * Re = exp(c * logsq2 - d * arg(z_1)) * cos(d * logsq2 + c * arg(z_1))\n     * Im = exp(c * logsq2 - d * arg(z_1)) * sin(d * logsq2 + c * arg(z_1))\n     *\n     */\n\n    if (tIsZero && z['re'] > 0) { // Same behavior as Wolframalpha, Zero if real part is zero\n      return Complex['ZERO'];\n    }\n\n    const arg = Math.atan2(this['im'], this['re']);\n    const loh = logHypot(this['re'], this['im']);\n\n    let re = Math.exp(z['re'] * loh - z['im'] * arg);\n    let im = z['im'] * loh + z['re'] * arg;\n    return new Complex(\n      re * Math.cos(im),\n      re * Math.sin(im));\n  },\n\n  /**\n   * Calculate the complex square root\n   *\n   * @returns {Complex}\n   */\n  'sqrt': function () {\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (b === 0) {\n      // Real number case\n      if (a >= 0) {\n        return new Complex(Math.sqrt(a), 0);\n      } else {\n        return new Complex(0, Math.sqrt(-a));\n      }\n    }\n\n    const r = hypot(a, b);\n\n    let re = Math.sqrt(0.5 * (r + Math.abs(a))); // sqrt(2x) / 2 = sqrt(x / 2)\n    let im = Math.abs(b) / (2 * re);\n\n    if (a >= 0) {\n      return new Complex(re, b < 0 ? -im : im);\n    } else {\n      return new Complex(im, b < 0 ? -re : re);\n    }\n  },\n\n  /**\n   * Calculate the complex exponent\n   *\n   * @returns {Complex}\n   */\n  'exp': function () {\n\n    const er = Math.exp(this['re']);\n\n    if (this['im'] === 0) {\n      return new Complex(er, 0);\n    }\n    return new Complex(\n      er * Math.cos(this['im']),\n      er * Math.sin(this['im']));\n  },\n\n  /**\n   * Calculate the complex exponent and subtracts one.\n   *\n   * This may be more accurate than `Complex(x).exp().sub(1)` if\n   * `x` is small.\n   *\n   * @returns {Complex}\n   */\n  'expm1': function () {\n\n    /**\n     * exp(a + i*b) - 1\n     = exp(a) * (cos(b) + j*sin(b)) - 1\n     = expm1(a)*cos(b) + cosm1(b) + j*exp(a)*sin(b)\n     */\n\n    const a = this['re'];\n    const b = this['im'];\n\n    return new Complex(\n      Math.expm1(a) * Math.cos(b) + cosm1(b),\n      Math.exp(a) * Math.sin(b));\n  },\n\n  /**\n   * Calculate the natural log\n   *\n   * @returns {Complex}\n   */\n  'log': function () {\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (b === 0 && a > 0) {\n      return new Complex(Math.log(a), 0);\n    }\n\n    return new Complex(\n      logHypot(a, b),\n      Math.atan2(b, a));\n  },\n\n  /**\n   * Calculate the magnitude of the complex number\n   *\n   * @returns {number}\n   */\n  'abs': function () {\n\n    return hypot(this['re'], this['im']);\n  },\n\n  /**\n   * Calculate the angle of the complex number\n   *\n   * @returns {number}\n   */\n  'arg': function () {\n\n    return Math.atan2(this['im'], this['re']);\n  },\n\n  /**\n   * Calculate the sine of the complex number\n   *\n   * @returns {Complex}\n   */\n  'sin': function () {\n\n    // sin(z) = ( e^iz - e^-iz ) / 2i \n    //        = sin(a)cosh(b) + i cos(a)sinh(b)\n\n    const a = this['re'];\n    const b = this['im'];\n\n    return new Complex(\n      Math.sin(a) * cosh(b),\n      Math.cos(a) * sinh(b));\n  },\n\n  /**\n   * Calculate the cosine\n   *\n   * @returns {Complex}\n   */\n  'cos': function () {\n\n    // cos(z) = ( e^iz + e^-iz ) / 2 \n    //        = cos(a)cosh(b) - i sin(a)sinh(b)\n\n    const a = this['re'];\n    const b = this['im'];\n\n    return new Complex(\n      Math.cos(a) * cosh(b),\n      -Math.sin(a) * sinh(b));\n  },\n\n  /**\n   * Calculate the tangent\n   *\n   * @returns {Complex}\n   */\n  'tan': function () {\n\n    // tan(z) = sin(z) / cos(z) \n    //        = ( e^iz - e^-iz ) / ( i( e^iz + e^-iz ) )\n    //        = ( e^2iz - 1 ) / i( e^2iz + 1 )\n    //        = ( sin(2a) + i sinh(2b) ) / ( cos(2a) + cosh(2b) )\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = Math.cos(a) + cosh(b);\n\n    return new Complex(\n      Math.sin(a) / d,\n      sinh(b) / d);\n  },\n\n  /**\n   * Calculate the cotangent\n   *\n   * @returns {Complex}\n   */\n  'cot': function () {\n\n    // cot(c) = i(e^(ci) + e^(-ci)) / (e^(ci) - e^(-ci))\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = Math.cos(a) - cosh(b);\n\n    return new Complex(\n      -Math.sin(a) / d,\n      sinh(b) / d);\n  },\n\n  /**\n   * Calculate the secant\n   *\n   * @returns {Complex}\n   */\n  'sec': function () {\n\n    // sec(c) = 2 / (e^(ci) + e^(-ci))\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = 0.5 * cosh(2 * b) + 0.5 * Math.cos(2 * a);\n\n    return new Complex(\n      Math.cos(a) * cosh(b) / d,\n      Math.sin(a) * sinh(b) / d);\n  },\n\n  /**\n   * Calculate the cosecans\n   *\n   * @returns {Complex}\n   */\n  'csc': function () {\n\n    // csc(c) = 2i / (e^(ci) - e^(-ci))\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = 0.5 * cosh(2 * b) - 0.5 * Math.cos(2 * a);\n\n    return new Complex(\n      Math.sin(a) * cosh(b) / d,\n      -Math.cos(a) * sinh(b) / d);\n  },\n\n  /**\n   * Calculate the complex arcus sinus\n   *\n   * @returns {Complex}\n   */\n  'asin': function () {\n\n    // asin(c) = -i * log(ci + sqrt(1 - c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n\n    const t1 = new Complex(\n      b * b - a * a + 1,\n      -2 * a * b)['sqrt']();\n\n    const t2 = new Complex(\n      t1['re'] - b,\n      t1['im'] + a)['log']();\n\n    return new Complex(t2['im'], -t2['re']);\n  },\n\n  /**\n   * Calculate the complex arcus cosinus\n   *\n   * @returns {Complex}\n   */\n  'acos': function () {\n\n    // acos(c) = i * log(c - i * sqrt(1 - c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n\n    const t1 = new Complex(\n      b * b - a * a + 1,\n      -2 * a * b)['sqrt']();\n\n    const t2 = new Complex(\n      t1['re'] - b,\n      t1['im'] + a)['log']();\n\n    return new Complex(Math.PI / 2 - t2['im'], t2['re']);\n  },\n\n  /**\n   * Calculate the complex arcus tangent\n   *\n   * @returns {Complex}\n   */\n  'atan': function () {\n\n    // atan(c) = i / 2 log((i + x) / (i - x))\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (a === 0) {\n\n      if (b === 1) {\n        return new Complex(0, Infinity);\n      }\n\n      if (b === -1) {\n        return new Complex(0, -Infinity);\n      }\n    }\n\n    const d = a * a + (1.0 - b) * (1.0 - b);\n\n    const t1 = new Complex(\n      (1 - b * b - a * a) / d,\n      -2 * a / d).log();\n\n    return new Complex(-0.5 * t1['im'], 0.5 * t1['re']);\n  },\n\n  /**\n   * Calculate the complex arcus cotangent\n   *\n   * @returns {Complex}\n   */\n  'acot': function () {\n\n    // acot(c) = i / 2 log((c - i) / (c + i))\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (b === 0) {\n      return new Complex(Math.atan2(1, a), 0);\n    }\n\n    const d = a * a + b * b;\n    return (d !== 0)\n      ? new Complex(\n        a / d,\n        -b / d).atan()\n      : new Complex(\n        (a !== 0) ? a / 0 : 0,\n        (b !== 0) ? -b / 0 : 0).atan();\n  },\n\n  /**\n   * Calculate the complex arcus secant\n   *\n   * @returns {Complex}\n   */\n  'asec': function () {\n\n    // asec(c) = -i * log(1 / c + sqrt(1 - i / c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (a === 0 && b === 0) {\n      return new Complex(0, Infinity);\n    }\n\n    const d = a * a + b * b;\n    return (d !== 0)\n      ? new Complex(\n        a / d,\n        -b / d).acos()\n      : new Complex(\n        (a !== 0) ? a / 0 : 0,\n        (b !== 0) ? -b / 0 : 0).acos();\n  },\n\n  /**\n   * Calculate the complex arcus cosecans\n   *\n   * @returns {Complex}\n   */\n  'acsc': function () {\n\n    // acsc(c) = -i * log(i / c + sqrt(1 - 1 / c^2))\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (a === 0 && b === 0) {\n      return new Complex(Math.PI / 2, Infinity);\n    }\n\n    const d = a * a + b * b;\n    return (d !== 0)\n      ? new Complex(\n        a / d,\n        -b / d).asin()\n      : new Complex(\n        (a !== 0) ? a / 0 : 0,\n        (b !== 0) ? -b / 0 : 0).asin();\n  },\n\n  /**\n   * Calculate the complex sinh\n   *\n   * @returns {Complex}\n   */\n  'sinh': function () {\n\n    // sinh(c) = (e^c - e^-c) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n\n    return new Complex(\n      sinh(a) * Math.cos(b),\n      cosh(a) * Math.sin(b));\n  },\n\n  /**\n   * Calculate the complex cosh\n   *\n   * @returns {Complex}\n   */\n  'cosh': function () {\n\n    // cosh(c) = (e^c + e^-c) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n\n    return new Complex(\n      cosh(a) * Math.cos(b),\n      sinh(a) * Math.sin(b));\n  },\n\n  /**\n   * Calculate the complex tanh\n   *\n   * @returns {Complex}\n   */\n  'tanh': function () {\n\n    // tanh(c) = (e^c - e^-c) / (e^c + e^-c)\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = cosh(a) + Math.cos(b);\n\n    return new Complex(\n      sinh(a) / d,\n      Math.sin(b) / d);\n  },\n\n  /**\n   * Calculate the complex coth\n   *\n   * @returns {Complex}\n   */\n  'coth': function () {\n\n    // coth(c) = (e^c + e^-c) / (e^c - e^-c)\n\n    const a = 2 * this['re'];\n    const b = 2 * this['im'];\n    const d = cosh(a) - Math.cos(b);\n\n    return new Complex(\n      sinh(a) / d,\n      -Math.sin(b) / d);\n  },\n\n  /**\n   * Calculate the complex coth\n   *\n   * @returns {Complex}\n   */\n  'csch': function () {\n\n    // csch(c) = 2 / (e^c - e^-c)\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = Math.cos(2 * b) - cosh(2 * a);\n\n    return new Complex(\n      -2 * sinh(a) * Math.cos(b) / d,\n      2 * cosh(a) * Math.sin(b) / d);\n  },\n\n  /**\n   * Calculate the complex sech\n   *\n   * @returns {Complex}\n   */\n  'sech': function () {\n\n    // sech(c) = 2 / (e^c + e^-c)\n\n    const a = this['re'];\n    const b = this['im'];\n    const d = Math.cos(2 * b) + cosh(2 * a);\n\n    return new Complex(\n      2 * cosh(a) * Math.cos(b) / d,\n      -2 * sinh(a) * Math.sin(b) / d);\n  },\n\n  /**\n   * Calculate the complex asinh\n   *\n   * @returns {Complex}\n   */\n  'asinh': function () {\n\n    // asinh(c) = log(c + sqrt(c^2 + 1))\n\n    let tmp = this['im'];\n    this['im'] = -this['re'];\n    this['re'] = tmp;\n    const res = this['asin']();\n\n    this['re'] = -this['im'];\n    this['im'] = tmp;\n    tmp = res['re'];\n\n    res['re'] = -res['im'];\n    res['im'] = tmp;\n    return res;\n  },\n\n  /**\n   * Calculate the complex acosh\n   *\n   * @returns {Complex}\n   */\n  'acosh': function () {\n\n    // acosh(c) = log(c + sqrt(c^2 - 1))\n\n    const res = this['acos']();\n    if (res['im'] <= 0) {\n      const tmp = res['re'];\n      res['re'] = -res['im'];\n      res['im'] = tmp;\n    } else {\n      const tmp = res['im'];\n      res['im'] = -res['re'];\n      res['re'] = tmp;\n    }\n    return res;\n  },\n\n  /**\n   * Calculate the complex atanh\n   *\n   * @returns {Complex}\n   */\n  'atanh': function () {\n\n    // atanh(c) = log((1+c) / (1-c)) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n\n    const noIM = a > 1 && b === 0;\n    const oneMinus = 1 - a;\n    const onePlus = 1 + a;\n    const d = oneMinus * oneMinus + b * b;\n\n    const x = (d !== 0)\n      ? new Complex(\n        (onePlus * oneMinus - b * b) / d,\n        (b * oneMinus + onePlus * b) / d)\n      : new Complex(\n        (a !== -1) ? (a / 0) : 0,\n        (b !== 0) ? (b / 0) : 0);\n\n    const temp = x['re'];\n    x['re'] = logHypot(x['re'], x['im']) / 2;\n    x['im'] = Math.atan2(x['im'], temp) / 2;\n    if (noIM) {\n      x['im'] = -x['im'];\n    }\n    return x;\n  },\n\n  /**\n   * Calculate the complex acoth\n   *\n   * @returns {Complex}\n   */\n  'acoth': function () {\n\n    // acoth(c) = log((c+1) / (c-1)) / 2\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (a === 0 && b === 0) {\n      return new Complex(0, Math.PI / 2);\n    }\n\n    const d = a * a + b * b;\n    return (d !== 0)\n      ? new Complex(\n        a / d,\n        -b / d).atanh()\n      : new Complex(\n        (a !== 0) ? a / 0 : 0,\n        (b !== 0) ? -b / 0 : 0).atanh();\n  },\n\n  /**\n   * Calculate the complex acsch\n   *\n   * @returns {Complex}\n   */\n  'acsch': function () {\n\n    // acsch(c) = log((1+sqrt(1+c^2))/c)\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (b === 0) {\n\n      return new Complex(\n        (a !== 0)\n          ? Math.log(a + Math.sqrt(a * a + 1))\n          : Infinity, 0);\n    }\n\n    const d = a * a + b * b;\n    return (d !== 0)\n      ? new Complex(\n        a / d,\n        -b / d).asinh()\n      : new Complex(\n        (a !== 0) ? a / 0 : 0,\n        (b !== 0) ? -b / 0 : 0).asinh();\n  },\n\n  /**\n   * Calculate the complex asech\n   *\n   * @returns {Complex}\n   */\n  'asech': function () {\n\n    // asech(c) = log((1+sqrt(1-c^2))/c)\n\n    const a = this['re'];\n    const b = this['im'];\n\n    if (this['isZero']()) {\n      return Complex['INFINITY'];\n    }\n\n    const d = a * a + b * b;\n    return (d !== 0)\n      ? new Complex(\n        a / d,\n        -b / d).acosh()\n      : new Complex(\n        (a !== 0) ? a / 0 : 0,\n        (b !== 0) ? -b / 0 : 0).acosh();\n  },\n\n  /**\n   * Calculate the complex inverse 1/z\n   *\n   * @returns {Complex}\n   */\n  'inverse': function () {\n\n    // 1 / 0 = Infinity and 1 / Infinity = 0\n    if (this['isZero']()) {\n      return Complex['INFINITY'];\n    }\n\n    if (this['isInfinite']()) {\n      return Complex['ZERO'];\n    }\n\n    const a = this['re'];\n    const b = this['im'];\n\n    const d = a * a + b * b;\n\n    return new Complex(a / d, -b / d);\n  },\n\n  /**\n   * Returns the complex conjugate\n   *\n   * @returns {Complex}\n   */\n  'conjugate': function () {\n\n    return new Complex(this['re'], -this['im']);\n  },\n\n  /**\n   * Gets the negated complex number\n   *\n   * @returns {Complex}\n   */\n  'neg': function () {\n\n    return new Complex(-this['re'], -this['im']);\n  },\n\n  /**\n   * Ceils the actual complex number\n   *\n   * @returns {Complex}\n   */\n  'ceil': function (places) {\n\n    places = Math.pow(10, places || 0);\n\n    return new Complex(\n      Math.ceil(this['re'] * places) / places,\n      Math.ceil(this['im'] * places) / places);\n  },\n\n  /**\n   * Floors the actual complex number\n   *\n   * @returns {Complex}\n   */\n  'floor': function (places) {\n\n    places = Math.pow(10, places || 0);\n\n    return new Complex(\n      Math.floor(this['re'] * places) / places,\n      Math.floor(this['im'] * places) / places);\n  },\n\n  /**\n   * Ceils the actual complex number\n   *\n   * @returns {Complex}\n   */\n  'round': function (places) {\n\n    places = Math.pow(10, places || 0);\n\n    return new Complex(\n      Math.round(this['re'] * places) / places,\n      Math.round(this['im'] * places) / places);\n  },\n\n  /**\n   * Compares two complex numbers\n   *\n   * **Note:** new Complex(Infinity).equals(Infinity) === false\n   *\n   * @returns {boolean}\n   */\n  'equals': function (a, b) {\n\n    const z = parse(a, b);\n\n    return Math.abs(z['re'] - this['re']) <= Complex['EPSILON'] &&\n      Math.abs(z['im'] - this['im']) <= Complex['EPSILON'];\n  },\n\n  /**\n   * Clones the actual object\n   *\n   * @returns {Complex}\n   */\n  'clone': function () {\n\n    return new Complex(this['re'], this['im']);\n  },\n\n  /**\n   * Gets a string of the actual complex number\n   *\n   * @returns {string}\n   */\n  'toString': function () {\n\n    let a = this['re'];\n    let b = this['im'];\n    let ret = \"\";\n\n    if (this['isNaN']()) {\n      return 'NaN';\n    }\n\n    if (this['isInfinite']()) {\n      return 'Infinity';\n    }\n\n    if (Math.abs(a) < Complex['EPSILON']) {\n      a = 0;\n    }\n\n    if (Math.abs(b) < Complex['EPSILON']) {\n      b = 0;\n    }\n\n    // If is real number\n    if (b === 0) {\n      return ret + a;\n    }\n\n    if (a !== 0) {\n      ret += a;\n      ret += \" \";\n      if (b < 0) {\n        b = -b;\n        ret += \"-\";\n      } else {\n        ret += \"+\";\n      }\n      ret += \" \";\n    } else if (b < 0) {\n      b = -b;\n      ret += \"-\";\n    }\n\n    if (1 !== b) { // b is the absolute imaginary part\n      ret += b;\n    }\n    return ret + \"i\";\n  },\n\n  /**\n   * Returns the actual number as a vector\n   *\n   * @returns {Array}\n   */\n  'toVector': function () {\n\n    return [this['re'], this['im']];\n  },\n\n  /**\n   * Returns the actual real value of the current object\n   *\n   * @returns {number|null}\n   */\n  'valueOf': function () {\n\n    if (this['im'] === 0) {\n      return this['re'];\n    }\n    return null;\n  },\n\n  /**\n   * Determines whether a complex number is not on the Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isNaN': function () {\n    return isNaN(this['re']) || isNaN(this['im']);\n  },\n\n  /**\n   * Determines whether or not a complex number is at the zero pole of the\n   * Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isZero': function () {\n    return this['im'] === 0 && this['re'] === 0;\n  },\n\n  /**\n   * Determines whether a complex number is not at the infinity pole of the\n   * Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isFinite': function () {\n    return isFinite(this['re']) && isFinite(this['im']);\n  },\n\n  /**\n   * Determines whether or not a complex number is at the infinity pole of the\n   * Riemann sphere.\n   *\n   * @returns {boolean}\n   */\n  'isInfinite': function () {\n    return !this['isFinite']();\n  }\n};\n\nComplex['ZERO'] = new Complex(0, 0);\nComplex['ONE'] = new Complex(1, 0);\nComplex['I'] = new Complex(0, 1);\nComplex['PI'] = new Complex(Math.PI, 0);\nComplex['E'] = new Complex(Math.E, 0);\nComplex['INFINITY'] = new Complex(Infinity, Infinity);\nComplex['NAN'] = new Complex(NaN, NaN);\nComplex['EPSILON'] = 1e-15;\nexport {\n  Complex as default, Complex\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,MAAMA,IAAI,GAAGC,IAAI,CAACD,IAAI,IAAI,UAAUE,CAAC,EAAE;EACrC,OAAOD,IAAI,CAACE,GAAG,CAACD,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,GAAGA,CAAC,GAAG,CAACD,IAAI,CAACG,GAAG,CAACF,CAAC,CAAC,GAAGD,IAAI,CAACG,GAAG,CAAC,CAACF,CAAC,CAAC,IAAI,GAAG;AACxE,CAAC;AAED,MAAMG,IAAI,GAAGJ,IAAI,CAACI,IAAI,IAAI,UAAUH,CAAC,EAAE;EACrC,OAAOD,IAAI,CAACE,GAAG,CAACD,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAACD,IAAI,CAACG,GAAG,CAACF,CAAC,CAAC,GAAGD,IAAI,CAACG,GAAG,CAAC,CAACF,CAAC,CAAC,IAAI,GAAG;AACpE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,KAAK,GAAG,SAAAA,CAAUJ,CAAC,EAAE;EAEzB,MAAMK,CAAC,GAAGN,IAAI,CAACO,EAAE,GAAG,CAAC;EACrB,IAAI,CAACD,CAAC,GAAGL,CAAC,IAAIA,CAAC,GAAGK,CAAC,EAAE;IACnB,OAAON,IAAI,CAACQ,GAAG,CAACP,CAAC,CAAC,GAAG,GAAG;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAMQ,EAAE,GAAGR,CAAC,GAAGA,CAAC;EAChB,OAAOQ,EAAE,IACPA,EAAE,IACAA,EAAE,IACAA,EAAE,IACAA,EAAE,IACAA,EAAE,IACAA,EAAE,IACAA,EAAE,GAAG,cAAc,GACjB,CAAC,GAAG,WAAW,CAAC,GAClB,CAAC,GAAG,SAAS,CAAC,GAChB,CAAC,GAAG,OAAO,CAAC,GACd,CAAC,GAAG,KAAK,CAAC,GACZ,CAAC,GAAG,GAAG,CAAC,GACV,CAAC,GAAG,EAAE,CAAC,GACT,CAAC,GAAG,CAAC,CAAC;AACZ,CAAC;AAED,MAAMC,KAAK,GAAG,SAAAA,CAAUT,CAAC,EAAEU,CAAC,EAAE;EAE5BV,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACD,CAAC,CAAC;EACfU,CAAC,GAAGX,IAAI,CAACE,GAAG,CAACS,CAAC,CAAC;;EAEf;EACA,IAAIV,CAAC,GAAGU,CAAC,EAAE,CAACV,CAAC,EAAEU,CAAC,CAAC,GAAG,CAACA,CAAC,EAAEV,CAAC,CAAC;;EAE1B;EACA,IAAIA,CAAC,GAAG,GAAG,EAAE,OAAOD,IAAI,CAACY,IAAI,CAACX,CAAC,GAAGA,CAAC,GAAGU,CAAC,GAAGA,CAAC,CAAC;;EAE5C;EACAA,CAAC,IAAIV,CAAC;EACN,OAAOA,CAAC,GAAGD,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGD,CAAC,GAAGA,CAAC,CAAC;AACjC,CAAC;AAED,MAAME,WAAW,GAAG,SAAAA,CAAA,EAAY;EAC9B,MAAMC,WAAW,CAAC,eAAe,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,CAAC,EAAEV,CAAC,EAAE;EAEtB,MAAMW,EAAE,GAAGjB,IAAI,CAACE,GAAG,CAACc,CAAC,CAAC;EACtB,MAAME,EAAE,GAAGlB,IAAI,CAACE,GAAG,CAACI,CAAC,CAAC;EAEtB,IAAIU,CAAC,KAAK,CAAC,EAAE;IACX,OAAOhB,IAAI,CAACmB,GAAG,CAACD,EAAE,CAAC;EACrB;EAEA,IAAIZ,CAAC,KAAK,CAAC,EAAE;IACX,OAAON,IAAI,CAACmB,GAAG,CAACF,EAAE,CAAC;EACrB;EAEA,IAAIA,EAAE,GAAG,IAAI,IAAIC,EAAE,GAAG,IAAI,EAAE;IAC1B,OAAOlB,IAAI,CAACmB,GAAG,CAACH,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC,CAAC,GAAG,GAAG;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAWEU,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXV,CAAC,GAAGA,CAAC,GAAG,GAAG;EAEX,OAAO,GAAG,GAAGN,IAAI,CAACmB,GAAG,CAACH,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC,CAAC,GAAGN,IAAI,CAACoB,GAAG;AACjD;AAEA,MAAMC,CAAC,GAAG;EAAE,IAAI,EAAE,CAAC;EAAE,IAAI,EAAE;AAAE,CAAC;AAC9B,MAAMC,KAAK,GAAG,SAAAA,CAAUN,CAAC,EAAEV,CAAC,EAAE;EAE5B,MAAMiB,CAAC,GAAGF,CAAC;EAEX,IAAIL,CAAC,KAAKQ,SAAS,IAAIR,CAAC,KAAK,IAAI,EAAE;IACjCO,CAAC,CAAC,IAAI,CAAC,GACLA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACf,CAAC,MAAM,IAAIjB,CAAC,KAAKkB,SAAS,EAAE;IAC1BD,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC;IACXO,CAAC,CAAC,IAAI,CAAC,GAAGjB,CAAC;EACb,CAAC,MACC,QAAQ,OAAOU,CAAC;IAEd,KAAK,QAAQ;MAEX,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,IAAIA,CAAC,EAAE;QAC1BO,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,IAAI,CAAC;QACjBO,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,IAAI,CAAC;MACnB,CAAC,MAAM,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,IAAIA,CAAC,EAAE;QACnC,IAAI,CAACS,QAAQ,CAACT,CAAC,CAAC,KAAK,CAAC,CAAC,IAAIS,QAAQ,CAACT,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAOU,OAAO,CAAC,UAAU,CAAC;QAC5B;QACAH,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,KAAK,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,KAAK,CAAC,CAAC;QACvCO,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,KAAK,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,KAAK,CAAC,CAAC;MACzC,CAAC,MAAM,IAAI,GAAG,IAAIA,CAAC,IAAI,KAAK,IAAIA,CAAC,EAAE;QACjC,IAAI,CAACS,QAAQ,CAACT,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIS,QAAQ,CAACT,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;UAC3C,OAAOU,OAAO,CAAC,UAAU,CAAC;QAC5B;QACAH,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,GAAG,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,KAAK,CAAC,CAAC;QACrCO,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,GAAG,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,KAAK,CAAC,CAAC;MACvC,CAAC,MAAM,IAAIA,CAAC,CAACY,MAAM,KAAK,CAAC,EAAE;QAAE;QAC3BL,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,CAAC,CAAC;QACdO,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACLH,WAAW,CAAC,CAAC;MACf;MACA;IAEF,KAAK,QAAQ;MAEXU,CAAC,CAAC,IAAI,CAAC,GAAG;MACVA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;MAEX,MAAMM,MAAM,GAAGb,CAAC,CAACc,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAC/BC,KAAK,CAAC,uCAAuC,CAAC;MACjD,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAIC,KAAK,GAAG,CAAC;MAEb,IAAIJ,MAAM,KAAK,IAAI,EAAE;QACnBhB,WAAW,CAAC,CAAC;MACf;MAEA,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACD,MAAM,EAAEM,CAAC,EAAE,EAAE;QAEtC,MAAMC,CAAC,GAAGN,MAAM,CAACK,CAAC,CAAC;QAEnB,IAAIC,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,IAAI,EAAE;UACzC;QAAA,CACD,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;UACpBH,IAAI,EAAE;QACR,CAAC,MAAM,IAAIG,CAAC,KAAK,GAAG,EAAE;UACpBF,KAAK,EAAE;QACT,CAAC,MAAM,IAAIE,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;UAEjC,IAAIH,IAAI,GAAGC,KAAK,KAAK,CAAC,EAAE;YACtBpB,WAAW,CAAC,CAAC;UACf;UAEA,IAAIgB,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAACE,KAAK,CAACP,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAClDX,CAAC,CAAC,IAAI,CAAC,IAAIc,UAAU,CAAC,CAACJ,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIJ,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7DA,CAAC,EAAE;UACL,CAAC,MAAM;YACLX,CAAC,CAAC,IAAI,CAAC,IAAIc,UAAU,CAAC,CAACJ,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC;UACrD;UACAD,IAAI,GAAGC,KAAK,GAAG,CAAC;QAElB,CAAC,MAAM;UAEL,IAAID,IAAI,GAAGC,KAAK,KAAK,CAAC,IAAIG,KAAK,CAACD,CAAC,CAAC,EAAE;YAClCtB,WAAW,CAAC,CAAC;UACf;UAEA,IAAIgB,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAIL,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAClDX,CAAC,CAAC,IAAI,CAAC,IAAIc,UAAU,CAAC,CAACJ,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIE,CAAC,CAAC;YACjDD,CAAC,EAAE;UACL,CAAC,MAAM;YACLX,CAAC,CAAC,IAAI,CAAC,IAAIc,UAAU,CAAC,CAACJ,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIE,CAAC,CAAC;UACnD;UACAH,IAAI,GAAGC,KAAK,GAAG,CAAC;QAClB;MACF;;MAEA;MACA,IAAID,IAAI,GAAGC,KAAK,GAAG,CAAC,EAAE;QACpBpB,WAAW,CAAC,CAAC;MACf;MACA;IAEF,KAAK,QAAQ;MACXU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;MACXA,CAAC,CAAC,IAAI,CAAC,GAAGP,CAAC;MACX;IAEF;MACEH,WAAW,CAAC,CAAC;EACjB;EAEF,IAAIuB,KAAK,CAACb,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIa,KAAK,CAACb,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;IACpC;IACA;EAAA;EAGF,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASG,OAAOA,CAACV,CAAC,EAAEV,CAAC,EAAE;EAErB,IAAI,EAAE,IAAI,YAAYoB,OAAO,CAAC,EAAE;IAC9B,OAAO,IAAIA,OAAO,CAACV,CAAC,EAAEV,CAAC,CAAC;EAC1B;EAEA,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;EAErB,IAAI,CAAC,IAAI,CAAC,GAAGiB,CAAC,CAAC,IAAI,CAAC;EACpB,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC;AACtB;AAEAG,OAAO,CAACY,SAAS,GAAG;EAElB,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EAEP;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAC,CAAA,EAAY;IAElB,MAAMrC,GAAG,GAAGQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAEzC,OAAO,IAAIgB,OAAO,CAChB,IAAI,CAAC,IAAI,CAAC,GAAGxB,GAAG,EAChB,IAAI,CAAC,IAAI,CAAC,GAAGA,GAAG,CAAC;EACrB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAsC,CAAUxB,CAAC,EAAEV,CAAC,EAAE;IAErB,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;IAErB,MAAMmC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IACnC,MAAMC,MAAM,GAAG,EAAEjB,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIE,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAExD,IAAIkB,MAAM,IAAIC,MAAM,EAAE;MAEpB,IAAID,MAAM,IAAIC,MAAM,EAAE;QACpB;QACA,OAAOhB,OAAO,CAAC,KAAK,CAAC;MACvB;MACA;MACA,OAAOA,OAAO,CAAC,UAAU,CAAC;IAC5B;IAEA,OAAO,IAAIA,OAAO,CAChB,IAAI,CAAC,IAAI,CAAC,GAAGH,CAAC,CAAC,IAAI,CAAC,EACpB,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC,CAAC;EACzB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAoB,CAAU3B,CAAC,EAAEV,CAAC,EAAE;IAErB,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;IAErB,MAAMmC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IACnC,MAAMC,MAAM,GAAG,EAAEjB,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIE,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAExD,IAAIkB,MAAM,IAAIC,MAAM,EAAE;MAEpB,IAAID,MAAM,IAAIC,MAAM,EAAE;QACpB;QACA,OAAOhB,OAAO,CAAC,KAAK,CAAC;MACvB;MACA;MACA,OAAOA,OAAO,CAAC,UAAU,CAAC;IAC5B;IAEA,OAAO,IAAIA,OAAO,CAChB,IAAI,CAAC,IAAI,CAAC,GAAGH,CAAC,CAAC,IAAI,CAAC,EACpB,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC,CAAC;EACzB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAqB,CAAU5B,CAAC,EAAEV,CAAC,EAAE;IAErB,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;IAErB,MAAMmC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IACnC,MAAMC,MAAM,GAAG,EAAEjB,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIE,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,MAAMsB,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IACpD,MAAMC,OAAO,GAAGvB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;;IAE9C;IACA,IAAIkB,MAAM,IAAIK,OAAO,IAAIJ,MAAM,IAAIG,OAAO,EAAE;MAC1C,OAAOnB,OAAO,CAAC,KAAK,CAAC;IACvB;;IAEA;IACA,IAAIe,MAAM,IAAIC,MAAM,EAAE;MACpB,OAAOhB,OAAO,CAAC,UAAU,CAAC;IAC5B;;IAEA;IACA,IAAIH,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACrC,OAAO,IAAIG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGH,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7C;IAEA,OAAO,IAAIG,OAAO,CAChB,IAAI,CAAC,IAAI,CAAC,GAAGH,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC,EAC3C,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC,CAAC;EAChD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAwB,CAAU/B,CAAC,EAAEV,CAAC,EAAE;IAErB,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;IAErB,MAAMmC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IACnC,MAAMC,MAAM,GAAG,EAAEjB,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIE,QAAQ,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,MAAMsB,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IACpD,MAAMC,OAAO,GAAGvB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;;IAE9C;IACA,IAAIsB,OAAO,IAAIC,OAAO,IAAIL,MAAM,IAAIC,MAAM,EAAE;MAC1C,OAAOhB,OAAO,CAAC,KAAK,CAAC;IACvB;;IAEA;IACA,IAAIoB,OAAO,IAAIL,MAAM,EAAE;MACrB,OAAOf,OAAO,CAAC,UAAU,CAAC;IAC5B;;IAEA;IACA,IAAImB,OAAO,IAAIH,MAAM,EAAE;MACrB,OAAOhB,OAAO,CAAC,MAAM,CAAC;IACxB;IAEA,IAAI,CAAC,KAAKH,CAAC,CAAC,IAAI,CAAC,EAAE;MACjB;MACA,OAAO,IAAIG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGH,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC,CAAC;IAChE;IAEA,IAAIvB,IAAI,CAACE,GAAG,CAACqB,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGvB,IAAI,CAACE,GAAG,CAACqB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;MAEzC,MAAMtB,CAAC,GAAGsB,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC;MAC3B,MAAMyB,CAAC,GAAGzB,CAAC,CAAC,IAAI,CAAC,GAAGtB,CAAC,GAAGsB,CAAC,CAAC,IAAI,CAAC;MAE/B,OAAO,IAAIG,OAAO,CAChB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGzB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI+C,CAAC,EACjC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG/C,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI+C,CAAC,CAAC;IAEtC,CAAC,MAAM;MAEL,MAAM/C,CAAC,GAAGsB,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAI,CAAC;MAC3B,MAAMyB,CAAC,GAAGzB,CAAC,CAAC,IAAI,CAAC,GAAGtB,CAAC,GAAGsB,CAAC,CAAC,IAAI,CAAC;MAE/B,OAAO,IAAIG,OAAO,CAChB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAGzB,CAAC,IAAI+C,CAAC,EACjC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG/C,CAAC,IAAI+C,CAAC,CAAC;IACtC;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAC,CAAUjC,CAAC,EAAEV,CAAC,EAAE;IAErB,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;IAErB,MAAMuC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IACpD,MAAMC,OAAO,GAAGvB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IAE9C,IAAIuB,OAAO,EAAE;MACX,OAAOpB,OAAO,CAAC,KAAK,CAAC;IACvB;;IAEA;IACA,IAAIH,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MAEjB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAEtC,OAAO,IAAIG,OAAO,CAAC1B,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE1B,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAEtD,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAAE;;QAE7B,QAAQ,CAACA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;UAC3B,KAAK,CAAC;YACJ,OAAO,IAAIG,OAAO,CAAC1B,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE1B,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UACtD,KAAK,CAAC;YACJ,OAAO,IAAIG,OAAO,CAAC,CAAC,EAAE1B,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE1B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;UACtD,KAAK,CAAC;YACJ,OAAO,IAAIG,OAAO,CAAC,CAAC1B,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE1B,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UACvD,KAAK,CAAC;YACJ,OAAO,IAAIG,OAAO,CAAC,CAAC,EAAE,CAAC1B,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE1B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACzD;MACF;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI,IAAIsB,OAAO,IAAItB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;MAAE;MAC5B,OAAOG,OAAO,CAAC,MAAM,CAAC;IACxB;IAEA,MAAMwB,GAAG,GAAGlD,IAAI,CAACmD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,MAAMC,GAAG,GAAGrC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAE5C,IAAIsC,EAAE,GAAGrD,IAAI,CAACG,GAAG,CAACoB,CAAC,CAAC,IAAI,CAAC,GAAG6B,GAAG,GAAG7B,CAAC,CAAC,IAAI,CAAC,GAAG2B,GAAG,CAAC;IAChD,IAAII,EAAE,GAAG/B,CAAC,CAAC,IAAI,CAAC,GAAG6B,GAAG,GAAG7B,CAAC,CAAC,IAAI,CAAC,GAAG2B,GAAG;IACtC,OAAO,IAAIxB,OAAO,CAChB2B,EAAE,GAAGrD,IAAI,CAACQ,GAAG,CAAC8C,EAAE,CAAC,EACjBD,EAAE,GAAGrD,IAAI,CAAC2B,GAAG,CAAC2B,EAAE,CAAC,CAAC;EACtB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAA1C,CAAA,EAAY;IAElB,MAAMI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX;MACA,IAAIU,CAAC,IAAI,CAAC,EAAE;QACV,OAAO,IAAIU,OAAO,CAAC1B,IAAI,CAACY,IAAI,CAACI,CAAC,CAAC,EAAE,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAO,IAAIU,OAAO,CAAC,CAAC,EAAE1B,IAAI,CAACY,IAAI,CAAC,CAACI,CAAC,CAAC,CAAC;MACtC;IACF;IAEA,MAAMuC,CAAC,GAAG7C,KAAK,CAACM,CAAC,EAAEV,CAAC,CAAC;IAErB,IAAI+C,EAAE,GAAGrD,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI2C,CAAC,GAAGvD,IAAI,CAACE,GAAG,CAACc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAIsC,EAAE,GAAGtD,IAAI,CAACE,GAAG,CAACI,CAAC,CAAC,IAAI,CAAC,GAAG+C,EAAE,CAAC;IAE/B,IAAIrC,CAAC,IAAI,CAAC,EAAE;MACV,OAAO,IAAIU,OAAO,CAAC2B,EAAE,EAAE/C,CAAC,GAAG,CAAC,GAAG,CAACgD,EAAE,GAAGA,EAAE,CAAC;IAC1C,CAAC,MAAM;MACL,OAAO,IAAI5B,OAAO,CAAC4B,EAAE,EAAEhD,CAAC,GAAG,CAAC,GAAG,CAAC+C,EAAE,GAAGA,EAAE,CAAC;IAC1C;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAlD,CAAA,EAAY;IAEjB,MAAMqD,EAAE,GAAGxD,IAAI,CAACG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE/B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACpB,OAAO,IAAIuB,OAAO,CAAC8B,EAAE,EAAE,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI9B,OAAO,CAChB8B,EAAE,GAAGxD,IAAI,CAACQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EACzBgD,EAAE,GAAGxD,IAAI,CAAC2B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAA8B,CAAA,EAAY;IAEnB;AACJ;AACA;AACA;AACA;;IAEI,MAAMzC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,OAAO,IAAIoB,OAAO,CAChB1B,IAAI,CAACyD,KAAK,CAACzC,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC,EACtCN,IAAI,CAACG,GAAG,CAACa,CAAC,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAa,CAAA,EAAY;IAEjB,MAAMH,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIA,CAAC,KAAK,CAAC,IAAIU,CAAC,GAAG,CAAC,EAAE;MACpB,OAAO,IAAIU,OAAO,CAAC1B,IAAI,CAACmB,GAAG,CAACH,CAAC,CAAC,EAAE,CAAC,CAAC;IACpC;IAEA,OAAO,IAAIU,OAAO,CAChBX,QAAQ,CAACC,CAAC,EAAEV,CAAC,CAAC,EACdN,IAAI,CAACmD,KAAK,CAAC7C,CAAC,EAAEU,CAAC,CAAC,CAAC;EACrB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAd,CAAA,EAAY;IAEjB,OAAOQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;EACtC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAwC,CAAA,EAAY;IAEjB,OAAOlD,IAAI,CAACmD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAxB,CAAA,EAAY;IAEjB;IACA;;IAEA,MAAMX,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,OAAO,IAAIoB,OAAO,CAChB1B,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,GAAGjB,IAAI,CAACO,CAAC,CAAC,EACrBN,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,GAAGZ,IAAI,CAACE,CAAC,CAAC,CAAC;EAC1B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAE,CAAA,EAAY;IAEjB;IACA;;IAEA,MAAMQ,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,OAAO,IAAIoB,OAAO,CAChB1B,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,GAAGjB,IAAI,CAACO,CAAC,CAAC,EACrB,CAACN,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,GAAGZ,IAAI,CAACE,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAoD,CAAA,EAAY;IAEjB;IACA;IACA;IACA;;IAEA,MAAM1C,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMV,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMqD,CAAC,GAAG3D,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,GAAGjB,IAAI,CAACO,CAAC,CAAC;IAE/B,OAAO,IAAIoB,OAAO,CAChB1B,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,GAAG2C,CAAC,EACfvD,IAAI,CAACE,CAAC,CAAC,GAAGqD,CAAC,CAAC;EAChB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAC,CAAA,EAAY;IAEjB;;IAEA,MAAM5C,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMV,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMqD,CAAC,GAAG3D,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,GAAGjB,IAAI,CAACO,CAAC,CAAC;IAE/B,OAAO,IAAIoB,OAAO,CAChB,CAAC1B,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,GAAG2C,CAAC,EAChBvD,IAAI,CAACE,CAAC,CAAC,GAAGqD,CAAC,CAAC;EAChB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAE,CAAA,EAAY;IAEjB;;IAEA,MAAM7C,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMqD,CAAC,GAAG,GAAG,GAAG5D,IAAI,CAAC,CAAC,GAAGO,CAAC,CAAC,GAAG,GAAG,GAAGN,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGQ,CAAC,CAAC;IAEnD,OAAO,IAAIU,OAAO,CAChB1B,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,GAAGjB,IAAI,CAACO,CAAC,CAAC,GAAGqD,CAAC,EACzB3D,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,GAAGZ,IAAI,CAACE,CAAC,CAAC,GAAGqD,CAAC,CAAC;EAC9B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAG,CAAA,EAAY;IAEjB;;IAEA,MAAM9C,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMqD,CAAC,GAAG,GAAG,GAAG5D,IAAI,CAAC,CAAC,GAAGO,CAAC,CAAC,GAAG,GAAG,GAAGN,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGQ,CAAC,CAAC;IAEnD,OAAO,IAAIU,OAAO,CAChB1B,IAAI,CAAC2B,GAAG,CAACX,CAAC,CAAC,GAAGjB,IAAI,CAACO,CAAC,CAAC,GAAGqD,CAAC,EACzB,CAAC3D,IAAI,CAACQ,GAAG,CAACQ,CAAC,CAAC,GAAGZ,IAAI,CAACE,CAAC,CAAC,GAAGqD,CAAC,CAAC;EAC/B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAI,CAAA,EAAY;IAElB;;IAEA,MAAM/C,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,MAAM0D,EAAE,GAAG,IAAItC,OAAO,CACpBpB,CAAC,GAAGA,CAAC,GAAGU,CAAC,GAAGA,CAAC,GAAG,CAAC,EACjB,CAAC,CAAC,GAAGA,CAAC,GAAGV,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAEvB,MAAM2D,EAAE,GAAG,IAAIvC,OAAO,CACpBsC,EAAE,CAAC,IAAI,CAAC,GAAG1D,CAAC,EACZ0D,EAAE,CAAC,IAAI,CAAC,GAAGhD,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAExB,OAAO,IAAIU,OAAO,CAACuC,EAAE,CAAC,IAAI,CAAC,EAAE,CAACA,EAAE,CAAC,IAAI,CAAC,CAAC;EACzC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAC,CAAA,EAAY;IAElB;;IAEA,MAAMlD,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,MAAM0D,EAAE,GAAG,IAAItC,OAAO,CACpBpB,CAAC,GAAGA,CAAC,GAAGU,CAAC,GAAGA,CAAC,GAAG,CAAC,EACjB,CAAC,CAAC,GAAGA,CAAC,GAAGV,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAEvB,MAAM2D,EAAE,GAAG,IAAIvC,OAAO,CACpBsC,EAAE,CAAC,IAAI,CAAC,GAAG1D,CAAC,EACZ0D,EAAE,CAAC,IAAI,CAAC,GAAGhD,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAExB,OAAO,IAAIU,OAAO,CAAC1B,IAAI,CAACO,EAAE,GAAG,CAAC,GAAG0D,EAAE,CAAC,IAAI,CAAC,EAAEA,EAAE,CAAC,IAAI,CAAC,CAAC;EACtD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAE,CAAA,EAAY;IAElB;;IAEA,MAAMnD,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIU,CAAC,KAAK,CAAC,EAAE;MAEX,IAAIV,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,IAAIoB,OAAO,CAAC,CAAC,EAAE0C,QAAQ,CAAC;MACjC;MAEA,IAAI9D,CAAC,KAAK,CAAC,CAAC,EAAE;QACZ,OAAO,IAAIoB,OAAO,CAAC,CAAC,EAAE,CAAC0C,QAAQ,CAAC;MAClC;IACF;IAEA,MAAMT,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,GAAGV,CAAC,KAAK,GAAG,GAAGA,CAAC,CAAC;IAEvC,MAAM0D,EAAE,GAAG,IAAItC,OAAO,CACpB,CAAC,CAAC,GAAGpB,CAAC,GAAGA,CAAC,GAAGU,CAAC,GAAGA,CAAC,IAAI2C,CAAC,EACvB,CAAC,CAAC,GAAG3C,CAAC,GAAG2C,CAAC,CAAC,CAACxC,GAAG,CAAC,CAAC;IAEnB,OAAO,IAAIO,OAAO,CAAC,CAAC,GAAG,GAAGsC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,GAAGA,EAAE,CAAC,IAAI,CAAC,CAAC;EACrD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAK,CAAA,EAAY;IAElB;;IAEA,MAAMrD,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,IAAIoB,OAAO,CAAC1B,IAAI,CAACmD,KAAK,CAAC,CAAC,EAAEnC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzC;IAEA,MAAM2C,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IACvB,OAAQqD,CAAC,KAAK,CAAC,GACX,IAAIjC,OAAO,CACXV,CAAC,GAAG2C,CAAC,EACL,CAACrD,CAAC,GAAGqD,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC,GACd,IAAIzC,OAAO,CACVV,CAAC,KAAK,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACpBV,CAAC,KAAK,CAAC,GAAI,CAACA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC6D,IAAI,CAAC,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAG,CAAA,EAAY;IAElB;;IAEA,MAAMtD,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIU,CAAC,KAAK,CAAC,IAAIV,CAAC,KAAK,CAAC,EAAE;MACtB,OAAO,IAAIoB,OAAO,CAAC,CAAC,EAAE0C,QAAQ,CAAC;IACjC;IAEA,MAAMT,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IACvB,OAAQqD,CAAC,KAAK,CAAC,GACX,IAAIjC,OAAO,CACXV,CAAC,GAAG2C,CAAC,EACL,CAACrD,CAAC,GAAGqD,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,GACd,IAAIxC,OAAO,CACVV,CAAC,KAAK,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACpBV,CAAC,KAAK,CAAC,GAAI,CAACA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC4D,IAAI,CAAC,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAK,CAAA,EAAY;IAElB;;IAEA,MAAMvD,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIU,CAAC,KAAK,CAAC,IAAIV,CAAC,KAAK,CAAC,EAAE;MACtB,OAAO,IAAIoB,OAAO,CAAC1B,IAAI,CAACO,EAAE,GAAG,CAAC,EAAE6D,QAAQ,CAAC;IAC3C;IAEA,MAAMT,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IACvB,OAAQqD,CAAC,KAAK,CAAC,GACX,IAAIjC,OAAO,CACXV,CAAC,GAAG2C,CAAC,EACL,CAACrD,CAAC,GAAGqD,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,GACd,IAAIrC,OAAO,CACVV,CAAC,KAAK,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACpBV,CAAC,KAAK,CAAC,GAAI,CAACA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACyD,IAAI,CAAC,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAA3D,CAAA,EAAY;IAElB;;IAEA,MAAMY,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,OAAO,IAAIoB,OAAO,CAChBtB,IAAI,CAACY,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC,EACrBP,IAAI,CAACiB,CAAC,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,CAAC;EAC1B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAP,CAAA,EAAY;IAElB;;IAEA,MAAMiB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,OAAO,IAAIoB,OAAO,CAChB3B,IAAI,CAACiB,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC,EACrBF,IAAI,CAACY,CAAC,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,CAAC;EAC1B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAkE,CAAA,EAAY;IAElB;;IAEA,MAAMxD,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMV,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMqD,CAAC,GAAG5D,IAAI,CAACiB,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC;IAE/B,OAAO,IAAIoB,OAAO,CAChBtB,IAAI,CAACY,CAAC,CAAC,GAAG2C,CAAC,EACX3D,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,GAAGqD,CAAC,CAAC;EACpB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAc,CAAA,EAAY;IAElB;;IAEA,MAAMzD,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMV,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,MAAMqD,CAAC,GAAG5D,IAAI,CAACiB,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC;IAE/B,OAAO,IAAIoB,OAAO,CAChBtB,IAAI,CAACY,CAAC,CAAC,GAAG2C,CAAC,EACX,CAAC3D,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,GAAGqD,CAAC,CAAC;EACrB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAe,CAAA,EAAY;IAElB;;IAEA,MAAM1D,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMqD,CAAC,GAAG3D,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC,GAAGP,IAAI,CAAC,CAAC,GAAGiB,CAAC,CAAC;IAEvC,OAAO,IAAIU,OAAO,CAChB,CAAC,CAAC,GAAGtB,IAAI,CAACY,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC,GAAGqD,CAAC,EAC9B,CAAC,GAAG5D,IAAI,CAACiB,CAAC,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,GAAGqD,CAAC,CAAC;EAClC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAgB,CAAA,EAAY;IAElB;;IAEA,MAAM3D,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMqD,CAAC,GAAG3D,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC,GAAGP,IAAI,CAAC,CAAC,GAAGiB,CAAC,CAAC;IAEvC,OAAO,IAAIU,OAAO,CAChB,CAAC,GAAG3B,IAAI,CAACiB,CAAC,CAAC,GAAGhB,IAAI,CAACQ,GAAG,CAACF,CAAC,CAAC,GAAGqD,CAAC,EAC7B,CAAC,CAAC,GAAGvD,IAAI,CAACY,CAAC,CAAC,GAAGhB,IAAI,CAAC2B,GAAG,CAACrB,CAAC,CAAC,GAAGqD,CAAC,CAAC;EACnC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAiB,CAAA,EAAY;IAEnB;;IAEA,IAAIC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC,GAAGA,GAAG;IAChB,MAAMC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC,GAAGD,GAAG;IAChBA,GAAG,GAAGC,GAAG,CAAC,IAAI,CAAC;IAEfA,GAAG,CAAC,IAAI,CAAC,GAAG,CAACA,GAAG,CAAC,IAAI,CAAC;IACtBA,GAAG,CAAC,IAAI,CAAC,GAAGD,GAAG;IACf,OAAOC,GAAG;EACZ,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAC,CAAA,EAAY;IAEnB;;IAEA,MAAMD,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1B,IAAIA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;MAClB,MAAMD,GAAG,GAAGC,GAAG,CAAC,IAAI,CAAC;MACrBA,GAAG,CAAC,IAAI,CAAC,GAAG,CAACA,GAAG,CAAC,IAAI,CAAC;MACtBA,GAAG,CAAC,IAAI,CAAC,GAAGD,GAAG;IACjB,CAAC,MAAM;MACL,MAAMA,GAAG,GAAGC,GAAG,CAAC,IAAI,CAAC;MACrBA,GAAG,CAAC,IAAI,CAAC,GAAG,CAACA,GAAG,CAAC,IAAI,CAAC;MACtBA,GAAG,CAAC,IAAI,CAAC,GAAGD,GAAG;IACjB;IACA,OAAOC,GAAG;EACZ,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAE,CAAA,EAAY;IAEnB;;IAEA,MAAMhE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,MAAM2E,IAAI,GAAGjE,CAAC,GAAG,CAAC,IAAIV,CAAC,KAAK,CAAC;IAC7B,MAAM4E,QAAQ,GAAG,CAAC,GAAGlE,CAAC;IACtB,MAAMmE,OAAO,GAAG,CAAC,GAAGnE,CAAC;IACrB,MAAM2C,CAAC,GAAGuB,QAAQ,GAAGA,QAAQ,GAAG5E,CAAC,GAAGA,CAAC;IAErC,MAAML,CAAC,GAAI0D,CAAC,KAAK,CAAC,GACd,IAAIjC,OAAO,CACX,CAACyD,OAAO,GAAGD,QAAQ,GAAG5E,CAAC,GAAGA,CAAC,IAAIqD,CAAC,EAChC,CAACrD,CAAC,GAAG4E,QAAQ,GAAGC,OAAO,GAAG7E,CAAC,IAAIqD,CAAC,CAAC,GACjC,IAAIjC,OAAO,CACVV,CAAC,KAAK,CAAC,CAAC,GAAKA,CAAC,GAAG,CAAC,GAAI,CAAC,EACvBV,CAAC,KAAK,CAAC,GAAKA,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;IAE5B,MAAM8E,IAAI,GAAGnF,CAAC,CAAC,IAAI,CAAC;IACpBA,CAAC,CAAC,IAAI,CAAC,GAAGc,QAAQ,CAACd,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;IACxCA,CAAC,CAAC,IAAI,CAAC,GAAGD,IAAI,CAACmD,KAAK,CAAClD,CAAC,CAAC,IAAI,CAAC,EAAEmF,IAAI,CAAC,GAAG,CAAC;IACvC,IAAIH,IAAI,EAAE;MACRhF,CAAC,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,CAAC,IAAI,CAAC;IACpB;IACA,OAAOA,CAAC;EACV,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAoF,CAAA,EAAY;IAEnB;;IAEA,MAAMrE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIU,CAAC,KAAK,CAAC,IAAIV,CAAC,KAAK,CAAC,EAAE;MACtB,OAAO,IAAIoB,OAAO,CAAC,CAAC,EAAE1B,IAAI,CAACO,EAAE,GAAG,CAAC,CAAC;IACpC;IAEA,MAAMoD,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IACvB,OAAQqD,CAAC,KAAK,CAAC,GACX,IAAIjC,OAAO,CACXV,CAAC,GAAG2C,CAAC,EACL,CAACrD,CAAC,GAAGqD,CAAC,CAAC,CAACqB,KAAK,CAAC,CAAC,GACf,IAAItD,OAAO,CACVV,CAAC,KAAK,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACpBV,CAAC,KAAK,CAAC,GAAI,CAACA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC0E,KAAK,CAAC,CAAC;EACrC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAM,CAAA,EAAY;IAEnB;;IAEA,MAAMtE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAIA,CAAC,KAAK,CAAC,EAAE;MAEX,OAAO,IAAIoB,OAAO,CACfV,CAAC,KAAK,CAAC,GACJhB,IAAI,CAACmB,GAAG,CAACH,CAAC,GAAGhB,IAAI,CAACY,IAAI,CAACI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,CAAC,GAClCoD,QAAQ,EAAE,CAAC,CAAC;IACpB;IAEA,MAAMT,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IACvB,OAAQqD,CAAC,KAAK,CAAC,GACX,IAAIjC,OAAO,CACXV,CAAC,GAAG2C,CAAC,EACL,CAACrD,CAAC,GAAGqD,CAAC,CAAC,CAACiB,KAAK,CAAC,CAAC,GACf,IAAIlD,OAAO,CACVV,CAAC,KAAK,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACpBV,CAAC,KAAK,CAAC,GAAI,CAACA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACsE,KAAK,CAAC,CAAC;EACrC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAW,CAAA,EAAY;IAEnB;;IAEA,MAAMvE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;MACpB,OAAOoB,OAAO,CAAC,UAAU,CAAC;IAC5B;IAEA,MAAMiC,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IACvB,OAAQqD,CAAC,KAAK,CAAC,GACX,IAAIjC,OAAO,CACXV,CAAC,GAAG2C,CAAC,EACL,CAACrD,CAAC,GAAGqD,CAAC,CAAC,CAACoB,KAAK,CAAC,CAAC,GACf,IAAIrD,OAAO,CACVV,CAAC,KAAK,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACpBV,CAAC,KAAK,CAAC,GAAI,CAACA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACyE,KAAK,CAAC,CAAC;EACrC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAAS,CAAA,EAAY;IAErB;IACA,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;MACpB,OAAO9D,OAAO,CAAC,UAAU,CAAC;IAC5B;IAEA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;MACxB,OAAOA,OAAO,CAAC,MAAM,CAAC;IACxB;IAEA,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACpB,MAAMV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAEpB,MAAMqD,CAAC,GAAG3C,CAAC,GAAGA,CAAC,GAAGV,CAAC,GAAGA,CAAC;IAEvB,OAAO,IAAIoB,OAAO,CAACV,CAAC,GAAG2C,CAAC,EAAE,CAACrD,CAAC,GAAGqD,CAAC,CAAC;EACnC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,WAAW,EAAE,SAAA8B,CAAA,EAAY;IAEvB,OAAO,IAAI/D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAgE,CAAA,EAAY;IAEjB,OAAO,IAAIhE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC9C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAAiE,CAAUC,MAAM,EAAE;IAExBA,MAAM,GAAG5F,IAAI,CAACiD,GAAG,CAAC,EAAE,EAAE2C,MAAM,IAAI,CAAC,CAAC;IAElC,OAAO,IAAIlE,OAAO,CAChB1B,IAAI,CAAC2F,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGC,MAAM,CAAC,GAAGA,MAAM,EACvC5F,IAAI,CAAC2F,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGC,MAAM,CAAC,GAAGA,MAAM,CAAC;EAC5C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAC,CAAUD,MAAM,EAAE;IAEzBA,MAAM,GAAG5F,IAAI,CAACiD,GAAG,CAAC,EAAE,EAAE2C,MAAM,IAAI,CAAC,CAAC;IAElC,OAAO,IAAIlE,OAAO,CAChB1B,IAAI,CAAC6F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGD,MAAM,CAAC,GAAGA,MAAM,EACxC5F,IAAI,CAAC6F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGD,MAAM,CAAC,GAAGA,MAAM,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAE,CAAUF,MAAM,EAAE;IAEzBA,MAAM,GAAG5F,IAAI,CAACiD,GAAG,CAAC,EAAE,EAAE2C,MAAM,IAAI,CAAC,CAAC;IAElC,OAAO,IAAIlE,OAAO,CAChB1B,IAAI,CAAC8F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGF,MAAM,CAAC,GAAGA,MAAM,EACxC5F,IAAI,CAAC8F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGF,MAAM,CAAC,GAAGA,MAAM,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,QAAQ,EAAE,SAAAG,CAAU/E,CAAC,EAAEV,CAAC,EAAE;IAExB,MAAMiB,CAAC,GAAGD,KAAK,CAACN,CAAC,EAAEV,CAAC,CAAC;IAErB,OAAON,IAAI,CAACE,GAAG,CAACqB,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIG,OAAO,CAAC,SAAS,CAAC,IACzD1B,IAAI,CAACE,GAAG,CAACqB,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIG,OAAO,CAAC,SAAS,CAAC;EACxD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAsE,CAAA,EAAY;IAEnB,OAAO,IAAItE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,UAAU,EAAE,SAAAuE,CAAA,EAAY;IAEtB,IAAIjF,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAClB,IAAIV,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IAClB,IAAI4F,GAAG,GAAG,EAAE;IAEZ,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;MACnB,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;MACxB,OAAO,UAAU;IACnB;IAEA,IAAIlG,IAAI,CAACE,GAAG,CAACc,CAAC,CAAC,GAAGU,OAAO,CAAC,SAAS,CAAC,EAAE;MACpCV,CAAC,GAAG,CAAC;IACP;IAEA,IAAIhB,IAAI,CAACE,GAAG,CAACI,CAAC,CAAC,GAAGoB,OAAO,CAAC,SAAS,CAAC,EAAE;MACpCpB,CAAC,GAAG,CAAC;IACP;;IAEA;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO4F,GAAG,GAAGlF,CAAC;IAChB;IAEA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACXkF,GAAG,IAAIlF,CAAC;MACRkF,GAAG,IAAI,GAAG;MACV,IAAI5F,CAAC,GAAG,CAAC,EAAE;QACTA,CAAC,GAAG,CAACA,CAAC;QACN4F,GAAG,IAAI,GAAG;MACZ,CAAC,MAAM;QACLA,GAAG,IAAI,GAAG;MACZ;MACAA,GAAG,IAAI,GAAG;IACZ,CAAC,MAAM,IAAI5F,CAAC,GAAG,CAAC,EAAE;MAChBA,CAAC,GAAG,CAACA,CAAC;MACN4F,GAAG,IAAI,GAAG;IACZ;IAEA,IAAI,CAAC,KAAK5F,CAAC,EAAE;MAAE;MACb4F,GAAG,IAAI5F,CAAC;IACV;IACA,OAAO4F,GAAG,GAAG,GAAG;EAClB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,UAAU,EAAE,SAAAC,CAAA,EAAY;IAEtB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;EACjC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAAC,CAAA,EAAY;IAErB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACpB,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB;IACA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAhE,CAAA,EAAY;IACnB,OAAOA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE,QAAQ,EAAE,SAAAiE,CAAA,EAAY;IACpB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE,UAAU,EAAE,SAAA5E,CAAA,EAAY;IACtB,OAAOA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACrD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE,YAAY,EAAE,SAAA6E,CAAA,EAAY;IACxB,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;EAC5B;AACF,CAAC;AAED5E,OAAO,CAAC,MAAM,CAAC,GAAG,IAAIA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AACnCA,OAAO,CAAC,KAAK,CAAC,GAAG,IAAIA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAClCA,OAAO,CAAC,GAAG,CAAC,GAAG,IAAIA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAChCA,OAAO,CAAC,IAAI,CAAC,GAAG,IAAIA,OAAO,CAAC1B,IAAI,CAACO,EAAE,EAAE,CAAC,CAAC;AACvCmB,OAAO,CAAC,GAAG,CAAC,GAAG,IAAIA,OAAO,CAAC1B,IAAI,CAACuG,CAAC,EAAE,CAAC,CAAC;AACrC7E,OAAO,CAAC,UAAU,CAAC,GAAG,IAAIA,OAAO,CAAC0C,QAAQ,EAAEA,QAAQ,CAAC;AACrD1C,OAAO,CAAC,KAAK,CAAC,GAAG,IAAIA,OAAO,CAAC8E,GAAG,EAAEA,GAAG,CAAC;AACtC9E,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;AAC1B,SACEA,OAAO,IAAI+E,OAAO,EAAE/E,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}