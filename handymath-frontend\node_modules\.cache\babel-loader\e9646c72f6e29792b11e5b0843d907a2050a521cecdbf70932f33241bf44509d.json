{"ast": null, "code": "/**\n * Transpose a matrix\n * @param {Array} mat\n * @returns {Array} ret\n * @private\n */\nexport function _switch(mat) {\n  var I = mat.length;\n  var J = mat[0].length;\n  var i, j;\n  var ret = [];\n  for (j = 0; j < J; j++) {\n    var tmp = [];\n    for (i = 0; i < I; i++) {\n      tmp.push(mat[i][j]);\n    }\n    ret.push(tmp);\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["_switch", "mat", "I", "length", "J", "i", "j", "ret", "tmp", "push"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/switch.js"], "sourcesContent": ["/**\n * Transpose a matrix\n * @param {Array} mat\n * @returns {Array} ret\n * @private\n */\nexport function _switch(mat) {\n  var I = mat.length;\n  var J = mat[0].length;\n  var i, j;\n  var ret = [];\n  for (j = 0; j < J; j++) {\n    var tmp = [];\n    for (i = 0; i < I; i++) {\n      tmp.push(mat[i][j]);\n    }\n    ret.push(tmp);\n  }\n  return ret;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAIC,CAAC,GAAGD,GAAG,CAACE,MAAM;EAClB,IAAIC,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACE,MAAM;EACrB,IAAIE,CAAC,EAAEC,CAAC;EACR,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IACtB,IAAIE,GAAG,GAAG,EAAE;IACZ,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACtBG,GAAG,CAACC,IAAI,CAACR,GAAG,CAACI,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC;IACrB;IACAC,GAAG,CAACE,IAAI,<PERSON>ACD,GAAG,CAAC;EACf;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}