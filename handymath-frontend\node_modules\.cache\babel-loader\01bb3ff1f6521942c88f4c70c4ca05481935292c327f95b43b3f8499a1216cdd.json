{"ast": null, "code": "export var simplifyConstantDocs = {\n  name: 'simplifyConstant',\n  category: 'Algebra',\n  syntax: ['simplifyConstant(expr)', 'simplifyConstant(expr, options)'],\n  description: 'Replace constant subexpressions of node with their values.',\n  examples: ['simplifyConstant(\"(3-3)*x\")', 'simplifyConstant(parse(\"z-cos(tau/8)\"))'],\n  seealso: ['simplify', 'simplifyCore', 'evaluate']\n};", "map": {"version": 3, "names": ["simplifyConstantDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/simplifyConstant.js"], "sourcesContent": ["export var simplifyConstantDocs = {\n  name: 'simplifyConstant',\n  category: 'Algebra',\n  syntax: ['simplifyConstant(expr)', 'simplifyConstant(expr, options)'],\n  description: 'Replace constant subexpressions of node with their values.',\n  examples: ['simplifyConstant(\"(3-3)*x\")', 'simplifyConstant(parse(\"z-cos(tau/8)\"))'],\n  seealso: ['simplify', 'simplifyCore', 'evaluate']\n};"], "mappings": "AAAA,OAAO,IAAIA,oBAAoB,GAAG;EAChCC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,wBAAwB,EAAE,iCAAiC,CAAC;EACrEC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,CAAC,6BAA6B,EAAE,yCAAyC,CAAC;EACpFC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}