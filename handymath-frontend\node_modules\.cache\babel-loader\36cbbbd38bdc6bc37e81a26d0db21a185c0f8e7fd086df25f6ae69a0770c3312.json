{"ast": null, "code": "export var log2Docs = {\n  name: 'log2',\n  category: 'Arithmetic',\n  syntax: ['log2(x)'],\n  description: 'Calculate the 2-base of a value. This is the same as calculating `log(x, 2)`.',\n  examples: ['log2(0.03125)', 'log2(16)', 'log2(16) / log2(2)', 'pow(2, 4)'],\n  seealso: ['exp', 'log1p', 'log', 'log10']\n};", "map": {"version": 3, "names": ["log2Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/log2.js"], "sourcesContent": ["export var log2Docs = {\n  name: 'log2',\n  category: 'Arithmetic',\n  syntax: ['log2(x)'],\n  description: 'Calculate the 2-base of a value. This is the same as calculating `log(x, 2)`.',\n  examples: ['log2(0.03125)', 'log2(16)', 'log2(16) / log2(2)', 'pow(2, 4)'],\n  seealso: ['exp', 'log1p', 'log', 'log10']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,+EAA+E;EAC5FC,QAAQ,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,oBAAoB,EAAE,WAAW,CAAC;EAC1EC,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}