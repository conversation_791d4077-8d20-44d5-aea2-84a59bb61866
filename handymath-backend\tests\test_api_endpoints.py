"""
Tests pour les endpoints de l'API REST
"""
import pytest
from django.urls import reverse
from rest_framework import status
from unittest.mock import patch, MagicMock

from .factories import UserFactory, EquationFactory


@pytest.mark.api
@pytest.mark.django_db
class TestUserAPIEndpoints:
    """Tests pour les endpoints utilisateur"""
    
    def test_user_list_endpoint_admin(self, admin_client):
        """Test de l'endpoint liste des utilisateurs (admin)"""
        # Créer quelques utilisateurs de test
        UserFactory.create_batch(3)
        
        url = reverse('user-list')
        response = admin_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 3
        assert 'username' in response.data[0]
        assert 'email' in response.data[0]
    
    def test_user_list_endpoint_student_forbidden(self, student_client):
        """Test que les étudiants ne peuvent pas accéder à la liste des utilisateurs"""
        url = reverse('user-list')
        response = student_client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_user_detail_endpoint(self, authenticated_client, user):
        """Test de l'endpoint détail utilisateur"""
        url = reverse('user-detail', kwargs={'pk': user.pk})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == user.id
        assert response.data['username'] == user.username
    
    def test_user_me_endpoint(self, authenticated_client, user):
        """Test de l'endpoint utilisateur actuel"""
        url = reverse('user-me')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == user.id
        assert response.data['username'] == user.username
    
    def test_user_create_endpoint(self, api_client, user_registration_data):
        """Test de création d'utilisateur via API"""
        url = reverse('user-list')
        response = api_client.post(url, user_registration_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['username'] == user_registration_data['username']
        assert 'password' not in response.data  # Le mot de passe ne doit pas être retourné
    
    def test_user_update_endpoint(self, authenticated_client, user):
        """Test de mise à jour d'utilisateur"""
        update_data = {
            'first_name': 'Updated',
            'last_name': 'Name'
        }
        
        url = reverse('user-detail', kwargs={'pk': user.pk})
        response = authenticated_client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['first_name'] == update_data['first_name']
        assert response.data['last_name'] == update_data['last_name']
    
    def test_user_delete_endpoint_admin(self, admin_client, user):
        """Test de suppression d'utilisateur (admin)"""
        url = reverse('user-detail', kwargs={'pk': user.pk})
        response = admin_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
    
    def test_user_delete_endpoint_student_forbidden(self, student_client, user):
        """Test que les étudiants ne peuvent pas supprimer d'utilisateurs"""
        url = reverse('user-detail', kwargs={'pk': user.pk})
        response = student_client.delete(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.api
@pytest.mark.django_db
class TestEquationAPIEndpoints:
    """Tests pour les endpoints d'équations"""
    
    def test_equation_list_endpoint(self, authenticated_client):
        """Test de l'endpoint liste des équations"""
        # Créer quelques équations de test
        EquationFactory.create_batch(3)
        
        url = reverse('equation-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 3
        assert 'equation_text' in response.data[0]
    
    def test_equation_create_endpoint(self, authenticated_client, sample_equation_data):
        """Test de création d'équation"""
        url = reverse('equation-list')
        response = authenticated_client.post(url, sample_equation_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['equation_text'] == sample_equation_data['equation_text']
    
    def test_equation_solve_endpoint(self, authenticated_client):
        """Test de l'endpoint de résolution d'équation"""
        equation_data = {"equation": "2x + 3 = 11"}
        
        url = reverse('equation-solve')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
        assert 'steps' in response.data
    
    def test_equation_history_endpoint(self, authenticated_client, user):
        """Test de l'endpoint historique des équations"""
        # Créer quelques équations pour l'utilisateur
        EquationFactory.create_batch(3, created_by=user)
        
        url = reverse('equation-history')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 3
    
    def test_equation_detail_endpoint(self, authenticated_client):
        """Test de l'endpoint détail d'équation"""
        equation = EquationFactory()
        
        url = reverse('equation-detail', kwargs={'pk': equation.pk})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == equation.id
        assert response.data['equation_text'] == equation.equation_text


@pytest.mark.api
@pytest.mark.django_db
class TestOCRAPIEndpoints:
    """Tests pour les endpoints OCR"""
    
    @patch('api.views.extract_text_from_image')
    def test_ocr_recognize_endpoint(self, mock_extract_text, authenticated_client, ocr_test_images):
        """Test de l'endpoint de reconnaissance OCR"""
        mock_extract_text.return_value = "2x + 3 = 11"
        
        ocr_data = {
            "image": ocr_test_images["simple_equation"]
        }
        
        url = reverse('ocr-recognize')
        response = authenticated_client.post(url, ocr_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'text' in response.data
        assert response.data['text'] == "2x + 3 = 11"
        mock_extract_text.assert_called_once()
    
    @patch('api.views.extract_text_from_image')
    def test_ocr_solve_endpoint(self, mock_extract_text, authenticated_client, ocr_test_images):
        """Test de l'endpoint OCR + résolution"""
        mock_extract_text.return_value = "x + 5 = 10"
        
        ocr_data = {
            "image": ocr_test_images["simple_equation"],
            "solve": True
        }
        
        url = reverse('ocr-solve')
        response = authenticated_client.post(url, ocr_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'text' in response.data
        assert 'solution' in response.data
        assert 'steps' in response.data
    
    def test_ocr_invalid_image(self, authenticated_client):
        """Test OCR avec image invalide"""
        ocr_data = {
            "image": "invalid_image_data"
        }
        
        url = reverse('ocr-recognize')
        response = authenticated_client.post(url, ocr_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data


@pytest.mark.api
@pytest.mark.django_db
class TestAdminAPIEndpoints:
    """Tests pour les endpoints administrateur"""
    
    def test_admin_dashboard_stats(self, admin_client):
        """Test de l'endpoint statistiques du dashboard admin"""
        # Créer des données de test
        UserFactory.create_batch(5)
        EquationFactory.create_batch(10)
        
        url = reverse('admin-dashboard-stats')
        response = admin_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'total_users' in response.data
        assert 'total_equations' in response.data
        assert 'active_users' in response.data
        assert response.data['total_users'] >= 5
        assert response.data['total_equations'] >= 10
    
    def test_admin_user_management(self, admin_client):
        """Test de gestion des utilisateurs par l'admin"""
        user = UserFactory()
        
        # Test de mise à jour du statut utilisateur
        update_data = {'is_active': False}
        url = reverse('admin-user-update', kwargs={'pk': user.pk})
        response = admin_client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['is_active'] is False
    
    def test_admin_analytics_endpoint(self, admin_client):
        """Test de l'endpoint analytics"""
        url = reverse('admin-analytics')
        response = admin_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'user_activity' in response.data
        assert 'equation_trends' in response.data
    
    def test_admin_endpoints_student_forbidden(self, student_client):
        """Test que les étudiants ne peuvent pas accéder aux endpoints admin"""
        admin_urls = [
            reverse('admin-dashboard-stats'),
            reverse('admin-analytics'),
        ]
        
        for url in admin_urls:
            response = student_client.get(url)
            assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.api
@pytest.mark.django_db
class TestAPIErrorHandling:
    """Tests pour la gestion d'erreurs de l'API"""
    
    def test_404_endpoint(self, authenticated_client):
        """Test d'endpoint inexistant"""
        response = authenticated_client.get('/api/nonexistent/')
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_method_not_allowed(self, authenticated_client):
        """Test de méthode HTTP non autorisée"""
        url = reverse('user-me')
        response = authenticated_client.delete(url)  # DELETE non autorisé sur user-me
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
    
    def test_invalid_json_data(self, authenticated_client):
        """Test avec données JSON invalides"""
        url = reverse('equation-solve')
        response = authenticated_client.post(
            url, 
            'invalid json', 
            content_type='application/json'
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_missing_required_fields(self, authenticated_client):
        """Test avec champs requis manquants"""
        url = reverse('equation-solve')
        response = authenticated_client.post(url, {}, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'equation' in response.data or 'error' in response.data


@pytest.mark.api
@pytest.mark.django_db
class TestAPIPerformance:
    """Tests de performance pour l'API"""
    
    @pytest.mark.slow
    def test_large_dataset_performance(self, authenticated_client):
        """Test de performance avec un grand jeu de données"""
        import time
        
        # Créer un grand nombre d'équations
        EquationFactory.create_batch(100)
        
        url = reverse('equation-list')
        start_time = time.time()
        response = authenticated_client.get(url)
        end_time = time.time()
        
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 2  # Moins de 2 secondes
    
    def test_pagination_performance(self, authenticated_client):
        """Test de performance de la pagination"""
        EquationFactory.create_batch(50)
        
        url = reverse('equation-list') + '?page=1&page_size=10'
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) <= 10
        assert 'next' in response.data
        assert 'previous' in response.data
