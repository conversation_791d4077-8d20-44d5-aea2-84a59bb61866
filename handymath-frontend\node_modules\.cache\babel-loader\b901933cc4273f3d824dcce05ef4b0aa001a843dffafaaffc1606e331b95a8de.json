{"ast": null, "code": "export var phiDocs = {\n  name: 'phi',\n  category: 'Constants',\n  syntax: ['phi'],\n  description: 'Phi is the golden ratio. Two quantities are in the golden ratio if their ratio is the same as the ratio of their sum to the larger of the two quantities. Phi is defined as `(1 + sqrt(5)) / 2` and is approximately 1.618034...',\n  examples: ['phi'],\n  seealso: []\n};", "map": {"version": 3, "names": ["phiDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/phi.js"], "sourcesContent": ["export var phiDocs = {\n  name: 'phi',\n  category: 'Constants',\n  syntax: ['phi'],\n  description: 'Phi is the golden ratio. Two quantities are in the golden ratio if their ratio is the same as the ratio of their sum to the larger of the two quantities. Phi is defined as `(1 + sqrt(5)) / 2` and is approximately 1.618034...',\n  examples: ['phi'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,KAAK,CAAC;EACfC,WAAW,EAAE,kOAAkO;EAC/OC,QAAQ,EAAE,CAAC,KAAK,CAAC;EACjBC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}