{"ast": null, "code": "export var signDocs = {\n  name: 'sign',\n  category: 'Arithmetic',\n  syntax: ['sign(x)'],\n  description: 'Compute the sign of a value. The sign of a value x is 1 when x>0, -1 when x<0, and 0 when x=0.',\n  examples: ['sign(3.5)', 'sign(-4.2)', 'sign(0)'],\n  seealso: ['abs']\n};", "map": {"version": 3, "names": ["signDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/sign.js"], "sourcesContent": ["export var signDocs = {\n  name: 'sign',\n  category: 'Arithmetic',\n  syntax: ['sign(x)'],\n  description: 'Compute the sign of a value. The sign of a value x is 1 when x>0, -1 when x<0, and 0 when x=0.',\n  examples: ['sign(3.5)', 'sign(-4.2)', 'sign(0)'],\n  seealso: ['abs']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,gGAAgG;EAC7GC,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC;EAChDC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}