{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createTranspose } from '../../factoriesAny.js';\nexport var transposeDependencies = {\n  matrixDependencies,\n  typedDependencies,\n  createTranspose\n};", "map": {"version": 3, "names": ["matrixDependencies", "typedDependencies", "createTranspose", "transposeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesTranspose.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createTranspose } from '../../factoriesAny.js';\nexport var transposeDependencies = {\n  matrixDependencies,\n  typedDependencies,\n  createTranspose\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}