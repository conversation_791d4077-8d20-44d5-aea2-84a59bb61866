{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nexport var createUseMatrixForArrayScalar = /* #__PURE__ */factory('useMatrixForArrayScalar', ['typed', 'matrix'], _ref => {\n  var {\n    typed,\n    matrix\n  } = _ref;\n  return {\n    'Array, number': typed.referTo('DenseMatrix, number', selfDn => (x, y) => selfDn(matrix(x), y).valueOf()),\n    'Array, BigNumber': typed.referTo('DenseMatrix, BigNumber', selfDB => (x, y) => selfDB(matrix(x), y).valueOf()),\n    'number, Array': typed.referTo('number, DenseMatrix', selfnD => (x, y) => selfnD(x, matrix(y)).valueOf()),\n    'BigNumber, Array': typed.referTo('BigNumber, DenseMatrix', selfBD => (x, y) => selfBD(x, matrix(y)).valueOf())\n  };\n});", "map": {"version": 3, "names": ["factory", "createUseMatrixForArrayScalar", "_ref", "typed", "matrix", "referTo", "selfDn", "x", "y", "valueOf", "selfDB", "selfnD", "selfBD"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/bitwise/useMatrixForArrayScalar.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nexport var createUseMatrixForArrayScalar = /* #__PURE__ */factory('useMatrixForArrayScalar', ['typed', 'matrix'], _ref => {\n  var {\n    typed,\n    matrix\n  } = _ref;\n  return {\n    'Array, number': typed.referTo('DenseMatrix, number', selfDn => (x, y) => selfDn(matrix(x), y).valueOf()),\n    'Array, BigNumber': typed.referTo('DenseMatrix, BigNumber', selfDB => (x, y) => selfDB(matrix(x), y).valueOf()),\n    'number, Array': typed.referTo('number, DenseMatrix', selfnD => (x, y) => selfnD(x, matrix(y)).valueOf()),\n    'BigNumber, Array': typed.referTo('BigNumber, DenseMatrix', selfBD => (x, y) => selfBD(x, matrix(y)).valueOf())\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,OAAO,IAAIC,6BAA6B,GAAG,eAAeD,OAAO,CAAC,yBAAyB,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAEE,IAAI,IAAI;EACxH,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR,OAAO;IACL,eAAe,EAAEC,KAAK,CAACE,OAAO,CAAC,qBAAqB,EAAEC,MAAM,IAAI,CAACC,CAAC,EAAEC,CAAC,KAAKF,MAAM,CAACF,MAAM,CAACG,CAAC,CAAC,EAAEC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IACzG,kBAAkB,EAAEN,KAAK,CAACE,OAAO,CAAC,wBAAwB,EAAEK,MAAM,IAAI,CAACH,CAAC,EAAEC,CAAC,KAAKE,MAAM,CAACN,MAAM,CAACG,CAAC,CAAC,EAAEC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IAC/G,eAAe,EAAEN,KAAK,CAACE,OAAO,CAAC,qBAAqB,EAAEM,MAAM,IAAI,CAACJ,CAAC,EAAEC,CAAC,KAAKG,MAAM,CAACJ,CAAC,EAAEH,MAAM,CAACI,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IACzG,kBAAkB,EAAEN,KAAK,CAACE,OAAO,CAAC,wBAAwB,EAAEO,MAAM,IAAI,CAACL,CAAC,EAAEC,CAAC,KAAKI,MAAM,CAACL,CAAC,EAAEH,MAAM,CAACI,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAChH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}