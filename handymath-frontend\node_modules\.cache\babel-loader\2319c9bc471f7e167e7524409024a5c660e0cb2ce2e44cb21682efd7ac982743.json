{"ast": null, "code": "import { clone as objectClone } from '../../utils/object.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'clone';\nvar dependencies = ['typed'];\nexport var createClone = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Clone an object. Will make a deep copy of the data.\n   *\n   * Syntax:\n   *\n   *     math.clone(x)\n   *\n   * Examples:\n   *\n   *    math.clone(3.5)                   // returns number 3.5\n   *    math.clone(math.complex('2-4i'))  // returns Complex 2 - 4i\n   *    math.clone(math.unit(45, 'deg'))  // returns Unit 45 deg\n   *    math.clone([[1, 2], [3, 4]])      // returns Array [[1, 2], [3, 4]]\n   *    math.clone(\"hello world\")         // returns string \"hello world\"\n   *\n   * @param {*} x   Object to be cloned\n   * @return {*} A clone of object x\n   */\n  return typed(name, {\n    any: objectClone\n  });\n});", "map": {"version": 3, "names": ["clone", "objectClone", "factory", "name", "dependencies", "createClone", "_ref", "typed", "any"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/clone.js"], "sourcesContent": ["import { clone as objectClone } from '../../utils/object.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'clone';\nvar dependencies = ['typed'];\nexport var createClone = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Clone an object. Will make a deep copy of the data.\n   *\n   * Syntax:\n   *\n   *     math.clone(x)\n   *\n   * Examples:\n   *\n   *    math.clone(3.5)                   // returns number 3.5\n   *    math.clone(math.complex('2-4i'))  // returns Complex 2 - 4i\n   *    math.clone(math.unit(45, 'deg'))  // returns Unit 45 deg\n   *    math.clone([[1, 2], [3, 4]])      // returns Array [[1, 2], [3, 4]]\n   *    math.clone(\"hello world\")         // returns string \"hello world\"\n   *\n   * @param {*} x   Object to be cloned\n   * @return {*} A clone of object x\n   */\n  return typed(name, {\n    any: objectClone\n  });\n});"], "mappings": "AAAA,SAASA,KAAK,IAAIC,WAAW,QAAQ,uBAAuB;AAC5D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,OAAO;AAClB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,WAAW,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC1E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBK,GAAG,EAAEP;EACP,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}