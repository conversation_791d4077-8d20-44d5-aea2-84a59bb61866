#!/usr/bin/env python3
"""
Test de stabilité de progression en temps réel
"""

import os
import sys
import django
import time

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import User, Course, Chapter, Lesson, LessonProgress, CourseEnrollment

def test_progression_stability():
    """Tester la stabilité de la progression lors d'interactions"""
    print("🧪 TEST DE STABILITÉ DE PROGRESSION EN TEMPS RÉEL")
    print("=" * 60)
    
    # Créer un utilisateur de test
    test_user, created = User.objects.get_or_create(
        username='stability_real_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Stability',
            'last_name': 'Real'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Utilisateur test créé: {test_user.username}")
    else:
        # Nettoyer les progressions existantes
        LessonProgress.objects.filter(user=test_user).delete()
        print(f"🧹 Progressions nettoyées pour: {test_user.username}")
    
    # Sélectionner un cours pour le test
    course = Course.objects.filter(status='published').first()
    if not course:
        print("❌ Aucun cours publié trouvé")
        return
    
    print(f"📚 Cours de test: {course.title}")
    
    # Test 1: Progression initiale
    print(f"\n1️⃣ TEST: Progression initiale")
    initial_progress = course.get_progress_for_user(test_user)
    print(f"   📊 Progression initiale: {initial_progress}%")
    
    if initial_progress != 0:
        print(f"   ⚠️ PROBLÈME: Progression non nulle pour un nouvel utilisateur!")
        return False
    
    # Test 2: Accès à une leçon (simulation API)
    print(f"\n2️⃣ TEST: Accès à une leçon")
    first_lesson = Lesson.objects.filter(
        chapter__course=course,
        is_published=True
    ).order_by('chapter__order', 'order').first()
    
    if not first_lesson:
        print("   ❌ Aucune leçon trouvée")
        return False
    
    print(f"   📖 Première leçon: {first_lesson.title}")
    
    # Simuler l'accès à la leçon (comme dans l'API)
    lesson_progress, created = LessonProgress.objects.get_or_create(
        user=test_user,
        lesson=first_lesson,
        defaults={'completed': False, 'time_spent': 0}
    )
    
    print(f"   📊 Progression créée: {created}")
    
    # Vérifier la progression après accès
    progress_after_access = course.get_progress_for_user(test_user)
    print(f"   📈 Progression après accès: {progress_after_access}%")
    
    if progress_after_access != 0:
        print(f"   ⚠️ PROBLÈME: Progression modifiée après simple accès!")
        return False
    
    # Test 3: Complétion d'une leçon
    print(f"\n3️⃣ TEST: Complétion d'une leçon")
    lesson_progress.completed = True
    lesson_progress.time_spent = 300
    lesson_progress.save()
    
    progress_after_completion = course.get_progress_for_user(test_user)
    print(f"   📈 Progression après complétion: {progress_after_completion}%")
    
    # Calculer la progression attendue
    total_lessons = Lesson.objects.filter(
        chapter__course=course,
        is_published=True
    ).count()
    expected_progress = (1 / total_lessons) * 100
    
    print(f"   🧮 Progression attendue: {expected_progress}%")
    
    if abs(progress_after_completion - expected_progress) > 0.1:
        print(f"   ⚠️ PROBLÈME: Progression incorrecte après complétion!")
        return False
    
    # Test 4: Stabilité lors de multiples accès
    print(f"\n4️⃣ TEST: Stabilité lors de multiples accès")
    
    for i in range(5):
        # Simuler plusieurs accès à la même leçon
        progress_before = course.get_progress_for_user(test_user)
        
        # Accès à la leçon (comme dans l'API)
        lesson_progress_check = LessonProgress.objects.filter(
            user=test_user,
            lesson=first_lesson
        ).first()
        
        progress_after = course.get_progress_for_user(test_user)
        
        print(f"   🔄 Accès {i+1}: {progress_before}% → {progress_after}%")
        
        if progress_before != progress_after:
            print(f"   ⚠️ PROBLÈME: Progression instable lors d'accès multiples!")
            return False
        
        time.sleep(0.1)  # Petite pause
    
    # Test 5: Test avec d'autres leçons
    print(f"\n5️⃣ TEST: Progression avec plusieurs leçons")
    
    lessons = Lesson.objects.filter(
        chapter__course=course,
        is_published=True
    ).order_by('chapter__order', 'order')[:3]  # Prendre les 3 premières
    
    completed_count = 1  # On a déjà complété la première
    
    for lesson in lessons[1:]:  # Commencer à partir de la deuxième
        print(f"   📖 Complétion de: {lesson.title}")
        
        # Créer/récupérer la progression
        lesson_progress, created = LessonProgress.objects.get_or_create(
            user=test_user,
            lesson=lesson,
            defaults={'completed': False, 'time_spent': 0}
        )
        
        # Compléter la leçon
        lesson_progress.completed = True
        lesson_progress.time_spent = 300
        lesson_progress.save()
        
        completed_count += 1
        
        # Vérifier la progression
        current_progress = course.get_progress_for_user(test_user)
        expected_progress = (completed_count / total_lessons) * 100
        
        print(f"      📊 Progression: {current_progress}% (attendue: {expected_progress}%)")
        
        if abs(current_progress - expected_progress) > 0.1:
            print(f"      ⚠️ PROBLÈME: Progression incorrecte!")
            return False
    
    print(f"\n✅ TOUS LES TESTS PASSÉS - PROGRESSION STABLE")
    return True

def test_multiple_users():
    """Tester la stabilité avec plusieurs utilisateurs"""
    print(f"\n\n🧪 TEST MULTI-UTILISATEURS")
    print("=" * 40)
    
    course = Course.objects.filter(status='published').first()
    if not course:
        return
    
    # Créer plusieurs utilisateurs de test
    test_users = []
    for i in range(3):
        user, created = User.objects.get_or_create(
            username=f'multi_test_user_{i}',
            defaults={
                'email': f'multi_test_{i}@test.com',
                'first_name': f'Multi{i}',
                'last_name': 'Test'
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
        else:
            # Nettoyer les progressions existantes
            LessonProgress.objects.filter(user=user).delete()
        
        test_users.append(user)
    
    print(f"👥 {len(test_users)} utilisateurs de test créés")
    
    # Tester que les progressions sont indépendantes
    first_lesson = Lesson.objects.filter(
        chapter__course=course,
        is_published=True
    ).order_by('chapter__order', 'order').first()
    
    # Utilisateur 1 complète une leçon
    progress1, created = LessonProgress.objects.get_or_create(
        user=test_users[0],
        lesson=first_lesson,
        defaults={'completed': True, 'time_spent': 300}
    )
    progress1.completed = True
    progress1.save()
    
    # Vérifier que les autres utilisateurs ne sont pas affectés
    for i, user in enumerate(test_users):
        user_progress = course.get_progress_for_user(user)
        expected = 20.0 if i == 0 else 0.0  # Seulement le premier utilisateur devrait avoir une progression
        
        print(f"   👤 {user.username}: {user_progress}% (attendu: {expected}%)")
        
        if abs(user_progress - expected) > 0.1:
            print(f"   ⚠️ PROBLÈME: Progression incorrecte pour {user.username}!")
            return False
    
    print(f"✅ TEST MULTI-UTILISATEURS PASSÉ")
    return True

def main():
    """Fonction principale"""
    try:
        # Test de stabilité principal
        stability_ok = test_progression_stability()
        
        # Test multi-utilisateurs
        multi_user_ok = test_multiple_users()
        
        # Résumé
        print(f"\n\n📋 RÉSUMÉ DES TESTS")
        print("=" * 30)
        
        if stability_ok and multi_user_ok:
            print("✅ TOUS LES TESTS PASSÉS")
            print("🎉 Le système de progression est STABLE")
        else:
            print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
            print("⚠️ Le système de progression présente des INSTABILITÉS")
            
            if not stability_ok:
                print("   - Problème de stabilité de progression")
            if not multi_user_ok:
                print("   - Problème d'isolation entre utilisateurs")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
