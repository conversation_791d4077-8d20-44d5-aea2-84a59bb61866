{"ast": null, "code": "import { isBigNumber, isNumber } from '../is.js';\nimport { isInteger, normalizeFormatOptions } from '../number.js';\n\n/**\n * Formats a BigNumber in a given base\n * @param {BigNumber} n\n * @param {number} base\n * @param {number} size\n * @returns {string}\n */\nfunction formatBigNumberToBase(n, base, size) {\n  var BigNumberCtor = n.constructor;\n  var big2 = new BigNumberCtor(2);\n  var suffix = '';\n  if (size) {\n    if (size < 1) {\n      throw new Error('size must be in greater than 0');\n    }\n    if (!isInteger(size)) {\n      throw new Error('size must be an integer');\n    }\n    if (n.greaterThan(big2.pow(size - 1).sub(1)) || n.lessThan(big2.pow(size - 1).mul(-1))) {\n      throw new Error(\"Value must be in range [-2^\".concat(size - 1, \", 2^\").concat(size - 1, \"-1]\"));\n    }\n    if (!n.isInteger()) {\n      throw new Error('Value must be an integer');\n    }\n    if (n.lessThan(0)) {\n      n = n.add(big2.pow(size));\n    }\n    suffix = \"i\".concat(size);\n  }\n  switch (base) {\n    case 2:\n      return \"\".concat(n.toBinary()).concat(suffix);\n    case 8:\n      return \"\".concat(n.toOctal()).concat(suffix);\n    case 16:\n      return \"\".concat(n.toHexadecimal()).concat(suffix);\n    default:\n      throw new Error(\"Base \".concat(base, \" not supported \"));\n  }\n}\n\n/**\n * Convert a BigNumber to a formatted string representation.\n *\n * Syntax:\n *\n *    format(value)\n *    format(value, options)\n *    format(value, precision)\n *    format(value, fn)\n *\n * Where:\n *\n *    {number} value   The value to be formatted\n *    {Object} options An object with formatting options. Available options:\n *                     {string} notation\n *                         Number notation. Choose from:\n *                         'fixed'          Always use regular number notation.\n *                                          For example '123.40' and '14000000'\n *                         'exponential'    Always use exponential notation.\n *                                          For example '1.234e+2' and '1.4e+7'\n *                         'auto' (default) Regular number notation for numbers\n *                                          having an absolute value between\n *                                          `lower` and `upper` bounds, and uses\n *                                          exponential notation elsewhere.\n *                                          Lower bound is included, upper bound\n *                                          is excluded.\n *                                          For example '123.4' and '1.4e7'.\n *                         'bin', 'oct, or\n *                         'hex'            Format the number using binary, octal,\n *                                          or hexadecimal notation.\n *                                          For example '0b1101' and '0x10fe'.\n *                     {number} wordSize    The word size in bits to use for formatting\n *                                          in binary, octal, or hexadecimal notation.\n *                                          To be used only with 'bin', 'oct', or 'hex'\n *                                          values for 'notation' option. When this option\n *                                          is defined the value is formatted as a signed\n *                                          twos complement integer of the given word size\n *                                          and the size suffix is appended to the output.\n *                                          For example\n *                                          format(-1, {notation: 'hex', wordSize: 8}) === '0xffi8'.\n *                                          Default value is undefined.\n *                     {number} precision   A number between 0 and 16 to round\n *                                          the digits of the number.\n *                                          In case of notations 'exponential',\n *                                          'engineering', and 'auto',\n *                                          `precision` defines the total\n *                                          number of significant digits returned.\n *                                          In case of notation 'fixed',\n *                                          `precision` defines the number of\n *                                          significant digits after the decimal\n *                                          point.\n *                                          `precision` is undefined by default.\n *                     {number} lowerExp    Exponent determining the lower boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `-3`.\n *                     {number} upperExp    Exponent determining the upper boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `5`.\n *    {Function} fn    A custom formatting function. Can be used to override the\n *                     built-in notations. Function `fn` is called with `value` as\n *                     parameter and must return a string. Is useful for example to\n *                     format all values inside a matrix in a particular way.\n *\n * Examples:\n *\n *    format(6.4)                                        // '6.4'\n *    format(1240000)                                    // '1.24e6'\n *    format(1/3)                                        // '0.3333333333333333'\n *    format(1/3, 3)                                     // '0.333'\n *    format(21385, 2)                                   // '21000'\n *    format(12e8, {notation: 'fixed'})                  // returns '1200000000'\n *    format(2.3,    {notation: 'fixed', precision: 4})  // returns '2.3000'\n *    format(52.8,   {notation: 'exponential'})          // returns '5.28e+1'\n *    format(12400,  {notation: 'engineering'})          // returns '12.400e+3'\n *\n * @param {BigNumber} value\n * @param {Object | Function | number | BigNumber} [options]\n * @return {string} str The formatted value\n */\nexport function format(value, options) {\n  if (typeof options === 'function') {\n    // handle format(value, fn)\n    return options(value);\n  }\n\n  // handle special cases\n  if (!value.isFinite()) {\n    return value.isNaN() ? 'NaN' : value.gt(0) ? 'Infinity' : '-Infinity';\n  }\n  var {\n    notation,\n    precision,\n    wordSize\n  } = normalizeFormatOptions(options);\n\n  // handle the various notations\n  switch (notation) {\n    case 'fixed':\n      return toFixed(value, precision);\n    case 'exponential':\n      return toExponential(value, precision);\n    case 'engineering':\n      return toEngineering(value, precision);\n    case 'bin':\n      return formatBigNumberToBase(value, 2, wordSize);\n    case 'oct':\n      return formatBigNumberToBase(value, 8, wordSize);\n    case 'hex':\n      return formatBigNumberToBase(value, 16, wordSize);\n    case 'auto':\n      {\n        // determine lower and upper bound for exponential notation.\n        // TODO: implement support for upper and lower to be BigNumbers themselves\n        var lowerExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.lowerExp, -3);\n        var upperExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.upperExp, 5);\n\n        // handle special case zero\n        if (value.isZero()) return '0';\n\n        // determine whether or not to output exponential notation\n        var str;\n        var rounded = value.toSignificantDigits(precision);\n        var exp = rounded.e;\n        if (exp >= lowerExp && exp < upperExp) {\n          // normal number notation\n          str = rounded.toFixed();\n        } else {\n          // exponential notation\n          str = toExponential(value, precision);\n        }\n\n        // remove trailing zeros after the decimal point\n        return str.replace(/((\\.\\d*?)(0+))($|e)/, function () {\n          var digits = arguments[2];\n          var e = arguments[4];\n          return digits !== '.' ? digits + e : e;\n        });\n      }\n    default:\n      throw new Error('Unknown notation \"' + notation + '\". ' + 'Choose \"auto\", \"exponential\", \"fixed\", \"bin\", \"oct\", or \"hex.');\n  }\n}\n\n/**\n * Format a BigNumber in engineering notation. Like '1.23e+6', '2.3e+0', '3.500e-3'\n * @param {BigNumber} value\n * @param {number} [precision]        Optional number of significant figures to return.\n */\nexport function toEngineering(value, precision) {\n  // find nearest lower multiple of 3 for exponent\n  var e = value.e;\n  var newExp = e % 3 === 0 ? e : e < 0 ? e - 3 - e % 3 : e - e % 3;\n\n  // find difference in exponents, and calculate the value without exponent\n  var valueWithoutExp = value.mul(Math.pow(10, -newExp));\n  var valueStr = valueWithoutExp.toPrecision(precision);\n  if (valueStr.includes('e')) {\n    var BigNumber = value.constructor;\n    valueStr = new BigNumber(valueStr).toFixed();\n  }\n  return valueStr + 'e' + (e >= 0 ? '+' : '') + newExp.toString();\n}\n\n/**\n * Format a number in exponential notation. Like '1.23e+5', '2.3e+0', '3.500e-3'\n * @param {BigNumber} value\n * @param {number} [precision]  Number of digits in formatted output.\n *                              If not provided, the maximum available digits\n *                              is used.\n * @returns {string} str\n */\nexport function toExponential(value, precision) {\n  if (precision !== undefined) {\n    return value.toExponential(precision - 1); // Note the offset of one\n  } else {\n    return value.toExponential();\n  }\n}\n\n/**\n * Format a number with fixed notation.\n * @param {BigNumber} value\n * @param {number} [precision=undefined] Optional number of decimals after the\n *                                       decimal point. Undefined by default.\n */\nexport function toFixed(value, precision) {\n  return value.toFixed(precision);\n}\nfunction _toNumberOrDefault(value, defaultValue) {\n  if (isNumber(value)) {\n    return value;\n  } else if (isBigNumber(value)) {\n    return value.toNumber();\n  } else {\n    return defaultValue;\n  }\n}", "map": {"version": 3, "names": ["isBigNumber", "isNumber", "isInteger", "normalizeFormatOptions", "formatBigNumberToBase", "n", "base", "size", "BigNumberCtor", "constructor", "big2", "suffix", "Error", "greaterThan", "pow", "sub", "lessThan", "mul", "concat", "add", "toBinary", "toOctal", "toHexadecimal", "format", "value", "options", "isFinite", "isNaN", "gt", "notation", "precision", "wordSize", "toFixed", "toExponential", "toEngineering", "lowerExp", "_toNumberOrDefault", "upperExp", "isZero", "str", "rounded", "toSignificantDigits", "exp", "e", "replace", "digits", "arguments", "newExp", "valueWithoutExp", "Math", "valueStr", "toPrecision", "includes", "BigNumber", "toString", "undefined", "defaultValue", "toNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/bignumber/formatter.js"], "sourcesContent": ["import { isBigNumber, isNumber } from '../is.js';\nimport { isInteger, normalizeFormatOptions } from '../number.js';\n\n/**\n * Formats a BigNumber in a given base\n * @param {BigNumber} n\n * @param {number} base\n * @param {number} size\n * @returns {string}\n */\nfunction formatBigNumberToBase(n, base, size) {\n  var BigNumberCtor = n.constructor;\n  var big2 = new BigNumberCtor(2);\n  var suffix = '';\n  if (size) {\n    if (size < 1) {\n      throw new Error('size must be in greater than 0');\n    }\n    if (!isInteger(size)) {\n      throw new Error('size must be an integer');\n    }\n    if (n.greaterThan(big2.pow(size - 1).sub(1)) || n.lessThan(big2.pow(size - 1).mul(-1))) {\n      throw new Error(\"Value must be in range [-2^\".concat(size - 1, \", 2^\").concat(size - 1, \"-1]\"));\n    }\n    if (!n.isInteger()) {\n      throw new Error('Value must be an integer');\n    }\n    if (n.lessThan(0)) {\n      n = n.add(big2.pow(size));\n    }\n    suffix = \"i\".concat(size);\n  }\n  switch (base) {\n    case 2:\n      return \"\".concat(n.toBinary()).concat(suffix);\n    case 8:\n      return \"\".concat(n.toOctal()).concat(suffix);\n    case 16:\n      return \"\".concat(n.toHexadecimal()).concat(suffix);\n    default:\n      throw new Error(\"Base \".concat(base, \" not supported \"));\n  }\n}\n\n/**\n * Convert a BigNumber to a formatted string representation.\n *\n * Syntax:\n *\n *    format(value)\n *    format(value, options)\n *    format(value, precision)\n *    format(value, fn)\n *\n * Where:\n *\n *    {number} value   The value to be formatted\n *    {Object} options An object with formatting options. Available options:\n *                     {string} notation\n *                         Number notation. Choose from:\n *                         'fixed'          Always use regular number notation.\n *                                          For example '123.40' and '14000000'\n *                         'exponential'    Always use exponential notation.\n *                                          For example '1.234e+2' and '1.4e+7'\n *                         'auto' (default) Regular number notation for numbers\n *                                          having an absolute value between\n *                                          `lower` and `upper` bounds, and uses\n *                                          exponential notation elsewhere.\n *                                          Lower bound is included, upper bound\n *                                          is excluded.\n *                                          For example '123.4' and '1.4e7'.\n *                         'bin', 'oct, or\n *                         'hex'            Format the number using binary, octal,\n *                                          or hexadecimal notation.\n *                                          For example '0b1101' and '0x10fe'.\n *                     {number} wordSize    The word size in bits to use for formatting\n *                                          in binary, octal, or hexadecimal notation.\n *                                          To be used only with 'bin', 'oct', or 'hex'\n *                                          values for 'notation' option. When this option\n *                                          is defined the value is formatted as a signed\n *                                          twos complement integer of the given word size\n *                                          and the size suffix is appended to the output.\n *                                          For example\n *                                          format(-1, {notation: 'hex', wordSize: 8}) === '0xffi8'.\n *                                          Default value is undefined.\n *                     {number} precision   A number between 0 and 16 to round\n *                                          the digits of the number.\n *                                          In case of notations 'exponential',\n *                                          'engineering', and 'auto',\n *                                          `precision` defines the total\n *                                          number of significant digits returned.\n *                                          In case of notation 'fixed',\n *                                          `precision` defines the number of\n *                                          significant digits after the decimal\n *                                          point.\n *                                          `precision` is undefined by default.\n *                     {number} lowerExp    Exponent determining the lower boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `-3`.\n *                     {number} upperExp    Exponent determining the upper boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `5`.\n *    {Function} fn    A custom formatting function. Can be used to override the\n *                     built-in notations. Function `fn` is called with `value` as\n *                     parameter and must return a string. Is useful for example to\n *                     format all values inside a matrix in a particular way.\n *\n * Examples:\n *\n *    format(6.4)                                        // '6.4'\n *    format(1240000)                                    // '1.24e6'\n *    format(1/3)                                        // '0.3333333333333333'\n *    format(1/3, 3)                                     // '0.333'\n *    format(21385, 2)                                   // '21000'\n *    format(12e8, {notation: 'fixed'})                  // returns '1200000000'\n *    format(2.3,    {notation: 'fixed', precision: 4})  // returns '2.3000'\n *    format(52.8,   {notation: 'exponential'})          // returns '5.28e+1'\n *    format(12400,  {notation: 'engineering'})          // returns '12.400e+3'\n *\n * @param {BigNumber} value\n * @param {Object | Function | number | BigNumber} [options]\n * @return {string} str The formatted value\n */\nexport function format(value, options) {\n  if (typeof options === 'function') {\n    // handle format(value, fn)\n    return options(value);\n  }\n\n  // handle special cases\n  if (!value.isFinite()) {\n    return value.isNaN() ? 'NaN' : value.gt(0) ? 'Infinity' : '-Infinity';\n  }\n  var {\n    notation,\n    precision,\n    wordSize\n  } = normalizeFormatOptions(options);\n\n  // handle the various notations\n  switch (notation) {\n    case 'fixed':\n      return toFixed(value, precision);\n    case 'exponential':\n      return toExponential(value, precision);\n    case 'engineering':\n      return toEngineering(value, precision);\n    case 'bin':\n      return formatBigNumberToBase(value, 2, wordSize);\n    case 'oct':\n      return formatBigNumberToBase(value, 8, wordSize);\n    case 'hex':\n      return formatBigNumberToBase(value, 16, wordSize);\n    case 'auto':\n      {\n        // determine lower and upper bound for exponential notation.\n        // TODO: implement support for upper and lower to be BigNumbers themselves\n        var lowerExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.lowerExp, -3);\n        var upperExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.upperExp, 5);\n\n        // handle special case zero\n        if (value.isZero()) return '0';\n\n        // determine whether or not to output exponential notation\n        var str;\n        var rounded = value.toSignificantDigits(precision);\n        var exp = rounded.e;\n        if (exp >= lowerExp && exp < upperExp) {\n          // normal number notation\n          str = rounded.toFixed();\n        } else {\n          // exponential notation\n          str = toExponential(value, precision);\n        }\n\n        // remove trailing zeros after the decimal point\n        return str.replace(/((\\.\\d*?)(0+))($|e)/, function () {\n          var digits = arguments[2];\n          var e = arguments[4];\n          return digits !== '.' ? digits + e : e;\n        });\n      }\n    default:\n      throw new Error('Unknown notation \"' + notation + '\". ' + 'Choose \"auto\", \"exponential\", \"fixed\", \"bin\", \"oct\", or \"hex.');\n  }\n}\n\n/**\n * Format a BigNumber in engineering notation. Like '1.23e+6', '2.3e+0', '3.500e-3'\n * @param {BigNumber} value\n * @param {number} [precision]        Optional number of significant figures to return.\n */\nexport function toEngineering(value, precision) {\n  // find nearest lower multiple of 3 for exponent\n  var e = value.e;\n  var newExp = e % 3 === 0 ? e : e < 0 ? e - 3 - e % 3 : e - e % 3;\n\n  // find difference in exponents, and calculate the value without exponent\n  var valueWithoutExp = value.mul(Math.pow(10, -newExp));\n  var valueStr = valueWithoutExp.toPrecision(precision);\n  if (valueStr.includes('e')) {\n    var BigNumber = value.constructor;\n    valueStr = new BigNumber(valueStr).toFixed();\n  }\n  return valueStr + 'e' + (e >= 0 ? '+' : '') + newExp.toString();\n}\n\n/**\n * Format a number in exponential notation. Like '1.23e+5', '2.3e+0', '3.500e-3'\n * @param {BigNumber} value\n * @param {number} [precision]  Number of digits in formatted output.\n *                              If not provided, the maximum available digits\n *                              is used.\n * @returns {string} str\n */\nexport function toExponential(value, precision) {\n  if (precision !== undefined) {\n    return value.toExponential(precision - 1); // Note the offset of one\n  } else {\n    return value.toExponential();\n  }\n}\n\n/**\n * Format a number with fixed notation.\n * @param {BigNumber} value\n * @param {number} [precision=undefined] Optional number of decimals after the\n *                                       decimal point. Undefined by default.\n */\nexport function toFixed(value, precision) {\n  return value.toFixed(precision);\n}\nfunction _toNumberOrDefault(value, defaultValue) {\n  if (isNumber(value)) {\n    return value;\n  } else if (isBigNumber(value)) {\n    return value.toNumber();\n  } else {\n    return defaultValue;\n  }\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,UAAU;AAChD,SAASC,SAAS,EAAEC,sBAAsB,QAAQ,cAAc;;AAEhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC5C,IAAIC,aAAa,GAAGH,CAAC,CAACI,WAAW;EACjC,IAAIC,IAAI,GAAG,IAAIF,aAAa,CAAC,CAAC,CAAC;EAC/B,IAAIG,MAAM,GAAG,EAAE;EACf,IAAIJ,IAAI,EAAE;IACR,IAAIA,IAAI,GAAG,CAAC,EAAE;MACZ,MAAM,IAAIK,KAAK,CAAC,gCAAgC,CAAC;IACnD;IACA,IAAI,CAACV,SAAS,CAACK,IAAI,CAAC,EAAE;MACpB,MAAM,IAAIK,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAIP,CAAC,CAACQ,WAAW,CAACH,IAAI,CAACI,GAAG,CAACP,IAAI,GAAG,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIV,CAAC,CAACW,QAAQ,CAACN,IAAI,CAACI,GAAG,CAACP,IAAI,GAAG,CAAC,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtF,MAAM,IAAIL,KAAK,CAAC,6BAA6B,CAACM,MAAM,CAACX,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,CAACW,MAAM,CAACX,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IACjG;IACA,IAAI,CAACF,CAAC,CAACH,SAAS,CAAC,CAAC,EAAE;MAClB,MAAM,IAAIU,KAAK,CAAC,0BAA0B,CAAC;IAC7C;IACA,IAAIP,CAAC,CAACW,QAAQ,CAAC,CAAC,CAAC,EAAE;MACjBX,CAAC,GAAGA,CAAC,CAACc,GAAG,CAACT,IAAI,CAACI,GAAG,CAACP,IAAI,CAAC,CAAC;IAC3B;IACAI,MAAM,GAAG,GAAG,CAACO,MAAM,CAACX,IAAI,CAAC;EAC3B;EACA,QAAQD,IAAI;IACV,KAAK,CAAC;MACJ,OAAO,EAAE,CAACY,MAAM,CAACb,CAAC,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACF,MAAM,CAACP,MAAM,CAAC;IAC/C,KAAK,CAAC;MACJ,OAAO,EAAE,CAACO,MAAM,CAACb,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACH,MAAM,CAACP,MAAM,CAAC;IAC9C,KAAK,EAAE;MACL,OAAO,EAAE,CAACO,MAAM,CAACb,CAAC,CAACiB,aAAa,CAAC,CAAC,CAAC,CAACJ,MAAM,CAACP,MAAM,CAAC;IACpD;MACE,MAAM,IAAIC,KAAK,CAAC,OAAO,CAACM,MAAM,CAACZ,IAAI,EAAE,iBAAiB,CAAC,CAAC;EAC5D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,MAAMA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACrC,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;IACjC;IACA,OAAOA,OAAO,CAACD,KAAK,CAAC;EACvB;;EAEA;EACA,IAAI,CAACA,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE;IACrB,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC,GAAG,KAAK,GAAGH,KAAK,CAACI,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,WAAW;EACvE;EACA,IAAI;IACFC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAG5B,sBAAsB,CAACsB,OAAO,CAAC;;EAEnC;EACA,QAAQI,QAAQ;IACd,KAAK,OAAO;MACV,OAAOG,OAAO,CAACR,KAAK,EAAEM,SAAS,CAAC;IAClC,KAAK,aAAa;MAChB,OAAOG,aAAa,CAACT,KAAK,EAAEM,SAAS,CAAC;IACxC,KAAK,aAAa;MAChB,OAAOI,aAAa,CAACV,KAAK,EAAEM,SAAS,CAAC;IACxC,KAAK,KAAK;MACR,OAAO1B,qBAAqB,CAACoB,KAAK,EAAE,CAAC,EAAEO,QAAQ,CAAC;IAClD,KAAK,KAAK;MACR,OAAO3B,qBAAqB,CAACoB,KAAK,EAAE,CAAC,EAAEO,QAAQ,CAAC;IAClD,KAAK,KAAK;MACR,OAAO3B,qBAAqB,CAACoB,KAAK,EAAE,EAAE,EAAEO,QAAQ,CAAC;IACnD,KAAK,MAAM;MACT;QACE;QACA;QACA,IAAII,QAAQ,GAAGC,kBAAkB,CAACX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACU,QAAQ,EAAE,CAAC,CAAC,CAAC;QACzG,IAAIE,QAAQ,GAAGD,kBAAkB,CAACX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,QAAQ,EAAE,CAAC,CAAC;;QAExG;QACA,IAAIb,KAAK,CAACc,MAAM,CAAC,CAAC,EAAE,OAAO,GAAG;;QAE9B;QACA,IAAIC,GAAG;QACP,IAAIC,OAAO,GAAGhB,KAAK,CAACiB,mBAAmB,CAACX,SAAS,CAAC;QAClD,IAAIY,GAAG,GAAGF,OAAO,CAACG,CAAC;QACnB,IAAID,GAAG,IAAIP,QAAQ,IAAIO,GAAG,GAAGL,QAAQ,EAAE;UACrC;UACAE,GAAG,GAAGC,OAAO,CAACR,OAAO,CAAC,CAAC;QACzB,CAAC,MAAM;UACL;UACAO,GAAG,GAAGN,aAAa,CAACT,KAAK,EAAEM,SAAS,CAAC;QACvC;;QAEA;QACA,OAAOS,GAAG,CAACK,OAAO,CAAC,qBAAqB,EAAE,YAAY;UACpD,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;UACzB,IAAIH,CAAC,GAAGG,SAAS,CAAC,CAAC,CAAC;UACpB,OAAOD,MAAM,KAAK,GAAG,GAAGA,MAAM,GAAGF,CAAC,GAAGA,CAAC;QACxC,CAAC,CAAC;MACJ;IACF;MACE,MAAM,IAAI/B,KAAK,CAAC,oBAAoB,GAAGiB,QAAQ,GAAG,KAAK,GAAG,+DAA+D,CAAC;EAC9H;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,aAAaA,CAACV,KAAK,EAAEM,SAAS,EAAE;EAC9C;EACA,IAAIa,CAAC,GAAGnB,KAAK,CAACmB,CAAC;EACf,IAAII,MAAM,GAAGJ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;;EAEhE;EACA,IAAIK,eAAe,GAAGxB,KAAK,CAACP,GAAG,CAACgC,IAAI,CAACnC,GAAG,CAAC,EAAE,EAAE,CAACiC,MAAM,CAAC,CAAC;EACtD,IAAIG,QAAQ,GAAGF,eAAe,CAACG,WAAW,CAACrB,SAAS,CAAC;EACrD,IAAIoB,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC1B,IAAIC,SAAS,GAAG7B,KAAK,CAACf,WAAW;IACjCyC,QAAQ,GAAG,IAAIG,SAAS,CAACH,QAAQ,CAAC,CAAClB,OAAO,CAAC,CAAC;EAC9C;EACA,OAAOkB,QAAQ,GAAG,GAAG,IAAIP,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGI,MAAM,CAACO,QAAQ,CAAC,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASrB,aAAaA,CAACT,KAAK,EAAEM,SAAS,EAAE;EAC9C,IAAIA,SAAS,KAAKyB,SAAS,EAAE;IAC3B,OAAO/B,KAAK,CAACS,aAAa,CAACH,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7C,CAAC,MAAM;IACL,OAAON,KAAK,CAACS,aAAa,CAAC,CAAC;EAC9B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASD,OAAOA,CAACR,KAAK,EAAEM,SAAS,EAAE;EACxC,OAAON,KAAK,CAACQ,OAAO,CAACF,SAAS,CAAC;AACjC;AACA,SAASM,kBAAkBA,CAACZ,KAAK,EAAEgC,YAAY,EAAE;EAC/C,IAAIvD,QAAQ,CAACuB,KAAK,CAAC,EAAE;IACnB,OAAOA,KAAK;EACd,CAAC,MAAM,IAAIxB,WAAW,CAACwB,KAAK,CAAC,EAAE;IAC7B,OAAOA,KAAK,CAACiC,QAAQ,CAAC,CAAC;EACzB,CAAC,MAAM;IACL,OAAOD,YAAY;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}