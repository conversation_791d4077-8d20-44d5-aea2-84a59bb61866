import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

interface Exercise {
  id: number;
  title: string;
  question: string;
  exercise_type: string;
  difficulty: string;
  points: number;
  time_limit: number;
  attempts_count: number;
  success_rate: number;
  is_active: boolean;
  created_at: string;
}

const AdminExercises: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState('all');
  const [filterDifficulty, setFilterDifficulty] = useState('all');

  useEffect(() => {
    if (user && user.role !== 'admin') {
      navigate('/');
      return;
    }

    setTimeout(() => {
      setExercises([
        {
          id: 1,
          title: 'Résoudre x + 5 = 12',
          question: 'Quelle est la valeur de x dans l\'équation x + 5 = 12 ?',
          exercise_type: 'multiple_choice',
          difficulty: 'easy',
          points: 10,
          time_limit: 300,
          attempts_count: 156,
          success_rate: 89.7,
          is_active: true,
          created_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          title: 'Équation du premier degré',
          question: 'Résolvez 2x - 6 = 0',
          exercise_type: 'multiple_choice',
          difficulty: 'easy',
          points: 10,
          time_limit: 300,
          attempts_count: 134,
          success_rate: 76.1,
          is_active: true,
          created_at: '2024-01-16T14:20:00Z'
        },
        {
          id: 3,
          title: 'Équation quadratique simple',
          question: 'Résolvez x² = 4',
          exercise_type: 'multiple_choice',
          difficulty: 'medium',
          points: 15,
          time_limit: 600,
          attempts_count: 89,
          success_rate: 62.9,
          is_active: true,
          created_at: '2024-01-17T09:45:00Z'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, [user, navigate]);

  const toggleExerciseStatus = (exerciseId: number) => {
    setExercises(exercises.map(e => 
      e.id === exerciseId ? { ...e, is_active: !e.is_active } : e
    ));
  };

  const deleteExercise = (exerciseId: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet exercice ?')) {
      setExercises(exercises.filter(e => e.id !== exerciseId));
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'Facile';
      case 'medium':
        return 'Moyen';
      case 'hard':
        return 'Difficile';
      default:
        return difficulty;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'QCM';
      case 'equation':
        return 'Équation';
      case 'calculation':
        return 'Calcul';
      default:
        return type;
    }
  };

  const filteredExercises = exercises.filter(exercise => {
    const matchesType = filterType === 'all' || exercise.exercise_type === filterType;
    const matchesDifficulty = filterDifficulty === 'all' || exercise.difficulty === filterDifficulty;
    return matchesType && matchesDifficulty;
  });

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}min`;
  };

  if (!user || user.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Accès refusé</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Vous devez être administrateur pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement des exercices...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <button
          onClick={() => navigate('/admin')}
          className="mb-4 flex items-center text-primary-600 hover:text-primary-700 transition-colors"
        >
          <span className="mr-2">←</span>
          Retour au tableau de bord
        </button>
        
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Gestion des exercices
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              {filteredExercises.length} exercice(s) • Administration HandyMath
            </p>
          </div>
          
          <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium">
            + Nouvel exercice
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type d'exercice
            </label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Tous les types</option>
              <option value="multiple_choice">QCM</option>
              <option value="equation">Équation</option>
              <option value="calculation">Calcul</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Difficulté
            </label>
            <select
              value={filterDifficulty}
              onChange={(e) => setFilterDifficulty(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Toutes les difficultés</option>
              <option value="easy">Facile</option>
              <option value="medium">Moyen</option>
              <option value="hard">Difficile</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredExercises.map((exercise, index) => (
          <motion.div
            key={exercise.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {exercise.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                  {exercise.question}
                </p>
                <div className="flex space-x-2 mb-3">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {getTypeLabel(exercise.exercise_type)}
                  </span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDifficultyColor(exercise.difficulty)}`}>
                    {getDifficultyLabel(exercise.difficulty)}
                  </span>
                </div>
              </div>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                exercise.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {exercise.is_active ? 'Actif' : 'Inactif'}
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
              <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="font-semibold text-gray-900 dark:text-white">
                  {exercise.points} pts
                </div>
                <div className="text-gray-600 dark:text-gray-400">Points</div>
              </div>
              <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="font-semibold text-gray-900 dark:text-white">
                  {formatTime(exercise.time_limit)}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Temps limite</div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
              <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="font-semibold text-gray-900 dark:text-white">
                  {exercise.attempts_count}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Tentatives</div>
              </div>
              <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="font-semibold text-gray-900 dark:text-white">
                  {exercise.success_rate.toFixed(1)}%
                </div>
                <div className="text-gray-600 dark:text-gray-400">Réussite</div>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => navigate(`/exercises/${exercise.id}`)}
                className="flex-1 bg-blue-100 text-blue-800 hover:bg-blue-200 px-3 py-2 rounded text-sm font-medium"
              >
                Voir
              </button>
              <button
                onClick={() => toggleExerciseStatus(exercise.id)}
                className={`flex-1 px-3 py-2 rounded text-sm font-medium ${
                  exercise.is_active
                    ? 'bg-red-100 text-red-800 hover:bg-red-200'
                    : 'bg-green-100 text-green-800 hover:bg-green-200'
                }`}
              >
                {exercise.is_active ? 'Désactiver' : 'Activer'}
              </button>
              <button
                onClick={() => deleteExercise(exercise.id)}
                className="px-3 py-2 bg-red-100 text-red-800 hover:bg-red-200 rounded text-sm font-medium"
              >
                Supprimer
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Total exercices
              </h3>
              <p className="text-3xl font-bold text-blue-600">
                {exercises.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Exercices actifs
              </h3>
              <p className="text-3xl font-bold text-green-600">
                {exercises.filter(e => e.is_active).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Total tentatives
              </h3>
              <p className="text-3xl font-bold text-purple-600">
                {exercises.reduce((sum, e) => sum + e.attempts_count, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-full">
              <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Taux moyen
              </h3>
              <p className="text-3xl font-bold text-yellow-600">
                {(exercises.reduce((sum, e) => sum + e.success_rate, 0) / exercises.length).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminExercises;
