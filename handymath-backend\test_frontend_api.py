#!/usr/bin/env python3
"""
Test des appels API depuis le frontend
"""

import os
import sys
import django
import requests

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import User
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken

def test_authentication():
    """Tester l'authentification et obtenir un token"""
    print("🔐 TEST: Authentification et token")
    print("=" * 40)
    
    # Créer un utilisateur de test s'il n'existe pas
    test_user, created = User.objects.get_or_create(
        username='frontend_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Frontend',
            'last_name': 'Test'
        }
    )
    
    if created or not test_user.check_password('testpass123'):
        test_user.set_password('testpass123')
        test_user.save()
        print(f"👤 Utilisateur créé/mis à jour: {test_user.username}")
    else:
        print(f"👤 Utilisateur existant: {test_user.username}")
    
    # Test de connexion via API JWT
    login_url = 'http://127.0.0.1:8000/api/token/'
    login_data = {
        'username': 'frontend_test_user',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"📡 Connexion API: Status {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('access')
            print(f"✅ Token obtenu: {token[:50]}...")
            return token
        else:
            print(f"❌ Erreur de connexion: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return None

def test_courses_api(token=None):
    """Tester l'API des cours"""
    print(f"\n📚 TEST: API des cours")
    print("=" * 30)
    
    headers = {}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    # Test de l'endpoint des cours
    courses_url = 'http://127.0.0.1:8000/api/courses/'
    
    try:
        response = requests.get(courses_url, headers=headers)
        print(f"📡 GET /api/courses/: Status {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            courses = data.get('courses', [])
            print(f"✅ {len(courses)} cours récupérés")

            # Afficher les 3 premiers cours
            for i, course in enumerate(courses):
                if i < 3:
                    print(f"   📖 {course['title']} (ID: {course['id']})")

            return courses
        else:
            print(f"❌ Erreur: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_course_detail_api(token=None, course_id=8):
    """Tester l'API de détail d'un cours"""
    print(f"\n📖 TEST: API détail du cours {course_id}")
    print("=" * 40)
    
    headers = {}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    # Test de l'endpoint de détail
    detail_url = f'http://127.0.0.1:8000/api/courses/{course_id}/'
    
    try:
        response = requests.get(detail_url, headers=headers)
        print(f"📡 GET /api/courses/{course_id}/: Status {response.status_code}")
        
        if response.status_code == 200:
            course = response.json()
            print(f"✅ Cours récupéré: {course['title']}")
            print(f"   📊 Progression: {course.get('progress_percentage', 0)}%")
            print(f"   📖 Chapitres: {len(course.get('chapters', []))}")
            
            # Afficher les chapitres
            for chapter in course.get('chapters', [])[:2]:
                print(f"      📚 {chapter['title']}: {len(chapter.get('lessons', []))} leçons")
            
            return course
        else:
            print(f"❌ Erreur: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_enrollment_api(token=None, course_id=8):
    """Tester l'API d'inscription"""
    print(f"\n📝 TEST: API d'inscription au cours {course_id}")
    print("=" * 40)
    
    headers = {}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    # Test de l'endpoint d'inscription
    enroll_url = f'http://127.0.0.1:8000/api/courses/{course_id}/enroll/'
    
    try:
        response = requests.post(enroll_url, headers=headers)
        print(f"📡 POST /api/courses/{course_id}/enroll/: Status {response.status_code}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"✅ Inscription réussie: {result.get('message', 'OK')}")
            return True
        else:
            print(f"❌ Erreur d'inscription: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_cors_headers():
    """Tester les headers CORS"""
    print(f"\n🌐 TEST: Headers CORS")
    print("=" * 25)
    
    try:
        response = requests.options('http://127.0.0.1:8000/api/courses/')
        print(f"📡 OPTIONS /api/courses/: Status {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        for header, value in cors_headers.items():
            if value:
                print(f"✅ {header}: {value}")
            else:
                print(f"❌ {header}: Non défini")
                
    except Exception as e:
        print(f"❌ Erreur CORS: {e}")

def main():
    """Fonction principale"""
    try:
        print("🧪 TEST DES APPELS API FRONTEND")
        print("=" * 40)
        
        # 1. Test d'authentification
        token = test_authentication()
        
        # 2. Test de l'API des cours
        courses = test_courses_api(token)
        
        # 3. Test de l'API de détail d'un cours
        if courses:
            first_course_id = courses[0]['id']
            test_course_detail_api(token, first_course_id)
            
            # 4. Test d'inscription
            test_enrollment_api(token, first_course_id)
        
        # 5. Test des headers CORS
        test_cors_headers()
        
        print(f"\n\n✅ TESTS TERMINÉS")
        print("=" * 20)
        print("📋 Instructions pour le frontend:")
        if token:
            print(f"   🔑 Token de test: {token}")
            print("   📝 Utilisateur: frontend_test_user / testpass123")
            print("   🌐 URL de base: http://127.0.0.1:8000/api/")
            print("   📚 Endpoint cours: /api/courses/")
            print("   📖 Endpoint détail: /api/courses/{id}/")
            print("   📝 Endpoint inscription: /api/courses/{id}/enroll/")
        else:
            print("   ❌ Problème d'authentification détecté")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
