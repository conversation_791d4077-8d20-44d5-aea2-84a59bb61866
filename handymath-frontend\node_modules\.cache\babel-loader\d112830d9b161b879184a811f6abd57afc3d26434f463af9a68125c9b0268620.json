{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createVersion } from '../../factoriesAny.js';\nexport var versionDependencies = {\n  createVersion\n};", "map": {"version": 3, "names": ["createVersion", "versionDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesVersion.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createVersion } from '../../factoriesAny.js';\nexport var versionDependencies = {\n  createVersion\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,mBAAmB,GAAG;EAC/BD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}