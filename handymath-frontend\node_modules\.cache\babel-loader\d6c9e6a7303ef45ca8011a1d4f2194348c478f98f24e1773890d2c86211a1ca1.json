{"ast": null, "code": "export var isIntegerDocs = {\n  name: 'isInteger',\n  category: 'Utils',\n  syntax: ['isInteger(x)'],\n  description: 'Test whether a value is an integer number.',\n  examples: ['isInteger(2)', 'isInteger(3.5)', 'isInteger([3, 0.5, -2])'],\n  seealso: ['isNegative', 'isNumeric', 'isPositive', 'isZero']\n};", "map": {"version": 3, "names": ["isIntegerDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isInteger.js"], "sourcesContent": ["export var isIntegerDocs = {\n  name: 'isInteger',\n  category: 'Utils',\n  syntax: ['isInteger(x)'],\n  description: 'Test whether a value is an integer number.',\n  examples: ['isInteger(2)', 'isInteger(3.5)', 'isInteger([3, 0.5, -2])'],\n  seealso: ['isNegative', 'isNumeric', 'isPositive', 'isZero']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,cAAc,CAAC;EACxBC,WAAW,EAAE,4CAA4C;EACzDC,QAAQ,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,yBAAyB,CAAC;EACvEC,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ;AAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}