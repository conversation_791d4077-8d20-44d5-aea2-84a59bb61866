{"ast": null, "code": "export var LOG2EDocs = {\n  name: 'LOG2E',\n  category: 'Constants',\n  syntax: ['LOG2E'],\n  description: 'Returns the base-2 logarithm of E, approximately equal to 1.442',\n  examples: ['LOG2E', 'log(e, 2)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["LOG2EDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/LOG2E.js"], "sourcesContent": ["export var LOG2EDocs = {\n  name: 'LOG2E',\n  category: 'Constants',\n  syntax: ['LOG2E'],\n  description: 'Returns the base-2 logarithm of E, approximately equal to 1.442',\n  examples: ['LOG2E', 'log(e, 2)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,WAAW,EAAE,iEAAiE;EAC9EC,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;EAChCC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}