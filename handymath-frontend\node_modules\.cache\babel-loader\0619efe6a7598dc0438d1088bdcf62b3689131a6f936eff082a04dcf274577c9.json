{"ast": null, "code": "export var expDocs = {\n  name: 'exp',\n  category: 'Arithmetic',\n  syntax: ['exp(x)'],\n  description: 'Calculate the exponent of a value.',\n  examples: ['exp(1.3)', 'e ^ 1.3', 'log(exp(1.3))', 'x = 2.4', '(exp(i*x) == cos(x) + i*sin(x))   # Euler\\'s formula'],\n  seealso: ['expm', 'expm1', 'pow', 'log']\n};", "map": {"version": 3, "names": ["expDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/exp.js"], "sourcesContent": ["export var expDocs = {\n  name: 'exp',\n  category: 'Arithmetic',\n  syntax: ['exp(x)'],\n  description: 'Calculate the exponent of a value.',\n  examples: ['exp(1.3)', 'e ^ 1.3', 'log(exp(1.3))', 'x = 2.4', '(exp(i*x) == cos(x) + i*sin(x))   # Euler\\'s formula'],\n  seealso: ['expm', 'expm1', 'pow', 'log']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,oCAAoC;EACjDC,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE,sDAAsD,CAAC;EACrHC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}