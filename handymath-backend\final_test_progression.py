#!/usr/bin/env python
"""
Test final du système de progression après toutes les corrections
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import Course, Chapter, Lesson, LessonProgress, CourseEnrollment
from django.contrib.auth import get_user_model

User = get_user_model()

def test_new_user_progression():
    """Tester qu'un nouvel utilisateur a 0% de progression"""
    print("🧪 Test de progression pour un nouvel utilisateur...\n")
    
    # Supprimer et recréer l'utilisateur de test
    User.objects.filter(username='final_test_user').delete()
    
    test_user = User.objects.create_user(
        username='final_test_user',
        email='<EMAIL>',
        password='testpass123'
    )
    
    print(f"👤 Utilisateur créé: {test_user.username}")
    
    # Vérifier la progression sur tous les cours publiés
    published_courses = Course.objects.filter(status='published')
    print(f"📚 Cours publiés: {published_courses.count()}")
    
    all_zero = True
    for course in published_courses:
        progress = course.get_progress_for_user(test_user)
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        print(f"   📖 {course.title}: {progress}% ({total_lessons} leçons)")
        
        if progress != 0:
            all_zero = False
            print(f"      ⚠️ PROBLÈME: Progression non nulle pour un nouvel utilisateur!")
    
    if all_zero:
        print(f"\n✅ SUCCÈS: Tous les cours ont 0% de progression pour le nouvel utilisateur")
    else:
        print(f"\n❌ ÉCHEC: Certains cours ont une progression non nulle")
    
    return all_zero

def test_lesson_completion_flow():
    """Tester le flux complet de completion des leçons"""
    print("\n🧪 Test du flux de completion des leçons...\n")
    
    test_user = User.objects.get(username='final_test_user')
    
    # Prendre le premier cours avec des leçons
    course = Course.objects.filter(status='published').first()
    if not course:
        print("❌ Aucun cours publié trouvé")
        return False
    
    print(f"📚 Test avec le cours: {course.title}")
    
    # Obtenir la première leçon
    first_lesson = Lesson.objects.filter(
        chapter__course=course,
        is_published=True,
        order=0
    ).first()
    
    if not first_lesson:
        print("❌ Aucune première leçon trouvée")
        return False
    
    print(f"📝 Première leçon: {first_lesson.title}")
    
    # Vérifier qu'elle est accessible
    accessible = first_lesson.is_accessible_for_user(test_user)
    print(f"🔓 Accessible: {accessible}")
    
    if not accessible:
        print("❌ La première leçon devrait être accessible")
        return False
    
    # Créer la progression
    progress, created = LessonProgress.objects.get_or_create(
        user=test_user,
        lesson=first_lesson,
        defaults={'completed': False, 'time_spent': 0}
    )
    
    print(f"📊 Progression {'créée' if created else 'existante'}")
    
    # Vérifier la progression du cours avant completion
    progress_before = course.get_progress_for_user(test_user)
    print(f"📈 Progression du cours avant: {progress_before}%")
    
    # Compléter la leçon
    progress.completed = True
    progress.time_spent = 300
    progress.save()
    
    print(f"✅ Leçon marquée comme terminée")
    
    # Vérifier la progression du cours après completion
    progress_after = course.get_progress_for_user(test_user)
    print(f"📈 Progression du cours après: {progress_after}%")
    
    # Calculer la progression attendue
    total_lessons = Lesson.objects.filter(
        chapter__course=course,
        is_published=True
    ).count()
    expected_progress = (1 / total_lessons) * 100
    
    print(f"🧮 Progression attendue: {expected_progress}%")
    
    if abs(progress_after - expected_progress) < 0.1:
        print(f"✅ SUCCÈS: Progression calculée correctement")
        return True
    else:
        print(f"❌ ÉCHEC: Progression incorrecte")
        return False

def test_lesson_accessibility_logic():
    """Tester la logique d'accessibilité des leçons"""
    print("\n🧪 Test de la logique d'accessibilité...\n")
    
    test_user = User.objects.get(username='final_test_user')
    
    # Prendre un cours avec plusieurs leçons
    course = Course.objects.filter(status='published').annotate(
        lesson_count=models.Count('chapters__lessons', filter=models.Q(chapters__lessons__is_published=True))
    ).filter(lesson_count__gt=1).first()
    
    if not course:
        print("❌ Aucun cours avec plusieurs leçons trouvé")
        return False
    
    print(f"📚 Test avec le cours: {course.title}")
    
    # Obtenir les leçons du premier chapitre
    first_chapter = course.chapters.first()
    lessons = list(first_chapter.lessons.filter(is_published=True).order_by('order'))
    
    if len(lessons) < 2:
        print("❌ Pas assez de leçons pour tester")
        return False
    
    print(f"📖 Chapitre: {first_chapter.title} ({len(lessons)} leçons)")
    
    # Vérifier que la première leçon est accessible
    first_lesson = lessons[0]
    first_accessible = first_lesson.is_accessible_for_user(test_user)
    print(f"   📝 {first_lesson.title} (ordre {first_lesson.order}): {'🔓 Accessible' if first_accessible else '🔒 Verrouillée'}")
    
    if not first_accessible:
        print("❌ La première leçon devrait être accessible")
        return False
    
    # Vérifier que la deuxième leçon est verrouillée
    second_lesson = lessons[1]
    second_accessible = second_lesson.is_accessible_for_user(test_user)
    print(f"   📝 {second_lesson.title} (ordre {second_lesson.order}): {'🔓 Accessible' if second_accessible else '🔒 Verrouillée'}")
    
    if second_accessible:
        print("❌ La deuxième leçon devrait être verrouillée")
        return False
    
    # Compléter la première leçon
    progress, _ = LessonProgress.objects.get_or_create(
        user=test_user,
        lesson=first_lesson,
        defaults={'completed': False, 'time_spent': 0}
    )
    progress.completed = True
    progress.save()
    
    print(f"   ✅ Première leçon terminée")
    
    # Vérifier que la deuxième leçon est maintenant accessible
    second_accessible_after = second_lesson.is_accessible_for_user(test_user)
    print(f"   📝 {second_lesson.title} maintenant: {'🔓 Accessible' if second_accessible_after else '🔒 Verrouillée'}")
    
    if not second_accessible_after:
        print("❌ La deuxième leçon devrait être accessible après completion de la première")
        return False
    
    print("✅ SUCCÈS: Logique d'accessibilité fonctionne correctement")
    return True

def main():
    print("🚀 Test final du système de progression après corrections...\n")
    
    try:
        # Import nécessaire pour les annotations
        from django.db import models
        from django.db.models import Q, Count
        
        # Ajouter les imports au niveau global
        globals()['models'] = models
        
        test1 = test_new_user_progression()
        test2 = test_lesson_completion_flow()
        test3 = test_lesson_accessibility_logic()
        
        print(f"\n📋 Résultats des tests:")
        print(f"   {'✅' if test1 else '❌'} Progression nouvel utilisateur")
        print(f"   {'✅' if test2 else '❌'} Flux de completion")
        print(f"   {'✅' if test3 else '❌'} Logique d'accessibilité")
        
        if all([test1, test2, test3]):
            print(f"\n🎉 TOUS LES TESTS RÉUSSIS!")
            print(f"Le système de progression fonctionne correctement.")
        else:
            print(f"\n⚠️ Certains tests ont échoué")
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
