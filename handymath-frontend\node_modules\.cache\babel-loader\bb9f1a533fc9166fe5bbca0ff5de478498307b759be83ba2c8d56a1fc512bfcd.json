{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'hasNumericValue';\nvar dependencies = ['typed', 'isNumeric'];\nexport var createHasNumericValue = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    isNumeric\n  } = _ref;\n  /**\n   * Test whether a value is an numeric value.\n   *\n   * In case of a string, true is returned if the string contains a numeric value.\n   *\n   * Syntax:\n   *\n   *     math.hasNumericValue(x)\n   *\n   * Examples:\n   *\n   *    math.hasNumericValue(2)                     // returns true\n   *    math.hasNumericValue('2')                   // returns true\n   *    math.isNumeric('2')                         // returns false\n   *    math.hasNumericValue(0)                     // returns true\n   *    math.hasNumericValue(math.bignumber('500')) // returns true\n   *    math.hasNumericValue(math.bigint('42'))     // returns true\n   *    math.hasNumericValue(42n)                   // returns true\n   *    math.hasNumericValue(math.fraction(4))      // returns true\n   *    math.hasNumericValue(math.complex('2-4i'))  // returns false\n   *    math.hasNumericValue(false)                 // returns true\n   *    math.hasNumericValue([2.3, 'foo', false])   // returns [true, false, true]\n   *\n   * See also:\n   *\n   *    isZero, isPositive, isNegative, isInteger, isNumeric\n   *\n   * @param {*} x       Value to be tested\n   * @return {boolean}  Returns true when `x` is a `number`, `BigNumber`,\n   *                    `Fraction`, `Boolean`, or a `String` containing number. Returns false for other types.\n   *                    Throws an error in case of unknown types.\n   */\n  return typed(name, {\n    boolean: () => true,\n    string: function string(x) {\n      return x.trim().length > 0 && !isNaN(Number(x));\n    },\n    any: function any(x) {\n      return isNumeric(x);\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createHasNumericValue", "_ref", "typed", "isNumeric", "boolean", "string", "x", "trim", "length", "isNaN", "Number", "any"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/hasNumericValue.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'hasNumericValue';\nvar dependencies = ['typed', 'isNumeric'];\nexport var createHasNumericValue = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    isNumeric\n  } = _ref;\n  /**\n   * Test whether a value is an numeric value.\n   *\n   * In case of a string, true is returned if the string contains a numeric value.\n   *\n   * Syntax:\n   *\n   *     math.hasNumericValue(x)\n   *\n   * Examples:\n   *\n   *    math.hasNumericValue(2)                     // returns true\n   *    math.hasNumericValue('2')                   // returns true\n   *    math.isNumeric('2')                         // returns false\n   *    math.hasNumericValue(0)                     // returns true\n   *    math.hasNumericValue(math.bignumber('500')) // returns true\n   *    math.hasNumericValue(math.bigint('42'))     // returns true\n   *    math.hasNumericValue(42n)                   // returns true\n   *    math.hasNumericValue(math.fraction(4))      // returns true\n   *    math.hasNumericValue(math.complex('2-4i'))  // returns false\n   *    math.hasNumericValue(false)                 // returns true\n   *    math.hasNumericValue([2.3, 'foo', false])   // returns [true, false, true]\n   *\n   * See also:\n   *\n   *    isZero, isPositive, isNegative, isInteger, isNumeric\n   *\n   * @param {*} x       Value to be tested\n   * @return {boolean}  Returns true when `x` is a `number`, `BigNumber`,\n   *                    `Fraction`, `Boolean`, or a `String` containing number. Returns false for other types.\n   *                    Throws an error in case of unknown types.\n   */\n  return typed(name, {\n    boolean: () => true,\n    string: function string(x) {\n      return x.trim().length > 0 && !isNaN(Number(x));\n    },\n    any: function any(x) {\n      return isNumeric(x);\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,iBAAiB;AAC5B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC;AACzC,OAAO,IAAIC,qBAAqB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACpF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBM,OAAO,EAAEA,CAAA,KAAM,IAAI;IACnBC,MAAM,EAAE,SAASA,MAAMA,CAACC,CAAC,EAAE;MACzB,OAAOA,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACJ,CAAC,CAAC,CAAC;IACjD,CAAC;IACDK,GAAG,EAAE,SAASA,GAAGA,CAACL,CAAC,EAAE;MACnB,OAAOH,SAAS,CAACG,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}