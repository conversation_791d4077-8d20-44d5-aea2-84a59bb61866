{"ast": null, "code": "export var solveODEDocs = {\n  name: 'solveODE',\n  category: 'Numeric',\n  syntax: ['solveODE(func, tspan, y0)', 'solveODE(func, tspan, y0, options)'],\n  description: 'Numerical Integration of Ordinary Differential Equations.',\n  examples: ['f(t,y) = y', 'tspan = [0, 4]', 'solveODE(f, tspan, 1)', 'solveODE(f, tspan, [1, 2])', 'solveODE(f, tspan, 1, { method:\"RK23\", maxStep:0.1 })'],\n  seealso: ['derivative', 'simplifyCore']\n};", "map": {"version": 3, "names": ["solveODEDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/numeric/solveODE.js"], "sourcesContent": ["export var solveODEDocs = {\n  name: 'solveODE',\n  category: 'Numeric',\n  syntax: ['solveODE(func, tspan, y0)', 'solveODE(func, tspan, y0, options)'],\n  description: 'Numerical Integration of Ordinary Differential Equations.',\n  examples: ['f(t,y) = y', 'tspan = [0, 4]', 'solveODE(f, tspan, 1)', 'solveODE(f, tspan, [1, 2])', 'solveODE(f, tspan, 1, { method:\"RK23\", maxStep:0.1 })'],\n  seealso: ['derivative', 'simplifyCore']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,2BAA2B,EAAE,oCAAoC,CAAC;EAC3EC,WAAW,EAAE,2DAA2D;EACxEC,QAAQ,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,uDAAuD,CAAC;EAC1JC,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}