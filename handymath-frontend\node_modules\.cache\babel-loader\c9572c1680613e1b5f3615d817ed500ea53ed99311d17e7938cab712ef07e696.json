{"ast": null, "code": "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() : typeof define === 'function' && define.amd ? define(factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global[\"'typed'\"] = factory());\n})(this, function () {\n  'use strict';\n\n  function ok() {\n    return true;\n  }\n  function notOk() {\n    return false;\n  }\n  function undef() {\n    return undefined;\n  }\n  const NOT_TYPED_FUNCTION = 'Argument is not a typed-function.';\n\n  /**\n   * @typedef {{\n   *   params: Param[],\n   *   fn: function,\n   *   test: function,\n   *   implementation: function\n   * }} Signature\n   *\n   * @typedef {{\n   *   types: Type[],\n   *   hasAny: boolean,\n   *   hasConversion: boolean,\n   *   restParam: boolean\n   * }} Param\n   *\n   * @typedef {{\n   *   name: string,\n   *   typeIndex: number,\n   *   test: function,\n   *   isAny: boolean,\n   *   conversion?: ConversionDef,\n   *   conversionIndex: number,\n   * }} Type\n   *\n   * @typedef {{\n   *   from: string,\n   *   to: string,\n   *   convert: function (*) : *\n   * }} ConversionDef\n   *\n   * @typedef {{\n   *   name: string,\n   *   test: function(*) : boolean,\n   *   isAny?: boolean\n   * }} TypeDef\n   */\n\n  /**\n   * @returns {() => function}\n   */\n  function create() {\n    // data type tests\n\n    /**\n     * Returns true if the argument is a non-null \"plain\" object\n     */\n    function isPlainObject(x) {\n      return typeof x === 'object' && x !== null && x.constructor === Object;\n    }\n    const _types = [{\n      name: 'number',\n      test: function (x) {\n        return typeof x === 'number';\n      }\n    }, {\n      name: 'string',\n      test: function (x) {\n        return typeof x === 'string';\n      }\n    }, {\n      name: 'boolean',\n      test: function (x) {\n        return typeof x === 'boolean';\n      }\n    }, {\n      name: 'Function',\n      test: function (x) {\n        return typeof x === 'function';\n      }\n    }, {\n      name: 'Array',\n      test: Array.isArray\n    }, {\n      name: 'Date',\n      test: function (x) {\n        return x instanceof Date;\n      }\n    }, {\n      name: 'RegExp',\n      test: function (x) {\n        return x instanceof RegExp;\n      }\n    }, {\n      name: 'Object',\n      test: isPlainObject\n    }, {\n      name: 'null',\n      test: function (x) {\n        return x === null;\n      }\n    }, {\n      name: 'undefined',\n      test: function (x) {\n        return x === undefined;\n      }\n    }];\n    const anyType = {\n      name: 'any',\n      test: ok,\n      isAny: true\n    };\n\n    // Data structures to track the types. As these are local variables in\n    // create(), each typed universe will get its own copy, but the variables\n    // will only be accessible through the (closures of the) functions supplied\n    // as properties of the typed object, not directly.\n    // These will be initialized in clear() below\n    let typeMap; // primary store of all types\n    let typeList; // Array of just type names, for the sake of ordering\n\n    // And similar data structures for the type conversions:\n    let nConversions = 0;\n    // the actual conversions are stored on a property of the destination types\n\n    // This is a temporary object, will be replaced with a function at the end\n    let typed = {\n      createCount: 0\n    };\n\n    /**\n     * Takes a type name and returns the corresponding official type object\n     * for that type.\n     *\n     * @param {string} typeName\n     * @returns {TypeDef} type\n     */\n    function findType(typeName) {\n      const type = typeMap.get(typeName);\n      if (type) {\n        return type;\n      }\n      // Remainder is error handling\n      let message = 'Unknown type \"' + typeName + '\"';\n      const name = typeName.toLowerCase();\n      let otherName;\n      for (otherName of typeList) {\n        if (otherName.toLowerCase() === name) {\n          message += '. Did you mean \"' + otherName + '\" ?';\n          break;\n        }\n      }\n      throw new TypeError(message);\n    }\n\n    /**\n     * Adds an array `types` of type definitions to this typed instance.\n     * Each type definition should be an object with properties:\n     * 'name' - a string giving the name of the type; 'test' - function\n     * returning a boolean that tests membership in the type; and optionally\n     * 'isAny' - true only for the 'any' type.\n     *\n     * The second optional argument, `before`, gives the name of a type that\n     * these types should be added before. The new types are added in the\n     * order specified.\n     * @param {TypeDef[]} types\n     * @param {string | boolean} [beforeSpec='any'] before\n     */\n    function addTypes(types) {\n      let beforeSpec = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'any';\n      const beforeIndex = beforeSpec ? findType(beforeSpec).index : typeList.length;\n      const newTypes = [];\n      for (let i = 0; i < types.length; ++i) {\n        if (!types[i] || typeof types[i].name !== 'string' || typeof types[i].test !== 'function') {\n          throw new TypeError('Object with properties {name: string, test: function} expected');\n        }\n        const typeName = types[i].name;\n        if (typeMap.has(typeName)) {\n          throw new TypeError('Duplicate type name \"' + typeName + '\"');\n        }\n        newTypes.push(typeName);\n        typeMap.set(typeName, {\n          name: typeName,\n          test: types[i].test,\n          isAny: types[i].isAny,\n          index: beforeIndex + i,\n          conversionsTo: [] // Newly added type can't have any conversions to it\n        });\n      }\n      // update the typeList\n      const affectedTypes = typeList.slice(beforeIndex);\n      typeList = typeList.slice(0, beforeIndex).concat(newTypes).concat(affectedTypes);\n      // Fix the indices\n      for (let i = beforeIndex + newTypes.length; i < typeList.length; ++i) {\n        typeMap.get(typeList[i]).index = i;\n      }\n    }\n\n    /**\n     * Removes all types and conversions from this typed instance.\n     * May cause previously constructed typed-functions to throw\n     * strange errors when they are called with types that do not\n     * match any of their signatures.\n     */\n    function clear() {\n      typeMap = new Map();\n      typeList = [];\n      nConversions = 0;\n      addTypes([anyType], false);\n    }\n\n    // initialize the types to the default list\n    clear();\n    addTypes(_types);\n\n    /**\n     * Removes all conversions, leaving the types alone.\n     */\n    function clearConversions() {\n      let typeName;\n      for (typeName of typeList) {\n        typeMap.get(typeName).conversionsTo = [];\n      }\n      nConversions = 0;\n    }\n\n    /**\n     * Find the type names that match a value.\n     * @param {*} value\n     * @return {string[]} Array of names of types for which\n     *                  the type test matches the value.\n     */\n    function findTypeNames(value) {\n      const matches = typeList.filter(name => {\n        const type = typeMap.get(name);\n        return !type.isAny && type.test(value);\n      });\n      if (matches.length) {\n        return matches;\n      }\n      return ['any'];\n    }\n\n    /**\n     * Check if an entity is a typed function created by any instance\n     * @param {any} entity\n     * @returns {boolean}\n     */\n    function isTypedFunction(entity) {\n      return entity && typeof entity === 'function' && '_typedFunctionData' in entity;\n    }\n\n    /**\n     * Find a specific signature from a (composed) typed function, for example:\n     *\n     *   typed.findSignature(fn, ['number', 'string'])\n     *   typed.findSignature(fn, 'number, string')\n     *   typed.findSignature(fn, 'number,string', {exact: true})\n     *\n     * This function findSignature will by default return the best match to\n     * the given signature, possibly employing type conversions.\n     *\n     * The (optional) third argument is a plain object giving options\n     * controlling the signature search. Currently the only implemented\n     * option is `exact`: if specified as true (default is false), only\n     * exact matches will be returned (i.e. signatures for which `fn` was\n     * directly defined). Note that a (possibly different) type matching\n     * `any`, or one or more instances of TYPE matching `...TYPE` are\n     * considered exact matches in this regard, as no conversions are used.\n     *\n     * This function returns a \"signature\" object, as does `typed.resolve()`,\n     * which is a plain object with four keys: `params` (the array of parameters\n     * for this signature), `fn` (the originally supplied function for this\n     * signature), `test` (a generated function that determines if an argument\n     * list matches this signature, and `implementation` (the function to call\n     * on a matching argument list, that performs conversions if necessary and\n     * then calls the originally supplied function).\n     *\n     * @param {Function} fn                   A typed-function\n     * @param {string | string[]} signature\n     *     Signature to be found, can be an array or a comma separated string.\n     * @param {object} options  Controls the signature search as documented\n     * @return {{ params: Param[], fn: function, test: function, implementation: function }}\n     *     Returns the matching signature, or throws an error when no signature\n     *     is found.\n     */\n    function findSignature(fn, signature, options) {\n      if (!isTypedFunction(fn)) {\n        throw new TypeError(NOT_TYPED_FUNCTION);\n      }\n\n      // Canonicalize input\n      const exact = options && options.exact;\n      const stringSignature = Array.isArray(signature) ? signature.join(',') : signature;\n      const params = parseSignature(stringSignature);\n      const canonicalSignature = stringifyParams(params);\n\n      // First hope we get lucky and exactly match a signature\n      if (!exact || canonicalSignature in fn.signatures) {\n        // OK, we can check the internal signatures\n        const match = fn._typedFunctionData.signatureMap.get(canonicalSignature);\n        if (match) {\n          return match;\n        }\n      }\n\n      // Oh well, we did not; so we have to go back and check the parameters\n      // one by one, in order to catch things like `any` and rest params.\n      // Note here we can assume there is at least one parameter, because\n      // the empty signature would have matched successfully above.\n      const nParams = params.length;\n      let remainingSignatures;\n      if (exact) {\n        remainingSignatures = [];\n        let name;\n        for (name in fn.signatures) {\n          remainingSignatures.push(fn._typedFunctionData.signatureMap.get(name));\n        }\n      } else {\n        remainingSignatures = fn._typedFunctionData.signatures;\n      }\n      for (let i = 0; i < nParams; ++i) {\n        const want = params[i];\n        const filteredSignatures = [];\n        let possibility;\n        for (possibility of remainingSignatures) {\n          const have = getParamAtIndex(possibility.params, i);\n          if (!have || want.restParam && !have.restParam) {\n            continue;\n          }\n          if (!have.hasAny) {\n            // have to check all of the wanted types are available\n            const haveTypes = paramTypeSet(have);\n            if (want.types.some(wtype => !haveTypes.has(wtype.name))) {\n              continue;\n            }\n          }\n          // OK, this looks good\n          filteredSignatures.push(possibility);\n        }\n        remainingSignatures = filteredSignatures;\n        if (remainingSignatures.length === 0) break;\n      }\n      // Return the first remaining signature that was totally matched:\n      let candidate;\n      for (candidate of remainingSignatures) {\n        if (candidate.params.length <= nParams) {\n          return candidate;\n        }\n      }\n      throw new TypeError('Signature not found (signature: ' + (fn.name || 'unnamed') + '(' + stringifyParams(params, ', ') + '))');\n    }\n\n    /**\n     * Find the proper function to call for a specific signature from\n     * a (composed) typed function, for example:\n     *\n     *   typed.find(fn, ['number', 'string'])\n     *   typed.find(fn, 'number, string')\n     *   typed.find(fn, 'number,string', {exact: true})\n     *\n     * This function find will by default return the best match to\n     * the given signature, possibly employing type conversions (and returning\n     * a function that will perform those conversions as needed). The\n     * (optional) third argument is a plain object giving options contolling\n     * the signature search. Currently only the option `exact` is implemented,\n     * which defaults to \"false\". If `exact` is specified as true, then only\n     * exact matches will be returned (i.e. signatures for which `fn` was\n     * directly defined). Uses of `any` and `...TYPE` are considered exact if\n     * no conversions are necessary to apply the corresponding function.\n     *\n     * @param {Function} fn                   A typed-function\n     * @param {string | string[]} signature\n     *     Signature to be found, can be an array or a comma separated string.\n     * @param {object} options  Controls the signature match as documented\n     * @return {function}\n     *     Returns the function to call for the given signature, or throws an\n     *     error if no match is found.\n     */\n    function find(fn, signature, options) {\n      return findSignature(fn, signature, options).implementation;\n    }\n\n    /**\n     * Convert a given value to another data type, specified by type name.\n     *\n     * @param {*} value\n     * @param {string} typeName\n     */\n    function convert(value, typeName) {\n      // check conversion is needed\n      const type = findType(typeName);\n      if (type.test(value)) {\n        return value;\n      }\n      const conversions = type.conversionsTo;\n      if (conversions.length === 0) {\n        throw new Error('There are no conversions to ' + typeName + ' defined.');\n      }\n      for (let i = 0; i < conversions.length; i++) {\n        const fromType = findType(conversions[i].from);\n        if (fromType.test(value)) {\n          return conversions[i].convert(value);\n        }\n      }\n      throw new Error('Cannot convert ' + value + ' to ' + typeName);\n    }\n\n    /**\n     * Stringify parameters in a normalized way\n     * @param {Param[]} params\n     * @param {string} [','] separator\n     * @return {string}\n     */\n    function stringifyParams(params) {\n      let separator = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ',';\n      return params.map(p => p.name).join(separator);\n    }\n\n    /**\n     * Parse a parameter, like \"...number | boolean\"\n     * @param {string} param\n     * @return {Param} param\n     */\n    function parseParam(param) {\n      const restParam = param.indexOf('...') === 0;\n      const types = !restParam ? param : param.length > 3 ? param.slice(3) : 'any';\n      const typeDefs = types.split('|').map(s => findType(s.trim()));\n      let hasAny = false;\n      let paramName = restParam ? '...' : '';\n      const exactTypes = typeDefs.map(function (type) {\n        hasAny = type.isAny || hasAny;\n        paramName += type.name + '|';\n        return {\n          name: type.name,\n          typeIndex: type.index,\n          test: type.test,\n          isAny: type.isAny,\n          conversion: null,\n          conversionIndex: -1\n        };\n      });\n      return {\n        types: exactTypes,\n        name: paramName.slice(0, -1),\n        // remove trailing '|' from above\n        hasAny,\n        hasConversion: false,\n        restParam\n      };\n    }\n\n    /**\n     * Expands a parsed parameter with the types available from currently\n     * defined conversions.\n     * @param {Param} param\n     * @return {Param} param\n     */\n    function expandParam(param) {\n      const typeNames = param.types.map(t => t.name);\n      const matchingConversions = availableConversions(typeNames);\n      let hasAny = param.hasAny;\n      let newName = param.name;\n      const convertibleTypes = matchingConversions.map(function (conversion) {\n        const type = findType(conversion.from);\n        hasAny = type.isAny || hasAny;\n        newName += '|' + conversion.from;\n        return {\n          name: conversion.from,\n          typeIndex: type.index,\n          test: type.test,\n          isAny: type.isAny,\n          conversion,\n          conversionIndex: conversion.index\n        };\n      });\n      return {\n        types: param.types.concat(convertibleTypes),\n        name: newName,\n        hasAny,\n        hasConversion: convertibleTypes.length > 0,\n        restParam: param.restParam\n      };\n    }\n\n    /**\n     * Return the set of type names in a parameter.\n     * Caches the result for efficiency\n     *\n     * @param {Param} param\n     * @return {Set<string>} typenames\n     */\n    function paramTypeSet(param) {\n      if (!param.typeSet) {\n        param.typeSet = new Set();\n        param.types.forEach(type => param.typeSet.add(type.name));\n      }\n      return param.typeSet;\n    }\n\n    /**\n     * Parse a signature with comma separated parameters,\n     * like \"number | boolean, ...string\"\n     *\n     * @param {string} signature\n     * @return {Param[]} params\n     */\n    function parseSignature(rawSignature) {\n      const params = [];\n      if (typeof rawSignature !== 'string') {\n        throw new TypeError('Signatures must be strings');\n      }\n      const signature = rawSignature.trim();\n      if (signature === '') {\n        return params;\n      }\n      const rawParams = signature.split(',');\n      for (let i = 0; i < rawParams.length; ++i) {\n        const parsedParam = parseParam(rawParams[i].trim());\n        if (parsedParam.restParam && i !== rawParams.length - 1) {\n          throw new SyntaxError('Unexpected rest parameter \"' + rawParams[i] + '\": ' + 'only allowed for the last parameter');\n        }\n        // if invalid, short-circuit (all the types may have been filtered)\n        if (parsedParam.types.length === 0) {\n          return null;\n        }\n        params.push(parsedParam);\n      }\n      return params;\n    }\n\n    /**\n     * Test whether a set of params contains a restParam\n     * @param {Param[]} params\n     * @return {boolean} Returns true when the last parameter is a restParam\n     */\n    function hasRestParam(params) {\n      const param = last(params);\n      return param ? param.restParam : false;\n    }\n\n    /**\n     * Create a type test for a single parameter, which can have one or multiple\n     * types.\n     * @param {Param} param\n     * @return {function(x: *) : boolean} Returns a test function\n     */\n    function compileTest(param) {\n      if (!param || param.types.length === 0) {\n        // nothing to do\n        return ok;\n      } else if (param.types.length === 1) {\n        return findType(param.types[0].name).test;\n      } else if (param.types.length === 2) {\n        const test0 = findType(param.types[0].name).test;\n        const test1 = findType(param.types[1].name).test;\n        return function or(x) {\n          return test0(x) || test1(x);\n        };\n      } else {\n        // param.types.length > 2\n        const tests = param.types.map(function (type) {\n          return findType(type.name).test;\n        });\n        return function or(x) {\n          for (let i = 0; i < tests.length; i++) {\n            if (tests[i](x)) {\n              return true;\n            }\n          }\n          return false;\n        };\n      }\n    }\n\n    /**\n     * Create a test for all parameters of a signature\n     * @param {Param[]} params\n     * @return {function(args: Array<*>) : boolean}\n     */\n    function compileTests(params) {\n      let tests, test0, test1;\n      if (hasRestParam(params)) {\n        // variable arguments like '...number'\n        tests = initial(params).map(compileTest);\n        const varIndex = tests.length;\n        const lastTest = compileTest(last(params));\n        const testRestParam = function (args) {\n          for (let i = varIndex; i < args.length; i++) {\n            if (!lastTest(args[i])) {\n              return false;\n            }\n          }\n          return true;\n        };\n        return function testArgs(args) {\n          for (let i = 0; i < tests.length; i++) {\n            if (!tests[i](args[i])) {\n              return false;\n            }\n          }\n          return testRestParam(args) && args.length >= varIndex + 1;\n        };\n      } else {\n        // no variable arguments\n        if (params.length === 0) {\n          return function testArgs(args) {\n            return args.length === 0;\n          };\n        } else if (params.length === 1) {\n          test0 = compileTest(params[0]);\n          return function testArgs(args) {\n            return test0(args[0]) && args.length === 1;\n          };\n        } else if (params.length === 2) {\n          test0 = compileTest(params[0]);\n          test1 = compileTest(params[1]);\n          return function testArgs(args) {\n            return test0(args[0]) && test1(args[1]) && args.length === 2;\n          };\n        } else {\n          // arguments.length > 2\n          tests = params.map(compileTest);\n          return function testArgs(args) {\n            for (let i = 0; i < tests.length; i++) {\n              if (!tests[i](args[i])) {\n                return false;\n              }\n            }\n            return args.length === tests.length;\n          };\n        }\n      }\n    }\n\n    /**\n     * Find the parameter at a specific index of a Params list.\n     * Handles rest parameters.\n     * @param {Param[]} params\n     * @param {number} index\n     * @return {Param | null} Returns the matching parameter when found,\n     *                        null otherwise.\n     */\n    function getParamAtIndex(params, index) {\n      return index < params.length ? params[index] : hasRestParam(params) ? last(params) : null;\n    }\n\n    /**\n     * Get all type names of a parameter\n     * @param {Params[]} params\n     * @param {number} index\n     * @return {string[]} Returns an array with type names\n     */\n    function getTypeSetAtIndex(params, index) {\n      const param = getParamAtIndex(params, index);\n      if (!param) {\n        return new Set();\n      }\n      return paramTypeSet(param);\n    }\n\n    /**\n     * Test whether a type is an exact type or conversion\n     * @param {Type} type\n     * @return {boolean} Returns true when\n     */\n    function isExactType(type) {\n      return type.conversion === null || type.conversion === undefined;\n    }\n\n    /**\n     * Helper function for creating error messages: create an array with\n     * all available types on a specific argument index.\n     * @param {Signature[]} signatures\n     * @param {number} index\n     * @return {string[]} Returns an array with available types\n     */\n    function mergeExpectedParams(signatures, index) {\n      const typeSet = new Set();\n      signatures.forEach(signature => {\n        const paramSet = getTypeSetAtIndex(signature.params, index);\n        let name;\n        for (name of paramSet) {\n          typeSet.add(name);\n        }\n      });\n      return typeSet.has('any') ? ['any'] : Array.from(typeSet);\n    }\n\n    /**\n     * Create\n     * @param {string} name             The name of the function\n     * @param {array.<*>} args          The actual arguments passed to the function\n     * @param {Signature[]} signatures  A list with available signatures\n     * @return {TypeError} Returns a type error with additional data\n     *                     attached to it in the property `data`\n     */\n    function createError(name, args, signatures) {\n      let err, expected;\n      const _name = name || 'unnamed';\n\n      // test for wrong type at some index\n      let matchingSignatures = signatures;\n      let index;\n      for (index = 0; index < args.length; index++) {\n        const nextMatchingDefs = [];\n        matchingSignatures.forEach(signature => {\n          const param = getParamAtIndex(signature.params, index);\n          const test = compileTest(param);\n          if ((index < signature.params.length || hasRestParam(signature.params)) && test(args[index])) {\n            nextMatchingDefs.push(signature);\n          }\n        });\n        if (nextMatchingDefs.length === 0) {\n          // no matching signatures anymore, throw error \"wrong type\"\n          expected = mergeExpectedParams(matchingSignatures, index);\n          if (expected.length > 0) {\n            const actualTypes = findTypeNames(args[index]);\n            err = new TypeError('Unexpected type of argument in function ' + _name + ' (expected: ' + expected.join(' or ') + ', actual: ' + actualTypes.join(' | ') + ', index: ' + index + ')');\n            err.data = {\n              category: 'wrongType',\n              fn: _name,\n              index,\n              actual: actualTypes,\n              expected\n            };\n            return err;\n          }\n        } else {\n          matchingSignatures = nextMatchingDefs;\n        }\n      }\n\n      // test for too few arguments\n      const lengths = matchingSignatures.map(function (signature) {\n        return hasRestParam(signature.params) ? Infinity : signature.params.length;\n      });\n      if (args.length < Math.min.apply(null, lengths)) {\n        expected = mergeExpectedParams(matchingSignatures, index);\n        err = new TypeError('Too few arguments in function ' + _name + ' (expected: ' + expected.join(' or ') + ', index: ' + args.length + ')');\n        err.data = {\n          category: 'tooFewArgs',\n          fn: _name,\n          index: args.length,\n          expected\n        };\n        return err;\n      }\n\n      // test for too many arguments\n      const maxLength = Math.max.apply(null, lengths);\n      if (args.length > maxLength) {\n        err = new TypeError('Too many arguments in function ' + _name + ' (expected: ' + maxLength + ', actual: ' + args.length + ')');\n        err.data = {\n          category: 'tooManyArgs',\n          fn: _name,\n          index: args.length,\n          expectedLength: maxLength\n        };\n        return err;\n      }\n\n      // Generic error\n      const argTypes = [];\n      for (let i = 0; i < args.length; ++i) {\n        argTypes.push(findTypeNames(args[i]).join('|'));\n      }\n      err = new TypeError('Arguments of type \"' + argTypes.join(', ') + '\" do not match any of the defined signatures of function ' + _name + '.');\n      err.data = {\n        category: 'mismatch',\n        actual: argTypes\n      };\n      return err;\n    }\n\n    /**\n     * Find the lowest index of all exact types of a parameter (no conversions)\n     * @param {Param} param\n     * @return {number} Returns the index of the lowest type in typed.types\n     */\n    function getLowestTypeIndex(param) {\n      let min = typeList.length + 1;\n      for (let i = 0; i < param.types.length; i++) {\n        if (isExactType(param.types[i])) {\n          min = Math.min(min, param.types[i].typeIndex);\n        }\n      }\n      return min;\n    }\n\n    /**\n     * Find the lowest index of the conversion of all types of the parameter\n     * having a conversion\n     * @param {Param} param\n     * @return {number} Returns the lowest index of the conversions of this type\n     */\n    function getLowestConversionIndex(param) {\n      let min = nConversions + 1;\n      for (let i = 0; i < param.types.length; i++) {\n        if (!isExactType(param.types[i])) {\n          min = Math.min(min, param.types[i].conversionIndex);\n        }\n      }\n      return min;\n    }\n\n    /**\n     * Compare two params\n     * @param {Param} param1\n     * @param {Param} param2\n     * @return {number} returns -1 when param1 must get a lower\n     *                  index than param2, 1 when the opposite,\n     *                  or zero when both are equal\n     */\n    function compareParams(param1, param2) {\n      // We compare a number of metrics on a param in turn:\n      // 1) 'any' parameters are the least preferred\n      if (param1.hasAny) {\n        if (!param2.hasAny) {\n          return 1;\n        }\n      } else if (param2.hasAny) {\n        return -1;\n      }\n\n      // 2) Prefer non-rest to rest parameters\n      if (param1.restParam) {\n        if (!param2.restParam) {\n          return 1;\n        }\n      } else if (param2.restParam) {\n        return -1;\n      }\n\n      // 3) Prefer exact type match to conversions\n      if (param1.hasConversion) {\n        if (!param2.hasConversion) {\n          return 1;\n        }\n      } else if (param2.hasConversion) {\n        return -1;\n      }\n\n      // 4) Prefer lower type index:\n      const typeDiff = getLowestTypeIndex(param1) - getLowestTypeIndex(param2);\n      if (typeDiff < 0) {\n        return -1;\n      }\n      if (typeDiff > 0) {\n        return 1;\n      }\n\n      // 5) Prefer lower conversion index\n      const convDiff = getLowestConversionIndex(param1) - getLowestConversionIndex(param2);\n      if (convDiff < 0) {\n        return -1;\n      }\n      if (convDiff > 0) {\n        return 1;\n      }\n\n      // Don't have a basis for preference\n      return 0;\n    }\n\n    /**\n     * Compare two signatures\n     * @param {Signature} signature1\n     * @param {Signature} signature2\n     * @return {number} returns a negative number when param1 must get a lower\n     *                  index than param2, a positive number when the opposite,\n     *                  or zero when both are equal\n     */\n    function compareSignatures(signature1, signature2) {\n      const pars1 = signature1.params;\n      const pars2 = signature2.params;\n      const last1 = last(pars1);\n      const last2 = last(pars2);\n      const hasRest1 = hasRestParam(pars1);\n      const hasRest2 = hasRestParam(pars2);\n      // We compare a number of metrics on signatures in turn:\n      // 1) An \"any rest param\" is least preferred\n      if (hasRest1 && last1.hasAny) {\n        if (!hasRest2 || !last2.hasAny) {\n          return 1;\n        }\n      } else if (hasRest2 && last2.hasAny) {\n        return -1;\n      }\n\n      // 2) Minimize the number of 'any' parameters\n      let any1 = 0;\n      let conv1 = 0;\n      let par;\n      for (par of pars1) {\n        if (par.hasAny) ++any1;\n        if (par.hasConversion) ++conv1;\n      }\n      let any2 = 0;\n      let conv2 = 0;\n      for (par of pars2) {\n        if (par.hasAny) ++any2;\n        if (par.hasConversion) ++conv2;\n      }\n      if (any1 !== any2) {\n        return any1 - any2;\n      }\n\n      // 3) A conversion rest param is less preferred\n      if (hasRest1 && last1.hasConversion) {\n        if (!hasRest2 || !last2.hasConversion) {\n          return 1;\n        }\n      } else if (hasRest2 && last2.hasConversion) {\n        return -1;\n      }\n\n      // 4) Minimize the number of conversions\n      if (conv1 !== conv2) {\n        return conv1 - conv2;\n      }\n\n      // 5) Prefer no rest param\n      if (hasRest1) {\n        if (!hasRest2) {\n          return 1;\n        }\n      } else if (hasRest2) {\n        return -1;\n      }\n\n      // 6) Prefer shorter with rest param, longer without\n      const lengthCriterion = (pars1.length - pars2.length) * (hasRest1 ? -1 : 1);\n      if (lengthCriterion !== 0) {\n        return lengthCriterion;\n      }\n\n      // Signatures are identical in each of the above metrics.\n      // In particular, they are the same length.\n      // We can therefore compare the parameters one by one.\n      // First we count which signature has more preferred parameters.\n      const comparisons = [];\n      let tc = 0;\n      for (let i = 0; i < pars1.length; ++i) {\n        const thisComparison = compareParams(pars1[i], pars2[i]);\n        comparisons.push(thisComparison);\n        tc += thisComparison;\n      }\n      if (tc !== 0) {\n        return tc;\n      }\n\n      // They have the same number of preferred parameters, so go by the\n      // earliest parameter in which we have a preference.\n      // In other words, dispatch is driven somewhat more by earlier\n      // parameters than later ones.\n      let c;\n      for (c of comparisons) {\n        if (c !== 0) {\n          return c;\n        }\n      }\n\n      // It's a tossup:\n      return 0;\n    }\n\n    /**\n     * Produce a list of all conversions from distinct types to one of\n     * the given types.\n     *\n     * @param {string[]} typeNames\n     * @return {ConversionDef[]} Returns the conversions that are available\n     *                        resulting in any given type (if any)\n     */\n    function availableConversions(typeNames) {\n      if (typeNames.length === 0) {\n        return [];\n      }\n      const types = typeNames.map(findType);\n      if (typeNames.length > 1) {\n        types.sort((t1, t2) => t1.index - t2.index);\n      }\n      let matches = types[0].conversionsTo;\n      if (typeNames.length === 1) {\n        return matches;\n      }\n      matches = matches.concat([]); // shallow copy the matches\n      // Since the types are now in index order, we just want the first\n      // occurrence of any from type:\n      const knownTypes = new Set(typeNames);\n      for (let i = 1; i < types.length; ++i) {\n        let newMatch;\n        for (newMatch of types[i].conversionsTo) {\n          if (!knownTypes.has(newMatch.from)) {\n            matches.push(newMatch);\n            knownTypes.add(newMatch.from);\n          }\n        }\n      }\n      return matches;\n    }\n\n    /**\n     * Preprocess arguments before calling the original function:\n     * - if needed convert the parameters\n     * - in case of rest parameters, move the rest parameters into an Array\n     * @param {Param[]} params\n     * @param {function} fn\n     * @return {function} Returns a wrapped function\n     */\n    function compileArgsPreprocessing(params, fn) {\n      let fnConvert = fn;\n\n      // TODO: can we make this wrapper function smarter/simpler?\n\n      if (params.some(p => p.hasConversion)) {\n        const restParam = hasRestParam(params);\n        const compiledConversions = params.map(compileArgConversion);\n        fnConvert = function convertArgs() {\n          const args = [];\n          const last = restParam ? arguments.length - 1 : arguments.length;\n          for (let i = 0; i < last; i++) {\n            args[i] = compiledConversions[i](arguments[i]);\n          }\n          if (restParam) {\n            args[last] = arguments[last].map(compiledConversions[last]);\n          }\n          return fn.apply(this, args);\n        };\n      }\n      let fnPreprocess = fnConvert;\n      if (hasRestParam(params)) {\n        const offset = params.length - 1;\n        fnPreprocess = function preprocessRestParams() {\n          return fnConvert.apply(this, slice(arguments, 0, offset).concat([slice(arguments, offset)]));\n        };\n      }\n      return fnPreprocess;\n    }\n\n    /**\n     * Compile conversion for a parameter to the right type\n     * @param {Param} param\n     * @return {function} Returns the wrapped function that will convert arguments\n     *\n     */\n    function compileArgConversion(param) {\n      let test0, test1, conversion0, conversion1;\n      const tests = [];\n      const conversions = [];\n      param.types.forEach(function (type) {\n        if (type.conversion) {\n          tests.push(findType(type.conversion.from).test);\n          conversions.push(type.conversion.convert);\n        }\n      });\n\n      // create optimized conversion functions depending on the number of conversions\n      switch (conversions.length) {\n        case 0:\n          return function convertArg(arg) {\n            return arg;\n          };\n        case 1:\n          test0 = tests[0];\n          conversion0 = conversions[0];\n          return function convertArg(arg) {\n            if (test0(arg)) {\n              return conversion0(arg);\n            }\n            return arg;\n          };\n        case 2:\n          test0 = tests[0];\n          test1 = tests[1];\n          conversion0 = conversions[0];\n          conversion1 = conversions[1];\n          return function convertArg(arg) {\n            if (test0(arg)) {\n              return conversion0(arg);\n            }\n            if (test1(arg)) {\n              return conversion1(arg);\n            }\n            return arg;\n          };\n        default:\n          return function convertArg(arg) {\n            for (let i = 0; i < conversions.length; i++) {\n              if (tests[i](arg)) {\n                return conversions[i](arg);\n              }\n            }\n            return arg;\n          };\n      }\n    }\n\n    /**\n     * Split params with union types in to separate params.\n     *\n     * For example:\n     *\n     *     splitParams([['Array', 'Object'], ['string', 'RegExp'])\n     *     // returns:\n     *     // [\n     *     //   ['Array', 'string'],\n     *     //   ['Array', 'RegExp'],\n     *     //   ['Object', 'string'],\n     *     //   ['Object', 'RegExp']\n     *     // ]\n     *\n     * @param {Param[]} params\n     * @return {Param[]}\n     */\n    function splitParams(params) {\n      function _splitParams(params, index, paramsSoFar) {\n        if (index < params.length) {\n          const param = params[index];\n          let resultingParams = [];\n          if (param.restParam) {\n            // split the types of a rest parameter in two:\n            // one with only exact types, and one with exact types and conversions\n            const exactTypes = param.types.filter(isExactType);\n            if (exactTypes.length < param.types.length) {\n              resultingParams.push({\n                types: exactTypes,\n                name: '...' + exactTypes.map(t => t.name).join('|'),\n                hasAny: exactTypes.some(t => t.isAny),\n                hasConversion: false,\n                restParam: true\n              });\n            }\n            resultingParams.push(param);\n          } else {\n            // split all the types of a regular parameter into one type per param\n            resultingParams = param.types.map(function (type) {\n              return {\n                types: [type],\n                name: type.name,\n                hasAny: type.isAny,\n                hasConversion: type.conversion,\n                restParam: false\n              };\n            });\n          }\n\n          // recurse over the groups with types\n          return flatMap(resultingParams, function (nextParam) {\n            return _splitParams(params, index + 1, paramsSoFar.concat([nextParam]));\n          });\n        } else {\n          // we've reached the end of the parameters.\n          return [paramsSoFar];\n        }\n      }\n      return _splitParams(params, 0, []);\n    }\n\n    /**\n     * Test whether two param lists represent conflicting signatures\n     * @param {Param[]} params1\n     * @param {Param[]} params2\n     * @return {boolean} Returns true when the signatures conflict, false otherwise.\n     */\n    function conflicting(params1, params2) {\n      const ii = Math.max(params1.length, params2.length);\n      for (let i = 0; i < ii; i++) {\n        const typeSet1 = getTypeSetAtIndex(params1, i);\n        const typeSet2 = getTypeSetAtIndex(params2, i);\n        let overlap = false;\n        let name;\n        for (name of typeSet2) {\n          if (typeSet1.has(name)) {\n            overlap = true;\n            break;\n          }\n        }\n        if (!overlap) {\n          return false;\n        }\n      }\n      const len1 = params1.length;\n      const len2 = params2.length;\n      const restParam1 = hasRestParam(params1);\n      const restParam2 = hasRestParam(params2);\n      return restParam1 ? restParam2 ? len1 === len2 : len2 >= len1 : restParam2 ? len1 >= len2 : len1 === len2;\n    }\n\n    /**\n     * Helper function for `resolveReferences` that returns a copy of\n     * functionList wihe any prior resolutions cleared out, in case we are\n     * recycling signatures from a prior typed function construction.\n     *\n     * @param {Array.<function|typed-reference>} functionList\n     * @return {Array.<function|typed-reference>}\n     */\n    function clearResolutions(functionList) {\n      return functionList.map(fn => {\n        if (isReferToSelf(fn)) {\n          return referToSelf(fn.referToSelf.callback);\n        }\n        if (isReferTo(fn)) {\n          return makeReferTo(fn.referTo.references, fn.referTo.callback);\n        }\n        return fn;\n      });\n    }\n\n    /**\n     * Take a list of references, a list of functions functionList, and a\n     * signatureMap indexing signatures into functionList, and return\n     * the list of resolutions, or a false-y value if they don't all\n     * resolve in a valid way (yet).\n     *\n     * @param {string[]} references\n     * @param {Array<function|typed-reference} functionList\n     * @param {Object.<string, integer>} signatureMap\n     * @return {function[] | false} resolutions\n     */\n    function collectResolutions(references, functionList, signatureMap) {\n      const resolvedReferences = [];\n      let reference;\n      for (reference of references) {\n        let resolution = signatureMap[reference];\n        if (typeof resolution !== 'number') {\n          throw new TypeError('No definition for referenced signature \"' + reference + '\"');\n        }\n        resolution = functionList[resolution];\n        if (typeof resolution !== 'function') {\n          return false;\n        }\n        resolvedReferences.push(resolution);\n      }\n      return resolvedReferences;\n    }\n\n    /**\n     * Resolve any references in the functionList for the typed function\n     * itself. The signatureMap tells which index in the functionList a\n     * given signature should be mapped to (for use in resolving typed.referTo)\n     * and self provides the destions of a typed.referToSelf.\n     *\n     * @param {Array<function | typed-reference-object>} functionList\n     * @param {Object.<string, function>} signatureMap\n     * @param {function} self  The typed-function itself\n     * @return {Array<function>} The list of resolved functions\n     */\n    function resolveReferences(functionList, signatureMap, self) {\n      const resolvedFunctions = clearResolutions(functionList);\n      const isResolved = new Array(resolvedFunctions.length).fill(false);\n      let leftUnresolved = true;\n      while (leftUnresolved) {\n        leftUnresolved = false;\n        let nothingResolved = true;\n        for (let i = 0; i < resolvedFunctions.length; ++i) {\n          if (isResolved[i]) continue;\n          const fn = resolvedFunctions[i];\n          if (isReferToSelf(fn)) {\n            resolvedFunctions[i] = fn.referToSelf.callback(self);\n            // Preserve reference in case signature is reused someday:\n            resolvedFunctions[i].referToSelf = fn.referToSelf;\n            isResolved[i] = true;\n            nothingResolved = false;\n          } else if (isReferTo(fn)) {\n            const resolvedReferences = collectResolutions(fn.referTo.references, resolvedFunctions, signatureMap);\n            if (resolvedReferences) {\n              resolvedFunctions[i] = fn.referTo.callback.apply(this, resolvedReferences);\n              // Preserve reference in case signature is reused someday:\n              resolvedFunctions[i].referTo = fn.referTo;\n              isResolved[i] = true;\n              nothingResolved = false;\n            } else {\n              leftUnresolved = true;\n            }\n          }\n        }\n        if (nothingResolved && leftUnresolved) {\n          throw new SyntaxError('Circular reference detected in resolving typed.referTo');\n        }\n      }\n      return resolvedFunctions;\n    }\n\n    /**\n     * Validate whether any of the function bodies contains a self-reference\n     * usage like `this(...)` or `this.signatures`. This self-referencing is\n     * deprecated since typed-function v3. It has been replaced with\n     * the functions typed.referTo and typed.referToSelf.\n     * @param {Object.<string, function>} signaturesMap\n     */\n    function validateDeprecatedThis(signaturesMap) {\n      // TODO: remove this deprecation warning logic some day (it's introduced in v3)\n\n      // match occurrences like 'this(' and 'this.signatures'\n      const deprecatedThisRegex = /\\bthis(\\(|\\.signatures\\b)/;\n      Object.keys(signaturesMap).forEach(signature => {\n        const fn = signaturesMap[signature];\n        if (deprecatedThisRegex.test(fn.toString())) {\n          throw new SyntaxError('Using `this` to self-reference a function ' + 'is deprecated since typed-function@3. ' + 'Use typed.referTo and typed.referToSelf instead.');\n        }\n      });\n    }\n\n    /**\n     * Create a typed function\n     * @param {String} name               The name for the typed function\n     * @param {Object.<string, function>} rawSignaturesMap\n     *                                    An object with one or\n     *                                    multiple signatures as key, and the\n     *                                    function corresponding to the\n     *                                    signature as value.\n     * @return {function}  Returns the created typed function.\n     */\n    function createTypedFunction(name, rawSignaturesMap) {\n      typed.createCount++;\n      if (Object.keys(rawSignaturesMap).length === 0) {\n        throw new SyntaxError('No signatures provided');\n      }\n      if (typed.warnAgainstDeprecatedThis) {\n        validateDeprecatedThis(rawSignaturesMap);\n      }\n\n      // Main processing loop for signatures\n      const parsedParams = [];\n      const originalFunctions = [];\n      const signaturesMap = {};\n      const preliminarySignatures = []; // may have duplicates from conversions\n      let signature;\n      for (signature in rawSignaturesMap) {\n        // A) Protect against polluted Object prototype:\n        if (!Object.prototype.hasOwnProperty.call(rawSignaturesMap, signature)) {\n          continue;\n        }\n        // B) Parse the signature\n        const params = parseSignature(signature);\n        if (!params) continue;\n        // C) Check for conflicts\n        parsedParams.forEach(function (pp) {\n          if (conflicting(pp, params)) {\n            throw new TypeError('Conflicting signatures \"' + stringifyParams(pp) + '\" and \"' + stringifyParams(params) + '\".');\n          }\n        });\n        parsedParams.push(params);\n        // D) Store the provided function and add conversions\n        const functionIndex = originalFunctions.length;\n        originalFunctions.push(rawSignaturesMap[signature]);\n        const conversionParams = params.map(expandParam);\n        // E) Split the signatures and collect them up\n        let sp;\n        for (sp of splitParams(conversionParams)) {\n          const spName = stringifyParams(sp);\n          preliminarySignatures.push({\n            params: sp,\n            name: spName,\n            fn: functionIndex\n          });\n          if (sp.every(p => !p.hasConversion)) {\n            signaturesMap[spName] = functionIndex;\n          }\n        }\n      }\n      preliminarySignatures.sort(compareSignatures);\n\n      // Note the forward reference to theTypedFn\n      const resolvedFunctions = resolveReferences(originalFunctions, signaturesMap, theTypedFn);\n\n      // Fill in the proper function for each signature\n      let s;\n      for (s in signaturesMap) {\n        if (Object.prototype.hasOwnProperty.call(signaturesMap, s)) {\n          signaturesMap[s] = resolvedFunctions[signaturesMap[s]];\n        }\n      }\n      const signatures = [];\n      const internalSignatureMap = new Map(); // benchmarks faster than object\n      for (s of preliminarySignatures) {\n        // Note it's only safe to eliminate duplicates like this\n        // _after_ the signature sorting step above; otherwise we might\n        // remove the wrong one.\n        if (!internalSignatureMap.has(s.name)) {\n          s.fn = resolvedFunctions[s.fn];\n          signatures.push(s);\n          internalSignatureMap.set(s.name, s);\n        }\n      }\n\n      // we create a highly optimized checks for the first couple of signatures with max 2 arguments\n      const ok0 = signatures[0] && signatures[0].params.length <= 2 && !hasRestParam(signatures[0].params);\n      const ok1 = signatures[1] && signatures[1].params.length <= 2 && !hasRestParam(signatures[1].params);\n      const ok2 = signatures[2] && signatures[2].params.length <= 2 && !hasRestParam(signatures[2].params);\n      const ok3 = signatures[3] && signatures[3].params.length <= 2 && !hasRestParam(signatures[3].params);\n      const ok4 = signatures[4] && signatures[4].params.length <= 2 && !hasRestParam(signatures[4].params);\n      const ok5 = signatures[5] && signatures[5].params.length <= 2 && !hasRestParam(signatures[5].params);\n      const allOk = ok0 && ok1 && ok2 && ok3 && ok4 && ok5;\n\n      // compile the tests\n      for (let i = 0; i < signatures.length; ++i) {\n        signatures[i].test = compileTests(signatures[i].params);\n      }\n      const test00 = ok0 ? compileTest(signatures[0].params[0]) : notOk;\n      const test10 = ok1 ? compileTest(signatures[1].params[0]) : notOk;\n      const test20 = ok2 ? compileTest(signatures[2].params[0]) : notOk;\n      const test30 = ok3 ? compileTest(signatures[3].params[0]) : notOk;\n      const test40 = ok4 ? compileTest(signatures[4].params[0]) : notOk;\n      const test50 = ok5 ? compileTest(signatures[5].params[0]) : notOk;\n      const test01 = ok0 ? compileTest(signatures[0].params[1]) : notOk;\n      const test11 = ok1 ? compileTest(signatures[1].params[1]) : notOk;\n      const test21 = ok2 ? compileTest(signatures[2].params[1]) : notOk;\n      const test31 = ok3 ? compileTest(signatures[3].params[1]) : notOk;\n      const test41 = ok4 ? compileTest(signatures[4].params[1]) : notOk;\n      const test51 = ok5 ? compileTest(signatures[5].params[1]) : notOk;\n\n      // compile the functions\n      for (let i = 0; i < signatures.length; ++i) {\n        signatures[i].implementation = compileArgsPreprocessing(signatures[i].params, signatures[i].fn);\n      }\n      const fn0 = ok0 ? signatures[0].implementation : undef;\n      const fn1 = ok1 ? signatures[1].implementation : undef;\n      const fn2 = ok2 ? signatures[2].implementation : undef;\n      const fn3 = ok3 ? signatures[3].implementation : undef;\n      const fn4 = ok4 ? signatures[4].implementation : undef;\n      const fn5 = ok5 ? signatures[5].implementation : undef;\n      const len0 = ok0 ? signatures[0].params.length : -1;\n      const len1 = ok1 ? signatures[1].params.length : -1;\n      const len2 = ok2 ? signatures[2].params.length : -1;\n      const len3 = ok3 ? signatures[3].params.length : -1;\n      const len4 = ok4 ? signatures[4].params.length : -1;\n      const len5 = ok5 ? signatures[5].params.length : -1;\n\n      // simple and generic, but also slow\n      const iStart = allOk ? 6 : 0;\n      const iEnd = signatures.length;\n      // de-reference ahead for execution speed:\n      const tests = signatures.map(s => s.test);\n      const fns = signatures.map(s => s.implementation);\n      const generic = function generic() {\n        for (let i = iStart; i < iEnd; i++) {\n          if (tests[i](arguments)) {\n            return fns[i].apply(this, arguments);\n          }\n        }\n        return typed.onMismatch(name, arguments, signatures);\n      };\n\n      // create the typed function\n      // fast, specialized version. Falls back to the slower, generic one if needed\n      function theTypedFn(arg0, arg1) {\n        if (arguments.length === len0 && test00(arg0) && test01(arg1)) {\n          return fn0.apply(this, arguments);\n        }\n        if (arguments.length === len1 && test10(arg0) && test11(arg1)) {\n          return fn1.apply(this, arguments);\n        }\n        if (arguments.length === len2 && test20(arg0) && test21(arg1)) {\n          return fn2.apply(this, arguments);\n        }\n        if (arguments.length === len3 && test30(arg0) && test31(arg1)) {\n          return fn3.apply(this, arguments);\n        }\n        if (arguments.length === len4 && test40(arg0) && test41(arg1)) {\n          return fn4.apply(this, arguments);\n        }\n        if (arguments.length === len5 && test50(arg0) && test51(arg1)) {\n          return fn5.apply(this, arguments);\n        }\n        return generic.apply(this, arguments);\n      }\n\n      // attach name the typed function\n      try {\n        Object.defineProperty(theTypedFn, 'name', {\n          value: name\n        });\n      } catch (err) {\n        // old browsers do not support Object.defineProperty and some don't support setting the name property\n        // the function name is not essential for the functioning, it's mostly useful for debugging,\n        // so it's fine to have unnamed functions.\n      }\n\n      // attach signatures to the function.\n      // This property is close to the original collection of signatures\n      // used to create the typed-function, just with unions split:\n      theTypedFn.signatures = signaturesMap;\n\n      // Store internal data for functions like resolve, find, etc.\n      // Also serves as the flag that this is a typed-function\n      theTypedFn._typedFunctionData = {\n        signatures,\n        signatureMap: internalSignatureMap\n      };\n      return theTypedFn;\n    }\n\n    /**\n     * Action to take on mismatch\n     * @param {string} name      Name of function that was attempted to be called\n     * @param {Array} args       Actual arguments to the call\n     * @param {Array} signatures Known signatures of the named typed-function\n     */\n    function _onMismatch(name, args, signatures) {\n      throw createError(name, args, signatures);\n    }\n\n    /**\n     * Return all but the last items of an array or function Arguments\n     * @param {Array | Arguments} arr\n     * @return {Array}\n     */\n    function initial(arr) {\n      return slice(arr, 0, arr.length - 1);\n    }\n\n    /**\n     * return the last item of an array or function Arguments\n     * @param {Array | Arguments} arr\n     * @return {*}\n     */\n    function last(arr) {\n      return arr[arr.length - 1];\n    }\n\n    /**\n     * Slice an array or function Arguments\n     * @param {Array | Arguments | IArguments} arr\n     * @param {number} start\n     * @param {number} [end]\n     * @return {Array}\n     */\n    function slice(arr, start, end) {\n      return Array.prototype.slice.call(arr, start, end);\n    }\n\n    /**\n     * Return the first item from an array for which test(arr[i]) returns true\n     * @param {Array} arr\n     * @param {function} test\n     * @return {* | undefined} Returns the first matching item\n     *                         or undefined when there is no match\n     */\n    function findInArray(arr, test) {\n      for (let i = 0; i < arr.length; i++) {\n        if (test(arr[i])) {\n          return arr[i];\n        }\n      }\n      return undefined;\n    }\n\n    /**\n     * Flat map the result invoking a callback for every item in an array.\n     * https://gist.github.com/samgiles/762ee337dff48623e729\n     * @param {Array} arr\n     * @param {function} callback\n     * @return {Array}\n     */\n    function flatMap(arr, callback) {\n      return Array.prototype.concat.apply([], arr.map(callback));\n    }\n\n    /**\n     * Create a reference callback to one or multiple signatures\n     *\n     * Syntax:\n     *\n     *     typed.referTo(signature1, signature2, ..., function callback(fn1, fn2, ...) {\n     *       // ...\n     *     })\n     *\n     * @returns {{referTo: {references: string[], callback}}}\n     */\n    function referTo() {\n      const references = initial(arguments).map(s => stringifyParams(parseSignature(s)));\n      const callback = last(arguments);\n      if (typeof callback !== 'function') {\n        throw new TypeError('Callback function expected as last argument');\n      }\n      return makeReferTo(references, callback);\n    }\n    function makeReferTo(references, callback) {\n      return {\n        referTo: {\n          references,\n          callback\n        }\n      };\n    }\n\n    /**\n     * Create a reference callback to the typed-function itself\n     *\n     * @param {(self: function) => function} callback\n     * @returns {{referToSelf: { callback: function }}}\n     */\n    function referToSelf(callback) {\n      if (typeof callback !== 'function') {\n        throw new TypeError('Callback function expected as first argument');\n      }\n      return {\n        referToSelf: {\n          callback\n        }\n      };\n    }\n\n    /**\n     * Test whether something is a referTo object, holding a list with reference\n     * signatures and a callback.\n     *\n     * @param {Object | function} objectOrFn\n     * @returns {boolean}\n     */\n    function isReferTo(objectOrFn) {\n      return objectOrFn && typeof objectOrFn.referTo === 'object' && Array.isArray(objectOrFn.referTo.references) && typeof objectOrFn.referTo.callback === 'function';\n    }\n\n    /**\n     * Test whether something is a referToSelf object, holding a callback where\n     * to pass `self`.\n     *\n     * @param {Object | function} objectOrFn\n     * @returns {boolean}\n     */\n    function isReferToSelf(objectOrFn) {\n      return objectOrFn && typeof objectOrFn.referToSelf === 'object' && typeof objectOrFn.referToSelf.callback === 'function';\n    }\n\n    /**\n     * Check if name is (A) new, (B) a match, or (C) a mismatch; and throw\n     * an error in case (C).\n     *\n     * @param { string | undefined } nameSoFar\n     * @param { string | undefined } newName\n     * @returns { string } updated name\n     */\n    function checkName(nameSoFar, newName) {\n      if (!nameSoFar) {\n        return newName;\n      }\n      if (newName && newName !== nameSoFar) {\n        const err = new Error('Function names do not match (expected: ' + nameSoFar + ', actual: ' + newName + ')');\n        err.data = {\n          actual: newName,\n          expected: nameSoFar\n        };\n        throw err;\n      }\n      return nameSoFar;\n    }\n\n    /**\n     * Retrieve the implied name from an object with signature keys\n     * and function values, checking whether all value names match\n     *\n     * @param { {string: function} } obj\n     */\n    function getObjectName(obj) {\n      let name;\n      for (const key in obj) {\n        // Only pay attention to own properties, and only if their values\n        // are typed functions or functions with a signature property\n        if (Object.prototype.hasOwnProperty.call(obj, key) && (isTypedFunction(obj[key]) || typeof obj[key].signature === 'string')) {\n          name = checkName(name, obj[key].name);\n        }\n      }\n      return name;\n    }\n\n    /**\n     * Copy all of the signatures from the second argument into the first,\n     * which is modified by side effect, checking for conflicts\n     *\n     * @param {Object.<string, function|typed-reference>} dest\n     * @param {Object.<string, function|typed-reference>} source\n     */\n    function mergeSignatures(dest, source) {\n      let key;\n      for (key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          if (key in dest) {\n            if (source[key] !== dest[key]) {\n              const err = new Error('Signature \"' + key + '\" is defined twice');\n              err.data = {\n                signature: key,\n                sourceFunction: source[key],\n                destFunction: dest[key]\n              };\n              throw err;\n            }\n            // else: both signatures point to the same function, that's fine\n          }\n          dest[key] = source[key];\n        }\n      }\n    }\n    const saveTyped = typed;\n\n    /**\n     * Originally the main function was a typed function itself, but then\n     * it might not be able to generate error messages if the client\n     * replaced the type system with different names.\n     *\n     * Main entry: typed([name], functions/objects with signatures...)\n     *\n     * Assembles and returns a new typed-function from the given items\n     * that provide signatures and implementations, each of which may be\n     * * a plain object mapping (string) signatures to implementing functions,\n     * * a previously constructed typed function, or\n     * * any other single function with a string-valued property `signature`.\n      * The name of the resulting typed-function will be given by the\n     * string-valued name argument if present, or if not, by the name\n     * of any of the arguments that have one, as long as any that do are\n     * consistent with each other. If no name is specified, the name will be\n     * an empty string.\n     *\n     * @param {string} maybeName [optional]\n     * @param {(function|object)[]} signature providers\n     * @returns {typed-function}\n     */\n    typed = function (maybeName) {\n      const named = typeof maybeName === 'string';\n      const start = named ? 1 : 0;\n      let name = named ? maybeName : '';\n      const allSignatures = {};\n      for (let i = start; i < arguments.length; ++i) {\n        const item = arguments[i];\n        let theseSignatures = {};\n        let thisName;\n        if (typeof item === 'function') {\n          thisName = item.name;\n          if (typeof item.signature === 'string') {\n            // Case 1: Ordinary function with a string 'signature' property\n            theseSignatures[item.signature] = item;\n          } else if (isTypedFunction(item)) {\n            // Case 2: Existing typed function\n            theseSignatures = item.signatures;\n          }\n        } else if (isPlainObject(item)) {\n          // Case 3: Plain object, assume keys = signatures, values = functions\n          theseSignatures = item;\n          if (!named) {\n            thisName = getObjectName(item);\n          }\n        }\n        if (Object.keys(theseSignatures).length === 0) {\n          const err = new TypeError('Argument to \\'typed\\' at index ' + i + ' is not a (typed) function, ' + 'nor an object with signatures as keys and functions as values.');\n          err.data = {\n            index: i,\n            argument: item\n          };\n          throw err;\n        }\n        if (!named) {\n          name = checkName(name, thisName);\n        }\n        mergeSignatures(allSignatures, theseSignatures);\n      }\n      return createTypedFunction(name || '', allSignatures);\n    };\n    typed.create = create;\n    typed.createCount = saveTyped.createCount;\n    typed.onMismatch = _onMismatch;\n    typed.throwMismatchError = _onMismatch;\n    typed.createError = createError;\n    typed.clear = clear;\n    typed.clearConversions = clearConversions;\n    typed.addTypes = addTypes;\n    typed._findType = findType; // For unit testing only\n    typed.referTo = referTo;\n    typed.referToSelf = referToSelf;\n    typed.convert = convert;\n    typed.findSignature = findSignature;\n    typed.find = find;\n    typed.isTypedFunction = isTypedFunction;\n    typed.warnAgainstDeprecatedThis = true;\n\n    /**\n     * add a type (convenience wrapper for typed.addTypes)\n     * @param {{name: string, test: function}} type\n     * @param {boolean} [beforeObjectTest=true]\n     *                          If true, the new test will be inserted before\n     *                          the test with name 'Object' (if any), since\n     *                          tests for Object match Array and classes too.\n     */\n    typed.addType = function (type, beforeObjectTest) {\n      let before = 'any';\n      if (beforeObjectTest !== false && typeMap.has('Object')) {\n        before = 'Object';\n      }\n      typed.addTypes([type], before);\n    };\n\n    /**\n     * Verify that the ConversionDef conversion has a valid format.\n     *\n     * @param {conversionDef} conversion\n     * @return {void}\n     * @throws {TypeError|SyntaxError}\n     */\n    function _validateConversion(conversion) {\n      if (!conversion || typeof conversion.from !== 'string' || typeof conversion.to !== 'string' || typeof conversion.convert !== 'function') {\n        throw new TypeError('Object with properties {from: string, to: string, convert: function} expected');\n      }\n      if (conversion.to === conversion.from) {\n        throw new SyntaxError('Illegal to define conversion from \"' + conversion.from + '\" to itself.');\n      }\n    }\n\n    /**\n     * Add a conversion\n     *\n     * @param {ConversionDef} conversion\n     * @param {{override: boolean}} [options]\n     * @returns {void}\n     * @throws {TypeError}\n     */\n    typed.addConversion = function (conversion) {\n      let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        override: false\n      };\n      _validateConversion(conversion);\n      const to = findType(conversion.to);\n      const existing = to.conversionsTo.find(other => other.from === conversion.from);\n      if (existing) {\n        if (options && options.override) {\n          typed.removeConversion({\n            from: existing.from,\n            to: conversion.to,\n            convert: existing.convert\n          });\n        } else {\n          throw new Error('There is already a conversion from \"' + conversion.from + '\" to \"' + to.name + '\"');\n        }\n      }\n      to.conversionsTo.push({\n        from: conversion.from,\n        convert: conversion.convert,\n        index: nConversions++\n      });\n    };\n\n    /**\n     * Convenience wrapper to call addConversion on each conversion in a list.\n     *\n     * @param {ConversionDef[]} conversions\n     * @param {{override: boolean}} [options]\n     * @returns {void}\n     * @throws {TypeError}\n     */\n    typed.addConversions = function (conversions, options) {\n      conversions.forEach(conversion => typed.addConversion(conversion, options));\n    };\n\n    /**\n     * Remove the specified conversion. The format is the same as for\n     * addConversion, and the convert function must match or an error\n     * is thrown.\n     *\n     * @param {{from: string, to: string, convert: function}} conversion\n     * @returns {void}\n     * @throws {TypeError|SyntaxError|Error}\n     */\n    typed.removeConversion = function (conversion) {\n      _validateConversion(conversion);\n      const to = findType(conversion.to);\n      const existingConversion = findInArray(to.conversionsTo, c => c.from === conversion.from);\n      if (!existingConversion) {\n        throw new Error('Attempt to remove nonexistent conversion from ' + conversion.from + ' to ' + conversion.to);\n      }\n      if (existingConversion.convert !== conversion.convert) {\n        throw new Error('Conversion to remove does not match existing conversion');\n      }\n      const index = to.conversionsTo.indexOf(existingConversion);\n      to.conversionsTo.splice(index, 1);\n    };\n\n    /**\n     * Produce the specific signature that a typed function\n     * will execute on the given arguments. Here, a \"signature\" is an\n     * object with properties 'params', 'test', 'fn', and 'implementation'.\n     * This last property is a function that converts params as necessary\n     * and then calls 'fn'. Returns null if there is no matching signature.\n     * @param {typed-function} tf\n     * @param {any[]} argList\n     * @returns {{params: string, test: function, fn: function, implementation: function}}\n     */\n    typed.resolve = function (tf, argList) {\n      if (!isTypedFunction(tf)) {\n        throw new TypeError(NOT_TYPED_FUNCTION);\n      }\n      const sigs = tf._typedFunctionData.signatures;\n      for (let i = 0; i < sigs.length; ++i) {\n        if (sigs[i].test(argList)) {\n          return sigs[i];\n        }\n      }\n      return null;\n    };\n    return typed;\n  }\n  var typedFunction = create();\n  return typedFunction;\n});", "map": {"version": 3, "names": ["ok", "notOk", "undef", "undefined", "NOT_TYPED_FUNCTION", "create", "isPlainObject", "x", "constructor", "Object", "_types", "name", "test", "Array", "isArray", "Date", "RegExp", "anyType", "isAny", "typeMap", "typeList", "nConversions", "typed", "createCount", "findType", "typeName", "type", "get", "message", "toLowerCase", "otherName", "TypeError", "addTypes", "types", "beforeSpec", "arguments", "length", "beforeIndex", "index", "newTypes", "i", "has", "push", "set", "conversionsTo", "affectedTypes", "slice", "concat", "clear", "Map", "clearConversions", "findTypeNames", "value", "matches", "filter", "isTypedFunction", "entity", "findSignature", "fn", "signature", "options", "exact", "stringSignature", "join", "params", "parseSignature", "canonicalSignature", "stringifyParams", "signatures", "match", "_typedFunctionData", "signatureMap", "nParams", "remainingSignatures", "want", "filteredSignatures", "possibility", "have", "getParamAtIndex", "restParam", "hasAny", "haveTypes", "paramTypeSet", "some", "wtype", "candidate", "find", "implementation", "convert", "conversions", "Error", "fromType", "from", "separator", "map", "p", "parseParam", "param", "indexOf", "typeDefs", "split", "s", "trim", "paramName", "exactTypes", "typeIndex", "conversion", "conversionIndex", "hasConversion", "expandParam", "typeNames", "t", "matchingConversions", "availableConversions", "newName", "convertibleTypes", "typeSet", "Set", "for<PERSON>ach", "add", "rawSignature", "rawParams", "parsed<PERSON><PERSON><PERSON>", "SyntaxError", "hasRestParam", "last", "compileTest", "test0", "test1", "or", "tests", "compileTests", "initial", "varIndex", "lastTest", "testRestParam", "args", "testArgs", "getTypeSetAtIndex", "isExactType", "mergeExpectedParams", "paramSet", "createError", "err", "expected", "_name", "matchingSignatures", "nextMatchingDefs", "actualTypes", "data", "category", "actual", "lengths", "Infinity", "Math", "min", "apply", "max<PERSON><PERSON><PERSON>", "max", "<PERSON><PERSON><PERSON><PERSON>", "argTypes", "getLowestTypeIndex", "getLowestConversionIndex", "compareParams", "param1", "param2", "typeDiff", "convDiff", "compareSignatures", "signature1", "signature2", "pars1", "pars2", "last1", "last2", "hasRest1", "hasRest2", "any1", "conv1", "par", "any2", "conv2", "lengthCriterion", "comparisons", "tc", "thisComparison", "c", "sort", "t1", "t2", "knownTypes", "newMatch", "compileArgsPreprocessing", "fnConvert", "compiledConversions", "compileArgConversion", "convertArgs", "fnPreprocess", "offset", "preprocessRestParams", "conversion0", "conversion1", "convertArg", "arg", "splitParams", "_splitParams", "paramsSoFar", "resultingParams", "flatMap", "nextParam", "conflicting", "params1", "params2", "ii", "typeSet1", "typeSet2", "overlap", "len1", "len2", "restParam1", "restParam2", "clearResolutions", "functionList", "isReferToSelf", "referToSelf", "callback", "isReferTo", "makeReferTo", "referTo", "references", "collectResolutions", "resolvedReferences", "reference", "resolution", "resolveReferences", "self", "resolvedFunctions", "isResolved", "fill", "leftUnresolved", "nothingResolved", "validateDeprecatedThis", "signaturesMap", "deprecatedThisRegex", "keys", "toString", "createTypedFunction", "rawSignaturesMap", "warnAgainstDeprecatedThis", "parsedParams", "originalFunctions", "preliminarySignatures", "prototype", "hasOwnProperty", "call", "pp", "functionIndex", "conversionParams", "sp", "spName", "every", "theTypedFn", "internalSignatureMap", "ok0", "ok1", "ok2", "ok3", "ok4", "ok5", "allOk", "test00", "test10", "test20", "test30", "test40", "test50", "test01", "test11", "test21", "test31", "test41", "test51", "fn0", "fn1", "fn2", "fn3", "fn4", "fn5", "len0", "len3", "len4", "len5", "iStart", "iEnd", "fns", "generic", "onMismatch", "arg0", "arg1", "defineProperty", "_onMismatch", "arr", "start", "end", "findInArray", "objectOrFn", "checkName", "nameSoFar", "getObjectName", "obj", "key", "mergeSignatures", "dest", "source", "sourceFunction", "destFunction", "saveTyped", "maybe<PERSON><PERSON>", "named", "allSignatures", "item", "theseSignatures", "thisName", "argument", "throwMismatchError", "_findType", "addType", "beforeObjectTest", "before", "_validateConversion", "to", "addConversion", "override", "existing", "other", "removeConversion", "addConversions", "existingConversion", "splice", "resolve", "tf", "argList", "sigs", "typedFunction"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\node_modules\\typed-function\\lib\\esm\\typed-function.mjs"], "sourcesContent": ["function ok() {\n  return true;\n}\nfunction notOk() {\n  return false;\n}\nfunction undef() {\n  return undefined;\n}\nconst NOT_TYPED_FUNCTION = 'Argument is not a typed-function.';\n\n/**\n * @typedef {{\n *   params: Param[],\n *   fn: function,\n *   test: function,\n *   implementation: function\n * }} Signature\n *\n * @typedef {{\n *   types: Type[],\n *   hasAny: boolean,\n *   hasConversion: boolean,\n *   restParam: boolean\n * }} Param\n *\n * @typedef {{\n *   name: string,\n *   typeIndex: number,\n *   test: function,\n *   isAny: boolean,\n *   conversion?: ConversionDef,\n *   conversionIndex: number,\n * }} Type\n *\n * @typedef {{\n *   from: string,\n *   to: string,\n *   convert: function (*) : *\n * }} ConversionDef\n *\n * @typedef {{\n *   name: string,\n *   test: function(*) : boolean,\n *   isAny?: boolean\n * }} TypeDef\n */\n\n/**\n * @returns {() => function}\n */\nfunction create() {\n  // data type tests\n\n  /**\n   * Returns true if the argument is a non-null \"plain\" object\n   */\n  function isPlainObject(x) {\n    return typeof x === 'object' && x !== null && x.constructor === Object;\n  }\n  const _types = [{\n    name: 'number',\n    test: function (x) {\n      return typeof x === 'number';\n    }\n  }, {\n    name: 'string',\n    test: function (x) {\n      return typeof x === 'string';\n    }\n  }, {\n    name: 'boolean',\n    test: function (x) {\n      return typeof x === 'boolean';\n    }\n  }, {\n    name: 'Function',\n    test: function (x) {\n      return typeof x === 'function';\n    }\n  }, {\n    name: 'Array',\n    test: Array.isArray\n  }, {\n    name: 'Date',\n    test: function (x) {\n      return x instanceof Date;\n    }\n  }, {\n    name: 'RegExp',\n    test: function (x) {\n      return x instanceof RegExp;\n    }\n  }, {\n    name: 'Object',\n    test: isPlainObject\n  }, {\n    name: 'null',\n    test: function (x) {\n      return x === null;\n    }\n  }, {\n    name: 'undefined',\n    test: function (x) {\n      return x === undefined;\n    }\n  }];\n  const anyType = {\n    name: 'any',\n    test: ok,\n    isAny: true\n  };\n\n  // Data structures to track the types. As these are local variables in\n  // create(), each typed universe will get its own copy, but the variables\n  // will only be accessible through the (closures of the) functions supplied\n  // as properties of the typed object, not directly.\n  // These will be initialized in clear() below\n  let typeMap; // primary store of all types\n  let typeList; // Array of just type names, for the sake of ordering\n\n  // And similar data structures for the type conversions:\n  let nConversions = 0;\n  // the actual conversions are stored on a property of the destination types\n\n  // This is a temporary object, will be replaced with a function at the end\n  let typed = {\n    createCount: 0\n  };\n\n  /**\n   * Takes a type name and returns the corresponding official type object\n   * for that type.\n   *\n   * @param {string} typeName\n   * @returns {TypeDef} type\n   */\n  function findType(typeName) {\n    const type = typeMap.get(typeName);\n    if (type) {\n      return type;\n    }\n    // Remainder is error handling\n    let message = 'Unknown type \"' + typeName + '\"';\n    const name = typeName.toLowerCase();\n    let otherName;\n    for (otherName of typeList) {\n      if (otherName.toLowerCase() === name) {\n        message += '. Did you mean \"' + otherName + '\" ?';\n        break;\n      }\n    }\n    throw new TypeError(message);\n  }\n\n  /**\n   * Adds an array `types` of type definitions to this typed instance.\n   * Each type definition should be an object with properties:\n   * 'name' - a string giving the name of the type; 'test' - function\n   * returning a boolean that tests membership in the type; and optionally\n   * 'isAny' - true only for the 'any' type.\n   *\n   * The second optional argument, `before`, gives the name of a type that\n   * these types should be added before. The new types are added in the\n   * order specified.\n   * @param {TypeDef[]} types\n   * @param {string | boolean} [beforeSpec='any'] before\n   */\n  function addTypes(types) {\n    let beforeSpec = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'any';\n    const beforeIndex = beforeSpec ? findType(beforeSpec).index : typeList.length;\n    const newTypes = [];\n    for (let i = 0; i < types.length; ++i) {\n      if (!types[i] || typeof types[i].name !== 'string' || typeof types[i].test !== 'function') {\n        throw new TypeError('Object with properties {name: string, test: function} expected');\n      }\n      const typeName = types[i].name;\n      if (typeMap.has(typeName)) {\n        throw new TypeError('Duplicate type name \"' + typeName + '\"');\n      }\n      newTypes.push(typeName);\n      typeMap.set(typeName, {\n        name: typeName,\n        test: types[i].test,\n        isAny: types[i].isAny,\n        index: beforeIndex + i,\n        conversionsTo: [] // Newly added type can't have any conversions to it\n      });\n    }\n    // update the typeList\n    const affectedTypes = typeList.slice(beforeIndex);\n    typeList = typeList.slice(0, beforeIndex).concat(newTypes).concat(affectedTypes);\n    // Fix the indices\n    for (let i = beforeIndex + newTypes.length; i < typeList.length; ++i) {\n      typeMap.get(typeList[i]).index = i;\n    }\n  }\n\n  /**\n   * Removes all types and conversions from this typed instance.\n   * May cause previously constructed typed-functions to throw\n   * strange errors when they are called with types that do not\n   * match any of their signatures.\n   */\n  function clear() {\n    typeMap = new Map();\n    typeList = [];\n    nConversions = 0;\n    addTypes([anyType], false);\n  }\n\n  // initialize the types to the default list\n  clear();\n  addTypes(_types);\n\n  /**\n   * Removes all conversions, leaving the types alone.\n   */\n  function clearConversions() {\n    let typeName;\n    for (typeName of typeList) {\n      typeMap.get(typeName).conversionsTo = [];\n    }\n    nConversions = 0;\n  }\n\n  /**\n   * Find the type names that match a value.\n   * @param {*} value\n   * @return {string[]} Array of names of types for which\n   *                  the type test matches the value.\n   */\n  function findTypeNames(value) {\n    const matches = typeList.filter(name => {\n      const type = typeMap.get(name);\n      return !type.isAny && type.test(value);\n    });\n    if (matches.length) {\n      return matches;\n    }\n    return ['any'];\n  }\n\n  /**\n   * Check if an entity is a typed function created by any instance\n   * @param {any} entity\n   * @returns {boolean}\n   */\n  function isTypedFunction(entity) {\n    return entity && typeof entity === 'function' && '_typedFunctionData' in entity;\n  }\n\n  /**\n   * Find a specific signature from a (composed) typed function, for example:\n   *\n   *   typed.findSignature(fn, ['number', 'string'])\n   *   typed.findSignature(fn, 'number, string')\n   *   typed.findSignature(fn, 'number,string', {exact: true})\n   *\n   * This function findSignature will by default return the best match to\n   * the given signature, possibly employing type conversions.\n   *\n   * The (optional) third argument is a plain object giving options\n   * controlling the signature search. Currently the only implemented\n   * option is `exact`: if specified as true (default is false), only\n   * exact matches will be returned (i.e. signatures for which `fn` was\n   * directly defined). Note that a (possibly different) type matching\n   * `any`, or one or more instances of TYPE matching `...TYPE` are\n   * considered exact matches in this regard, as no conversions are used.\n   *\n   * This function returns a \"signature\" object, as does `typed.resolve()`,\n   * which is a plain object with four keys: `params` (the array of parameters\n   * for this signature), `fn` (the originally supplied function for this\n   * signature), `test` (a generated function that determines if an argument\n   * list matches this signature, and `implementation` (the function to call\n   * on a matching argument list, that performs conversions if necessary and\n   * then calls the originally supplied function).\n   *\n   * @param {Function} fn                   A typed-function\n   * @param {string | string[]} signature\n   *     Signature to be found, can be an array or a comma separated string.\n   * @param {object} options  Controls the signature search as documented\n   * @return {{ params: Param[], fn: function, test: function, implementation: function }}\n   *     Returns the matching signature, or throws an error when no signature\n   *     is found.\n   */\n  function findSignature(fn, signature, options) {\n    if (!isTypedFunction(fn)) {\n      throw new TypeError(NOT_TYPED_FUNCTION);\n    }\n\n    // Canonicalize input\n    const exact = options && options.exact;\n    const stringSignature = Array.isArray(signature) ? signature.join(',') : signature;\n    const params = parseSignature(stringSignature);\n    const canonicalSignature = stringifyParams(params);\n\n    // First hope we get lucky and exactly match a signature\n    if (!exact || canonicalSignature in fn.signatures) {\n      // OK, we can check the internal signatures\n      const match = fn._typedFunctionData.signatureMap.get(canonicalSignature);\n      if (match) {\n        return match;\n      }\n    }\n\n    // Oh well, we did not; so we have to go back and check the parameters\n    // one by one, in order to catch things like `any` and rest params.\n    // Note here we can assume there is at least one parameter, because\n    // the empty signature would have matched successfully above.\n    const nParams = params.length;\n    let remainingSignatures;\n    if (exact) {\n      remainingSignatures = [];\n      let name;\n      for (name in fn.signatures) {\n        remainingSignatures.push(fn._typedFunctionData.signatureMap.get(name));\n      }\n    } else {\n      remainingSignatures = fn._typedFunctionData.signatures;\n    }\n    for (let i = 0; i < nParams; ++i) {\n      const want = params[i];\n      const filteredSignatures = [];\n      let possibility;\n      for (possibility of remainingSignatures) {\n        const have = getParamAtIndex(possibility.params, i);\n        if (!have || want.restParam && !have.restParam) {\n          continue;\n        }\n        if (!have.hasAny) {\n          // have to check all of the wanted types are available\n          const haveTypes = paramTypeSet(have);\n          if (want.types.some(wtype => !haveTypes.has(wtype.name))) {\n            continue;\n          }\n        }\n        // OK, this looks good\n        filteredSignatures.push(possibility);\n      }\n      remainingSignatures = filteredSignatures;\n      if (remainingSignatures.length === 0) break;\n    }\n    // Return the first remaining signature that was totally matched:\n    let candidate;\n    for (candidate of remainingSignatures) {\n      if (candidate.params.length <= nParams) {\n        return candidate;\n      }\n    }\n    throw new TypeError('Signature not found (signature: ' + (fn.name || 'unnamed') + '(' + stringifyParams(params, ', ') + '))');\n  }\n\n  /**\n   * Find the proper function to call for a specific signature from\n   * a (composed) typed function, for example:\n   *\n   *   typed.find(fn, ['number', 'string'])\n   *   typed.find(fn, 'number, string')\n   *   typed.find(fn, 'number,string', {exact: true})\n   *\n   * This function find will by default return the best match to\n   * the given signature, possibly employing type conversions (and returning\n   * a function that will perform those conversions as needed). The\n   * (optional) third argument is a plain object giving options contolling\n   * the signature search. Currently only the option `exact` is implemented,\n   * which defaults to \"false\". If `exact` is specified as true, then only\n   * exact matches will be returned (i.e. signatures for which `fn` was\n   * directly defined). Uses of `any` and `...TYPE` are considered exact if\n   * no conversions are necessary to apply the corresponding function.\n   *\n   * @param {Function} fn                   A typed-function\n   * @param {string | string[]} signature\n   *     Signature to be found, can be an array or a comma separated string.\n   * @param {object} options  Controls the signature match as documented\n   * @return {function}\n   *     Returns the function to call for the given signature, or throws an\n   *     error if no match is found.\n   */\n  function find(fn, signature, options) {\n    return findSignature(fn, signature, options).implementation;\n  }\n\n  /**\n   * Convert a given value to another data type, specified by type name.\n   *\n   * @param {*} value\n   * @param {string} typeName\n   */\n  function convert(value, typeName) {\n    // check conversion is needed\n    const type = findType(typeName);\n    if (type.test(value)) {\n      return value;\n    }\n    const conversions = type.conversionsTo;\n    if (conversions.length === 0) {\n      throw new Error('There are no conversions to ' + typeName + ' defined.');\n    }\n    for (let i = 0; i < conversions.length; i++) {\n      const fromType = findType(conversions[i].from);\n      if (fromType.test(value)) {\n        return conversions[i].convert(value);\n      }\n    }\n    throw new Error('Cannot convert ' + value + ' to ' + typeName);\n  }\n\n  /**\n   * Stringify parameters in a normalized way\n   * @param {Param[]} params\n   * @param {string} [','] separator\n   * @return {string}\n   */\n  function stringifyParams(params) {\n    let separator = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ',';\n    return params.map(p => p.name).join(separator);\n  }\n\n  /**\n   * Parse a parameter, like \"...number | boolean\"\n   * @param {string} param\n   * @return {Param} param\n   */\n  function parseParam(param) {\n    const restParam = param.indexOf('...') === 0;\n    const types = !restParam ? param : param.length > 3 ? param.slice(3) : 'any';\n    const typeDefs = types.split('|').map(s => findType(s.trim()));\n    let hasAny = false;\n    let paramName = restParam ? '...' : '';\n    const exactTypes = typeDefs.map(function (type) {\n      hasAny = type.isAny || hasAny;\n      paramName += type.name + '|';\n      return {\n        name: type.name,\n        typeIndex: type.index,\n        test: type.test,\n        isAny: type.isAny,\n        conversion: null,\n        conversionIndex: -1\n      };\n    });\n    return {\n      types: exactTypes,\n      name: paramName.slice(0, -1),\n      // remove trailing '|' from above\n      hasAny,\n      hasConversion: false,\n      restParam\n    };\n  }\n\n  /**\n   * Expands a parsed parameter with the types available from currently\n   * defined conversions.\n   * @param {Param} param\n   * @return {Param} param\n   */\n  function expandParam(param) {\n    const typeNames = param.types.map(t => t.name);\n    const matchingConversions = availableConversions(typeNames);\n    let hasAny = param.hasAny;\n    let newName = param.name;\n    const convertibleTypes = matchingConversions.map(function (conversion) {\n      const type = findType(conversion.from);\n      hasAny = type.isAny || hasAny;\n      newName += '|' + conversion.from;\n      return {\n        name: conversion.from,\n        typeIndex: type.index,\n        test: type.test,\n        isAny: type.isAny,\n        conversion,\n        conversionIndex: conversion.index\n      };\n    });\n    return {\n      types: param.types.concat(convertibleTypes),\n      name: newName,\n      hasAny,\n      hasConversion: convertibleTypes.length > 0,\n      restParam: param.restParam\n    };\n  }\n\n  /**\n   * Return the set of type names in a parameter.\n   * Caches the result for efficiency\n   *\n   * @param {Param} param\n   * @return {Set<string>} typenames\n   */\n  function paramTypeSet(param) {\n    if (!param.typeSet) {\n      param.typeSet = new Set();\n      param.types.forEach(type => param.typeSet.add(type.name));\n    }\n    return param.typeSet;\n  }\n\n  /**\n   * Parse a signature with comma separated parameters,\n   * like \"number | boolean, ...string\"\n   *\n   * @param {string} signature\n   * @return {Param[]} params\n   */\n  function parseSignature(rawSignature) {\n    const params = [];\n    if (typeof rawSignature !== 'string') {\n      throw new TypeError('Signatures must be strings');\n    }\n    const signature = rawSignature.trim();\n    if (signature === '') {\n      return params;\n    }\n    const rawParams = signature.split(',');\n    for (let i = 0; i < rawParams.length; ++i) {\n      const parsedParam = parseParam(rawParams[i].trim());\n      if (parsedParam.restParam && i !== rawParams.length - 1) {\n        throw new SyntaxError('Unexpected rest parameter \"' + rawParams[i] + '\": ' + 'only allowed for the last parameter');\n      }\n      // if invalid, short-circuit (all the types may have been filtered)\n      if (parsedParam.types.length === 0) {\n        return null;\n      }\n      params.push(parsedParam);\n    }\n    return params;\n  }\n\n  /**\n   * Test whether a set of params contains a restParam\n   * @param {Param[]} params\n   * @return {boolean} Returns true when the last parameter is a restParam\n   */\n  function hasRestParam(params) {\n    const param = last(params);\n    return param ? param.restParam : false;\n  }\n\n  /**\n   * Create a type test for a single parameter, which can have one or multiple\n   * types.\n   * @param {Param} param\n   * @return {function(x: *) : boolean} Returns a test function\n   */\n  function compileTest(param) {\n    if (!param || param.types.length === 0) {\n      // nothing to do\n      return ok;\n    } else if (param.types.length === 1) {\n      return findType(param.types[0].name).test;\n    } else if (param.types.length === 2) {\n      const test0 = findType(param.types[0].name).test;\n      const test1 = findType(param.types[1].name).test;\n      return function or(x) {\n        return test0(x) || test1(x);\n      };\n    } else {\n      // param.types.length > 2\n      const tests = param.types.map(function (type) {\n        return findType(type.name).test;\n      });\n      return function or(x) {\n        for (let i = 0; i < tests.length; i++) {\n          if (tests[i](x)) {\n            return true;\n          }\n        }\n        return false;\n      };\n    }\n  }\n\n  /**\n   * Create a test for all parameters of a signature\n   * @param {Param[]} params\n   * @return {function(args: Array<*>) : boolean}\n   */\n  function compileTests(params) {\n    let tests, test0, test1;\n    if (hasRestParam(params)) {\n      // variable arguments like '...number'\n      tests = initial(params).map(compileTest);\n      const varIndex = tests.length;\n      const lastTest = compileTest(last(params));\n      const testRestParam = function (args) {\n        for (let i = varIndex; i < args.length; i++) {\n          if (!lastTest(args[i])) {\n            return false;\n          }\n        }\n        return true;\n      };\n      return function testArgs(args) {\n        for (let i = 0; i < tests.length; i++) {\n          if (!tests[i](args[i])) {\n            return false;\n          }\n        }\n        return testRestParam(args) && args.length >= varIndex + 1;\n      };\n    } else {\n      // no variable arguments\n      if (params.length === 0) {\n        return function testArgs(args) {\n          return args.length === 0;\n        };\n      } else if (params.length === 1) {\n        test0 = compileTest(params[0]);\n        return function testArgs(args) {\n          return test0(args[0]) && args.length === 1;\n        };\n      } else if (params.length === 2) {\n        test0 = compileTest(params[0]);\n        test1 = compileTest(params[1]);\n        return function testArgs(args) {\n          return test0(args[0]) && test1(args[1]) && args.length === 2;\n        };\n      } else {\n        // arguments.length > 2\n        tests = params.map(compileTest);\n        return function testArgs(args) {\n          for (let i = 0; i < tests.length; i++) {\n            if (!tests[i](args[i])) {\n              return false;\n            }\n          }\n          return args.length === tests.length;\n        };\n      }\n    }\n  }\n\n  /**\n   * Find the parameter at a specific index of a Params list.\n   * Handles rest parameters.\n   * @param {Param[]} params\n   * @param {number} index\n   * @return {Param | null} Returns the matching parameter when found,\n   *                        null otherwise.\n   */\n  function getParamAtIndex(params, index) {\n    return index < params.length ? params[index] : hasRestParam(params) ? last(params) : null;\n  }\n\n  /**\n   * Get all type names of a parameter\n   * @param {Params[]} params\n   * @param {number} index\n   * @return {string[]} Returns an array with type names\n   */\n  function getTypeSetAtIndex(params, index) {\n    const param = getParamAtIndex(params, index);\n    if (!param) {\n      return new Set();\n    }\n    return paramTypeSet(param);\n  }\n\n  /**\n   * Test whether a type is an exact type or conversion\n   * @param {Type} type\n   * @return {boolean} Returns true when\n   */\n  function isExactType(type) {\n    return type.conversion === null || type.conversion === undefined;\n  }\n\n  /**\n   * Helper function for creating error messages: create an array with\n   * all available types on a specific argument index.\n   * @param {Signature[]} signatures\n   * @param {number} index\n   * @return {string[]} Returns an array with available types\n   */\n  function mergeExpectedParams(signatures, index) {\n    const typeSet = new Set();\n    signatures.forEach(signature => {\n      const paramSet = getTypeSetAtIndex(signature.params, index);\n      let name;\n      for (name of paramSet) {\n        typeSet.add(name);\n      }\n    });\n    return typeSet.has('any') ? ['any'] : Array.from(typeSet);\n  }\n\n  /**\n   * Create\n   * @param {string} name             The name of the function\n   * @param {array.<*>} args          The actual arguments passed to the function\n   * @param {Signature[]} signatures  A list with available signatures\n   * @return {TypeError} Returns a type error with additional data\n   *                     attached to it in the property `data`\n   */\n  function createError(name, args, signatures) {\n    let err, expected;\n    const _name = name || 'unnamed';\n\n    // test for wrong type at some index\n    let matchingSignatures = signatures;\n    let index;\n    for (index = 0; index < args.length; index++) {\n      const nextMatchingDefs = [];\n      matchingSignatures.forEach(signature => {\n        const param = getParamAtIndex(signature.params, index);\n        const test = compileTest(param);\n        if ((index < signature.params.length || hasRestParam(signature.params)) && test(args[index])) {\n          nextMatchingDefs.push(signature);\n        }\n      });\n      if (nextMatchingDefs.length === 0) {\n        // no matching signatures anymore, throw error \"wrong type\"\n        expected = mergeExpectedParams(matchingSignatures, index);\n        if (expected.length > 0) {\n          const actualTypes = findTypeNames(args[index]);\n          err = new TypeError('Unexpected type of argument in function ' + _name + ' (expected: ' + expected.join(' or ') + ', actual: ' + actualTypes.join(' | ') + ', index: ' + index + ')');\n          err.data = {\n            category: 'wrongType',\n            fn: _name,\n            index,\n            actual: actualTypes,\n            expected\n          };\n          return err;\n        }\n      } else {\n        matchingSignatures = nextMatchingDefs;\n      }\n    }\n\n    // test for too few arguments\n    const lengths = matchingSignatures.map(function (signature) {\n      return hasRestParam(signature.params) ? Infinity : signature.params.length;\n    });\n    if (args.length < Math.min.apply(null, lengths)) {\n      expected = mergeExpectedParams(matchingSignatures, index);\n      err = new TypeError('Too few arguments in function ' + _name + ' (expected: ' + expected.join(' or ') + ', index: ' + args.length + ')');\n      err.data = {\n        category: 'tooFewArgs',\n        fn: _name,\n        index: args.length,\n        expected\n      };\n      return err;\n    }\n\n    // test for too many arguments\n    const maxLength = Math.max.apply(null, lengths);\n    if (args.length > maxLength) {\n      err = new TypeError('Too many arguments in function ' + _name + ' (expected: ' + maxLength + ', actual: ' + args.length + ')');\n      err.data = {\n        category: 'tooManyArgs',\n        fn: _name,\n        index: args.length,\n        expectedLength: maxLength\n      };\n      return err;\n    }\n\n    // Generic error\n    const argTypes = [];\n    for (let i = 0; i < args.length; ++i) {\n      argTypes.push(findTypeNames(args[i]).join('|'));\n    }\n    err = new TypeError('Arguments of type \"' + argTypes.join(', ') + '\" do not match any of the defined signatures of function ' + _name + '.');\n    err.data = {\n      category: 'mismatch',\n      actual: argTypes\n    };\n    return err;\n  }\n\n  /**\n   * Find the lowest index of all exact types of a parameter (no conversions)\n   * @param {Param} param\n   * @return {number} Returns the index of the lowest type in typed.types\n   */\n  function getLowestTypeIndex(param) {\n    let min = typeList.length + 1;\n    for (let i = 0; i < param.types.length; i++) {\n      if (isExactType(param.types[i])) {\n        min = Math.min(min, param.types[i].typeIndex);\n      }\n    }\n    return min;\n  }\n\n  /**\n   * Find the lowest index of the conversion of all types of the parameter\n   * having a conversion\n   * @param {Param} param\n   * @return {number} Returns the lowest index of the conversions of this type\n   */\n  function getLowestConversionIndex(param) {\n    let min = nConversions + 1;\n    for (let i = 0; i < param.types.length; i++) {\n      if (!isExactType(param.types[i])) {\n        min = Math.min(min, param.types[i].conversionIndex);\n      }\n    }\n    return min;\n  }\n\n  /**\n   * Compare two params\n   * @param {Param} param1\n   * @param {Param} param2\n   * @return {number} returns -1 when param1 must get a lower\n   *                  index than param2, 1 when the opposite,\n   *                  or zero when both are equal\n   */\n  function compareParams(param1, param2) {\n    // We compare a number of metrics on a param in turn:\n    // 1) 'any' parameters are the least preferred\n    if (param1.hasAny) {\n      if (!param2.hasAny) {\n        return 1;\n      }\n    } else if (param2.hasAny) {\n      return -1;\n    }\n\n    // 2) Prefer non-rest to rest parameters\n    if (param1.restParam) {\n      if (!param2.restParam) {\n        return 1;\n      }\n    } else if (param2.restParam) {\n      return -1;\n    }\n\n    // 3) Prefer exact type match to conversions\n    if (param1.hasConversion) {\n      if (!param2.hasConversion) {\n        return 1;\n      }\n    } else if (param2.hasConversion) {\n      return -1;\n    }\n\n    // 4) Prefer lower type index:\n    const typeDiff = getLowestTypeIndex(param1) - getLowestTypeIndex(param2);\n    if (typeDiff < 0) {\n      return -1;\n    }\n    if (typeDiff > 0) {\n      return 1;\n    }\n\n    // 5) Prefer lower conversion index\n    const convDiff = getLowestConversionIndex(param1) - getLowestConversionIndex(param2);\n    if (convDiff < 0) {\n      return -1;\n    }\n    if (convDiff > 0) {\n      return 1;\n    }\n\n    // Don't have a basis for preference\n    return 0;\n  }\n\n  /**\n   * Compare two signatures\n   * @param {Signature} signature1\n   * @param {Signature} signature2\n   * @return {number} returns a negative number when param1 must get a lower\n   *                  index than param2, a positive number when the opposite,\n   *                  or zero when both are equal\n   */\n  function compareSignatures(signature1, signature2) {\n    const pars1 = signature1.params;\n    const pars2 = signature2.params;\n    const last1 = last(pars1);\n    const last2 = last(pars2);\n    const hasRest1 = hasRestParam(pars1);\n    const hasRest2 = hasRestParam(pars2);\n    // We compare a number of metrics on signatures in turn:\n    // 1) An \"any rest param\" is least preferred\n    if (hasRest1 && last1.hasAny) {\n      if (!hasRest2 || !last2.hasAny) {\n        return 1;\n      }\n    } else if (hasRest2 && last2.hasAny) {\n      return -1;\n    }\n\n    // 2) Minimize the number of 'any' parameters\n    let any1 = 0;\n    let conv1 = 0;\n    let par;\n    for (par of pars1) {\n      if (par.hasAny) ++any1;\n      if (par.hasConversion) ++conv1;\n    }\n    let any2 = 0;\n    let conv2 = 0;\n    for (par of pars2) {\n      if (par.hasAny) ++any2;\n      if (par.hasConversion) ++conv2;\n    }\n    if (any1 !== any2) {\n      return any1 - any2;\n    }\n\n    // 3) A conversion rest param is less preferred\n    if (hasRest1 && last1.hasConversion) {\n      if (!hasRest2 || !last2.hasConversion) {\n        return 1;\n      }\n    } else if (hasRest2 && last2.hasConversion) {\n      return -1;\n    }\n\n    // 4) Minimize the number of conversions\n    if (conv1 !== conv2) {\n      return conv1 - conv2;\n    }\n\n    // 5) Prefer no rest param\n    if (hasRest1) {\n      if (!hasRest2) {\n        return 1;\n      }\n    } else if (hasRest2) {\n      return -1;\n    }\n\n    // 6) Prefer shorter with rest param, longer without\n    const lengthCriterion = (pars1.length - pars2.length) * (hasRest1 ? -1 : 1);\n    if (lengthCriterion !== 0) {\n      return lengthCriterion;\n    }\n\n    // Signatures are identical in each of the above metrics.\n    // In particular, they are the same length.\n    // We can therefore compare the parameters one by one.\n    // First we count which signature has more preferred parameters.\n    const comparisons = [];\n    let tc = 0;\n    for (let i = 0; i < pars1.length; ++i) {\n      const thisComparison = compareParams(pars1[i], pars2[i]);\n      comparisons.push(thisComparison);\n      tc += thisComparison;\n    }\n    if (tc !== 0) {\n      return tc;\n    }\n\n    // They have the same number of preferred parameters, so go by the\n    // earliest parameter in which we have a preference.\n    // In other words, dispatch is driven somewhat more by earlier\n    // parameters than later ones.\n    let c;\n    for (c of comparisons) {\n      if (c !== 0) {\n        return c;\n      }\n    }\n\n    // It's a tossup:\n    return 0;\n  }\n\n  /**\n   * Produce a list of all conversions from distinct types to one of\n   * the given types.\n   *\n   * @param {string[]} typeNames\n   * @return {ConversionDef[]} Returns the conversions that are available\n   *                        resulting in any given type (if any)\n   */\n  function availableConversions(typeNames) {\n    if (typeNames.length === 0) {\n      return [];\n    }\n    const types = typeNames.map(findType);\n    if (typeNames.length > 1) {\n      types.sort((t1, t2) => t1.index - t2.index);\n    }\n    let matches = types[0].conversionsTo;\n    if (typeNames.length === 1) {\n      return matches;\n    }\n    matches = matches.concat([]); // shallow copy the matches\n    // Since the types are now in index order, we just want the first\n    // occurrence of any from type:\n    const knownTypes = new Set(typeNames);\n    for (let i = 1; i < types.length; ++i) {\n      let newMatch;\n      for (newMatch of types[i].conversionsTo) {\n        if (!knownTypes.has(newMatch.from)) {\n          matches.push(newMatch);\n          knownTypes.add(newMatch.from);\n        }\n      }\n    }\n    return matches;\n  }\n\n  /**\n   * Preprocess arguments before calling the original function:\n   * - if needed convert the parameters\n   * - in case of rest parameters, move the rest parameters into an Array\n   * @param {Param[]} params\n   * @param {function} fn\n   * @return {function} Returns a wrapped function\n   */\n  function compileArgsPreprocessing(params, fn) {\n    let fnConvert = fn;\n\n    // TODO: can we make this wrapper function smarter/simpler?\n\n    if (params.some(p => p.hasConversion)) {\n      const restParam = hasRestParam(params);\n      const compiledConversions = params.map(compileArgConversion);\n      fnConvert = function convertArgs() {\n        const args = [];\n        const last = restParam ? arguments.length - 1 : arguments.length;\n        for (let i = 0; i < last; i++) {\n          args[i] = compiledConversions[i](arguments[i]);\n        }\n        if (restParam) {\n          args[last] = arguments[last].map(compiledConversions[last]);\n        }\n        return fn.apply(this, args);\n      };\n    }\n    let fnPreprocess = fnConvert;\n    if (hasRestParam(params)) {\n      const offset = params.length - 1;\n      fnPreprocess = function preprocessRestParams() {\n        return fnConvert.apply(this, slice(arguments, 0, offset).concat([slice(arguments, offset)]));\n      };\n    }\n    return fnPreprocess;\n  }\n\n  /**\n   * Compile conversion for a parameter to the right type\n   * @param {Param} param\n   * @return {function} Returns the wrapped function that will convert arguments\n   *\n   */\n  function compileArgConversion(param) {\n    let test0, test1, conversion0, conversion1;\n    const tests = [];\n    const conversions = [];\n    param.types.forEach(function (type) {\n      if (type.conversion) {\n        tests.push(findType(type.conversion.from).test);\n        conversions.push(type.conversion.convert);\n      }\n    });\n\n    // create optimized conversion functions depending on the number of conversions\n    switch (conversions.length) {\n      case 0:\n        return function convertArg(arg) {\n          return arg;\n        };\n      case 1:\n        test0 = tests[0];\n        conversion0 = conversions[0];\n        return function convertArg(arg) {\n          if (test0(arg)) {\n            return conversion0(arg);\n          }\n          return arg;\n        };\n      case 2:\n        test0 = tests[0];\n        test1 = tests[1];\n        conversion0 = conversions[0];\n        conversion1 = conversions[1];\n        return function convertArg(arg) {\n          if (test0(arg)) {\n            return conversion0(arg);\n          }\n          if (test1(arg)) {\n            return conversion1(arg);\n          }\n          return arg;\n        };\n      default:\n        return function convertArg(arg) {\n          for (let i = 0; i < conversions.length; i++) {\n            if (tests[i](arg)) {\n              return conversions[i](arg);\n            }\n          }\n          return arg;\n        };\n    }\n  }\n\n  /**\n   * Split params with union types in to separate params.\n   *\n   * For example:\n   *\n   *     splitParams([['Array', 'Object'], ['string', 'RegExp'])\n   *     // returns:\n   *     // [\n   *     //   ['Array', 'string'],\n   *     //   ['Array', 'RegExp'],\n   *     //   ['Object', 'string'],\n   *     //   ['Object', 'RegExp']\n   *     // ]\n   *\n   * @param {Param[]} params\n   * @return {Param[]}\n   */\n  function splitParams(params) {\n    function _splitParams(params, index, paramsSoFar) {\n      if (index < params.length) {\n        const param = params[index];\n        let resultingParams = [];\n        if (param.restParam) {\n          // split the types of a rest parameter in two:\n          // one with only exact types, and one with exact types and conversions\n          const exactTypes = param.types.filter(isExactType);\n          if (exactTypes.length < param.types.length) {\n            resultingParams.push({\n              types: exactTypes,\n              name: '...' + exactTypes.map(t => t.name).join('|'),\n              hasAny: exactTypes.some(t => t.isAny),\n              hasConversion: false,\n              restParam: true\n            });\n          }\n          resultingParams.push(param);\n        } else {\n          // split all the types of a regular parameter into one type per param\n          resultingParams = param.types.map(function (type) {\n            return {\n              types: [type],\n              name: type.name,\n              hasAny: type.isAny,\n              hasConversion: type.conversion,\n              restParam: false\n            };\n          });\n        }\n\n        // recurse over the groups with types\n        return flatMap(resultingParams, function (nextParam) {\n          return _splitParams(params, index + 1, paramsSoFar.concat([nextParam]));\n        });\n      } else {\n        // we've reached the end of the parameters.\n        return [paramsSoFar];\n      }\n    }\n    return _splitParams(params, 0, []);\n  }\n\n  /**\n   * Test whether two param lists represent conflicting signatures\n   * @param {Param[]} params1\n   * @param {Param[]} params2\n   * @return {boolean} Returns true when the signatures conflict, false otherwise.\n   */\n  function conflicting(params1, params2) {\n    const ii = Math.max(params1.length, params2.length);\n    for (let i = 0; i < ii; i++) {\n      const typeSet1 = getTypeSetAtIndex(params1, i);\n      const typeSet2 = getTypeSetAtIndex(params2, i);\n      let overlap = false;\n      let name;\n      for (name of typeSet2) {\n        if (typeSet1.has(name)) {\n          overlap = true;\n          break;\n        }\n      }\n      if (!overlap) {\n        return false;\n      }\n    }\n    const len1 = params1.length;\n    const len2 = params2.length;\n    const restParam1 = hasRestParam(params1);\n    const restParam2 = hasRestParam(params2);\n    return restParam1 ? restParam2 ? len1 === len2 : len2 >= len1 : restParam2 ? len1 >= len2 : len1 === len2;\n  }\n\n  /**\n   * Helper function for `resolveReferences` that returns a copy of\n   * functionList wihe any prior resolutions cleared out, in case we are\n   * recycling signatures from a prior typed function construction.\n   *\n   * @param {Array.<function|typed-reference>} functionList\n   * @return {Array.<function|typed-reference>}\n   */\n  function clearResolutions(functionList) {\n    return functionList.map(fn => {\n      if (isReferToSelf(fn)) {\n        return referToSelf(fn.referToSelf.callback);\n      }\n      if (isReferTo(fn)) {\n        return makeReferTo(fn.referTo.references, fn.referTo.callback);\n      }\n      return fn;\n    });\n  }\n\n  /**\n   * Take a list of references, a list of functions functionList, and a\n   * signatureMap indexing signatures into functionList, and return\n   * the list of resolutions, or a false-y value if they don't all\n   * resolve in a valid way (yet).\n   *\n   * @param {string[]} references\n   * @param {Array<function|typed-reference} functionList\n   * @param {Object.<string, integer>} signatureMap\n   * @return {function[] | false} resolutions\n   */\n  function collectResolutions(references, functionList, signatureMap) {\n    const resolvedReferences = [];\n    let reference;\n    for (reference of references) {\n      let resolution = signatureMap[reference];\n      if (typeof resolution !== 'number') {\n        throw new TypeError('No definition for referenced signature \"' + reference + '\"');\n      }\n      resolution = functionList[resolution];\n      if (typeof resolution !== 'function') {\n        return false;\n      }\n      resolvedReferences.push(resolution);\n    }\n    return resolvedReferences;\n  }\n\n  /**\n   * Resolve any references in the functionList for the typed function\n   * itself. The signatureMap tells which index in the functionList a\n   * given signature should be mapped to (for use in resolving typed.referTo)\n   * and self provides the destions of a typed.referToSelf.\n   *\n   * @param {Array<function | typed-reference-object>} functionList\n   * @param {Object.<string, function>} signatureMap\n   * @param {function} self  The typed-function itself\n   * @return {Array<function>} The list of resolved functions\n   */\n  function resolveReferences(functionList, signatureMap, self) {\n    const resolvedFunctions = clearResolutions(functionList);\n    const isResolved = new Array(resolvedFunctions.length).fill(false);\n    let leftUnresolved = true;\n    while (leftUnresolved) {\n      leftUnresolved = false;\n      let nothingResolved = true;\n      for (let i = 0; i < resolvedFunctions.length; ++i) {\n        if (isResolved[i]) continue;\n        const fn = resolvedFunctions[i];\n        if (isReferToSelf(fn)) {\n          resolvedFunctions[i] = fn.referToSelf.callback(self);\n          // Preserve reference in case signature is reused someday:\n          resolvedFunctions[i].referToSelf = fn.referToSelf;\n          isResolved[i] = true;\n          nothingResolved = false;\n        } else if (isReferTo(fn)) {\n          const resolvedReferences = collectResolutions(fn.referTo.references, resolvedFunctions, signatureMap);\n          if (resolvedReferences) {\n            resolvedFunctions[i] = fn.referTo.callback.apply(this, resolvedReferences);\n            // Preserve reference in case signature is reused someday:\n            resolvedFunctions[i].referTo = fn.referTo;\n            isResolved[i] = true;\n            nothingResolved = false;\n          } else {\n            leftUnresolved = true;\n          }\n        }\n      }\n      if (nothingResolved && leftUnresolved) {\n        throw new SyntaxError('Circular reference detected in resolving typed.referTo');\n      }\n    }\n    return resolvedFunctions;\n  }\n\n  /**\n   * Validate whether any of the function bodies contains a self-reference\n   * usage like `this(...)` or `this.signatures`. This self-referencing is\n   * deprecated since typed-function v3. It has been replaced with\n   * the functions typed.referTo and typed.referToSelf.\n   * @param {Object.<string, function>} signaturesMap\n   */\n  function validateDeprecatedThis(signaturesMap) {\n    // TODO: remove this deprecation warning logic some day (it's introduced in v3)\n\n    // match occurrences like 'this(' and 'this.signatures'\n    const deprecatedThisRegex = /\\bthis(\\(|\\.signatures\\b)/;\n    Object.keys(signaturesMap).forEach(signature => {\n      const fn = signaturesMap[signature];\n      if (deprecatedThisRegex.test(fn.toString())) {\n        throw new SyntaxError('Using `this` to self-reference a function ' + 'is deprecated since typed-function@3. ' + 'Use typed.referTo and typed.referToSelf instead.');\n      }\n    });\n  }\n\n  /**\n   * Create a typed function\n   * @param {String} name               The name for the typed function\n   * @param {Object.<string, function>} rawSignaturesMap\n   *                                    An object with one or\n   *                                    multiple signatures as key, and the\n   *                                    function corresponding to the\n   *                                    signature as value.\n   * @return {function}  Returns the created typed function.\n   */\n  function createTypedFunction(name, rawSignaturesMap) {\n    typed.createCount++;\n    if (Object.keys(rawSignaturesMap).length === 0) {\n      throw new SyntaxError('No signatures provided');\n    }\n    if (typed.warnAgainstDeprecatedThis) {\n      validateDeprecatedThis(rawSignaturesMap);\n    }\n\n    // Main processing loop for signatures\n    const parsedParams = [];\n    const originalFunctions = [];\n    const signaturesMap = {};\n    const preliminarySignatures = []; // may have duplicates from conversions\n    let signature;\n    for (signature in rawSignaturesMap) {\n      // A) Protect against polluted Object prototype:\n      if (!Object.prototype.hasOwnProperty.call(rawSignaturesMap, signature)) {\n        continue;\n      }\n      // B) Parse the signature\n      const params = parseSignature(signature);\n      if (!params) continue;\n      // C) Check for conflicts\n      parsedParams.forEach(function (pp) {\n        if (conflicting(pp, params)) {\n          throw new TypeError('Conflicting signatures \"' + stringifyParams(pp) + '\" and \"' + stringifyParams(params) + '\".');\n        }\n      });\n      parsedParams.push(params);\n      // D) Store the provided function and add conversions\n      const functionIndex = originalFunctions.length;\n      originalFunctions.push(rawSignaturesMap[signature]);\n      const conversionParams = params.map(expandParam);\n      // E) Split the signatures and collect them up\n      let sp;\n      for (sp of splitParams(conversionParams)) {\n        const spName = stringifyParams(sp);\n        preliminarySignatures.push({\n          params: sp,\n          name: spName,\n          fn: functionIndex\n        });\n        if (sp.every(p => !p.hasConversion)) {\n          signaturesMap[spName] = functionIndex;\n        }\n      }\n    }\n    preliminarySignatures.sort(compareSignatures);\n\n    // Note the forward reference to theTypedFn\n    const resolvedFunctions = resolveReferences(originalFunctions, signaturesMap, theTypedFn);\n\n    // Fill in the proper function for each signature\n    let s;\n    for (s in signaturesMap) {\n      if (Object.prototype.hasOwnProperty.call(signaturesMap, s)) {\n        signaturesMap[s] = resolvedFunctions[signaturesMap[s]];\n      }\n    }\n    const signatures = [];\n    const internalSignatureMap = new Map(); // benchmarks faster than object\n    for (s of preliminarySignatures) {\n      // Note it's only safe to eliminate duplicates like this\n      // _after_ the signature sorting step above; otherwise we might\n      // remove the wrong one.\n      if (!internalSignatureMap.has(s.name)) {\n        s.fn = resolvedFunctions[s.fn];\n        signatures.push(s);\n        internalSignatureMap.set(s.name, s);\n      }\n    }\n\n    // we create a highly optimized checks for the first couple of signatures with max 2 arguments\n    const ok0 = signatures[0] && signatures[0].params.length <= 2 && !hasRestParam(signatures[0].params);\n    const ok1 = signatures[1] && signatures[1].params.length <= 2 && !hasRestParam(signatures[1].params);\n    const ok2 = signatures[2] && signatures[2].params.length <= 2 && !hasRestParam(signatures[2].params);\n    const ok3 = signatures[3] && signatures[3].params.length <= 2 && !hasRestParam(signatures[3].params);\n    const ok4 = signatures[4] && signatures[4].params.length <= 2 && !hasRestParam(signatures[4].params);\n    const ok5 = signatures[5] && signatures[5].params.length <= 2 && !hasRestParam(signatures[5].params);\n    const allOk = ok0 && ok1 && ok2 && ok3 && ok4 && ok5;\n\n    // compile the tests\n    for (let i = 0; i < signatures.length; ++i) {\n      signatures[i].test = compileTests(signatures[i].params);\n    }\n    const test00 = ok0 ? compileTest(signatures[0].params[0]) : notOk;\n    const test10 = ok1 ? compileTest(signatures[1].params[0]) : notOk;\n    const test20 = ok2 ? compileTest(signatures[2].params[0]) : notOk;\n    const test30 = ok3 ? compileTest(signatures[3].params[0]) : notOk;\n    const test40 = ok4 ? compileTest(signatures[4].params[0]) : notOk;\n    const test50 = ok5 ? compileTest(signatures[5].params[0]) : notOk;\n    const test01 = ok0 ? compileTest(signatures[0].params[1]) : notOk;\n    const test11 = ok1 ? compileTest(signatures[1].params[1]) : notOk;\n    const test21 = ok2 ? compileTest(signatures[2].params[1]) : notOk;\n    const test31 = ok3 ? compileTest(signatures[3].params[1]) : notOk;\n    const test41 = ok4 ? compileTest(signatures[4].params[1]) : notOk;\n    const test51 = ok5 ? compileTest(signatures[5].params[1]) : notOk;\n\n    // compile the functions\n    for (let i = 0; i < signatures.length; ++i) {\n      signatures[i].implementation = compileArgsPreprocessing(signatures[i].params, signatures[i].fn);\n    }\n    const fn0 = ok0 ? signatures[0].implementation : undef;\n    const fn1 = ok1 ? signatures[1].implementation : undef;\n    const fn2 = ok2 ? signatures[2].implementation : undef;\n    const fn3 = ok3 ? signatures[3].implementation : undef;\n    const fn4 = ok4 ? signatures[4].implementation : undef;\n    const fn5 = ok5 ? signatures[5].implementation : undef;\n    const len0 = ok0 ? signatures[0].params.length : -1;\n    const len1 = ok1 ? signatures[1].params.length : -1;\n    const len2 = ok2 ? signatures[2].params.length : -1;\n    const len3 = ok3 ? signatures[3].params.length : -1;\n    const len4 = ok4 ? signatures[4].params.length : -1;\n    const len5 = ok5 ? signatures[5].params.length : -1;\n\n    // simple and generic, but also slow\n    const iStart = allOk ? 6 : 0;\n    const iEnd = signatures.length;\n    // de-reference ahead for execution speed:\n    const tests = signatures.map(s => s.test);\n    const fns = signatures.map(s => s.implementation);\n    const generic = function generic() {\n      'use strict';\n\n      for (let i = iStart; i < iEnd; i++) {\n        if (tests[i](arguments)) {\n          return fns[i].apply(this, arguments);\n        }\n      }\n      return typed.onMismatch(name, arguments, signatures);\n    };\n\n    // create the typed function\n    // fast, specialized version. Falls back to the slower, generic one if needed\n    function theTypedFn(arg0, arg1) {\n      'use strict';\n\n      if (arguments.length === len0 && test00(arg0) && test01(arg1)) {\n        return fn0.apply(this, arguments);\n      }\n      if (arguments.length === len1 && test10(arg0) && test11(arg1)) {\n        return fn1.apply(this, arguments);\n      }\n      if (arguments.length === len2 && test20(arg0) && test21(arg1)) {\n        return fn2.apply(this, arguments);\n      }\n      if (arguments.length === len3 && test30(arg0) && test31(arg1)) {\n        return fn3.apply(this, arguments);\n      }\n      if (arguments.length === len4 && test40(arg0) && test41(arg1)) {\n        return fn4.apply(this, arguments);\n      }\n      if (arguments.length === len5 && test50(arg0) && test51(arg1)) {\n        return fn5.apply(this, arguments);\n      }\n      return generic.apply(this, arguments);\n    }\n\n    // attach name the typed function\n    try {\n      Object.defineProperty(theTypedFn, 'name', {\n        value: name\n      });\n    } catch (err) {\n      // old browsers do not support Object.defineProperty and some don't support setting the name property\n      // the function name is not essential for the functioning, it's mostly useful for debugging,\n      // so it's fine to have unnamed functions.\n    }\n\n    // attach signatures to the function.\n    // This property is close to the original collection of signatures\n    // used to create the typed-function, just with unions split:\n    theTypedFn.signatures = signaturesMap;\n\n    // Store internal data for functions like resolve, find, etc.\n    // Also serves as the flag that this is a typed-function\n    theTypedFn._typedFunctionData = {\n      signatures,\n      signatureMap: internalSignatureMap\n    };\n    return theTypedFn;\n  }\n\n  /**\n   * Action to take on mismatch\n   * @param {string} name      Name of function that was attempted to be called\n   * @param {Array} args       Actual arguments to the call\n   * @param {Array} signatures Known signatures of the named typed-function\n   */\n  function _onMismatch(name, args, signatures) {\n    throw createError(name, args, signatures);\n  }\n\n  /**\n   * Return all but the last items of an array or function Arguments\n   * @param {Array | Arguments} arr\n   * @return {Array}\n   */\n  function initial(arr) {\n    return slice(arr, 0, arr.length - 1);\n  }\n\n  /**\n   * return the last item of an array or function Arguments\n   * @param {Array | Arguments} arr\n   * @return {*}\n   */\n  function last(arr) {\n    return arr[arr.length - 1];\n  }\n\n  /**\n   * Slice an array or function Arguments\n   * @param {Array | Arguments | IArguments} arr\n   * @param {number} start\n   * @param {number} [end]\n   * @return {Array}\n   */\n  function slice(arr, start, end) {\n    return Array.prototype.slice.call(arr, start, end);\n  }\n\n  /**\n   * Return the first item from an array for which test(arr[i]) returns true\n   * @param {Array} arr\n   * @param {function} test\n   * @return {* | undefined} Returns the first matching item\n   *                         or undefined when there is no match\n   */\n  function findInArray(arr, test) {\n    for (let i = 0; i < arr.length; i++) {\n      if (test(arr[i])) {\n        return arr[i];\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * Flat map the result invoking a callback for every item in an array.\n   * https://gist.github.com/samgiles/762ee337dff48623e729\n   * @param {Array} arr\n   * @param {function} callback\n   * @return {Array}\n   */\n  function flatMap(arr, callback) {\n    return Array.prototype.concat.apply([], arr.map(callback));\n  }\n\n  /**\n   * Create a reference callback to one or multiple signatures\n   *\n   * Syntax:\n   *\n   *     typed.referTo(signature1, signature2, ..., function callback(fn1, fn2, ...) {\n   *       // ...\n   *     })\n   *\n   * @returns {{referTo: {references: string[], callback}}}\n   */\n  function referTo() {\n    const references = initial(arguments).map(s => stringifyParams(parseSignature(s)));\n    const callback = last(arguments);\n    if (typeof callback !== 'function') {\n      throw new TypeError('Callback function expected as last argument');\n    }\n    return makeReferTo(references, callback);\n  }\n  function makeReferTo(references, callback) {\n    return {\n      referTo: {\n        references,\n        callback\n      }\n    };\n  }\n\n  /**\n   * Create a reference callback to the typed-function itself\n   *\n   * @param {(self: function) => function} callback\n   * @returns {{referToSelf: { callback: function }}}\n   */\n  function referToSelf(callback) {\n    if (typeof callback !== 'function') {\n      throw new TypeError('Callback function expected as first argument');\n    }\n    return {\n      referToSelf: {\n        callback\n      }\n    };\n  }\n\n  /**\n   * Test whether something is a referTo object, holding a list with reference\n   * signatures and a callback.\n   *\n   * @param {Object | function} objectOrFn\n   * @returns {boolean}\n   */\n  function isReferTo(objectOrFn) {\n    return objectOrFn && typeof objectOrFn.referTo === 'object' && Array.isArray(objectOrFn.referTo.references) && typeof objectOrFn.referTo.callback === 'function';\n  }\n\n  /**\n   * Test whether something is a referToSelf object, holding a callback where\n   * to pass `self`.\n   *\n   * @param {Object | function} objectOrFn\n   * @returns {boolean}\n   */\n  function isReferToSelf(objectOrFn) {\n    return objectOrFn && typeof objectOrFn.referToSelf === 'object' && typeof objectOrFn.referToSelf.callback === 'function';\n  }\n\n  /**\n   * Check if name is (A) new, (B) a match, or (C) a mismatch; and throw\n   * an error in case (C).\n   *\n   * @param { string | undefined } nameSoFar\n   * @param { string | undefined } newName\n   * @returns { string } updated name\n   */\n  function checkName(nameSoFar, newName) {\n    if (!nameSoFar) {\n      return newName;\n    }\n    if (newName && newName !== nameSoFar) {\n      const err = new Error('Function names do not match (expected: ' + nameSoFar + ', actual: ' + newName + ')');\n      err.data = {\n        actual: newName,\n        expected: nameSoFar\n      };\n      throw err;\n    }\n    return nameSoFar;\n  }\n\n  /**\n   * Retrieve the implied name from an object with signature keys\n   * and function values, checking whether all value names match\n   *\n   * @param { {string: function} } obj\n   */\n  function getObjectName(obj) {\n    let name;\n    for (const key in obj) {\n      // Only pay attention to own properties, and only if their values\n      // are typed functions or functions with a signature property\n      if (Object.prototype.hasOwnProperty.call(obj, key) && (isTypedFunction(obj[key]) || typeof obj[key].signature === 'string')) {\n        name = checkName(name, obj[key].name);\n      }\n    }\n    return name;\n  }\n\n  /**\n   * Copy all of the signatures from the second argument into the first,\n   * which is modified by side effect, checking for conflicts\n   *\n   * @param {Object.<string, function|typed-reference>} dest\n   * @param {Object.<string, function|typed-reference>} source\n   */\n  function mergeSignatures(dest, source) {\n    let key;\n    for (key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        if (key in dest) {\n          if (source[key] !== dest[key]) {\n            const err = new Error('Signature \"' + key + '\" is defined twice');\n            err.data = {\n              signature: key,\n              sourceFunction: source[key],\n              destFunction: dest[key]\n            };\n            throw err;\n          }\n          // else: both signatures point to the same function, that's fine\n        }\n        dest[key] = source[key];\n      }\n    }\n  }\n  const saveTyped = typed;\n\n  /**\n   * Originally the main function was a typed function itself, but then\n   * it might not be able to generate error messages if the client\n   * replaced the type system with different names.\n   *\n   * Main entry: typed([name], functions/objects with signatures...)\n   *\n   * Assembles and returns a new typed-function from the given items\n   * that provide signatures and implementations, each of which may be\n   * * a plain object mapping (string) signatures to implementing functions,\n   * * a previously constructed typed function, or\n   * * any other single function with a string-valued property `signature`.\n    * The name of the resulting typed-function will be given by the\n   * string-valued name argument if present, or if not, by the name\n   * of any of the arguments that have one, as long as any that do are\n   * consistent with each other. If no name is specified, the name will be\n   * an empty string.\n   *\n   * @param {string} maybeName [optional]\n   * @param {(function|object)[]} signature providers\n   * @returns {typed-function}\n   */\n  typed = function (maybeName) {\n    const named = typeof maybeName === 'string';\n    const start = named ? 1 : 0;\n    let name = named ? maybeName : '';\n    const allSignatures = {};\n    for (let i = start; i < arguments.length; ++i) {\n      const item = arguments[i];\n      let theseSignatures = {};\n      let thisName;\n      if (typeof item === 'function') {\n        thisName = item.name;\n        if (typeof item.signature === 'string') {\n          // Case 1: Ordinary function with a string 'signature' property\n          theseSignatures[item.signature] = item;\n        } else if (isTypedFunction(item)) {\n          // Case 2: Existing typed function\n          theseSignatures = item.signatures;\n        }\n      } else if (isPlainObject(item)) {\n        // Case 3: Plain object, assume keys = signatures, values = functions\n        theseSignatures = item;\n        if (!named) {\n          thisName = getObjectName(item);\n        }\n      }\n      if (Object.keys(theseSignatures).length === 0) {\n        const err = new TypeError('Argument to \\'typed\\' at index ' + i + ' is not a (typed) function, ' + 'nor an object with signatures as keys and functions as values.');\n        err.data = {\n          index: i,\n          argument: item\n        };\n        throw err;\n      }\n      if (!named) {\n        name = checkName(name, thisName);\n      }\n      mergeSignatures(allSignatures, theseSignatures);\n    }\n    return createTypedFunction(name || '', allSignatures);\n  };\n  typed.create = create;\n  typed.createCount = saveTyped.createCount;\n  typed.onMismatch = _onMismatch;\n  typed.throwMismatchError = _onMismatch;\n  typed.createError = createError;\n  typed.clear = clear;\n  typed.clearConversions = clearConversions;\n  typed.addTypes = addTypes;\n  typed._findType = findType; // For unit testing only\n  typed.referTo = referTo;\n  typed.referToSelf = referToSelf;\n  typed.convert = convert;\n  typed.findSignature = findSignature;\n  typed.find = find;\n  typed.isTypedFunction = isTypedFunction;\n  typed.warnAgainstDeprecatedThis = true;\n\n  /**\n   * add a type (convenience wrapper for typed.addTypes)\n   * @param {{name: string, test: function}} type\n   * @param {boolean} [beforeObjectTest=true]\n   *                          If true, the new test will be inserted before\n   *                          the test with name 'Object' (if any), since\n   *                          tests for Object match Array and classes too.\n   */\n  typed.addType = function (type, beforeObjectTest) {\n    let before = 'any';\n    if (beforeObjectTest !== false && typeMap.has('Object')) {\n      before = 'Object';\n    }\n    typed.addTypes([type], before);\n  };\n\n  /**\n   * Verify that the ConversionDef conversion has a valid format.\n   *\n   * @param {conversionDef} conversion\n   * @return {void}\n   * @throws {TypeError|SyntaxError}\n   */\n  function _validateConversion(conversion) {\n    if (!conversion || typeof conversion.from !== 'string' || typeof conversion.to !== 'string' || typeof conversion.convert !== 'function') {\n      throw new TypeError('Object with properties {from: string, to: string, convert: function} expected');\n    }\n    if (conversion.to === conversion.from) {\n      throw new SyntaxError('Illegal to define conversion from \"' + conversion.from + '\" to itself.');\n    }\n  }\n\n  /**\n   * Add a conversion\n   *\n   * @param {ConversionDef} conversion\n   * @param {{override: boolean}} [options]\n   * @returns {void}\n   * @throws {TypeError}\n   */\n  typed.addConversion = function (conversion) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      override: false\n    };\n    _validateConversion(conversion);\n    const to = findType(conversion.to);\n    const existing = to.conversionsTo.find(other => other.from === conversion.from);\n    if (existing) {\n      if (options && options.override) {\n        typed.removeConversion({\n          from: existing.from,\n          to: conversion.to,\n          convert: existing.convert\n        });\n      } else {\n        throw new Error('There is already a conversion from \"' + conversion.from + '\" to \"' + to.name + '\"');\n      }\n    }\n    to.conversionsTo.push({\n      from: conversion.from,\n      convert: conversion.convert,\n      index: nConversions++\n    });\n  };\n\n  /**\n   * Convenience wrapper to call addConversion on each conversion in a list.\n   *\n   * @param {ConversionDef[]} conversions\n   * @param {{override: boolean}} [options]\n   * @returns {void}\n   * @throws {TypeError}\n   */\n  typed.addConversions = function (conversions, options) {\n    conversions.forEach(conversion => typed.addConversion(conversion, options));\n  };\n\n  /**\n   * Remove the specified conversion. The format is the same as for\n   * addConversion, and the convert function must match or an error\n   * is thrown.\n   *\n   * @param {{from: string, to: string, convert: function}} conversion\n   * @returns {void}\n   * @throws {TypeError|SyntaxError|Error}\n   */\n  typed.removeConversion = function (conversion) {\n    _validateConversion(conversion);\n    const to = findType(conversion.to);\n    const existingConversion = findInArray(to.conversionsTo, c => c.from === conversion.from);\n    if (!existingConversion) {\n      throw new Error('Attempt to remove nonexistent conversion from ' + conversion.from + ' to ' + conversion.to);\n    }\n    if (existingConversion.convert !== conversion.convert) {\n      throw new Error('Conversion to remove does not match existing conversion');\n    }\n    const index = to.conversionsTo.indexOf(existingConversion);\n    to.conversionsTo.splice(index, 1);\n  };\n\n  /**\n   * Produce the specific signature that a typed function\n   * will execute on the given arguments. Here, a \"signature\" is an\n   * object with properties 'params', 'test', 'fn', and 'implementation'.\n   * This last property is a function that converts params as necessary\n   * and then calls 'fn'. Returns null if there is no matching signature.\n   * @param {typed-function} tf\n   * @param {any[]} argList\n   * @returns {{params: string, test: function, fn: function, implementation: function}}\n   */\n  typed.resolve = function (tf, argList) {\n    if (!isTypedFunction(tf)) {\n      throw new TypeError(NOT_TYPED_FUNCTION);\n    }\n    const sigs = tf._typedFunctionData.signatures;\n    for (let i = 0; i < sigs.length; ++i) {\n      if (sigs[i].test(argList)) {\n        return sigs[i];\n      }\n    }\n    return null;\n  };\n  return typed;\n}\nexport default create();\n//# sourceMappingURL=typed-function.mjs.map"], "mappings": ";;;;;EAAA,SAASA,EAAEA,CAAA,EAAG;IACZ,OAAO,IAAI;EACb;EACA,SAASC,KAAKA,CAAA,EAAG;IACf,OAAO,KAAK;EACd;EACA,SAASC,KAAKA,CAAA,EAAG;IACf,OAAOC,SAAS;EAClB;EACA,MAAMC,kBAAkB,GAAG,mCAAmC;;EAE9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAqCA;;;EAGA,SAASC,MAAMA,CAAA,EAAG;IAClB;;IAEA;;;IAGE,SAASC,aAAaA,CAACC,CAAC,EAAE;MACxB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACC,WAAW,KAAKC,MAAM;IAC1E;IACE,MAAMC,MAAM,GAAG,CAAC;MACdC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAO,OAAOA,CAAC,KAAK,QAAQ;MAClC;IACA,CAAG,EAAE;MACDI,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAO,OAAOA,CAAC,KAAK,QAAQ;MAClC;IACA,CAAG,EAAE;MACDI,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAO,OAAOA,CAAC,KAAK,SAAS;MACnC;IACA,CAAG,EAAE;MACDI,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAO,OAAOA,CAAC,KAAK,UAAU;MACpC;IACA,CAAG,EAAE;MACDI,IAAI,EAAE,OAAO;MACbC,IAAI,EAAEC,KAAK,CAACC;IAChB,CAAG,EAAE;MACDH,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAOA,CAAC,YAAYQ,IAAI;MAC9B;IACA,CAAG,EAAE;MACDJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAOA,CAAC,YAAYS,MAAM;MAChC;IACA,CAAG,EAAE;MACDL,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAEN;IACV,CAAG,EAAE;MACDK,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAOA,CAAC,KAAK,IAAI;MACvB;IACA,CAAG,EAAE;MACDI,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;QACjB,OAAOA,CAAC,KAAKJ,SAAS;MAC5B;IACA,CAAG,CAAC;IACF,MAAMc,OAAO,GAAG;MACdN,IAAI,EAAE,KAAK;MACXC,IAAI,EAAEZ,EAAE;MACRkB,KAAK,EAAE;IACX,CAAG;;IAEH;IACA;IACA;IACA;IACA;IACE,IAAIC,OAAO,CAAC;IACZ,IAAIC,QAAQ,CAAC;;IAEf;IACE,IAAIC,YAAY,GAAG,CAAC;IACtB;;IAEA;IACE,IAAIC,KAAK,GAAG;MACVC,WAAW,EAAE;IACjB,CAAG;;IAEH;;;;;;;IAOE,SAASC,QAAQA,CAACC,QAAQ,EAAE;MAC1B,MAAMC,IAAI,GAAGP,OAAO,CAACQ,GAAG,CAACF,QAAQ,CAAC;MAClC,IAAIC,IAAI,EAAE;QACR,OAAOA,IAAI;MACjB;MACA;MACI,IAAIE,OAAO,GAAG,gBAAgB,GAAGH,QAAQ,GAAG,GAAG;MAC/C,MAAMd,IAAI,GAAGc,QAAQ,CAACI,WAAW,EAAE;MACnC,IAAIC,SAAS;MACb,KAAKA,SAAS,IAAIV,QAAQ,EAAE;QAC1B,IAAIU,SAAS,CAACD,WAAW,EAAE,KAAKlB,IAAI,EAAE;UACpCiB,OAAO,IAAI,kBAAkB,GAAGE,SAAS,GAAG,KAAK;UACjD;QACR;MACA;MACI,MAAM,IAAIC,SAAS,CAACH,OAAO,CAAC;IAChC;;IAEA;;;;;;;;;;;;;IAaE,SAASI,QAAQA,CAACC,KAAK,EAAE;MACvB,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKhC,SAAS,GAAGgC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC1F,MAAME,WAAW,GAAGH,UAAU,GAAGV,QAAQ,CAACU,UAAU,CAAC,CAACI,KAAK,GAAGlB,QAAQ,CAACgB,MAAM;MAC7E,MAAMG,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACG,MAAM,EAAE,EAAEI,CAAC,EAAE;QACrC,IAAI,CAACP,KAAK,CAACO,CAAC,CAAC,IAAI,OAAOP,KAAK,CAACO,CAAC,CAAC,CAAC7B,IAAI,KAAK,QAAQ,IAAI,OAAOsB,KAAK,CAACO,CAAC,CAAC,CAAC5B,IAAI,KAAK,UAAU,EAAE;UACzF,MAAM,IAAImB,SAAS,CAAC,gEAAgE,CAAC;QAC7F;QACM,MAAMN,QAAQ,GAAGQ,KAAK,CAACO,CAAC,CAAC,CAAC7B,IAAI;QAC9B,IAAIQ,OAAO,CAACsB,GAAG,CAAChB,QAAQ,CAAC,EAAE;UACzB,MAAM,IAAIM,SAAS,CAAC,uBAAuB,GAAGN,QAAQ,GAAG,GAAG,CAAC;QACrE;QACMc,QAAQ,CAACG,IAAI,CAACjB,QAAQ,CAAC;QACvBN,OAAO,CAACwB,GAAG,CAAClB,QAAQ,EAAE;UACpBd,IAAI,EAAEc,QAAQ;UACdb,IAAI,EAAEqB,KAAK,CAACO,CAAC,CAAC,CAAC5B,IAAI;UACnBM,KAAK,EAAEe,KAAK,CAACO,CAAC,CAAC,CAACtB,KAAK;UACrBoB,KAAK,EAAED,WAAW,GAAGG,CAAC;UACtBI,aAAa,EAAE,EAAE;QACzB,CAAO,CAAC;MACR;MACA;MACI,MAAMC,aAAa,GAAGzB,QAAQ,CAAC0B,KAAK,CAACT,WAAW,CAAC;MACjDjB,QAAQ,GAAGA,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAET,WAAW,CAAC,CAACU,MAAM,CAACR,QAAQ,CAAC,CAACQ,MAAM,CAACF,aAAa,CAAC;MACpF;MACI,KAAK,IAAIL,CAAC,GAAGH,WAAW,GAAGE,QAAQ,CAACH,MAAM,EAAEI,CAAC,GAAGpB,QAAQ,CAACgB,MAAM,EAAE,EAAEI,CAAC,EAAE;QACpErB,OAAO,CAACQ,GAAG,CAACP,QAAQ,CAACoB,CAAC,CAAC,CAAC,CAACF,KAAK,GAAGE,CAAC;MACxC;IACA;;IAEA;;;;;;IAME,SAASQ,KAAKA,CAAA,EAAG;MACf7B,OAAO,GAAG,IAAI8B,GAAG,EAAE;MACnB7B,QAAQ,GAAG,EAAE;MACbC,YAAY,GAAG,CAAC;MAChBW,QAAQ,CAAC,CAACf,OAAO,CAAC,EAAE,KAAK,CAAC;IAC9B;;IAEA;IACE+B,KAAK,EAAE;IACPhB,QAAQ,CAACtB,MAAM,CAAC;;IAElB;;;IAGE,SAASwC,gBAAgBA,CAAA,EAAG;MAC1B,IAAIzB,QAAQ;MACZ,KAAKA,QAAQ,IAAIL,QAAQ,EAAE;QACzBD,OAAO,CAACQ,GAAG,CAACF,QAAQ,CAAC,CAACmB,aAAa,GAAG,EAAE;MAC9C;MACIvB,YAAY,GAAG,CAAC;IACpB;;IAEA;;;;;;IAME,SAAS8B,aAAaA,CAACC,KAAK,EAAE;MAC5B,MAAMC,OAAO,GAAGjC,QAAQ,CAACkC,MAAM,CAAC3C,IAAI,IAAI;QACtC,MAAMe,IAAI,GAAGP,OAAO,CAACQ,GAAG,CAAChB,IAAI,CAAC;QAC9B,OAAO,CAACe,IAAI,CAACR,KAAK,IAAIQ,IAAI,CAACd,IAAI,CAACwC,KAAK,CAAC;MAC5C,CAAK,CAAC;MACF,IAAIC,OAAO,CAACjB,MAAM,EAAE;QAClB,OAAOiB,OAAO;MACpB;MACI,OAAO,CAAC,KAAK,CAAC;IAClB;;IAEA;;;;;IAKE,SAASE,eAAeA,CAACC,MAAM,EAAE;MAC/B,OAAOA,MAAM,IAAI,OAAOA,MAAM,KAAK,UAAU,IAAI,oBAAoB,IAAIA,MAAM;IACnF;;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCE,SAASC,aAAaA,CAACC,EAAE,EAAEC,SAAS,EAAEC,OAAO,EAAE;MAC7C,IAAI,CAACL,eAAe,CAACG,EAAE,CAAC,EAAE;QACxB,MAAM,IAAI3B,SAAS,CAAC3B,kBAAkB,CAAC;MAC7C;;MAEA;MACI,MAAMyD,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAACC,KAAK;MACtC,MAAMC,eAAe,GAAGjD,KAAK,CAACC,OAAO,CAAC6C,SAAS,CAAC,GAAGA,SAAS,CAACI,IAAI,CAAC,GAAG,CAAC,GAAGJ,SAAS;MAClF,MAAMK,MAAM,GAAGC,cAAc,CAACH,eAAe,CAAC;MAC9C,MAAMI,kBAAkB,GAAGC,eAAe,CAACH,MAAM,CAAC;;MAEtD;MACI,IAAI,CAACH,KAAK,IAAIK,kBAAkB,IAAIR,EAAE,CAACU,UAAU,EAAE;QACvD;QACM,MAAMC,KAAK,GAAGX,EAAE,CAACY,kBAAkB,CAACC,YAAY,CAAC5C,GAAG,CAACuC,kBAAkB,CAAC;QACxE,IAAIG,KAAK,EAAE;UACT,OAAOA,KAAK;QACpB;MACA;;MAEA;MACA;MACA;MACA;MACI,MAAMG,OAAO,GAAGR,MAAM,CAAC5B,MAAM;MAC7B,IAAIqC,mBAAmB;MACvB,IAAIZ,KAAK,EAAE;QACTY,mBAAmB,GAAG,EAAE;QACxB,IAAI9D,IAAI;QACR,KAAKA,IAAI,IAAI+C,EAAE,CAACU,UAAU,EAAE;UAC1BK,mBAAmB,CAAC/B,IAAI,CAACgB,EAAE,CAACY,kBAAkB,CAACC,YAAY,CAAC5C,GAAG,CAAChB,IAAI,CAAC,CAAC;QAC9E;MACA,CAAK,MAAM;QACL8D,mBAAmB,GAAGf,EAAE,CAACY,kBAAkB,CAACF,UAAU;MAC5D;MACI,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,OAAO,EAAE,EAAEhC,CAAC,EAAE;QAChC,MAAMkC,IAAI,GAAGV,MAAM,CAACxB,CAAC,CAAC;QACtB,MAAMmC,kBAAkB,GAAG,EAAE;QAC7B,IAAIC,WAAW;QACf,KAAKA,WAAW,IAAIH,mBAAmB,EAAE;UACvC,MAAMI,IAAI,GAAGC,eAAe,CAACF,WAAW,CAACZ,MAAM,EAAExB,CAAC,CAAC;UACnD,IAAI,CAACqC,IAAI,IAAIH,IAAI,CAACK,SAAS,IAAI,CAACF,IAAI,CAACE,SAAS,EAAE;YAC9C;UACV;UACQ,IAAI,CAACF,IAAI,CAACG,MAAM,EAAE;YAC1B;YACU,MAAMC,SAAS,GAAGC,YAAY,CAACL,IAAI,CAAC;YACpC,IAAIH,IAAI,CAACzC,KAAK,CAACkD,IAAI,CAACC,KAAK,IAAI,CAACH,SAAS,CAACxC,GAAG,CAAC2C,KAAK,CAACzE,IAAI,CAAC,CAAC,EAAE;cACxD;YACZ;UACA;UACA;UACQgE,kBAAkB,CAACjC,IAAI,CAACkC,WAAW,CAAC;QAC5C;QACMH,mBAAmB,GAAGE,kBAAkB;QACxC,IAAIF,mBAAmB,CAACrC,MAAM,KAAK,CAAC,EAAE;MAC5C;MACA;MACI,IAAIiD,SAAS;MACb,KAAKA,SAAS,IAAIZ,mBAAmB,EAAE;QACrC,IAAIY,SAAS,CAACrB,MAAM,CAAC5B,MAAM,IAAIoC,OAAO,EAAE;UACtC,OAAOa,SAAS;QACxB;MACA;MACI,MAAM,IAAItD,SAAS,CAAC,kCAAkC,IAAI2B,EAAE,CAAC/C,IAAI,IAAI,SAAS,CAAC,GAAG,GAAG,GAAGwD,eAAe,CAACH,MAAM,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;IACjI;;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BE,SAASsB,IAAIA,CAAC5B,EAAE,EAAEC,SAAS,EAAEC,OAAO,EAAE;MACpC,OAAOH,aAAa,CAACC,EAAE,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAAC2B,cAAc;IAC/D;;IAEA;;;;;;IAME,SAASC,OAAOA,CAACpC,KAAK,EAAE3B,QAAQ,EAAE;MACpC;MACI,MAAMC,IAAI,GAAGF,QAAQ,CAACC,QAAQ,CAAC;MAC/B,IAAIC,IAAI,CAACd,IAAI,CAACwC,KAAK,CAAC,EAAE;QACpB,OAAOA,KAAK;MAClB;MACI,MAAMqC,WAAW,GAAG/D,IAAI,CAACkB,aAAa;MACtC,IAAI6C,WAAW,CAACrD,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIsD,KAAK,CAAC,8BAA8B,GAAGjE,QAAQ,GAAG,WAAW,CAAC;MAC9E;MACI,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,WAAW,CAACrD,MAAM,EAAEI,CAAC,EAAE,EAAE;QAC3C,MAAMmD,QAAQ,GAAGnE,QAAQ,CAACiE,WAAW,CAACjD,CAAC,CAAC,CAACoD,IAAI,CAAC;QAC9C,IAAID,QAAQ,CAAC/E,IAAI,CAACwC,KAAK,CAAC,EAAE;UACxB,OAAOqC,WAAW,CAACjD,CAAC,CAAC,CAACgD,OAAO,CAACpC,KAAK,CAAC;QAC5C;MACA;MACI,MAAM,IAAIsC,KAAK,CAAC,iBAAiB,GAAGtC,KAAK,GAAG,MAAM,GAAG3B,QAAQ,CAAC;IAClE;;IAEA;;;;;;IAME,SAAS0C,eAAeA,CAACH,MAAM,EAAE;MAC/B,IAAI6B,SAAS,GAAG1D,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKhC,SAAS,GAAGgC,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;MACvF,OAAO6B,MAAM,CAAC8B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpF,IAAI,CAAC,CAACoD,IAAI,CAAC8B,SAAS,CAAC;IAClD;;IAEA;;;;;IAKE,SAASG,UAAUA,CAACC,KAAK,EAAE;MACzB,MAAMlB,SAAS,GAAGkB,KAAK,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;MAC5C,MAAMjE,KAAK,GAAG,CAAC8C,SAAS,GAAGkB,KAAK,GAAGA,KAAK,CAAC7D,MAAM,GAAG,CAAC,GAAG6D,KAAK,CAACnD,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK;MAC5E,MAAMqD,QAAQ,GAAGlE,KAAK,CAACmE,KAAK,CAAC,GAAG,CAAC,CAACN,GAAG,CAACO,CAAC,IAAI7E,QAAQ,CAAC6E,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC;MAC9D,IAAItB,MAAM,GAAG,KAAK;MAClB,IAAIuB,SAAS,GAAGxB,SAAS,GAAG,KAAK,GAAG,EAAE;MACtC,MAAMyB,UAAU,GAAGL,QAAQ,CAACL,GAAG,CAAC,UAAUpE,IAAI,EAAE;QAC9CsD,MAAM,GAAGtD,IAAI,CAACR,KAAK,IAAI8D,MAAM;QAC7BuB,SAAS,IAAI7E,IAAI,CAACf,IAAI,GAAG,GAAG;QAC5B,OAAO;UACLA,IAAI,EAAEe,IAAI,CAACf,IAAI;UACf8F,SAAS,EAAE/E,IAAI,CAACY,KAAK;UACrB1B,IAAI,EAAEc,IAAI,CAACd,IAAI;UACfM,KAAK,EAAEQ,IAAI,CAACR,KAAK;UACjBwF,UAAU,EAAE,IAAI;UAChBC,eAAe,EAAE,CAAC;QAC1B,CAAO;MACP,CAAK,CAAC;MACF,OAAO;QACL1E,KAAK,EAAEuE,UAAU;QACjB7F,IAAI,EAAE4F,SAAS,CAACzD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC;QACMkC,MAAM;QACN4B,aAAa,EAAE,KAAK;QACpB7B;MACN,CAAK;IACL;;IAEA;;;;;;IAME,SAAS8B,WAAWA,CAACZ,KAAK,EAAE;MAC1B,MAAMa,SAAS,GAAGb,KAAK,CAAChE,KAAK,CAAC6D,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACpG,IAAI,CAAC;MAC9C,MAAMqG,mBAAmB,GAAGC,oBAAoB,CAACH,SAAS,CAAC;MAC3D,IAAI9B,MAAM,GAAGiB,KAAK,CAACjB,MAAM;MACzB,IAAIkC,OAAO,GAAGjB,KAAK,CAACtF,IAAI;MACxB,MAAMwG,gBAAgB,GAAGH,mBAAmB,CAAClB,GAAG,CAAC,UAAUY,UAAU,EAAE;QACrE,MAAMhF,IAAI,GAAGF,QAAQ,CAACkF,UAAU,CAACd,IAAI,CAAC;QACtCZ,MAAM,GAAGtD,IAAI,CAACR,KAAK,IAAI8D,MAAM;QAC7BkC,OAAO,IAAI,GAAG,GAAGR,UAAU,CAACd,IAAI;QAChC,OAAO;UACLjF,IAAI,EAAE+F,UAAU,CAACd,IAAI;UACrBa,SAAS,EAAE/E,IAAI,CAACY,KAAK;UACrB1B,IAAI,EAAEc,IAAI,CAACd,IAAI;UACfM,KAAK,EAAEQ,IAAI,CAACR,KAAK;UACjBwF,UAAU;UACVC,eAAe,EAAED,UAAU,CAACpE;QACpC,CAAO;MACP,CAAK,CAAC;MACF,OAAO;QACLL,KAAK,EAAEgE,KAAK,CAAChE,KAAK,CAACc,MAAM,CAACoE,gBAAgB,CAAC;QAC3CxG,IAAI,EAAEuG,OAAO;QACblC,MAAM;QACN4B,aAAa,EAAEO,gBAAgB,CAAC/E,MAAM,GAAG,CAAC;QAC1C2C,SAAS,EAAEkB,KAAK,CAAClB;MACvB,CAAK;IACL;;IAEA;;;;;;;IAOE,SAASG,YAAYA,CAACe,KAAK,EAAE;MAC3B,IAAI,CAACA,KAAK,CAACmB,OAAO,EAAE;QAClBnB,KAAK,CAACmB,OAAO,GAAG,IAAIC,GAAG,EAAE;QACzBpB,KAAK,CAAChE,KAAK,CAACqF,OAAO,CAAC5F,IAAI,IAAIuE,KAAK,CAACmB,OAAO,CAACG,GAAG,CAAC7F,IAAI,CAACf,IAAI,CAAC,CAAC;MAC/D;MACI,OAAOsF,KAAK,CAACmB,OAAO;IACxB;;IAEA;;;;;;;IAOE,SAASnD,cAAcA,CAACuD,YAAY,EAAE;MACpC,MAAMxD,MAAM,GAAG,EAAE;MACjB,IAAI,OAAOwD,YAAY,KAAK,QAAQ,EAAE;QACpC,MAAM,IAAIzF,SAAS,CAAC,4BAA4B,CAAC;MACvD;MACI,MAAM4B,SAAS,GAAG6D,YAAY,CAAClB,IAAI,EAAE;MACrC,IAAI3C,SAAS,KAAK,EAAE,EAAE;QACpB,OAAOK,MAAM;MACnB;MACI,MAAMyD,SAAS,GAAG9D,SAAS,CAACyC,KAAK,CAAC,GAAG,CAAC;MACtC,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,SAAS,CAACrF,MAAM,EAAE,EAAEI,CAAC,EAAE;QACzC,MAAMkF,WAAW,GAAG1B,UAAU,CAACyB,SAAS,CAACjF,CAAC,CAAC,CAAC8D,IAAI,EAAE,CAAC;QACnD,IAAIoB,WAAW,CAAC3C,SAAS,IAAIvC,CAAC,KAAKiF,SAAS,CAACrF,MAAM,GAAG,CAAC,EAAE;UACvD,MAAM,IAAIuF,WAAW,CAAC,6BAA6B,GAAGF,SAAS,CAACjF,CAAC,CAAC,GAAG,KAAK,GAAG,qCAAqC,CAAC;QAC3H;QACA;QACM,IAAIkF,WAAW,CAACzF,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;UAClC,OAAO,IAAI;QACnB;QACM4B,MAAM,CAACtB,IAAI,CAACgF,WAAW,CAAC;MAC9B;MACI,OAAO1D,MAAM;IACjB;;IAEA;;;;;IAKE,SAAS4D,YAAYA,CAAC5D,MAAM,EAAE;MAC5B,MAAMiC,KAAK,GAAG4B,IAAI,CAAC7D,MAAM,CAAC;MAC1B,OAAOiC,KAAK,GAAGA,KAAK,CAAClB,SAAS,GAAG,KAAK;IAC1C;;IAEA;;;;;;IAME,SAAS+C,WAAWA,CAAC7B,KAAK,EAAE;MAC1B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAChE,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;QAC5C;QACM,OAAOpC,EAAE;MACf,CAAK,MAAM,IAAIiG,KAAK,CAAChE,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;QACnC,OAAOZ,QAAQ,CAACyE,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAACtB,IAAI,CAAC,CAACC,IAAI;MAC/C,CAAK,MAAM,IAAIqF,KAAK,CAAChE,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;QACnC,MAAM2F,KAAK,GAAGvG,QAAQ,CAACyE,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAACtB,IAAI,CAAC,CAACC,IAAI;QAChD,MAAMoH,KAAK,GAAGxG,QAAQ,CAACyE,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAACtB,IAAI,CAAC,CAACC,IAAI;QAChD,OAAO,SAASqH,EAAEA,CAAC1H,CAAC,EAAE;UACpB,OAAOwH,KAAK,CAACxH,CAAC,CAAC,IAAIyH,KAAK,CAACzH,CAAC,CAAC;QACnC,CAAO;MACP,CAAK,MAAM;QACX;QACM,MAAM2H,KAAK,GAAGjC,KAAK,CAAChE,KAAK,CAAC6D,GAAG,CAAC,UAAUpE,IAAI,EAAE;UAC5C,OAAOF,QAAQ,CAACE,IAAI,CAACf,IAAI,CAAC,CAACC,IAAI;QACvC,CAAO,CAAC;QACF,OAAO,SAASqH,EAAEA,CAAC1H,CAAC,EAAE;UACpB,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC9F,MAAM,EAAEI,CAAC,EAAE,EAAE;YACrC,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,CAACjC,CAAC,CAAC,EAAE;cACf,OAAO,IAAI;YACvB;UACA;UACQ,OAAO,KAAK;QACpB,CAAO;MACP;IACA;;IAEA;;;;;IAKE,SAAS4H,YAAYA,CAACnE,MAAM,EAAE;MAC5B,IAAIkE,KAAK,EAAEH,KAAK,EAAEC,KAAK;MACvB,IAAIJ,YAAY,CAAC5D,MAAM,CAAC,EAAE;QAC9B;QACMkE,KAAK,GAAGE,OAAO,CAACpE,MAAM,CAAC,CAAC8B,GAAG,CAACgC,WAAW,CAAC;QACxC,MAAMO,QAAQ,GAAGH,KAAK,CAAC9F,MAAM;QAC7B,MAAMkG,QAAQ,GAAGR,WAAW,CAACD,IAAI,CAAC7D,MAAM,CAAC,CAAC;QAC1C,MAAMuE,aAAa,GAAG,SAAAA,CAAUC,IAAI,EAAE;UACpC,KAAK,IAAIhG,CAAC,GAAG6F,QAAQ,EAAE7F,CAAC,GAAGgG,IAAI,CAACpG,MAAM,EAAEI,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC8F,QAAQ,CAACE,IAAI,CAAChG,CAAC,CAAC,CAAC,EAAE;cACtB,OAAO,KAAK;YACxB;UACA;UACQ,OAAO,IAAI;QACnB,CAAO;QACD,OAAO,SAASiG,QAAQA,CAACD,IAAI,EAAE;UAC7B,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC9F,MAAM,EAAEI,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC0F,KAAK,CAAC1F,CAAC,CAAC,CAACgG,IAAI,CAAChG,CAAC,CAAC,CAAC,EAAE;cACtB,OAAO,KAAK;YACxB;UACA;UACQ,OAAO+F,aAAa,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACpG,MAAM,IAAIiG,QAAQ,GAAG,CAAC;QACjE,CAAO;MACP,CAAK,MAAM;QACX;QACM,IAAIrE,MAAM,CAAC5B,MAAM,KAAK,CAAC,EAAE;UACvB,OAAO,SAASqG,QAAQA,CAACD,IAAI,EAAE;YAC7B,OAAOA,IAAI,CAACpG,MAAM,KAAK,CAAC;UAClC,CAAS;QACT,CAAO,MAAM,IAAI4B,MAAM,CAAC5B,MAAM,KAAK,CAAC,EAAE;UAC9B2F,KAAK,GAAGD,WAAW,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9B,OAAO,SAASyE,QAAQA,CAACD,IAAI,EAAE;YAC7B,OAAOT,KAAK,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAACpG,MAAM,KAAK,CAAC;UACpD,CAAS;QACT,CAAO,MAAM,IAAI4B,MAAM,CAAC5B,MAAM,KAAK,CAAC,EAAE;UAC9B2F,KAAK,GAAGD,WAAW,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9BgE,KAAK,GAAGF,WAAW,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9B,OAAO,SAASyE,QAAQA,CAACD,IAAI,EAAE;YAC7B,OAAOT,KAAK,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIR,KAAK,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAACpG,MAAM,KAAK,CAAC;UACtE,CAAS;QACT,CAAO,MAAM;UACb;UACQ8F,KAAK,GAAGlE,MAAM,CAAC8B,GAAG,CAACgC,WAAW,CAAC;UAC/B,OAAO,SAASW,QAAQA,CAACD,IAAI,EAAE;YAC7B,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC9F,MAAM,EAAEI,CAAC,EAAE,EAAE;cACrC,IAAI,CAAC0F,KAAK,CAAC1F,CAAC,CAAC,CAACgG,IAAI,CAAChG,CAAC,CAAC,CAAC,EAAE;gBACtB,OAAO,KAAK;cAC1B;YACA;YACU,OAAOgG,IAAI,CAACpG,MAAM,KAAK8F,KAAK,CAAC9F,MAAM;UAC7C,CAAS;QACT;MACA;IACA;;IAEA;;;;;;;;IAQE,SAAS0C,eAAeA,CAACd,MAAM,EAAE1B,KAAK,EAAE;MACtC,OAAOA,KAAK,GAAG0B,MAAM,CAAC5B,MAAM,GAAG4B,MAAM,CAAC1B,KAAK,CAAC,GAAGsF,YAAY,CAAC5D,MAAM,CAAC,GAAG6D,IAAI,CAAC7D,MAAM,CAAC,GAAG,IAAI;IAC7F;;IAEA;;;;;;IAME,SAAS0E,iBAAiBA,CAAC1E,MAAM,EAAE1B,KAAK,EAAE;MACxC,MAAM2D,KAAK,GAAGnB,eAAe,CAACd,MAAM,EAAE1B,KAAK,CAAC;MAC5C,IAAI,CAAC2D,KAAK,EAAE;QACV,OAAO,IAAIoB,GAAG,EAAE;MACtB;MACI,OAAOnC,YAAY,CAACe,KAAK,CAAC;IAC9B;;IAEA;;;;;IAKE,SAAS0C,WAAWA,CAACjH,IAAI,EAAE;MACzB,OAAOA,IAAI,CAACgF,UAAU,KAAK,IAAI,IAAIhF,IAAI,CAACgF,UAAU,KAAKvG,SAAS;IACpE;;IAEA;;;;;;;IAOE,SAASyI,mBAAmBA,CAACxE,UAAU,EAAE9B,KAAK,EAAE;MAC9C,MAAM8E,OAAO,GAAG,IAAIC,GAAG,EAAE;MACzBjD,UAAU,CAACkD,OAAO,CAAC3D,SAAS,IAAI;QAC9B,MAAMkF,QAAQ,GAAGH,iBAAiB,CAAC/E,SAAS,CAACK,MAAM,EAAE1B,KAAK,CAAC;QAC3D,IAAI3B,IAAI;QACR,KAAKA,IAAI,IAAIkI,QAAQ,EAAE;UACrBzB,OAAO,CAACG,GAAG,CAAC5G,IAAI,CAAC;QACzB;MACA,CAAK,CAAC;MACF,OAAOyG,OAAO,CAAC3E,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG5B,KAAK,CAAC+E,IAAI,CAACwB,OAAO,CAAC;IAC7D;;IAEA;;;;;;;;IAQE,SAAS0B,WAAWA,CAACnI,IAAI,EAAE6H,IAAI,EAAEpE,UAAU,EAAE;MAC3C,IAAI2E,GAAG,EAAEC,QAAQ;MACjB,MAAMC,KAAK,GAAGtI,IAAI,IAAI,SAAS;;MAEnC;MACI,IAAIuI,kBAAkB,GAAG9E,UAAU;MACnC,IAAI9B,KAAK;MACT,KAAKA,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGkG,IAAI,CAACpG,MAAM,EAAEE,KAAK,EAAE,EAAE;QAC5C,MAAM6G,gBAAgB,GAAG,EAAE;QAC3BD,kBAAkB,CAAC5B,OAAO,CAAC3D,SAAS,IAAI;UACtC,MAAMsC,KAAK,GAAGnB,eAAe,CAACnB,SAAS,CAACK,MAAM,EAAE1B,KAAK,CAAC;UACtD,MAAM1B,IAAI,GAAGkH,WAAW,CAAC7B,KAAK,CAAC;UAC/B,IAAI,CAAC3D,KAAK,GAAGqB,SAAS,CAACK,MAAM,CAAC5B,MAAM,IAAIwF,YAAY,CAACjE,SAAS,CAACK,MAAM,CAAC,KAAKpD,IAAI,CAAC4H,IAAI,CAAClG,KAAK,CAAC,CAAC,EAAE;YAC5F6G,gBAAgB,CAACzG,IAAI,CAACiB,SAAS,CAAC;UAC1C;QACA,CAAO,CAAC;QACF,IAAIwF,gBAAgB,CAAC/G,MAAM,KAAK,CAAC,EAAE;UACzC;UACQ4G,QAAQ,GAAGJ,mBAAmB,CAACM,kBAAkB,EAAE5G,KAAK,CAAC;UACzD,IAAI0G,QAAQ,CAAC5G,MAAM,GAAG,CAAC,EAAE;YACvB,MAAMgH,WAAW,GAAGjG,aAAa,CAACqF,IAAI,CAAClG,KAAK,CAAC,CAAC;YAC9CyG,GAAG,GAAG,IAAIhH,SAAS,CAAC,0CAA0C,GAAGkH,KAAK,GAAG,cAAc,GAAGD,QAAQ,CAACjF,IAAI,CAAC,MAAM,CAAC,GAAG,YAAY,GAAGqF,WAAW,CAACrF,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,GAAGzB,KAAK,GAAG,GAAG,CAAC;YACrLyG,GAAG,CAACM,IAAI,GAAG;cACTC,QAAQ,EAAE,WAAW;cACrB5F,EAAE,EAAEuF,KAAK;cACT3G,KAAK;cACLiH,MAAM,EAAEH,WAAW;cACnBJ;YACZ,CAAW;YACD,OAAOD,GAAG;UACpB;QACA,CAAO,MAAM;UACLG,kBAAkB,GAAGC,gBAAgB;QAC7C;MACA;;MAEA;MACI,MAAMK,OAAO,GAAGN,kBAAkB,CAACpD,GAAG,CAAC,UAAUnC,SAAS,EAAE;QAC1D,OAAOiE,YAAY,CAACjE,SAAS,CAACK,MAAM,CAAC,GAAGyF,QAAQ,GAAG9F,SAAS,CAACK,MAAM,CAAC5B,MAAM;MAChF,CAAK,CAAC;MACF,IAAIoG,IAAI,CAACpG,MAAM,GAAGsH,IAAI,CAACC,GAAG,CAACC,KAAK,CAAC,IAAI,EAAEJ,OAAO,CAAC,EAAE;QAC/CR,QAAQ,GAAGJ,mBAAmB,CAACM,kBAAkB,EAAE5G,KAAK,CAAC;QACzDyG,GAAG,GAAG,IAAIhH,SAAS,CAAC,gCAAgC,GAAGkH,KAAK,GAAG,cAAc,GAAGD,QAAQ,CAACjF,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,GAAGyE,IAAI,CAACpG,MAAM,GAAG,GAAG,CAAC;QACxI2G,GAAG,CAACM,IAAI,GAAG;UACTC,QAAQ,EAAE,YAAY;UACtB5F,EAAE,EAAEuF,KAAK;UACT3G,KAAK,EAAEkG,IAAI,CAACpG,MAAM;UAClB4G;QACR,CAAO;QACD,OAAOD,GAAG;MAChB;;MAEA;MACI,MAAMc,SAAS,GAAGH,IAAI,CAACI,GAAG,CAACF,KAAK,CAAC,IAAI,EAAEJ,OAAO,CAAC;MAC/C,IAAIhB,IAAI,CAACpG,MAAM,GAAGyH,SAAS,EAAE;QAC3Bd,GAAG,GAAG,IAAIhH,SAAS,CAAC,iCAAiC,GAAGkH,KAAK,GAAG,cAAc,GAAGY,SAAS,GAAG,YAAY,GAAGrB,IAAI,CAACpG,MAAM,GAAG,GAAG,CAAC;QAC9H2G,GAAG,CAACM,IAAI,GAAG;UACTC,QAAQ,EAAE,aAAa;UACvB5F,EAAE,EAAEuF,KAAK;UACT3G,KAAK,EAAEkG,IAAI,CAACpG,MAAM;UAClB2H,cAAc,EAAEF;QACxB,CAAO;QACD,OAAOd,GAAG;MAChB;;MAEA;MACI,MAAMiB,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,IAAI,CAACpG,MAAM,EAAE,EAAEI,CAAC,EAAE;QACpCwH,QAAQ,CAACtH,IAAI,CAACS,aAAa,CAACqF,IAAI,CAAChG,CAAC,CAAC,CAAC,CAACuB,IAAI,CAAC,GAAG,CAAC,CAAC;MACrD;MACIgF,GAAG,GAAG,IAAIhH,SAAS,CAAC,qBAAqB,GAAGiI,QAAQ,CAACjG,IAAI,CAAC,IAAI,CAAC,GAAG,2DAA2D,GAAGkF,KAAK,GAAG,GAAG,CAAC;MAC5IF,GAAG,CAACM,IAAI,GAAG;QACTC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAES;MACd,CAAK;MACD,OAAOjB,GAAG;IACd;;IAEA;;;;;IAKE,SAASkB,kBAAkBA,CAAChE,KAAK,EAAE;MACjC,IAAI0D,GAAG,GAAGvI,QAAQ,CAACgB,MAAM,GAAG,CAAC;MAC7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,KAAK,CAAChE,KAAK,CAACG,MAAM,EAAEI,CAAC,EAAE,EAAE;QAC3C,IAAImG,WAAW,CAAC1C,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAAC,EAAE;UAC/BmH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAE1D,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAACiE,SAAS,CAAC;QACrD;MACA;MACI,OAAOkD,GAAG;IACd;;IAEA;;;;;;IAME,SAASO,wBAAwBA,CAACjE,KAAK,EAAE;MACvC,IAAI0D,GAAG,GAAGtI,YAAY,GAAG,CAAC;MAC1B,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,KAAK,CAAChE,KAAK,CAACG,MAAM,EAAEI,CAAC,EAAE,EAAE;QAC3C,IAAI,CAACmG,WAAW,CAAC1C,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAAC,EAAE;UAChCmH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAE1D,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAACmE,eAAe,CAAC;QAC3D;MACA;MACI,OAAOgD,GAAG;IACd;;IAEA;;;;;;;;IAQE,SAASQ,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;MACzC;MACA;MACI,IAAID,MAAM,CAACpF,MAAM,EAAE;QACjB,IAAI,CAACqF,MAAM,CAACrF,MAAM,EAAE;UAClB,OAAO,CAAC;QAChB;MACA,CAAK,MAAM,IAAIqF,MAAM,CAACrF,MAAM,EAAE;QACxB,OAAO,CAAC,CAAC;MACf;;MAEA;MACI,IAAIoF,MAAM,CAACrF,SAAS,EAAE;QACpB,IAAI,CAACsF,MAAM,CAACtF,SAAS,EAAE;UACrB,OAAO,CAAC;QAChB;MACA,CAAK,MAAM,IAAIsF,MAAM,CAACtF,SAAS,EAAE;QAC3B,OAAO,CAAC,CAAC;MACf;;MAEA;MACI,IAAIqF,MAAM,CAACxD,aAAa,EAAE;QACxB,IAAI,CAACyD,MAAM,CAACzD,aAAa,EAAE;UACzB,OAAO,CAAC;QAChB;MACA,CAAK,MAAM,IAAIyD,MAAM,CAACzD,aAAa,EAAE;QAC/B,OAAO,CAAC,CAAC;MACf;;MAEA;MACI,MAAM0D,QAAQ,GAAGL,kBAAkB,CAACG,MAAM,CAAC,GAAGH,kBAAkB,CAACI,MAAM,CAAC;MACxE,IAAIC,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,CAAC,CAAC;MACf;MACI,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,CAAC;MACd;;MAEA;MACI,MAAMC,QAAQ,GAAGL,wBAAwB,CAACE,MAAM,CAAC,GAAGF,wBAAwB,CAACG,MAAM,CAAC;MACpF,IAAIE,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,CAAC,CAAC;MACf;MACI,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,CAAC;MACd;;MAEA;MACI,OAAO,CAAC;IACZ;;IAEA;;;;;;;;IAQE,SAASC,iBAAiBA,CAACC,UAAU,EAAEC,UAAU,EAAE;MACjD,MAAMC,KAAK,GAAGF,UAAU,CAACzG,MAAM;MAC/B,MAAM4G,KAAK,GAAGF,UAAU,CAAC1G,MAAM;MAC/B,MAAM6G,KAAK,GAAGhD,IAAI,CAAC8C,KAAK,CAAC;MACzB,MAAMG,KAAK,GAAGjD,IAAI,CAAC+C,KAAK,CAAC;MACzB,MAAMG,QAAQ,GAAGnD,YAAY,CAAC+C,KAAK,CAAC;MACpC,MAAMK,QAAQ,GAAGpD,YAAY,CAACgD,KAAK,CAAC;MACxC;MACA;MACI,IAAIG,QAAQ,IAAIF,KAAK,CAAC7F,MAAM,EAAE;QAC5B,IAAI,CAACgG,QAAQ,IAAI,CAACF,KAAK,CAAC9F,MAAM,EAAE;UAC9B,OAAO,CAAC;QAChB;MACA,CAAK,MAAM,IAAIgG,QAAQ,IAAIF,KAAK,CAAC9F,MAAM,EAAE;QACnC,OAAO,CAAC,CAAC;MACf;;MAEA;MACI,IAAIiG,IAAI,GAAG,CAAC;MACZ,IAAIC,KAAK,GAAG,CAAC;MACb,IAAIC,GAAG;MACP,KAAKA,GAAG,IAAIR,KAAK,EAAE;QACjB,IAAIQ,GAAG,CAACnG,MAAM,EAAE,EAAEiG,IAAI;QACtB,IAAIE,GAAG,CAACvE,aAAa,EAAE,EAAEsE,KAAK;MACpC;MACI,IAAIE,IAAI,GAAG,CAAC;MACZ,IAAIC,KAAK,GAAG,CAAC;MACb,KAAKF,GAAG,IAAIP,KAAK,EAAE;QACjB,IAAIO,GAAG,CAACnG,MAAM,EAAE,EAAEoG,IAAI;QACtB,IAAID,GAAG,CAACvE,aAAa,EAAE,EAAEyE,KAAK;MACpC;MACI,IAAIJ,IAAI,KAAKG,IAAI,EAAE;QACjB,OAAOH,IAAI,GAAGG,IAAI;MACxB;;MAEA;MACI,IAAIL,QAAQ,IAAIF,KAAK,CAACjE,aAAa,EAAE;QACnC,IAAI,CAACoE,QAAQ,IAAI,CAACF,KAAK,CAAClE,aAAa,EAAE;UACrC,OAAO,CAAC;QAChB;MACA,CAAK,MAAM,IAAIoE,QAAQ,IAAIF,KAAK,CAAClE,aAAa,EAAE;QAC1C,OAAO,CAAC,CAAC;MACf;;MAEA;MACI,IAAIsE,KAAK,KAAKG,KAAK,EAAE;QACnB,OAAOH,KAAK,GAAGG,KAAK;MAC1B;;MAEA;MACI,IAAIN,QAAQ,EAAE;QACZ,IAAI,CAACC,QAAQ,EAAE;UACb,OAAO,CAAC;QAChB;MACA,CAAK,MAAM,IAAIA,QAAQ,EAAE;QACnB,OAAO,CAAC,CAAC;MACf;;MAEA;MACI,MAAMM,eAAe,GAAG,CAACX,KAAK,CAACvI,MAAM,GAAGwI,KAAK,CAACxI,MAAM,KAAK2I,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3E,IAAIO,eAAe,KAAK,CAAC,EAAE;QACzB,OAAOA,eAAe;MAC5B;;MAEA;MACA;MACA;MACA;MACI,MAAMC,WAAW,GAAG,EAAE;MACtB,IAAIC,EAAE,GAAG,CAAC;MACV,KAAK,IAAIhJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmI,KAAK,CAACvI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACrC,MAAMiJ,cAAc,GAAGtB,aAAa,CAACQ,KAAK,CAACnI,CAAC,CAAC,EAAEoI,KAAK,CAACpI,CAAC,CAAC,CAAC;QACxD+I,WAAW,CAAC7I,IAAI,CAAC+I,cAAc,CAAC;QAChCD,EAAE,IAAIC,cAAc;MAC1B;MACI,IAAID,EAAE,KAAK,CAAC,EAAE;QACZ,OAAOA,EAAE;MACf;;MAEA;MACA;MACA;MACA;MACI,IAAIE,CAAC;MACL,KAAKA,CAAC,IAAIH,WAAW,EAAE;QACrB,IAAIG,CAAC,KAAK,CAAC,EAAE;UACX,OAAOA,CAAC;QAChB;MACA;;MAEA;MACI,OAAO,CAAC;IACZ;;IAEA;;;;;;;;IAQE,SAASzE,oBAAoBA,CAACH,SAAS,EAAE;MACvC,IAAIA,SAAS,CAAC1E,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,EAAE;MACf;MACI,MAAMH,KAAK,GAAG6E,SAAS,CAAChB,GAAG,CAACtE,QAAQ,CAAC;MACrC,IAAIsF,SAAS,CAAC1E,MAAM,GAAG,CAAC,EAAE;QACxBH,KAAK,CAAC0J,IAAI,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACtJ,KAAK,GAAGuJ,EAAE,CAACvJ,KAAK,CAAC;MACjD;MACI,IAAIe,OAAO,GAAGpB,KAAK,CAAC,CAAC,CAAC,CAACW,aAAa;MACpC,IAAIkE,SAAS,CAAC1E,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAOiB,OAAO;MACpB;MACIA,OAAO,GAAGA,OAAO,CAACN,MAAM,CAAC,EAAE,CAAC,CAAC;MACjC;MACA;MACI,MAAM+I,UAAU,GAAG,IAAIzE,GAAG,CAACP,SAAS,CAAC;MACrC,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACG,MAAM,EAAE,EAAEI,CAAC,EAAE;QACrC,IAAIuJ,QAAQ;QACZ,KAAKA,QAAQ,IAAI9J,KAAK,CAACO,CAAC,CAAC,CAACI,aAAa,EAAE;UACvC,IAAI,CAACkJ,UAAU,CAACrJ,GAAG,CAACsJ,QAAQ,CAACnG,IAAI,CAAC,EAAE;YAClCvC,OAAO,CAACX,IAAI,CAACqJ,QAAQ,CAAC;YACtBD,UAAU,CAACvE,GAAG,CAACwE,QAAQ,CAACnG,IAAI,CAAC;UACvC;QACA;MACA;MACI,OAAOvC,OAAO;IAClB;;IAEA;;;;;;;;IAQE,SAAS2I,wBAAwBA,CAAChI,MAAM,EAAEN,EAAE,EAAE;MAC5C,IAAIuI,SAAS,GAAGvI,EAAE;;MAEtB;;MAEI,IAAIM,MAAM,CAACmB,IAAI,CAACY,CAAC,IAAIA,CAAC,CAACa,aAAa,CAAC,EAAE;QACrC,MAAM7B,SAAS,GAAG6C,YAAY,CAAC5D,MAAM,CAAC;QACtC,MAAMkI,mBAAmB,GAAGlI,MAAM,CAAC8B,GAAG,CAACqG,oBAAoB,CAAC;QAC5DF,SAAS,GAAG,SAASG,WAAWA,CAAA,EAAG;UACjC,MAAM5D,IAAI,GAAG,EAAE;UACf,MAAMX,IAAI,GAAG9C,SAAS,GAAG5C,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAACC,MAAM;UAChE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqF,IAAI,EAAErF,CAAC,EAAE,EAAE;YAC7BgG,IAAI,CAAChG,CAAC,CAAC,GAAG0J,mBAAmB,CAAC1J,CAAC,CAAC,CAACL,SAAS,CAACK,CAAC,CAAC,CAAC;UACxD;UACQ,IAAIuC,SAAS,EAAE;YACbyD,IAAI,CAACX,IAAI,CAAC,GAAG1F,SAAS,CAAC0F,IAAI,CAAC,CAAC/B,GAAG,CAACoG,mBAAmB,CAACrE,IAAI,CAAC,CAAC;UACrE;UACQ,OAAOnE,EAAE,CAACkG,KAAK,CAAC,IAAI,EAAEpB,IAAI,CAAC;QACnC,CAAO;MACP;MACI,IAAI6D,YAAY,GAAGJ,SAAS;MAC5B,IAAIrE,YAAY,CAAC5D,MAAM,CAAC,EAAE;QACxB,MAAMsI,MAAM,GAAGtI,MAAM,CAAC5B,MAAM,GAAG,CAAC;QAChCiK,YAAY,GAAG,SAASE,oBAAoBA,CAAA,EAAG;UAC7C,OAAON,SAAS,CAACrC,KAAK,CAAC,IAAI,EAAE9G,KAAK,CAACX,SAAS,EAAE,CAAC,EAAEmK,MAAM,CAAC,CAACvJ,MAAM,CAAC,CAACD,KAAK,CAACX,SAAS,EAAEmK,MAAM,CAAC,CAAC,CAAC,CAAC;QACpG,CAAO;MACP;MACI,OAAOD,YAAY;IACvB;;IAEA;;;;;;IAME,SAASF,oBAAoBA,CAAClG,KAAK,EAAE;MACnC,IAAI8B,KAAK,EAAEC,KAAK,EAAEwE,WAAW,EAAEC,WAAW;MAC1C,MAAMvE,KAAK,GAAG,EAAE;MAChB,MAAMzC,WAAW,GAAG,EAAE;MACtBQ,KAAK,CAAChE,KAAK,CAACqF,OAAO,CAAC,UAAU5F,IAAI,EAAE;QAClC,IAAIA,IAAI,CAACgF,UAAU,EAAE;UACnBwB,KAAK,CAACxF,IAAI,CAAClB,QAAQ,CAACE,IAAI,CAACgF,UAAU,CAACd,IAAI,CAAC,CAAChF,IAAI,CAAC;UAC/C6E,WAAW,CAAC/C,IAAI,CAAChB,IAAI,CAACgF,UAAU,CAAClB,OAAO,CAAC;QACjD;MACA,CAAK,CAAC;;MAEN;MACI,QAAQC,WAAW,CAACrD,MAAM;QACxB,KAAK,CAAC;UACJ,OAAO,SAASsK,UAAUA,CAACC,GAAG,EAAE;YAC9B,OAAOA,GAAG;UACpB,CAAS;QACH,KAAK,CAAC;UACJ5E,KAAK,GAAGG,KAAK,CAAC,CAAC,CAAC;UAChBsE,WAAW,GAAG/G,WAAW,CAAC,CAAC,CAAC;UAC5B,OAAO,SAASiH,UAAUA,CAACC,GAAG,EAAE;YAC9B,IAAI5E,KAAK,CAAC4E,GAAG,CAAC,EAAE;cACd,OAAOH,WAAW,CAACG,GAAG,CAAC;YACnC;YACU,OAAOA,GAAG;UACpB,CAAS;QACH,KAAK,CAAC;UACJ5E,KAAK,GAAGG,KAAK,CAAC,CAAC,CAAC;UAChBF,KAAK,GAAGE,KAAK,CAAC,CAAC,CAAC;UAChBsE,WAAW,GAAG/G,WAAW,CAAC,CAAC,CAAC;UAC5BgH,WAAW,GAAGhH,WAAW,CAAC,CAAC,CAAC;UAC5B,OAAO,SAASiH,UAAUA,CAACC,GAAG,EAAE;YAC9B,IAAI5E,KAAK,CAAC4E,GAAG,CAAC,EAAE;cACd,OAAOH,WAAW,CAACG,GAAG,CAAC;YACnC;YACU,IAAI3E,KAAK,CAAC2E,GAAG,CAAC,EAAE;cACd,OAAOF,WAAW,CAACE,GAAG,CAAC;YACnC;YACU,OAAOA,GAAG;UACpB,CAAS;QACH;UACE,OAAO,SAASD,UAAUA,CAACC,GAAG,EAAE;YAC9B,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,WAAW,CAACrD,MAAM,EAAEI,CAAC,EAAE,EAAE;cAC3C,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,CAACmK,GAAG,CAAC,EAAE;gBACjB,OAAOlH,WAAW,CAACjD,CAAC,CAAC,CAACmK,GAAG,CAAC;cACxC;YACA;YACU,OAAOA,GAAG;UACpB,CAAS;MACT;IACA;;IAEA;;;;;;;;;;;;;;;;;IAiBE,SAASC,WAAWA,CAAC5I,MAAM,EAAE;MAC3B,SAAS6I,YAAYA,CAAC7I,MAAM,EAAE1B,KAAK,EAAEwK,WAAW,EAAE;QAChD,IAAIxK,KAAK,GAAG0B,MAAM,CAAC5B,MAAM,EAAE;UACzB,MAAM6D,KAAK,GAAGjC,MAAM,CAAC1B,KAAK,CAAC;UAC3B,IAAIyK,eAAe,GAAG,EAAE;UACxB,IAAI9G,KAAK,CAAClB,SAAS,EAAE;YAC7B;YACA;YACU,MAAMyB,UAAU,GAAGP,KAAK,CAAChE,KAAK,CAACqB,MAAM,CAACqF,WAAW,CAAC;YAClD,IAAInC,UAAU,CAACpE,MAAM,GAAG6D,KAAK,CAAChE,KAAK,CAACG,MAAM,EAAE;cAC1C2K,eAAe,CAACrK,IAAI,CAAC;gBACnBT,KAAK,EAAEuE,UAAU;gBACjB7F,IAAI,EAAE,KAAK,GAAG6F,UAAU,CAACV,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACpG,IAAI,CAAC,CAACoD,IAAI,CAAC,GAAG,CAAC;gBACnDiB,MAAM,EAAEwB,UAAU,CAACrB,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAAC7F,KAAK,CAAC;gBACrC0F,aAAa,EAAE,KAAK;gBACpB7B,SAAS,EAAE;cACzB,CAAa,CAAC;YACd;YACUgI,eAAe,CAACrK,IAAI,CAACuD,KAAK,CAAC;UACrC,CAAS,MAAM;YACf;YACU8G,eAAe,GAAG9G,KAAK,CAAChE,KAAK,CAAC6D,GAAG,CAAC,UAAUpE,IAAI,EAAE;cAChD,OAAO;gBACLO,KAAK,EAAE,CAACP,IAAI,CAAC;gBACbf,IAAI,EAAEe,IAAI,CAACf,IAAI;gBACfqE,MAAM,EAAEtD,IAAI,CAACR,KAAK;gBAClB0F,aAAa,EAAElF,IAAI,CAACgF,UAAU;gBAC9B3B,SAAS,EAAE;cACzB,CAAa;YACb,CAAW,CAAC;UACZ;;UAEA;UACQ,OAAOiI,OAAO,CAACD,eAAe,EAAE,UAAUE,SAAS,EAAE;YACnD,OAAOJ,YAAY,CAAC7I,MAAM,EAAE1B,KAAK,GAAG,CAAC,EAAEwK,WAAW,CAAC/J,MAAM,CAAC,CAACkK,SAAS,CAAC,CAAC,CAAC;UACjF,CAAS,CAAC;QACV,CAAO,MAAM;UACb;UACQ,OAAO,CAACH,WAAW,CAAC;QAC5B;MACA;MACI,OAAOD,YAAY,CAAC7I,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IACtC;;IAEA;;;;;;IAME,SAASkJ,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;MACrC,MAAMC,EAAE,GAAG3D,IAAI,CAACI,GAAG,CAACqD,OAAO,CAAC/K,MAAM,EAAEgL,OAAO,CAAChL,MAAM,CAAC;MACnD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6K,EAAE,EAAE7K,CAAC,EAAE,EAAE;QAC3B,MAAM8K,QAAQ,GAAG5E,iBAAiB,CAACyE,OAAO,EAAE3K,CAAC,CAAC;QAC9C,MAAM+K,QAAQ,GAAG7E,iBAAiB,CAAC0E,OAAO,EAAE5K,CAAC,CAAC;QAC9C,IAAIgL,OAAO,GAAG,KAAK;QACnB,IAAI7M,IAAI;QACR,KAAKA,IAAI,IAAI4M,QAAQ,EAAE;UACrB,IAAID,QAAQ,CAAC7K,GAAG,CAAC9B,IAAI,CAAC,EAAE;YACtB6M,OAAO,GAAG,IAAI;YACd;UACV;QACA;QACM,IAAI,CAACA,OAAO,EAAE;UACZ,OAAO,KAAK;QACpB;MACA;MACI,MAAMC,IAAI,GAAGN,OAAO,CAAC/K,MAAM;MAC3B,MAAMsL,IAAI,GAAGN,OAAO,CAAChL,MAAM;MAC3B,MAAMuL,UAAU,GAAG/F,YAAY,CAACuF,OAAO,CAAC;MACxC,MAAMS,UAAU,GAAGhG,YAAY,CAACwF,OAAO,CAAC;MACxC,OAAOO,UAAU,GAAGC,UAAU,GAAGH,IAAI,KAAKC,IAAI,GAAGA,IAAI,IAAID,IAAI,GAAGG,UAAU,GAAGH,IAAI,IAAIC,IAAI,GAAGD,IAAI,KAAKC,IAAI;IAC7G;;IAEA;;;;;;;;IAQE,SAASG,gBAAgBA,CAACC,YAAY,EAAE;MACtC,OAAOA,YAAY,CAAChI,GAAG,CAACpC,EAAE,IAAI;QAC5B,IAAIqK,aAAa,CAACrK,EAAE,CAAC,EAAE;UACrB,OAAOsK,WAAW,CAACtK,EAAE,CAACsK,WAAW,CAACC,QAAQ,CAAC;QACnD;QACM,IAAIC,SAAS,CAACxK,EAAE,CAAC,EAAE;UACjB,OAAOyK,WAAW,CAACzK,EAAE,CAAC0K,OAAO,CAACC,UAAU,EAAE3K,EAAE,CAAC0K,OAAO,CAACH,QAAQ,CAAC;QACtE;QACM,OAAOvK,EAAE;MACf,CAAK,CAAC;IACN;;IAEA;;;;;;;;;;;IAWE,SAAS4K,kBAAkBA,CAACD,UAAU,EAAEP,YAAY,EAAEvJ,YAAY,EAAE;MAClE,MAAMgK,kBAAkB,GAAG,EAAE;MAC7B,IAAIC,SAAS;MACb,KAAKA,SAAS,IAAIH,UAAU,EAAE;QAC5B,IAAII,UAAU,GAAGlK,YAAY,CAACiK,SAAS,CAAC;QACxC,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;UAClC,MAAM,IAAI1M,SAAS,CAAC,0CAA0C,GAAGyM,SAAS,GAAG,GAAG,CAAC;QACzF;QACMC,UAAU,GAAGX,YAAY,CAACW,UAAU,CAAC;QACrC,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;UACpC,OAAO,KAAK;QACpB;QACMF,kBAAkB,CAAC7L,IAAI,CAAC+L,UAAU,CAAC;MACzC;MACI,OAAOF,kBAAkB;IAC7B;;IAEA;;;;;;;;;;;IAWE,SAASG,iBAAiBA,CAACZ,YAAY,EAAEvJ,YAAY,EAAEoK,IAAI,EAAE;MAC3D,MAAMC,iBAAiB,GAAGf,gBAAgB,CAACC,YAAY,CAAC;MACxD,MAAMe,UAAU,GAAG,IAAIhO,KAAK,CAAC+N,iBAAiB,CAACxM,MAAM,CAAC,CAAC0M,IAAI,CAAC,KAAK,CAAC;MAClE,IAAIC,cAAc,GAAG,IAAI;MACzB,OAAOA,cAAc,EAAE;QACrBA,cAAc,GAAG,KAAK;QACtB,IAAIC,eAAe,GAAG,IAAI;QAC1B,KAAK,IAAIxM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoM,iBAAiB,CAACxM,MAAM,EAAE,EAAEI,CAAC,EAAE;UACjD,IAAIqM,UAAU,CAACrM,CAAC,CAAC,EAAE;UACnB,MAAMkB,EAAE,GAAGkL,iBAAiB,CAACpM,CAAC,CAAC;UAC/B,IAAIuL,aAAa,CAACrK,EAAE,CAAC,EAAE;YACrBkL,iBAAiB,CAACpM,CAAC,CAAC,GAAGkB,EAAE,CAACsK,WAAW,CAACC,QAAQ,CAACU,IAAI,CAAC;YAC9D;YACUC,iBAAiB,CAACpM,CAAC,CAAC,CAACwL,WAAW,GAAGtK,EAAE,CAACsK,WAAW;YACjDa,UAAU,CAACrM,CAAC,CAAC,GAAG,IAAI;YACpBwM,eAAe,GAAG,KAAK;UACjC,CAAS,MAAM,IAAId,SAAS,CAACxK,EAAE,CAAC,EAAE;YACxB,MAAM6K,kBAAkB,GAAGD,kBAAkB,CAAC5K,EAAE,CAAC0K,OAAO,CAACC,UAAU,EAAEO,iBAAiB,EAAErK,YAAY,CAAC;YACrG,IAAIgK,kBAAkB,EAAE;cACtBK,iBAAiB,CAACpM,CAAC,CAAC,GAAGkB,EAAE,CAAC0K,OAAO,CAACH,QAAQ,CAACrE,KAAK,CAAC,IAAI,EAAE2E,kBAAkB,CAAC;cACtF;cACYK,iBAAiB,CAACpM,CAAC,CAAC,CAAC4L,OAAO,GAAG1K,EAAE,CAAC0K,OAAO;cACzCS,UAAU,CAACrM,CAAC,CAAC,GAAG,IAAI;cACpBwM,eAAe,GAAG,KAAK;YACnC,CAAW,MAAM;cACLD,cAAc,GAAG,IAAI;YACjC;UACA;QACA;QACM,IAAIC,eAAe,IAAID,cAAc,EAAE;UACrC,MAAM,IAAIpH,WAAW,CAAC,wDAAwD,CAAC;QACvF;MACA;MACI,OAAOiH,iBAAiB;IAC5B;;IAEA;;;;;;;IAOE,SAASK,sBAAsBA,CAACC,aAAa,EAAE;MACjD;;MAEA;MACI,MAAMC,mBAAmB,GAAG,2BAA2B;MACvD1O,MAAM,CAAC2O,IAAI,CAACF,aAAa,CAAC,CAAC5H,OAAO,CAAC3D,SAAS,IAAI;QAC9C,MAAMD,EAAE,GAAGwL,aAAa,CAACvL,SAAS,CAAC;QACnC,IAAIwL,mBAAmB,CAACvO,IAAI,CAAC8C,EAAE,CAAC2L,QAAQ,EAAE,CAAC,EAAE;UAC3C,MAAM,IAAI1H,WAAW,CAAC,4CAA4C,GAAG,wCAAwC,GAAG,kDAAkD,CAAC;QAC3K;MACA,CAAK,CAAC;IACN;;IAEA;;;;;;;;;;IAUE,SAAS2H,mBAAmBA,CAAC3O,IAAI,EAAE4O,gBAAgB,EAAE;MACnDjO,KAAK,CAACC,WAAW,EAAE;MACnB,IAAId,MAAM,CAAC2O,IAAI,CAACG,gBAAgB,CAAC,CAACnN,MAAM,KAAK,CAAC,EAAE;QAC9C,MAAM,IAAIuF,WAAW,CAAC,wBAAwB,CAAC;MACrD;MACI,IAAIrG,KAAK,CAACkO,yBAAyB,EAAE;QACnCP,sBAAsB,CAACM,gBAAgB,CAAC;MAC9C;;MAEA;MACI,MAAME,YAAY,GAAG,EAAE;MACvB,MAAMC,iBAAiB,GAAG,EAAE;MAC5B,MAAMR,aAAa,GAAG,EAAE;MACxB,MAAMS,qBAAqB,GAAG,EAAE,CAAC;MACjC,IAAIhM,SAAS;MACb,KAAKA,SAAS,IAAI4L,gBAAgB,EAAE;QACxC;QACM,IAAI,CAAC9O,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,gBAAgB,EAAE5L,SAAS,CAAC,EAAE;UACtE;QACR;QACA;QACM,MAAMK,MAAM,GAAGC,cAAc,CAACN,SAAS,CAAC;QACxC,IAAI,CAACK,MAAM,EAAE;QACnB;QACMyL,YAAY,CAACnI,OAAO,CAAC,UAAUyI,EAAE,EAAE;UACjC,IAAI7C,WAAW,CAAC6C,EAAE,EAAE/L,MAAM,CAAC,EAAE;YAC3B,MAAM,IAAIjC,SAAS,CAAC,0BAA0B,GAAGoC,eAAe,CAAC4L,EAAE,CAAC,GAAG,SAAS,GAAG5L,eAAe,CAACH,MAAM,CAAC,GAAG,IAAI,CAAC;UAC5H;QACA,CAAO,CAAC;QACFyL,YAAY,CAAC/M,IAAI,CAACsB,MAAM,CAAC;QAC/B;QACM,MAAMgM,aAAa,GAAGN,iBAAiB,CAACtN,MAAM;QAC9CsN,iBAAiB,CAAChN,IAAI,CAAC6M,gBAAgB,CAAC5L,SAAS,CAAC,CAAC;QACnD,MAAMsM,gBAAgB,GAAGjM,MAAM,CAAC8B,GAAG,CAACe,WAAW,CAAC;QACtD;QACM,IAAIqJ,EAAE;QACN,KAAKA,EAAE,IAAItD,WAAW,CAACqD,gBAAgB,CAAC,EAAE;UACxC,MAAME,MAAM,GAAGhM,eAAe,CAAC+L,EAAE,CAAC;UAClCP,qBAAqB,CAACjN,IAAI,CAAC;YACzBsB,MAAM,EAAEkM,EAAE;YACVvP,IAAI,EAAEwP,MAAM;YACZzM,EAAE,EAAEsM;UACd,CAAS,CAAC;UACF,IAAIE,EAAE,CAACE,KAAK,CAACrK,CAAC,IAAI,CAACA,CAAC,CAACa,aAAa,CAAC,EAAE;YACnCsI,aAAa,CAACiB,MAAM,CAAC,GAAGH,aAAa;UAC/C;QACA;MACA;MACIL,qBAAqB,CAAChE,IAAI,CAACnB,iBAAiB,CAAC;;MAEjD;MACI,MAAMoE,iBAAiB,GAAGF,iBAAiB,CAACgB,iBAAiB,EAAER,aAAa,EAAEmB,UAAU,CAAC;;MAE7F;MACI,IAAIhK,CAAC;MACL,KAAKA,CAAC,IAAI6I,aAAa,EAAE;QACvB,IAAIzO,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAACZ,aAAa,EAAE7I,CAAC,CAAC,EAAE;UAC1D6I,aAAa,CAAC7I,CAAC,CAAC,GAAGuI,iBAAiB,CAACM,aAAa,CAAC7I,CAAC,CAAC,CAAC;QAC9D;MACA;MACI,MAAMjC,UAAU,GAAG,EAAE;MACrB,MAAMkM,oBAAoB,GAAG,IAAIrN,GAAG,EAAE,CAAC;MACvC,KAAKoD,CAAC,IAAIsJ,qBAAqB,EAAE;QACrC;QACA;QACA;QACM,IAAI,CAACW,oBAAoB,CAAC7N,GAAG,CAAC4D,CAAC,CAAC1F,IAAI,CAAC,EAAE;UACrC0F,CAAC,CAAC3C,EAAE,GAAGkL,iBAAiB,CAACvI,CAAC,CAAC3C,EAAE,CAAC;UAC9BU,UAAU,CAAC1B,IAAI,CAAC2D,CAAC,CAAC;UAClBiK,oBAAoB,CAAC3N,GAAG,CAAC0D,CAAC,CAAC1F,IAAI,EAAE0F,CAAC,CAAC;QAC3C;MACA;;MAEA;MACI,MAAMkK,GAAG,GAAGnM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;MACpG,MAAMwM,GAAG,GAAGpM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;MACpG,MAAMyM,GAAG,GAAGrM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;MACpG,MAAM0M,GAAG,GAAGtM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;MACpG,MAAM2M,GAAG,GAAGvM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;MACpG,MAAM4M,GAAG,GAAGxM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;MACpG,MAAM6M,KAAK,GAAGN,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG;;MAExD;MACI,KAAK,IAAIpO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,UAAU,CAAChC,MAAM,EAAE,EAAEI,CAAC,EAAE;QAC1C4B,UAAU,CAAC5B,CAAC,CAAC,CAAC5B,IAAI,GAAGuH,YAAY,CAAC/D,UAAU,CAAC5B,CAAC,CAAC,CAACwB,MAAM,CAAC;MAC7D;MACI,MAAM8M,MAAM,GAAGP,GAAG,GAAGzI,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAM8Q,MAAM,GAAGP,GAAG,GAAG1I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAM+Q,MAAM,GAAGP,GAAG,GAAG3I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMgR,MAAM,GAAGP,GAAG,GAAG5I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMiR,MAAM,GAAGP,GAAG,GAAG7I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMkR,MAAM,GAAGP,GAAG,GAAG9I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMmR,MAAM,GAAGb,GAAG,GAAGzI,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMoR,MAAM,GAAGb,GAAG,GAAG1I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMqR,MAAM,GAAGb,GAAG,GAAG3I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMsR,MAAM,GAAGb,GAAG,GAAG5I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMuR,MAAM,GAAGb,GAAG,GAAG7I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;MACjE,MAAMwR,MAAM,GAAGb,GAAG,GAAG9I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;;MAErE;MACI,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,UAAU,CAAChC,MAAM,EAAE,EAAEI,CAAC,EAAE;QAC1C4B,UAAU,CAAC5B,CAAC,CAAC,CAAC+C,cAAc,GAAGyG,wBAAwB,CAAC5H,UAAU,CAAC5B,CAAC,CAAC,CAACwB,MAAM,EAAEI,UAAU,CAAC5B,CAAC,CAAC,CAACkB,EAAE,CAAC;MACrG;MACI,MAAMgO,GAAG,GAAGnB,GAAG,GAAGnM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;MACtD,MAAMyR,GAAG,GAAGnB,GAAG,GAAGpM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;MACtD,MAAM0R,GAAG,GAAGnB,GAAG,GAAGrM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;MACtD,MAAM2R,GAAG,GAAGnB,GAAG,GAAGtM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;MACtD,MAAM4R,GAAG,GAAGnB,GAAG,GAAGvM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;MACtD,MAAM6R,GAAG,GAAGnB,GAAG,GAAGxM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;MACtD,MAAM8R,IAAI,GAAGzB,GAAG,GAAGnM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;MACnD,MAAMqL,IAAI,GAAG+C,GAAG,GAAGpM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;MACnD,MAAMsL,IAAI,GAAG+C,GAAG,GAAGrM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;MACnD,MAAM6P,IAAI,GAAGvB,GAAG,GAAGtM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;MACnD,MAAM8P,IAAI,GAAGvB,GAAG,GAAGvM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;MACnD,MAAM+P,IAAI,GAAGvB,GAAG,GAAGxM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;;MAEvD;MACI,MAAMgQ,MAAM,GAAGvB,KAAK,GAAG,CAAC,GAAG,CAAC;MAC5B,MAAMwB,IAAI,GAAGjO,UAAU,CAAChC,MAAM;MAClC;MACI,MAAM8F,KAAK,GAAG9D,UAAU,CAAC0B,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACzF,IAAI,CAAC;MACzC,MAAM0R,GAAG,GAAGlO,UAAU,CAAC0B,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACd,cAAc,CAAC;MACjD,MAAMgN,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;QAGjC,KAAK,IAAI/P,CAAC,GAAG4P,MAAM,EAAE5P,CAAC,GAAG6P,IAAI,EAAE7P,CAAC,EAAE,EAAE;UAClC,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,CAACL,SAAS,CAAC,EAAE;YACvB,OAAOmQ,GAAG,CAAC9P,CAAC,CAAC,CAACoH,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;UAC9C;QACA;QACM,OAAOb,KAAK,CAACkR,UAAU,CAAC7R,IAAI,EAAEwB,SAAS,EAAEiC,UAAU,CAAC;MAC1D,CAAK;;MAEL;MACA;MACI,SAASiM,UAAUA,CAACoC,IAAI,EAAEC,IAAI,EAAE;QAG9B,IAAIvQ,SAAS,CAACC,MAAM,KAAK4P,IAAI,IAAIlB,MAAM,CAAC2B,IAAI,CAAC,IAAIrB,MAAM,CAACsB,IAAI,CAAC,EAAE;UAC7D,OAAOhB,GAAG,CAAC9H,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACzC;QACM,IAAIA,SAAS,CAACC,MAAM,KAAKqL,IAAI,IAAIsD,MAAM,CAAC0B,IAAI,CAAC,IAAIpB,MAAM,CAACqB,IAAI,CAAC,EAAE;UAC7D,OAAOf,GAAG,CAAC/H,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACzC;QACM,IAAIA,SAAS,CAACC,MAAM,KAAKsL,IAAI,IAAIsD,MAAM,CAACyB,IAAI,CAAC,IAAInB,MAAM,CAACoB,IAAI,CAAC,EAAE;UAC7D,OAAOd,GAAG,CAAChI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACzC;QACM,IAAIA,SAAS,CAACC,MAAM,KAAK6P,IAAI,IAAIhB,MAAM,CAACwB,IAAI,CAAC,IAAIlB,MAAM,CAACmB,IAAI,CAAC,EAAE;UAC7D,OAAOb,GAAG,CAACjI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACzC;QACM,IAAIA,SAAS,CAACC,MAAM,KAAK8P,IAAI,IAAIhB,MAAM,CAACuB,IAAI,CAAC,IAAIjB,MAAM,CAACkB,IAAI,CAAC,EAAE;UAC7D,OAAOZ,GAAG,CAAClI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACzC;QACM,IAAIA,SAAS,CAACC,MAAM,KAAK+P,IAAI,IAAIhB,MAAM,CAACsB,IAAI,CAAC,IAAIhB,MAAM,CAACiB,IAAI,CAAC,EAAE;UAC7D,OAAOX,GAAG,CAACnI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACzC;QACM,OAAOoQ,OAAO,CAAC3I,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAC3C;;MAEA;MACI,IAAI;QACF1B,MAAM,CAACkS,cAAc,CAACtC,UAAU,EAAE,MAAM,EAAE;UACxCjN,KAAK,EAAEzC;QACf,CAAO,CAAC;MACR,CAAK,CAAC,OAAOoI,GAAG,EAAE;QAClB;QACA;QACA;MAAA;;MAGA;MACA;MACA;MACIsH,UAAU,CAACjM,UAAU,GAAG8K,aAAa;;MAEzC;MACA;MACImB,UAAU,CAAC/L,kBAAkB,GAAG;QAC9BF,UAAU;QACVG,YAAY,EAAE+L;MACpB,CAAK;MACD,OAAOD,UAAU;IACrB;;IAEA;;;;;;IAME,SAASuC,WAAWA,CAACjS,IAAI,EAAE6H,IAAI,EAAEpE,UAAU,EAAE;MAC3C,MAAM0E,WAAW,CAACnI,IAAI,EAAE6H,IAAI,EAAEpE,UAAU,CAAC;IAC7C;;IAEA;;;;;IAKE,SAASgE,OAAOA,CAACyK,GAAG,EAAE;MACpB,OAAO/P,KAAK,CAAC+P,GAAG,EAAE,CAAC,EAAEA,GAAG,CAACzQ,MAAM,GAAG,CAAC,CAAC;IACxC;;IAEA;;;;;IAKE,SAASyF,IAAIA,CAACgL,GAAG,EAAE;MACjB,OAAOA,GAAG,CAACA,GAAG,CAACzQ,MAAM,GAAG,CAAC,CAAC;IAC9B;;IAEA;;;;;;;IAOE,SAASU,KAAKA,CAAC+P,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAE;MAC9B,OAAOlS,KAAK,CAAC+O,SAAS,CAAC9M,KAAK,CAACgN,IAAI,CAAC+C,GAAG,EAAEC,KAAK,EAAEC,GAAG,CAAC;IACtD;;IAEA;;;;;;;IAOE,SAASC,WAAWA,CAACH,GAAG,EAAEjS,IAAI,EAAE;MAC9B,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqQ,GAAG,CAACzQ,MAAM,EAAEI,CAAC,EAAE,EAAE;QACnC,IAAI5B,IAAI,CAACiS,GAAG,CAACrQ,CAAC,CAAC,CAAC,EAAE;UAChB,OAAOqQ,GAAG,CAACrQ,CAAC,CAAC;QACrB;MACA;MACI,OAAOrC,SAAS;IACpB;;IAEA;;;;;;;IAOE,SAAS6M,OAAOA,CAAC6F,GAAG,EAAE5E,QAAQ,EAAE;MAC9B,OAAOpN,KAAK,CAAC+O,SAAS,CAAC7M,MAAM,CAAC6G,KAAK,CAAC,EAAE,EAAEiJ,GAAG,CAAC/M,GAAG,CAACmI,QAAQ,CAAC,CAAC;IAC9D;;IAEA;;;;;;;;;;;IAWE,SAASG,OAAOA,CAAA,EAAG;MACjB,MAAMC,UAAU,GAAGjG,OAAO,CAACjG,SAAS,CAAC,CAAC2D,GAAG,CAACO,CAAC,IAAIlC,eAAe,CAACF,cAAc,CAACoC,CAAC,CAAC,CAAC,CAAC;MAClF,MAAM4H,QAAQ,GAAGpG,IAAI,CAAC1F,SAAS,CAAC;MAChC,IAAI,OAAO8L,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIlM,SAAS,CAAC,6CAA6C,CAAC;MACxE;MACI,OAAOoM,WAAW,CAACE,UAAU,EAAEJ,QAAQ,CAAC;IAC5C;IACE,SAASE,WAAWA,CAACE,UAAU,EAAEJ,QAAQ,EAAE;MACzC,OAAO;QACLG,OAAO,EAAE;UACPC,UAAU;UACVJ;QACR;MACA,CAAK;IACL;;IAEA;;;;;;IAME,SAASD,WAAWA,CAACC,QAAQ,EAAE;MAC7B,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIlM,SAAS,CAAC,8CAA8C,CAAC;MACzE;MACI,OAAO;QACLiM,WAAW,EAAE;UACXC;QACR;MACA,CAAK;IACL;;IAEA;;;;;;;IAOE,SAASC,SAASA,CAAC+E,UAAU,EAAE;MAC7B,OAAOA,UAAU,IAAI,OAAOA,UAAU,CAAC7E,OAAO,KAAK,QAAQ,IAAIvN,KAAK,CAACC,OAAO,CAACmS,UAAU,CAAC7E,OAAO,CAACC,UAAU,CAAC,IAAI,OAAO4E,UAAU,CAAC7E,OAAO,CAACH,QAAQ,KAAK,UAAU;IACpK;;IAEA;;;;;;;IAOE,SAASF,aAAaA,CAACkF,UAAU,EAAE;MACjC,OAAOA,UAAU,IAAI,OAAOA,UAAU,CAACjF,WAAW,KAAK,QAAQ,IAAI,OAAOiF,UAAU,CAACjF,WAAW,CAACC,QAAQ,KAAK,UAAU;IAC5H;;IAEA;;;;;;;;IAQE,SAASiF,SAASA,CAACC,SAAS,EAAEjM,OAAO,EAAE;MACrC,IAAI,CAACiM,SAAS,EAAE;QACd,OAAOjM,OAAO;MACpB;MACI,IAAIA,OAAO,IAAIA,OAAO,KAAKiM,SAAS,EAAE;QACpC,MAAMpK,GAAG,GAAG,IAAIrD,KAAK,CAAC,yCAAyC,GAAGyN,SAAS,GAAG,YAAY,GAAGjM,OAAO,GAAG,GAAG,CAAC;QAC3G6B,GAAG,CAACM,IAAI,GAAG;UACTE,MAAM,EAAErC,OAAO;UACf8B,QAAQ,EAAEmK;QAClB,CAAO;QACD,MAAMpK,GAAG;MACf;MACI,OAAOoK,SAAS;IACpB;;IAEA;;;;;;IAME,SAASC,aAAaA,CAACC,GAAG,EAAE;MAC1B,IAAI1S,IAAI;MACR,KAAK,MAAM2S,GAAG,IAAID,GAAG,EAAE;QAC3B;QACA;QACM,IAAI5S,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuD,GAAG,EAAEC,GAAG,CAAC,KAAK/P,eAAe,CAAC8P,GAAG,CAACC,GAAG,CAAC,CAAC,IAAI,OAAOD,GAAG,CAACC,GAAG,CAAC,CAAC3P,SAAS,KAAK,QAAQ,CAAC,EAAE;UAC3HhD,IAAI,GAAGuS,SAAS,CAACvS,IAAI,EAAE0S,GAAG,CAACC,GAAG,CAAC,CAAC3S,IAAI,CAAC;QAC7C;MACA;MACI,OAAOA,IAAI;IACf;;IAEA;;;;;;;IAOE,SAAS4S,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAE;MACrC,IAAIH,GAAG;MACP,KAAKA,GAAG,IAAIG,MAAM,EAAE;QAClB,IAAIhT,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2D,MAAM,EAAEH,GAAG,CAAC,EAAE;UACrD,IAAIA,GAAG,IAAIE,IAAI,EAAE;YACf,IAAIC,MAAM,CAACH,GAAG,CAAC,KAAKE,IAAI,CAACF,GAAG,CAAC,EAAE;cAC7B,MAAMvK,GAAG,GAAG,IAAIrD,KAAK,CAAC,aAAa,GAAG4N,GAAG,GAAG,oBAAoB,CAAC;cACjEvK,GAAG,CAACM,IAAI,GAAG;gBACT1F,SAAS,EAAE2P,GAAG;gBACdI,cAAc,EAAED,MAAM,CAACH,GAAG,CAAC;gBAC3BK,YAAY,EAAEH,IAAI,CAACF,GAAG;cACpC,CAAa;cACD,MAAMvK,GAAG;YACrB;YACA;UACA;UACQyK,IAAI,CAACF,GAAG,CAAC,GAAGG,MAAM,CAACH,GAAG,CAAC;QAC/B;MACA;IACA;IACE,MAAMM,SAAS,GAAGtS,KAAK;;IAEzB;;;;;;;;;;;;;;;;;;;;;;IAsBEA,KAAK,GAAG,SAAAA,CAAUuS,SAAS,EAAE;MAC3B,MAAMC,KAAK,GAAG,OAAOD,SAAS,KAAK,QAAQ;MAC3C,MAAMf,KAAK,GAAGgB,KAAK,GAAG,CAAC,GAAG,CAAC;MAC3B,IAAInT,IAAI,GAAGmT,KAAK,GAAGD,SAAS,GAAG,EAAE;MACjC,MAAME,aAAa,GAAG,EAAE;MACxB,KAAK,IAAIvR,CAAC,GAAGsQ,KAAK,EAAEtQ,CAAC,GAAGL,SAAS,CAACC,MAAM,EAAE,EAAEI,CAAC,EAAE;QAC7C,MAAMwR,IAAI,GAAG7R,SAAS,CAACK,CAAC,CAAC;QACzB,IAAIyR,eAAe,GAAG,EAAE;QACxB,IAAIC,QAAQ;QACZ,IAAI,OAAOF,IAAI,KAAK,UAAU,EAAE;UAC9BE,QAAQ,GAAGF,IAAI,CAACrT,IAAI;UACpB,IAAI,OAAOqT,IAAI,CAACrQ,SAAS,KAAK,QAAQ,EAAE;YAChD;YACUsQ,eAAe,CAACD,IAAI,CAACrQ,SAAS,CAAC,GAAGqQ,IAAI;UAChD,CAAS,MAAM,IAAIzQ,eAAe,CAACyQ,IAAI,CAAC,EAAE;YAC1C;YACUC,eAAe,GAAGD,IAAI,CAAC5P,UAAU;UAC3C;QACA,CAAO,MAAM,IAAI9D,aAAa,CAAC0T,IAAI,CAAC,EAAE;UACtC;UACQC,eAAe,GAAGD,IAAI;UACtB,IAAI,CAACF,KAAK,EAAE;YACVI,QAAQ,GAAGd,aAAa,CAACY,IAAI,CAAC;UACxC;QACA;QACM,IAAIvT,MAAM,CAAC2O,IAAI,CAAC6E,eAAe,CAAC,CAAC7R,MAAM,KAAK,CAAC,EAAE;UAC7C,MAAM2G,GAAG,GAAG,IAAIhH,SAAS,CAAC,iCAAiC,GAAGS,CAAC,GAAG,8BAA8B,GAAG,gEAAgE,CAAC;UACpKuG,GAAG,CAACM,IAAI,GAAG;YACT/G,KAAK,EAAEE,CAAC;YACR2R,QAAQ,EAAEH;UACpB,CAAS;UACD,MAAMjL,GAAG;QACjB;QACM,IAAI,CAAC+K,KAAK,EAAE;UACVnT,IAAI,GAAGuS,SAAS,CAACvS,IAAI,EAAEuT,QAAQ,CAAC;QACxC;QACMX,eAAe,CAACQ,aAAa,EAAEE,eAAe,CAAC;MACrD;MACI,OAAO3E,mBAAmB,CAAC3O,IAAI,IAAI,EAAE,EAAEoT,aAAa,CAAC;IACzD,CAAG;IACDzS,KAAK,CAACjB,MAAM,GAAGA,MAAM;IACrBiB,KAAK,CAACC,WAAW,GAAGqS,SAAS,CAACrS,WAAW;IACzCD,KAAK,CAACkR,UAAU,GAAGI,WAAW;IAC9BtR,KAAK,CAAC8S,kBAAkB,GAAGxB,WAAW;IACtCtR,KAAK,CAACwH,WAAW,GAAGA,WAAW;IAC/BxH,KAAK,CAAC0B,KAAK,GAAGA,KAAK;IACnB1B,KAAK,CAAC4B,gBAAgB,GAAGA,gBAAgB;IACzC5B,KAAK,CAACU,QAAQ,GAAGA,QAAQ;IACzBV,KAAK,CAAC+S,SAAS,GAAG7S,QAAQ,CAAC;IAC3BF,KAAK,CAAC8M,OAAO,GAAGA,OAAO;IACvB9M,KAAK,CAAC0M,WAAW,GAAGA,WAAW;IAC/B1M,KAAK,CAACkE,OAAO,GAAGA,OAAO;IACvBlE,KAAK,CAACmC,aAAa,GAAGA,aAAa;IACnCnC,KAAK,CAACgE,IAAI,GAAGA,IAAI;IACjBhE,KAAK,CAACiC,eAAe,GAAGA,eAAe;IACvCjC,KAAK,CAACkO,yBAAyB,GAAG,IAAI;;IAExC;;;;;;;;IAQElO,KAAK,CAACgT,OAAO,GAAG,UAAU5S,IAAI,EAAE6S,gBAAgB,EAAE;MAChD,IAAIC,MAAM,GAAG,KAAK;MAClB,IAAID,gBAAgB,KAAK,KAAK,IAAIpT,OAAO,CAACsB,GAAG,CAAC,QAAQ,CAAC,EAAE;QACvD+R,MAAM,GAAG,QAAQ;MACvB;MACIlT,KAAK,CAACU,QAAQ,CAAC,CAACN,IAAI,CAAC,EAAE8S,MAAM,CAAC;IAClC,CAAG;;IAEH;;;;;;;IAOE,SAASC,mBAAmBA,CAAC/N,UAAU,EAAE;MACvC,IAAI,CAACA,UAAU,IAAI,OAAOA,UAAU,CAACd,IAAI,KAAK,QAAQ,IAAI,OAAOc,UAAU,CAACgO,EAAE,KAAK,QAAQ,IAAI,OAAOhO,UAAU,CAAClB,OAAO,KAAK,UAAU,EAAE;QACvI,MAAM,IAAIzD,SAAS,CAAC,+EAA+E,CAAC;MAC1G;MACI,IAAI2E,UAAU,CAACgO,EAAE,KAAKhO,UAAU,CAACd,IAAI,EAAE;QACrC,MAAM,IAAI+B,WAAW,CAAC,qCAAqC,GAAGjB,UAAU,CAACd,IAAI,GAAG,cAAc,CAAC;MACrG;IACA;;IAEA;;;;;;;;IAQEtE,KAAK,CAACqT,aAAa,GAAG,UAAUjO,UAAU,EAAE;MAC1C,IAAI9C,OAAO,GAAGzB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKhC,SAAS,GAAGgC,SAAS,CAAC,CAAC,CAAC,GAAG;QAChFyS,QAAQ,EAAE;MAChB,CAAK;MACDH,mBAAmB,CAAC/N,UAAU,CAAC;MAC/B,MAAMgO,EAAE,GAAGlT,QAAQ,CAACkF,UAAU,CAACgO,EAAE,CAAC;MAClC,MAAMG,QAAQ,GAAGH,EAAE,CAAC9R,aAAa,CAAC0C,IAAI,CAACwP,KAAK,IAAIA,KAAK,CAAClP,IAAI,KAAKc,UAAU,CAACd,IAAI,CAAC;MAC/E,IAAIiP,QAAQ,EAAE;QACZ,IAAIjR,OAAO,IAAIA,OAAO,CAACgR,QAAQ,EAAE;UAC/BtT,KAAK,CAACyT,gBAAgB,CAAC;YACrBnP,IAAI,EAAEiP,QAAQ,CAACjP,IAAI;YACnB8O,EAAE,EAAEhO,UAAU,CAACgO,EAAE;YACjBlP,OAAO,EAAEqP,QAAQ,CAACrP;UAC5B,CAAS,CAAC;QACV,CAAO,MAAM;UACL,MAAM,IAAIE,KAAK,CAAC,sCAAsC,GAAGgB,UAAU,CAACd,IAAI,GAAG,QAAQ,GAAG8O,EAAE,CAAC/T,IAAI,GAAG,GAAG,CAAC;QAC5G;MACA;MACI+T,EAAE,CAAC9R,aAAa,CAACF,IAAI,CAAC;QACpBkD,IAAI,EAAEc,UAAU,CAACd,IAAI;QACrBJ,OAAO,EAAEkB,UAAU,CAAClB,OAAO;QAC3BlD,KAAK,EAAEjB,YAAY;MACzB,CAAK,CAAC;IACN,CAAG;;IAEH;;;;;;;;IAQEC,KAAK,CAAC0T,cAAc,GAAG,UAAUvP,WAAW,EAAE7B,OAAO,EAAE;MACrD6B,WAAW,CAAC6B,OAAO,CAACZ,UAAU,IAAIpF,KAAK,CAACqT,aAAa,CAACjO,UAAU,EAAE9C,OAAO,CAAC,CAAC;IAC/E,CAAG;;IAEH;;;;;;;;;IASEtC,KAAK,CAACyT,gBAAgB,GAAG,UAAUrO,UAAU,EAAE;MAC7C+N,mBAAmB,CAAC/N,UAAU,CAAC;MAC/B,MAAMgO,EAAE,GAAGlT,QAAQ,CAACkF,UAAU,CAACgO,EAAE,CAAC;MAClC,MAAMO,kBAAkB,GAAGjC,WAAW,CAAC0B,EAAE,CAAC9R,aAAa,EAAE8I,CAAC,IAAIA,CAAC,CAAC9F,IAAI,KAAKc,UAAU,CAACd,IAAI,CAAC;MACzF,IAAI,CAACqP,kBAAkB,EAAE;QACvB,MAAM,IAAIvP,KAAK,CAAC,gDAAgD,GAAGgB,UAAU,CAACd,IAAI,GAAG,MAAM,GAAGc,UAAU,CAACgO,EAAE,CAAC;MAClH;MACI,IAAIO,kBAAkB,CAACzP,OAAO,KAAKkB,UAAU,CAAClB,OAAO,EAAE;QACrD,MAAM,IAAIE,KAAK,CAAC,yDAAyD,CAAC;MAChF;MACI,MAAMpD,KAAK,GAAGoS,EAAE,CAAC9R,aAAa,CAACsD,OAAO,CAAC+O,kBAAkB,CAAC;MAC1DP,EAAE,CAAC9R,aAAa,CAACsS,MAAM,CAAC5S,KAAK,EAAE,CAAC,CAAC;IACrC,CAAG;;IAEH;;;;;;;;;;IAUEhB,KAAK,CAAC6T,OAAO,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;MACrC,IAAI,CAAC9R,eAAe,CAAC6R,EAAE,CAAC,EAAE;QACxB,MAAM,IAAIrT,SAAS,CAAC3B,kBAAkB,CAAC;MAC7C;MACI,MAAMkV,IAAI,GAAGF,EAAE,CAAC9Q,kBAAkB,CAACF,UAAU;MAC7C,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8S,IAAI,CAAClT,MAAM,EAAE,EAAEI,CAAC,EAAE;QACpC,IAAI8S,IAAI,CAAC9S,CAAC,CAAC,CAAC5B,IAAI,CAACyU,OAAO,CAAC,EAAE;UACzB,OAAOC,IAAI,CAAC9S,CAAC,CAAC;QACtB;MACA;MACI,OAAO,IAAI;IACf,CAAG;IACD,OAAOlB,KAAK;EACd;EACA,IAAAiU,aAAA,GAAelV,MAAM,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}