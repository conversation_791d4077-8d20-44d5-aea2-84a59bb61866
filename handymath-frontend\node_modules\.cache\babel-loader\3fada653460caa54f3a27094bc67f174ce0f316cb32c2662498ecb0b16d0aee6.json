{"ast": null, "code": "export var reshapeDocs = {\n  name: 'reshape',\n  category: 'Matrix',\n  syntax: ['reshape(x, sizes)'],\n  description: 'Reshape a multi dimensional array to fit the specified dimensions.',\n  examples: ['reshape([1, 2, 3, 4, 5, 6], [2, 3])', 'reshape([[1, 2], [3, 4]], [1, 4])', 'reshape([[1, 2], [3, 4]], [4])', 'reshape([1, 2, 3, 4], [-1, 2])'],\n  seealso: ['size', 'squeeze', 'resize']\n};", "map": {"version": 3, "names": ["reshapeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/reshape.js"], "sourcesContent": ["export var reshapeDocs = {\n  name: 'reshape',\n  category: 'Matrix',\n  syntax: ['reshape(x, sizes)'],\n  description: 'Reshape a multi dimensional array to fit the specified dimensions.',\n  examples: ['reshape([1, 2, 3, 4, 5, 6], [2, 3])', 'reshape([[1, 2], [3, 4]], [1, 4])', 'reshape([[1, 2], [3, 4]], [4])', 'reshape([1, 2, 3, 4], [-1, 2])'],\n  seealso: ['size', 'squeeze', 'resize']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,mBAAmB,CAAC;EAC7BC,WAAW,EAAE,oEAAoE;EACjFC,QAAQ,EAAE,CAAC,qCAAqC,EAAE,mCAAmC,EAAE,gCAAgC,EAAE,gCAAgC,CAAC;EAC1JC,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}