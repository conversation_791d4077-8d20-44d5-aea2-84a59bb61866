{"ast": null, "code": "function E() {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n    return this;\n  },\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener() {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    }\n    ;\n    listener._ = callback;\n    return this.on(name, listener, ctx);\n  },\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n    return this;\n  },\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback) liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    liveEvents.length ? e[name] = liveEvents : delete e[name];\n    return this;\n  }\n};\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;", "map": {"version": 3, "names": ["E", "prototype", "on", "name", "callback", "ctx", "e", "push", "fn", "once", "self", "listener", "off", "apply", "arguments", "_", "emit", "data", "slice", "call", "evtArr", "i", "len", "length", "evts", "liveEvents", "module", "exports", "TinyEmitter"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/tiny-emitter/index.js"], "sourcesContent": ["function E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAI;EACZ;EACA;AAAA;AAGFA,CAAC,CAACC,SAAS,GAAG;EACZC,EAAE,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IACjC,IAAIC,CAAC,GAAG,IAAI,CAACA,CAAC,KAAK,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC;IAE/B,CAACA,CAAC,CAACH,IAAI,CAAC,KAAKG,CAAC,CAACH,IAAI,CAAC,GAAG,EAAE,CAAC,EAAEI,IAAI,CAAC;MAC/BC,EAAE,EAAEJ,QAAQ;MACZC,GAAG,EAAEA;IACP,CAAC,CAAC;IAEF,OAAO,IAAI;EACb,CAAC;EAEDI,IAAI,EAAE,SAAAA,CAAUN,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IACnC,IAAIK,IAAI,GAAG,IAAI;IACf,SAASC,QAAQA,CAAA,EAAI;MACnBD,IAAI,CAACE,GAAG,CAACT,IAAI,EAAEQ,QAAQ,CAAC;MACxBP,QAAQ,CAACS,KAAK,CAACR,GAAG,EAAES,SAAS,CAAC;IAChC;IAAC;IAEDH,QAAQ,CAACI,CAAC,GAAGX,QAAQ;IACrB,OAAO,IAAI,CAACF,EAAE,CAACC,IAAI,EAAEQ,QAAQ,EAAEN,GAAG,CAAC;EACrC,CAAC;EAEDW,IAAI,EAAE,SAAAA,CAAUb,IAAI,EAAE;IACpB,IAAIc,IAAI,GAAG,EAAE,CAACC,KAAK,CAACC,IAAI,CAACL,SAAS,EAAE,CAAC,CAAC;IACtC,IAAIM,MAAM,GAAG,CAAC,CAAC,IAAI,CAACd,CAAC,KAAK,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC,IAAI,EAAE,EAAEe,KAAK,CAAC,CAAC;IAC5D,IAAIG,CAAC,GAAG,CAAC;IACT,IAAIC,GAAG,GAAGF,MAAM,CAACG,MAAM;IAEvB,KAAKF,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpBD,MAAM,CAACC,CAAC,CAAC,CAACb,EAAE,CAACK,KAAK,CAACO,MAAM,CAACC,CAAC,CAAC,CAAChB,GAAG,EAAEY,IAAI,CAAC;IACzC;IAEA,OAAO,IAAI;EACb,CAAC;EAEDL,GAAG,EAAE,SAAAA,CAAUT,IAAI,EAAEC,QAAQ,EAAE;IAC7B,IAAIE,CAAC,GAAG,IAAI,CAACA,CAAC,KAAK,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,IAAIkB,IAAI,GAAGlB,CAAC,CAACH,IAAI,CAAC;IAClB,IAAIsB,UAAU,GAAG,EAAE;IAEnB,IAAID,IAAI,IAAIpB,QAAQ,EAAE;MACpB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGE,IAAI,CAACD,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAIG,IAAI,CAACH,CAAC,CAAC,CAACb,EAAE,KAAKJ,QAAQ,IAAIoB,IAAI,CAACH,CAAC,CAAC,CAACb,EAAE,CAACO,CAAC,KAAKX,QAAQ,EACtDqB,UAAU,CAAClB,IAAI,CAACiB,IAAI,CAACH,CAAC,CAAC,CAAC;MAC5B;IACF;;IAEA;IACA;IACA;;IAECI,UAAU,CAACF,MAAM,GACdjB,CAAC,CAACH,IAAI,CAAC,GAAGsB,UAAU,GACpB,OAAOnB,CAAC,CAACH,IAAI,CAAC;IAElB,OAAO,IAAI;EACb;AACF,CAAC;AAEDuB,MAAM,CAACC,OAAO,GAAG3B,CAAC;AAClB0B,MAAM,CAACC,OAAO,CAACC,WAAW,GAAG5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}