{"ast": null, "code": "export var simplifyCoreDocs = {\n  name: 'simplifyCore',\n  category: 'Algebra',\n  syntax: ['simplifyCore(node)'],\n  description: 'Perform simple one-pass simplifications on an expression tree.',\n  examples: ['simplifyCore(parse(\"0*x\"))', 'simplifyCore(parse(\"(x+0)*2\"))'],\n  seealso: ['simplify', 'simplifyConstant', 'evaluate']\n};", "map": {"version": 3, "names": ["simplifyCoreDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/simplifyCore.js"], "sourcesContent": ["export var simplifyCoreDocs = {\n  name: 'simplifyCore',\n  category: 'Algebra',\n  syntax: ['simplifyCore(node)'],\n  description: 'Perform simple one-pass simplifications on an expression tree.',\n  examples: ['simplifyCore(parse(\"0*x\"))', 'simplifyCore(parse(\"(x+0)*2\"))'],\n  seealso: ['simplify', 'simplifyConstant', 'evaluate']\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,oBAAoB,CAAC;EAC9BC,WAAW,EAAE,gEAAgE;EAC7EC,QAAQ,EAAE,CAAC,4BAA4B,EAAE,gCAAgC,CAAC;EAC1EC,OAAO,EAAE,CAAC,UAAU,EAAE,kBAAkB,EAAE,UAAU;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}