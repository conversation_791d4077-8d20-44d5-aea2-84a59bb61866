@tailwind base;
@tailwind components;
@tailwind utilities;

/* Configuration du mode sombre */
@layer base {
  html {
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Mode clair (par défaut) */
  html {
    background-color: #ffffff;
    color: #1f2937;
  }

  /* Mode sombre */
  html.dark {
    background-color: #111827;
    color: #f9fafb;
  }

  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: inherit;
    color: inherit;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Styles personnalisés pour le mode sombre */
@layer utilities {
  .dark-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
}

