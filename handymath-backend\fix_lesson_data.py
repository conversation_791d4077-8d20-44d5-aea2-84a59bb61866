#!/usr/bin/env python
"""
Script pour corriger les données des leçons et s'assurer que l'ordre est correct
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import Course, Chapter, Lesson, LessonProgress
from django.contrib.auth import get_user_model

User = get_user_model()

def fix_lesson_order():
    """Corriger l'ordre des leçons pour s'assurer qu'elles commencent à 0"""
    print("🔧 Correction de l'ordre des leçons...")
    
    for chapter in Chapter.objects.all():
        lessons = chapter.lessons.filter(is_published=True).order_by('order')
        print(f"  📚 Chapitre: {chapter.title}")
        
        for index, lesson in enumerate(lessons):
            if lesson.order != index:
                print(f"    ✏️ Correction de l'ordre de '{lesson.title}': {lesson.order} → {index}")
                lesson.order = index
                lesson.save()
            else:
                print(f"    ✅ '{lesson.title}' - ordre correct: {lesson.order}")

def create_missing_progress():
    """Créer les progressions manquantes pour tous les utilisateurs"""
    print("\n📊 Création des progressions manquantes...")
    
    users = User.objects.all()
    lessons = Lesson.objects.filter(is_published=True)
    
    for user in users:
        print(f"  👤 Utilisateur: {user.username}")
        created_count = 0
        
        for lesson in lessons:
            progress, created = LessonProgress.objects.get_or_create(
                user=user,
                lesson=lesson,
                defaults={'completed': False, 'time_spent': 0}
            )
            if created:
                created_count += 1
        
        print(f"    ➕ {created_count} progressions créées")

def check_accessibility():
    """Vérifier l'accessibilité des leçons pour tous les utilisateurs"""
    print("\n🔍 Vérification de l'accessibilité des leçons...")
    
    users = User.objects.all()
    
    for user in users:
        print(f"  👤 Utilisateur: {user.username}")
        
        for course in Course.objects.filter(status='published'):
            if not course.is_accessible_for_user(user):
                print(f"    ❌ Cours non accessible: {course.title}")
                continue

            print(f"    📚 Cours: {course.title}")

            for chapter in course.chapters.all().order_by('order'):
                print(f"      📖 Chapitre: {chapter.title}")
                
                for lesson in chapter.lessons.filter(is_published=True).order_by('order'):
                    is_accessible = lesson.is_accessible_for_user(user)
                    progress = LessonProgress.objects.filter(user=user, lesson=lesson).first()
                    completed = progress.completed if progress else False
                    
                    status_icon = "✅" if completed else ("🔓" if is_accessible else "🔒")
                    print(f"        {status_icon} {lesson.title} (ordre: {lesson.order})")

def main():
    print("🚀 Démarrage de la correction des données des leçons...\n")
    
    try:
        fix_lesson_order()
        create_missing_progress()
        check_accessibility()
        
        print("\n✅ Correction terminée avec succès!")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la correction: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
