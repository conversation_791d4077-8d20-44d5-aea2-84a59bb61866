{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromFunction';\nvar dependencies = ['typed', 'matrix', 'isZero'];\nexport var createMatrixFromFunction = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    isZero\n  } = _ref;\n  /**\n   * Create a matrix by evaluating a generating function at each index.\n   * The simplest overload returns a multi-dimensional array as long as `size`\n   * is an array.\n   * Passing `size` as a Matrix or specifying a `format` will result in\n   * returning a Matrix.\n   *\n   * Syntax:\n   *\n   *    math.matrixFromFunction(size, fn)\n   *    math.matrixFromFunction(size, fn, format)\n   *    math.matrixFromFunction(size, fn, format, datatype)\n   *    math.matrixFromFunction(size, format, fn)\n   *    math.matrixFromFunction(size, format, datatype, fn)\n   *\n   * Where:\n   *\n   *    - `size: (number[] | Matrix)`\n   *      A vector giving the extent of the array to be created in each\n   *      dimension. If size has one entry, a vector is created; if it\n   *      has two, a rectangular array/Matrix is created; if three, a\n   *      three-dimensional array/Matrix is created; and so on.\n   *    - `fn: (index: number[]) => MathType`\n   *      The callback function that will generate the entries of the\n   *      matrix. It is called in turn with the index of each entry of\n   *      the matrix. The index is always an ordinary array of numbers\n   *      with the same length as _size_. So for vectors, you will get\n   *      indices like `[0]` or `[1]`, whereas for matrices, you will\n   *      get indices like `[2, 0]` or `[1,3]`. The return value may\n   *      be any type that can go in an array or Matrix entry, although\n   *      if you supply the _datatype_ argument, you must yourself ensure\n   *      the type of the return value matches. Note that currently,\n   *      your callback _fn_ will receive 0-based indices for the matrix\n   *      entries, regardless of whether matrixFromFunction is invoked\n   *      directly from JavaScript or via the mathjs expression language.\n   *    - `format: 'dense'|'sparse'`\n   *      Specifies the storage format for the resulting Matrix. Note that\n   *      if this argument is given, the return value will always be a\n   *      Matrix (rather than possibly an Array).\n   *    - `datatype: string`\n   *      Specifies the data type of entries of the new matrix. If given,\n   *      it should be the name of a data type that mathjs supports, as\n   *      returned by the math.typeOf function. It is up to the caller\n   *      to make certain that all values returned by _fn_ are consistent\n   *      with this datatype if specified.\n   *\n   * Examples:\n   *\n   *    math.matrixFromFunction([3,3], i => i[0] - i[1]) // an antisymmetric matrix\n   *    math.matrixFromFunction([100, 100], 'sparse', i => i[0] - i[1] === 1 ? 4 : 0) // a sparse subdiagonal matrix\n   *    math.matrixFromFunction([5], i => math.random()) // a random vector\n   *\n   * See also:\n   *\n   *    matrix, typeOf, zeros\n   *\n   * @param {Array | Matrix} size   The size of the matrix to be created\n   * @param {function} fn           Callback function invoked for every entry in the matrix\n   * @param {string} [format]       The Matrix storage format, either `'dense'` or `'sparse'`\n   * @param {string} [datatype]     Type of the values\n   * @return {Array | Matrix} Returns the created matrix\n   */\n  return typed(name, {\n    'Array | Matrix, function, string, string': function Array__Matrix_function_string_string(size, fn, format, datatype) {\n      return _create(size, fn, format, datatype);\n    },\n    'Array | Matrix, function, string': function Array__Matrix_function_string(size, fn, format) {\n      return _create(size, fn, format);\n    },\n    'Matrix, function': function Matrix_function(size, fn) {\n      return _create(size, fn, 'dense');\n    },\n    'Array, function': function Array_function(size, fn) {\n      return _create(size, fn, 'dense').toArray();\n    },\n    'Array | Matrix, string, function': function Array__Matrix_string_function(size, format, fn) {\n      return _create(size, fn, format);\n    },\n    'Array | Matrix, string, string, function': function Array__Matrix_string_string_function(size, format, datatype, fn) {\n      return _create(size, fn, format, datatype);\n    }\n  });\n  function _create(size, fn, format, datatype) {\n    var m;\n    if (datatype !== undefined) {\n      m = matrix(format, datatype);\n    } else {\n      m = matrix(format);\n    }\n    m.resize(size);\n    m.forEach(function (_, index) {\n      var val = fn(index);\n      if (isZero(val)) return;\n      m.set(index, val);\n    });\n    return m;\n  }\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatrixFromFunction", "_ref", "typed", "matrix", "isZero", "Array__Matrix_function_string_string", "size", "fn", "format", "datatype", "_create", "Array__Matrix_function_string", "Matrix_function", "Array_function", "toArray", "Array__Matrix_string_function", "Array__Matrix_string_string_function", "m", "undefined", "resize", "for<PERSON>ach", "_", "index", "val", "set"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/matrix/matrixFromFunction.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromFunction';\nvar dependencies = ['typed', 'matrix', 'isZero'];\nexport var createMatrixFromFunction = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    isZero\n  } = _ref;\n  /**\n   * Create a matrix by evaluating a generating function at each index.\n   * The simplest overload returns a multi-dimensional array as long as `size`\n   * is an array.\n   * Passing `size` as a Matrix or specifying a `format` will result in\n   * returning a Matrix.\n   *\n   * Syntax:\n   *\n   *    math.matrixFromFunction(size, fn)\n   *    math.matrixFromFunction(size, fn, format)\n   *    math.matrixFromFunction(size, fn, format, datatype)\n   *    math.matrixFromFunction(size, format, fn)\n   *    math.matrixFromFunction(size, format, datatype, fn)\n   *\n   * Where:\n   *\n   *    - `size: (number[] | Matrix)`\n   *      A vector giving the extent of the array to be created in each\n   *      dimension. If size has one entry, a vector is created; if it\n   *      has two, a rectangular array/Matrix is created; if three, a\n   *      three-dimensional array/Matrix is created; and so on.\n   *    - `fn: (index: number[]) => MathType`\n   *      The callback function that will generate the entries of the\n   *      matrix. It is called in turn with the index of each entry of\n   *      the matrix. The index is always an ordinary array of numbers\n   *      with the same length as _size_. So for vectors, you will get\n   *      indices like `[0]` or `[1]`, whereas for matrices, you will\n   *      get indices like `[2, 0]` or `[1,3]`. The return value may\n   *      be any type that can go in an array or Matrix entry, although\n   *      if you supply the _datatype_ argument, you must yourself ensure\n   *      the type of the return value matches. Note that currently,\n   *      your callback _fn_ will receive 0-based indices for the matrix\n   *      entries, regardless of whether matrixFromFunction is invoked\n   *      directly from JavaScript or via the mathjs expression language.\n   *    - `format: 'dense'|'sparse'`\n   *      Specifies the storage format for the resulting Matrix. Note that\n   *      if this argument is given, the return value will always be a\n   *      Matrix (rather than possibly an Array).\n   *    - `datatype: string`\n   *      Specifies the data type of entries of the new matrix. If given,\n   *      it should be the name of a data type that mathjs supports, as\n   *      returned by the math.typeOf function. It is up to the caller\n   *      to make certain that all values returned by _fn_ are consistent\n   *      with this datatype if specified.\n   *\n   * Examples:\n   *\n   *    math.matrixFromFunction([3,3], i => i[0] - i[1]) // an antisymmetric matrix\n   *    math.matrixFromFunction([100, 100], 'sparse', i => i[0] - i[1] === 1 ? 4 : 0) // a sparse subdiagonal matrix\n   *    math.matrixFromFunction([5], i => math.random()) // a random vector\n   *\n   * See also:\n   *\n   *    matrix, typeOf, zeros\n   *\n   * @param {Array | Matrix} size   The size of the matrix to be created\n   * @param {function} fn           Callback function invoked for every entry in the matrix\n   * @param {string} [format]       The Matrix storage format, either `'dense'` or `'sparse'`\n   * @param {string} [datatype]     Type of the values\n   * @return {Array | Matrix} Returns the created matrix\n   */\n  return typed(name, {\n    'Array | Matrix, function, string, string': function Array__Matrix_function_string_string(size, fn, format, datatype) {\n      return _create(size, fn, format, datatype);\n    },\n    'Array | Matrix, function, string': function Array__Matrix_function_string(size, fn, format) {\n      return _create(size, fn, format);\n    },\n    'Matrix, function': function Matrix_function(size, fn) {\n      return _create(size, fn, 'dense');\n    },\n    'Array, function': function Array_function(size, fn) {\n      return _create(size, fn, 'dense').toArray();\n    },\n    'Array | Matrix, string, function': function Array__Matrix_string_function(size, format, fn) {\n      return _create(size, fn, format);\n    },\n    'Array | Matrix, string, string, function': function Array__Matrix_string_string_function(size, format, datatype, fn) {\n      return _create(size, fn, format, datatype);\n    }\n  });\n  function _create(size, fn, format, datatype) {\n    var m;\n    if (datatype !== undefined) {\n      m = matrix(format, datatype);\n    } else {\n      m = matrix(format);\n    }\n    m.resize(size);\n    m.forEach(function (_, index) {\n      var val = fn(index);\n      if (isZero(val)) return;\n      m.set(index, val);\n    });\n    return m;\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,oBAAoB;AAC/B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAChD,OAAO,IAAIC,wBAAwB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACvF,IAAI;IACFC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGH,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,0CAA0C,EAAE,SAASO,oCAAoCA,CAACC,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,EAAE;MACpH,OAAOC,OAAO,CAACJ,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,CAAC;IAC5C,CAAC;IACD,kCAAkC,EAAE,SAASE,6BAA6BA,CAACL,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAE;MAC3F,OAAOE,OAAO,CAACJ,IAAI,EAAEC,EAAE,EAAEC,MAAM,CAAC;IAClC,CAAC;IACD,kBAAkB,EAAE,SAASI,eAAeA,CAACN,IAAI,EAAEC,EAAE,EAAE;MACrD,OAAOG,OAAO,CAACJ,IAAI,EAAEC,EAAE,EAAE,OAAO,CAAC;IACnC,CAAC;IACD,iBAAiB,EAAE,SAASM,cAAcA,CAACP,IAAI,EAAEC,EAAE,EAAE;MACnD,OAAOG,OAAO,CAACJ,IAAI,EAAEC,EAAE,EAAE,OAAO,CAAC,CAACO,OAAO,CAAC,CAAC;IAC7C,CAAC;IACD,kCAAkC,EAAE,SAASC,6BAA6BA,CAACT,IAAI,EAAEE,MAAM,EAAED,EAAE,EAAE;MAC3F,OAAOG,OAAO,CAACJ,IAAI,EAAEC,EAAE,EAAEC,MAAM,CAAC;IAClC,CAAC;IACD,0CAA0C,EAAE,SAASQ,oCAAoCA,CAACV,IAAI,EAAEE,MAAM,EAAEC,QAAQ,EAAEF,EAAE,EAAE;MACpH,OAAOG,OAAO,CAACJ,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,CAAC;IAC5C;EACF,CAAC,CAAC;EACF,SAASC,OAAOA,CAACJ,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAC3C,IAAIQ,CAAC;IACL,IAAIR,QAAQ,KAAKS,SAAS,EAAE;MAC1BD,CAAC,GAAGd,MAAM,CAACK,MAAM,EAAEC,QAAQ,CAAC;IAC9B,CAAC,MAAM;MACLQ,CAAC,GAAGd,MAAM,CAACK,MAAM,CAAC;IACpB;IACAS,CAAC,CAACE,MAAM,CAACb,IAAI,CAAC;IACdW,CAAC,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAEC,KAAK,EAAE;MAC5B,IAAIC,GAAG,GAAGhB,EAAE,CAACe,KAAK,CAAC;MACnB,IAAIlB,MAAM,CAACmB,GAAG,CAAC,EAAE;MACjBN,CAAC,CAACO,GAAG,CAACF,KAAK,EAAEC,GAAG,CAAC;IACnB,CAAC,CAAC;IACF,OAAON,CAAC;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}