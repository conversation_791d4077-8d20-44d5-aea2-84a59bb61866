# Generated by Django 5.2.1 on 2025-06-04 00:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0007_add_gamification_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='LearningPath',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('difficulty', models.CharField(choices=[('beginner', 'Débutant'), ('intermediate', 'Intermédiaire'), ('advanced', 'Avancé'), ('expert', 'Expert')], default='beginner', max_length=20)),
                ('estimated_duration', models.IntegerField(default=300, help_text='Durée estimée en minutes')),
                ('thumbnail', models.<PERSON><PERSON><PERSON><PERSON>(default='🎯', max_length=10)),
                ('is_featured', models.BooleanField(default=False)),
                ('is_published', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': "Parcours d'apprentissage",
                'verbose_name_plural': "Parcours d'apprentissage",
            },
        ),
        migrations.AlterModelOptions(
            name='course',
            options={'ordering': ['order', 'title'], 'verbose_name': 'Cours', 'verbose_name_plural': 'Cours'},
        ),
        migrations.AddField(
            model_name='course',
            name='estimated_duration',
            field=models.IntegerField(default=60, help_text='Durée estimée en minutes'),
        ),
        migrations.AddField(
            model_name='course',
            name='is_featured',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='course',
            name='order',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='course',
            name='prerequisites',
            field=models.ManyToManyField(blank=True, related_name='unlocks', to='api.course'),
        ),
        migrations.AddField(
            model_name='course',
            name='status',
            field=models.CharField(choices=[('draft', 'Brouillon'), ('published', 'Publié'), ('archived', 'Archivé')], default='published', max_length=20),
        ),
        migrations.AddField(
            model_name='course',
            name='thumbnail',
            field=models.CharField(default='📚', max_length=10),
        ),
        migrations.AddField(
            model_name='course',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='Chapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('order', models.IntegerField(default=0)),
                ('is_published', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chapters', to='api.course')),
            ],
            options={
                'verbose_name': 'Chapitre',
                'verbose_name_plural': 'Chapitres',
                'ordering': ['order', 'title'],
                'unique_together': {('course', 'order')},
            },
        ),
        migrations.CreateModel(
            name='LearningPathCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.IntegerField(default=0)),
                ('is_optional', models.BooleanField(default=False)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.course')),
                ('learning_path', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.learningpath')),
            ],
            options={
                'verbose_name': 'Cours du parcours',
                'verbose_name_plural': 'Cours des parcours',
                'ordering': ['order'],
                'unique_together': {('learning_path', 'course')},
            },
        ),
        migrations.AddField(
            model_name='learningpath',
            name='courses',
            field=models.ManyToManyField(related_name='learning_paths', through='api.LearningPathCourse', to='api.course'),
        ),
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('lesson_type', models.CharField(choices=[('theory', 'Théorie'), ('example', 'Exemple'), ('exercise', 'Exercice'), ('quiz', 'Quiz'), ('video', 'Vidéo')], default='theory', max_length=20)),
                ('order', models.IntegerField(default=0)),
                ('estimated_duration', models.IntegerField(default=10, help_text='Durée estimée en minutes')),
                ('is_published', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lessons', to='api.chapter')),
                ('exercise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lessons', to='api.exercise')),
            ],
            options={
                'verbose_name': 'Leçon',
                'verbose_name_plural': 'Leçons',
                'ordering': ['order', 'title'],
                'unique_together': {('chapter', 'order')},
            },
        ),
        migrations.CreateModel(
            name='CourseEnrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrolled_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='api.course')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Inscription au cours',
                'verbose_name_plural': 'Inscriptions aux cours',
                'unique_together': {('user', 'course')},
            },
        ),
        migrations.CreateModel(
            name='LessonProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('completed', models.BooleanField(default=False)),
                ('time_spent', models.IntegerField(default=0, help_text='Temps passé en secondes')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress', to='api.lesson')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_progress', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Progression de leçon',
                'verbose_name_plural': 'Progressions de leçons',
                'unique_together': {('user', 'lesson')},
            },
        ),
    ]
