import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../components/NotificationSystem';
import StudentLayout from '../components/StudentLayout';
import Pagination from '../components/Pagination';
import api from '../services/api';

// Types
interface Exercise {
  id: number;
  title: string;
  description: string;
  course: {
    id: number;
    title: string;
    level: string;
  };
  difficulty: string;
  difficulty_display: string;
  exercise_type: string;
  type_display: string;
  points: number;
  time_limit: number;
  user_attempt?: {
    completed: boolean;
    is_correct: boolean;
    points_earned: number;
    time_taken: number;
  } | null;
  created_at: string;
}

interface Filters {
  difficulty: string;
  type: string;
  course: string;
}

interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
}

const ExercisesPage: React.FC = () => {
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // Helper functions for notifications
  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message });
  };
  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };
  const showWarning = (title: string, message: string) => {
    addNotification({ type: 'warning', title, message });
  };

  // State
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    difficulty: '',
    type: '',
    course: ''
  });
  const [availableFilters, setAvailableFilters] = useState<any>({});
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    itemsPerPage: 9,
    totalItems: 0,
    totalPages: 1
  });

  // Effects
  useEffect(() => {
    if (user) {
      fetchExercises();
    }
  }, [user, filters]);

  // API Functions
  const fetchExercises = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filters.difficulty) params.append('difficulty', filters.difficulty);
      if (filters.type) params.append('type', filters.type);
      if (filters.course) params.append('course', filters.course);

      const response = await api.get(`/exercises/?${params.toString()}`);
      if (response.data) {
        setExercises(response.data.exercises || []);
        setAvailableFilters(response.data.filters || {});
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des exercices:', error);
      showError('Erreur', 'Impossible de charger les exercices');
    } finally {
      setLoading(false);
    }
  };

  // Event Handlers
  const handleFilterChange = (filterType: keyof Filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      difficulty: '',
      type: '',
      course: ''
    });
  };

  // Utility Functions
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'hard':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'equation':
        return '📐';
      case 'multiple_choice':
        return '📝';
      case 'calculation':
        return '🧮';
      case 'proof':
        return '📋';
      default:
        return '📚';
    }
  };

  const getStatusIcon = (exercise: Exercise) => {
    if (!exercise.user_attempt) return '🆕';
    if (exercise.user_attempt.completed) {
      return exercise.user_attempt.is_correct ? '✅' : '❌';
    }
    return '⏳';
  };

  const getStatusText = (exercise: Exercise) => {
    if (!exercise.user_attempt) return 'Nouveau';
    if (exercise.user_attempt.completed) {
      return exercise.user_attempt.is_correct ? 'Réussi' : 'Échoué';
    }
    return 'En cours';
  };

  // Loading and Error States
  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Exercices Interactifs</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour accéder aux exercices.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  // Main Render
  return (
    <StudentLayout
      title="Exercices Interactifs"
      subtitle="Pratiquez avec nos exercices de mathématiques"
    >
      <div className="space-y-6">

      {/* Filtres */}
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          🔍 Filtres
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {/* Filtre difficulté */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Difficulté
            </label>
            <select
              value={filters.difficulty}
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">Toutes</option>
              {availableFilters.difficulties?.map(([value, label]: [string, string]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>

          {/* Filtre type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type
            </label>
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">Tous</option>
              {availableFilters.types?.map(([value, label]: [string, string]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>

          {/* Bouton effacer */}
          <div className="flex items-end">
            <button
              onClick={clearFilters}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              Effacer filtres
            </button>
          </div>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {exercises.length} exercice{exercises.length !== 1 ? 's' : ''} trouvé{exercises.length !== 1 ? 's' : ''}
        </div>
      </motion.div>

      {/* État de chargement */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement des exercices...</p>
        </div>
      )}

      {/* Liste des exercices */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {exercises.map((exercise, index) => (
            <motion.div
              key={exercise.id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="p-6">
                {/* En-tête de l'exercice */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getTypeIcon(exercise.exercise_type)}</span>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                        {exercise.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {exercise.course.title}
                      </p>
                    </div>
                  </div>
                  <span className="text-xl">{getStatusIcon(exercise)}</span>
                </div>

                {/* Description */}
                <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                  {exercise.description}
                </p>

                {/* Badges */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                    {exercise.difficulty_display}
                  </span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300">
                    {exercise.type_display}
                  </span>
                </div>

                {/* Informations */}
                <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                  <div className="flex items-center">
                    <span className="mr-1">🏆</span>
                    <span>{exercise.points} pts</span>
                  </div>
                  <div className="flex items-center">
                    <span className="mr-1">⏱</span>
                    <span>{Math.floor(exercise.time_limit / 60)}min</span>
                  </div>
                </div>

                {/* Statut et résultats */}
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Statut:</span>
                    <span className={`font-medium ${
                      exercise.user_attempt?.completed
                        ? exercise.user_attempt.is_correct
                          ? 'text-green-600'
                          : 'text-red-600'
                        : 'text-blue-600'
                    }`}>
                      {getStatusText(exercise)}
                    </span>
                  </div>
                  {exercise.user_attempt?.completed && (
                    <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                      Points obtenus: {exercise.user_attempt.points_earned}/{exercise.points}
                    </div>
                  )}
                </div>

                {/* Bouton d'action */}
                <a
                  href={`/exercises/${exercise.id}`}
                  className={`block w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${
                    exercise.user_attempt?.completed
                      ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300'
                      : 'bg-primary-600 hover:bg-primary-700 text-white'
                  }`}
                >
                  {exercise.user_attempt?.completed ? 'Revoir' : 'Commencer'}
                </a>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Message si aucun exercice */}
      {!loading && exercises.length === 0 && (
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-xl font-semibold mb-2">Aucun exercice trouvé</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Essayez de modifier vos filtres ou revenez plus tard.
          </p>
          <button
            onClick={clearFilters}
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Effacer les filtres
          </button>
        </motion.div>
      )}
      </div>
      </div>
    </StudentLayout>
  );
};

export default ExercisesPage;
