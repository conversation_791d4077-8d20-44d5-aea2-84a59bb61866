{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isNumeric';\nvar dependencies = ['typed'];\nexport var createIsNumeric = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is an numeric value.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isNumeric(x)\n   *\n   * Examples:\n   *\n   *    math.isNumeric(2)                     // returns true\n   *    math.isNumeric('2')                   // returns false\n   *    math.hasNumericValue('2')             // returns true\n   *    math.isNumeric(0)                     // returns true\n   *    math.isNumeric(math.bignumber('42'))  // returns true\n   *    math.isNumeric(math.bigint('42'))     // returns true\n   *    math.isNumeric(math.fraction(4))      // returns true\n   *    math.isNumeric(math.complex('2-4i'))  // returns false\n   *    math.isNumeric([2.3, 'foo', false])   // returns [true, false, true]\n   *\n   * See also:\n   *\n   *    isZero, isPositive, isNegative, isInteger, hasNumericValue\n   *\n   * @param {*} x       Value to be tested\n   * @return {boolean}  Returns true when `x` is a `number`, `BigNumber`,\n   *                    `Fraction`, or `boolean`. Returns false for other types.\n   *                    Throws an error in case of unknown types.\n   */\n  return typed(name, {\n    'number | BigNumber | bigint | Fraction | boolean': () => true,\n    'Complex | Unit | string | null | undefined | Node': () => false,\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "factory", "name", "dependencies", "createIsNumeric", "_ref", "typed", "number | BigNumber | bigint | Fraction | boolean", "Complex | Unit | string | null | undefined | Node", "referToSelf", "self", "x"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/isNumeric.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isNumeric';\nvar dependencies = ['typed'];\nexport var createIsNumeric = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is an numeric value.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isNumeric(x)\n   *\n   * Examples:\n   *\n   *    math.isNumeric(2)                     // returns true\n   *    math.isNumeric('2')                   // returns false\n   *    math.hasNumericValue('2')             // returns true\n   *    math.isNumeric(0)                     // returns true\n   *    math.isNumeric(math.bignumber('42'))  // returns true\n   *    math.isNumeric(math.bigint('42'))     // returns true\n   *    math.isNumeric(math.fraction(4))      // returns true\n   *    math.isNumeric(math.complex('2-4i'))  // returns false\n   *    math.isNumeric([2.3, 'foo', false])   // returns [true, false, true]\n   *\n   * See also:\n   *\n   *    isZero, isPositive, isNegative, isInteger, hasNumericValue\n   *\n   * @param {*} x       Value to be tested\n   * @return {boolean}  Returns true when `x` is a `number`, `BigNumber`,\n   *                    `Fraction`, or `boolean`. Returns false for other types.\n   *                    Throws an error in case of unknown types.\n   */\n  return typed(name, {\n    'number | BigNumber | bigint | Fraction | boolean': () => true,\n    'Complex | Unit | string | null | undefined | Node': () => false,\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,WAAW;AACtB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC9E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,kDAAkD,EAAEK,CAAA,KAAM,IAAI;IAC9D,mDAAmD,EAAEC,CAAA,KAAM,KAAK;IAChE,gBAAgB,EAAEF,KAAK,CAACG,WAAW,CAACC,IAAI,IAAIC,CAAC,IAAIX,OAAO,CAACW,CAAC,EAAED,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}