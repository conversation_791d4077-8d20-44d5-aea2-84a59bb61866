{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CoursesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CoursesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    level: '',\n    featured: false\n  });\n  const [pagination, setPagination] = useState({\n    currentPage: 1,\n    itemsPerPage: 6,\n    totalItems: 0,\n    totalPages: 1\n  });\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters]);\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        const allCourses = [{\n          id: 1,\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          is_featured: true,\n          chapters_count: 4,\n          lessons_count: 12,\n          progress_percentage: 45,\n          is_accessible: true,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        }, {\n          id: 2,\n          title: 'Géométrie euclidienne',\n          description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 240,\n          is_featured: false,\n          chapters_count: 6,\n          lessons_count: 18,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-16T14:20:00Z'\n        }, {\n          id: 3,\n          title: 'Calcul différentiel',\n          description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '∫',\n          estimated_duration: 360,\n          is_featured: true,\n          chapters_count: 8,\n          lessons_count: 24,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [{\n            id: 1,\n            title: 'Algèbre de base',\n            progress: 45\n          }],\n          created_at: '2024-01-17T09:45:00Z'\n        }, {\n          id: 4,\n          title: 'Statistiques descriptives',\n          description: 'Apprenez les bases des statistiques et de l\\'analyse de données.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📊',\n          estimated_duration: 200,\n          is_featured: false,\n          chapters_count: 5,\n          lessons_count: 15,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-18T11:30:00Z'\n        }, {\n          id: 5,\n          title: 'Trigonométrie',\n          description: 'Maîtrisez les fonctions trigonométriques et leurs applications.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 280,\n          is_featured: true,\n          chapters_count: 6,\n          lessons_count: 20,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-19T14:15:00Z'\n        }, {\n          id: 6,\n          title: 'Analyse complexe',\n          description: 'Explorez les nombres complexes et leurs propriétés.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '🔢',\n          estimated_duration: 400,\n          is_featured: false,\n          chapters_count: 10,\n          lessons_count: 30,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [{\n            id: 3,\n            title: 'Calcul différentiel',\n            progress: 0\n          }],\n          created_at: '2024-01-20T16:45:00Z'\n        }];\n        setCourses(allCourses);\n        setPagination(prev => ({\n          ...prev,\n          totalItems: allCourses.length,\n          totalPages: Math.ceil(allCourses.length / prev.itemsPerPage)\n        }));\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async (courseId, courseTitle) => {\n    try {\n      setCourses(courses.map(course => course.id === courseId ? {\n        ...course,\n        is_enrolled: true,\n        enrollment_date: new Date().toISOString()\n      } : course));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  const filteredCourses = courses.filter(course => {\n    if (filters.level && course.level !== filters.level) return false;\n    if (filters.featured && !course.is_featured) return false;\n    return true;\n  });\n\n  // Pagination des cours filtrés\n  const paginatedCourses = filteredCourses.slice((pagination.currentPage - 1) * pagination.itemsPerPage, pagination.currentPage * pagination.itemsPerPage);\n\n  // Gestion de la pagination\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      currentPage: page\n    }));\n  };\n  const handleItemsPerPageChange = itemsPerPage => {\n    setPagination(prev => ({\n      ...prev,\n      itemsPerPage,\n      currentPage: 1,\n      totalPages: Math.ceil(filteredCourses.length / itemsPerPage)\n    }));\n  };\n\n  // Mettre à jour la pagination quand les filtres changent\n  useEffect(() => {\n    setPagination(prev => ({\n      ...prev,\n      currentPage: 1,\n      totalItems: filteredCourses.length,\n      totalPages: Math.ceil(filteredCourses.length / prev.itemsPerPage)\n    }));\n  }, [filteredCourses.length]);\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours Structur\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n      className: \"text-4xl font-bold mb-8 text-center text-primary-600\",\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: \"Cours Structur\\xE9s\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Filtres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Niveau\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.level,\n            onChange: e => setFilters(prev => ({\n              ...prev,\n              level: e.target.value\n            })),\n            className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Tous les niveaux\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"beginner\",\n              children: \"D\\xE9butant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"intermediate\",\n              children: \"Interm\\xE9diaire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"advanced\",\n              children: \"Avanc\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: filters.featured,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                featured: e.target.checked\n              })),\n              className: \"mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: \"Cours vedettes uniquement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setFilters({\n              level: '',\n              featured: false\n            }),\n            className: \"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n            children: \"Effacer filtres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600 dark:text-gray-400\",\n        children: [filteredCourses.length, \" cours trouv\\xE9\", filteredCourses.length !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 dark:text-gray-400\",\n        children: \"Chargement des cours...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 9\n    }, this) : filteredCourses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-2\",\n        children: \"Aucun cours trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n        children: \"Essayez de modifier vos filtres ou revenez plus tard.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setFilters({\n          level: '',\n          featured: false\n        }),\n        className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n        children: \"Voir tous les cours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: filteredCourses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl mr-3\",\n                children: course.thumbnail\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-lg text-gray-900 dark:text-white\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 23\n                }, this), course.is_featured && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\",\n                  children: \"Vedette\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`,\n              children: course.level_display\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2\",\n            children: course.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.chapters_count, \" chapitres\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.lessons_count, \" le\\xE7ons\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: \"\\u23F1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatDuration(course.estimated_duration)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.progress_percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 17\n          }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Progression\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.progress_percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${course.progress_percentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 19\n          }, this), course.prerequisites.length > 0 && !course.is_accessible && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\",\n              children: \"Pr\\xE9requis requis :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 21\n            }, this), course.prerequisites.map(prereq => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-yellow-700 dark:text-yellow-300\",\n              children: [\"\\u2022 \", prereq.title, \" (\", prereq.progress, \"% termin\\xE9)\"]\n            }, prereq.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 23\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [!course.is_accessible ? /*#__PURE__*/_jsxDEV(\"button\", {\n              disabled: true,\n              className: \"w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed\",\n              children: \"Non accessible\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 21\n            }, this) : !course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleEnroll(course.id, course.title),\n              className: \"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n              children: \"S'inscrire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/courses/${course.id}`,\n              className: \"block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors\",\n              children: course.progress_percentage > 0 ? 'Continuer' : 'Commencer'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/courses/${course.id}`,\n              className: \"block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors\",\n              children: \"Voir les d\\xE9tails\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 15\n        }, this)\n      }, course.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 5\n  }, this);\n};\n_s(CoursesPage, \"mdDrts9J7b5ucCkcJzCK/jjYjVk=\", false, function () {\n  return [useAuth];\n});\n_c = CoursesPage;\nexport default CoursesPage;\nvar _c;\n$RefreshReg$(_c, \"CoursesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "jsxDEV", "_jsxDEV", "CoursesPage", "_s", "user", "courses", "setCourses", "loading", "setLoading", "filters", "setFilters", "level", "featured", "pagination", "setPagination", "currentPage", "itemsPerPage", "totalItems", "totalPages", "fetchCourses", "setTimeout", "allCourses", "id", "title", "description", "level_display", "thumbnail", "estimated_duration", "is_featured", "chapters_count", "lessons_count", "progress_percentage", "is_accessible", "is_enrolled", "enrollment_date", "prerequisites", "created_at", "progress", "prev", "length", "Math", "ceil", "error", "console", "handleEnroll", "courseId", "courseTitle", "map", "course", "Date", "toISOString", "alert", "getLevelColor", "formatDuration", "minutes", "hours", "floor", "mins", "filteredCourses", "filter", "paginatedCourses", "slice", "handlePageChange", "page", "handleItemsPerPageChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "h1", "initial", "opacity", "y", "animate", "div", "transition", "delay", "value", "onChange", "e", "target", "type", "checked", "onClick", "index", "style", "width", "prereq", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/CoursesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentLayout from '../components/StudentLayout';\nimport Pagination from '../components/Pagination';\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  is_featured: boolean;\n  chapters_count: number;\n  lessons_count: number;\n  progress_percentage: number;\n  is_accessible: boolean;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\ninterface Filters {\n  level: string;\n  featured: boolean;\n}\n\ninterface PaginationState {\n  currentPage: number;\n  itemsPerPage: number;\n  totalItems: number;\n  totalPages: number;\n}\n\nconst CoursesPage: React.FC = () => {\n  const { user } = useAuth();\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    level: '',\n    featured: false\n  });\n  const [pagination, setPagination] = useState<PaginationState>({\n    currentPage: 1,\n    itemsPerPage: 6,\n    totalItems: 0,\n    totalPages: 1\n  });\n\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters]);\n\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        const allCourses = [\n          {\n            id: 1,\n            title: 'Algèbre de base',\n            description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n            level: 'beginner',\n            level_display: 'Débutant',\n            thumbnail: '📚',\n            estimated_duration: 180,\n            is_featured: true,\n            chapters_count: 4,\n            lessons_count: 12,\n            progress_percentage: 45,\n            is_accessible: true,\n            is_enrolled: true,\n            enrollment_date: '2024-01-15T10:30:00Z',\n            prerequisites: [],\n            created_at: '2024-01-15T10:30:00Z'\n          },\n          {\n            id: 2,\n            title: 'Géométrie euclidienne',\n            description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n            level: 'intermediate',\n            level_display: 'Intermédiaire',\n            thumbnail: '📐',\n            estimated_duration: 240,\n            is_featured: false,\n            chapters_count: 6,\n            lessons_count: 18,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-16T14:20:00Z'\n          },\n          {\n            id: 3,\n            title: 'Calcul différentiel',\n            description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n            level: 'advanced',\n            level_display: 'Avancé',\n            thumbnail: '∫',\n            estimated_duration: 360,\n            is_featured: true,\n            chapters_count: 8,\n            lessons_count: 24,\n            progress_percentage: 0,\n            is_accessible: false,\n            is_enrolled: false,\n            prerequisites: [\n              { id: 1, title: 'Algèbre de base', progress: 45 }\n            ],\n            created_at: '2024-01-17T09:45:00Z'\n          },\n          {\n            id: 4,\n            title: 'Statistiques descriptives',\n            description: 'Apprenez les bases des statistiques et de l\\'analyse de données.',\n            level: 'beginner',\n            level_display: 'Débutant',\n            thumbnail: '📊',\n            estimated_duration: 200,\n            is_featured: false,\n            chapters_count: 5,\n            lessons_count: 15,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-18T11:30:00Z'\n          },\n          {\n            id: 5,\n            title: 'Trigonométrie',\n            description: 'Maîtrisez les fonctions trigonométriques et leurs applications.',\n            level: 'intermediate',\n            level_display: 'Intermédiaire',\n            thumbnail: '📐',\n            estimated_duration: 280,\n            is_featured: true,\n            chapters_count: 6,\n            lessons_count: 20,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-19T14:15:00Z'\n          },\n          {\n            id: 6,\n            title: 'Analyse complexe',\n            description: 'Explorez les nombres complexes et leurs propriétés.',\n            level: 'advanced',\n            level_display: 'Avancé',\n            thumbnail: '🔢',\n            estimated_duration: 400,\n            is_featured: false,\n            chapters_count: 10,\n            lessons_count: 30,\n            progress_percentage: 0,\n            is_accessible: false,\n            is_enrolled: false,\n            prerequisites: [\n              { id: 3, title: 'Calcul différentiel', progress: 0 }\n            ],\n            created_at: '2024-01-20T16:45:00Z'\n          }\n        ];\n\n        setCourses(allCourses);\n        setPagination(prev => ({\n          ...prev,\n          totalItems: allCourses.length,\n          totalPages: Math.ceil(allCourses.length / prev.itemsPerPage)\n        }));\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async (courseId: number, courseTitle: string) => {\n    try {\n      setCourses(courses.map(course => \n        course.id === courseId \n          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }\n          : course\n      ));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  const filteredCourses = courses.filter(course => {\n    if (filters.level && course.level !== filters.level) return false;\n    if (filters.featured && !course.is_featured) return false;\n    return true;\n  });\n\n  // Pagination des cours filtrés\n  const paginatedCourses = filteredCourses.slice(\n    (pagination.currentPage - 1) * pagination.itemsPerPage,\n    pagination.currentPage * pagination.itemsPerPage\n  );\n\n  // Gestion de la pagination\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, currentPage: page }));\n  };\n\n  const handleItemsPerPageChange = (itemsPerPage: number) => {\n    setPagination(prev => ({\n      ...prev,\n      itemsPerPage,\n      currentPage: 1,\n      totalPages: Math.ceil(filteredCourses.length / itemsPerPage)\n    }));\n  };\n\n  // Mettre à jour la pagination quand les filtres changent\n  useEffect(() => {\n    setPagination(prev => ({\n      ...prev,\n      currentPage: 1,\n      totalItems: filteredCourses.length,\n      totalPages: Math.ceil(filteredCourses.length / prev.itemsPerPage)\n    }));\n  }, [filteredCourses.length]);\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours Structurés</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <motion.h1\n        className=\"text-4xl font-bold mb-8 text-center text-primary-600\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        Cours Structurés\n      </motion.h1>\n\n      <motion.div\n        className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <h2 className=\"text-xl font-semibold mb-4\">\n          Filtres\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Niveau\n            </label>\n            <select\n              value={filters.level}\n              onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Tous les niveaux</option>\n              <option value=\"beginner\">Débutant</option>\n              <option value=\"intermediate\">Intermédiaire</option>\n              <option value=\"advanced\">Avancé</option>\n            </select>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Type\n            </label>\n            <label className=\"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700\">\n              <input\n                type=\"checkbox\"\n                checked={filters.featured}\n                onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}\n                className=\"mr-3\"\n              />\n              <span className=\"text-gray-900 dark:text-white\">Cours vedettes uniquement</span>\n            </label>\n          </div>\n          \n          <div className=\"flex items-end\">\n            <button\n              onClick={() => setFilters({ level: '', featured: false })}\n              className=\"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n            >\n              Effacer filtres\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n          {filteredCourses.length} cours trouvé{filteredCourses.length !== 1 ? 's' : ''}\n        </div>\n      </motion.div>\n\n      {loading ? (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement des cours...</p>\n        </div>\n      ) : filteredCourses.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-xl font-semibold mb-2\">Aucun cours trouvé</h3>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            Essayez de modifier vos filtres ou revenez plus tard.\n          </p>\n          <button\n            onClick={() => setFilters({ level: '', featured: false })}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n          >\n            Voir tous les cours\n          </button>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredCourses.map((course, index) => (\n            <motion.div\n              key={course.id}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-3xl mr-3\">{course.thumbnail}</span>\n                    <div>\n                      <h3 className=\"font-semibold text-lg text-gray-900 dark:text-white\">\n                        {course.title}\n                      </h3>\n                      {course.is_featured && (\n                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                          Vedette\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>\n                    {course.level_display}\n                  </span>\n                </div>\n\n                <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2\">\n                  {course.description}\n                </p>\n\n                <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  <div className=\"flex items-center\">\n                    <span>{course.chapters_count} chapitres</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span>{course.lessons_count} leçons</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">⏱</span>\n                    <span>{formatDuration(course.estimated_duration)}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span>{course.progress_percentage}%</span>\n                  </div>\n                </div>\n\n                {course.is_enrolled && (\n                  <div className=\"mb-4\">\n                    <div className=\"flex justify-between text-sm mb-1\">\n                      <span>Progression</span>\n                      <span>{course.progress_percentage}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div\n                        className=\"bg-primary-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${course.progress_percentage}%` }}\n                      />\n                    </div>\n                  </div>\n                )}\n\n                {course.prerequisites.length > 0 && !course.is_accessible && (\n                  <div className=\"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg\">\n                    <p className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                      Prérequis requis :\n                    </p>\n                    {course.prerequisites.map(prereq => (\n                      <div key={prereq.id} className=\"text-xs text-yellow-700 dark:text-yellow-300\">\n                        • {prereq.title} ({prereq.progress}% terminé)\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                <div className=\"space-y-2\">\n                  {!course.is_accessible ? (\n                    <button\n                      disabled\n                      className=\"w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed\"\n                    >\n                      Non accessible\n                    </button>\n                  ) : !course.is_enrolled ? (\n                    <button\n                      onClick={() => handleEnroll(course.id, course.title)}\n                      className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n                    >\n                      S'inscrire\n                    </button>\n                  ) : (\n                    <a\n                      href={`/courses/${course.id}`}\n                      className=\"block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors\"\n                    >\n                      {course.progress_percentage > 0 ? 'Continuer' : 'Commencer'}\n                    </a>\n                  )}\n                  \n                  <a\n                    href={`/courses/${course.id}`}\n                    className=\"block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors\"\n                  >\n                    Voir les détails\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CoursesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuClD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAU;IAC9Ce,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAkB;IAC5DmB,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEFrB,SAAS,CAAC,MAAM;IACd,IAAIO,IAAI,EAAE;MACRe,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,IAAI,EAAEK,OAAO,CAAC,CAAC;EAEnB,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChBY,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG,CACjB;UACEC,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,iBAAiB;UACxBC,WAAW,EAAE,yGAAyG;UACtHb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,UAAU;UACzBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,EAAE;UACvBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE,sBAAsB;UACvCC,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,uBAAuB;UAC9BC,WAAW,EAAE,oFAAoF;UACjGb,KAAK,EAAE,cAAc;UACrBc,aAAa,EAAE,eAAe;UAC9BC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,qBAAqB;UAC5BC,WAAW,EAAE,4DAA4D;UACzEb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,QAAQ;UACvBC,SAAS,EAAE,GAAG;UACdC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,KAAK;UACpBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,CACb;YAAEb,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE,iBAAiB;YAAEc,QAAQ,EAAE;UAAG,CAAC,CAClD;UACDD,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,2BAA2B;UAClCC,WAAW,EAAE,kEAAkE;UAC/Eb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,UAAU;UACzBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE,iEAAiE;UAC9Eb,KAAK,EAAE,cAAc;UACrBc,aAAa,EAAE,eAAe;UAC9BC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,kBAAkB;UACzBC,WAAW,EAAE,qDAAqD;UAClEb,KAAK,EAAE,UAAU;UACjBc,aAAa,EAAE,QAAQ;UACvBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,EAAE;UAClBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,KAAK;UACpBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,CACb;YAAEb,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE,qBAAqB;YAAEc,QAAQ,EAAE;UAAE,CAAC,CACrD;UACDD,UAAU,EAAE;QACd,CAAC,CACF;QAED9B,UAAU,CAACe,UAAU,CAAC;QACtBP,aAAa,CAACwB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPrB,UAAU,EAAEI,UAAU,CAACkB,MAAM;UAC7BrB,UAAU,EAAEsB,IAAI,CAACC,IAAI,CAACpB,UAAU,CAACkB,MAAM,GAAGD,IAAI,CAACtB,YAAY;QAC7D,CAAC,CAAC,CAAC;QACHR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjElC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,WAAmB,KAAK;IACpE,IAAI;MACFxC,UAAU,CAACD,OAAO,CAAC0C,GAAG,CAACC,MAAM,IAC3BA,MAAM,CAAC1B,EAAE,KAAKuB,QAAQ,GAClB;QAAE,GAAGG,MAAM;QAAEf,WAAW,EAAE,IAAI;QAAEC,eAAe,EAAE,IAAIe,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,GAC3EF,MACN,CAAC,CAAC;MACFG,KAAK,CAAC,iCAAiCL,WAAW,KAAK,CAAC;IAC1D,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDS,KAAK,CAAC,+BAA+B,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,aAAa,GAAIzC,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAM0C,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGf,IAAI,CAACgB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIE,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,MAAMC,eAAe,GAAGrD,OAAO,CAACsD,MAAM,CAACX,MAAM,IAAI;IAC/C,IAAIvC,OAAO,CAACE,KAAK,IAAIqC,MAAM,CAACrC,KAAK,KAAKF,OAAO,CAACE,KAAK,EAAE,OAAO,KAAK;IACjE,IAAIF,OAAO,CAACG,QAAQ,IAAI,CAACoC,MAAM,CAACpB,WAAW,EAAE,OAAO,KAAK;IACzD,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMgC,gBAAgB,GAAGF,eAAe,CAACG,KAAK,CAC5C,CAAChD,UAAU,CAACE,WAAW,GAAG,CAAC,IAAIF,UAAU,CAACG,YAAY,EACtDH,UAAU,CAACE,WAAW,GAAGF,UAAU,CAACG,YACtC,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAIC,IAAY,IAAK;IACzCjD,aAAa,CAACwB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvB,WAAW,EAAEgD;IAAK,CAAC,CAAC,CAAC;EACzD,CAAC;EAED,MAAMC,wBAAwB,GAAIhD,YAAoB,IAAK;IACzDF,aAAa,CAACwB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPtB,YAAY;MACZD,WAAW,EAAE,CAAC;MACdG,UAAU,EAAEsB,IAAI,CAACC,IAAI,CAACiB,eAAe,CAACnB,MAAM,GAAGvB,YAAY;IAC7D,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACAnB,SAAS,CAAC,MAAM;IACdiB,aAAa,CAACwB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPvB,WAAW,EAAE,CAAC;MACdE,UAAU,EAAEyC,eAAe,CAACnB,MAAM;MAClCrB,UAAU,EAAEsB,IAAI,CAACC,IAAI,CAACiB,eAAe,CAACnB,MAAM,GAAGD,IAAI,CAACtB,YAAY;IAClE,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC0C,eAAe,CAACnB,MAAM,CAAC,CAAC;EAE5B,IAAI,CAACnC,IAAI,EAAE;IACT,oBACEH,OAAA;MAAKgE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CjE,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjE,OAAA;UAAIgE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DrE,OAAA;UAAGgE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrE,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErE,OAAA;IAAKgE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CjE,OAAA,CAACH,MAAM,CAAC0E,EAAE;MACRP,SAAS,EAAC,sDAAsD;MAChEQ,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC/B;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAEZrE,OAAA,CAACH,MAAM,CAAC+E,GAAG;MACTZ,SAAS,EAAC,yDAAyD;MACnEQ,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BG,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,gBAE3BjE,OAAA;QAAIgE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELrE,OAAA;QAAKgE,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDjE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA;YAAOgE,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrE,OAAA;YACE+E,KAAK,EAAEvE,OAAO,CAACE,KAAM;YACrBsE,QAAQ,EAAGC,CAAC,IAAKxE,UAAU,CAAC4B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE3B,KAAK,EAAEuE,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC1Ef,SAAS,EAAC,2HAA2H;YAAAC,QAAA,gBAErIjE,OAAA;cAAQ+E,KAAK,EAAC,EAAE;cAAAd,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CrE,OAAA;cAAQ+E,KAAK,EAAC,UAAU;cAAAd,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CrE,OAAA;cAAQ+E,KAAK,EAAC,cAAc;cAAAd,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnDrE,OAAA;cAAQ+E,KAAK,EAAC,UAAU;cAAAd,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA;YAAOgE,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrE,OAAA;YAAOgE,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBACvHjE,OAAA;cACEmF,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE5E,OAAO,CAACG,QAAS;cAC1BqE,QAAQ,EAAGC,CAAC,IAAKxE,UAAU,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,QAAQ,EAAEsE,CAAC,CAACC,MAAM,CAACE;cAAQ,CAAC,CAAC,CAAE;cAC/EpB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFrE,OAAA;cAAMgE,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BjE,OAAA;YACEqF,OAAO,EAAEA,CAAA,KAAM5E,UAAU,CAAC;cAAEC,KAAK,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAE;YAC1DqD,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAC/G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA;QAAKgE,SAAS,EAAC,0CAA0C;QAAAC,QAAA,GACtDR,eAAe,CAACnB,MAAM,EAAC,kBAAa,EAACmB,eAAe,CAACnB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAEZ/D,OAAO,gBACNN,OAAA;MAAKgE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjE,OAAA;QAAKgE,SAAS,EAAC;MAAgF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtGrE,OAAA;QAAGgE,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,GACJZ,eAAe,CAACnB,MAAM,KAAK,CAAC,gBAC9BtC,OAAA;MAAKgE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjE,OAAA;QAAIgE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClErE,OAAA;QAAGgE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrE,OAAA;QACEqF,OAAO,EAAEA,CAAA,KAAM5E,UAAU,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAE;QAC1DqD,SAAS,EAAC,mGAAmG;QAAAC,QAAA,EAC9G;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENrE,OAAA;MAAKgE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClER,eAAe,CAACX,GAAG,CAAC,CAACC,MAAM,EAAEuC,KAAK,kBACjCtF,OAAA,CAACH,MAAM,CAAC+E,GAAG;QAETZ,SAAS,EAAC,kGAAkG;QAC5GQ,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAEQ,KAAK,GAAG;QAAI,CAAE;QAAArB,QAAA,eAEnCjE,OAAA;UAAKgE,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBjE,OAAA;YAAKgE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDjE,OAAA;cAAKgE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjE,OAAA;gBAAMgE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAElB,MAAM,CAACtB;cAAS;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDrE,OAAA;gBAAAiE,QAAA,gBACEjE,OAAA;kBAAIgE,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAChElB,MAAM,CAACzB;gBAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EACJtB,MAAM,CAACpB,WAAW,iBACjB3B,OAAA;kBAAMgE,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EAAC;gBAE5J;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrE,OAAA;cAAMgE,SAAS,EAAE,8CAA8Cb,aAAa,CAACJ,MAAM,CAACrC,KAAK,CAAC,EAAG;cAAAuD,QAAA,EAC1FlB,MAAM,CAACvB;YAAa;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENrE,OAAA;YAAGgE,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EACtElB,MAAM,CAACxB;UAAW;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEJrE,OAAA;YAAKgE,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnFjE,OAAA;cAAKgE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCjE,OAAA;gBAAAiE,QAAA,GAAOlB,MAAM,CAACnB,cAAc,EAAC,YAAU;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNrE,OAAA;cAAKgE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCjE,OAAA;gBAAAiE,QAAA,GAAOlB,MAAM,CAAClB,aAAa,EAAC,YAAO;cAAA;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNrE,OAAA;cAAKgE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjE,OAAA;gBAAMgE,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BrE,OAAA;gBAAAiE,QAAA,EAAOb,cAAc,CAACL,MAAM,CAACrB,kBAAkB;cAAC;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNrE,OAAA;cAAKgE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCjE,OAAA;gBAAAiE,QAAA,GAAOlB,MAAM,CAACjB,mBAAmB,EAAC,GAAC;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELtB,MAAM,CAACf,WAAW,iBACjBhC,OAAA;YAAKgE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjE,OAAA;cAAKgE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjE,OAAA;gBAAAiE,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxBrE,OAAA;gBAAAiE,QAAA,GAAOlB,MAAM,CAACjB,mBAAmB,EAAC,GAAC;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNrE,OAAA;cAAKgE,SAAS,EAAC,sDAAsD;cAAAC,QAAA,eACnEjE,OAAA;gBACEgE,SAAS,EAAC,6DAA6D;gBACvEuB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGzC,MAAM,CAACjB,mBAAmB;gBAAI;cAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAtB,MAAM,CAACb,aAAa,CAACI,MAAM,GAAG,CAAC,IAAI,CAACS,MAAM,CAAChB,aAAa,iBACvD/B,OAAA;YAAKgE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEjE,OAAA;cAAGgE,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACHtB,MAAM,CAACb,aAAa,CAACY,GAAG,CAAC2C,MAAM,iBAC9BzF,OAAA;cAAqBgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,SAC1E,EAACwB,MAAM,CAACnE,KAAK,EAAC,IAAE,EAACmE,MAAM,CAACrD,QAAQ,EAAC,eACrC;YAAA,GAFUqD,MAAM,CAACpE,EAAE;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDrE,OAAA;YAAKgE,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvB,CAAClB,MAAM,CAAChB,aAAa,gBACpB/B,OAAA;cACE0F,QAAQ;cACR1B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAC9F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GACP,CAACtB,MAAM,CAACf,WAAW,gBACrBhC,OAAA;cACEqF,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAACI,MAAM,CAAC1B,EAAE,EAAE0B,MAAM,CAACzB,KAAK,CAAE;cACrD0C,SAAS,EAAC,0GAA0G;cAAAC,QAAA,EACrH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETrE,OAAA;cACEsE,IAAI,EAAE,YAAYvB,MAAM,CAAC1B,EAAE,EAAG;cAC9B2C,SAAS,EAAC,wHAAwH;cAAAC,QAAA,EAEjIlB,MAAM,CAACjB,mBAAmB,GAAG,CAAC,GAAG,WAAW,GAAG;YAAW;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CACJ,eAEDrE,OAAA;cACEsE,IAAI,EAAE,YAAYvB,MAAM,CAAC1B,EAAE,EAAG;cAC9B2C,SAAS,EAAC,oLAAoL;cAAAC,QAAA,EAC/L;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAzGDtB,MAAM,CAAC1B,EAAE;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0GJ,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnE,EAAA,CAzbID,WAAqB;EAAA,QACRH,OAAO;AAAA;AAAA6F,EAAA,GADpB1F,WAAqB;AA2b3B,eAAeA,WAAW;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}