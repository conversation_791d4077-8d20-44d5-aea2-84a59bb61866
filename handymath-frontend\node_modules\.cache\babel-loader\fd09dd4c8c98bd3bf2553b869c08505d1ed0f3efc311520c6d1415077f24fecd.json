{"ast": null, "code": "export var ifftDocs = {\n  name: 'ifft',\n  category: 'Matrix',\n  syntax: ['ifft(x)'],\n  description: 'Calculate N-dimensional inverse Fourier transform',\n  examples: ['ifft([[2, 2], [0, 0]])'],\n  seealso: ['fft']\n};", "map": {"version": 3, "names": ["ifftDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/ifft.js"], "sourcesContent": ["export var ifftDocs = {\n  name: 'ifft',\n  category: 'Matrix',\n  syntax: ['ifft(x)'],\n  description: 'Calculate N-dimensional inverse Fourier transform',\n  examples: ['ifft([[2, 2], [0, 0]])'],\n  seealso: ['fft']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,CAAC,wBAAwB,CAAC;EACpCC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}