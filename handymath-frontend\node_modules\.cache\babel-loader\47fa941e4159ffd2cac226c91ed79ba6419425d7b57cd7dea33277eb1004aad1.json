{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo04xSidSid';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo04xSidSid = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked MAX(NNZA, NNZB) times\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 && B(i,j) !== 0\n   * C(i,j) = ┤  A(i,j)       ; A(i,j) !== 0 && B(i,j) === 0\n   *          └  B(i,j)       ; A(i,j) === 0\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo04xSidSid(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspace\n    var xa = avalues && bvalues ? [] : undefined;\n    var xb = avalues && bvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var wa = [];\n    var wb = [];\n\n    // vars\n    var i, j, k, k0, k1;\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // loop A(:,j)\n      for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // update c\n        cindex.push(i);\n        // update workspace\n        wa[i] = mark;\n        // check we need to process values\n        if (xa) {\n          xa[i] = avalues[k];\n        }\n      }\n      // loop B(:,j)\n      for (k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // check row exists in A\n        if (wa[i] === mark) {\n          // update record in xa @ i\n          if (xa) {\n            // invoke callback\n            var v = cf(xa[i], bvalues[k]);\n            // check for zero\n            if (!eq(v, zero)) {\n              // update workspace\n              xa[i] = v;\n            } else {\n              // remove mark (index will be removed later)\n              wa[i] = null;\n            }\n          }\n        } else {\n          // update c\n          cindex.push(i);\n          // update workspace\n          wb[i] = mark;\n          // check we need to process values\n          if (xb) {\n            xb[i] = bvalues[k];\n          }\n        }\n      }\n      // check we need to process values (non pattern matrix)\n      if (xa && xb) {\n        // initialize first index in j\n        k = cptr[j];\n        // loop index in j\n        while (k < cindex.length) {\n          // row\n          i = cindex[k];\n          // check workspace has value @ i\n          if (wa[i] === mark) {\n            // push value (Aij != 0 || (Aij != 0 && Bij != 0))\n            cvalues[k] = xa[i];\n            // increment pointer\n            k++;\n          } else if (wb[i] === mark) {\n            // push value (bij != 0)\n            cvalues[k] = xb[i];\n            // increment pointer\n            k++;\n          } else {\n            // remove index @ k\n            cindex.splice(k, 1);\n          }\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo04xSidSid", "_ref", "typed", "equalScalar", "matAlgo04xSidSid", "a", "b", "callback", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bvalues", "bindex", "bptr", "bsize", "bdt", "length", "RangeError", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "xa", "xb", "wa", "wb", "i", "j", "k", "k0", "k1", "mark", "push", "v", "splice", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo04xSidSid.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo04xSidSid';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo04xSidSid = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked MAX(NNZA, NNZB) times\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 && B(i,j) !== 0\n   * C(i,j) = ┤  A(i,j)       ; A(i,j) !== 0 && B(i,j) === 0\n   *          └  B(i,j)       ; A(i,j) === 0\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo04xSidSid(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspace\n    var xa = avalues && bvalues ? [] : undefined;\n    var xb = avalues && bvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var wa = [];\n    var wb = [];\n\n    // vars\n    var i, j, k, k0, k1;\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // loop A(:,j)\n      for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // update c\n        cindex.push(i);\n        // update workspace\n        wa[i] = mark;\n        // check we need to process values\n        if (xa) {\n          xa[i] = avalues[k];\n        }\n      }\n      // loop B(:,j)\n      for (k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // check row exists in A\n        if (wa[i] === mark) {\n          // update record in xa @ i\n          if (xa) {\n            // invoke callback\n            var v = cf(xa[i], bvalues[k]);\n            // check for zero\n            if (!eq(v, zero)) {\n              // update workspace\n              xa[i] = v;\n            } else {\n              // remove mark (index will be removed later)\n              wa[i] = null;\n            }\n          }\n        } else {\n          // update c\n          cindex.push(i);\n          // update workspace\n          wb[i] = mark;\n          // check we need to process values\n          if (xb) {\n            xb[i] = bvalues[k];\n          }\n        }\n      }\n      // check we need to process values (non pattern matrix)\n      if (xa && xb) {\n        // initialize first index in j\n        k = cptr[j];\n        // loop index in j\n        while (k < cindex.length) {\n          // row\n          i = cindex[k];\n          // check workspace has value @ i\n          if (wa[i] === mark) {\n            // push value (Aij != 0 || (Aij != 0 && Bij != 0))\n            cvalues[k] = xa[i];\n            // increment pointer\n            k++;\n          } else if (wb[i] === mark) {\n            // push value (bij != 0)\n            cvalues[k] = xb[i];\n            // increment pointer\n            k++;\n          } else {\n            // remove index @ k\n            cindex.splice(k, 1);\n          }\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,kBAAkB;AAC7B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,sBAAsB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACrF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,gBAAgBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC/C;IACA,IAAIC,OAAO,GAAGH,CAAC,CAACI,OAAO;IACvB,IAAIC,MAAM,GAAGL,CAAC,CAACM,MAAM;IACrB,IAAIC,IAAI,GAAGP,CAAC,CAACQ,IAAI;IACjB,IAAIC,KAAK,GAAGT,CAAC,CAACU,KAAK;IACnB,IAAIC,GAAG,GAAGX,CAAC,CAACY,SAAS,IAAIZ,CAAC,CAACa,KAAK,KAAKC,SAAS,GAAGd,CAAC,CAACY,SAAS,GAAGZ,CAAC,CAACe,WAAW,CAAC,CAAC;IAC9E;IACA,IAAIC,OAAO,GAAGf,CAAC,CAACG,OAAO;IACvB,IAAIa,MAAM,GAAGhB,CAAC,CAACK,MAAM;IACrB,IAAIY,IAAI,GAAGjB,CAAC,CAACO,IAAI;IACjB,IAAIW,KAAK,GAAGlB,CAAC,CAACS,KAAK;IACnB,IAAIU,GAAG,GAAGnB,CAAC,CAACW,SAAS,IAAIX,CAAC,CAACY,KAAK,KAAKC,SAAS,GAAGb,CAAC,CAACW,SAAS,GAAGX,CAAC,CAACc,WAAW,CAAC,CAAC;;IAE9E;IACA,IAAIN,KAAK,CAACY,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;MACjC,MAAM,IAAI7B,cAAc,CAACiB,KAAK,CAACY,MAAM,EAAEF,KAAK,CAACE,MAAM,CAAC;IACtD;;IAEA;IACA,IAAIZ,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,IAAIV,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIG,UAAU,CAAC,gCAAgC,GAAGb,KAAK,GAAG,yBAAyB,GAAGU,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAII,IAAI,GAAGd,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIe,OAAO,GAAGf,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIgB,EAAE;IACN;IACA,IAAIC,EAAE,GAAG5B,WAAW;IACpB;IACA,IAAI6B,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAG1B,QAAQ;;IAEjB;IACA,IAAI,OAAOS,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKS,GAAG,IAAIT,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAc,EAAE,GAAGd,GAAG;MACR;MACAe,EAAE,GAAG7B,KAAK,CAACgC,IAAI,CAAC/B,WAAW,EAAE,CAAC2B,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAG9B,KAAK,CAACiC,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAG,EAAE,GAAG/B,KAAK,CAACgC,IAAI,CAAC3B,QAAQ,EAAE,CAACuB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAG5B,OAAO,IAAIa,OAAO,GAAG,EAAE,GAAGF,SAAS;IACjD,IAAIkB,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,IAAIC,EAAE,GAAG/B,OAAO,IAAIa,OAAO,GAAG,EAAE,GAAGF,SAAS;IAC5C,IAAIqB,EAAE,GAAGhC,OAAO,IAAIa,OAAO,GAAG,EAAE,GAAGF,SAAS;IAC5C;IACA,IAAIsB,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;;IAEX;IACA,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE;;IAEnB;IACA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,OAAO,EAAEe,CAAC,EAAE,EAAE;MAC5B;MACAN,IAAI,CAACM,CAAC,CAAC,GAAGP,MAAM,CAACX,MAAM;MACvB;MACA,IAAIsB,IAAI,GAAGJ,CAAC,GAAG,CAAC;MAChB;MACA,KAAKE,EAAE,GAAGlC,IAAI,CAACgC,CAAC,CAAC,EAAEG,EAAE,GAAGnC,IAAI,CAACgC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;QACxD;QACAF,CAAC,GAAGjC,MAAM,CAACmC,CAAC,CAAC;QACb;QACAR,MAAM,CAACY,IAAI,CAACN,CAAC,CAAC;QACd;QACAF,EAAE,CAACE,CAAC,CAAC,GAAGK,IAAI;QACZ;QACA,IAAIT,EAAE,EAAE;UACNA,EAAE,CAACI,CAAC,CAAC,GAAGnC,OAAO,CAACqC,CAAC,CAAC;QACpB;MACF;MACA;MACA,KAAKC,EAAE,GAAGvB,IAAI,CAACqB,CAAC,CAAC,EAAEG,EAAE,GAAGxB,IAAI,CAACqB,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;QACxD;QACAF,CAAC,GAAGrB,MAAM,CAACuB,CAAC,CAAC;QACb;QACA,IAAIJ,EAAE,CAACE,CAAC,CAAC,KAAKK,IAAI,EAAE;UAClB;UACA,IAAIT,EAAE,EAAE;YACN;YACA,IAAIW,CAAC,GAAGjB,EAAE,CAACM,EAAE,CAACI,CAAC,CAAC,EAAEtB,OAAO,CAACwB,CAAC,CAAC,CAAC;YAC7B;YACA,IAAI,CAACd,EAAE,CAACmB,CAAC,EAAElB,IAAI,CAAC,EAAE;cAChB;cACAO,EAAE,CAACI,CAAC,CAAC,GAAGO,CAAC;YACX,CAAC,MAAM;cACL;cACAT,EAAE,CAACE,CAAC,CAAC,GAAG,IAAI;YACd;UACF;QACF,CAAC,MAAM;UACL;UACAN,MAAM,CAACY,IAAI,CAACN,CAAC,CAAC;UACd;UACAD,EAAE,CAACC,CAAC,CAAC,GAAGK,IAAI;UACZ;UACA,IAAIR,EAAE,EAAE;YACNA,EAAE,CAACG,CAAC,CAAC,GAAGtB,OAAO,CAACwB,CAAC,CAAC;UACpB;QACF;MACF;MACA;MACA,IAAIN,EAAE,IAAIC,EAAE,EAAE;QACZ;QACAK,CAAC,GAAGP,IAAI,CAACM,CAAC,CAAC;QACX;QACA,OAAOC,CAAC,GAAGR,MAAM,CAACX,MAAM,EAAE;UACxB;UACAiB,CAAC,GAAGN,MAAM,CAACQ,CAAC,CAAC;UACb;UACA,IAAIJ,EAAE,CAACE,CAAC,CAAC,KAAKK,IAAI,EAAE;YAClB;YACAZ,OAAO,CAACS,CAAC,CAAC,GAAGN,EAAE,CAACI,CAAC,CAAC;YAClB;YACAE,CAAC,EAAE;UACL,CAAC,MAAM,IAAIH,EAAE,CAACC,CAAC,CAAC,KAAKK,IAAI,EAAE;YACzB;YACAZ,OAAO,CAACS,CAAC,CAAC,GAAGL,EAAE,CAACG,CAAC,CAAC;YAClB;YACAE,CAAC,EAAE;UACL,CAAC,MAAM;YACL;YACAR,MAAM,CAACc,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC;UACrB;QACF;MACF;IACF;IACA;IACAP,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACX,MAAM;;IAE7B;IACA,OAAOrB,CAAC,CAAC+C,kBAAkB,CAAC;MAC1BC,MAAM,EAAEjB,OAAO;MACfkB,KAAK,EAAEjB,MAAM;MACbkB,GAAG,EAAEjB,IAAI;MACTkB,IAAI,EAAE,CAAC5B,IAAI,EAAEC,OAAO,CAAC;MACrB4B,QAAQ,EAAEzC,GAAG,KAAKX,CAAC,CAACY,SAAS,IAAIQ,GAAG,KAAKnB,CAAC,CAACW,SAAS,GAAGa,EAAE,GAAGX;IAC9D,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}