{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { formatDependencies } from './dependenciesFormat.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createOct } from '../../factoriesAny.js';\nexport var octDependencies = {\n  formatDependencies,\n  typedDependencies,\n  createOct\n};", "map": {"version": 3, "names": ["formatDependencies", "typedDependencies", "createOct", "octDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesOct.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { formatDependencies } from './dependenciesFormat.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createOct } from '../../factoriesAny.js';\nexport var octDependencies = {\n  formatDependencies,\n  typedDependencies,\n  createOct\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BH,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}