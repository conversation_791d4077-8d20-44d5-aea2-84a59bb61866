{"ast": null, "code": "export var sinDocs = {\n  name: 'sin',\n  category: 'Trigonometry',\n  syntax: ['sin(x)'],\n  description: 'Compute the sine of x in radians.',\n  examples: ['sin(2)', 'sin(pi / 4) ^ 2', 'sin(90 deg)', 'sin(30 deg)', 'sin(0.2)^2 + cos(0.2)^2'],\n  seealso: ['asin', 'cos', 'tan']\n};", "map": {"version": 3, "names": ["sinDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/sin.js"], "sourcesContent": ["export var sinDocs = {\n  name: 'sin',\n  category: 'Trigonometry',\n  syntax: ['sin(x)'],\n  description: 'Compute the sine of x in radians.',\n  examples: ['sin(2)', 'sin(pi / 4) ^ 2', 'sin(90 deg)', 'sin(30 deg)', 'sin(0.2)^2 + cos(0.2)^2'],\n  seealso: ['asin', 'cos', 'tan']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,yBAAyB,CAAC;EAChGC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}