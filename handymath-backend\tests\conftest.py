"""
Configuration globale pour les tests pytest
"""
import pytest
import os
import django

# Configuration Django pour les tests
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')

def pytest_configure():
    django.setup()


@pytest.fixture
def api_client():
    """Client API pour les tests"""
    from rest_framework.test import APIClient
    return APIClient()


@pytest.fixture
def user():
    """Utilisateur de test standard"""
    from django.contrib.auth.models import User
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpassword123'
    )


@pytest.fixture
def admin_user():
    """Administrateur de test"""
    from django.contrib.auth.models import User
    return User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='adminpassword123'
    )


@pytest.fixture
def student_user():
    """Étudiant de test"""
    from django.contrib.auth.models import User
    return User.objects.create_user(
        username='student',
        email='<EMAIL>',
        password='studentpassword123'
    )


@pytest.fixture
def authenticated_client(api_client, user):
    """Client API avec utilisateur authentifié"""
    from rest_framework_simplejwt.tokens import RefreshToken
    refresh = RefreshToken.for_user(user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def admin_client(api_client, admin_user):
    """Client API avec administrateur authentifié"""
    from rest_framework_simplejwt.tokens import RefreshToken
    refresh = RefreshToken.for_user(admin_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def student_client(api_client, student_user):
    """Client API avec étudiant authentifié"""
    from rest_framework_simplejwt.tokens import RefreshToken
    refresh = RefreshToken.for_user(student_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def sample_equations():
    """Équations de test"""
    return [
        "2x + 3 = 11",
        "x^2 - 4 = 0",
        "3x - 7 = 14",
        "x^2 + 5x + 6 = 0",
        "x + 1 = 5"
    ]


@pytest.fixture
def sample_equation_data():
    """Données d'équation pour les tests API"""
    return {
        "equation_text": "2x + 3 = 11",
        "expected_solution": "x = 4",
        "steps": ["2x = 11 - 3", "2x = 8", "x = 4"]
    }


@pytest.fixture
def invalid_equation_data():
    """Données d'équation invalides pour les tests"""
    return {
        "equation_text": "",
        "malformed": "2x + + = 11",
        "non_equation": "hello world"
    }


@pytest.fixture
def user_registration_data():
    """Données d'inscription utilisateur"""
    return {
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "password_confirm": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }


@pytest.fixture
def login_data(user):
    """Données de connexion"""
    return {
        "username": user.username,
        "password": "testpassword123"
    }


@pytest.fixture
def math_test_cases():
    """Cas de test mathématiques"""
    return [
        {
            "equation": "x + 5 = 10",
            "expected_solution": [5],
            "type": "linear"
        },
        {
            "equation": "x^2 - 4 = 0",
            "expected_solution": [-2, 2],
            "type": "quadratic"
        },
        {
            "equation": "2x + 3 = 11",
            "expected_solution": [4],
            "type": "linear"
        },
        {
            "equation": "x^2 + 5x + 6 = 0",
            "expected_solution": [-2, -3],
            "type": "quadratic"
        }
    ]


@pytest.fixture
def ocr_test_images():
    """Images de test pour l'OCR"""
    return {
        "simple_equation": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        "complex_equation": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    }


@pytest.fixture
def performance_test_data():
    """Données pour les tests de performance"""
    return {
        "large_equation_set": [f"x + {i} = {i+10}" for i in range(100)],
        "complex_equations": [
            "x^3 + 2x^2 - 5x + 2 = 0",
            "sin(x) + cos(x) = 1",
            "log(x) + x^2 = 10"
        ]
    }


# Fixtures pour les tests d'intégration
@pytest.fixture
def full_test_dataset(db):
    """Jeu de données complet pour les tests d'intégration"""
    from .factories import create_test_dataset
    return create_test_dataset()


# Fixtures pour les mocks
@pytest.fixture
def mock_ocr_response():
    """Mock de réponse OCR"""
    return {
        "text": "2x + 3 = 11",
        "confidence": 0.95,
        "processing_time": 0.5
    }


@pytest.fixture
def mock_math_solver_response():
    """Mock de réponse du solveur mathématique"""
    return {
        "solution": "x = 4",
        "steps": [
            "2x + 3 = 11",
            "2x = 11 - 3",
            "2x = 8", 
            "x = 8/2",
            "x = 4"
        ],
        "solution_type": "linear",
        "variables": ["x"]
    }


# Helpers pour les tests
@pytest.fixture
def assert_equation_solved():
    """Helper pour vérifier qu'une équation est résolue"""
    def _assert_solved(response_data, expected_solution):
        assert "solution" in response_data
        assert "steps" in response_data
        assert response_data["solution"] is not None
        if expected_solution:
            assert str(expected_solution) in str(response_data["solution"])
    return _assert_solved


@pytest.fixture
def assert_api_response():
    """Helper pour vérifier les réponses API"""
    def _assert_response(response, status_code=200, has_data=True):
        assert response.status_code == status_code
        if has_data and status_code == 200:
            assert response.data is not None
        return response.data
    return _assert_response


# Configuration pour les tests lents
@pytest.fixture
def skip_slow(request):
    """Skip les tests lents si pas de flag --slow"""
    if request.config.getoption("--slow") is None:
        pytest.skip("Test lent - utilisez --slow pour l'exécuter")


# Nettoyage après les tests
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Nettoyage automatique après chaque test"""
    yield
    # Nettoyage si nécessaire
    pass


# Configuration des markers
def pytest_configure(config):
    """Configuration des markers pytest"""
    config.addinivalue_line("markers", "unit: Tests unitaires")
    config.addinivalue_line("markers", "integration: Tests d'intégration")
    config.addinivalue_line("markers", "api: Tests d'API")
    config.addinivalue_line("markers", "math: Tests mathématiques")
    config.addinivalue_line("markers", "auth: Tests d'authentification")
    config.addinivalue_line("markers", "slow: Tests lents")


def pytest_addoption(parser):
    """Options de ligne de commande personnalisées"""
    parser.addoption(
        "--slow", action="store_true", default=False,
        help="Exécuter les tests lents"
    )
