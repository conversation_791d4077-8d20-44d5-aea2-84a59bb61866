# Ajouter ces lignes à votre fichier settings.py

INSTALLED_APPS = [
    # ...
    'corsheaders',
    # ...
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # Doit être placé avant CommonMiddleware
    'django.middleware.common.CommonMiddleware',
    # ...
]

# Autoriser les requêtes CORS depuis le frontend
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Autoriser les cookies dans les requêtes CORS
CORS_ALLOW_CREDENTIALS = True

# Autoriser tous les en-têtes dans les requêtes
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]