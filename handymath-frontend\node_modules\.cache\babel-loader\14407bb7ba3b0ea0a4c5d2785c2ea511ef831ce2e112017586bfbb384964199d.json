{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const {\n    addNotification\n  } = useNotifications();\n  const [settings, setSettings] = useState({\n    notifications: {\n      email: true,\n      push: false,\n      exercise_reminders: true,\n      progress_updates: true\n    },\n    preferences: {\n      language: 'fr',\n      timezone: 'Europe/Paris',\n      difficulty_level: 'intermediate',\n      auto_save: true\n    },\n    privacy: {\n      profile_visibility: 'public',\n      show_progress: true,\n      show_statistics: true\n    }\n  });\n  const [loading, setLoading] = useState(false);\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // Simuler une sauvegarde\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      addNotification({\n        type: 'success',\n        title: 'Paramètres sauvegardés',\n        message: 'Vos préférences ont été mises à jour'\n      });\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder les paramètres'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateSetting = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(StudentLayout, {\n      title: \"Param\\xE8tres\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s refus\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux param\\xE8tres.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StudentLayout, {\n    title: \"Param\\xE8tres\",\n    subtitle: \"Personnalisez votre exp\\xE9rience d'apprentissage\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-2xl mr-3\",\n            children: \"\\uD83C\\uDFA8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this), \"Apparence\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                children: \"Th\\xE8me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: \"Choisir entre le mode clair et sombre\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleTheme,\n              className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${theme === 'dark' ? 'bg-primary-600' : 'bg-gray-200'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${theme === 'dark' ? 'translate-x-6' : 'translate-x-1'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-2xl mr-3\",\n            children: \"\\uD83D\\uDD14\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), \"Notifications\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: Object.entries(settings.notifications).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                children: [key === 'email' && 'Notifications par email', key === 'push' && 'Notifications push', key === 'exercise_reminders' && 'Rappels d\\'exercices', key === 'progress_updates' && 'Mises à jour de progression']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => updateSetting('notifications', key, !value),\n              className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${value ? 'bg-primary-600' : 'bg-gray-200'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${value ? 'translate-x-6' : 'translate-x-1'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-2xl mr-3\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), \"Pr\\xE9f\\xE9rences\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Langue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.preferences.language,\n              onChange: e => updateSetting('preferences', 'language', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"fr\",\n                children: \"Fran\\xE7ais\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"en\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"es\",\n                children: \"Espa\\xF1ol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Niveau de difficult\\xE9 par d\\xE9faut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.preferences.difficulty_level,\n              onChange: e => updateSetting('preferences', 'difficulty_level', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beginner\",\n                children: \"D\\xE9butant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"intermediate\",\n                children: \"Interm\\xE9diaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advanced\",\n                children: \"Avanc\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                children: \"Sauvegarde automatique\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: \"Sauvegarder automatiquement votre progression\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => updateSetting('preferences', 'auto_save', !settings.preferences.auto_save),\n              className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.preferences.auto_save ? 'bg-primary-600' : 'bg-gray-200'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.preferences.auto_save ? 'translate-x-6' : 'translate-x-1'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-2xl mr-3\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), \"Confidentialit\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Visibilit\\xE9 du profil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.privacy.profile_visibility,\n              onChange: e => updateSetting('privacy', 'profile_visibility', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"public\",\n                children: \"Public\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"friends\",\n                children: \"Amis seulement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"private\",\n                children: \"Priv\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                children: \"Afficher ma progression\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateSetting('privacy', 'show_progress', !settings.privacy.show_progress),\n                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.privacy.show_progress ? 'bg-primary-600' : 'bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.privacy.show_progress ? 'translate-x-6' : 'translate-x-1'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                children: \"Afficher mes statistiques\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateSetting('privacy', 'show_statistics', !settings.privacy.show_statistics),\n                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.privacy.show_statistics ? 'bg-primary-600' : 'bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.privacy.show_statistics ? 'translate-x-6' : 'translate-x-1'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex justify-end\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          disabled: loading,\n          className: \"px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center\",\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this), \"Sauvegarde...\"]\n          }, void 0, true) : 'Sauvegarder les paramètres'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPage, \"XL9v8Nzk1aR8fIvsxYuOBhK8Xo0=\", false, function () {\n  return [useAuth, useTheme, useNotifications];\n});\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "useAuth", "useTheme", "useNotifications", "StudentLayout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsPage", "_s", "user", "theme", "toggleTheme", "addNotification", "settings", "setSettings", "notifications", "email", "push", "exercise_reminders", "progress_updates", "preferences", "language", "timezone", "difficulty_level", "auto_save", "privacy", "profile_visibility", "show_progress", "show_statistics", "loading", "setLoading", "handleSave", "Promise", "resolve", "setTimeout", "type", "title", "message", "error", "updateSetting", "category", "key", "value", "prev", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subtitle", "div", "initial", "opacity", "y", "animate", "onClick", "transition", "delay", "Object", "entries", "map", "onChange", "e", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/SettingsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\n\ninterface Settings {\n  notifications: {\n    email: boolean;\n    push: boolean;\n    exercise_reminders: boolean;\n    progress_updates: boolean;\n  };\n  preferences: {\n    language: string;\n    timezone: string;\n    difficulty_level: string;\n    auto_save: boolean;\n  };\n  privacy: {\n    profile_visibility: string;\n    show_progress: boolean;\n    show_statistics: boolean;\n  };\n}\n\nconst SettingsPage: React.FC = () => {\n  const { user } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const { addNotification } = useNotifications();\n\n  const [settings, setSettings] = useState<Settings>({\n    notifications: {\n      email: true,\n      push: false,\n      exercise_reminders: true,\n      progress_updates: true\n    },\n    preferences: {\n      language: 'fr',\n      timezone: 'Europe/Paris',\n      difficulty_level: 'intermediate',\n      auto_save: true\n    },\n    privacy: {\n      profile_visibility: 'public',\n      show_progress: true,\n      show_statistics: true\n    }\n  });\n\n  const [loading, setLoading] = useState(false);\n\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // Simuler une sauvegarde\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      addNotification({\n        type: 'success',\n        title: 'Paramètres sauvegardés',\n        message: 'Vos préférences ont été mises à jour'\n      });\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder les paramètres'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateSetting = (category: keyof Settings, key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n\n  if (!user) {\n    return (\n      <StudentLayout title=\"Paramètres\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès refusé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Vous devez être connecté pour accéder aux paramètres.\n          </p>\n        </div>\n      </StudentLayout>\n    );\n  }\n\n  return (\n    <StudentLayout\n      title=\"Paramètres\"\n      subtitle=\"Personnalisez votre expérience d'apprentissage\"\n    >\n      <div className=\"max-w-4xl mx-auto space-y-8\">\n          \n          {/* Apparence */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">🎨</span>\n              Apparence\n            </h2>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Thème\n                  </label>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Choisir entre le mode clair et sombre\n                  </p>\n                </div>\n                <button\n                  onClick={toggleTheme}\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                    theme === 'dark' ? 'bg-primary-600' : 'bg-gray-200'\n                  }`}\n                >\n                  <span\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                      theme === 'dark' ? 'translate-x-6' : 'translate-x-1'\n                    }`}\n                  />\n                </button>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Notifications */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.1 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">🔔</span>\n              Notifications\n            </h2>\n            \n            <div className=\"space-y-4\">\n              {Object.entries(settings.notifications).map(([key, value]) => (\n                <div key={key} className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      {key === 'email' && 'Notifications par email'}\n                      {key === 'push' && 'Notifications push'}\n                      {key === 'exercise_reminders' && 'Rappels d\\'exercices'}\n                      {key === 'progress_updates' && 'Mises à jour de progression'}\n                    </label>\n                  </div>\n                  <button\n                    onClick={() => updateSetting('notifications', key, !value)}\n                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                      value ? 'bg-primary-600' : 'bg-gray-200'\n                    }`}\n                  >\n                    <span\n                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                        value ? 'translate-x-6' : 'translate-x-1'\n                      }`}\n                    />\n                  </button>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Préférences */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">⚙️</span>\n              Préférences\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Langue\n                </label>\n                <select\n                  value={settings.preferences.language}\n                  onChange={(e) => updateSetting('preferences', 'language', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"fr\">Français</option>\n                  <option value=\"en\">English</option>\n                  <option value=\"es\">Español</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Niveau de difficulté par défaut\n                </label>\n                <select\n                  value={settings.preferences.difficulty_level}\n                  onChange={(e) => updateSetting('preferences', 'difficulty_level', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"beginner\">Débutant</option>\n                  <option value=\"intermediate\">Intermédiaire</option>\n                  <option value=\"advanced\">Avancé</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Sauvegarde automatique\n                  </label>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Sauvegarder automatiquement votre progression\n                  </p>\n                </div>\n                <button\n                  onClick={() => updateSetting('preferences', 'auto_save', !settings.preferences.auto_save)}\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                    settings.preferences.auto_save ? 'bg-primary-600' : 'bg-gray-200'\n                  }`}\n                >\n                  <span\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                      settings.preferences.auto_save ? 'translate-x-6' : 'translate-x-1'\n                    }`}\n                  />\n                </button>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Confidentialité */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n              <span className=\"text-2xl mr-3\">🔒</span>\n              Confidentialité\n            </h2>\n            \n            <div className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Visibilité du profil\n                </label>\n                <select\n                  value={settings.privacy.profile_visibility}\n                  onChange={(e) => updateSetting('privacy', 'profile_visibility', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"public\">Public</option>\n                  <option value=\"friends\">Amis seulement</option>\n                  <option value=\"private\">Privé</option>\n                </select>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Afficher ma progression\n                  </label>\n                  <button\n                    onClick={() => updateSetting('privacy', 'show_progress', !settings.privacy.show_progress)}\n                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                      settings.privacy.show_progress ? 'bg-primary-600' : 'bg-gray-200'\n                    }`}\n                  >\n                    <span\n                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                        settings.privacy.show_progress ? 'translate-x-6' : 'translate-x-1'\n                      }`}\n                    />\n                  </button>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Afficher mes statistiques\n                  </label>\n                  <button\n                    onClick={() => updateSetting('privacy', 'show_statistics', !settings.privacy.show_statistics)}\n                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                      settings.privacy.show_statistics ? 'bg-primary-600' : 'bg-gray-200'\n                    }`}\n                  >\n                    <span\n                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                        settings.privacy.show_statistics ? 'translate-x-6' : 'translate-x-1'\n                      }`}\n                    />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Bouton de sauvegarde */}\n          <motion.div\n            className=\"flex justify-end\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.4 }}\n          >\n            <button\n              onClick={handleSave}\n              disabled={loading}\n              className=\"px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center\"\n            >\n              {loading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Sauvegarde...\n                </>\n              ) : (\n                'Sauvegarder les paramètres'\n              )}\n            </button>\n          </motion.div>\n      </div>\n    </StudentLayout>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsBxD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEW,KAAK;IAAEC;EAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC;EACzC,MAAM;IAAEY;EAAgB,CAAC,GAAGX,gBAAgB,CAAC,CAAC;EAE9C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAW;IACjDkB,aAAa,EAAE;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,KAAK;MACXC,kBAAkB,EAAE,IAAI;MACxBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,WAAW,EAAE;MACXC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,cAAc;MACxBC,gBAAgB,EAAE,cAAc;MAChCC,SAAS,EAAE;IACb,CAAC;IACDC,OAAO,EAAE;MACPC,kBAAkB,EAAE,QAAQ;MAC5BC,aAAa,EAAE,IAAI;MACnBC,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDrB,eAAe,CAAC;QACduB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1B,eAAe,CAAC;QACduB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,aAAa,GAAGA,CAACC,QAAwB,EAAEC,GAAW,EAAEC,KAAU,KAAK;IAC3E5B,WAAW,CAAC6B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,QAAQ,GAAG;QACV,GAAGG,IAAI,CAACH,QAAQ,CAAC;QACjB,CAACC,GAAG,GAAGC;MACT;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAACjC,IAAI,EAAE;IACT,oBACEL,OAAA,CAACF,aAAa;MAACkC,KAAK,EAAC,eAAY;MAAAQ,QAAA,eAC/BxC,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1BxC,OAAA;UAAIyC,SAAS,EAAC,yBAAyB;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzD7C,OAAA;UAAGyC,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAEhD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEA,oBACE7C,OAAA,CAACF,aAAa;IACZkC,KAAK,EAAC,eAAY;IAClBc,QAAQ,EAAC,mDAAgD;IAAAN,QAAA,eAEzDxC,OAAA;MAAKyC,SAAS,EAAC,6BAA6B;MAAAD,QAAA,gBAGxCxC,OAAA,CAACN,MAAM,CAACqD,GAAG;QACTN,SAAS,EAAC,oDAAoD;QAC9DO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAE9BxC,OAAA;UAAIyC,SAAS,EAAC,wEAAwE;UAAAD,QAAA,gBACpFxC,OAAA;YAAMyC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,aAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL7C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAD,QAAA,eACxBxC,OAAA;YAAKyC,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDxC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAOyC,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,EAAC;cAExE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBAAGyC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAExD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN7C,OAAA;cACEoD,OAAO,EAAE7C,WAAY;cACrBkC,SAAS,EAAE,6EACTnC,KAAK,KAAK,MAAM,GAAG,gBAAgB,GAAG,aAAa,EAClD;cAAAkC,QAAA,eAEHxC,OAAA;gBACEyC,SAAS,EAAE,6EACTnC,KAAK,KAAK,MAAM,GAAG,eAAe,GAAG,eAAe;cACnD;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb7C,OAAA,CAACN,MAAM,CAACqD,GAAG;QACTN,SAAS,EAAC,oDAAoD;QAC9DO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,gBAE3BxC,OAAA;UAAIyC,SAAS,EAAC,wEAAwE;UAAAD,QAAA,gBACpFxC,OAAA;YAAMyC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,iBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL7C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAD,QAAA,EACvBe,MAAM,CAACC,OAAO,CAAC/C,QAAQ,CAACE,aAAa,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEC,KAAK,CAAC,kBACvDtC,OAAA;YAAeyC,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAC1DxC,OAAA;cAAAwC,QAAA,eACExC,OAAA;gBAAOyC,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,GACpEH,GAAG,KAAK,OAAO,IAAI,yBAAyB,EAC5CA,GAAG,KAAK,MAAM,IAAI,oBAAoB,EACtCA,GAAG,KAAK,oBAAoB,IAAI,sBAAsB,EACtDA,GAAG,KAAK,kBAAkB,IAAI,6BAA6B;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN7C,OAAA;cACEoD,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC,eAAe,EAAEE,GAAG,EAAE,CAACC,KAAK,CAAE;cAC3DG,SAAS,EAAE,6EACTH,KAAK,GAAG,gBAAgB,GAAG,aAAa,EACvC;cAAAE,QAAA,eAEHxC,OAAA;gBACEyC,SAAS,EAAE,6EACTH,KAAK,GAAG,eAAe,GAAG,eAAe;cACxC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA,GApBDR,GAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBR,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb7C,OAAA,CAACN,MAAM,CAACqD,GAAG;QACTN,SAAS,EAAC,oDAAoD;QAC9DO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,gBAE3BxC,OAAA;UAAIyC,SAAS,EAAC,wEAAwE;UAAAD,QAAA,gBACpFxC,OAAA;YAAMyC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,qBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL7C,OAAA;UAAKyC,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBACpDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAOyC,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEsC,KAAK,EAAE7B,QAAQ,CAACO,WAAW,CAACC,QAAS;cACrCyC,QAAQ,EAAGC,CAAC,IAAKxB,aAAa,CAAC,aAAa,EAAE,UAAU,EAAEwB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cAC1EG,SAAS,EAAC,iIAAiI;cAAAD,QAAA,gBAE3IxC,OAAA;gBAAQsC,KAAK,EAAC,IAAI;gBAAAE,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC7C,OAAA;gBAAQsC,KAAK,EAAC,IAAI;gBAAAE,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC7C,OAAA;gBAAQsC,KAAK,EAAC,IAAI;gBAAAE,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7C,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAOyC,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEsC,KAAK,EAAE7B,QAAQ,CAACO,WAAW,CAACG,gBAAiB;cAC7CuC,QAAQ,EAAGC,CAAC,IAAKxB,aAAa,CAAC,aAAa,EAAE,kBAAkB,EAAEwB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cAClFG,SAAS,EAAC,iIAAiI;cAAAD,QAAA,gBAE3IxC,OAAA;gBAAQsC,KAAK,EAAC,UAAU;gBAAAE,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C7C,OAAA;gBAAQsC,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnD7C,OAAA;gBAAQsC,KAAK,EAAC,UAAU;gBAAAE,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7C,OAAA;UAAKyC,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBxC,OAAA;YAAKyC,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDxC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAOyC,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,EAAC;cAExE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBAAGyC,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAExD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN7C,OAAA;cACEoD,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC1B,QAAQ,CAACO,WAAW,CAACI,SAAS,CAAE;cAC1FqB,SAAS,EAAE,6EACThC,QAAQ,CAACO,WAAW,CAACI,SAAS,GAAG,gBAAgB,GAAG,aAAa,EAChE;cAAAoB,QAAA,eAEHxC,OAAA;gBACEyC,SAAS,EAAE,6EACThC,QAAQ,CAACO,WAAW,CAACI,SAAS,GAAG,eAAe,GAAG,eAAe;cACjE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb7C,OAAA,CAACN,MAAM,CAACqD,GAAG;QACTN,SAAS,EAAC,oDAAoD;QAC9DO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,gBAE3BxC,OAAA;UAAIyC,SAAS,EAAC,wEAAwE;UAAAD,QAAA,gBACpFxC,OAAA;YAAMyC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,sBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL7C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAOyC,SAAS,EAAC,iEAAiE;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEsC,KAAK,EAAE7B,QAAQ,CAACY,OAAO,CAACC,kBAAmB;cAC3CoC,QAAQ,EAAGC,CAAC,IAAKxB,aAAa,CAAC,SAAS,EAAE,oBAAoB,EAAEwB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cAChFG,SAAS,EAAC,iIAAiI;cAAAD,QAAA,gBAE3IxC,OAAA;gBAAQsC,KAAK,EAAC,QAAQ;gBAAAE,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC7C,OAAA;gBAAQsC,KAAK,EAAC,SAAS;gBAAAE,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C7C,OAAA;gBAAQsC,KAAK,EAAC,SAAS;gBAAAE,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7C,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBxC,OAAA;cAAKyC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDxC,OAAA;gBAAOyC,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,EAAC;cAExE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEoD,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC1B,QAAQ,CAACY,OAAO,CAACE,aAAa,CAAE;gBAC1FkB,SAAS,EAAE,6EACThC,QAAQ,CAACY,OAAO,CAACE,aAAa,GAAG,gBAAgB,GAAG,aAAa,EAChE;gBAAAiB,QAAA,eAEHxC,OAAA;kBACEyC,SAAS,EAAE,6EACThC,QAAQ,CAACY,OAAO,CAACE,aAAa,GAAG,eAAe,GAAG,eAAe;gBACjE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7C,OAAA;cAAKyC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDxC,OAAA;gBAAOyC,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,EAAC;cAExE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEoD,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC,SAAS,EAAE,iBAAiB,EAAE,CAAC1B,QAAQ,CAACY,OAAO,CAACG,eAAe,CAAE;gBAC9FiB,SAAS,EAAE,6EACThC,QAAQ,CAACY,OAAO,CAACG,eAAe,GAAG,gBAAgB,GAAG,aAAa,EAClE;gBAAAgB,QAAA,eAEHxC,OAAA;kBACEyC,SAAS,EAAE,6EACThC,QAAQ,CAACY,OAAO,CAACG,eAAe,GAAG,eAAe,GAAG,eAAe;gBACnE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb7C,OAAA,CAACN,MAAM,CAACqD,GAAG;QACTN,SAAS,EAAC,kBAAkB;QAC5BO,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBI,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,eAE3BxC,OAAA;UACEoD,OAAO,EAAEzB,UAAW;UACpBkC,QAAQ,EAAEpC,OAAQ;UAClBgB,SAAS,EAAC,0IAA0I;UAAAD,QAAA,EAEnJf,OAAO,gBACNzB,OAAA,CAAAE,SAAA;YAAAsC,QAAA,gBACExC,OAAA;cAAKyC,SAAS,EAAC;YAAgE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAExF;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACzC,EAAA,CA/TID,YAAsB;EAAA,QACTR,OAAO,EACOC,QAAQ,EACXC,gBAAgB;AAAA;AAAAiE,EAAA,GAHxC3D,YAAsB;AAiU5B,eAAeA,YAAY;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}