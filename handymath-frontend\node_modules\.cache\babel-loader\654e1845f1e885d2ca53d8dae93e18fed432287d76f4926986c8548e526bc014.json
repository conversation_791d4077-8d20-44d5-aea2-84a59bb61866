{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\SolverPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport ImageUpload from '../components/ImageUpload';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SolverPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title, message) => {\n    addNotification({\n      type: 'success',\n      title,\n      message\n    });\n  };\n  const showError = (title, message) => {\n    addNotification({\n      type: 'error',\n      title,\n      message\n    });\n  };\n  const showWarning = (title, message) => {\n    addNotification({\n      type: 'warning',\n      title,\n      message\n    });\n  };\n\n  // State\n  const [equation, setEquation] = useState('');\n  const [solution, setSolution] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [imageLoading, setImageLoading] = useState(false);\n  const [inputMethod, setInputMethod] = useState('text');\n  const [suggestions, setSuggestions] = useState([]);\n  const [ocrInfo, setOcrInfo] = useState({});\n\n  // Event Handlers\n  const handleSolve = async () => {\n    if (!equation.trim()) {\n      showWarning('Équation manquante', 'Veuillez entrer une équation');\n      return;\n    }\n    if (!user) {\n      showError('Authentification requise', 'Vous devez être connecté pour résoudre des équations');\n      return;\n    }\n    setLoading(true);\n    setSolution(null);\n    try {\n      const response = await api.post('/equations/solve/', {\n        equation: equation.trim()\n      });\n      if (response.data) {\n        const {\n          solution: solutionText,\n          steps,\n          equation_id\n        } = response.data;\n\n        // Debug: Afficher les données reçues\n        console.log('🔍 Données reçues de l\\'API:', response.data);\n        console.log('📋 Étapes reçues:', steps);\n        console.log('📊 Nombre d\\'étapes:', steps ? steps.length : 0);\n        setSolution({\n          result: solutionText,\n          steps: steps || [],\n          equation_id: equation_id\n        });\n        showSuccess('Équation résolue !', `L'équation a été résolue avec succès. ${steps ? steps.length : 0} étapes générées.`);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response3;\n      console.error('Erreur lors de la résolution:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.error) {\n        showError('Erreur de résolution', error.response.data.error);\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 401) {\n        showError('Session expirée', 'Veuillez vous reconnecter.');\n      } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 400) {\n        showError('Équation invalide', 'Vérifiez la syntaxe de votre équation.');\n      } else {\n        showError('Erreur de résolution', 'Une erreur est survenue lors de la résolution.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClear = () => {\n    setEquation('');\n    setSolution(null);\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !loading && user) {\n      handleSolve();\n    }\n  };\n\n  // Gestion de l'upload d'image\n  const handleImageUpload = async file => {\n    if (!user) {\n      showError('Authentification requise', 'Vous devez être connecté pour utiliser la reconnaissance d\\'image');\n      return;\n    }\n    setImageLoading(true);\n    setSolution(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n      const response = await api.post('/equations/recognize/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.recognized_text) {\n        const recognizedEquation = response.data.recognized_text;\n        const confidence = response.data.confidence || 0;\n        const suggestions = response.data.suggestions || [];\n        const rawText = response.data.ocr_raw_text || '';\n        setEquation(recognizedEquation);\n        setSuggestions(suggestions);\n        setOcrInfo({\n          rawText,\n          confidence\n        });\n        if (confidence >= 0.8) {\n          showSuccess('Équation reconnue !', `Équation détectée: ${recognizedEquation} (${Math.round(confidence * 100)}%)`);\n        } else if (confidence >= 0.5) {\n          showWarning('Reconnaissance partielle', `Équation détectée: ${recognizedEquation} (${Math.round(confidence * 100)}%). Vérifiez le résultat.`);\n        } else {\n          showWarning('Reconnaissance difficile', `Texte OCR: \"${rawText}\". ${suggestions.length > 0 ? 'Suggestions disponibles ci-dessous.' : 'Essayez avec une image plus claire.'}`);\n        }\n\n        // Optionnel : résoudre automatiquement l'équation reconnue\n        if (response.data.auto_solve && response.data.solution) {\n          setSolution({\n            result: response.data.solution,\n            steps: response.data.steps || [],\n            equation_id: response.data.equation_id\n          });\n        }\n      } else {\n        showWarning('Reconnaissance échouée', 'Impossible de reconnaître l\\'équation. Essayez avec une image plus claire.');\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Erreur lors de la reconnaissance:', error);\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.error) {\n        showError('Erreur de reconnaissance', error.response.data.error);\n      } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 401) {\n        showError('Session expirée', 'Veuillez vous reconnecter.');\n      } else if (((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.status) === 413) {\n        showError('Fichier trop volumineux', 'L\\'image est trop grande. Taille maximale : 5MB');\n      } else {\n        showError('Erreur de reconnaissance', 'Une erreur est survenue lors de la reconnaissance de l\\'image.');\n      }\n    } finally {\n      setImageLoading(false);\n    }\n  };\n  const handleEquationRecognized = recognizedEquation => {\n    setEquation(recognizedEquation);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"R\\xE9solution d'\\xE9quations\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n        className: \"text-4xl font-bold mb-8 text-center text-primary-600\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: \"R\\xE9solution d'\\xE9quations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setInputMethod('text'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${inputMethod === 'text' ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'}`,\n            children: \"\\u2328\\uFE0F Saisie manuelle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setInputMethod('image'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${inputMethod === 'image' ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'}`,\n            children: \"\\uD83D\\uDCF8 Reconnaissance manuscrite\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: inputMethod === 'text' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"equation\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Entrez votre \\xE9quation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"equation\",\n                value: equation,\n                onChange: e => setEquation(e.target.value),\n                onKeyDown: handleKeyDown,\n                placeholder: \"Ex: 2x + 3 = 7, x^2 - 4 = 0\",\n                className: \"flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\",\n                disabled: loading || imageLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSolve,\n                disabled: loading || imageLoading || !user || !equation.trim(),\n                className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-r-lg transition-colors\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this), \"R\\xE9solution...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this) : 'Résoudre'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), equation && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleClear,\n                className: \"ml-2 bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n                children: \"Effacer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Exemples d'\\xE9quations :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: [\"2x + 3 = 7\", \"x^2 - 4 = 0\", \"3x - 7 = 2x + 8\"].map((example, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setEquation(example),\n                  className: \"text-xs px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n                  disabled: loading || imageLoading,\n                  children: example\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(ImageUpload, {\n              onImageUpload: handleImageUpload,\n              onEquationRecognized: handleEquationRecognized,\n              loading: imageLoading,\n              disabled: loading || !user\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), suggestions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-3\",\n                children: \"\\uD83E\\uDD14 La reconnaissance n'est pas parfaite ? Essayez ces suggestions :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                children: suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setEquation(suggestion);\n                    setSuggestions([]);\n                    showSuccess('Équation sélectionnée', `Équation choisie: ${suggestion}`);\n                  },\n                  className: \"text-left p-3 bg-white dark:bg-gray-700 rounded-lg border border-yellow-300 dark:border-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-800/30 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-mono text-sm text-gray-900 dark:text-white\",\n                    children: suggestion\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-yellow-700 dark:text-yellow-400\",\n                  children: ocrInfo.rawText && `Texte OCR détecté: \"${ocrInfo.rawText}\"`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setSuggestions([]),\n                  className: \"text-xs text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200\",\n                  children: \"Masquer les suggestions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), equation && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-medium text-green-800 dark:text-green-300 mb-2\",\n                children: \"\\u2705 \\xC9quation reconnue :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-mono text-green-700 dark:text-green-400 mb-3\",\n                children: equation\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSolve,\n                  disabled: loading || imageLoading || !user,\n                  className: `flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${loading || imageLoading || !user ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl'}`,\n                  children: loading ? 'Résolution en cours...' : 'Résoudre cette équation'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setEquation(''),\n                  className: \"px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors\",\n                  children: \"Effacer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 9\n        }, this), !user && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"mt-4 p-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-200 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl mr-2\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Vous devez \\xEAtre connect\\xE9 pour r\\xE9soudre des \\xE9quations.\", ' ', /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/login\",\n                className: \"underline font-medium\",\n                children: \"Se connecter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), solution && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"mt-8\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold text-primary-600\",\n              children: \"Solution\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), solution.equation_id && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 dark:text-gray-400\",\n              children: [\"ID: #\", solution.equation_id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 rounded-lg mb-6 border border-green-200 dark:border-green-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-semibold text-gray-700 dark:text-gray-300\",\n                children: \"R\\xE9sultat final :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-mono font-bold text-green-700 dark:text-green-300\",\n              children: solution.result\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), solution.steps && solution.steps.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white flex items-center\",\n                children: [\"\\uD83D\\uDCDA \\xC9tapes de r\\xE9solution d\\xE9taill\\xE9es\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm font-normal text-gray-500 dark:text-gray-400\",\n                  children: [\"(\", solution.steps.length, \" \\xE9tapes)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: solution.steps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  className: \"flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-sm font-semibold\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900 dark:text-white font-mono text-sm leading-relaxed\",\n                      children: step\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SolverPage, \"r1zrvTBOL9ME34Rf9T74JgrOePY=\", false, function () {\n  return [useAuth, useNotifications];\n});\n_c = SolverPage;\nexport default SolverPage;\nvar _c;\n$RefreshReg$(_c, \"SolverPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "api", "useAuth", "useNotifications", "ImageUpload", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SolverPage", "_s", "user", "addNotification", "showSuccess", "title", "message", "type", "showError", "showWarning", "equation", "setEquation", "solution", "setSolution", "loading", "setLoading", "imageLoading", "setImageLoading", "inputMethod", "setInputMethod", "suggestions", "setSuggestions", "ocrInfo", "setOcrInfo", "handleSolve", "trim", "response", "post", "data", "solutionText", "steps", "equation_id", "console", "log", "length", "result", "error", "_error$response", "_error$response$data", "_error$response2", "_error$response3", "status", "handleClear", "handleKeyDown", "e", "key", "handleImageUpload", "file", "formData", "FormData", "append", "headers", "recognized_text", "recognizedEquation", "confidence", "rawText", "ocr_raw_text", "Math", "round", "auto_solve", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "handleEquationRecognized", "children", "SimpleHeader", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "h1", "initial", "opacity", "y", "animate", "onClick", "htmlFor", "id", "value", "onChange", "target", "onKeyDown", "placeholder", "disabled", "map", "example", "index", "onImageUpload", "onEquationRecognized", "suggestion", "div", "href", "step", "x", "transition", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/SolverPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport ImageUpload from '../components/ImageUpload';\nimport StudentLayout from '../components/StudentLayout';\n\nconst SolverPage: React.FC = () => {\n  const { user } = useAuth();\n  const { addNotification } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message });\n  };\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message });\n  };\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message });\n  };\n\n  // State\n  const [equation, setEquation] = useState('');\n  const [solution, setSolution] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [imageLoading, setImageLoading] = useState(false);\n  const [inputMethod, setInputMethod] = useState<'text' | 'image'>('text');\n  const [suggestions, setSuggestions] = useState<string[]>([]);\n  const [ocrInfo, setOcrInfo] = useState<{rawText?: string, confidence?: number}>({});\n\n  // Event Handlers\n  const handleSolve = async () => {\n    if (!equation.trim()) {\n      showWarning('Équation manquante', 'Veuillez entrer une équation');\n      return;\n    }\n\n    if (!user) {\n      showError('Authentification requise', 'Vous devez être connecté pour résoudre des équations');\n      return;\n    }\n\n    setLoading(true);\n    setSolution(null);\n\n    try {\n      const response = await api.post('/equations/solve/', {\n        equation: equation.trim()\n      });\n\n      if (response.data) {\n        const { solution: solutionText, steps, equation_id } = response.data;\n\n        // Debug: Afficher les données reçues\n        console.log('🔍 Données reçues de l\\'API:', response.data);\n        console.log('📋 Étapes reçues:', steps);\n        console.log('📊 Nombre d\\'étapes:', steps ? steps.length : 0);\n\n        setSolution({\n          result: solutionText,\n          steps: steps || [],\n          equation_id: equation_id\n        });\n        showSuccess('Équation résolue !', `L'équation a été résolue avec succès. ${steps ? steps.length : 0} étapes générées.`);\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la résolution:', error);\n      if (error.response?.data?.error) {\n        showError('Erreur de résolution', error.response.data.error);\n      } else if (error.response?.status === 401) {\n        showError('Session expirée', 'Veuillez vous reconnecter.');\n      } else if (error.response?.status === 400) {\n        showError('Équation invalide', 'Vérifiez la syntaxe de votre équation.');\n      } else {\n        showError('Erreur de résolution', 'Une erreur est survenue lors de la résolution.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClear = () => {\n    setEquation('');\n    setSolution(null);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !loading && user) {\n      handleSolve();\n    }\n  };\n\n  // Gestion de l'upload d'image\n  const handleImageUpload = async (file: File) => {\n    if (!user) {\n      showError('Authentification requise', 'Vous devez être connecté pour utiliser la reconnaissance d\\'image');\n      return;\n    }\n\n    setImageLoading(true);\n    setSolution(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n\n      const response = await api.post('/equations/recognize/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (response.data && response.data.recognized_text) {\n        const recognizedEquation = response.data.recognized_text;\n        const confidence = response.data.confidence || 0;\n        const suggestions = response.data.suggestions || [];\n        const rawText = response.data.ocr_raw_text || '';\n\n        setEquation(recognizedEquation);\n        setSuggestions(suggestions);\n        setOcrInfo({ rawText, confidence });\n\n        if (confidence >= 0.8) {\n          showSuccess('Équation reconnue !', `Équation détectée: ${recognizedEquation} (${Math.round(confidence * 100)}%)`);\n        } else if (confidence >= 0.5) {\n          showWarning('Reconnaissance partielle', `Équation détectée: ${recognizedEquation} (${Math.round(confidence * 100)}%). Vérifiez le résultat.`);\n        } else {\n          showWarning('Reconnaissance difficile', `Texte OCR: \"${rawText}\". ${suggestions.length > 0 ? 'Suggestions disponibles ci-dessous.' : 'Essayez avec une image plus claire.'}`);\n        }\n\n        // Optionnel : résoudre automatiquement l'équation reconnue\n        if (response.data.auto_solve && response.data.solution) {\n          setSolution({\n            result: response.data.solution,\n            steps: response.data.steps || [],\n            equation_id: response.data.equation_id\n          });\n        }\n      } else {\n        showWarning('Reconnaissance échouée', 'Impossible de reconnaître l\\'équation. Essayez avec une image plus claire.');\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la reconnaissance:', error);\n      if (error.response?.data?.error) {\n        showError('Erreur de reconnaissance', error.response.data.error);\n      } else if (error.response?.status === 401) {\n        showError('Session expirée', 'Veuillez vous reconnecter.');\n      } else if (error.response?.status === 413) {\n        showError('Fichier trop volumineux', 'L\\'image est trop grande. Taille maximale : 5MB');\n      } else {\n        showError('Erreur de reconnaissance', 'Une erreur est survenue lors de la reconnaissance de l\\'image.');\n      }\n    } finally {\n      setImageLoading(false);\n    }\n  };\n\n  const handleEquationRecognized = (recognizedEquation: string) => {\n    setEquation(recognizedEquation);\n  };\n\n  return (\n    <>\n      <SimpleHeader title=\"Résolution d'équations\" />\n      <div className=\"container mx-auto px-4 py-8\">\n        <motion.h1\n          className=\"text-4xl font-bold mb-8 text-center text-primary-600\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          Résolution d'équations\n        </motion.h1>\n\n      <div className=\"max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n        {/* Onglets de sélection */}\n        <div className=\"flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6\">\n          <button\n            onClick={() => setInputMethod('text')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${\n              inputMethod === 'text'\n                ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'\n                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\n            }`}\n          >\n            ⌨️ Saisie manuelle\n          </button>\n          <button\n            onClick={() => setInputMethod('image')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${\n              inputMethod === 'image'\n                ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'\n                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\n            }`}\n          >\n            📸 Reconnaissance manuscrite\n          </button>\n        </div>\n\n        {/* Contenu selon la méthode sélectionnée */}\n        <div className=\"mb-6\">\n          {inputMethod === 'text' ? (\n            <div>\n              <label htmlFor=\"equation\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Entrez votre équation\n              </label>\n              <div className=\"flex\">\n                <input\n                  type=\"text\"\n                  id=\"equation\"\n                  value={equation}\n                  onChange={(e) => setEquation(e.target.value)}\n                  onKeyDown={handleKeyDown}\n                  placeholder=\"Ex: 2x + 3 = 7, x^2 - 4 = 0\"\n                  className=\"flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  disabled={loading || imageLoading}\n                />\n                <button\n                  onClick={handleSolve}\n                  disabled={loading || imageLoading || !user || !equation.trim()}\n                  className=\"bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-r-lg transition-colors\"\n                >\n                  {loading ? (\n                    <div className=\"flex items-center\">\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                      Résolution...\n                    </div>\n                  ) : 'Résoudre'}\n                </button>\n                {equation && (\n                  <button\n                    onClick={handleClear}\n                    className=\"ml-2 bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n                  >\n                    Effacer\n                  </button>\n                )}\n              </div>\n\n              {/* Exemples d'équations */}\n              <div className=\"mt-4\">\n                <p className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Exemples d'équations :\n                </p>\n                <div className=\"flex flex-wrap gap-2\">\n                  {[\n                    \"2x + 3 = 7\",\n                    \"x^2 - 4 = 0\",\n                    \"3x - 7 = 2x + 8\"\n                  ].map((example, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setEquation(example)}\n                      className=\"text-xs px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                      disabled={loading || imageLoading}\n                    >\n                      {example}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <ImageUpload\n                onImageUpload={handleImageUpload}\n                onEquationRecognized={handleEquationRecognized}\n                loading={imageLoading}\n                disabled={loading || !user}\n              />\n\n              {/* Suggestions d'équations si la reconnaissance a échoué */}\n              {suggestions.length > 0 && (\n                <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700\">\n                  <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-3\">\n                    🤔 La reconnaissance n'est pas parfaite ? Essayez ces suggestions :\n                  </h4>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\n                    {suggestions.map((suggestion, index) => (\n                      <button\n                        key={index}\n                        onClick={() => {\n                          setEquation(suggestion);\n                          setSuggestions([]);\n                          showSuccess('Équation sélectionnée', `Équation choisie: ${suggestion}`);\n                        }}\n                        className=\"text-left p-3 bg-white dark:bg-gray-700 rounded-lg border border-yellow-300 dark:border-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-800/30 transition-colors\"\n                      >\n                        <span className=\"font-mono text-sm text-gray-900 dark:text-white\">\n                          {suggestion}\n                        </span>\n                      </button>\n                    ))}\n                  </div>\n                  <div className=\"mt-3 flex justify-between items-center\">\n                    <p className=\"text-xs text-yellow-700 dark:text-yellow-400\">\n                      {ocrInfo.rawText && `Texte OCR détecté: \"${ocrInfo.rawText}\"`}\n                    </p>\n                    <button\n                      onClick={() => setSuggestions([])}\n                      className=\"text-xs text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200\"\n                    >\n                      Masquer les suggestions\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {equation && (\n                <div className=\"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700\">\n                  <h4 className=\"text-sm font-medium text-green-800 dark:text-green-300 mb-2\">\n                    ✅ Équation reconnue :\n                  </h4>\n                  <p className=\"text-lg font-mono text-green-700 dark:text-green-400 mb-3\">\n                    {equation}\n                  </p>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={handleSolve}\n                      disabled={loading || imageLoading || !user}\n                      className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${\n                        loading || imageLoading || !user\n                          ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'\n                          : 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl'\n                      }`}\n                    >\n                      {loading ? 'Résolution en cours...' : 'Résoudre cette équation'}\n                    </button>\n                    <button\n                      onClick={() => setEquation('')}\n                      className=\"px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors\"\n                    >\n                      Effacer\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Avertissement pour utilisateurs non connectés */}\n        {!user && (\n          <motion.div\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"mt-4 p-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-200 rounded-lg\"\n          >\n            <div className=\"flex items-center\">\n              <span className=\"text-xl mr-2\">⚠️</span>\n              <span>\n                Vous devez être connecté pour résoudre des équations.{' '}\n                <a href=\"/login\" className=\"underline font-medium\">Se connecter</a>\n              </span>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Section solution */}\n        {solution && (\n          <motion.div\n            className=\"mt-8\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n          >\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-2xl font-semibold text-primary-600\">Solution</h2>\n              {solution.equation_id && (\n                <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  ID: #{solution.equation_id}\n                </div>\n              )}\n            </div>\n\n            {/* Résultat final */}\n            <div className=\"p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 rounded-lg mb-6 border border-green-200 dark:border-green-700\">\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-lg font-semibold text-gray-700 dark:text-gray-300\">Résultat final :</span>\n              </div>\n              <p className=\"text-2xl font-mono font-bold text-green-700 dark:text-green-300\">\n                {solution.result}\n              </p>\n            </div>\n\n            {/* Étapes de résolution détaillées */}\n            {solution.steps && solution.steps.length > 0 && (\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden\">\n                <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                    📚 Étapes de résolution détaillées\n                    <span className=\"ml-2 text-sm font-normal text-gray-500 dark:text-gray-400\">\n                      ({solution.steps.length} étapes)\n                    </span>\n                  </h3>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"space-y-4\">\n                    {solution.steps.map((step: string, index: number) => (\n                      <motion.div\n                        key={index}\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: index * 0.1 }}\n                        className=\"flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600\"\n                      >\n                        <div className=\"flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-sm font-semibold\">\n                          {index + 1}\n                        </div>\n                        <div className=\"flex-1\">\n                          <p className=\"text-gray-900 dark:text-white font-mono text-sm leading-relaxed\">\n                            {step}\n                          </p>\n                        </div>\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default SolverPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGpD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEU;EAAgB,CAAC,GAAGT,gBAAgB,CAAC,CAAC;;EAE9C;EACA,MAAMU,WAAW,GAAGA,CAACC,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;EACD,MAAME,SAAS,GAAGA,CAACH,KAAa,EAAEC,OAAe,KAAK;IACpDH,eAAe,CAAC;MAAEI,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACpD,CAAC;EACD,MAAMG,WAAW,GAAGA,CAACJ,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAM,IAAI,CAAC;EACnD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAmB,MAAM,CAAC;EACxE,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAA0C,CAAC,CAAC,CAAC;;EAEnF;EACA,MAAMkC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACd,QAAQ,CAACe,IAAI,CAAC,CAAC,EAAE;MACpBhB,WAAW,CAAC,oBAAoB,EAAE,8BAA8B,CAAC;MACjE;IACF;IAEA,IAAI,CAACP,IAAI,EAAE;MACTM,SAAS,CAAC,0BAA0B,EAAE,sDAAsD,CAAC;MAC7F;IACF;IAEAO,UAAU,CAAC,IAAI,CAAC;IAChBF,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMlC,GAAG,CAACmC,IAAI,CAAC,mBAAmB,EAAE;QACnDjB,QAAQ,EAAEA,QAAQ,CAACe,IAAI,CAAC;MAC1B,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACE,IAAI,EAAE;QACjB,MAAM;UAAEhB,QAAQ,EAAEiB,YAAY;UAAEC,KAAK;UAAEC;QAAY,CAAC,GAAGL,QAAQ,CAACE,IAAI;;QAEpE;QACAI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEP,QAAQ,CAACE,IAAI,CAAC;QAC1DI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,KAAK,CAAC;QACvCE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,KAAK,GAAGA,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;QAE7DrB,WAAW,CAAC;UACVsB,MAAM,EAAEN,YAAY;UACpBC,KAAK,EAAEA,KAAK,IAAI,EAAE;UAClBC,WAAW,EAAEA;QACf,CAAC,CAAC;QACF3B,WAAW,CAAC,oBAAoB,EAAE,yCAAyC0B,KAAK,GAAGA,KAAK,CAACI,MAAM,GAAG,CAAC,mBAAmB,CAAC;MACzH;IACF,CAAC,CAAC,OAAOE,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACnBR,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,KAAAC,eAAA,GAAID,KAAK,CAACV,QAAQ,cAAAW,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBT,IAAI,cAAAU,oBAAA,eAApBA,oBAAA,CAAsBF,KAAK,EAAE;QAC/B5B,SAAS,CAAC,sBAAsB,EAAE4B,KAAK,CAACV,QAAQ,CAACE,IAAI,CAACQ,KAAK,CAAC;MAC9D,CAAC,MAAM,IAAI,EAAAG,gBAAA,GAAAH,KAAK,CAACV,QAAQ,cAAAa,gBAAA,uBAAdA,gBAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QACzCjC,SAAS,CAAC,iBAAiB,EAAE,4BAA4B,CAAC;MAC5D,CAAC,MAAM,IAAI,EAAAgC,gBAAA,GAAAJ,KAAK,CAACV,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QACzCjC,SAAS,CAAC,mBAAmB,EAAE,wCAAwC,CAAC;MAC1E,CAAC,MAAM;QACLA,SAAS,CAAC,sBAAsB,EAAE,gDAAgD,CAAC;MACrF;IACF,CAAC,SAAS;MACRO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,WAAW,GAAGA,CAAA,KAAM;IACxB/B,WAAW,CAAC,EAAE,CAAC;IACfE,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAM8B,aAAa,GAAIC,CAAsB,IAAK;IAChD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAAC/B,OAAO,IAAIZ,IAAI,EAAE;MACzCsB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAG,MAAOC,IAAU,IAAK;IAC9C,IAAI,CAAC7C,IAAI,EAAE;MACTM,SAAS,CAAC,0BAA0B,EAAE,mEAAmE,CAAC;MAC1G;IACF;IAEAS,eAAe,CAAC,IAAI,CAAC;IACrBJ,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,MAAMmC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;MAE9B,MAAMrB,QAAQ,GAAG,MAAMlC,GAAG,CAACmC,IAAI,CAAC,uBAAuB,EAAEqB,QAAQ,EAAE;QACjEG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIzB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACwB,eAAe,EAAE;QAClD,MAAMC,kBAAkB,GAAG3B,QAAQ,CAACE,IAAI,CAACwB,eAAe;QACxD,MAAME,UAAU,GAAG5B,QAAQ,CAACE,IAAI,CAAC0B,UAAU,IAAI,CAAC;QAChD,MAAMlC,WAAW,GAAGM,QAAQ,CAACE,IAAI,CAACR,WAAW,IAAI,EAAE;QACnD,MAAMmC,OAAO,GAAG7B,QAAQ,CAACE,IAAI,CAAC4B,YAAY,IAAI,EAAE;QAEhD7C,WAAW,CAAC0C,kBAAkB,CAAC;QAC/BhC,cAAc,CAACD,WAAW,CAAC;QAC3BG,UAAU,CAAC;UAAEgC,OAAO;UAAED;QAAW,CAAC,CAAC;QAEnC,IAAIA,UAAU,IAAI,GAAG,EAAE;UACrBlD,WAAW,CAAC,qBAAqB,EAAE,sBAAsBiD,kBAAkB,KAAKI,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;QACnH,CAAC,MAAM,IAAIA,UAAU,IAAI,GAAG,EAAE;UAC5B7C,WAAW,CAAC,0BAA0B,EAAE,sBAAsB4C,kBAAkB,KAAKI,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAG,GAAG,CAAC,2BAA2B,CAAC;QAC/I,CAAC,MAAM;UACL7C,WAAW,CAAC,0BAA0B,EAAE,eAAe8C,OAAO,MAAMnC,WAAW,CAACc,MAAM,GAAG,CAAC,GAAG,qCAAqC,GAAG,qCAAqC,EAAE,CAAC;QAC/K;;QAEA;QACA,IAAIR,QAAQ,CAACE,IAAI,CAAC+B,UAAU,IAAIjC,QAAQ,CAACE,IAAI,CAAChB,QAAQ,EAAE;UACtDC,WAAW,CAAC;YACVsB,MAAM,EAAET,QAAQ,CAACE,IAAI,CAAChB,QAAQ;YAC9BkB,KAAK,EAAEJ,QAAQ,CAACE,IAAI,CAACE,KAAK,IAAI,EAAE;YAChCC,WAAW,EAAEL,QAAQ,CAACE,IAAI,CAACG;UAC7B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLtB,WAAW,CAAC,wBAAwB,EAAE,4EAA4E,CAAC;MACrH;IACF,CAAC,CAAC,OAAO2B,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACnB/B,OAAO,CAACI,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,KAAAwB,gBAAA,GAAIxB,KAAK,CAACV,QAAQ,cAAAkC,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,eAApBA,qBAAA,CAAsBzB,KAAK,EAAE;QAC/B5B,SAAS,CAAC,0BAA0B,EAAE4B,KAAK,CAACV,QAAQ,CAACE,IAAI,CAACQ,KAAK,CAAC;MAClE,CAAC,MAAM,IAAI,EAAA0B,gBAAA,GAAA1B,KAAK,CAACV,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBrB,MAAM,MAAK,GAAG,EAAE;QACzCjC,SAAS,CAAC,iBAAiB,EAAE,4BAA4B,CAAC;MAC5D,CAAC,MAAM,IAAI,EAAAuD,gBAAA,GAAA3B,KAAK,CAACV,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM,MAAK,GAAG,EAAE;QACzCjC,SAAS,CAAC,yBAAyB,EAAE,iDAAiD,CAAC;MACzF,CAAC,MAAM;QACLA,SAAS,CAAC,0BAA0B,EAAE,gEAAgE,CAAC;MACzG;IACF,CAAC,SAAS;MACRS,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM+C,wBAAwB,GAAIX,kBAA0B,IAAK;IAC/D1C,WAAW,CAAC0C,kBAAkB,CAAC;EACjC,CAAC;EAED,oBACExD,OAAA,CAAAE,SAAA;IAAAkE,QAAA,gBACEpE,OAAA,CAACqE,YAAY;MAAC7D,KAAK,EAAC;IAAwB;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/CzE,OAAA;MAAK0E,SAAS,EAAC,6BAA6B;MAAAN,QAAA,gBAC1CpE,OAAA,CAACN,MAAM,CAACiF,EAAE;QACRD,SAAS,EAAC,sDAAsD;QAChEE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,EAC/B;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAEdzE,OAAA;QAAK0E,SAAS,EAAC,sEAAsE;QAAAN,QAAA,gBAEnFpE,OAAA;UAAK0E,SAAS,EAAC,iEAAiE;UAAAN,QAAA,gBAC9EpE,OAAA;YACEgF,OAAO,EAAEA,CAAA,KAAM1D,cAAc,CAAC,MAAM,CAAE;YACtCoD,SAAS,EAAE,+EACTrD,WAAW,KAAK,MAAM,GAClB,4EAA4E,GAC5E,+EAA+E,EAClF;YAAA+C,QAAA,EACJ;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzE,OAAA;YACEgF,OAAO,EAAEA,CAAA,KAAM1D,cAAc,CAAC,OAAO,CAAE;YACvCoD,SAAS,EAAE,+EACTrD,WAAW,KAAK,OAAO,GACnB,4EAA4E,GAC5E,+EAA+E,EAClF;YAAA+C,QAAA,EACJ;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzE,OAAA;UAAK0E,SAAS,EAAC,MAAM;UAAAN,QAAA,EAClB/C,WAAW,KAAK,MAAM,gBACrBrB,OAAA;YAAAoE,QAAA,gBACEpE,OAAA;cAAOiF,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,iEAAiE;cAAAN,QAAA,EAAC;YAEtG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzE,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAN,QAAA,gBACnBpE,OAAA;gBACEU,IAAI,EAAC,MAAM;gBACXwE,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAEtE,QAAS;gBAChBuE,QAAQ,EAAGrC,CAAC,IAAKjC,WAAW,CAACiC,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;gBAC7CG,SAAS,EAAExC,aAAc;gBACzByC,WAAW,EAAC,6BAA6B;gBACzCb,SAAS,EAAC,sJAAsJ;gBAChKc,QAAQ,EAAEvE,OAAO,IAAIE;cAAa;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzE,OAAA;gBACEgF,OAAO,EAAErD,WAAY;gBACrB6D,QAAQ,EAAEvE,OAAO,IAAIE,YAAY,IAAI,CAACd,IAAI,IAAI,CAACQ,QAAQ,CAACe,IAAI,CAAC,CAAE;gBAC/D8C,SAAS,EAAC,sJAAsJ;gBAAAN,QAAA,EAE/JnD,OAAO,gBACNjB,OAAA;kBAAK0E,SAAS,EAAC,mBAAmB;kBAAAN,QAAA,gBAChCpE,OAAA;oBAAK0E,SAAS,EAAC;kBAAgE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,oBAExF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,GACJ;cAAU;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,EACR5D,QAAQ,iBACPb,OAAA;gBACEgF,OAAO,EAAEnC,WAAY;gBACrB6B,SAAS,EAAC,kGAAkG;gBAAAN,QAAA,EAC7G;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNzE,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAN,QAAA,gBACnBpE,OAAA;gBAAG0E,SAAS,EAAC,2DAA2D;gBAAAN,QAAA,EAAC;cAEzE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzE,OAAA;gBAAK0E,SAAS,EAAC,sBAAsB;gBAAAN,QAAA,EAClC,CACC,YAAY,EACZ,aAAa,EACb,iBAAiB,CAClB,CAACqB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnB3F,OAAA;kBAEEgF,OAAO,EAAEA,CAAA,KAAMlE,WAAW,CAAC4E,OAAO,CAAE;kBACpChB,SAAS,EAAC,wHAAwH;kBAClIc,QAAQ,EAAEvE,OAAO,IAAIE,YAAa;kBAAAiD,QAAA,EAEjCsB;gBAAO,GALHC,KAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENzE,OAAA;YAAK0E,SAAS,EAAC,WAAW;YAAAN,QAAA,gBACxBpE,OAAA,CAACF,WAAW;cACV8F,aAAa,EAAE3C,iBAAkB;cACjC4C,oBAAoB,EAAE1B,wBAAyB;cAC/ClD,OAAO,EAAEE,YAAa;cACtBqE,QAAQ,EAAEvE,OAAO,IAAI,CAACZ;YAAK;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EAGDlD,WAAW,CAACc,MAAM,GAAG,CAAC,iBACrBrC,OAAA;cAAK0E,SAAS,EAAC,wGAAwG;cAAAN,QAAA,gBACrHpE,OAAA;gBAAI0E,SAAS,EAAC,+DAA+D;gBAAAN,QAAA,EAAC;cAE9E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzE,OAAA;gBAAK0E,SAAS,EAAC,uCAAuC;gBAAAN,QAAA,EACnD7C,WAAW,CAACkE,GAAG,CAAC,CAACK,UAAU,EAAEH,KAAK,kBACjC3F,OAAA;kBAEEgF,OAAO,EAAEA,CAAA,KAAM;oBACblE,WAAW,CAACgF,UAAU,CAAC;oBACvBtE,cAAc,CAAC,EAAE,CAAC;oBAClBjB,WAAW,CAAC,uBAAuB,EAAE,qBAAqBuF,UAAU,EAAE,CAAC;kBACzE,CAAE;kBACFpB,SAAS,EAAC,sKAAsK;kBAAAN,QAAA,eAEhLpE,OAAA;oBAAM0E,SAAS,EAAC,iDAAiD;oBAAAN,QAAA,EAC9D0B;kBAAU;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC,GAVFkB,KAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNzE,OAAA;gBAAK0E,SAAS,EAAC,wCAAwC;gBAAAN,QAAA,gBACrDpE,OAAA;kBAAG0E,SAAS,EAAC,8CAA8C;kBAAAN,QAAA,EACxD3C,OAAO,CAACiC,OAAO,IAAI,uBAAuBjC,OAAO,CAACiC,OAAO;gBAAG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACJzE,OAAA;kBACEgF,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,EAAE,CAAE;kBAClCkD,SAAS,EAAC,+FAA+F;kBAAAN,QAAA,EAC1G;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEA5D,QAAQ,iBACPb,OAAA;cAAK0E,SAAS,EAAC,+FAA+F;cAAAN,QAAA,gBAC5GpE,OAAA;gBAAI0E,SAAS,EAAC,6DAA6D;gBAAAN,QAAA,EAAC;cAE5E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzE,OAAA;gBAAG0E,SAAS,EAAC,2DAA2D;gBAAAN,QAAA,EACrEvD;cAAQ;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACJzE,OAAA;gBAAK0E,SAAS,EAAC,gBAAgB;gBAAAN,QAAA,gBAC7BpE,OAAA;kBACEgF,OAAO,EAAErD,WAAY;kBACrB6D,QAAQ,EAAEvE,OAAO,IAAIE,YAAY,IAAI,CAACd,IAAK;kBAC3CqE,SAAS,EAAE,uEACTzD,OAAO,IAAIE,YAAY,IAAI,CAACd,IAAI,GAC5B,kFAAkF,GAClF,sEAAsE,EACzE;kBAAA+D,QAAA,EAEFnD,OAAO,GAAG,wBAAwB,GAAG;gBAAyB;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACTzE,OAAA;kBACEgF,OAAO,EAAEA,CAAA,KAAMlE,WAAW,CAAC,EAAE,CAAE;kBAC/B4D,SAAS,EAAC,iFAAiF;kBAAAN,QAAA,EAC5F;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL,CAACpE,IAAI,iBACJL,OAAA,CAACN,MAAM,CAACqG,GAAG;UACTnB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BJ,SAAS,EAAC,2IAA2I;UAAAN,QAAA,eAErJpE,OAAA;YAAK0E,SAAS,EAAC,mBAAmB;YAAAN,QAAA,gBAChCpE,OAAA;cAAM0E,SAAS,EAAC,cAAc;cAAAN,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCzE,OAAA;cAAAoE,QAAA,GAAM,mEACiD,EAAC,GAAG,eACzDpE,OAAA;gBAAGgG,IAAI,EAAC,QAAQ;gBAACtB,SAAS,EAAC,uBAAuB;gBAAAN,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,EAGA1D,QAAQ,iBACPf,OAAA,CAACN,MAAM,CAACqG,GAAG;UACTrB,SAAS,EAAC,MAAM;UAChBE,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UAAAT,QAAA,gBAExBpE,OAAA;YAAK0E,SAAS,EAAC,wCAAwC;YAAAN,QAAA,gBACrDpE,OAAA;cAAI0E,SAAS,EAAC,yCAAyC;cAAAN,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACpE1D,QAAQ,CAACmB,WAAW,iBACnBlC,OAAA;cAAK0E,SAAS,EAAC,0CAA0C;cAAAN,QAAA,GAAC,OACnD,EAACrD,QAAQ,CAACmB,WAAW;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzE,OAAA;YAAK0E,SAAS,EAAC,kJAAkJ;YAAAN,QAAA,gBAC/JpE,OAAA;cAAK0E,SAAS,EAAC,wBAAwB;cAAAN,QAAA,eACrCpE,OAAA;gBAAM0E,SAAS,EAAC,wDAAwD;gBAAAN,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACNzE,OAAA;cAAG0E,SAAS,EAAC,iEAAiE;cAAAN,QAAA,EAC3ErD,QAAQ,CAACuB;YAAM;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAGL1D,QAAQ,CAACkB,KAAK,IAAIlB,QAAQ,CAACkB,KAAK,CAACI,MAAM,GAAG,CAAC,iBAC1CrC,OAAA;YAAK0E,SAAS,EAAC,kGAAkG;YAAAN,QAAA,gBAC/GpE,OAAA;cAAK0E,SAAS,EAAC,qFAAqF;cAAAN,QAAA,eAClGpE,OAAA;gBAAI0E,SAAS,EAAC,uEAAuE;gBAAAN,QAAA,GAAC,0DAEpF,eAAApE,OAAA;kBAAM0E,SAAS,EAAC,2DAA2D;kBAAAN,QAAA,GAAC,GACzE,EAACrD,QAAQ,CAACkB,KAAK,CAACI,MAAM,EAAC,aAC1B;gBAAA;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNzE,OAAA;cAAK0E,SAAS,EAAC,KAAK;cAAAN,QAAA,eAClBpE,OAAA;gBAAK0E,SAAS,EAAC,WAAW;gBAAAN,QAAA,EACvBrD,QAAQ,CAACkB,KAAK,CAACwD,GAAG,CAAC,CAACQ,IAAY,EAAEN,KAAa,kBAC9C3F,OAAA,CAACN,MAAM,CAACqG,GAAG;kBAETnB,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCnB,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEqB,CAAC,EAAE;kBAAE,CAAE;kBAC9BC,UAAU,EAAE;oBAAEC,KAAK,EAAET,KAAK,GAAG;kBAAI,CAAE;kBACnCjB,SAAS,EAAC,mHAAmH;kBAAAN,QAAA,gBAE7HpE,OAAA;oBAAK0E,SAAS,EAAC,qKAAqK;oBAAAN,QAAA,EACjLuB,KAAK,GAAG;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNzE,OAAA;oBAAK0E,SAAS,EAAC,QAAQ;oBAAAN,QAAA,eACrBpE,OAAA;sBAAG0E,SAAS,EAAC,iEAAiE;sBAAAN,QAAA,EAC3E6B;oBAAI;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA,GAbDkB,KAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAACrE,EAAA,CAlaID,UAAoB;EAAA,QACPP,OAAO,EACIC,gBAAgB;AAAA;AAAAwG,EAAA,GAFxClG,UAAoB;AAoa1B,eAAeA,UAAU;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}