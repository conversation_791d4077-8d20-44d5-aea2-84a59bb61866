{"ast": null, "code": "export var permutationsDocs = {\n  name: 'permutations',\n  category: 'Probability',\n  syntax: ['permutations(n)', 'permutations(n, k)'],\n  description: 'Compute the number of permutations of n items taken k at a time',\n  examples: ['permutations(5)', 'permutations(5, 3)'],\n  seealso: ['combinations', 'combinationsWithRep', 'factorial']\n};", "map": {"version": 3, "names": ["permutationsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/permutations.js"], "sourcesContent": ["export var permutationsDocs = {\n  name: 'permutations',\n  category: 'Probability',\n  syntax: ['permutations(n)', 'permutations(n, k)'],\n  description: 'Compute the number of permutations of n items taken k at a time',\n  examples: ['permutations(5)', 'permutations(5, 3)'],\n  seealso: ['combinations', 'combinationsWithRep', 'factorial']\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;EACjDC,WAAW,EAAE,iEAAiE;EAC9EC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;EACnDC,OAAO,EAAE,CAAC,cAAc,EAAE,qBAAqB,EAAE,WAAW;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}