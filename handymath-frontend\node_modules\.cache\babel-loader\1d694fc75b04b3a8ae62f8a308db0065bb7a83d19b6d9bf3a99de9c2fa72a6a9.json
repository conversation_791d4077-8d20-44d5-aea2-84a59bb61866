{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { absNumber } from '../../plain/number/index.js';\nvar name = 'abs';\nvar dependencies = ['typed'];\nexport var createAbs = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Calculate the absolute value of a number. For matrices, the function is\n   * evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.abs(x)\n   *\n   * Examples:\n   *\n   *    math.abs(3.5)                // returns number 3.5\n   *    math.abs(-4.2)               // returns number 4.2\n   *\n   *    math.abs([3, -5, -1, 0, 2])  // returns Array [3, 5, 1, 0, 2]\n   *\n   * See also:\n   *\n   *    sign\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Array | Matrix | Unit} x\n   *            A number or matrix for which to get the absolute value\n   * @return {number | BigNumber | bigint | Fraction | Complex | Array | Matrix | Unit}\n   *            Absolute value of `x`\n   */\n  return typed(name, {\n    number: absNumber,\n    'Complex | BigNumber | Fraction | Unit': x => x.abs(),\n    bigint: x => x < 0n ? -x : x,\n    // deep map collection, skip zeros since abs(0) = 0\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self, true))\n  });\n});", "map": {"version": 3, "names": ["factory", "deepMap", "absNumber", "name", "dependencies", "createAbs", "_ref", "typed", "number", "x", "abs", "bigint", "referToSelf", "self"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/arithmetic/abs.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { absNumber } from '../../plain/number/index.js';\nvar name = 'abs';\nvar dependencies = ['typed'];\nexport var createAbs = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Calculate the absolute value of a number. For matrices, the function is\n   * evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.abs(x)\n   *\n   * Examples:\n   *\n   *    math.abs(3.5)                // returns number 3.5\n   *    math.abs(-4.2)               // returns number 4.2\n   *\n   *    math.abs([3, -5, -1, 0, 2])  // returns Array [3, 5, 1, 0, 2]\n   *\n   * See also:\n   *\n   *    sign\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Array | Matrix | Unit} x\n   *            A number or matrix for which to get the absolute value\n   * @return {number | BigNumber | bigint | Fraction | Complex | Array | Matrix | Unit}\n   *            Absolute value of `x`\n   */\n  return typed(name, {\n    number: absNumber,\n    'Complex | BigNumber | Fraction | Unit': x => x.abs(),\n    bigint: x => x < 0n ? -x : x,\n    // deep map collection, skip zeros since abs(0) = 0\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self, true))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,IAAIC,IAAI,GAAG,KAAK;AAChB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,SAAS,GAAG,eAAeL,OAAO,CAACG,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACxE,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBK,MAAM,EAAEN,SAAS;IACjB,uCAAuC,EAAEO,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC;IACrDC,MAAM,EAAEF,CAAC,IAAIA,CAAC,GAAG,EAAE,GAAG,CAACA,CAAC,GAAGA,CAAC;IAC5B;IACA,gBAAgB,EAAEF,KAAK,CAACK,WAAW,CAACC,IAAI,IAAIJ,CAAC,IAAIR,OAAO,CAACQ,CAAC,EAAEI,IAAI,EAAE,IAAI,CAAC;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}