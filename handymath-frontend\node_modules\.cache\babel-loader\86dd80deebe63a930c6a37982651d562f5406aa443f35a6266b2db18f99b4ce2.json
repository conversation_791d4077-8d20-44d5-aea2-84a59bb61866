{"ast": null, "code": "import { acosh, asinh, atanh, cosh, sign, sinh, tanh } from '../../utils/number.js';\nvar n1 = 'number';\nvar n2 = 'number, number';\nexport function acosNumber(x) {\n  return Math.acos(x);\n}\nacosNumber.signature = n1;\nexport function acoshNumber(x) {\n  return acosh(x);\n}\nacoshNumber.signature = n1;\nexport function acotNumber(x) {\n  return Math.atan(1 / x);\n}\nacotNumber.signature = n1;\nexport function acothNumber(x) {\n  return isFinite(x) ? (Math.log((x + 1) / x) + Math.log(x / (x - 1))) / 2 : 0;\n}\nacothNumber.signature = n1;\nexport function acscNumber(x) {\n  return Math.asin(1 / x);\n}\nacscNumber.signature = n1;\nexport function acschNumber(x) {\n  var xInv = 1 / x;\n  return Math.log(xInv + Math.sqrt(xInv * xInv + 1));\n}\nacschNumber.signature = n1;\nexport function asecNumber(x) {\n  return Math.acos(1 / x);\n}\nasecNumber.signature = n1;\nexport function asechNumber(x) {\n  var xInv = 1 / x;\n  var ret = Math.sqrt(xInv * xInv - 1);\n  return Math.log(ret + xInv);\n}\nasechNumber.signature = n1;\nexport function asinNumber(x) {\n  return Math.asin(x);\n}\nasinNumber.signature = n1;\nexport function asinhNumber(x) {\n  return asinh(x);\n}\nasinhNumber.signature = n1;\nexport function atanNumber(x) {\n  return Math.atan(x);\n}\natanNumber.signature = n1;\nexport function atan2Number(y, x) {\n  return Math.atan2(y, x);\n}\natan2Number.signature = n2;\nexport function atanhNumber(x) {\n  return atanh(x);\n}\natanhNumber.signature = n1;\nexport function cosNumber(x) {\n  return Math.cos(x);\n}\ncosNumber.signature = n1;\nexport function coshNumber(x) {\n  return cosh(x);\n}\ncoshNumber.signature = n1;\nexport function cotNumber(x) {\n  return 1 / Math.tan(x);\n}\ncotNumber.signature = n1;\nexport function cothNumber(x) {\n  var e = Math.exp(2 * x);\n  return (e + 1) / (e - 1);\n}\ncothNumber.signature = n1;\nexport function cscNumber(x) {\n  return 1 / Math.sin(x);\n}\ncscNumber.signature = n1;\nexport function cschNumber(x) {\n  // consider values close to zero (+/-)\n  if (x === 0) {\n    return Number.POSITIVE_INFINITY;\n  } else {\n    return Math.abs(2 / (Math.exp(x) - Math.exp(-x))) * sign(x);\n  }\n}\ncschNumber.signature = n1;\nexport function secNumber(x) {\n  return 1 / Math.cos(x);\n}\nsecNumber.signature = n1;\nexport function sechNumber(x) {\n  return 2 / (Math.exp(x) + Math.exp(-x));\n}\nsechNumber.signature = n1;\nexport function sinNumber(x) {\n  return Math.sin(x);\n}\nsinNumber.signature = n1;\nexport function sinhNumber(x) {\n  return sinh(x);\n}\nsinhNumber.signature = n1;\nexport function tanNumber(x) {\n  return Math.tan(x);\n}\ntanNumber.signature = n1;\nexport function tanhNumber(x) {\n  return tanh(x);\n}\ntanhNumber.signature = n1;", "map": {"version": 3, "names": ["acosh", "asinh", "atanh", "cosh", "sign", "sinh", "tanh", "n1", "n2", "acosNumber", "x", "Math", "acos", "signature", "acoshNumber", "acotNumber", "atan", "acothNumber", "isFinite", "log", "acscNumber", "asin", "acschNumber", "xInv", "sqrt", "asecNumber", "asechNumber", "ret", "asinNumber", "asinhNumber", "atanNumber", "atan2Number", "y", "atan2", "atanhNumber", "cosNumber", "cos", "coshNumber", "cotNumber", "tan", "cothNumber", "e", "exp", "cscNumber", "sin", "cschNumber", "Number", "POSITIVE_INFINITY", "abs", "secNumber", "sechNumber", "sinNumber", "sinhNumber", "tanNumber", "tanhNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/trigonometry.js"], "sourcesContent": ["import { acosh, asinh, atanh, cosh, sign, sinh, tanh } from '../../utils/number.js';\nvar n1 = 'number';\nvar n2 = 'number, number';\nexport function acosNumber(x) {\n  return Math.acos(x);\n}\nacosNumber.signature = n1;\nexport function acoshNumber(x) {\n  return acosh(x);\n}\nacoshNumber.signature = n1;\nexport function acotNumber(x) {\n  return Math.atan(1 / x);\n}\nacotNumber.signature = n1;\nexport function acothNumber(x) {\n  return isFinite(x) ? (Math.log((x + 1) / x) + Math.log(x / (x - 1))) / 2 : 0;\n}\nacothNumber.signature = n1;\nexport function acscNumber(x) {\n  return Math.asin(1 / x);\n}\nacscNumber.signature = n1;\nexport function acschNumber(x) {\n  var xInv = 1 / x;\n  return Math.log(xInv + Math.sqrt(xInv * xInv + 1));\n}\nacschNumber.signature = n1;\nexport function asecNumber(x) {\n  return Math.acos(1 / x);\n}\nasecNumber.signature = n1;\nexport function asechNumber(x) {\n  var xInv = 1 / x;\n  var ret = Math.sqrt(xInv * xInv - 1);\n  return Math.log(ret + xInv);\n}\nasechNumber.signature = n1;\nexport function asinNumber(x) {\n  return Math.asin(x);\n}\nasinNumber.signature = n1;\nexport function asinhNumber(x) {\n  return asinh(x);\n}\nasinhNumber.signature = n1;\nexport function atanNumber(x) {\n  return Math.atan(x);\n}\natanNumber.signature = n1;\nexport function atan2Number(y, x) {\n  return Math.atan2(y, x);\n}\natan2Number.signature = n2;\nexport function atanhNumber(x) {\n  return atanh(x);\n}\natanhNumber.signature = n1;\nexport function cosNumber(x) {\n  return Math.cos(x);\n}\ncosNumber.signature = n1;\nexport function coshNumber(x) {\n  return cosh(x);\n}\ncoshNumber.signature = n1;\nexport function cotNumber(x) {\n  return 1 / Math.tan(x);\n}\ncotNumber.signature = n1;\nexport function cothNumber(x) {\n  var e = Math.exp(2 * x);\n  return (e + 1) / (e - 1);\n}\ncothNumber.signature = n1;\nexport function cscNumber(x) {\n  return 1 / Math.sin(x);\n}\ncscNumber.signature = n1;\nexport function cschNumber(x) {\n  // consider values close to zero (+/-)\n  if (x === 0) {\n    return Number.POSITIVE_INFINITY;\n  } else {\n    return Math.abs(2 / (Math.exp(x) - Math.exp(-x))) * sign(x);\n  }\n}\ncschNumber.signature = n1;\nexport function secNumber(x) {\n  return 1 / Math.cos(x);\n}\nsecNumber.signature = n1;\nexport function sechNumber(x) {\n  return 2 / (Math.exp(x) + Math.exp(-x));\n}\nsechNumber.signature = n1;\nexport function sinNumber(x) {\n  return Math.sin(x);\n}\nsinNumber.signature = n1;\nexport function sinhNumber(x) {\n  return sinh(x);\n}\nsinhNumber.signature = n1;\nexport function tanNumber(x) {\n  return Math.tan(x);\n}\ntanNumber.signature = n1;\nexport function tanhNumber(x) {\n  return tanh(x);\n}\ntanhNumber.signature = n1;"], "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,QAAQ,uBAAuB;AACnF,IAAIC,EAAE,GAAG,QAAQ;AACjB,IAAIC,EAAE,GAAG,gBAAgB;AACzB,OAAO,SAASC,UAAUA,CAACC,CAAC,EAAE;EAC5B,OAAOC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC;AACrB;AACAD,UAAU,CAACI,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASO,WAAWA,CAACJ,CAAC,EAAE;EAC7B,OAAOV,KAAK,CAACU,CAAC,CAAC;AACjB;AACAI,WAAW,CAACD,SAAS,GAAGN,EAAE;AAC1B,OAAO,SAASQ,UAAUA,CAACL,CAAC,EAAE;EAC5B,OAAOC,IAAI,CAACK,IAAI,CAAC,CAAC,GAAGN,CAAC,CAAC;AACzB;AACAK,UAAU,CAACF,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASU,WAAWA,CAACP,CAAC,EAAE;EAC7B,OAAOQ,QAAQ,CAACR,CAAC,CAAC,GAAG,CAACC,IAAI,CAACQ,GAAG,CAAC,CAACT,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC,GAAGC,IAAI,CAACQ,GAAG,CAACT,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC9E;AACAO,WAAW,CAACJ,SAAS,GAAGN,EAAE;AAC1B,OAAO,SAASa,UAAUA,CAACV,CAAC,EAAE;EAC5B,OAAOC,IAAI,CAACU,IAAI,CAAC,CAAC,GAAGX,CAAC,CAAC;AACzB;AACAU,UAAU,CAACP,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASe,WAAWA,CAACZ,CAAC,EAAE;EAC7B,IAAIa,IAAI,GAAG,CAAC,GAAGb,CAAC;EAChB,OAAOC,IAAI,CAACQ,GAAG,CAACI,IAAI,GAAGZ,IAAI,CAACa,IAAI,CAACD,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC,CAAC;AACpD;AACAD,WAAW,CAACT,SAAS,GAAGN,EAAE;AAC1B,OAAO,SAASkB,UAAUA,CAACf,CAAC,EAAE;EAC5B,OAAOC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGF,CAAC,CAAC;AACzB;AACAe,UAAU,CAACZ,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASmB,WAAWA,CAAChB,CAAC,EAAE;EAC7B,IAAIa,IAAI,GAAG,CAAC,GAAGb,CAAC;EAChB,IAAIiB,GAAG,GAAGhB,IAAI,CAACa,IAAI,CAACD,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC;EACpC,OAAOZ,IAAI,CAACQ,GAAG,CAACQ,GAAG,GAAGJ,IAAI,CAAC;AAC7B;AACAG,WAAW,CAACb,SAAS,GAAGN,EAAE;AAC1B,OAAO,SAASqB,UAAUA,CAAClB,CAAC,EAAE;EAC5B,OAAOC,IAAI,CAACU,IAAI,CAACX,CAAC,CAAC;AACrB;AACAkB,UAAU,CAACf,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASsB,WAAWA,CAACnB,CAAC,EAAE;EAC7B,OAAOT,KAAK,CAACS,CAAC,CAAC;AACjB;AACAmB,WAAW,CAAChB,SAAS,GAAGN,EAAE;AAC1B,OAAO,SAASuB,UAAUA,CAACpB,CAAC,EAAE;EAC5B,OAAOC,IAAI,CAACK,IAAI,CAACN,CAAC,CAAC;AACrB;AACAoB,UAAU,CAACjB,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASwB,WAAWA,CAACC,CAAC,EAAEtB,CAAC,EAAE;EAChC,OAAOC,IAAI,CAACsB,KAAK,CAACD,CAAC,EAAEtB,CAAC,CAAC;AACzB;AACAqB,WAAW,CAAClB,SAAS,GAAGL,EAAE;AAC1B,OAAO,SAAS0B,WAAWA,CAACxB,CAAC,EAAE;EAC7B,OAAOR,KAAK,CAACQ,CAAC,CAAC;AACjB;AACAwB,WAAW,CAACrB,SAAS,GAAGN,EAAE;AAC1B,OAAO,SAAS4B,SAASA,CAACzB,CAAC,EAAE;EAC3B,OAAOC,IAAI,CAACyB,GAAG,CAAC1B,CAAC,CAAC;AACpB;AACAyB,SAAS,CAACtB,SAAS,GAAGN,EAAE;AACxB,OAAO,SAAS8B,UAAUA,CAAC3B,CAAC,EAAE;EAC5B,OAAOP,IAAI,CAACO,CAAC,CAAC;AAChB;AACA2B,UAAU,CAACxB,SAAS,GAAGN,EAAE;AACzB,OAAO,SAAS+B,SAASA,CAAC5B,CAAC,EAAE;EAC3B,OAAO,CAAC,GAAGC,IAAI,CAAC4B,GAAG,CAAC7B,CAAC,CAAC;AACxB;AACA4B,SAAS,CAACzB,SAAS,GAAGN,EAAE;AACxB,OAAO,SAASiC,UAAUA,CAAC9B,CAAC,EAAE;EAC5B,IAAI+B,CAAC,GAAG9B,IAAI,CAAC+B,GAAG,CAAC,CAAC,GAAGhC,CAAC,CAAC;EACvB,OAAO,CAAC+B,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;AAC1B;AACAD,UAAU,CAAC3B,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASoC,SAASA,CAACjC,CAAC,EAAE;EAC3B,OAAO,CAAC,GAAGC,IAAI,CAACiC,GAAG,CAAClC,CAAC,CAAC;AACxB;AACAiC,SAAS,CAAC9B,SAAS,GAAGN,EAAE;AACxB,OAAO,SAASsC,UAAUA,CAACnC,CAAC,EAAE;EAC5B;EACA,IAAIA,CAAC,KAAK,CAAC,EAAE;IACX,OAAOoC,MAAM,CAACC,iBAAiB;EACjC,CAAC,MAAM;IACL,OAAOpC,IAAI,CAACqC,GAAG,CAAC,CAAC,IAAIrC,IAAI,CAAC+B,GAAG,CAAChC,CAAC,CAAC,GAAGC,IAAI,CAAC+B,GAAG,CAAC,CAAChC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACM,CAAC,CAAC;EAC7D;AACF;AACAmC,UAAU,CAAChC,SAAS,GAAGN,EAAE;AACzB,OAAO,SAAS0C,SAASA,CAACvC,CAAC,EAAE;EAC3B,OAAO,CAAC,GAAGC,IAAI,CAACyB,GAAG,CAAC1B,CAAC,CAAC;AACxB;AACAuC,SAAS,CAACpC,SAAS,GAAGN,EAAE;AACxB,OAAO,SAAS2C,UAAUA,CAACxC,CAAC,EAAE;EAC5B,OAAO,CAAC,IAAIC,IAAI,CAAC+B,GAAG,CAAChC,CAAC,CAAC,GAAGC,IAAI,CAAC+B,GAAG,CAAC,CAAChC,CAAC,CAAC,CAAC;AACzC;AACAwC,UAAU,CAACrC,SAAS,GAAGN,EAAE;AACzB,OAAO,SAAS4C,SAASA,CAACzC,CAAC,EAAE;EAC3B,OAAOC,IAAI,CAACiC,GAAG,CAAClC,CAAC,CAAC;AACpB;AACAyC,SAAS,CAACtC,SAAS,GAAGN,EAAE;AACxB,OAAO,SAAS6C,UAAUA,CAAC1C,CAAC,EAAE;EAC5B,OAAOL,IAAI,CAACK,CAAC,CAAC;AAChB;AACA0C,UAAU,CAACvC,SAAS,GAAGN,EAAE;AACzB,OAAO,SAAS8C,SAASA,CAAC3C,CAAC,EAAE;EAC3B,OAAOC,IAAI,CAAC4B,GAAG,CAAC7B,CAAC,CAAC;AACpB;AACA2C,SAAS,CAACxC,SAAS,GAAGN,EAAE;AACxB,OAAO,SAAS+C,UAAUA,CAAC5C,CAAC,EAAE;EAC5B,OAAOJ,IAAI,CAACI,CAAC,CAAC;AAChB;AACA4C,UAAU,CAACzC,SAAS,GAAGN,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}