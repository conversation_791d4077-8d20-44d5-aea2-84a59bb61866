{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { factory } from '../../../utils/factory.js';\nimport { csFkeep } from './csFkeep.js';\nimport { csFlip } from './csFlip.js';\nimport { csTdfs } from './csTdfs.js';\nvar name = 'csAmd';\nvar dependencies = ['add', 'multiply', 'transpose'];\nexport var createCsAmd = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    add,\n    multiply,\n    transpose\n  } = _ref;\n  /**\n   * Approximate minimum degree ordering. The minimum degree algorithm is a widely used\n   * heuristic for finding a permutation P so that P*A*P' has fewer nonzeros in its factorization\n   * than A. It is a gready method that selects the sparsest pivot row and column during the course\n   * of a right looking sparse Cholesky factorization.\n   *\n   * @param {Number} order    0: Natural, 1: <PERSON>lesky, 2: LU, 3: QR\n   * @param {Matrix} m        Sparse Matrix\n   */\n  return function csAmd(order, a) {\n    // check input parameters\n    if (!a || order <= 0 || order > 3) {\n      return null;\n    }\n    // a matrix arrays\n    var asize = a._size;\n    // rows and columns\n    var m = asize[0];\n    var n = asize[1];\n    // initialize vars\n    var lemax = 0;\n    // dense threshold\n    var dense = Math.max(16, 10 * Math.sqrt(n));\n    dense = Math.min(n - 2, dense);\n    // create target matrix C\n    var cm = _createTargetMatrix(order, a, m, n, dense);\n    // drop diagonal entries\n    csFkeep(cm, _diag, null);\n    // C matrix arrays\n    var cindex = cm._index;\n    var cptr = cm._ptr;\n\n    // number of nonzero elements in C\n    var cnz = cptr[n];\n\n    // allocate result (n+1)\n    var P = [];\n\n    // create workspace (8 * (n + 1))\n    var W = [];\n    var len = 0; // first n + 1 entries\n    var nv = n + 1; // next n + 1 entries\n    var next = 2 * (n + 1); // next n + 1 entries\n    var head = 3 * (n + 1); // next n + 1 entries\n    var elen = 4 * (n + 1); // next n + 1 entries\n    var degree = 5 * (n + 1); // next n + 1 entries\n    var w = 6 * (n + 1); // next n + 1 entries\n    var hhead = 7 * (n + 1); // last n + 1 entries\n\n    // use P as workspace for last\n    var last = P;\n\n    // initialize quotient graph\n    var mark = _initializeQuotientGraph(n, cptr, W, len, head, last, next, hhead, nv, w, elen, degree);\n\n    // initialize degree lists\n    var nel = _initializeDegreeLists(n, cptr, W, degree, elen, w, dense, nv, head, last, next);\n\n    // minimum degree node\n    var mindeg = 0;\n\n    // vars\n    var i, j, k, k1, k2, e, pj, ln, nvi, pk, eln, p1, p2, pn, h, d;\n\n    // while (selecting pivots) do\n    while (nel < n) {\n      // select node of minimum approximate degree. amd() is now ready to start eliminating the graph. It first\n      // finds a node k of minimum degree and removes it from its degree list. The variable nel keeps track of thow\n      // many nodes have been eliminated.\n      for (k = -1; mindeg < n && (k = W[head + mindeg]) === -1; mindeg++);\n      if (W[next + k] !== -1) {\n        last[W[next + k]] = -1;\n      }\n      // remove k from degree list\n      W[head + mindeg] = W[next + k];\n      // elenk = |Ek|\n      var elenk = W[elen + k];\n      // # of nodes k represents\n      var nvk = W[nv + k];\n      // W[nv + k] nodes of A eliminated\n      nel += nvk;\n\n      // Construct a new element. The new element Lk is constructed in place if |Ek| = 0. nv[i] is\n      // negated for all nodes i in Lk to flag them as members of this set. Each node i is removed from the\n      // degree lists. All elements e in Ek are absorved into element k.\n      var dk = 0;\n      // flag k as in Lk\n      W[nv + k] = -nvk;\n      var p = cptr[k];\n      // do in place if W[elen + k] === 0\n      var pk1 = elenk === 0 ? p : cnz;\n      var pk2 = pk1;\n      for (k1 = 1; k1 <= elenk + 1; k1++) {\n        if (k1 > elenk) {\n          // search the nodes in k\n          e = k;\n          // list of nodes starts at cindex[pj]\n          pj = p;\n          // length of list of nodes in k\n          ln = W[len + k] - elenk;\n        } else {\n          // search the nodes in e\n          e = cindex[p++];\n          pj = cptr[e];\n          // length of list of nodes in e\n          ln = W[len + e];\n        }\n        for (k2 = 1; k2 <= ln; k2++) {\n          i = cindex[pj++];\n          // check  node i dead, or seen\n          if ((nvi = W[nv + i]) <= 0) {\n            continue;\n          }\n          // W[degree + Lk] += size of node i\n          dk += nvi;\n          // negate W[nv + i] to denote i in Lk\n          W[nv + i] = -nvi;\n          // place i in Lk\n          cindex[pk2++] = i;\n          if (W[next + i] !== -1) {\n            last[W[next + i]] = last[i];\n          }\n          // check we need to remove i from degree list\n          if (last[i] !== -1) {\n            W[next + last[i]] = W[next + i];\n          } else {\n            W[head + W[degree + i]] = W[next + i];\n          }\n        }\n        if (e !== k) {\n          // absorb e into k\n          cptr[e] = csFlip(k);\n          // e is now a dead element\n          W[w + e] = 0;\n        }\n      }\n      // cindex[cnz...nzmax] is free\n      if (elenk !== 0) {\n        cnz = pk2;\n      }\n      // external degree of k - |Lk\\i|\n      W[degree + k] = dk;\n      // element k is in cindex[pk1..pk2-1]\n      cptr[k] = pk1;\n      W[len + k] = pk2 - pk1;\n      // k is now an element\n      W[elen + k] = -2;\n\n      // Find set differences. The scan1 function now computes the set differences |Le \\ Lk| for all elements e. At the start of the\n      // scan, no entry in the w array is greater than or equal to mark.\n\n      // clear w if necessary\n      mark = _wclear(mark, lemax, W, w, n);\n      // scan 1: find |Le\\Lk|\n      for (pk = pk1; pk < pk2; pk++) {\n        i = cindex[pk];\n        // check if W[elen + i] empty, skip it\n        if ((eln = W[elen + i]) <= 0) {\n          continue;\n        }\n        // W[nv + i] was negated\n        nvi = -W[nv + i];\n        var wnvi = mark - nvi;\n        // scan Ei\n        for (p = cptr[i], p1 = cptr[i] + eln - 1; p <= p1; p++) {\n          e = cindex[p];\n          if (W[w + e] >= mark) {\n            // decrement |Le\\Lk|\n            W[w + e] -= nvi;\n          } else if (W[w + e] !== 0) {\n            // ensure e is a live element, 1st time e seen in scan 1\n            W[w + e] = W[degree + e] + wnvi;\n          }\n        }\n      }\n\n      // degree update\n      // The second pass computes the approximate degree di, prunes the sets Ei and Ai, and computes a hash\n      // function h(i) for all nodes in Lk.\n\n      // scan2: degree update\n      for (pk = pk1; pk < pk2; pk++) {\n        // consider node i in Lk\n        i = cindex[pk];\n        p1 = cptr[i];\n        p2 = p1 + W[elen + i] - 1;\n        pn = p1;\n        // scan Ei\n        for (h = 0, d = 0, p = p1; p <= p2; p++) {\n          e = cindex[p];\n          // check e is an unabsorbed element\n          if (W[w + e] !== 0) {\n            // dext = |Le\\Lk|\n            var dext = W[w + e] - mark;\n            if (dext > 0) {\n              // sum up the set differences\n              d += dext;\n              // keep e in Ei\n              cindex[pn++] = e;\n              // compute the hash of node i\n              h += e;\n            } else {\n              // aggressive absorb. e->k\n              cptr[e] = csFlip(k);\n              // e is a dead element\n              W[w + e] = 0;\n            }\n          }\n        }\n        // W[elen + i] = |Ei|\n        W[elen + i] = pn - p1 + 1;\n        var p3 = pn;\n        var p4 = p1 + W[len + i];\n        // prune edges in Ai\n        for (p = p2 + 1; p < p4; p++) {\n          j = cindex[p];\n          // check node j dead or in Lk\n          var nvj = W[nv + j];\n          if (nvj <= 0) {\n            continue;\n          }\n          // degree(i) += |j|\n          d += nvj;\n          // place j in node list of i\n          cindex[pn++] = j;\n          // compute hash for node i\n          h += j;\n        }\n        // check for mass elimination\n        if (d === 0) {\n          // absorb i into k\n          cptr[i] = csFlip(k);\n          nvi = -W[nv + i];\n          // |Lk| -= |i|\n          dk -= nvi;\n          // |k| += W[nv + i]\n          nvk += nvi;\n          nel += nvi;\n          W[nv + i] = 0;\n          // node i is dead\n          W[elen + i] = -1;\n        } else {\n          // update degree(i)\n          W[degree + i] = Math.min(W[degree + i], d);\n          // move first node to end\n          cindex[pn] = cindex[p3];\n          // move 1st el. to end of Ei\n          cindex[p3] = cindex[p1];\n          // add k as 1st element in of Ei\n          cindex[p1] = k;\n          // new len of adj. list of node i\n          W[len + i] = pn - p1 + 1;\n          // finalize hash of i\n          h = (h < 0 ? -h : h) % n;\n          // place i in hash bucket\n          W[next + i] = W[hhead + h];\n          W[hhead + h] = i;\n          // save hash of i in last[i]\n          last[i] = h;\n        }\n      }\n      // finalize |Lk|\n      W[degree + k] = dk;\n      lemax = Math.max(lemax, dk);\n      // clear w\n      mark = _wclear(mark + lemax, lemax, W, w, n);\n\n      // Supernode detection. Supernode detection relies on the hash function h(i) computed for each node i.\n      // If two nodes have identical adjacency lists, their hash functions wil be identical.\n      for (pk = pk1; pk < pk2; pk++) {\n        i = cindex[pk];\n        // check i is dead, skip it\n        if (W[nv + i] >= 0) {\n          continue;\n        }\n        // scan hash bucket of node i\n        h = last[i];\n        i = W[hhead + h];\n        // hash bucket will be empty\n        W[hhead + h] = -1;\n        for (; i !== -1 && W[next + i] !== -1; i = W[next + i], mark++) {\n          ln = W[len + i];\n          eln = W[elen + i];\n          for (p = cptr[i] + 1; p <= cptr[i] + ln - 1; p++) {\n            W[w + cindex[p]] = mark;\n          }\n          var jlast = i;\n          // compare i with all j\n          for (j = W[next + i]; j !== -1;) {\n            var ok = W[len + j] === ln && W[elen + j] === eln;\n            for (p = cptr[j] + 1; ok && p <= cptr[j] + ln - 1; p++) {\n              // compare i and j\n              if (W[w + cindex[p]] !== mark) {\n                ok = 0;\n              }\n            }\n            // check i and j are identical\n            if (ok) {\n              // absorb j into i\n              cptr[j] = csFlip(i);\n              W[nv + i] += W[nv + j];\n              W[nv + j] = 0;\n              // node j is dead\n              W[elen + j] = -1;\n              // delete j from hash bucket\n              j = W[next + j];\n              W[next + jlast] = j;\n            } else {\n              // j and i are different\n              jlast = j;\n              j = W[next + j];\n            }\n          }\n        }\n      }\n\n      // Finalize new element. The elimination of node k is nearly complete. All nodes i in Lk are scanned one last time.\n      // Node i is removed from Lk if it is dead. The flagged status of nv[i] is cleared.\n      for (p = pk1, pk = pk1; pk < pk2; pk++) {\n        i = cindex[pk];\n        // check  i is dead, skip it\n        if ((nvi = -W[nv + i]) <= 0) {\n          continue;\n        }\n        // restore W[nv + i]\n        W[nv + i] = nvi;\n        // compute external degree(i)\n        d = W[degree + i] + dk - nvi;\n        d = Math.min(d, n - nel - nvi);\n        if (W[head + d] !== -1) {\n          last[W[head + d]] = i;\n        }\n        // put i back in degree list\n        W[next + i] = W[head + d];\n        last[i] = -1;\n        W[head + d] = i;\n        // find new minimum degree\n        mindeg = Math.min(mindeg, d);\n        W[degree + i] = d;\n        // place i in Lk\n        cindex[p++] = i;\n      }\n      // # nodes absorbed into k\n      W[nv + k] = nvk;\n      // length of adj list of element k\n      if ((W[len + k] = p - pk1) === 0) {\n        // k is a root of the tree\n        cptr[k] = -1;\n        // k is now a dead element\n        W[w + k] = 0;\n      }\n      if (elenk !== 0) {\n        // free unused space in Lk\n        cnz = p;\n      }\n    }\n\n    // Postordering. The elimination is complete, but no permutation has been computed. All that is left\n    // of the graph is the assembly tree (ptr) and a set of dead nodes and elements (i is a dead node if\n    // nv[i] is zero and a dead element if nv[i] > 0). It is from this information only that the final permutation\n    // is computed. The tree is restored by unflipping all of ptr.\n\n    // fix assembly tree\n    for (i = 0; i < n; i++) {\n      cptr[i] = csFlip(cptr[i]);\n    }\n    for (j = 0; j <= n; j++) {\n      W[head + j] = -1;\n    }\n    // place unordered nodes in lists\n    for (j = n; j >= 0; j--) {\n      // skip if j is an element\n      if (W[nv + j] > 0) {\n        continue;\n      }\n      // place j in list of its parent\n      W[next + j] = W[head + cptr[j]];\n      W[head + cptr[j]] = j;\n    }\n    // place elements in lists\n    for (e = n; e >= 0; e--) {\n      // skip unless e is an element\n      if (W[nv + e] <= 0) {\n        continue;\n      }\n      if (cptr[e] !== -1) {\n        // place e in list of its parent\n        W[next + e] = W[head + cptr[e]];\n        W[head + cptr[e]] = e;\n      }\n    }\n    // postorder the assembly tree\n    for (k = 0, i = 0; i <= n; i++) {\n      if (cptr[i] === -1) {\n        k = csTdfs(i, k, W, head, next, P, w);\n      }\n    }\n    // remove last item in array\n    P.splice(P.length - 1, 1);\n    // return P\n    return P;\n  };\n\n  /**\n   * Creates the matrix that will be used by the approximate minimum degree ordering algorithm. The function accepts the matrix M as input and returns a permutation\n   * vector P. The amd algorithm operates on a symmetrix matrix, so one of three symmetric matrices is formed.\n   *\n   * Order: 0\n   *   A natural ordering P=null matrix is returned.\n   *\n   * Order: 1\n   *   Matrix must be square. This is appropriate for a Cholesky or LU factorization.\n   *   P = M + M'\n   *\n   * Order: 2\n   *   Dense columns from M' are dropped, M recreated from M'. This is appropriatefor LU factorization of unsymmetric matrices.\n   *   P = M' * M\n   *\n   * Order: 3\n   *   This is best used for QR factorization or LU factorization is matrix M has no dense rows. A dense row is a row with more than 10*sqr(columns) entries.\n   *   P = M' * M\n   */\n  function _createTargetMatrix(order, a, m, n, dense) {\n    // compute A'\n    var at = transpose(a);\n\n    // check order = 1, matrix must be square\n    if (order === 1 && n === m) {\n      // C = A + A'\n      return add(a, at);\n    }\n\n    // check order = 2, drop dense columns from M'\n    if (order === 2) {\n      // transpose arrays\n      var tindex = at._index;\n      var tptr = at._ptr;\n      // new column index\n      var p2 = 0;\n      // loop A' columns (rows)\n      for (var j = 0; j < m; j++) {\n        // column j of AT starts here\n        var p = tptr[j];\n        // new column j starts here\n        tptr[j] = p2;\n        // skip dense col j\n        if (tptr[j + 1] - p > dense) {\n          continue;\n        }\n        // map rows in column j of A\n        for (var p1 = tptr[j + 1]; p < p1; p++) {\n          tindex[p2++] = tindex[p];\n        }\n      }\n      // finalize AT\n      tptr[m] = p2;\n      // recreate A from new transpose matrix\n      a = transpose(at);\n      // use A' * A\n      return multiply(at, a);\n    }\n\n    // use A' * A, square or rectangular matrix\n    return multiply(at, a);\n  }\n\n  /**\n   * Initialize quotient graph. There are four kind of nodes and elements that must be represented:\n   *\n   *  - A live node is a node i (or a supernode) that has not been selected as a pivot nad has not been merged into another supernode.\n   *  - A dead node i is one that has been removed from the graph, having been absorved into r = flip(ptr[i]).\n   *  - A live element e is one that is in the graph, having been formed when node e was selected as the pivot.\n   *  - A dead element e is one that has benn absorved into a subsequent element s = flip(ptr[e]).\n   */\n  function _initializeQuotientGraph(n, cptr, W, len, head, last, next, hhead, nv, w, elen, degree) {\n    // Initialize quotient graph\n    for (var k = 0; k < n; k++) {\n      W[len + k] = cptr[k + 1] - cptr[k];\n    }\n    W[len + n] = 0;\n    // initialize workspace\n    for (var i = 0; i <= n; i++) {\n      // degree list i is empty\n      W[head + i] = -1;\n      last[i] = -1;\n      W[next + i] = -1;\n      // hash list i is empty\n      W[hhead + i] = -1;\n      // node i is just one node\n      W[nv + i] = 1;\n      // node i is alive\n      W[w + i] = 1;\n      // Ek of node i is empty\n      W[elen + i] = 0;\n      // degree of node i\n      W[degree + i] = W[len + i];\n    }\n    // clear w\n    var mark = _wclear(0, 0, W, w, n);\n    // n is a dead element\n    W[elen + n] = -2;\n    // n is a root of assembly tree\n    cptr[n] = -1;\n    // n is a dead element\n    W[w + n] = 0;\n    // return mark\n    return mark;\n  }\n\n  /**\n   * Initialize degree lists. Each node is placed in its degree lists. Nodes of zero degree are eliminated immediately. Nodes with\n   * degree >= dense are alsol eliminated and merged into a placeholder node n, a dead element. Thes nodes will appera last in the\n   * output permutation p.\n   */\n  function _initializeDegreeLists(n, cptr, W, degree, elen, w, dense, nv, head, last, next) {\n    // result\n    var nel = 0;\n    // loop columns\n    for (var i = 0; i < n; i++) {\n      // degree @ i\n      var d = W[degree + i];\n      // check node i is empty\n      if (d === 0) {\n        // element i is dead\n        W[elen + i] = -2;\n        nel++;\n        // i is a root of assembly tree\n        cptr[i] = -1;\n        W[w + i] = 0;\n      } else if (d > dense) {\n        // absorb i into element n\n        W[nv + i] = 0;\n        // node i is dead\n        W[elen + i] = -1;\n        nel++;\n        cptr[i] = csFlip(n);\n        W[nv + n]++;\n      } else {\n        var h = W[head + d];\n        if (h !== -1) {\n          last[h] = i;\n        }\n        // put node i in degree list d\n        W[next + i] = W[head + d];\n        W[head + d] = i;\n      }\n    }\n    return nel;\n  }\n  function _wclear(mark, lemax, W, w, n) {\n    if (mark < 2 || mark + lemax < 0) {\n      for (var k = 0; k < n; k++) {\n        if (W[w + k] !== 0) {\n          W[w + k] = 1;\n        }\n      }\n      mark = 2;\n    }\n    // at this point, W [0..n-1] < mark holds\n    return mark;\n  }\n  function _diag(i, j) {\n    return i !== j;\n  }\n});", "map": {"version": 3, "names": ["factory", "csFkeep", "csFlip", "csTdfs", "name", "dependencies", "createCsAmd", "_ref", "add", "multiply", "transpose", "csAmd", "order", "a", "asize", "_size", "m", "n", "lemax", "dense", "Math", "max", "sqrt", "min", "cm", "_createTargetMatrix", "_diag", "cindex", "_index", "cptr", "_ptr", "cnz", "P", "W", "len", "nv", "next", "head", "elen", "degree", "w", "hhead", "last", "mark", "_initializeQuotientGraph", "nel", "_initializeDegreeLists", "mindeg", "i", "j", "k", "k1", "k2", "e", "pj", "ln", "nvi", "pk", "eln", "p1", "p2", "pn", "h", "d", "elenk", "nvk", "dk", "p", "pk1", "pk2", "_wclear", "wnvi", "dext", "p3", "p4", "nvj", "j<PERSON>", "ok", "splice", "length", "at", "tindex", "tptr"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csAmd.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { factory } from '../../../utils/factory.js';\nimport { csFkeep } from './csFkeep.js';\nimport { csFlip } from './csFlip.js';\nimport { csTdfs } from './csTdfs.js';\nvar name = 'csAmd';\nvar dependencies = ['add', 'multiply', 'transpose'];\nexport var createCsAmd = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    add,\n    multiply,\n    transpose\n  } = _ref;\n  /**\n   * Approximate minimum degree ordering. The minimum degree algorithm is a widely used\n   * heuristic for finding a permutation P so that P*A*P' has fewer nonzeros in its factorization\n   * than A. It is a gready method that selects the sparsest pivot row and column during the course\n   * of a right looking sparse Cholesky factorization.\n   *\n   * @param {Number} order    0: Natural, 1: <PERSON>lesky, 2: LU, 3: QR\n   * @param {Matrix} m        Sparse Matrix\n   */\n  return function csAmd(order, a) {\n    // check input parameters\n    if (!a || order <= 0 || order > 3) {\n      return null;\n    }\n    // a matrix arrays\n    var asize = a._size;\n    // rows and columns\n    var m = asize[0];\n    var n = asize[1];\n    // initialize vars\n    var lemax = 0;\n    // dense threshold\n    var dense = Math.max(16, 10 * Math.sqrt(n));\n    dense = Math.min(n - 2, dense);\n    // create target matrix C\n    var cm = _createTargetMatrix(order, a, m, n, dense);\n    // drop diagonal entries\n    csFkeep(cm, _diag, null);\n    // C matrix arrays\n    var cindex = cm._index;\n    var cptr = cm._ptr;\n\n    // number of nonzero elements in C\n    var cnz = cptr[n];\n\n    // allocate result (n+1)\n    var P = [];\n\n    // create workspace (8 * (n + 1))\n    var W = [];\n    var len = 0; // first n + 1 entries\n    var nv = n + 1; // next n + 1 entries\n    var next = 2 * (n + 1); // next n + 1 entries\n    var head = 3 * (n + 1); // next n + 1 entries\n    var elen = 4 * (n + 1); // next n + 1 entries\n    var degree = 5 * (n + 1); // next n + 1 entries\n    var w = 6 * (n + 1); // next n + 1 entries\n    var hhead = 7 * (n + 1); // last n + 1 entries\n\n    // use P as workspace for last\n    var last = P;\n\n    // initialize quotient graph\n    var mark = _initializeQuotientGraph(n, cptr, W, len, head, last, next, hhead, nv, w, elen, degree);\n\n    // initialize degree lists\n    var nel = _initializeDegreeLists(n, cptr, W, degree, elen, w, dense, nv, head, last, next);\n\n    // minimum degree node\n    var mindeg = 0;\n\n    // vars\n    var i, j, k, k1, k2, e, pj, ln, nvi, pk, eln, p1, p2, pn, h, d;\n\n    // while (selecting pivots) do\n    while (nel < n) {\n      // select node of minimum approximate degree. amd() is now ready to start eliminating the graph. It first\n      // finds a node k of minimum degree and removes it from its degree list. The variable nel keeps track of thow\n      // many nodes have been eliminated.\n      for (k = -1; mindeg < n && (k = W[head + mindeg]) === -1; mindeg++);\n      if (W[next + k] !== -1) {\n        last[W[next + k]] = -1;\n      }\n      // remove k from degree list\n      W[head + mindeg] = W[next + k];\n      // elenk = |Ek|\n      var elenk = W[elen + k];\n      // # of nodes k represents\n      var nvk = W[nv + k];\n      // W[nv + k] nodes of A eliminated\n      nel += nvk;\n\n      // Construct a new element. The new element Lk is constructed in place if |Ek| = 0. nv[i] is\n      // negated for all nodes i in Lk to flag them as members of this set. Each node i is removed from the\n      // degree lists. All elements e in Ek are absorved into element k.\n      var dk = 0;\n      // flag k as in Lk\n      W[nv + k] = -nvk;\n      var p = cptr[k];\n      // do in place if W[elen + k] === 0\n      var pk1 = elenk === 0 ? p : cnz;\n      var pk2 = pk1;\n      for (k1 = 1; k1 <= elenk + 1; k1++) {\n        if (k1 > elenk) {\n          // search the nodes in k\n          e = k;\n          // list of nodes starts at cindex[pj]\n          pj = p;\n          // length of list of nodes in k\n          ln = W[len + k] - elenk;\n        } else {\n          // search the nodes in e\n          e = cindex[p++];\n          pj = cptr[e];\n          // length of list of nodes in e\n          ln = W[len + e];\n        }\n        for (k2 = 1; k2 <= ln; k2++) {\n          i = cindex[pj++];\n          // check  node i dead, or seen\n          if ((nvi = W[nv + i]) <= 0) {\n            continue;\n          }\n          // W[degree + Lk] += size of node i\n          dk += nvi;\n          // negate W[nv + i] to denote i in Lk\n          W[nv + i] = -nvi;\n          // place i in Lk\n          cindex[pk2++] = i;\n          if (W[next + i] !== -1) {\n            last[W[next + i]] = last[i];\n          }\n          // check we need to remove i from degree list\n          if (last[i] !== -1) {\n            W[next + last[i]] = W[next + i];\n          } else {\n            W[head + W[degree + i]] = W[next + i];\n          }\n        }\n        if (e !== k) {\n          // absorb e into k\n          cptr[e] = csFlip(k);\n          // e is now a dead element\n          W[w + e] = 0;\n        }\n      }\n      // cindex[cnz...nzmax] is free\n      if (elenk !== 0) {\n        cnz = pk2;\n      }\n      // external degree of k - |Lk\\i|\n      W[degree + k] = dk;\n      // element k is in cindex[pk1..pk2-1]\n      cptr[k] = pk1;\n      W[len + k] = pk2 - pk1;\n      // k is now an element\n      W[elen + k] = -2;\n\n      // Find set differences. The scan1 function now computes the set differences |Le \\ Lk| for all elements e. At the start of the\n      // scan, no entry in the w array is greater than or equal to mark.\n\n      // clear w if necessary\n      mark = _wclear(mark, lemax, W, w, n);\n      // scan 1: find |Le\\Lk|\n      for (pk = pk1; pk < pk2; pk++) {\n        i = cindex[pk];\n        // check if W[elen + i] empty, skip it\n        if ((eln = W[elen + i]) <= 0) {\n          continue;\n        }\n        // W[nv + i] was negated\n        nvi = -W[nv + i];\n        var wnvi = mark - nvi;\n        // scan Ei\n        for (p = cptr[i], p1 = cptr[i] + eln - 1; p <= p1; p++) {\n          e = cindex[p];\n          if (W[w + e] >= mark) {\n            // decrement |Le\\Lk|\n            W[w + e] -= nvi;\n          } else if (W[w + e] !== 0) {\n            // ensure e is a live element, 1st time e seen in scan 1\n            W[w + e] = W[degree + e] + wnvi;\n          }\n        }\n      }\n\n      // degree update\n      // The second pass computes the approximate degree di, prunes the sets Ei and Ai, and computes a hash\n      // function h(i) for all nodes in Lk.\n\n      // scan2: degree update\n      for (pk = pk1; pk < pk2; pk++) {\n        // consider node i in Lk\n        i = cindex[pk];\n        p1 = cptr[i];\n        p2 = p1 + W[elen + i] - 1;\n        pn = p1;\n        // scan Ei\n        for (h = 0, d = 0, p = p1; p <= p2; p++) {\n          e = cindex[p];\n          // check e is an unabsorbed element\n          if (W[w + e] !== 0) {\n            // dext = |Le\\Lk|\n            var dext = W[w + e] - mark;\n            if (dext > 0) {\n              // sum up the set differences\n              d += dext;\n              // keep e in Ei\n              cindex[pn++] = e;\n              // compute the hash of node i\n              h += e;\n            } else {\n              // aggressive absorb. e->k\n              cptr[e] = csFlip(k);\n              // e is a dead element\n              W[w + e] = 0;\n            }\n          }\n        }\n        // W[elen + i] = |Ei|\n        W[elen + i] = pn - p1 + 1;\n        var p3 = pn;\n        var p4 = p1 + W[len + i];\n        // prune edges in Ai\n        for (p = p2 + 1; p < p4; p++) {\n          j = cindex[p];\n          // check node j dead or in Lk\n          var nvj = W[nv + j];\n          if (nvj <= 0) {\n            continue;\n          }\n          // degree(i) += |j|\n          d += nvj;\n          // place j in node list of i\n          cindex[pn++] = j;\n          // compute hash for node i\n          h += j;\n        }\n        // check for mass elimination\n        if (d === 0) {\n          // absorb i into k\n          cptr[i] = csFlip(k);\n          nvi = -W[nv + i];\n          // |Lk| -= |i|\n          dk -= nvi;\n          // |k| += W[nv + i]\n          nvk += nvi;\n          nel += nvi;\n          W[nv + i] = 0;\n          // node i is dead\n          W[elen + i] = -1;\n        } else {\n          // update degree(i)\n          W[degree + i] = Math.min(W[degree + i], d);\n          // move first node to end\n          cindex[pn] = cindex[p3];\n          // move 1st el. to end of Ei\n          cindex[p3] = cindex[p1];\n          // add k as 1st element in of Ei\n          cindex[p1] = k;\n          // new len of adj. list of node i\n          W[len + i] = pn - p1 + 1;\n          // finalize hash of i\n          h = (h < 0 ? -h : h) % n;\n          // place i in hash bucket\n          W[next + i] = W[hhead + h];\n          W[hhead + h] = i;\n          // save hash of i in last[i]\n          last[i] = h;\n        }\n      }\n      // finalize |Lk|\n      W[degree + k] = dk;\n      lemax = Math.max(lemax, dk);\n      // clear w\n      mark = _wclear(mark + lemax, lemax, W, w, n);\n\n      // Supernode detection. Supernode detection relies on the hash function h(i) computed for each node i.\n      // If two nodes have identical adjacency lists, their hash functions wil be identical.\n      for (pk = pk1; pk < pk2; pk++) {\n        i = cindex[pk];\n        // check i is dead, skip it\n        if (W[nv + i] >= 0) {\n          continue;\n        }\n        // scan hash bucket of node i\n        h = last[i];\n        i = W[hhead + h];\n        // hash bucket will be empty\n        W[hhead + h] = -1;\n        for (; i !== -1 && W[next + i] !== -1; i = W[next + i], mark++) {\n          ln = W[len + i];\n          eln = W[elen + i];\n          for (p = cptr[i] + 1; p <= cptr[i] + ln - 1; p++) {\n            W[w + cindex[p]] = mark;\n          }\n          var jlast = i;\n          // compare i with all j\n          for (j = W[next + i]; j !== -1;) {\n            var ok = W[len + j] === ln && W[elen + j] === eln;\n            for (p = cptr[j] + 1; ok && p <= cptr[j] + ln - 1; p++) {\n              // compare i and j\n              if (W[w + cindex[p]] !== mark) {\n                ok = 0;\n              }\n            }\n            // check i and j are identical\n            if (ok) {\n              // absorb j into i\n              cptr[j] = csFlip(i);\n              W[nv + i] += W[nv + j];\n              W[nv + j] = 0;\n              // node j is dead\n              W[elen + j] = -1;\n              // delete j from hash bucket\n              j = W[next + j];\n              W[next + jlast] = j;\n            } else {\n              // j and i are different\n              jlast = j;\n              j = W[next + j];\n            }\n          }\n        }\n      }\n\n      // Finalize new element. The elimination of node k is nearly complete. All nodes i in Lk are scanned one last time.\n      // Node i is removed from Lk if it is dead. The flagged status of nv[i] is cleared.\n      for (p = pk1, pk = pk1; pk < pk2; pk++) {\n        i = cindex[pk];\n        // check  i is dead, skip it\n        if ((nvi = -W[nv + i]) <= 0) {\n          continue;\n        }\n        // restore W[nv + i]\n        W[nv + i] = nvi;\n        // compute external degree(i)\n        d = W[degree + i] + dk - nvi;\n        d = Math.min(d, n - nel - nvi);\n        if (W[head + d] !== -1) {\n          last[W[head + d]] = i;\n        }\n        // put i back in degree list\n        W[next + i] = W[head + d];\n        last[i] = -1;\n        W[head + d] = i;\n        // find new minimum degree\n        mindeg = Math.min(mindeg, d);\n        W[degree + i] = d;\n        // place i in Lk\n        cindex[p++] = i;\n      }\n      // # nodes absorbed into k\n      W[nv + k] = nvk;\n      // length of adj list of element k\n      if ((W[len + k] = p - pk1) === 0) {\n        // k is a root of the tree\n        cptr[k] = -1;\n        // k is now a dead element\n        W[w + k] = 0;\n      }\n      if (elenk !== 0) {\n        // free unused space in Lk\n        cnz = p;\n      }\n    }\n\n    // Postordering. The elimination is complete, but no permutation has been computed. All that is left\n    // of the graph is the assembly tree (ptr) and a set of dead nodes and elements (i is a dead node if\n    // nv[i] is zero and a dead element if nv[i] > 0). It is from this information only that the final permutation\n    // is computed. The tree is restored by unflipping all of ptr.\n\n    // fix assembly tree\n    for (i = 0; i < n; i++) {\n      cptr[i] = csFlip(cptr[i]);\n    }\n    for (j = 0; j <= n; j++) {\n      W[head + j] = -1;\n    }\n    // place unordered nodes in lists\n    for (j = n; j >= 0; j--) {\n      // skip if j is an element\n      if (W[nv + j] > 0) {\n        continue;\n      }\n      // place j in list of its parent\n      W[next + j] = W[head + cptr[j]];\n      W[head + cptr[j]] = j;\n    }\n    // place elements in lists\n    for (e = n; e >= 0; e--) {\n      // skip unless e is an element\n      if (W[nv + e] <= 0) {\n        continue;\n      }\n      if (cptr[e] !== -1) {\n        // place e in list of its parent\n        W[next + e] = W[head + cptr[e]];\n        W[head + cptr[e]] = e;\n      }\n    }\n    // postorder the assembly tree\n    for (k = 0, i = 0; i <= n; i++) {\n      if (cptr[i] === -1) {\n        k = csTdfs(i, k, W, head, next, P, w);\n      }\n    }\n    // remove last item in array\n    P.splice(P.length - 1, 1);\n    // return P\n    return P;\n  };\n\n  /**\n   * Creates the matrix that will be used by the approximate minimum degree ordering algorithm. The function accepts the matrix M as input and returns a permutation\n   * vector P. The amd algorithm operates on a symmetrix matrix, so one of three symmetric matrices is formed.\n   *\n   * Order: 0\n   *   A natural ordering P=null matrix is returned.\n   *\n   * Order: 1\n   *   Matrix must be square. This is appropriate for a Cholesky or LU factorization.\n   *   P = M + M'\n   *\n   * Order: 2\n   *   Dense columns from M' are dropped, M recreated from M'. This is appropriatefor LU factorization of unsymmetric matrices.\n   *   P = M' * M\n   *\n   * Order: 3\n   *   This is best used for QR factorization or LU factorization is matrix M has no dense rows. A dense row is a row with more than 10*sqr(columns) entries.\n   *   P = M' * M\n   */\n  function _createTargetMatrix(order, a, m, n, dense) {\n    // compute A'\n    var at = transpose(a);\n\n    // check order = 1, matrix must be square\n    if (order === 1 && n === m) {\n      // C = A + A'\n      return add(a, at);\n    }\n\n    // check order = 2, drop dense columns from M'\n    if (order === 2) {\n      // transpose arrays\n      var tindex = at._index;\n      var tptr = at._ptr;\n      // new column index\n      var p2 = 0;\n      // loop A' columns (rows)\n      for (var j = 0; j < m; j++) {\n        // column j of AT starts here\n        var p = tptr[j];\n        // new column j starts here\n        tptr[j] = p2;\n        // skip dense col j\n        if (tptr[j + 1] - p > dense) {\n          continue;\n        }\n        // map rows in column j of A\n        for (var p1 = tptr[j + 1]; p < p1; p++) {\n          tindex[p2++] = tindex[p];\n        }\n      }\n      // finalize AT\n      tptr[m] = p2;\n      // recreate A from new transpose matrix\n      a = transpose(at);\n      // use A' * A\n      return multiply(at, a);\n    }\n\n    // use A' * A, square or rectangular matrix\n    return multiply(at, a);\n  }\n\n  /**\n   * Initialize quotient graph. There are four kind of nodes and elements that must be represented:\n   *\n   *  - A live node is a node i (or a supernode) that has not been selected as a pivot nad has not been merged into another supernode.\n   *  - A dead node i is one that has been removed from the graph, having been absorved into r = flip(ptr[i]).\n   *  - A live element e is one that is in the graph, having been formed when node e was selected as the pivot.\n   *  - A dead element e is one that has benn absorved into a subsequent element s = flip(ptr[e]).\n   */\n  function _initializeQuotientGraph(n, cptr, W, len, head, last, next, hhead, nv, w, elen, degree) {\n    // Initialize quotient graph\n    for (var k = 0; k < n; k++) {\n      W[len + k] = cptr[k + 1] - cptr[k];\n    }\n    W[len + n] = 0;\n    // initialize workspace\n    for (var i = 0; i <= n; i++) {\n      // degree list i is empty\n      W[head + i] = -1;\n      last[i] = -1;\n      W[next + i] = -1;\n      // hash list i is empty\n      W[hhead + i] = -1;\n      // node i is just one node\n      W[nv + i] = 1;\n      // node i is alive\n      W[w + i] = 1;\n      // Ek of node i is empty\n      W[elen + i] = 0;\n      // degree of node i\n      W[degree + i] = W[len + i];\n    }\n    // clear w\n    var mark = _wclear(0, 0, W, w, n);\n    // n is a dead element\n    W[elen + n] = -2;\n    // n is a root of assembly tree\n    cptr[n] = -1;\n    // n is a dead element\n    W[w + n] = 0;\n    // return mark\n    return mark;\n  }\n\n  /**\n   * Initialize degree lists. Each node is placed in its degree lists. Nodes of zero degree are eliminated immediately. Nodes with\n   * degree >= dense are alsol eliminated and merged into a placeholder node n, a dead element. Thes nodes will appera last in the\n   * output permutation p.\n   */\n  function _initializeDegreeLists(n, cptr, W, degree, elen, w, dense, nv, head, last, next) {\n    // result\n    var nel = 0;\n    // loop columns\n    for (var i = 0; i < n; i++) {\n      // degree @ i\n      var d = W[degree + i];\n      // check node i is empty\n      if (d === 0) {\n        // element i is dead\n        W[elen + i] = -2;\n        nel++;\n        // i is a root of assembly tree\n        cptr[i] = -1;\n        W[w + i] = 0;\n      } else if (d > dense) {\n        // absorb i into element n\n        W[nv + i] = 0;\n        // node i is dead\n        W[elen + i] = -1;\n        nel++;\n        cptr[i] = csFlip(n);\n        W[nv + n]++;\n      } else {\n        var h = W[head + d];\n        if (h !== -1) {\n          last[h] = i;\n        }\n        // put node i in degree list d\n        W[next + i] = W[head + d];\n        W[head + d] = i;\n      }\n    }\n    return nel;\n  }\n  function _wclear(mark, lemax, W, w, n) {\n    if (mark < 2 || mark + lemax < 0) {\n      for (var k = 0; k < n; k++) {\n        if (W[w + k] !== 0) {\n          W[w + k] = 1;\n        }\n      }\n      mark = 2;\n    }\n    // at this point, W [0..n-1] < mark holds\n    return mark;\n  }\n  function _diag(i, j) {\n    return i !== j;\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;AACpC,IAAIC,IAAI,GAAG,OAAO;AAClB,IAAIC,YAAY,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC;AACnD,OAAO,IAAIC,WAAW,GAAG,eAAeN,OAAO,CAACI,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC1E,IAAI;IACFC,GAAG;IACHC,QAAQ;IACRC;EACF,CAAC,GAAGH,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASI,KAAKA,CAACC,KAAK,EAAEC,CAAC,EAAE;IAC9B;IACA,IAAI,CAACA,CAAC,IAAID,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;IACA;IACA,IAAIE,KAAK,GAAGD,CAAC,CAACE,KAAK;IACnB;IACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIG,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC;IAChB;IACA,IAAII,KAAK,GAAG,CAAC;IACb;IACA,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAGD,IAAI,CAACE,IAAI,CAACL,CAAC,CAAC,CAAC;IAC3CE,KAAK,GAAGC,IAAI,CAACG,GAAG,CAACN,CAAC,GAAG,CAAC,EAAEE,KAAK,CAAC;IAC9B;IACA,IAAIK,EAAE,GAAGC,mBAAmB,CAACb,KAAK,EAAEC,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEE,KAAK,CAAC;IACnD;IACAlB,OAAO,CAACuB,EAAE,EAAEE,KAAK,EAAE,IAAI,CAAC;IACxB;IACA,IAAIC,MAAM,GAAGH,EAAE,CAACI,MAAM;IACtB,IAAIC,IAAI,GAAGL,EAAE,CAACM,IAAI;;IAElB;IACA,IAAIC,GAAG,GAAGF,IAAI,CAACZ,CAAC,CAAC;;IAEjB;IACA,IAAIe,CAAC,GAAG,EAAE;;IAEV;IACA,IAAIC,CAAC,GAAG,EAAE;IACV,IAAIC,GAAG,GAAG,CAAC,CAAC,CAAC;IACb,IAAIC,EAAE,GAAGlB,CAAC,GAAG,CAAC,CAAC,CAAC;IAChB,IAAImB,IAAI,GAAG,CAAC,IAAInB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAIoB,IAAI,GAAG,CAAC,IAAIpB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAIqB,IAAI,GAAG,CAAC,IAAIrB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAIsB,MAAM,GAAG,CAAC,IAAItB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAIuB,CAAC,GAAG,CAAC,IAAIvB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrB,IAAIwB,KAAK,GAAG,CAAC,IAAIxB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEzB;IACA,IAAIyB,IAAI,GAAGV,CAAC;;IAEZ;IACA,IAAIW,IAAI,GAAGC,wBAAwB,CAAC3B,CAAC,EAAEY,IAAI,EAAEI,CAAC,EAAEC,GAAG,EAAEG,IAAI,EAAEK,IAAI,EAAEN,IAAI,EAAEK,KAAK,EAAEN,EAAE,EAAEK,CAAC,EAAEF,IAAI,EAAEC,MAAM,CAAC;;IAElG;IACA,IAAIM,GAAG,GAAGC,sBAAsB,CAAC7B,CAAC,EAAEY,IAAI,EAAEI,CAAC,EAAEM,MAAM,EAAED,IAAI,EAAEE,CAAC,EAAErB,KAAK,EAAEgB,EAAE,EAAEE,IAAI,EAAEK,IAAI,EAAEN,IAAI,CAAC;;IAE1F;IACA,IAAIW,MAAM,GAAG,CAAC;;IAEd;IACA,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,CAAC;;IAE9D;IACA,OAAOlB,GAAG,GAAG5B,CAAC,EAAE;MACd;MACA;MACA;MACA,KAAKiC,CAAC,GAAG,CAAC,CAAC,EAAEH,MAAM,GAAG9B,CAAC,IAAI,CAACiC,CAAC,GAAGjB,CAAC,CAACI,IAAI,GAAGU,MAAM,CAAC,MAAM,CAAC,CAAC,EAAEA,MAAM,EAAE,CAAC;MACnE,IAAId,CAAC,CAACG,IAAI,GAAGc,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACtBR,IAAI,CAACT,CAAC,CAACG,IAAI,GAAGc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACxB;MACA;MACAjB,CAAC,CAACI,IAAI,GAAGU,MAAM,CAAC,GAAGd,CAAC,CAACG,IAAI,GAAGc,CAAC,CAAC;MAC9B;MACA,IAAIc,KAAK,GAAG/B,CAAC,CAACK,IAAI,GAAGY,CAAC,CAAC;MACvB;MACA,IAAIe,GAAG,GAAGhC,CAAC,CAACE,EAAE,GAAGe,CAAC,CAAC;MACnB;MACAL,GAAG,IAAIoB,GAAG;;MAEV;MACA;MACA;MACA,IAAIC,EAAE,GAAG,CAAC;MACV;MACAjC,CAAC,CAACE,EAAE,GAAGe,CAAC,CAAC,GAAG,CAACe,GAAG;MAChB,IAAIE,CAAC,GAAGtC,IAAI,CAACqB,CAAC,CAAC;MACf;MACA,IAAIkB,GAAG,GAAGJ,KAAK,KAAK,CAAC,GAAGG,CAAC,GAAGpC,GAAG;MAC/B,IAAIsC,GAAG,GAAGD,GAAG;MACb,KAAKjB,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAIa,KAAK,GAAG,CAAC,EAAEb,EAAE,EAAE,EAAE;QAClC,IAAIA,EAAE,GAAGa,KAAK,EAAE;UACd;UACAX,CAAC,GAAGH,CAAC;UACL;UACAI,EAAE,GAAGa,CAAC;UACN;UACAZ,EAAE,GAAGtB,CAAC,CAACC,GAAG,GAAGgB,CAAC,CAAC,GAAGc,KAAK;QACzB,CAAC,MAAM;UACL;UACAX,CAAC,GAAG1B,MAAM,CAACwC,CAAC,EAAE,CAAC;UACfb,EAAE,GAAGzB,IAAI,CAACwB,CAAC,CAAC;UACZ;UACAE,EAAE,GAAGtB,CAAC,CAACC,GAAG,GAAGmB,CAAC,CAAC;QACjB;QACA,KAAKD,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAIG,EAAE,EAAEH,EAAE,EAAE,EAAE;UAC3BJ,CAAC,GAAGrB,MAAM,CAAC2B,EAAE,EAAE,CAAC;UAChB;UACA,IAAI,CAACE,GAAG,GAAGvB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,KAAK,CAAC,EAAE;YAC1B;UACF;UACA;UACAkB,EAAE,IAAIV,GAAG;UACT;UACAvB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,GAAG,CAACQ,GAAG;UAChB;UACA7B,MAAM,CAAC0C,GAAG,EAAE,CAAC,GAAGrB,CAAC;UACjB,IAAIf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACtBN,IAAI,CAACT,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACM,CAAC,CAAC;UAC7B;UACA;UACA,IAAIN,IAAI,CAACM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAClBf,CAAC,CAACG,IAAI,GAAGM,IAAI,CAACM,CAAC,CAAC,CAAC,GAAGf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC;UACjC,CAAC,MAAM;YACLf,CAAC,CAACI,IAAI,GAAGJ,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC,CAAC,GAAGf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC;UACvC;QACF;QACA,IAAIK,CAAC,KAAKH,CAAC,EAAE;UACX;UACArB,IAAI,CAACwB,CAAC,CAAC,GAAGnD,MAAM,CAACgD,CAAC,CAAC;UACnB;UACAjB,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,GAAG,CAAC;QACd;MACF;MACA;MACA,IAAIW,KAAK,KAAK,CAAC,EAAE;QACfjC,GAAG,GAAGsC,GAAG;MACX;MACA;MACApC,CAAC,CAACM,MAAM,GAAGW,CAAC,CAAC,GAAGgB,EAAE;MAClB;MACArC,IAAI,CAACqB,CAAC,CAAC,GAAGkB,GAAG;MACbnC,CAAC,CAACC,GAAG,GAAGgB,CAAC,CAAC,GAAGmB,GAAG,GAAGD,GAAG;MACtB;MACAnC,CAAC,CAACK,IAAI,GAAGY,CAAC,CAAC,GAAG,CAAC,CAAC;;MAEhB;MACA;;MAEA;MACAP,IAAI,GAAG2B,OAAO,CAAC3B,IAAI,EAAEzB,KAAK,EAAEe,CAAC,EAAEO,CAAC,EAAEvB,CAAC,CAAC;MACpC;MACA,KAAKwC,EAAE,GAAGW,GAAG,EAAEX,EAAE,GAAGY,GAAG,EAAEZ,EAAE,EAAE,EAAE;QAC7BT,CAAC,GAAGrB,MAAM,CAAC8B,EAAE,CAAC;QACd;QACA,IAAI,CAACC,GAAG,GAAGzB,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,KAAK,CAAC,EAAE;UAC5B;QACF;QACA;QACAQ,GAAG,GAAG,CAACvB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC;QAChB,IAAIuB,IAAI,GAAG5B,IAAI,GAAGa,GAAG;QACrB;QACA,KAAKW,CAAC,GAAGtC,IAAI,CAACmB,CAAC,CAAC,EAAEW,EAAE,GAAG9B,IAAI,CAACmB,CAAC,CAAC,GAAGU,GAAG,GAAG,CAAC,EAAES,CAAC,IAAIR,EAAE,EAAEQ,CAAC,EAAE,EAAE;UACtDd,CAAC,GAAG1B,MAAM,CAACwC,CAAC,CAAC;UACb,IAAIlC,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,IAAIV,IAAI,EAAE;YACpB;YACAV,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,IAAIG,GAAG;UACjB,CAAC,MAAM,IAAIvB,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,KAAK,CAAC,EAAE;YACzB;YACApB,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,GAAGpB,CAAC,CAACM,MAAM,GAAGc,CAAC,CAAC,GAAGkB,IAAI;UACjC;QACF;MACF;;MAEA;MACA;MACA;;MAEA;MACA,KAAKd,EAAE,GAAGW,GAAG,EAAEX,EAAE,GAAGY,GAAG,EAAEZ,EAAE,EAAE,EAAE;QAC7B;QACAT,CAAC,GAAGrB,MAAM,CAAC8B,EAAE,CAAC;QACdE,EAAE,GAAG9B,IAAI,CAACmB,CAAC,CAAC;QACZY,EAAE,GAAGD,EAAE,GAAG1B,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,GAAG,CAAC;QACzBa,EAAE,GAAGF,EAAE;QACP;QACA,KAAKG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAGR,EAAE,EAAEQ,CAAC,IAAIP,EAAE,EAAEO,CAAC,EAAE,EAAE;UACvCd,CAAC,GAAG1B,MAAM,CAACwC,CAAC,CAAC;UACb;UACA,IAAIlC,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,KAAK,CAAC,EAAE;YAClB;YACA,IAAImB,IAAI,GAAGvC,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,GAAGV,IAAI;YAC1B,IAAI6B,IAAI,GAAG,CAAC,EAAE;cACZ;cACAT,CAAC,IAAIS,IAAI;cACT;cACA7C,MAAM,CAACkC,EAAE,EAAE,CAAC,GAAGR,CAAC;cAChB;cACAS,CAAC,IAAIT,CAAC;YACR,CAAC,MAAM;cACL;cACAxB,IAAI,CAACwB,CAAC,CAAC,GAAGnD,MAAM,CAACgD,CAAC,CAAC;cACnB;cACAjB,CAAC,CAACO,CAAC,GAAGa,CAAC,CAAC,GAAG,CAAC;YACd;UACF;QACF;QACA;QACApB,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,GAAGa,EAAE,GAAGF,EAAE,GAAG,CAAC;QACzB,IAAIc,EAAE,GAAGZ,EAAE;QACX,IAAIa,EAAE,GAAGf,EAAE,GAAG1B,CAAC,CAACC,GAAG,GAAGc,CAAC,CAAC;QACxB;QACA,KAAKmB,CAAC,GAAGP,EAAE,GAAG,CAAC,EAAEO,CAAC,GAAGO,EAAE,EAAEP,CAAC,EAAE,EAAE;UAC5BlB,CAAC,GAAGtB,MAAM,CAACwC,CAAC,CAAC;UACb;UACA,IAAIQ,GAAG,GAAG1C,CAAC,CAACE,EAAE,GAAGc,CAAC,CAAC;UACnB,IAAI0B,GAAG,IAAI,CAAC,EAAE;YACZ;UACF;UACA;UACAZ,CAAC,IAAIY,GAAG;UACR;UACAhD,MAAM,CAACkC,EAAE,EAAE,CAAC,GAAGZ,CAAC;UAChB;UACAa,CAAC,IAAIb,CAAC;QACR;QACA;QACA,IAAIc,CAAC,KAAK,CAAC,EAAE;UACX;UACAlC,IAAI,CAACmB,CAAC,CAAC,GAAG9C,MAAM,CAACgD,CAAC,CAAC;UACnBM,GAAG,GAAG,CAACvB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC;UAChB;UACAkB,EAAE,IAAIV,GAAG;UACT;UACAS,GAAG,IAAIT,GAAG;UACVX,GAAG,IAAIW,GAAG;UACVvB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,GAAG,CAAC;UACb;UACAf,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC,MAAM;UACL;UACAf,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC,GAAG5B,IAAI,CAACG,GAAG,CAACU,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC,EAAEe,CAAC,CAAC;UAC1C;UACApC,MAAM,CAACkC,EAAE,CAAC,GAAGlC,MAAM,CAAC8C,EAAE,CAAC;UACvB;UACA9C,MAAM,CAAC8C,EAAE,CAAC,GAAG9C,MAAM,CAACgC,EAAE,CAAC;UACvB;UACAhC,MAAM,CAACgC,EAAE,CAAC,GAAGT,CAAC;UACd;UACAjB,CAAC,CAACC,GAAG,GAAGc,CAAC,CAAC,GAAGa,EAAE,GAAGF,EAAE,GAAG,CAAC;UACxB;UACAG,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAGA,CAAC,IAAI7C,CAAC;UACxB;UACAgB,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,GAAGf,CAAC,CAACQ,KAAK,GAAGqB,CAAC,CAAC;UAC1B7B,CAAC,CAACQ,KAAK,GAAGqB,CAAC,CAAC,GAAGd,CAAC;UAChB;UACAN,IAAI,CAACM,CAAC,CAAC,GAAGc,CAAC;QACb;MACF;MACA;MACA7B,CAAC,CAACM,MAAM,GAAGW,CAAC,CAAC,GAAGgB,EAAE;MAClBhD,KAAK,GAAGE,IAAI,CAACC,GAAG,CAACH,KAAK,EAAEgD,EAAE,CAAC;MAC3B;MACAvB,IAAI,GAAG2B,OAAO,CAAC3B,IAAI,GAAGzB,KAAK,EAAEA,KAAK,EAAEe,CAAC,EAAEO,CAAC,EAAEvB,CAAC,CAAC;;MAE5C;MACA;MACA,KAAKwC,EAAE,GAAGW,GAAG,EAAEX,EAAE,GAAGY,GAAG,EAAEZ,EAAE,EAAE,EAAE;QAC7BT,CAAC,GAAGrB,MAAM,CAAC8B,EAAE,CAAC;QACd;QACA,IAAIxB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,IAAI,CAAC,EAAE;UAClB;QACF;QACA;QACAc,CAAC,GAAGpB,IAAI,CAACM,CAAC,CAAC;QACXA,CAAC,GAAGf,CAAC,CAACQ,KAAK,GAAGqB,CAAC,CAAC;QAChB;QACA7B,CAAC,CAACQ,KAAK,GAAGqB,CAAC,CAAC,GAAG,CAAC,CAAC;QACjB,OAAOd,CAAC,KAAK,CAAC,CAAC,IAAIf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,KAAK,CAAC,CAAC,EAAEA,CAAC,GAAGf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,EAAEL,IAAI,EAAE,EAAE;UAC9DY,EAAE,GAAGtB,CAAC,CAACC,GAAG,GAAGc,CAAC,CAAC;UACfU,GAAG,GAAGzB,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC;UACjB,KAAKmB,CAAC,GAAGtC,IAAI,CAACmB,CAAC,CAAC,GAAG,CAAC,EAAEmB,CAAC,IAAItC,IAAI,CAACmB,CAAC,CAAC,GAAGO,EAAE,GAAG,CAAC,EAAEY,CAAC,EAAE,EAAE;YAChDlC,CAAC,CAACO,CAAC,GAAGb,MAAM,CAACwC,CAAC,CAAC,CAAC,GAAGxB,IAAI;UACzB;UACA,IAAIiC,KAAK,GAAG5B,CAAC;UACb;UACA,KAAKC,CAAC,GAAGhB,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,EAAEC,CAAC,KAAK,CAAC,CAAC,GAAG;YAC/B,IAAI4B,EAAE,GAAG5C,CAAC,CAACC,GAAG,GAAGe,CAAC,CAAC,KAAKM,EAAE,IAAItB,CAAC,CAACK,IAAI,GAAGW,CAAC,CAAC,KAAKS,GAAG;YACjD,KAAKS,CAAC,GAAGtC,IAAI,CAACoB,CAAC,CAAC,GAAG,CAAC,EAAE4B,EAAE,IAAIV,CAAC,IAAItC,IAAI,CAACoB,CAAC,CAAC,GAAGM,EAAE,GAAG,CAAC,EAAEY,CAAC,EAAE,EAAE;cACtD;cACA,IAAIlC,CAAC,CAACO,CAAC,GAAGb,MAAM,CAACwC,CAAC,CAAC,CAAC,KAAKxB,IAAI,EAAE;gBAC7BkC,EAAE,GAAG,CAAC;cACR;YACF;YACA;YACA,IAAIA,EAAE,EAAE;cACN;cACAhD,IAAI,CAACoB,CAAC,CAAC,GAAG/C,MAAM,CAAC8C,CAAC,CAAC;cACnBf,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,IAAIf,CAAC,CAACE,EAAE,GAAGc,CAAC,CAAC;cACtBhB,CAAC,CAACE,EAAE,GAAGc,CAAC,CAAC,GAAG,CAAC;cACb;cACAhB,CAAC,CAACK,IAAI,GAAGW,CAAC,CAAC,GAAG,CAAC,CAAC;cAChB;cACAA,CAAC,GAAGhB,CAAC,CAACG,IAAI,GAAGa,CAAC,CAAC;cACfhB,CAAC,CAACG,IAAI,GAAGwC,KAAK,CAAC,GAAG3B,CAAC;YACrB,CAAC,MAAM;cACL;cACA2B,KAAK,GAAG3B,CAAC;cACTA,CAAC,GAAGhB,CAAC,CAACG,IAAI,GAAGa,CAAC,CAAC;YACjB;UACF;QACF;MACF;;MAEA;MACA;MACA,KAAKkB,CAAC,GAAGC,GAAG,EAAEX,EAAE,GAAGW,GAAG,EAAEX,EAAE,GAAGY,GAAG,EAAEZ,EAAE,EAAE,EAAE;QACtCT,CAAC,GAAGrB,MAAM,CAAC8B,EAAE,CAAC;QACd;QACA,IAAI,CAACD,GAAG,GAAG,CAACvB,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,KAAK,CAAC,EAAE;UAC3B;QACF;QACA;QACAf,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,GAAGQ,GAAG;QACf;QACAO,CAAC,GAAG9B,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC,GAAGkB,EAAE,GAAGV,GAAG;QAC5BO,CAAC,GAAG3C,IAAI,CAACG,GAAG,CAACwC,CAAC,EAAE9C,CAAC,GAAG4B,GAAG,GAAGW,GAAG,CAAC;QAC9B,IAAIvB,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;UACtBrB,IAAI,CAACT,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC,CAAC,GAAGf,CAAC;QACvB;QACA;QACAf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,GAAGf,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC;QACzBrB,IAAI,CAACM,CAAC,CAAC,GAAG,CAAC,CAAC;QACZf,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC,GAAGf,CAAC;QACf;QACAD,MAAM,GAAG3B,IAAI,CAACG,GAAG,CAACwB,MAAM,EAAEgB,CAAC,CAAC;QAC5B9B,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC,GAAGe,CAAC;QACjB;QACApC,MAAM,CAACwC,CAAC,EAAE,CAAC,GAAGnB,CAAC;MACjB;MACA;MACAf,CAAC,CAACE,EAAE,GAAGe,CAAC,CAAC,GAAGe,GAAG;MACf;MACA,IAAI,CAAChC,CAAC,CAACC,GAAG,GAAGgB,CAAC,CAAC,GAAGiB,CAAC,GAAGC,GAAG,MAAM,CAAC,EAAE;QAChC;QACAvC,IAAI,CAACqB,CAAC,CAAC,GAAG,CAAC,CAAC;QACZ;QACAjB,CAAC,CAACO,CAAC,GAAGU,CAAC,CAAC,GAAG,CAAC;MACd;MACA,IAAIc,KAAK,KAAK,CAAC,EAAE;QACf;QACAjC,GAAG,GAAGoC,CAAC;MACT;IACF;;IAEA;IACA;IACA;IACA;;IAEA;IACA,KAAKnB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,CAAC,EAAE+B,CAAC,EAAE,EAAE;MACtBnB,IAAI,CAACmB,CAAC,CAAC,GAAG9C,MAAM,CAAC2B,IAAI,CAACmB,CAAC,CAAC,CAAC;IAC3B;IACA,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIhC,CAAC,EAAEgC,CAAC,EAAE,EAAE;MACvBhB,CAAC,CAACI,IAAI,GAAGY,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB;IACA;IACA,KAAKA,CAAC,GAAGhC,CAAC,EAAEgC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvB;MACA,IAAIhB,CAAC,CAACE,EAAE,GAAGc,CAAC,CAAC,GAAG,CAAC,EAAE;QACjB;MACF;MACA;MACAhB,CAAC,CAACG,IAAI,GAAGa,CAAC,CAAC,GAAGhB,CAAC,CAACI,IAAI,GAAGR,IAAI,CAACoB,CAAC,CAAC,CAAC;MAC/BhB,CAAC,CAACI,IAAI,GAAGR,IAAI,CAACoB,CAAC,CAAC,CAAC,GAAGA,CAAC;IACvB;IACA;IACA,KAAKI,CAAC,GAAGpC,CAAC,EAAEoC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvB;MACA,IAAIpB,CAAC,CAACE,EAAE,GAAGkB,CAAC,CAAC,IAAI,CAAC,EAAE;QAClB;MACF;MACA,IAAIxB,IAAI,CAACwB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAClB;QACApB,CAAC,CAACG,IAAI,GAAGiB,CAAC,CAAC,GAAGpB,CAAC,CAACI,IAAI,GAAGR,IAAI,CAACwB,CAAC,CAAC,CAAC;QAC/BpB,CAAC,CAACI,IAAI,GAAGR,IAAI,CAACwB,CAAC,CAAC,CAAC,GAAGA,CAAC;MACvB;IACF;IACA;IACA,KAAKH,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI/B,CAAC,EAAE+B,CAAC,EAAE,EAAE;MAC9B,IAAInB,IAAI,CAACmB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAClBE,CAAC,GAAG/C,MAAM,CAAC6C,CAAC,EAAEE,CAAC,EAAEjB,CAAC,EAAEI,IAAI,EAAED,IAAI,EAAEJ,CAAC,EAAEQ,CAAC,CAAC;MACvC;IACF;IACA;IACAR,CAAC,CAAC8C,MAAM,CAAC9C,CAAC,CAAC+C,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IACzB;IACA,OAAO/C,CAAC;EACV,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASP,mBAAmBA,CAACb,KAAK,EAAEC,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEE,KAAK,EAAE;IAClD;IACA,IAAI6D,EAAE,GAAGtE,SAAS,CAACG,CAAC,CAAC;;IAErB;IACA,IAAID,KAAK,KAAK,CAAC,IAAIK,CAAC,KAAKD,CAAC,EAAE;MAC1B;MACA,OAAOR,GAAG,CAACK,CAAC,EAAEmE,EAAE,CAAC;IACnB;;IAEA;IACA,IAAIpE,KAAK,KAAK,CAAC,EAAE;MACf;MACA,IAAIqE,MAAM,GAAGD,EAAE,CAACpD,MAAM;MACtB,IAAIsD,IAAI,GAAGF,EAAE,CAAClD,IAAI;MAClB;MACA,IAAI8B,EAAE,GAAG,CAAC;MACV;MACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,CAAC,EAAEiC,CAAC,EAAE,EAAE;QAC1B;QACA,IAAIkB,CAAC,GAAGe,IAAI,CAACjC,CAAC,CAAC;QACf;QACAiC,IAAI,CAACjC,CAAC,CAAC,GAAGW,EAAE;QACZ;QACA,IAAIsB,IAAI,CAACjC,CAAC,GAAG,CAAC,CAAC,GAAGkB,CAAC,GAAGhD,KAAK,EAAE;UAC3B;QACF;QACA;QACA,KAAK,IAAIwC,EAAE,GAAGuB,IAAI,CAACjC,CAAC,GAAG,CAAC,CAAC,EAAEkB,CAAC,GAAGR,EAAE,EAAEQ,CAAC,EAAE,EAAE;UACtCc,MAAM,CAACrB,EAAE,EAAE,CAAC,GAAGqB,MAAM,CAACd,CAAC,CAAC;QAC1B;MACF;MACA;MACAe,IAAI,CAAClE,CAAC,CAAC,GAAG4C,EAAE;MACZ;MACA/C,CAAC,GAAGH,SAAS,CAACsE,EAAE,CAAC;MACjB;MACA,OAAOvE,QAAQ,CAACuE,EAAE,EAAEnE,CAAC,CAAC;IACxB;;IAEA;IACA,OAAOJ,QAAQ,CAACuE,EAAE,EAAEnE,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS+B,wBAAwBA,CAAC3B,CAAC,EAAEY,IAAI,EAAEI,CAAC,EAAEC,GAAG,EAAEG,IAAI,EAAEK,IAAI,EAAEN,IAAI,EAAEK,KAAK,EAAEN,EAAE,EAAEK,CAAC,EAAEF,IAAI,EAAEC,MAAM,EAAE;IAC/F;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,CAAC,EAAEiC,CAAC,EAAE,EAAE;MAC1BjB,CAAC,CAACC,GAAG,GAAGgB,CAAC,CAAC,GAAGrB,IAAI,CAACqB,CAAC,GAAG,CAAC,CAAC,GAAGrB,IAAI,CAACqB,CAAC,CAAC;IACpC;IACAjB,CAAC,CAACC,GAAG,GAAGjB,CAAC,CAAC,GAAG,CAAC;IACd;IACA,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI/B,CAAC,EAAE+B,CAAC,EAAE,EAAE;MAC3B;MACAf,CAAC,CAACI,IAAI,GAAGW,CAAC,CAAC,GAAG,CAAC,CAAC;MAChBN,IAAI,CAACM,CAAC,CAAC,GAAG,CAAC,CAAC;MACZf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,GAAG,CAAC,CAAC;MAChB;MACAf,CAAC,CAACQ,KAAK,GAAGO,CAAC,CAAC,GAAG,CAAC,CAAC;MACjB;MACAf,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,GAAG,CAAC;MACb;MACAf,CAAC,CAACO,CAAC,GAAGQ,CAAC,CAAC,GAAG,CAAC;MACZ;MACAf,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,GAAG,CAAC;MACf;MACAf,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC,GAAGf,CAAC,CAACC,GAAG,GAAGc,CAAC,CAAC;IAC5B;IACA;IACA,IAAIL,IAAI,GAAG2B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAErC,CAAC,EAAEO,CAAC,EAAEvB,CAAC,CAAC;IACjC;IACAgB,CAAC,CAACK,IAAI,GAAGrB,CAAC,CAAC,GAAG,CAAC,CAAC;IAChB;IACAY,IAAI,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IACZ;IACAgB,CAAC,CAACO,CAAC,GAAGvB,CAAC,CAAC,GAAG,CAAC;IACZ;IACA,OAAO0B,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASG,sBAAsBA,CAAC7B,CAAC,EAAEY,IAAI,EAAEI,CAAC,EAAEM,MAAM,EAAED,IAAI,EAAEE,CAAC,EAAErB,KAAK,EAAEgB,EAAE,EAAEE,IAAI,EAAEK,IAAI,EAAEN,IAAI,EAAE;IACxF;IACA,IAAIS,GAAG,GAAG,CAAC;IACX;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,CAAC,EAAE+B,CAAC,EAAE,EAAE;MAC1B;MACA,IAAIe,CAAC,GAAG9B,CAAC,CAACM,MAAM,GAAGS,CAAC,CAAC;MACrB;MACA,IAAIe,CAAC,KAAK,CAAC,EAAE;QACX;QACA9B,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,GAAG,CAAC,CAAC;QAChBH,GAAG,EAAE;QACL;QACAhB,IAAI,CAACmB,CAAC,CAAC,GAAG,CAAC,CAAC;QACZf,CAAC,CAACO,CAAC,GAAGQ,CAAC,CAAC,GAAG,CAAC;MACd,CAAC,MAAM,IAAIe,CAAC,GAAG5C,KAAK,EAAE;QACpB;QACAc,CAAC,CAACE,EAAE,GAAGa,CAAC,CAAC,GAAG,CAAC;QACb;QACAf,CAAC,CAACK,IAAI,GAAGU,CAAC,CAAC,GAAG,CAAC,CAAC;QAChBH,GAAG,EAAE;QACLhB,IAAI,CAACmB,CAAC,CAAC,GAAG9C,MAAM,CAACe,CAAC,CAAC;QACnBgB,CAAC,CAACE,EAAE,GAAGlB,CAAC,CAAC,EAAE;MACb,CAAC,MAAM;QACL,IAAI6C,CAAC,GAAG7B,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC;QACnB,IAAID,CAAC,KAAK,CAAC,CAAC,EAAE;UACZpB,IAAI,CAACoB,CAAC,CAAC,GAAGd,CAAC;QACb;QACA;QACAf,CAAC,CAACG,IAAI,GAAGY,CAAC,CAAC,GAAGf,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC;QACzB9B,CAAC,CAACI,IAAI,GAAG0B,CAAC,CAAC,GAAGf,CAAC;MACjB;IACF;IACA,OAAOH,GAAG;EACZ;EACA,SAASyB,OAAOA,CAAC3B,IAAI,EAAEzB,KAAK,EAAEe,CAAC,EAAEO,CAAC,EAAEvB,CAAC,EAAE;IACrC,IAAI0B,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAGzB,KAAK,GAAG,CAAC,EAAE;MAChC,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,CAAC,EAAEiC,CAAC,EAAE,EAAE;QAC1B,IAAIjB,CAAC,CAACO,CAAC,GAAGU,CAAC,CAAC,KAAK,CAAC,EAAE;UAClBjB,CAAC,CAACO,CAAC,GAAGU,CAAC,CAAC,GAAG,CAAC;QACd;MACF;MACAP,IAAI,GAAG,CAAC;IACV;IACA;IACA,OAAOA,IAAI;EACb;EACA,SAASjB,KAAKA,CAACsB,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAOD,CAAC,KAAKC,CAAC;EAChB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}