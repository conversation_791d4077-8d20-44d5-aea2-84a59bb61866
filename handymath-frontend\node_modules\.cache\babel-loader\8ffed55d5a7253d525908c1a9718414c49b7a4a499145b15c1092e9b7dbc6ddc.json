{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\StudentLayout.tsx\";\nimport React from 'react';\nimport StudentBreadcrumbs from './StudentBreadcrumbs';\nimport FloatingNavigation from './FloatingNavigation';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentLayout = ({\n  children,\n  title,\n  subtitle,\n  showBreadcrumbs = true,\n  breadcrumbItems,\n  actions,\n  className = '',\n  showFloatingNav = true\n}) => {\n  // Navigation flottante pour l'espace étudiant\n  const floatingNavItems = [{\n    id: 'dashboard',\n    label: 'Tableau de bord',\n    path: '/etudiant/dashboard',\n    icon: 'DB',\n    shortcut: 'd'\n  }, {\n    id: 'courses',\n    label: 'Mes Cours',\n    path: '/courses',\n    icon: 'CO',\n    shortcut: 'c'\n  }, {\n    id: 'exercises',\n    label: 'Exercices',\n    path: '/exercises',\n    icon: 'EX',\n    shortcut: 'e'\n  }, {\n    id: 'progress',\n    label: 'Ma Progress<PERSON>',\n    path: '/progress',\n    icon: 'PR',\n    shortcut: 'p'\n  }, {\n    id: 'solver',\n    label: 'Résolveur',\n    path: '/solver',\n    icon: 'SO',\n    shortcut: 's'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n        children: [showBreadcrumbs && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(StudentBreadcrumbs, {\n            items: breadcrumbItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), (title || subtitle || actions) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [title && /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                children: title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 21\n              }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-lg text-gray-600 dark:text-gray-400\",\n                children: subtitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this), actions && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: actions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: className,\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), showFloatingNav && /*#__PURE__*/_jsxDEV(FloatingNavigation, {\n      items: floatingNavItems,\n      position: \"bottom-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_c = StudentLayout;\nexport default StudentLayout;\nvar _c;\n$RefreshReg$(_c, \"StudentLayout\");", "map": {"version": 3, "names": ["React", "StudentBreadcrumbs", "FloatingNavigation", "Footer", "jsxDEV", "_jsxDEV", "StudentLayout", "children", "title", "subtitle", "showBreadcrumbs", "breadcrumbItems", "actions", "className", "showFloatingNav", "floatingNavItems", "id", "label", "path", "icon", "shortcut", "items", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/StudentLayout.tsx"], "sourcesContent": ["import React from 'react';\nimport StudentBreadcrumbs from './StudentBreadcrumbs';\nimport FloatingNavigation from './FloatingNavigation';\nimport Footer from './Footer';\n\ninterface StudentLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  showBreadcrumbs?: boolean;\n  breadcrumbItems?: Array<{\n    label: string;\n    path?: string;\n    icon?: string;\n    isActive?: boolean;\n  }>;\n  actions?: React.ReactNode;\n  className?: string;\n  showFloatingNav?: boolean;\n}\n\nconst StudentLayout: React.FC<StudentLayoutProps> = ({\n  children,\n  title,\n  subtitle,\n  showBreadcrumbs = true,\n  breadcrumbItems,\n  actions,\n  className = '',\n  showFloatingNav = true\n}) => {\n  // Navigation flottante pour l'espace étudiant\n  const floatingNavItems = [\n    {\n      id: 'dashboard',\n      label: 'Tableau de bord',\n      path: '/etudiant/dashboard',\n      icon: 'DB',\n      shortcut: 'd'\n    },\n    {\n      id: 'courses',\n      label: 'Mes Cours',\n      path: '/courses',\n      icon: 'CO',\n      shortcut: 'c'\n    },\n    {\n      id: 'exercises',\n      label: 'Exercices',\n      path: '/exercises',\n      icon: 'EX',\n      shortcut: 'e'\n    },\n    {\n      id: 'progress',\n      label: 'Ma Progression',\n      path: '/progress',\n      icon: 'PR',\n      shortcut: 'p'\n    },\n    {\n      id: 'solver',\n      label: 'Résolveur',\n      path: '/solver',\n      icon: 'SO',\n      shortcut: 's'\n    }\n  ];\n  return (\n    <div className=\"min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900\">\n      {/* Contenu principal */}\n      <main className=\"flex-1\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          {/* Breadcrumbs */}\n          {showBreadcrumbs && (\n            <div className=\"mb-6\">\n              <StudentBreadcrumbs items={breadcrumbItems} />\n            </div>\n          )}\n          \n          {/* En-tête de page */}\n          {(title || subtitle || actions) && (\n            <div className=\"mb-8\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n                <div className=\"flex-1\">\n                  {title && (\n                    <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                      {title}\n                    </h1>\n                  )}\n                  {subtitle && (\n                    <p className=\"mt-2 text-lg text-gray-600 dark:text-gray-400\">\n                      {subtitle}\n                    </p>\n                  )}\n                </div>\n                \n                {/* Actions */}\n                {actions && (\n                  <div className=\"flex-shrink-0\">\n                    {actions}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n          \n          {/* Contenu de la page */}\n          <div className={className}>\n            {children}\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <Footer />\n\n      {/* Navigation flottante */}\n      {showFloatingNav && (\n        <FloatingNavigation\n          items={floatingNavItems}\n          position=\"bottom-right\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default StudentLayout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkB9B,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,QAAQ;EACRC,KAAK;EACLC,QAAQ;EACRC,eAAe,GAAG,IAAI;EACtBC,eAAe;EACfC,OAAO;EACPC,SAAS,GAAG,EAAE;EACdC,eAAe,GAAG;AACpB,CAAC,KAAK;EACJ;EACA,MAAMC,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,CACF;EACD,oBACEf,OAAA;IAAKQ,SAAS,EAAC,wDAAwD;IAAAN,QAAA,gBAErEF,OAAA;MAAMQ,SAAS,EAAC,QAAQ;MAAAN,QAAA,eACtBF,OAAA;QAAKQ,SAAS,EAAC,6CAA6C;QAAAN,QAAA,GAEzDG,eAAe,iBACdL,OAAA;UAAKQ,SAAS,EAAC,MAAM;UAAAN,QAAA,eACnBF,OAAA,CAACJ,kBAAkB;YAACoB,KAAK,EAAEV;UAAgB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACN,EAGA,CAACjB,KAAK,IAAIC,QAAQ,IAAIG,OAAO,kBAC5BP,OAAA;UAAKQ,SAAS,EAAC,MAAM;UAAAN,QAAA,eACnBF,OAAA;YAAKQ,SAAS,EAAC,qFAAqF;YAAAN,QAAA,gBAClGF,OAAA;cAAKQ,SAAS,EAAC,QAAQ;cAAAN,QAAA,GACpBC,KAAK,iBACJH,OAAA;gBAAIQ,SAAS,EAAC,kDAAkD;gBAAAN,QAAA,EAC7DC;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACL,EACAhB,QAAQ,iBACPJ,OAAA;gBAAGQ,SAAS,EAAC,+CAA+C;gBAAAN,QAAA,EACzDE;cAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGLb,OAAO,iBACNP,OAAA;cAAKQ,SAAS,EAAC,eAAe;cAAAN,QAAA,EAC3BK;YAAO;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDpB,OAAA;UAAKQ,SAAS,EAAEA,SAAU;UAAAN,QAAA,EACvBA;QAAQ;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPpB,OAAA,CAACF,MAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGTX,eAAe,iBACdT,OAAA,CAACH,kBAAkB;MACjBmB,KAAK,EAAEN,gBAAiB;MACxBW,QAAQ,EAAC;IAAc;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACE,EAAA,GA1GIrB,aAA2C;AA4GjD,eAAeA,aAAa;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}