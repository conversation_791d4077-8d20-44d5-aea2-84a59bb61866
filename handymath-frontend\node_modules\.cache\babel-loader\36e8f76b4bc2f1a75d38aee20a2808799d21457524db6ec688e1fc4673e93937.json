{"ast": null, "code": "export var pi = Math.PI;\nexport var tau = 2 * Math.PI;\nexport var e = Math.E;\nexport var phi = 1.6180339887498948; // eslint-disable-line no-loss-of-precision", "map": {"version": 3, "names": ["pi", "Math", "PI", "tau", "e", "E", "phi"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/constants.js"], "sourcesContent": ["export var pi = Math.PI;\nexport var tau = 2 * Math.PI;\nexport var e = Math.E;\nexport var phi = 1.6180339887498948; // eslint-disable-line no-loss-of-precision"], "mappings": "AAAA,OAAO,IAAIA,EAAE,GAAGC,IAAI,CAACC,EAAE;AACvB,OAAO,IAAIC,GAAG,GAAG,CAAC,GAAGF,IAAI,CAACC,EAAE;AAC5B,OAAO,IAAIE,CAAC,GAAGH,IAAI,CAACI,CAAC;AACrB,OAAO,IAAIC,GAAG,GAAG,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}