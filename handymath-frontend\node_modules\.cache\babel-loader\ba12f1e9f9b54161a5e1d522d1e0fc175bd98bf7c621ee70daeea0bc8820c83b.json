{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\FloatingNavigation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FloatingNavigation = ({\n  items,\n  position = 'bottom-right',\n  className = ''\n}) => {\n  _s();\n  const location = useLocation();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [activeItem, setActiveItem] = useState('');\n  useEffect(() => {\n    // Déterminer l'élément actif basé sur l'URL actuelle\n    const currentItem = items.find(item => !item.isSeparator && !item.isAction && (location.pathname === item.path || item.path !== '/' && item.path && location.pathname.startsWith(item.path)));\n    setActiveItem((currentItem === null || currentItem === void 0 ? void 0 : currentItem.id) || '');\n  }, [location.pathname, items]);\n\n  // Gestion des raccourcis clavier\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.ctrlKey || event.metaKey) {\n        const item = items.find(item => item.shortcut === event.key.toLowerCase());\n        if (item) {\n          event.preventDefault();\n          if (item.isAction && item.action) {\n            item.action();\n          } else if (item.path) {\n            window.location.href = item.path;\n          }\n        }\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [items]);\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'bottom-6 left-6';\n      case 'bottom-center':\n        return 'bottom-6 left-1/2 transform -translate-x-1/2';\n      case 'bottom-right':\n      default:\n        return 'bottom-6 right-6';\n    }\n  };\n  const getExpandDirection = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'flex-col-reverse';\n      case 'bottom-center':\n        return 'flex-row-reverse';\n      case 'bottom-right':\n      default:\n        return 'flex-col-reverse';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `fixed z-50 ${getPositionClasses()} ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex ${getExpandDirection()} items-center space-y-3`,\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isExpanded && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `flex ${getExpandDirection()} space-y-2`,\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          exit: {\n            opacity: 0,\n            scale: 0.8\n          },\n          transition: {\n            duration: 0.2\n          },\n          children: items.map((item, index) => {\n            // Séparateur\n            if (item.isSeparator) {\n              return /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: 20\n                },\n                transition: {\n                  delay: index * 0.05\n                },\n                className: \"w-8 h-px bg-gray-300 dark:bg-gray-600 mx-auto my-1\"\n              }, item.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 21\n              }, this);\n            }\n            const isActive = activeItem === item.id;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: 20\n              },\n              transition: {\n                delay: index * 0.05\n              },\n              children: item.isAction ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (item.action) item.action();\n                  setIsExpanded(false);\n                },\n                className: `group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${item.id === 'logout' ? 'bg-red-500 hover:bg-red-600 text-white hover:scale-105' : item.id === 'theme' ? 'bg-yellow-500 hover:bg-yellow-600 text-white hover:scale-105' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'}`,\n                title: `${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\",\n                  children: [item.label, item.shortcut && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-xs opacity-75\",\n                    children: [\"Ctrl+\", item.shortcut.toUpperCase()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: item.path,\n                className: `group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${isActive ? 'bg-primary-600 text-white scale-110' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'}`,\n                onClick: () => setIsExpanded(false),\n                title: `${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\",\n                  children: [item.label, item.shortcut && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-xs opacity-75\",\n                    children: [\"Ctrl+\", item.shortcut.toUpperCase()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 23\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n        onClick: () => setIsExpanded(!isExpanded),\n        className: `flex items-center justify-center w-14 h-14 rounded-full shadow-lg transition-all duration-200 ${isExpanded ? 'bg-red-500 hover:bg-red-600 text-white rotate-45' : 'bg-primary-600 hover:bg-primary-700 text-white hover:scale-105'}`,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        title: isExpanded ? 'Fermer le menu' : 'Ouvrir le menu de navigation',\n        children: isExpanded ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4 6h16M4 12h16M4 18h16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingNavigation, \"pscPBzHr3iHEy25M6rbgTEHpc7U=\", false, function () {\n  return [useLocation];\n});\n_c = FloatingNavigation;\nexport default FloatingNavigation;\nvar _c;\n$RefreshReg$(_c, \"FloatingNavigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "FloatingNavigation", "items", "position", "className", "_s", "location", "isExpanded", "setIsExpanded", "activeItem", "setActiveItem", "currentItem", "find", "item", "isSeparator", "isAction", "pathname", "path", "startsWith", "id", "handleKeyDown", "event", "ctrl<PERSON>ey", "metaKey", "shortcut", "key", "toLowerCase", "preventDefault", "action", "window", "href", "addEventListener", "removeEventListener", "getPositionClasses", "getExpandDirection", "children", "div", "initial", "opacity", "scale", "animate", "exit", "transition", "duration", "map", "index", "y", "delay", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isActive", "onClick", "title", "label", "toUpperCase", "icon", "to", "button", "whileHover", "whileTap", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/FloatingNavigation.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { getIconByCode } from './icons/NavigationIcons';\n\ninterface FloatingNavItem {\n  id: string;\n  label: string;\n  path: string;\n  icon: string;\n  shortcut?: string;\n  isSeparator?: boolean;\n  isAction?: boolean;\n  action?: () => void;\n}\n\ninterface FloatingNavigationProps {\n  items: FloatingNavItem[];\n  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';\n  className?: string;\n}\n\nconst FloatingNavigation: React.FC<FloatingNavigationProps> = ({\n  items,\n  position = 'bottom-right',\n  className = ''\n}) => {\n  const location = useLocation();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [activeItem, setActiveItem] = useState<string>('');\n\n  useEffect(() => {\n    // Déterminer l'élément actif basé sur l'URL actuelle\n    const currentItem = items.find(item =>\n      !item.isSeparator && !item.isAction &&\n      (location.pathname === item.path ||\n      (item.path !== '/' && item.path && location.pathname.startsWith(item.path)))\n    );\n    setActiveItem(currentItem?.id || '');\n  }, [location.pathname, items]);\n\n  // Gestion des raccourcis clavier\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.ctrlKey || event.metaKey) {\n        const item = items.find(item => item.shortcut === event.key.toLowerCase());\n        if (item) {\n          event.preventDefault();\n          if (item.isAction && item.action) {\n            item.action();\n          } else if (item.path) {\n            window.location.href = item.path;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [items]);\n\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'bottom-6 left-6';\n      case 'bottom-center':\n        return 'bottom-6 left-1/2 transform -translate-x-1/2';\n      case 'bottom-right':\n      default:\n        return 'bottom-6 right-6';\n    }\n  };\n\n  const getExpandDirection = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'flex-col-reverse';\n      case 'bottom-center':\n        return 'flex-row-reverse';\n      case 'bottom-right':\n      default:\n        return 'flex-col-reverse';\n    }\n  };\n\n  return (\n    <div className={`fixed z-50 ${getPositionClasses()} ${className}`}>\n      <div className={`flex ${getExpandDirection()} items-center space-y-3`}>\n        {/* Menu items */}\n        <AnimatePresence>\n          {isExpanded && (\n            <motion.div\n              className={`flex ${getExpandDirection()} space-y-2`}\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              transition={{ duration: 0.2 }}\n            >\n              {items.map((item, index) => {\n                // Séparateur\n                if (item.isSeparator) {\n                  return (\n                    <motion.div\n                      key={item.id}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: 20 }}\n                      transition={{ delay: index * 0.05 }}\n                      className=\"w-8 h-px bg-gray-300 dark:bg-gray-600 mx-auto my-1\"\n                    />\n                  );\n                }\n\n                const isActive = activeItem === item.id;\n\n                return (\n                  <motion.div\n                    key={item.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: 20 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    {item.isAction ? (\n                      <button\n                        onClick={() => {\n                          if (item.action) item.action();\n                          setIsExpanded(false);\n                        }}\n                        className={`group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${\n                          item.id === 'logout'\n                            ? 'bg-red-500 hover:bg-red-600 text-white hover:scale-105'\n                            : item.id === 'theme'\n                            ? 'bg-yellow-500 hover:bg-yellow-600 text-white hover:scale-105'\n                            : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'\n                        }`}\n                        title={`${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`}\n                      >\n                        <span className=\"text-lg\">{item.icon}</span>\n\n                        {/* Tooltip */}\n                        <div className=\"absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\">\n                          {item.label}\n                          {item.shortcut && (\n                            <span className=\"ml-2 text-xs opacity-75\">\n                              Ctrl+{item.shortcut.toUpperCase()}\n                            </span>\n                          )}\n                          <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700\"></div>\n                        </div>\n                      </button>\n                    ) : (\n                      <Link\n                        to={item.path}\n                        className={`group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${\n                          isActive\n                            ? 'bg-primary-600 text-white scale-110'\n                            : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'\n                        }`}\n                        onClick={() => setIsExpanded(false)}\n                        title={`${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`}\n                      >\n                        <span className=\"text-lg\">{item.icon}</span>\n\n                        {/* Tooltip */}\n                        <div className=\"absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\">\n                          {item.label}\n                          {item.shortcut && (\n                            <span className=\"ml-2 text-xs opacity-75\">\n                              Ctrl+{item.shortcut.toUpperCase()}\n                            </span>\n                          )}\n                          <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700\"></div>\n                        </div>\n                      </Link>\n                    )}\n                  </motion.div>\n                );\n              })}\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Toggle button */}\n        <motion.button\n          onClick={() => setIsExpanded(!isExpanded)}\n          className={`flex items-center justify-center w-14 h-14 rounded-full shadow-lg transition-all duration-200 ${\n            isExpanded\n              ? 'bg-red-500 hover:bg-red-600 text-white rotate-45'\n              : 'bg-primary-600 hover:bg-primary-700 text-white hover:scale-105'\n          }`}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          title={isExpanded ? 'Fermer le menu' : 'Ouvrir le menu de navigation'}\n        >\n          {isExpanded ? (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n          ) : (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          )}\n        </motion.button>\n      </div>\n    </div>\n  );\n};\n\nexport default FloatingNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBxD,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,KAAK;EACLC,QAAQ,GAAG,cAAc;EACzBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiB,WAAW,GAAGT,KAAK,CAACU,IAAI,CAACC,IAAI,IACjC,CAACA,IAAI,CAACC,WAAW,IAAI,CAACD,IAAI,CAACE,QAAQ,KAClCT,QAAQ,CAACU,QAAQ,KAAKH,IAAI,CAACI,IAAI,IAC/BJ,IAAI,CAACI,IAAI,KAAK,GAAG,IAAIJ,IAAI,CAACI,IAAI,IAAIX,QAAQ,CAACU,QAAQ,CAACE,UAAU,CAACL,IAAI,CAACI,IAAI,CAAE,CAC7E,CAAC;IACDP,aAAa,CAAC,CAAAC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,EAAE,KAAI,EAAE,CAAC;EACtC,CAAC,EAAE,CAACb,QAAQ,CAACU,QAAQ,EAAEd,KAAK,CAAC,CAAC;;EAE9B;EACAR,SAAS,CAAC,MAAM;IACd,MAAM0B,aAAa,GAAIC,KAAoB,IAAK;MAC9C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,EAAE;QAClC,MAAMV,IAAI,GAAGX,KAAK,CAACU,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACW,QAAQ,KAAKH,KAAK,CAACI,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;QAC1E,IAAIb,IAAI,EAAE;UACRQ,KAAK,CAACM,cAAc,CAAC,CAAC;UACtB,IAAId,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACe,MAAM,EAAE;YAChCf,IAAI,CAACe,MAAM,CAAC,CAAC;UACf,CAAC,MAAM,IAAIf,IAAI,CAACI,IAAI,EAAE;YACpBY,MAAM,CAACvB,QAAQ,CAACwB,IAAI,GAAGjB,IAAI,CAACI,IAAI;UAClC;QACF;MACF;IACF,CAAC;IAEDY,MAAM,CAACE,gBAAgB,CAAC,SAAS,EAAEX,aAAa,CAAC;IACjD,OAAO,MAAMS,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEZ,aAAa,CAAC;EACnE,CAAC,EAAE,CAAClB,KAAK,CAAC,CAAC;EAEX,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQ9B,QAAQ;MACd,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,eAAe;QAClB,OAAO,8CAA8C;MACvD,KAAK,cAAc;MACnB;QACE,OAAO,kBAAkB;IAC7B;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQ/B,QAAQ;MACd,KAAK,aAAa;QAChB,OAAO,kBAAkB;MAC3B,KAAK,eAAe;QAClB,OAAO,kBAAkB;MAC3B,KAAK,cAAc;MACnB;QACE,OAAO,kBAAkB;IAC7B;EACF,CAAC;EAED,oBACEH,OAAA;IAAKI,SAAS,EAAE,cAAc6B,kBAAkB,CAAC,CAAC,IAAI7B,SAAS,EAAG;IAAA+B,QAAA,eAChEnC,OAAA;MAAKI,SAAS,EAAE,QAAQ8B,kBAAkB,CAAC,CAAC,yBAA0B;MAAAC,QAAA,gBAEpEnC,OAAA,CAACF,eAAe;QAAAqC,QAAA,EACb5B,UAAU,iBACTP,OAAA,CAACH,MAAM,CAACuC,GAAG;UACThC,SAAS,EAAE,QAAQ8B,kBAAkB,CAAC,CAAC,YAAa;UACpDG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjCG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAR,QAAA,EAE7BjC,KAAK,CAAC0C,GAAG,CAAC,CAAC/B,IAAI,EAAEgC,KAAK,KAAK;YAC1B;YACA,IAAIhC,IAAI,CAACC,WAAW,EAAE;cACpB,oBACEd,OAAA,CAACH,MAAM,CAACuC,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEQ,CAAC,EAAE;gBAAG,CAAE;gBAC/BN,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEQ,CAAC,EAAE;gBAAE,CAAE;gBAC9BL,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEQ,CAAC,EAAE;gBAAG,CAAE;gBAC5BJ,UAAU,EAAE;kBAAEK,KAAK,EAAEF,KAAK,GAAG;gBAAK,CAAE;gBACpCzC,SAAS,EAAC;cAAoD,GALzDS,IAAI,CAACM,EAAE;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMb,CAAC;YAEN;YAEA,MAAMC,QAAQ,GAAG3C,UAAU,KAAKI,IAAI,CAACM,EAAE;YAEvC,oBACEnB,OAAA,CAACH,MAAM,CAACuC,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEQ,CAAC,EAAE;cAAG,CAAE;cAC/BN,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEQ,CAAC,EAAE;cAAE,CAAE;cAC9BL,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEQ,CAAC,EAAE;cAAG,CAAE;cAC5BJ,UAAU,EAAE;gBAAEK,KAAK,EAAEF,KAAK,GAAG;cAAK,CAAE;cAAAV,QAAA,EAEnCtB,IAAI,CAACE,QAAQ,gBACZf,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAIxC,IAAI,CAACe,MAAM,EAAEf,IAAI,CAACe,MAAM,CAAC,CAAC;kBAC9BpB,aAAa,CAAC,KAAK,CAAC;gBACtB,CAAE;gBACFJ,SAAS,EAAE,gHACTS,IAAI,CAACM,EAAE,KAAK,QAAQ,GAChB,wDAAwD,GACxDN,IAAI,CAACM,EAAE,KAAK,OAAO,GACnB,8DAA8D,GAC9D,0KAA0K,EAC7K;gBACHmC,KAAK,EAAE,GAAGzC,IAAI,CAAC0C,KAAK,GAAG1C,IAAI,CAACW,QAAQ,GAAG,UAAUX,IAAI,CAACW,QAAQ,CAACgC,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,EAAG;gBAAArB,QAAA,gBAEvFnC,OAAA;kBAAMI,SAAS,EAAC,SAAS;kBAAA+B,QAAA,EAAEtB,IAAI,CAAC4C;gBAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAG5CnD,OAAA;kBAAKI,SAAS,EAAC,uMAAuM;kBAAA+B,QAAA,GACnNtB,IAAI,CAAC0C,KAAK,EACV1C,IAAI,CAACW,QAAQ,iBACZxB,OAAA;oBAAMI,SAAS,EAAC,yBAAyB;oBAAA+B,QAAA,GAAC,OACnC,EAACtB,IAAI,CAACW,QAAQ,CAACgC,WAAW,CAAC,CAAC;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACP,eACDnD,OAAA;oBAAKI,SAAS,EAAC;kBAA4J;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/K,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,gBAETnD,OAAA,CAACL,IAAI;gBACH+D,EAAE,EAAE7C,IAAI,CAACI,IAAK;gBACdb,SAAS,EAAE,gHACTgD,QAAQ,GACJ,qCAAqC,GACrC,0KAA0K,EAC7K;gBACHC,OAAO,EAAEA,CAAA,KAAM7C,aAAa,CAAC,KAAK,CAAE;gBACpC8C,KAAK,EAAE,GAAGzC,IAAI,CAAC0C,KAAK,GAAG1C,IAAI,CAACW,QAAQ,GAAG,UAAUX,IAAI,CAACW,QAAQ,CAACgC,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,EAAG;gBAAArB,QAAA,gBAEvFnC,OAAA;kBAAMI,SAAS,EAAC,SAAS;kBAAA+B,QAAA,EAAEtB,IAAI,CAAC4C;gBAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAG5CnD,OAAA;kBAAKI,SAAS,EAAC,uMAAuM;kBAAA+B,QAAA,GACnNtB,IAAI,CAAC0C,KAAK,EACV1C,IAAI,CAACW,QAAQ,iBACZxB,OAAA;oBAAMI,SAAS,EAAC,yBAAyB;oBAAA+B,QAAA,GAAC,OACnC,EAACtB,IAAI,CAACW,QAAQ,CAACgC,WAAW,CAAC,CAAC;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACP,eACDnD,OAAA;oBAAKI,SAAS,EAAC;kBAA4J;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/K,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YACP,GA1DItC,IAAI,CAACM,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2DF,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBnD,OAAA,CAACH,MAAM,CAAC8D,MAAM;QACZN,OAAO,EAAEA,CAAA,KAAM7C,aAAa,CAAC,CAACD,UAAU,CAAE;QAC1CH,SAAS,EAAE,iGACTG,UAAU,GACN,kDAAkD,GAClD,gEAAgE,EACnE;QACHqD,UAAU,EAAE;UAAErB,KAAK,EAAE;QAAK,CAAE;QAC5BsB,QAAQ,EAAE;UAAEtB,KAAK,EAAE;QAAK,CAAE;QAC1Be,KAAK,EAAE/C,UAAU,GAAG,gBAAgB,GAAG,8BAA+B;QAAA4B,QAAA,EAErE5B,UAAU,gBACTP,OAAA;UAAKI,SAAS,EAAC,SAAS;UAAC0D,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAA7B,QAAA,eAC5EnC,OAAA;YAAMiE,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAA4B;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,gBAENnD,OAAA;UAAKI,SAAS,EAAC,SAAS;UAAC0D,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAA7B,QAAA,eAC5EnC,OAAA;YAAMiE,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAyB;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA1LIJ,kBAAqD;EAAA,QAKxCL,WAAW;AAAA;AAAAyE,EAAA,GALxBpE,kBAAqD;AA4L3D,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}