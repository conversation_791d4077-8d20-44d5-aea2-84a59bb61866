{"ast": null, "code": "export var LOG10EDocs = {\n  name: 'LOG10E',\n  category: 'Constants',\n  syntax: ['LOG10E'],\n  description: 'Returns the base-10 logarithm of E, approximately equal to 0.434',\n  examples: ['LOG10E', 'log(e, 10)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["LOG10EDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/LOG10E.js"], "sourcesContent": ["export var LOG10EDocs = {\n  name: 'LOG10E',\n  category: 'Constants',\n  syntax: ['LOG10E'],\n  description: 'Returns the base-10 logarithm of E, approximately equal to 0.434',\n  examples: ['LOG10E', 'log(e, 10)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EAClCC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}