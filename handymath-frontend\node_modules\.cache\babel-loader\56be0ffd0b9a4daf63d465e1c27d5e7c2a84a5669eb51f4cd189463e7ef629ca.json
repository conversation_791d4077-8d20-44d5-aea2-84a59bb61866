{"ast": null, "code": "export var LN10Docs = {\n  name: 'LN10',\n  category: 'Constants',\n  syntax: ['LN10'],\n  description: 'Returns the natural logarithm of 10, approximately equal to 2.302',\n  examples: ['LN10', 'log(10)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["LN10Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/LN10.js"], "sourcesContent": ["export var LN10Docs = {\n  name: 'LN10',\n  category: 'Constants',\n  syntax: ['LN10'],\n  description: 'Returns the natural logarithm of 10, approximately equal to 2.302',\n  examples: ['LN10', 'log(10)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,MAAM,CAAC;EAChBC,WAAW,EAAE,mEAAmE;EAChFC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;EAC7BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}