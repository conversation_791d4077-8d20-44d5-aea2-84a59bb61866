{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ForgotPasswordPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport SimpleHeader from '../components/SimpleHeader';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ForgotPasswordPage = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [message, setMessage] = useState(null);\n  const [error, setError] = useState(null);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [devResetLink, setDevResetLink] = useState(null);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!email) {\n      setError('Veuillez entrer votre adresse email');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    setMessage(null);\n    try {\n      const response = await api.post('/password-reset/', {\n        email\n      });\n      if (response.data.success) {\n        setMessage(response.data.message);\n        setIsSubmitted(true);\n\n        // En mode développement, afficher le lien directement\n        if (response.data.dev_reset_link) {\n          console.log('Lien de réinitialisation (développement):', response.data.dev_reset_link);\n          // Optionnel: rediriger automatiquement vers le lien\n          // window.location.href = response.data.dev_reset_link;\n        }\n      } else {\n        setError(response.data.error || 'Une erreur est survenue');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Erreur lors de la demande de réinitialisation:', err);\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.error) {\n        setError(err.response.data.error);\n      } else {\n        setError('Une erreur est survenue. Veuillez réessayer.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"Mot de passe oubli\\xE9\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white\",\n            children: \"Mot de passe oubli\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\",\n            children: \"Entrez votre adresse email pour recevoir un lien de r\\xE9initialisation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), !isSubmitted ? /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"mt-8 space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-md\",\n            initial: {\n              opacity: 0,\n              scale: 0.95\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-red-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n              children: \"Adresse email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              required: true,\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              className: \"appearance-none relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 focus:z-10 sm:text-sm\",\n              placeholder: \"<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 23\n                }, this), \"Envoi en cours...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 21\n              }, this) : 'Envoyer le lien de réinitialisation'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-sm text-primary-600 hover:text-primary-500 font-medium\",\n              children: \"\\u2190 Retour \\xE0 la connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"text-sm text-primary-600 hover:text-primary-500 font-medium\",\n              children: \"Cr\\xE9er un compte\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        /* Message de confirmation */\n        _jsxDEV(motion.div, {\n          className: \"text-center space-y-6\",\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-8 w-8 text-green-600 dark:text-green-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n              children: \"Email envoy\\xE9 !\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 dark:text-gray-500\",\n              children: \"V\\xE9rifiez \\xE9galement votre dossier spam si vous ne recevez pas l'email.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setIsSubmitted(false);\n                setEmail('');\n                setMessage(null);\n                setError(null);\n              },\n              className: \"w-full px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-500 border border-primary-600 hover:border-primary-500 rounded-md transition-colors\",\n              children: \"Renvoyer un email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"block w-full px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md transition-colors text-center\",\n              children: \"Retour \\xE0 la connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ForgotPasswordPage, \"h5UTTG+hZzi8+jb0WX2c+JbY5z8=\");\n_c = ForgotPasswordPage;\nexport default ForgotPasswordPage;\nvar _c;\n$RefreshReg$(_c, \"ForgotPasswordPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "motion", "api", "SimpleHeader", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ForgotPasswordPage", "_s", "email", "setEmail", "isLoading", "setIsLoading", "message", "setMessage", "error", "setError", "isSubmitted", "setIsSubmitted", "devResetLink", "setDevResetLink", "handleSubmit", "e", "preventDefault", "response", "post", "data", "success", "dev_reset_link", "console", "log", "err", "_err$response", "_err$response$data", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "div", "initial", "opacity", "y", "animate", "transition", "duration", "onSubmit", "scale", "viewBox", "fill", "fillRule", "d", "clipRule", "htmlFor", "id", "name", "type", "required", "value", "onChange", "target", "placeholder", "disabled", "xmlns", "cx", "cy", "r", "stroke", "strokeWidth", "to", "strokeLinecap", "strokeLinejoin", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/ForgotPasswordPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport SimpleHeader from '../components/SimpleHeader';\n\nconst ForgotPasswordPage: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [message, setMessage] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [devResetLink, setDevResetLink] = useState<string | null>(null);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email) {\n      setError('Veuillez entrer votre adresse email');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    setMessage(null);\n\n    try {\n      const response = await api.post('/password-reset/', { email });\n      \n      if (response.data.success) {\n        setMessage(response.data.message);\n        setIsSubmitted(true);\n\n        // En mode développement, afficher le lien directement\n        if (response.data.dev_reset_link) {\n          console.log('Lien de réinitialisation (développement):', response.data.dev_reset_link);\n          // Optionnel: rediriger automatiquement vers le lien\n          // window.location.href = response.data.dev_reset_link;\n        }\n      } else {\n        setError(response.data.error || 'Une erreur est survenue');\n      }\n    } catch (err: any) {\n      console.error('Erreur lors de la demande de réinitialisation:', err);\n      \n      if (err.response?.data?.error) {\n        setError(err.response.data.error);\n      } else {\n        setError('Une erreur est survenue. Veuillez réessayer.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <SimpleHeader title=\"Mot de passe oublié\" />\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          {/* En-tête */}\n          <div>\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white\">\n              Mot de passe oublié\n            </h2>\n            <p className=\"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\">\n              Entrez votre adresse email pour recevoir un lien de réinitialisation\n            </p>\n          </div>\n\n          {!isSubmitted ? (\n            <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n              {/* Messages d'erreur */}\n              {error && (\n                <motion.div\n                  className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-md\"\n                  initial={{ opacity: 0, scale: 0.95 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm\">{error}</p>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Champ email */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Adresse email\n                </label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"appearance-none relative block w-full px-3 py-3 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 focus:z-10 sm:text-sm\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              {/* Bouton de soumission */}\n              <div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {isLoading ? (\n                    <div className=\"flex items-center\">\n                      <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Envoi en cours...\n                    </div>\n                  ) : (\n                    'Envoyer le lien de réinitialisation'\n                  )}\n                </button>\n              </div>\n\n              {/* Liens */}\n              <div className=\"flex items-center justify-between\">\n                <Link\n                  to=\"/login\"\n                  className=\"text-sm text-primary-600 hover:text-primary-500 font-medium\"\n                >\n                  ← Retour à la connexion\n                </Link>\n                <Link\n                  to=\"/register\"\n                  className=\"text-sm text-primary-600 hover:text-primary-500 font-medium\"\n                >\n                  Créer un compte\n                </Link>\n              </div>\n            </form>\n          ) : (\n            /* Message de confirmation */\n            <motion.div\n              className=\"text-center space-y-6\"\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              {/* Icône de succès */}\n              <div className=\"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20\">\n                <svg className=\"h-8 w-8 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n              </div>\n\n              {/* Message */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  Email envoyé !\n                </h3>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  {message}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-500\">\n                  Vérifiez également votre dossier spam si vous ne recevez pas l'email.\n                </p>\n              </div>\n\n              {/* Actions */}\n              <div className=\"space-y-3\">\n                <button\n                  onClick={() => {\n                    setIsSubmitted(false);\n                    setEmail('');\n                    setMessage(null);\n                    setError(null);\n                  }}\n                  className=\"w-full px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-500 border border-primary-600 hover:border-primary-500 rounded-md transition-colors\"\n                >\n                  Renvoyer un email\n                </button>\n                <Link\n                  to=\"/login\"\n                  className=\"block w-full px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md transition-colors text-center\"\n                >\n                  Retour à la connexion\n                </Link>\n              </div>\n            </motion.div>\n          )}\n        </motion.div>\n      </div>\n    </>\n  );\n};\n\nexport default ForgotPasswordPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EAErE,MAAMuB,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACd,KAAK,EAAE;MACVO,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;IAEAJ,YAAY,CAAC,IAAI,CAAC;IAClBI,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMvB,GAAG,CAACwB,IAAI,CAAC,kBAAkB,EAAE;QAAEhB;MAAM,CAAC,CAAC;MAE9D,IAAIe,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBb,UAAU,CAACU,QAAQ,CAACE,IAAI,CAACb,OAAO,CAAC;QACjCK,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACA,IAAIM,QAAQ,CAACE,IAAI,CAACE,cAAc,EAAE;UAChCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEN,QAAQ,CAACE,IAAI,CAACE,cAAc,CAAC;UACtF;UACA;QACF;MACF,CAAC,MAAM;QACLZ,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACX,KAAK,IAAI,yBAAyB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOgB,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBJ,OAAO,CAACd,KAAK,CAAC,gDAAgD,EAAEgB,GAAG,CAAC;MAEpE,KAAAC,aAAA,GAAID,GAAG,CAACP,QAAQ,cAAAQ,aAAA,gBAAAC,kBAAA,GAAZD,aAAA,CAAcN,IAAI,cAAAO,kBAAA,eAAlBA,kBAAA,CAAoBlB,KAAK,EAAE;QAC7BC,QAAQ,CAACe,GAAG,CAACP,QAAQ,CAACE,IAAI,CAACX,KAAK,CAAC;MACnC,CAAC,MAAM;QACLC,QAAQ,CAAC,8CAA8C,CAAC;MAC1D;IACF,CAAC,SAAS;MACRJ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACER,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACE9B,OAAA,CAACF,YAAY;MAACiC,KAAK,EAAC;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5CnC,OAAA;MAAKoC,SAAS,EAAC,sGAAsG;MAAAN,QAAA,eACnH9B,OAAA,CAACJ,MAAM,CAACyC,GAAG;QACTD,SAAS,EAAC,8EAA8E;QACxFE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAG9B9B,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAIoC,SAAS,EAAC,wEAAwE;YAAAN,QAAA,EAAC;UAEvF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnC,OAAA;YAAGoC,SAAS,EAAC,2DAA2D;YAAAN,QAAA,EAAC;UAEzE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAEL,CAACtB,WAAW,gBACXb,OAAA;UAAMoC,SAAS,EAAC,gBAAgB;UAACQ,QAAQ,EAAE3B,YAAa;UAAAa,QAAA,GAErDnB,KAAK,iBACJX,OAAA,CAACJ,MAAM,CAACyC,GAAG;YACTD,SAAS,EAAC,4HAA4H;YACtIE,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAK,CAAE;YACrCJ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,eAE9B9B,OAAA;cAAKoC,SAAS,EAAC,MAAM;cAAAN,QAAA,gBACnB9B,OAAA;gBAAKoC,SAAS,EAAC,eAAe;gBAAAN,QAAA,eAC5B9B,OAAA;kBAAKoC,SAAS,EAAC,sBAAsB;kBAACU,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAjB,QAAA,eAC3E9B,OAAA;oBAAMgD,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,yNAAyN;oBAACC,QAAQ,EAAC;kBAAS;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnC,OAAA;gBAAKoC,SAAS,EAAC,MAAM;gBAAAN,QAAA,eACnB9B,OAAA;kBAAGoC,SAAS,EAAC,SAAS;kBAAAN,QAAA,EAAEnB;gBAAK;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAGDnC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAOmD,OAAO,EAAC,OAAO;cAACf,SAAS,EAAC,iEAAiE;cAAAN,QAAA,EAAC;YAEnG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEoD,EAAE,EAAC,OAAO;cACVC,IAAI,EAAC,OAAO;cACZC,IAAI,EAAC,OAAO;cACZC,QAAQ;cACRC,KAAK,EAAEnD,KAAM;cACboD,QAAQ,EAAGvC,CAAC,IAAKZ,QAAQ,CAACY,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;cAC1CpB,SAAS,EAAC,+RAA+R;cACzSuB,WAAW,EAAC;YAAiB;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnC,OAAA;YAAA8B,QAAA,eACE9B,OAAA;cACEsD,IAAI,EAAC,QAAQ;cACbM,QAAQ,EAAErD,SAAU;cACpB6B,SAAS,EAAC,0SAA0S;cAAAN,QAAA,EAEnTvB,SAAS,gBACRP,OAAA;gBAAKoC,SAAS,EAAC,mBAAmB;gBAAAN,QAAA,gBAChC9B,OAAA;kBAAKoC,SAAS,EAAC,4CAA4C;kBAACyB,KAAK,EAAC,4BAA4B;kBAACd,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAAAhB,QAAA,gBAC5H9B,OAAA;oBAAQoC,SAAS,EAAC,YAAY;oBAAC0B,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACrGnC,OAAA;oBAAMoC,SAAS,EAAC,YAAY;oBAACW,IAAI,EAAC,cAAc;oBAACE,CAAC,EAAC;kBAAiH;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzK,CAAC,qBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNnC,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAN,QAAA,gBAChD9B,OAAA,CAACL,IAAI;cACHwE,EAAE,EAAC,QAAQ;cACX/B,SAAS,EAAC,6DAA6D;cAAAN,QAAA,EACxE;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnC,OAAA,CAACL,IAAI;cACHwE,EAAE,EAAC,WAAW;cACd/B,SAAS,EAAC,6DAA6D;cAAAN,QAAA,EACxE;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;QAAA;QAEP;QACAnC,OAAA,CAACJ,MAAM,CAACyC,GAAG;UACTD,SAAS,EAAC,uBAAuB;UACjCE,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,KAAK,EAAE;UAAK,CAAE;UACrCJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,KAAK,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAb,QAAA,gBAG9B9B,OAAA;YAAKoC,SAAS,EAAC,mGAAmG;YAAAN,QAAA,eAChH9B,OAAA;cAAKoC,SAAS,EAAC,4CAA4C;cAACW,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAACnB,OAAO,EAAC,WAAW;cAAAhB,QAAA,eAC/G9B,OAAA;gBAAMoE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACH,WAAW,EAAC,GAAG;gBAACjB,CAAC,EAAC;cAAsG;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAIoC,SAAS,EAAC,wDAAwD;cAAAN,QAAA,EAAC;YAEvE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnC,OAAA;cAAGoC,SAAS,EAAC,+CAA+C;cAAAN,QAAA,EACzDrB;YAAO;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACJnC,OAAA;cAAGoC,SAAS,EAAC,0CAA0C;cAAAN,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNnC,OAAA;YAAKoC,SAAS,EAAC,WAAW;YAAAN,QAAA,gBACxB9B,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAM;gBACbxD,cAAc,CAAC,KAAK,CAAC;gBACrBR,QAAQ,CAAC,EAAE,CAAC;gBACZI,UAAU,CAAC,IAAI,CAAC;gBAChBE,QAAQ,CAAC,IAAI,CAAC;cAChB,CAAE;cACFwB,SAAS,EAAC,8JAA8J;cAAAN,QAAA,EACzK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnC,OAAA,CAACL,IAAI;cACHwE,EAAE,EAAC,QAAQ;cACX/B,SAAS,EAAC,+MAA+M;cAAAN,QAAA,EAC1N;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC/B,EAAA,CAxMID,kBAA4B;AAAAoE,EAAA,GAA5BpE,kBAA4B;AA0MlC,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}