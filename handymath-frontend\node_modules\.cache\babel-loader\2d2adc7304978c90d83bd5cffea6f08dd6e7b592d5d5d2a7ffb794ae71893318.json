{"ast": null, "code": "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function (global, module, define) {\n  function XorGen(seed) {\n    var me = this,\n      strseed = '';\n\n    // Set up generator function.\n    me.next = function () {\n      var t = me.x ^ me.x >>> 2;\n      me.x = me.y;\n      me.y = me.z;\n      me.z = me.w;\n      me.w = me.v;\n      return (me.d = me.d + 362437 | 0) + (me.v = me.v ^ me.v << 4 ^ (t ^ t << 1)) | 0;\n    };\n    me.x = 0;\n    me.y = 0;\n    me.z = 0;\n    me.w = 0;\n    me.v = 0;\n    if (seed === (seed | 0)) {\n      // Integer seed.\n      me.x = seed;\n    } else {\n      // String seed.\n      strseed += seed;\n    }\n\n    // Mix in string seed, then discard an initial batch of 64 values.\n    for (var k = 0; k < strseed.length + 64; k++) {\n      me.x ^= strseed.charCodeAt(k) | 0;\n      if (k == strseed.length) {\n        me.d = me.x << 10 ^ me.x >>> 4;\n      }\n      me.next();\n    }\n  }\n  function copy(f, t) {\n    t.x = f.x;\n    t.y = f.y;\n    t.z = f.z;\n    t.w = f.w;\n    t.v = f.v;\n    t.d = f.d;\n    return t;\n  }\n  function impl(seed, opts) {\n    var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function () {\n        return (xg.next() >>> 0) / 0x100000000;\n      };\n    prng.double = function () {\n      do {\n        var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n      } while (result === 0);\n      return result;\n    };\n    prng.int32 = xg.next;\n    prng.quick = prng;\n    if (state) {\n      if (typeof state == 'object') copy(state, xg);\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n    return prng;\n  }\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.xorwow = impl;\n  }\n})(this, typeof module == 'object' && module,\n// present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "XorGen", "seed", "me", "strseed", "next", "t", "x", "y", "z", "w", "v", "d", "k", "length", "charCodeAt", "copy", "f", "impl", "opts", "xg", "state", "prng", "double", "top", "bot", "result", "int32", "quick", "exports", "amd", "xorwow"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/seedrandom/lib/xorwow.js"], "sourcesContent": ["// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n"], "mappings": "AAAA;AACA;;AAEA,CAAC,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAElC,SAASC,MAAMA,CAACC,IAAI,EAAE;IACpB,IAAIC,EAAE,GAAG,IAAI;MAAEC,OAAO,GAAG,EAAE;;IAE3B;IACAD,EAAE,CAACE,IAAI,GAAG,YAAW;MACnB,IAAIC,CAAC,GAAIH,EAAE,CAACI,CAAC,GAAIJ,EAAE,CAACI,CAAC,KAAK,CAAG;MAC7BJ,EAAE,CAACI,CAAC,GAAGJ,EAAE,CAACK,CAAC;MAAEL,EAAE,CAACK,CAAC,GAAGL,EAAE,CAACM,CAAC;MAAEN,EAAE,CAACM,CAAC,GAAGN,EAAE,CAACO,CAAC;MAAEP,EAAE,CAACO,CAAC,GAAGP,EAAE,CAACQ,CAAC;MAClD,OAAO,CAACR,EAAE,CAACS,CAAC,GAAIT,EAAE,CAACS,CAAC,GAAG,MAAM,GAAG,CAAE,KAC9BT,EAAE,CAACQ,CAAC,GAAIR,EAAE,CAACQ,CAAC,GAAIR,EAAE,CAACQ,CAAC,IAAI,CAAE,IAAKL,CAAC,GAAIA,CAAC,IAAI,CAAE,CAAC,CAAC,GAAG,CAAC;IACvD,CAAC;IAEDH,EAAE,CAACI,CAAC,GAAG,CAAC;IACRJ,EAAE,CAACK,CAAC,GAAG,CAAC;IACRL,EAAE,CAACM,CAAC,GAAG,CAAC;IACRN,EAAE,CAACO,CAAC,GAAG,CAAC;IACRP,EAAE,CAACQ,CAAC,GAAG,CAAC;IAER,IAAIT,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;MACvB;MACAC,EAAE,CAACI,CAAC,GAAGL,IAAI;IACb,CAAC,MAAM;MACL;MACAE,OAAO,IAAIF,IAAI;IACjB;;IAEA;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,OAAO,CAACU,MAAM,GAAG,EAAE,EAAED,CAAC,EAAE,EAAE;MAC5CV,EAAE,CAACI,CAAC,IAAIH,OAAO,CAACW,UAAU,CAACF,CAAC,CAAC,GAAG,CAAC;MACjC,IAAIA,CAAC,IAAIT,OAAO,CAACU,MAAM,EAAE;QACvBX,EAAE,CAACS,CAAC,GAAGT,EAAE,CAACI,CAAC,IAAI,EAAE,GAAGJ,EAAE,CAACI,CAAC,KAAK,CAAC;MAChC;MACAJ,EAAE,CAACE,IAAI,CAAC,CAAC;IACX;EACF;EAEA,SAASW,IAAIA,CAACC,CAAC,EAAEX,CAAC,EAAE;IAClBA,CAAC,CAACC,CAAC,GAAGU,CAAC,CAACV,CAAC;IACTD,CAAC,CAACE,CAAC,GAAGS,CAAC,CAACT,CAAC;IACTF,CAAC,CAACG,CAAC,GAAGQ,CAAC,CAACR,CAAC;IACTH,CAAC,CAACI,CAAC,GAAGO,CAAC,CAACP,CAAC;IACTJ,CAAC,CAACK,CAAC,GAAGM,CAAC,CAACN,CAAC;IACTL,CAAC,CAACM,CAAC,GAAGK,CAAC,CAACL,CAAC;IACT,OAAON,CAAC;EACV;EAEA,SAASY,IAAIA,CAAChB,IAAI,EAAEiB,IAAI,EAAE;IACxB,IAAIC,EAAE,GAAG,IAAInB,MAAM,CAACC,IAAI,CAAC;MACrBmB,KAAK,GAAGF,IAAI,IAAIA,IAAI,CAACE,KAAK;MAC1BC,IAAI,GAAG,SAAAA,CAAA,EAAW;QAAE,OAAO,CAACF,EAAE,CAACf,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;MAAE,CAAC;IACjEiB,IAAI,CAACC,MAAM,GAAG,YAAW;MACvB,GAAG;QACD,IAAIC,GAAG,GAAGJ,EAAE,CAACf,IAAI,CAAC,CAAC,KAAK,EAAE;UACtBoB,GAAG,GAAG,CAACL,EAAE,CAACf,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;UACrCqB,MAAM,GAAG,CAACF,GAAG,GAAGC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;MACtC,CAAC,QAAQC,MAAM,KAAK,CAAC;MACrB,OAAOA,MAAM;IACf,CAAC;IACDJ,IAAI,CAACK,KAAK,GAAGP,EAAE,CAACf,IAAI;IACpBiB,IAAI,CAACM,KAAK,GAAGN,IAAI;IACjB,IAAID,KAAK,EAAE;MACT,IAAI,OAAOA,KAAM,IAAI,QAAQ,EAAEL,IAAI,CAACK,KAAK,EAAED,EAAE,CAAC;MAC9CE,IAAI,CAACD,KAAK,GAAG,YAAW;QAAE,OAAOL,IAAI,CAACI,EAAE,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC;IAClD;IACA,OAAOE,IAAI;EACb;EAEA,IAAIvB,MAAM,IAAIA,MAAM,CAAC8B,OAAO,EAAE;IAC5B9B,MAAM,CAAC8B,OAAO,GAAGX,IAAI;EACvB,CAAC,MAAM,IAAIlB,MAAM,IAAIA,MAAM,CAAC8B,GAAG,EAAE;IAC/B9B,MAAM,CAAC,YAAW;MAAE,OAAOkB,IAAI;IAAE,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAI,CAACa,MAAM,GAAGb,IAAI;EACpB;AAEA,CAAC,EACC,IAAI,EACH,OAAOnB,MAAM,IAAK,QAAQ,IAAIA,MAAM;AAAK;AACzC,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAAG;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}