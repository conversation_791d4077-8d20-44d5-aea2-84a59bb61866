{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createReducedPlanckConstant } from '../../factoriesAny.js';\nexport var reducedPlanckConstantDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createReducedPlanckConstant\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createReducedPlanckConstant", "reducedPlanckConstantDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesReducedPlanckConstant.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createReducedPlanckConstant } from '../../factoriesAny.js';\nexport var reducedPlanckConstantDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createReducedPlanckConstant\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,2BAA2B,QAAQ,uBAAuB;AACnE,OAAO,IAAIC,iCAAiC,GAAG;EAC7CH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}