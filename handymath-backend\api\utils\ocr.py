import cv2
import pytesseract
from PIL import Image, ImageEnhance
import numpy as np
import re
import base64
import io

def preprocess_image(image, target_size=None, grayscale=True, enhance_contrast=True):
    """
    Prétraite l'image pour améliorer la reconnaissance OCR

    Args:
        image: PIL Image object ou chemin vers l'image
        target_size: tuple (width, height) pour redimensionner
        grayscale: bool, convertir en niveaux de gris
        enhance_contrast: bool, améliorer le contraste

    Returns:
        PIL Image object préprocessée
    """
    # Si c'est un chemin, charger l'image
    if isinstance(image, str):
        image = Image.open(image)

    # Redimensionner si nécessaire
    if target_size:
        image = image.resize(target_size, Image.Resampling.LANCZOS)

    # Convertir en niveaux de gris
    if grayscale and image.mode != 'L':
        image = image.convert('L')

    # Améliorer le contraste
    if enhance_contrast:
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(2.0)  # Augmenter le contraste

    return image

def preprocess_image_opencv(image_path_or_pil):
    """
    Prétraite l'image avec OpenCV pour améliorer la reconnaissance OCR
    """
    try:
        # Si c'est une image PIL, la convertir
        if hasattr(image_path_or_pil, 'save'):  # PIL Image
            img_array = np.array(image_path_or_pil)
            if len(img_array.shape) == 3:
                img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            else:
                img = img_array
        else:
            # Charger l'image depuis le chemin
            img = cv2.imread(image_path_or_pil)

        # Convertir en niveaux de gris
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img

        # Appliquer un flou gaussien pour réduire le bruit
        blur = cv2.GaussianBlur(gray, (5, 5), 0)

        # Appliquer un seuillage adaptatif
        thresh = cv2.adaptiveThreshold(blur, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                      cv2.THRESH_BINARY, 11, 2)

        # Morphologie pour nettoyer l'image
        kernel = np.ones((2, 2), np.uint8)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # Reconvertir en PIL Image
        return Image.fromarray(thresh)

    except Exception as e:
        print(f"Erreur de préprocessing OpenCV: {e}")
        # Retourner l'image originale en cas d'erreur
        if hasattr(image_path_or_pil, 'save'):
            return image_path_or_pil
        else:
            return Image.open(image_path_or_pil)

def recognize_equation(image_path):
    """
    Reconnaît une équation mathématique à partir d'une image (legacy function)
    """
    try:
        # Prétraiter l'image avec OpenCV
        processed_img = preprocess_image_opencv(image_path)

        # Configuration de pytesseract pour les équations mathématiques
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '

        # Reconnaître le texte
        equation_text = pytesseract.image_to_string(processed_img, config=custom_config)

        # Nettoyer le texte reconnu
        equation_text = clean_ocr_text(equation_text)

        return equation_text

    except Exception as e:
        print(f"Erreur dans recognize_equation: {e}")
        return ""

def extract_text_from_image(image, clean_text=False):
    """
    Extrait le texte d'une image PIL avec pytesseract

    Args:
        image: PIL Image object
        clean_text: bool, nettoyer le texte extrait

    Returns:
        str: texte extrait
    """
    try:
        # Configuration optimisée pour les mathématiques
        config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '

        # Extraire le texte
        text = pytesseract.image_to_string(image, config=config)

        # Nettoyer si demandé
        if clean_text:
            text = clean_ocr_text(text)

        return text.strip()

    except Exception as e:
        print(f"Erreur dans extract_text_from_image: {e}")
        return ""

def clean_ocr_text(text):
    """
    Nettoie et corrige le texte OCR pour les équations mathématiques
    """
    if not text:
        return ""

    # Supprimer les espaces multiples et les caractères indésirables
    cleaned = re.sub(r'\s+', ' ', text.strip())

    # Corrections communes d'OCR pour les mathématiques
    corrections = {
        # Corrections de caractères numériques
        'O': '0',  # O majuscule -> 0
        'o': '0',  # o minuscule -> 0 (dans certains contextes)
        'l': '1',  # l minuscule -> 1 (dans certains contextes)
        'I': '1',  # I majuscule -> 1
        'S': '5',  # S -> 5 (dans certains contextes)
        'Z': '2',  # Z -> 2 (dans certains contextes)

        # Corrections d'opérateurs
        '—': '-',  # tiret long -> moins
        '–': '-',  # tiret moyen -> moins
        '×': '*',  # multiplication -> *
        '÷': '/',  # division -> /

        # Corrections de parenthèses
        '[': '(',
        ']': ')',
        '{': '(',
        '}': ')',
    }

    # Appliquer les corrections
    for wrong, correct in corrections.items():
        cleaned = cleaned.replace(wrong, correct)

    # Corrections spécifiques pour les puissances (problème principal identifié)
    # Corriger les erreurs de reconnaissance de ^
    cleaned = re.sub(r'(\w)42(\s*=)', r'\1^2\2', cleaned)  # x42= -> x^2=
    cleaned = re.sub(r'(\w)43(\s*=)', r'\1^3\2', cleaned)  # x43= -> x^3=
    cleaned = re.sub(r'(\w)44(\s*=)', r'\1^4\2', cleaned)  # x44= -> x^4=
    cleaned = re.sub(r'(\w)2(\s*[+\-=])', r'\1^2\2', cleaned)  # x2+ -> x^2+
    cleaned = re.sub(r'(\w)3(\s*[+\-=])', r'\1^3\2', cleaned)  # x3+ -> x^3+

    # Corrections pour les caractères mal reconnus
    cleaned = re.sub(r'(\w)A2', r'\1^2', cleaned)  # xA2 -> x^2
    cleaned = re.sub(r'(\w)A3', r'\1^3', cleaned)  # xA3 -> x^3

    # Supprimer les caractères non mathématiques (en gardant ^)
    cleaned = re.sub(r'[^0-9a-zA-Z+\-*/=^()., ]', '', cleaned)

    # Supprimer les espaces en début/fin
    cleaned = cleaned.strip()

    return cleaned

def normalize_equation_format(text):
    """
    Normalise le format d'une équation
    """
    if not text:
        return ""

    # Supprimer les espaces autour des opérateurs
    text = re.sub(r'\s*([+\-*/=^])\s*', r'\1', text)

    # Ajouter des espaces autour des opérateurs pour la lisibilité
    text = re.sub(r'([+\-*/=])', r' \1 ', text)

    # Supprimer les espaces multiples
    text = re.sub(r'\s+', ' ', text)

    return text.strip()

def validate_image(image, min_size=(50, 25), max_size=(2000, 2000)):
    """
    Valide une image PIL

    Args:
        image: PIL Image object
        min_size: tuple (width, height) taille minimale
        max_size: tuple (width, height) taille maximale

    Returns:
        bool: True si l'image est valide
    """
    try:
        # Vérifier le format
        if hasattr(image, 'format'):
            valid_formats = ['JPEG', 'PNG', 'BMP', 'TIFF']
            if image.format not in valid_formats:
                return False

        # Vérifier la taille
        width, height = image.size

        if width < min_size[0] or height < min_size[1]:
            return False

        if width > max_size[0] or height > max_size[1]:
            return False

        return True

    except Exception:
        return False

def process_image_ocr(image_data, preprocess=True, enhance_contrast=False, grayscale=False):
    """
    Traite une image encodée en base64 et extrait le texte avec OCR

    Args:
        image_data: str, données image en base64 (avec ou sans préfixe data:image)
        preprocess: bool, appliquer le préprocessing
        enhance_contrast: bool, améliorer le contraste
        grayscale: bool, convertir en niveaux de gris

    Returns:
        dict: {'text': str, 'confidence': float}
    """
    try:
        # Nettoyer les données base64
        if image_data.startswith('data:image'):
            # Supprimer le préfixe data:image/...;base64,
            image_data = image_data.split(',', 1)[1]

        # Décoder l'image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))

        # Valider l'image
        if not validate_image(image):
            raise ValueError("Image invalide ou format non supporté")

        # Préprocessing si demandé
        if preprocess:
            image = preprocess_image(image,
                                   grayscale=grayscale,
                                   enhance_contrast=enhance_contrast)

        # Extraire le texte
        text = extract_text_from_image(image, clean_text=True)

        # Calculer une confiance approximative basée sur la longueur et la présence d'équation
        confidence = 0.5  # Confiance de base

        if '=' in text and len(text) > 3:
            confidence = 0.9
        elif len(text) > 5:
            confidence = 0.7
        elif len(text) > 0:
            confidence = 0.3
        else:
            confidence = 0.1

        return {
            'text': text,
            'confidence': confidence
        }

    except Exception as e:
        raise Exception(f"Erreur lors du traitement OCR: {str(e)}")