{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CourseDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentLayout from '../components/StudentLayout';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CourseDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const [course, setCourse] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n  useEffect(() => {\n    if (user && id) {\n      fetchCourse();\n    }\n  }, [user, id]);\n  const fetchCourse = async () => {\n    try {\n      setLoading(true);\n\n      // Appel API réel au lieu du contenu codé en dur\n      const response = await fetch(`http://127.0.0.1:8000/api/courses/${id}/`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const courseData = await response.json();\n      setCourse(courseData);\n      setLoading(false);\n    } catch (error) {\n      console.error('Erreur lors de la récupération du cours:', error);\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async () => {\n    if (!course) return;\n    try {\n      setEnrolling(true);\n      setTimeout(() => {\n        setCourse({\n          ...course,\n          is_enrolled: true\n        });\n        setEnrolling(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      setEnrolling(false);\n    }\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s restreint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der \\xE0 ce cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement du cours...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this);\n  }\n  if (!course) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours non trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Le cours demand\\xE9 n'existe pas ou n'est plus disponible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/courses'),\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Retour aux cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StudentLayout, {\n    title: course.title,\n    subtitle: course.description,\n    actions: /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => navigate('/courses'),\n      className: \"flex items-center text-primary-600 hover:text-primary-700 transition-colors\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mr-2\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), \"Retour aux cours\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-4xl mr-4\",\n                children: course.thumbnail\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`,\n                  children: course.level_display\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 dark:text-gray-300 mb-6\",\n            children: course.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Chapitres:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), \" \", course.chapters.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Le\\xE7ons:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this), \" \", course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: \"\\u23F1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Dur\\xE9e:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), \" \", formatDuration(course.estimated_duration)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Progression:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this), \" \", course.progress_percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 11\n          }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"Progression du cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.progress_percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary-600 h-3 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${course.progress_percentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), !course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleEnroll,\n              disabled: enrolling,\n              className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\",\n              children: enrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), \"Inscription...\"]\n              }, void 0, true) : 'S\\'inscrire au cours'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 9\n        }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: course.chapters.map((chapter, chapterIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: (chapterIndex + 1) * 0.1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                children: [\"Chapitre \", chapter.order, \": \", chapter.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: [chapter.progress_percentage, \"% termin\\xE9\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), chapter.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 dark:text-gray-300 mb-4\",\n              children: chapter.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${chapter.progress_percentage}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: chapter.lessons.map(lesson => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center justify-between p-3 rounded-lg border ${lesson.is_completed ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700' : lesson.is_accessible ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600' : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'} transition-colors`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xl mr-3\",\n                    children: lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 dark:text-white\",\n                      children: lesson.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-600 dark:text-gray-400\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-3\",\n                        children: lesson.type_display\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-3\",\n                        children: [\"\\u23F1\", lesson.estimated_duration, \"min\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 29\n                      }, this), lesson.time_spent > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [Math.floor(lesson.time_spent / 60), \"min pass\\xE9es\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: lesson.is_accessible ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/lessons/${lesson.id}`),\n                      className: \"bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\",\n                      children: lesson.is_completed ? 'Revoir' : 'Commencer'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 29\n                    }, this), lesson.exercise_id && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/exercises/${lesson.exercise_id}`),\n                      className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\",\n                      children: \"Exercice\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                    children: \"Terminez les le\\xE7ons pr\\xE9c\\xE9dentes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)]\n              }, lesson.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)]\n          }, chapter.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseDetailPage, \"Q6K2/JgYtpoHjM9R+Nhzj5R3tDQ=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = CourseDetailPage;\nexport default CourseDetailPage;\nvar _c;\n$RefreshReg$(_c, \"CourseDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "useAuth", "StudentLayout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CourseDetailPage", "_s", "id", "navigate", "user", "course", "setCourse", "loading", "setLoading", "enrolling", "setEnrolling", "fetchCourse", "response", "fetch", "headers", "localStorage", "getItem", "ok", "Error", "status", "courseData", "json", "error", "console", "handleEnroll", "setTimeout", "is_enrolled", "getLevelColor", "level", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "title", "subtitle", "description", "actions", "div", "initial", "opacity", "y", "animate", "thumbnail", "level_display", "chapters", "length", "reduce", "total", "chapter", "lessons", "estimated_duration", "progress_percentage", "style", "width", "disabled", "map", "chapterIndex", "transition", "delay", "order", "lesson", "is_completed", "is_accessible", "type_display", "time_spent", "exercise_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/CourseDetailPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentLayout from '../components/StudentLayout';\n\ninterface Lesson {\n  id: number;\n  title: string;\n  lesson_type: string;\n  type_display: string;\n  order: number;\n  estimated_duration: number;\n  is_accessible: boolean;\n  is_completed: boolean;\n  time_spent: number;\n  exercise_id?: number;\n}\n\ninterface Chapter {\n  id: number;\n  title: string;\n  description: string;\n  order: number;\n  progress_percentage: number;\n  lessons: Lesson[];\n}\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  progress_percentage: number;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  chapters: Chapter[];\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\nconst CourseDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const [course, setCourse] = useState<Course | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n\n  useEffect(() => {\n    if (user && id) {\n      fetchCourse();\n    }\n  }, [user, id]);\n\n  const fetchCourse = async () => {\n    try {\n      setLoading(true);\n\n      // Appel API réel au lieu du contenu codé en dur\n      const response = await fetch(`http://127.0.0.1:8000/api/courses/${id}/`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const courseData = await response.json();\n      setCourse(courseData);\n      setLoading(false);\n    } catch (error) {\n      console.error('Erreur lors de la récupération du cours:', error);\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async () => {\n    if (!course) return;\n    \n    try {\n      setEnrolling(true);\n      setTimeout(() => {\n        setCourse({ ...course, is_enrolled: true });\n        setEnrolling(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      setEnrolling(false);\n    }\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès restreint</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder à ce cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement du cours...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!course) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours non trouvé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Le cours demandé n'existe pas ou n'est plus disponible.\n          </p>\n          <button\n            onClick={() => navigate('/courses')}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Retour aux cours\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <StudentLayout\n      title={course.title}\n      subtitle={course.description}\n      actions={\n        <button\n          onClick={() => navigate('/courses')}\n          className=\"flex items-center text-primary-600 hover:text-primary-700 transition-colors\"\n        >\n          <span className=\"mr-2\">←</span>\n          Retour aux cours\n        </button>\n      }\n    >\n      <div className=\"space-y-6\">\n        <div className=\"max-w-4xl mx-auto\">\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-4xl mr-4\">{course.thumbnail}</span>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  {course.title}\n                </h1>\n                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`}>\n                  {course.level_display}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <p className=\"text-gray-700 dark:text-gray-300 mb-6\">\n            {course.description}\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6\">\n            <div className=\"flex items-center\">\n              <span><strong>Chapitres:</strong> {course.chapters.length}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Leçons:</strong> {course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"mr-2\">⏱</span>\n              <span><strong>Durée:</strong> {formatDuration(course.estimated_duration)}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Progression:</strong> {course.progress_percentage}%</span>\n            </div>\n          </div>\n\n          {course.is_enrolled && (\n            <div className=\"mb-6\">\n              <div className=\"flex justify-between text-sm mb-2\">\n                <span className=\"font-medium\">Progression du cours</span>\n                <span>{course.progress_percentage}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                <div\n                  className=\"bg-primary-600 h-3 rounded-full transition-all duration-300\"\n                  style={{ width: `${course.progress_percentage}%` }}\n                />\n              </div>\n            </div>\n          )}\n\n          {!course.is_enrolled && (\n            <div className=\"text-center\">\n              <button\n                onClick={handleEnroll}\n                disabled={enrolling}\n                className=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\"\n              >\n                {enrolling ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"></div>\n                    Inscription...\n                  </>\n                ) : (\n                  'S\\'inscrire au cours'\n                )}\n              </button>\n            </div>\n          )}\n        </motion.div>\n\n        {course.is_enrolled && (\n          <div className=\"space-y-6\">\n            {course.chapters.map((chapter, chapterIndex) => (\n              <motion.div\n                key={chapter.id}\n                className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: (chapterIndex + 1) * 0.1 }}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                    Chapitre {chapter.order}: {chapter.title}\n                  </h2>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {chapter.progress_percentage}% terminé\n                  </span>\n                </div>\n\n                {chapter.description && (\n                  <p className=\"text-gray-700 dark:text-gray-300 mb-4\">\n                    {chapter.description}\n                  </p>\n                )}\n\n                <div className=\"mb-4\">\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${chapter.progress_percentage}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {chapter.lessons.map((lesson) => (\n                    <div\n                      key={lesson.id}\n                      className={`flex items-center justify-between p-3 rounded-lg border ${\n                        lesson.is_completed\n                          ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700'\n                          : lesson.is_accessible\n                          ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'\n                          : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'\n                      } transition-colors`}\n                    >\n                      <div className=\"flex items-center\">\n                        <span className=\"text-xl mr-3\">\n                          {lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'}\n                        </span>\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                            {lesson.title}\n                          </h4>\n                          <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                            <span className=\"mr-3\">{lesson.type_display}</span>\n                            <span className=\"mr-3\">⏱{lesson.estimated_duration}min</span>\n                            {lesson.time_spent > 0 && (\n                              <span>{Math.floor(lesson.time_spent / 60)}min passées</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2\">\n                        {lesson.is_accessible ? (\n                          <>\n                            <button\n                              onClick={() => navigate(`/lessons/${lesson.id}`)}\n                              className=\"bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\"\n                            >\n                              {lesson.is_completed ? 'Revoir' : 'Commencer'}\n                            </button>\n                            {lesson.exercise_id && (\n                              <button\n                                onClick={() => navigate(`/exercises/${lesson.exercise_id}`)}\n                                className=\"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\"\n                              >\n                                Exercice\n                              </button>\n                            )}\n                          </>\n                        ) : (\n                          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                            Terminez les leçons précédentes\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        )}\n        </div>\n      </div>\n    </StudentLayout>\n  );\n};\n\nexport default CourseDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA4CxD,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAC1C,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,IAAIc,IAAI,IAAIF,EAAE,EAAE;MACdS,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACP,IAAI,EAAEF,EAAE,CAAC,CAAC;EAEd,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqCX,EAAE,GAAG,EAAE;QACvEY,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACJ,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBN,QAAQ,CAACO,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMC,UAAU,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MACxCf,SAAS,CAACc,UAAU,CAAC;MACrBZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACnB,MAAM,EAAE;IAEb,IAAI;MACFK,YAAY,CAAC,IAAI,CAAC;MAClBe,UAAU,CAAC,MAAM;QACfnB,SAAS,CAAC;UAAE,GAAGD,MAAM;UAAEqB,WAAW,EAAE;QAAK,CAAC,CAAC;QAC3ChB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDZ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiB,aAAa,GAAIC,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIG,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,IAAI,CAAC9B,IAAI,EAAE;IACT,oBACEP,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CvC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvC,OAAA;UAAIsC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5D3C,OAAA;UAAGsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ3C,OAAA;UACE4C,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjC,OAAO,EAAE;IACX,oBACEV,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CvC,OAAA;QAAKsC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvC,OAAA;UAAKsC,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtG3C,OAAA;UAAGsC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACnC,MAAM,EAAE;IACX,oBACER,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CvC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvC,OAAA;UAAIsC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D3C,OAAA;UAAGsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ3C,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,UAAU,CAAE;UACpCgC,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3C,OAAA,CAACF,aAAa;IACZgD,KAAK,EAAEtC,MAAM,CAACsC,KAAM;IACpBC,QAAQ,EAAEvC,MAAM,CAACwC,WAAY;IAC7BC,OAAO,eACLjD,OAAA;MACE6C,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,UAAU,CAAE;MACpCgC,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAEvFvC,OAAA;QAAMsC,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,oBAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;IAAAJ,QAAA,eAEDvC,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvC,OAAA;QAAKsC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClCvC,OAAA,CAACJ,MAAM,CAACsD,GAAG;UACTZ,SAAS,EAAC,yDAAyD;UACnEa,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAd,QAAA,gBAE9BvC,OAAA;YAAKsC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDvC,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCvC,OAAA;gBAAMsC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAE/B,MAAM,CAAC+C;cAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzD3C,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAIsC,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAC7D/B,MAAM,CAACsC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACL3C,OAAA;kBAAMsC,SAAS,EAAE,gEAAgER,aAAa,CAACtB,MAAM,CAACuB,KAAK,CAAC,EAAG;kBAAAQ,QAAA,EAC5G/B,MAAM,CAACgD;gBAAa;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA;YAAGsC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACjD/B,MAAM,CAACwC;UAAW;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEJ3C,OAAA;YAAKsC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjEvC,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCvC,OAAA;gBAAAuC,QAAA,gBAAMvC,OAAA;kBAAAuC,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnC,MAAM,CAACiD,QAAQ,CAACC,MAAM;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACN3C,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCvC,OAAA;gBAAAuC,QAAA,gBAAMvC,OAAA;kBAAAuC,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnC,MAAM,CAACiD,QAAQ,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGC,OAAO,CAACC,OAAO,CAACJ,MAAM,EAAE,CAAC,CAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC,eACN3C,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCvC,OAAA;gBAAMsC,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/B3C,OAAA;gBAAAuC,QAAA,gBAAMvC,OAAA;kBAAAuC,QAAA,EAAQ;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAACxB,MAAM,CAACuD,kBAAkB,CAAC;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACN3C,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCvC,OAAA;gBAAAuC,QAAA,gBAAMvC,OAAA;kBAAAuC,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnC,MAAM,CAACwD,mBAAmB,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELnC,MAAM,CAACqB,WAAW,iBACjB7B,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBvC,OAAA;cAAKsC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDvC,OAAA;gBAAMsC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzD3C,OAAA;gBAAAuC,QAAA,GAAO/B,MAAM,CAACwD,mBAAmB,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN3C,OAAA;cAAKsC,SAAS,EAAC,sDAAsD;cAAAC,QAAA,eACnEvC,OAAA;gBACEsC,SAAS,EAAC,6DAA6D;gBACvE2B,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAG1D,MAAM,CAACwD,mBAAmB;gBAAI;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA,CAACnC,MAAM,CAACqB,WAAW,iBAClB7B,OAAA;YAAKsC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BvC,OAAA;cACE6C,OAAO,EAAElB,YAAa;cACtBwC,QAAQ,EAAEvD,SAAU;cACpB0B,SAAS,EAAC,2HAA2H;cAAAC,QAAA,EAEpI3B,SAAS,gBACRZ,OAAA,CAAAE,SAAA;gBAAAqC,QAAA,gBACEvC,OAAA;kBAAKsC,SAAS,EAAC;gBAA6E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,kBAErG;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,EAEZnC,MAAM,CAACqB,WAAW,iBACjB7B,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB/B,MAAM,CAACiD,QAAQ,CAACW,GAAG,CAAC,CAACP,OAAO,EAAEQ,YAAY,kBACzCrE,OAAA,CAACJ,MAAM,CAACsD,GAAG;YAETZ,SAAS,EAAC,oDAAoD;YAC9Da,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BiB,UAAU,EAAE;cAAEC,KAAK,EAAE,CAACF,YAAY,GAAG,CAAC,IAAI;YAAI,CAAE;YAAA9B,QAAA,gBAEhDvC,OAAA;cAAKsC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDvC,OAAA;gBAAIsC,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,GAAC,WACzD,EAACsB,OAAO,CAACW,KAAK,EAAC,IAAE,EAACX,OAAO,CAACf,KAAK;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACL3C,OAAA;gBAAMsC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,GACvDsB,OAAO,CAACG,mBAAmB,EAAC,cAC/B;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELkB,OAAO,CAACb,WAAW,iBAClBhD,OAAA;cAAGsC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACjDsB,OAAO,CAACb;YAAW;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACJ,eAED3C,OAAA;cAAKsC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBvC,OAAA;gBAAKsC,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnEvC,OAAA;kBACEsC,SAAS,EAAC,2DAA2D;kBACrE2B,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGL,OAAO,CAACG,mBAAmB;kBAAI;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBsB,OAAO,CAACC,OAAO,CAACM,GAAG,CAAEK,MAAM,iBAC1BzE,OAAA;gBAEEsC,SAAS,EAAE,2DACTmC,MAAM,CAACC,YAAY,GACf,sEAAsE,GACtED,MAAM,CAACE,aAAa,GACpB,2GAA2G,GAC3G,8EAA8E,oBAC/D;gBAAApC,QAAA,gBAErBvC,OAAA;kBAAKsC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCvC,OAAA;oBAAMsC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3BkC,MAAM,CAACC,YAAY,GAAG,GAAG,GAAGD,MAAM,CAACE,aAAa,GAAG,IAAI,GAAG;kBAAI;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACP3C,OAAA;oBAAAuC,QAAA,gBACEvC,OAAA;sBAAIsC,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,EACtDkC,MAAM,CAAC3B;oBAAK;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACL3C,OAAA;sBAAKsC,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACzEvC,OAAA;wBAAMsC,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEkC,MAAM,CAACG;sBAAY;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACnD3C,OAAA;wBAAMsC,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,QAAC,EAACkC,MAAM,CAACV,kBAAkB,EAAC,KAAG;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAC5D8B,MAAM,CAACI,UAAU,GAAG,CAAC,iBACpB7E,OAAA;wBAAAuC,QAAA,GAAOJ,IAAI,CAACC,KAAK,CAACqC,MAAM,CAACI,UAAU,GAAG,EAAE,CAAC,EAAC,gBAAW;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC5D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3C,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EACzCkC,MAAM,CAACE,aAAa,gBACnB3E,OAAA,CAAAE,SAAA;oBAAAqC,QAAA,gBACEvC,OAAA;sBACE6C,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,YAAYmE,MAAM,CAACpE,EAAE,EAAE,CAAE;sBACjDiC,SAAS,EAAC,2GAA2G;sBAAAC,QAAA,EAEpHkC,MAAM,CAACC,YAAY,GAAG,QAAQ,GAAG;oBAAW;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,EACR8B,MAAM,CAACK,WAAW,iBACjB9E,OAAA;sBACE6C,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,cAAcmE,MAAM,CAACK,WAAW,EAAE,CAAE;sBAC5DxC,SAAS,EAAC,uGAAuG;sBAAAC,QAAA,EAClH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA,eACD,CAAC,gBAEH3C,OAAA;oBAAMsC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAlDD8B,MAAM,CAACpE,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmDX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAtFDkB,OAAO,CAACxD,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuFL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACvC,EAAA,CA1TID,gBAA0B;EAAA,QACfT,SAAS,EACPC,WAAW,EACXE,OAAO;AAAA;AAAAkF,EAAA,GAHpB5E,gBAA0B;AA4ThC,eAAeA,gBAAgB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}