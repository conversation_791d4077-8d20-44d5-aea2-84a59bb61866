{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\StudentLayout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport StudentBreadcrumbs from './StudentBreadcrumbs';\nimport FloatingNavigation from './FloatingNavigation';\nimport Footer from './Footer';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentLayout = ({\n  children,\n  title,\n  subtitle,\n  showBreadcrumbs = true,\n  breadcrumbItems,\n  actions,\n  className = '',\n  showFloatingNav = true\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigation flottante pour l'espace étudiant\n  const floatingNavItems = [{\n    id: 'dashboard',\n    label: 'Tableau de bord',\n    path: '/etudiant/dashboard',\n    icon: 'DB',\n    shortcut: 'd'\n  }, {\n    id: 'courses',\n    label: 'Mes Cours',\n    path: '/courses',\n    icon: 'CO',\n    shortcut: 'c'\n  }, {\n    id: 'exercises',\n    label: 'Exercices',\n    path: '/exercises',\n    icon: 'EX',\n    shortcut: 'e'\n  }, {\n    id: 'progress',\n    label: 'Ma Progression',\n    path: '/progress',\n    icon: 'PR',\n    shortcut: 'p'\n  }, {\n    id: 'solver',\n    label: 'Résolveur',\n    path: '/solver',\n    icon: 'SO',\n    shortcut: 's'\n  }, {\n    id: 'visualizer',\n    label: 'Visualiseur',\n    path: '/visualizer',\n    icon: 'VI',\n    shortcut: 'v'\n  },\n  // Séparateur\n  {\n    id: 'separator-1',\n    label: '',\n    path: '',\n    icon: '',\n    isSeparator: true\n  },\n  // Section Utilisateur\n  {\n    id: 'profile',\n    label: 'Mon Profil',\n    path: '/profile',\n    icon: 'US',\n    shortcut: 'u'\n  }, {\n    id: 'settings',\n    label: 'Paramètres',\n    path: '/settings',\n    icon: 'SE',\n    shortcut: 't'\n  },\n  // Séparateur\n  {\n    id: 'separator-2',\n    label: '',\n    path: '',\n    icon: '',\n    isSeparator: true\n  },\n  // Actions\n  {\n    id: 'theme',\n    label: theme === 'light' ? 'Mode Sombre' : 'Mode Clair',\n    path: '',\n    icon: theme === 'light' ? 'MO' : 'SU',\n    shortcut: 'm',\n    isAction: true,\n    action: toggleTheme\n  }, {\n    id: 'logout',\n    label: 'Déconnexion',\n    path: '',\n    icon: 'LO',\n    shortcut: 'q',\n    isAction: true,\n    action: handleLogout\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n        children: [showBreadcrumbs && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(StudentBreadcrumbs, {\n            items: breadcrumbItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), (title || subtitle || actions) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [title && /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                children: title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 21\n              }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-lg text-gray-600 dark:text-gray-400\",\n                children: subtitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), actions && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: actions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: className,\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), showFloatingNav && /*#__PURE__*/_jsxDEV(FloatingNavigation, {\n      items: floatingNavItems,\n      position: \"bottom-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentLayout, \"m8WNAyvVTlaWWa4TyPZbSi9X8yA=\", false, function () {\n  return [useAuth, useTheme, useNavigate];\n});\n_c = StudentLayout;\nexport default StudentLayout;\nvar _c;\n$RefreshReg$(_c, \"StudentLayout\");", "map": {"version": 3, "names": ["React", "StudentBreadcrumbs", "FloatingNavigation", "Footer", "useAuth", "useTheme", "useNavigate", "jsxDEV", "_jsxDEV", "StudentLayout", "children", "title", "subtitle", "showBreadcrumbs", "breadcrumbItems", "actions", "className", "showFloatingNav", "_s", "user", "logout", "theme", "toggleTheme", "navigate", "handleLogout", "floatingNavItems", "id", "label", "path", "icon", "shortcut", "isSeparator", "isAction", "action", "items", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/StudentLayout.tsx"], "sourcesContent": ["import React from 'react';\nimport StudentBreadcrumbs from './StudentBreadcrumbs';\nimport FloatingNavigation from './FloatingNavigation';\nimport Footer from './Footer';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useNavigate } from 'react-router-dom';\n\ninterface StudentLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  showBreadcrumbs?: boolean;\n  breadcrumbItems?: Array<{\n    label: string;\n    path?: string;\n    icon?: string;\n    isActive?: boolean;\n  }>;\n  actions?: React.ReactNode;\n  className?: string;\n  showFloatingNav?: boolean;\n}\n\nconst StudentLayout: React.FC<StudentLayoutProps> = ({\n  children,\n  title,\n  subtitle,\n  showBreadcrumbs = true,\n  breadcrumbItems,\n  actions,\n  className = '',\n  showFloatingNav = true\n}) => {\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigation flottante pour l'espace étudiant\n  const floatingNavItems = [\n    {\n      id: 'dashboard',\n      label: 'Tableau de bord',\n      path: '/etudiant/dashboard',\n      icon: 'DB',\n      shortcut: 'd'\n    },\n    {\n      id: 'courses',\n      label: 'Mes Cours',\n      path: '/courses',\n      icon: 'CO',\n      shortcut: 'c'\n    },\n    {\n      id: 'exercises',\n      label: 'Exercices',\n      path: '/exercises',\n      icon: 'EX',\n      shortcut: 'e'\n    },\n    {\n      id: 'progress',\n      label: 'Ma Progression',\n      path: '/progress',\n      icon: 'PR',\n      shortcut: 'p'\n    },\n    {\n      id: 'solver',\n      label: 'Résolveur',\n      path: '/solver',\n      icon: 'SO',\n      shortcut: 's'\n    },\n    {\n      id: 'visualizer',\n      label: 'Visualiseur',\n      path: '/visualizer',\n      icon: 'VI',\n      shortcut: 'v'\n    },\n    // Séparateur\n    { id: 'separator-1', label: '', path: '', icon: '', isSeparator: true },\n    // Section Utilisateur\n    {\n      id: 'profile',\n      label: 'Mon Profil',\n      path: '/profile',\n      icon: 'US',\n      shortcut: 'u'\n    },\n    {\n      id: 'settings',\n      label: 'Paramètres',\n      path: '/settings',\n      icon: 'SE',\n      shortcut: 't'\n    },\n    // Séparateur\n    { id: 'separator-2', label: '', path: '', icon: '', isSeparator: true },\n    // Actions\n    {\n      id: 'theme',\n      label: theme === 'light' ? 'Mode Sombre' : 'Mode Clair',\n      path: '',\n      icon: theme === 'light' ? 'MO' : 'SU',\n      shortcut: 'm',\n      isAction: true,\n      action: toggleTheme\n    },\n    {\n      id: 'logout',\n      label: 'Déconnexion',\n      path: '',\n      icon: 'LO',\n      shortcut: 'q',\n      isAction: true,\n      action: handleLogout\n    }\n  ];\n  return (\n    <div className=\"min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900\">\n      {/* Contenu principal */}\n      <main className=\"flex-1\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          {/* Breadcrumbs */}\n          {showBreadcrumbs && (\n            <div className=\"mb-6\">\n              <StudentBreadcrumbs items={breadcrumbItems} />\n            </div>\n          )}\n          \n          {/* En-tête de page */}\n          {(title || subtitle || actions) && (\n            <div className=\"mb-8\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n                <div className=\"flex-1\">\n                  {title && (\n                    <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                      {title}\n                    </h1>\n                  )}\n                  {subtitle && (\n                    <p className=\"mt-2 text-lg text-gray-600 dark:text-gray-400\">\n                      {subtitle}\n                    </p>\n                  )}\n                </div>\n                \n                {/* Actions */}\n                {actions && (\n                  <div className=\"flex-shrink-0\">\n                    {actions}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n          \n          {/* Contenu de la page */}\n          <div className={className}>\n            {children}\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <Footer />\n\n      {/* Navigation flottante */}\n      {showFloatingNav && (\n        <FloatingNavigation\n          items={floatingNavItems}\n          position=\"bottom-right\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default StudentLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkB/C,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,QAAQ;EACRC,KAAK;EACLC,QAAQ;EACRC,eAAe,GAAG,IAAI;EACtBC,eAAe;EACfC,OAAO;EACPC,SAAS,GAAG,EAAE;EACdC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEiB,KAAK;IAAEC;EAAY,CAAC,GAAGjB,QAAQ,CAAC,CAAC;EACzC,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzBJ,MAAM,CAAC,CAAC;IACRG,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IAAEJ,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEE,WAAW,EAAE;EAAK,CAAC;EACvE;EACA;IACEL,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IAAEJ,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEE,WAAW,EAAE;EAAK,CAAC;EACvE;EACA;IACEL,EAAE,EAAE,OAAO;IACXC,KAAK,EAAEN,KAAK,KAAK,OAAO,GAAG,aAAa,GAAG,YAAY;IACvDO,IAAI,EAAE,EAAE;IACRC,IAAI,EAAER,KAAK,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;IACrCS,QAAQ,EAAE,GAAG;IACbE,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAEX;EACV,CAAC,EACD;IACEI,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbE,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAET;EACV,CAAC,CACF;EACD,oBACEhB,OAAA;IAAKQ,SAAS,EAAC,wDAAwD;IAAAN,QAAA,gBAErEF,OAAA;MAAMQ,SAAS,EAAC,QAAQ;MAAAN,QAAA,eACtBF,OAAA;QAAKQ,SAAS,EAAC,6CAA6C;QAAAN,QAAA,GAEzDG,eAAe,iBACdL,OAAA;UAAKQ,SAAS,EAAC,MAAM;UAAAN,QAAA,eACnBF,OAAA,CAACP,kBAAkB;YAACiC,KAAK,EAAEpB;UAAgB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACN,EAGA,CAAC3B,KAAK,IAAIC,QAAQ,IAAIG,OAAO,kBAC5BP,OAAA;UAAKQ,SAAS,EAAC,MAAM;UAAAN,QAAA,eACnBF,OAAA;YAAKQ,SAAS,EAAC,qFAAqF;YAAAN,QAAA,gBAClGF,OAAA;cAAKQ,SAAS,EAAC,QAAQ;cAAAN,QAAA,GACpBC,KAAK,iBACJH,OAAA;gBAAIQ,SAAS,EAAC,kDAAkD;gBAAAN,QAAA,EAC7DC;cAAK;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACL,EACA1B,QAAQ,iBACPJ,OAAA;gBAAGQ,SAAS,EAAC,+CAA+C;gBAAAN,QAAA,EACzDE;cAAQ;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGLvB,OAAO,iBACNP,OAAA;cAAKQ,SAAS,EAAC,eAAe;cAAAN,QAAA,EAC3BK;YAAO;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD9B,OAAA;UAAKQ,SAAS,EAAEA,SAAU;UAAAN,QAAA,EACvBA;QAAQ;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9B,OAAA,CAACL,MAAM;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGTrB,eAAe,iBACdT,OAAA,CAACN,kBAAkB;MACjBgC,KAAK,EAAET,gBAAiB;MACxBc,QAAQ,EAAC;IAAc;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpB,EAAA,CAhKIT,aAA2C;EAAA,QAUtBL,OAAO,EACDC,QAAQ,EACtBC,WAAW;AAAA;AAAAkC,EAAA,GAZxB/B,aAA2C;AAkKjD,eAAeA,aAAa;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}