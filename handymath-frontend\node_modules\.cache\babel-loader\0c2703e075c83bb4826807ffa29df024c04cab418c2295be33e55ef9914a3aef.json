{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Computes the elimination tree of Matrix A (using triu(A)) or the\n * elimination tree of A'A without forming A'A.\n *\n * @param {Matrix}  a               The A Matrix\n * @param {boolean} ata             A value of true the function computes the etree of A'A\n */\nexport function csEtree(a, ata) {\n  // check inputs\n  if (!a) {\n    return null;\n  }\n  // a arrays\n  var aindex = a._index;\n  var aptr = a._ptr;\n  var asize = a._size;\n  // rows & columns\n  var m = asize[0];\n  var n = asize[1];\n\n  // allocate result\n  var parent = []; // (n)\n\n  // allocate workspace\n  var w = []; // (n + (ata ? m : 0))\n  var ancestor = 0; // first n entries in w\n  var prev = n; // last m entries (ata = true)\n\n  var i, inext;\n\n  // check we are calculating A'A\n  if (ata) {\n    // initialize workspace\n    for (i = 0; i < m; i++) {\n      w[prev + i] = -1;\n    }\n  }\n  // loop columns\n  for (var k = 0; k < n; k++) {\n    // node k has no parent yet\n    parent[k] = -1;\n    // nor does k have an ancestor\n    w[ancestor + k] = -1;\n    // values in column k\n    for (var p0 = aptr[k], p1 = aptr[k + 1], p = p0; p < p1; p++) {\n      // row\n      var r = aindex[p];\n      // node\n      i = ata ? w[prev + r] : r;\n      // traverse from i to k\n      for (; i !== -1 && i < k; i = inext) {\n        // inext = ancestor of i\n        inext = w[ancestor + i];\n        // path compression\n        w[ancestor + i] = k;\n        // check no anc., parent is k\n        if (inext === -1) {\n          parent[i] = k;\n        }\n      }\n      if (ata) {\n        w[prev + r] = k;\n      }\n    }\n  }\n  return parent;\n}", "map": {"version": 3, "names": ["csEtree", "a", "ata", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "m", "n", "parent", "w", "ancestor", "prev", "i", "inext", "k", "p0", "p1", "p", "r"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csEtree.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Computes the elimination tree of Matrix A (using triu(A)) or the\n * elimination tree of A'A without forming A'A.\n *\n * @param {Matrix}  a               The A Matrix\n * @param {boolean} ata             A value of true the function computes the etree of A'A\n */\nexport function csEtree(a, ata) {\n  // check inputs\n  if (!a) {\n    return null;\n  }\n  // a arrays\n  var aindex = a._index;\n  var aptr = a._ptr;\n  var asize = a._size;\n  // rows & columns\n  var m = asize[0];\n  var n = asize[1];\n\n  // allocate result\n  var parent = []; // (n)\n\n  // allocate workspace\n  var w = []; // (n + (ata ? m : 0))\n  var ancestor = 0; // first n entries in w\n  var prev = n; // last m entries (ata = true)\n\n  var i, inext;\n\n  // check we are calculating A'A\n  if (ata) {\n    // initialize workspace\n    for (i = 0; i < m; i++) {\n      w[prev + i] = -1;\n    }\n  }\n  // loop columns\n  for (var k = 0; k < n; k++) {\n    // node k has no parent yet\n    parent[k] = -1;\n    // nor does k have an ancestor\n    w[ancestor + k] = -1;\n    // values in column k\n    for (var p0 = aptr[k], p1 = aptr[k + 1], p = p0; p < p1; p++) {\n      // row\n      var r = aindex[p];\n      // node\n      i = ata ? w[prev + r] : r;\n      // traverse from i to k\n      for (; i !== -1 && i < k; i = inext) {\n        // inext = ancestor of i\n        inext = w[ancestor + i];\n        // path compression\n        w[ancestor + i] = k;\n        // check no anc., parent is k\n        if (inext === -1) {\n          parent[i] = k;\n        }\n      }\n      if (ata) {\n        w[prev + r] = k;\n      }\n    }\n  }\n  return parent;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC9B;EACA,IAAI,CAACD,CAAC,EAAE;IACN,OAAO,IAAI;EACb;EACA;EACA,IAAIE,MAAM,GAAGF,CAAC,CAACG,MAAM;EACrB,IAAIC,IAAI,GAAGJ,CAAC,CAACK,IAAI;EACjB,IAAIC,KAAK,GAAGN,CAAC,CAACO,KAAK;EACnB;EACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;EAChB,IAAIG,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC;;EAEhB;EACA,IAAII,MAAM,GAAG,EAAE,CAAC,CAAC;;EAEjB;EACA,IAAIC,CAAC,GAAG,EAAE,CAAC,CAAC;EACZ,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;EAClB,IAAIC,IAAI,GAAGJ,CAAC,CAAC,CAAC;;EAEd,IAAIK,CAAC,EAAEC,KAAK;;EAEZ;EACA,IAAId,GAAG,EAAE;IACP;IACA,KAAKa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,CAAC,EAAEM,CAAC,EAAE,EAAE;MACtBH,CAAC,CAACE,IAAI,GAAGC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB;EACF;EACA;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,CAAC,EAAEO,CAAC,EAAE,EAAE;IAC1B;IACAN,MAAM,CAACM,CAAC,CAAC,GAAG,CAAC,CAAC;IACd;IACAL,CAAC,CAACC,QAAQ,GAAGI,CAAC,CAAC,GAAG,CAAC,CAAC;IACpB;IACA,KAAK,IAAIC,EAAE,GAAGb,IAAI,CAACY,CAAC,CAAC,EAAEE,EAAE,GAAGd,IAAI,CAACY,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;MAC5D;MACA,IAAIC,CAAC,GAAGlB,MAAM,CAACiB,CAAC,CAAC;MACjB;MACAL,CAAC,GAAGb,GAAG,GAAGU,CAAC,CAACE,IAAI,GAAGO,CAAC,CAAC,GAAGA,CAAC;MACzB;MACA,OAAON,CAAC,KAAK,CAAC,CAAC,IAAIA,CAAC,GAAGE,CAAC,EAAEF,CAAC,GAAGC,KAAK,EAAE;QACnC;QACAA,KAAK,GAAGJ,CAAC,CAACC,QAAQ,GAAGE,CAAC,CAAC;QACvB;QACAH,CAAC,CAACC,QAAQ,GAAGE,CAAC,CAAC,GAAGE,CAAC;QACnB;QACA,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBL,MAAM,CAACI,CAAC,CAAC,GAAGE,CAAC;QACf;MACF;MACA,IAAIf,GAAG,EAAE;QACPU,CAAC,CAACE,IAAI,GAAGO,CAAC,CAAC,GAAGJ,CAAC;MACjB;IACF;EACF;EACA,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}