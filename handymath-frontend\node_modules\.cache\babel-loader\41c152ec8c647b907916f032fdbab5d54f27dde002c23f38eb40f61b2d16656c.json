{"ast": null, "code": "import Fraction from 'fraction.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'Fraction';\nvar dependencies = [];\nexport var createFractionClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(Fraction, 'name', {\n    value: 'Fraction'\n  });\n  Fraction.prototype.constructor = Fraction;\n  Fraction.prototype.type = 'Fraction';\n  Fraction.prototype.isFraction = true;\n\n  /**\n   * Get a JSON representation of a Fraction containing type information\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   */\n  Fraction.prototype.toJSON = function () {\n    return {\n      mathjs: 'Fraction',\n      n: String(this.s * this.n),\n      d: String(this.d)\n    };\n  };\n\n  /**\n   * Instantiate a Fraction from a JSON object\n   * @param {Object} json  a JSON object structured as:\n   *                       `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   * @return {BigNumber}\n   */\n  Fraction.fromJSON = function (json) {\n    return new Fraction(json);\n  };\n  return Fraction;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["Fraction", "factory", "name", "dependencies", "createFractionClass", "Object", "defineProperty", "value", "prototype", "constructor", "type", "isFraction", "toJSON", "mathjs", "n", "String", "s", "d", "fromJSON", "json", "isClass"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/fraction/Fraction.js"], "sourcesContent": ["import Fraction from 'fraction.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'Fraction';\nvar dependencies = [];\nexport var createFractionClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(Fraction, 'name', {\n    value: 'Fraction'\n  });\n  Fraction.prototype.constructor = Fraction;\n  Fraction.prototype.type = 'Fraction';\n  Fraction.prototype.isFraction = true;\n\n  /**\n   * Get a JSON representation of a Fraction containing type information\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   */\n  Fraction.prototype.toJSON = function () {\n    return {\n      mathjs: 'Fraction',\n      n: String(this.s * this.n),\n      d: String(this.d)\n    };\n  };\n\n  /**\n   * Instantiate a Fraction from a JSON object\n   * @param {Object} json  a JSON object structured as:\n   *                       `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   * @return {BigNumber}\n   */\n  Fraction.fromJSON = function (json) {\n    return new Fraction(json);\n  };\n  return Fraction;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,UAAU;AACrB,IAAIC,YAAY,GAAG,EAAE;AACrB,OAAO,IAAIC,mBAAmB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAE,MAAM;EAChF;AACF;AACA;EACEE,MAAM,CAACC,cAAc,CAACN,QAAQ,EAAE,MAAM,EAAE;IACtCO,KAAK,EAAE;EACT,CAAC,CAAC;EACFP,QAAQ,CAACQ,SAAS,CAACC,WAAW,GAAGT,QAAQ;EACzCA,QAAQ,CAACQ,SAAS,CAACE,IAAI,GAAG,UAAU;EACpCV,QAAQ,CAACQ,SAAS,CAACG,UAAU,GAAG,IAAI;;EAEpC;AACF;AACA;AACA;AACA;EACEX,QAAQ,CAACQ,SAAS,CAACI,MAAM,GAAG,YAAY;IACtC,OAAO;MACLC,MAAM,EAAE,UAAU;MAClBC,CAAC,EAAEC,MAAM,CAAC,IAAI,CAACC,CAAC,GAAG,IAAI,CAACF,CAAC,CAAC;MAC1BG,CAAC,EAAEF,MAAM,CAAC,IAAI,CAACE,CAAC;IAClB,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACEjB,QAAQ,CAACkB,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAClC,OAAO,IAAInB,QAAQ,CAACmB,IAAI,CAAC;EAC3B,CAAC;EACD,OAAOnB,QAAQ;AACjB,CAAC,EAAE;EACDoB,OAAO,EAAE;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}