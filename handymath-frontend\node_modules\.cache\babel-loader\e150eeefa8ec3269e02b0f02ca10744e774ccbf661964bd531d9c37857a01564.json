{"ast": null, "code": "export var rotateDocs = {\n  name: 'rotate',\n  category: 'Matrix',\n  syntax: ['rotate(w, theta)', 'rotate(w, theta, v)'],\n  description: 'Returns a 2-D rotation matrix (2x2) for a given angle (in radians). ' + 'Returns a 2-D rotation matrix (3x3) of a given angle (in radians) around given axis.',\n  examples: ['rotate([1, 0], pi / 2)', 'rotate(matrix([1, 0]), unit(\"35deg\"))', 'rotate([1, 0, 0], unit(\"90deg\"), [0, 0, 1])', 'rotate(matrix([1, 0, 0]), unit(\"90deg\"), matrix([0, 0, 1]))'],\n  seealso: ['matrix', 'rotationMatrix']\n};", "map": {"version": 3, "names": ["rotateDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/rotate.js"], "sourcesContent": ["export var rotateDocs = {\n  name: 'rotate',\n  category: 'Matrix',\n  syntax: ['rotate(w, theta)', 'rotate(w, theta, v)'],\n  description: 'Returns a 2-D rotation matrix (2x2) for a given angle (in radians). ' + 'Returns a 2-D rotation matrix (3x3) of a given angle (in radians) around given axis.',\n  examples: ['rotate([1, 0], pi / 2)', 'rotate(matrix([1, 0]), unit(\"35deg\"))', 'rotate([1, 0, 0], unit(\"90deg\"), [0, 0, 1])', 'rotate(matrix([1, 0, 0]), unit(\"90deg\"), matrix([0, 0, 1]))'],\n  seealso: ['matrix', 'rotationMatrix']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,kBAAkB,EAAE,qBAAqB,CAAC;EACnDC,WAAW,EAAE,sEAAsE,GAAG,sFAAsF;EAC5KC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,uCAAuC,EAAE,6CAA6C,EAAE,6DAA6D,CAAC;EAC3LC,OAAO,EAAE,CAAC,QAAQ,EAAE,gBAAgB;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}