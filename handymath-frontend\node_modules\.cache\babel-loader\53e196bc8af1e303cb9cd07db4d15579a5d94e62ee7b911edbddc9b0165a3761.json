{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\StudentNavbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { DashboardIcon, CoursesIcon, ExercisesIcon, ProgressIcon, SolverIcon, VisualizerIcon, UserIcon, ChevronDownIcon, SunIcon, MoonIcon } from './icons/NavigationIcons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentNavbar = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigation items pour l'espace étudiant\n  const navItems = [{\n    label: 'Tableau de bord',\n    path: '/etudiant/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    description: 'Vue d\\'ensemble de votre progression'\n  }, {\n    label: 'Mes Cours',\n    path: '/courses',\n    icon: /*#__PURE__*/_jsxDEV(CoursesIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this),\n    description: 'Accédez à vos cours et leçons'\n  }, {\n    label: 'Exercices',\n    path: '/exercises',\n    icon: /*#__PURE__*/_jsxDEV(ExercisesIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this),\n    description: 'Pratiquez avec nos exercices'\n  }, {\n    label: 'Ma Progression',\n    path: '/progress',\n    icon: /*#__PURE__*/_jsxDEV(ProgressIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    description: 'Suivez vos statistiques'\n  }, {\n    label: 'Résolveur',\n    path: '/solver',\n    icon: /*#__PURE__*/_jsxDEV(SolverIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    description: 'Résolvez des équations'\n  }, {\n    label: 'Visualiseur',\n    path: '/visualizer',\n    icon: /*#__PURE__*/_jsxDEV(VisualizerIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    description: 'Visualisez des fonctions'\n  }];\n  const isActivePath = path => {\n    if (path === '/etudiant/dashboard') {\n      return location.pathname === path;\n    }\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors\",\n            children: \"HandyMath\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 dark:text-gray-500\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-lg font-medium text-gray-700 dark:text-gray-300\",\n              children: \"Espace \\xC9tudiant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center space-x-1\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `group relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActivePath(item.path) ? 'bg-primary-600 text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'}`,\n            title: item.description,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\",\n              children: [item.description, /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: toggleTheme,\n            className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            title: theme === 'light' ? 'Mode sombre' : 'Mode clair',\n            children: theme === 'light' ? /*#__PURE__*/_jsxDEV(MoonIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SunIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 rounded-full bg-primary-600 flex items-center justify-center text-white text-sm font-semibold\",\n                children: user.username.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden md:block\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: user.prenom || user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-primary-600 dark:text-primary-400\",\n                  children: \"\\xC9tudiant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(ChevronDownIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/profile\",\n                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                    children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                      size: \"sm\",\n                      className: \"mr-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 25\n                    }, this), \"Mon Profil\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/settings\",\n                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 mr-3\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 25\n                    }, this), \"Param\\xE8tres\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-t border-gray-200 dark:border-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleLogout,\n                    className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 mr-3\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this), \"D\\xE9connexion\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"lg:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden border-t border-gray-200 dark:border-gray-700 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors ${isActivePath(item.path) ? 'bg-primary-600 text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentNavbar, \"mqNeklrjssChoYLNAXV6Lg6vJL0=\", false, function () {\n  return [useLocation, useNavigate, useAuth, useTheme];\n});\n_c = StudentNavbar;\nexport default StudentNavbar;\nvar _c;\n$RefreshReg$(_c, \"StudentNavbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "useAuth", "useTheme", "DashboardIcon", "CoursesIcon", "ExercisesIcon", "ProgressIcon", "SolverIcon", "VisualizerIcon", "UserIcon", "ChevronDownIcon", "SunIcon", "MoonIcon", "jsxDEV", "_jsxDEV", "StudentNavbar", "_s", "location", "navigate", "user", "logout", "theme", "toggleTheme", "isMobileMenuOpen", "setIsMobileMenuOpen", "handleLogout", "navItems", "label", "path", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "isActivePath", "pathname", "startsWith", "className", "children", "to", "map", "item", "title", "type", "onClick", "username", "char<PERSON>t", "toUpperCase", "prenom", "size", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/StudentNavbar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport {\n  DashboardIcon,\n  CoursesIcon,\n  ExercisesIcon,\n  ProgressIcon,\n  SolverIcon,\n  VisualizerIcon,\n  UserIcon,\n  SettingsIcon,\n  LogoutIcon,\n  MenuIcon,\n  CloseIcon,\n  ChevronDownIcon,\n  SunIcon,\n  MoonIcon\n} from './icons/NavigationIcons';\n\ninterface NavItem {\n  label: string;\n  path: string;\n  icon: React.ReactNode;\n  description: string;\n  badge?: string;\n}\n\nconst StudentNavbar: React.FC = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigation items pour l'espace étudiant\n  const navItems = [\n    {\n      label: 'Tableau de bord',\n      path: '/etudiant/dashboard',\n      icon: <DashboardIcon />,\n      description: 'Vue d\\'ensemble de votre progression'\n    },\n    {\n      label: 'Mes Cours',\n      path: '/courses',\n      icon: <CoursesIcon />,\n      description: 'Accédez à vos cours et leçons'\n    },\n    {\n      label: 'Exercices',\n      path: '/exercises',\n      icon: <ExercisesIcon />,\n      description: 'Pratiquez avec nos exercices'\n    },\n    {\n      label: 'Ma Progression',\n      path: '/progress',\n      icon: <ProgressIcon />,\n      description: 'Suivez vos statistiques'\n    },\n    {\n      label: 'Résolveur',\n      path: '/solver',\n      icon: <SolverIcon />,\n      description: 'Résolvez des équations'\n    },\n    {\n      label: 'Visualiseur',\n      path: '/visualizer',\n      icon: <VisualizerIcon />,\n      description: 'Visualisez des fonctions'\n    }\n  ];\n\n  const isActivePath = (path: string) => {\n    if (path === '/etudiant/dashboard') {\n      return location.pathname === path;\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo et titre */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              to=\"/\"\n              className=\"text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors\"\n            >\n              HandyMath\n            </Link>\n            <div className=\"hidden md:block\">\n              <span className=\"text-gray-400 dark:text-gray-500\">•</span>\n              <span className=\"ml-2 text-lg font-medium text-gray-700 dark:text-gray-300\">\n                Espace Étudiant\n              </span>\n            </div>\n          </div>\n\n          {/* Navigation principale - Desktop */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`group relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                  isActivePath(item.path)\n                    ? 'bg-primary-600 text-white shadow-sm'\n                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'\n                }`}\n                title={item.description}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <span>{item.icon}</span>\n                  <span>{item.label}</span>\n                </div>\n                \n                {/* Tooltip */}\n                <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\">\n                  {item.description}\n                  <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700\"></div>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Actions utilisateur */}\n          <div className=\"flex items-center space-x-3\">\n            {/* Bouton thème */}\n            <button\n              type=\"button\"\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              title={theme === 'light' ? 'Mode sombre' : 'Mode clair'}\n            >\n              {theme === 'light' ? <MoonIcon /> : <SunIcon />}\n            </button>\n\n            {/* Informations utilisateur */}\n            {user && (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"hidden sm:flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary-600 flex items-center justify-center text-white text-sm font-semibold\">\n                    {user.username.charAt(0).toUpperCase()}\n                  </div>\n                  <div className=\"hidden md:block\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {user.prenom || user.username}\n                    </div>\n                    <div className=\"text-xs text-primary-600 dark:text-primary-400\">\n                      Étudiant\n                    </div>\n                  </div>\n                </div>\n\n                {/* Menu utilisateur */}\n                <div className=\"relative group\">\n                  <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\">\n                    <ChevronDownIcon />\n                  </button>\n                  \n                  {/* Dropdown menu */}\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto\">\n                    <div className=\"py-1\">\n                      <Link\n                        to=\"/profile\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      >\n                        <UserIcon size=\"sm\" className=\"mr-3\" />\n                        Mon Profil\n                      </Link>\n                      <Link\n                        to=\"/settings\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      >\n                        <svg className=\"w-4 h-4 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                        Paramètres\n                      </Link>\n                      <div className=\"border-t border-gray-200 dark:border-gray-600\"></div>\n                      <button\n                        onClick={handleLogout}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20\"\n                      >\n                        <svg className=\"w-4 h-4 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        Déconnexion\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Menu mobile */}\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                {isMobileMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Menu mobile */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden border-t border-gray-200 dark:border-gray-700 py-4\">\n            <div className=\"space-y-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors ${\n                    isActivePath(item.path)\n                      ? 'bg-primary-600 text-white shadow-sm'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <span>{item.icon}</span>\n                  <div>\n                    <div>{item.label}</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">{item.description}</div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default StudentNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SACEC,aAAa,EACbC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,cAAc,EACdC,QAAQ,EAKRC,eAAe,EACfC,OAAO,EACPC,QAAQ,QACH,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUjC,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB,IAAI;IAAEC;EAAO,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEoB,KAAK;IAAEC;EAAY,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EACzC,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACzBL,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMQ,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,eAAEf,OAAA,CAACX,aAAa;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAEf,OAAA,CAACV,WAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAEf,OAAA,CAACT,aAAa;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAEf,OAAA,CAACR,YAAY;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,SAAS;IACfC,IAAI,eAAEf,OAAA,CAACP,UAAU;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,aAAa;IACnBC,IAAI,eAAEf,OAAA,CAACN,cAAc;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,YAAY,GAAIP,IAAY,IAAK;IACrC,IAAIA,IAAI,KAAK,qBAAqB,EAAE;MAClC,OAAOX,QAAQ,CAACmB,QAAQ,KAAKR,IAAI;IACnC;IACA,OAAOX,QAAQ,CAACmB,QAAQ,CAACC,UAAU,CAACT,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEd,OAAA;IAAKwB,SAAS,EAAC,qGAAqG;IAAAC,QAAA,eAClHzB,OAAA;MAAKwB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDzB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDzB,OAAA;UAAKwB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzB,OAAA,CAAChB,IAAI;YACH0C,EAAE,EAAC,GAAG;YACNF,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzB,OAAA;cAAMwB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DnB,OAAA;cAAMwB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAC;YAE5E;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnB,OAAA;UAAKwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDb,QAAQ,CAACe,GAAG,CAAEC,IAAI,iBACjB5B,OAAA,CAAChB,IAAI;YAEH0C,EAAE,EAAEE,IAAI,CAACd,IAAK;YACdU,SAAS,EAAE,uFACTH,YAAY,CAACO,IAAI,CAACd,IAAI,CAAC,GACnB,qCAAqC,GACrC,8HAA8H,EACjI;YACHe,KAAK,EAAED,IAAI,CAACR,WAAY;YAAAK,QAAA,gBAExBzB,OAAA;cAAKwB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CzB,OAAA;gBAAAyB,QAAA,EAAOG,IAAI,CAACb;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBnB,OAAA;gBAAAyB,QAAA,EAAOG,IAAI,CAACf;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGNnB,OAAA;cAAKwB,SAAS,EAAC,4OAA4O;cAAAC,QAAA,GACxPG,IAAI,CAACR,WAAW,eACjBpB,OAAA;gBAAKwB,SAAS,EAAC;cAA4J;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/K,CAAC;UAAA,GAlBDS,IAAI,CAACd,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnB,OAAA;UAAKwB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1CzB,OAAA;YACE8B,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEvB,WAAY;YACrBgB,SAAS,EAAC,yJAAyJ;YACnKK,KAAK,EAAEtB,KAAK,KAAK,OAAO,GAAG,aAAa,GAAG,YAAa;YAAAkB,QAAA,EAEvDlB,KAAK,KAAK,OAAO,gBAAGP,OAAA,CAACF,QAAQ;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnB,OAAA,CAACH,OAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EAGRd,IAAI,iBACHL,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzB,OAAA;cAAKwB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDzB,OAAA;gBAAKwB,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,EACnHpB,IAAI,CAAC2B,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNnB,OAAA;gBAAKwB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BzB,OAAA;kBAAKwB,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC/DpB,IAAI,CAAC8B,MAAM,IAAI9B,IAAI,CAAC2B;gBAAQ;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNnB,OAAA;kBAAKwB,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnB,OAAA;cAAKwB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzB,OAAA;gBAAQwB,SAAS,EAAC,yJAAyJ;gBAAAC,QAAA,eACzKzB,OAAA,CAACJ,eAAe;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGTnB,OAAA;gBAAKwB,SAAS,EAAC,6OAA6O;gBAAAC,QAAA,eAC1PzB,OAAA;kBAAKwB,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBzB,OAAA,CAAChB,IAAI;oBACH0C,EAAE,EAAC,UAAU;oBACbF,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,gBAEzHzB,OAAA,CAACL,QAAQ;sBAACyC,IAAI,EAAC,IAAI;sBAACZ,SAAS,EAAC;oBAAM;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,cAEzC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPnB,OAAA,CAAChB,IAAI;oBACH0C,EAAE,EAAC,WAAW;oBACdF,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,gBAEzHzB,OAAA;sBAAKwB,SAAS,EAAC,cAAc;sBAACa,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAd,QAAA,gBACjFzB,OAAA;wBAAMwC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAqe;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7iBnB,OAAA;wBAAMwC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAkC;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvG,CAAC,iBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPnB,OAAA;oBAAKwB,SAAS,EAAC;kBAA+C;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrEnB,OAAA;oBACE+B,OAAO,EAAEpB,YAAa;oBACtBa,SAAS,EAAC,oHAAoH;oBAAAC,QAAA,gBAE9HzB,OAAA;sBAAKwB,SAAS,EAAC,cAAc;sBAACa,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAd,QAAA,eACjFzB,OAAA;wBAAMwC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA2F;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChK,CAAC,kBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnB,OAAA;YACE+B,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtDe,SAAS,EAAC,mKAAmK;YAAAC,QAAA,eAE7KzB,OAAA;cAAKwB,SAAS,EAAC,SAAS;cAACa,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAd,QAAA,EAC3EhB,gBAAgB,gBACfT,OAAA;gBAAMwC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9FnB,OAAA;gBAAMwC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLV,gBAAgB,iBACfT,OAAA;QAAKwB,SAAS,EAAC,8DAA8D;QAAAC,QAAA,eAC3EzB,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBb,QAAQ,CAACe,GAAG,CAAEC,IAAI,iBACjB5B,OAAA,CAAChB,IAAI;YAEH0C,EAAE,EAAEE,IAAI,CAACd,IAAK;YACdiB,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAAC,KAAK,CAAE;YAC1Cc,SAAS,EAAE,0FACTH,YAAY,CAACO,IAAI,CAACd,IAAI,CAAC,GACnB,qCAAqC,GACrC,2EAA2E,EAC9E;YAAAW,QAAA,gBAEHzB,OAAA;cAAAyB,QAAA,EAAOG,IAAI,CAACb;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBnB,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAAyB,QAAA,EAAMG,IAAI,CAACf;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvBnB,OAAA;gBAAKwB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEG,IAAI,CAACR;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA,GAbDS,IAAI,CAACd,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA7NID,aAAuB;EAAA,QACVhB,WAAW,EACXC,WAAW,EACHC,OAAO,EACDC,QAAQ;AAAA;AAAAwD,EAAA,GAJnC3C,aAAuB;AA+N7B,eAAeA,aAAa;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}