import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import StudentLayout from '../components/StudentLayout';

interface Lesson {
  id: number;
  title: string;
  lesson_type: string;
  type_display: string;
  order: number;
  estimated_duration: number;
  is_accessible: boolean;
  is_completed: boolean;
  time_spent: number;
  exercise_id?: number;
}

interface Chapter {
  id: number;
  title: string;
  description: string;
  order: number;
  progress_percentage: number;
  lessons: Lesson[];
}

interface Course {
  id: number;
  title: string;
  description: string;
  level: string;
  level_display: string;
  thumbnail: string;
  estimated_duration: number;
  progress_percentage: number;
  is_enrolled: boolean;
  enrollment_date?: string;
  chapters: Chapter[];
  prerequisites: Array<{
    id: number;
    title: string;
    progress: number;
  }>;
  created_at: string;
}

const CourseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState(false);

  useEffect(() => {
    if (user && id) {
      fetchCourse();
    }
  }, [user, id]);

  const fetchCourse = async () => {
    try {
      setLoading(true);

      // Appel API réel au lieu du contenu codé en dur
      const response = await fetch(`http://127.0.0.1:8000/api/courses/${id}/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const courseData = await response.json();
      setCourse(courseData);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors de la récupération du cours:', error);
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    if (!course) return;

    try {
      setEnrolling(true);

      const response = await fetch(`http://127.0.0.1:8000/api/courses/${course.id}/enroll/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setCourse({ ...course, is_enrolled: true, enrollment_date: new Date().toISOString() });
      setEnrolling(false);

      // Recharger les données du cours pour avoir la progression à jour
      fetchCourse();
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      setEnrolling(false);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'intermediate':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'advanced':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;
    }
    return `${mins}min`;
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Accès restreint</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour accéder à ce cours.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement du cours...</p>
        </div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Cours non trouvé</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Le cours demandé n'existe pas ou n'est plus disponible.
          </p>
          <button
            onClick={() => navigate('/courses')}
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Retour aux cours
          </button>
        </div>
      </div>
    );
  }

  return (
    <StudentLayout
      title={course.title}
      subtitle={course.description}
      actions={
        <button
          onClick={() => navigate('/courses')}
          className="flex items-center text-primary-600 hover:text-primary-700 transition-colors"
        >
          <span className="mr-2">←</span>
          Retour aux cours
        </button>
      }
    >
      <div className="space-y-6">
        <div className="max-w-4xl mx-auto">
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <span className="text-4xl mr-4">{course.thumbnail}</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {course.title}
                </h1>
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`}>
                  {course.level_display}
                </span>
              </div>
            </div>
          </div>

          <p className="text-gray-700 dark:text-gray-300 mb-6">
            {course.description}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6">
            <div className="flex items-center">
              <span><strong>Chapitres:</strong> {course.chapters.length}</span>
            </div>
            <div className="flex items-center">
              <span><strong>Leçons:</strong> {course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)}</span>
            </div>
            <div className="flex items-center">
              <span className="mr-2">⏱</span>
              <span><strong>Durée:</strong> {formatDuration(course.estimated_duration)}</span>
            </div>
            <div className="flex items-center">
              <span><strong>Progression:</strong> {course.progress_percentage}%</span>
            </div>
          </div>

          {course.is_enrolled && (
            <div className="mb-6">
              <div className="flex justify-between text-sm mb-2">
                <span className="font-medium">Progression du cours</span>
                <span>{course.progress_percentage}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="bg-primary-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${course.progress_percentage}%` }}
                />
              </div>
            </div>
          )}

          {!course.is_enrolled && (
            <div className="text-center">
              <button
                onClick={handleEnroll}
                disabled={enrolling}
                className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors"
              >
                {enrolling ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2"></div>
                    Inscription...
                  </>
                ) : (
                  'S\'inscrire au cours'
                )}
              </button>
            </div>
          )}
        </motion.div>

        {course.is_enrolled && (
          <div className="space-y-6">
            {course.chapters.map((chapter, chapterIndex) => (
              <motion.div
                key={chapter.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: (chapterIndex + 1) * 0.1 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Chapitre {chapter.order}: {chapter.title}
                  </h2>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {chapter.progress_percentage}% terminé
                  </span>
                </div>

                {chapter.description && (
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    {chapter.description}
                  </p>
                )}

                <div className="mb-4">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${chapter.progress_percentage}%` }}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  {chapter.lessons.map((lesson) => (
                    <div
                      key={lesson.id}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        lesson.is_completed
                          ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700'
                          : lesson.is_accessible
                          ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                          : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'
                      } transition-colors`}
                    >
                      <div className="flex items-center">
                        <span className="text-xl mr-3">
                          {lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'}
                        </span>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {lesson.title}
                          </h4>
                          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <span className="mr-3">{lesson.type_display}</span>
                            <span className="mr-3">⏱{lesson.estimated_duration}min</span>
                            {lesson.time_spent > 0 && (
                              <span>{Math.floor(lesson.time_spent / 60)}min passées</span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {lesson.is_accessible ? (
                          <>
                            <button
                              onClick={() => navigate(`/lessons/${lesson.id}`)}
                              className="bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
                            >
                              {lesson.is_completed ? 'Revoir' : 'Commencer'}
                            </button>
                            {lesson.exercise_id && (
                              <button
                                onClick={() => navigate(`/exercises/${lesson.exercise_id}`)}
                                className="bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
                              >
                                Exercice
                              </button>
                            )}
                          </>
                        ) : (
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Terminez les leçons précédentes
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        )}
        </div>
      </div>
    </StudentLayout>
  );
};

export default CourseDetailPage;
