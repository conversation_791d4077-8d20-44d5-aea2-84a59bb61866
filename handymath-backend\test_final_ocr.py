#!/usr/bin/env python
"""
Test final de validation de la reconnaissance OCR
"""
import os
import sys
import django
from PIL import Image, ImageDraw, ImageFont

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

def create_equation_image(equation, filename):
    """Créer une image d'équation pour les tests"""
    img = Image.new('RGB', (400, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 36)
    except:
        font = ImageFont.load_default()
    
    # Centrer le texte
    bbox = draw.textbbox((0, 0), equation, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (400 - text_width) // 2
    y = (100 - text_height) // 2
    
    draw.text((x, y), equation, fill='black', font=font)
    img.save(filename)
    return img

def test_ocr_pipeline():
    """Test complet du pipeline OCR"""
    print("🧪 TEST FINAL - Pipeline OCR HandyMath")
    print("=" * 50)
    
    # Test des imports
    print("\n1. 📦 Test des imports...")
    try:
        import pytesseract
        import cv2
        from api.utils.ocr import extract_text_from_image, clean_ocr_text, preprocess_image
        print("   ✅ Tous les modules importés avec succès")
    except ImportError as e:
        print(f"   ❌ Erreur d'import: {e}")
        return False
    
    # Test des équations
    print("\n2. 🔢 Test de reconnaissance d'équations...")
    test_equations = [
        ("x + 5 = 10", "Équation linéaire simple"),
        ("2x - 3 = 7", "Équation linéaire avec soustraction"),
        ("x^2 = 16", "Équation quadratique"),
        ("3x + 2y = 12", "Équation à deux variables"),
        ("x/2 + 1 = 4", "Équation avec fraction")
    ]
    
    success_count = 0
    total_tests = len(test_equations)
    
    for i, (equation, description) in enumerate(test_equations, 1):
        print(f"\n   Test {i}: {description}")
        print(f"   Équation: {equation}")
        
        # Créer l'image
        filename = f"test_eq_{i}.png"
        img = create_equation_image(equation, filename)
        
        try:
            # Tester la reconnaissance
            result = extract_text_from_image(img, clean_text=True)
            
            # Vérifier la précision
            expected_clean = equation.replace(" ", "")
            result_clean = result.replace(" ", "")
            
            if expected_clean.lower() == result_clean.lower():
                print(f"   ✅ Succès: '{result}'")
                success_count += 1
            else:
                print(f"   ⚠️  Partiel: Attendu '{equation}', Obtenu '{result}'")
                # Vérifier si les éléments principaux sont présents
                if '=' in result and any(char in result for char in equation if char.isalnum()):
                    success_count += 0.5
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
        
        # Nettoyer
        try:
            os.remove(filename)
        except:
            pass
    
    # Test des corrections automatiques
    print("\n3. 🔧 Test des corrections automatiques...")
    correction_tests = [
        ("x42=9", "x^2=9", "Correction puissance x^2"),
        ("2x43+1=0", "2x4^3+1=0", "Correction puissance x^3"),
        ("x2-4=0", "x^2-4=0", "Correction x^2 simple")
    ]
    
    correction_success = 0
    for dirty, expected, description in correction_tests:
        try:
            cleaned = clean_ocr_text(dirty)
            if cleaned == expected:
                print(f"   ✅ {description}: '{dirty}' → '{cleaned}'")
                correction_success += 1
            else:
                print(f"   ⚠️  {description}: '{dirty}' → '{cleaned}' (attendu: '{expected}')")
        except Exception as e:
            print(f"   ❌ Erreur {description}: {e}")
    
    # Résultats finaux
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS FINAUX")
    print("=" * 50)
    
    recognition_rate = (success_count / total_tests) * 100
    correction_rate = (correction_success / len(correction_tests)) * 100
    
    print(f"🎯 Reconnaissance d'équations: {success_count}/{total_tests} ({recognition_rate:.1f}%)")
    print(f"🔧 Corrections automatiques: {correction_success}/{len(correction_tests)} ({correction_rate:.1f}%)")
    
    overall_success = recognition_rate >= 80 and correction_rate >= 80
    
    if overall_success:
        print("\n🎉 SUCCÈS: La reconnaissance manuscrite fonctionne correctement!")
        print("✅ Le système est prêt pour la production")
    else:
        print("\n⚠️  ATTENTION: Quelques améliorations nécessaires")
        if recognition_rate < 80:
            print("   - Améliorer la reconnaissance de base")
        if correction_rate < 80:
            print("   - Améliorer les corrections automatiques")
    
    return overall_success

def test_frontend_integration():
    """Test de l'intégration frontend"""
    print("\n4. 🌐 Test d'intégration frontend...")
    
    # Vérifier que le composant ImageUpload existe
    try:
        frontend_path = "../handymath-frontend/src/components/ImageUpload.tsx"
        if os.path.exists(frontend_path):
            print("   ✅ Composant ImageUpload.tsx trouvé")
        else:
            print("   ⚠️  Composant ImageUpload.tsx non trouvé")
    except:
        print("   ⚠️  Impossible de vérifier le frontend")
    
    # Vérifier l'endpoint API
    try:
        from api.views import recognize_equation_from_image
        print("   ✅ Endpoint API de reconnaissance trouvé")
    except ImportError:
        print("   ❌ Endpoint API non trouvé")

def main():
    """Fonction principale"""
    print("🚀 DÉMARRAGE DES TESTS FINAUX")
    print("Validation complète de la reconnaissance manuscrite HandyMath")
    
    success = test_ocr_pipeline()
    test_frontend_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎊 VALIDATION RÉUSSIE!")
        print("La reconnaissance manuscrite est opérationnelle.")
        print("\n📋 Prochaines étapes:")
        print("1. Démarrer le serveur Django: python manage.py runserver")
        print("2. Démarrer le frontend React: npm start")
        print("3. Tester l'upload d'images dans l'interface")
    else:
        print("❌ VALIDATION ÉCHOUÉE")
        print("Consultez le rapport détaillé ci-dessus.")
    
    print("\n📄 Rapport détaillé disponible dans: RAPPORT_CORRECTION_OCR.md")

if __name__ == "__main__":
    main()
