{"ast": null, "code": "export var derivativeDocs = {\n  name: 'derivative',\n  category: 'Algebra',\n  syntax: ['derivative(expr, variable)', 'derivative(expr, variable, {simplify: boolean})'],\n  description: 'Takes the derivative of an expression expressed in parser Nodes. The derivative will be taken over the supplied variable in the second parameter. If there are multiple variables in the expression, it will return a partial derivative.',\n  examples: ['derivative(\"2x^3\", \"x\")', 'derivative(\"2x^3\", \"x\", {simplify: false})', 'derivative(\"2x^2 + 3x + 4\", \"x\")', 'derivative(\"sin(2x)\", \"x\")', 'f = parse(\"x^2 + x\")', 'x = parse(\"x\")', 'df = derivative(f, x)', 'df.evaluate({x: 3})'],\n  seealso: ['simplify', 'parse', 'evaluate']\n};", "map": {"version": 3, "names": ["derivativeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/derivative.js"], "sourcesContent": ["export var derivativeDocs = {\n  name: 'derivative',\n  category: 'Algebra',\n  syntax: ['derivative(expr, variable)', 'derivative(expr, variable, {simplify: boolean})'],\n  description: 'Takes the derivative of an expression expressed in parser Nodes. The derivative will be taken over the supplied variable in the second parameter. If there are multiple variables in the expression, it will return a partial derivative.',\n  examples: ['derivative(\"2x^3\", \"x\")', 'derivative(\"2x^3\", \"x\", {simplify: false})', 'derivative(\"2x^2 + 3x + 4\", \"x\")', 'derivative(\"sin(2x)\", \"x\")', 'f = parse(\"x^2 + x\")', 'x = parse(\"x\")', 'df = derivative(f, x)', 'df.evaluate({x: 3})'],\n  seealso: ['simplify', 'parse', 'evaluate']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,4BAA4B,EAAE,iDAAiD,CAAC;EACzFC,WAAW,EAAE,2OAA2O;EACxPC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,4CAA4C,EAAE,kCAAkC,EAAE,4BAA4B,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,qBAAqB,CAAC;EAC/OC,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}