{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createRound } from '../../factoriesAny.js';\nexport var roundDependencies = {\n  BigNumberDependencies,\n  DenseMatrixDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createRound\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "DenseMatrixDependencies", "equalScalarDependencies", "matrixDependencies", "typedDependencies", "zerosDependencies", "createRound", "roundDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRound.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createRound } from '../../factoriesAny.js';\nexport var roundDependencies = {\n  BigNumberDependencies,\n  DenseMatrixDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createRound\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BP,qBAAqB;EACrBC,uBAAuB;EACvBC,uBAAuB;EACvBC,kBAAkB;EAClBC,iBAAiB;EACjBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}