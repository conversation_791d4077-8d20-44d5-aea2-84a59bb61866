{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createReshape } from '../../factoriesAny.js';\nexport var reshapeDependencies = {\n  isIntegerDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createReshape\n};", "map": {"version": 3, "names": ["isIntegerDependencies", "matrixDependencies", "typedDependencies", "createReshape", "reshapeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesReshape.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createReshape } from '../../factoriesAny.js';\nexport var reshapeDependencies = {\n  isIntegerDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createReshape\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,mBAAmB,GAAG;EAC/BJ,qBAAqB;EACrBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}