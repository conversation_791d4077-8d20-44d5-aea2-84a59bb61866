{"ast": null, "code": "export var cschDocs = {\n  name: 'csch',\n  category: 'Trigonometry',\n  syntax: ['csch(x)'],\n  description: 'Compute the hyperbolic cosecant of x in radians. Defined as 1/sinh(x)',\n  examples: ['csch(2)', '1 / sinh(2)'],\n  seealso: ['sech', 'coth', 'sinh']\n};", "map": {"version": 3, "names": ["cschDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/csch.js"], "sourcesContent": ["export var cschDocs = {\n  name: 'csch',\n  category: 'Trigonometry',\n  syntax: ['csch(x)'],\n  description: 'Compute the hyperbolic cosecant of x in radians. Defined as 1/sinh(x)',\n  examples: ['csch(2)', '1 / sinh(2)'],\n  seealso: ['sech', 'coth', 'sinh']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,uEAAuE;EACpFC,QAAQ,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;EACpCC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}