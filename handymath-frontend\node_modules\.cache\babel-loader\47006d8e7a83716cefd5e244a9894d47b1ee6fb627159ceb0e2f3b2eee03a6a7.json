{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { eDependencies } from './dependenciesE.generated.js';\nimport { createUppercaseE } from '../../factoriesAny.js';\nexport var EDependencies = {\n  eDependencies,\n  createUppercaseE\n};", "map": {"version": 3, "names": ["eDependencies", "createUppercaseE", "EDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUppercaseE.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { eDependencies } from './dependenciesE.generated.js';\nimport { createUppercaseE } from '../../factoriesAny.js';\nexport var EDependencies = {\n  eDependencies,\n  createUppercaseE\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,aAAa,GAAG;EACzBF,aAAa;EACbC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}