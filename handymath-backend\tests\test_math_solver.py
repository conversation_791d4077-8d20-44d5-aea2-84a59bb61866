"""
Tests pour le solveur mathématique et les calculs
"""
import pytest
from unittest.mock import patch, MagicMock
from django.urls import reverse
from rest_framework import status
import sympy as sp

from api.math_utils import solve_equation, parse_equation, validate_equation


@pytest.mark.math
@pytest.mark.django_db
class TestMathSolver:
    """Tests pour le solveur d'équations mathématiques"""
    
    def test_solve_linear_equation_simple(self, authenticated_client):
        """Test de résolution d'équation linéaire simple"""
        equation_data = {"equation": "x + 5 = 10"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
        assert 'steps' in response.data
        assert '5' in str(response.data['solution'])
    
    def test_solve_linear_equation_with_coefficients(self, authenticated_client):
        """Test de résolution d'équation linéaire avec coefficients"""
        equation_data = {"equation": "2x + 3 = 11"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
        assert '4' in str(response.data['solution'])
    
    def test_solve_quadratic_equation(self, authenticated_client):
        """Test de résolution d'équation quadratique"""
        equation_data = {"equation": "x^2 - 4 = 0"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
        # Vérifier que les solutions -2 et 2 sont présentes
        solution_str = str(response.data['solution'])
        assert '-2' in solution_str and '2' in solution_str
    
    def test_solve_quadratic_equation_complex(self, authenticated_client):
        """Test de résolution d'équation quadratique complexe"""
        equation_data = {"equation": "x^2 + 5x + 6 = 0"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
        # Solutions attendues: x = -2, x = -3
        solution_str = str(response.data['solution'])
        assert '-2' in solution_str and '-3' in solution_str
    
    def test_solve_equation_no_solution(self, authenticated_client):
        """Test d'équation sans solution"""
        equation_data = {"equation": "x + 1 = x + 2"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
        # Vérifier qu'il n'y a pas de solution
        assert response.data['solution'] == [] or 'no solution' in str(response.data['solution']).lower()
    
    def test_solve_equation_infinite_solutions(self, authenticated_client):
        """Test d'équation avec solutions infinies"""
        equation_data = {"equation": "2x + 4 = 2(x + 2)"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'solution' in response.data
    
    def test_solve_invalid_equation(self, authenticated_client):
        """Test de résolution d'équation invalide"""
        equation_data = {"equation": "invalid equation"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data
    
    def test_solve_empty_equation(self, authenticated_client):
        """Test de résolution d'équation vide"""
        equation_data = {"equation": ""}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_solve_equation_with_steps(self, authenticated_client):
        """Test que les étapes de résolution sont retournées"""
        equation_data = {"equation": "2x + 3 = 11", "show_steps": True}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'steps' in response.data
        assert len(response.data['steps']) > 0
        assert isinstance(response.data['steps'], list)


@pytest.mark.math
@pytest.mark.unit
class TestMathUtilsFunctions:
    """Tests unitaires pour les fonctions utilitaires mathématiques"""
    
    def test_parse_equation_valid(self):
        """Test de parsing d'équation valide"""
        equation = "2x + 3 = 11"
        left, right = parse_equation(equation)
        
        assert left is not None
        assert right is not None
        assert str(left) == "2*x + 3"
        assert str(right) == "11"
    
    def test_parse_equation_invalid(self):
        """Test de parsing d'équation invalide"""
        with pytest.raises(ValueError):
            parse_equation("invalid equation")
    
    def test_validate_equation_valid(self):
        """Test de validation d'équation valide"""
        valid_equations = [
            "x + 1 = 2",
            "2x + 3 = 11",
            "x^2 - 4 = 0",
            "3x - 7 = 14"
        ]
        
        for equation in valid_equations:
            assert validate_equation(equation) is True
    
    def test_validate_equation_invalid(self):
        """Test de validation d'équation invalide"""
        invalid_equations = [
            "",
            "x +",
            "= 5",
            "x + y = z + w + v",  # Trop de variables
            "hello world"
        ]
        
        for equation in invalid_equations:
            assert validate_equation(equation) is False
    
    def test_solve_equation_function_linear(self):
        """Test de la fonction solve_equation pour équations linéaires"""
        result = solve_equation("x + 5 = 10")
        
        assert 'solution' in result
        assert 'steps' in result
        assert result['solution'] == [5]
    
    def test_solve_equation_function_quadratic(self):
        """Test de la fonction solve_equation pour équations quadratiques"""
        result = solve_equation("x^2 - 4 = 0")
        
        assert 'solution' in result
        assert 'steps' in result
        assert set(result['solution']) == {-2, 2}
    
    def test_solve_equation_function_with_fractions(self):
        """Test de résolution d'équation avec fractions"""
        result = solve_equation("x/2 + 3 = 7")
        
        assert 'solution' in result
        assert result['solution'] == [8]
    
    def test_solve_equation_function_with_decimals(self):
        """Test de résolution d'équation avec décimaux"""
        result = solve_equation("0.5x + 1.5 = 3.5")
        
        assert 'solution' in result
        assert result['solution'] == [4]


@pytest.mark.math
@pytest.mark.integration
@pytest.mark.django_db
class TestMathSolverIntegration:
    """Tests d'intégration pour le solveur mathématique"""
    
    def test_solve_multiple_equations_batch(self, authenticated_client, math_test_cases):
        """Test de résolution de plusieurs équations en lot"""
        url = reverse('solve-equation')
        
        for test_case in math_test_cases:
            equation_data = {"equation": test_case["equation"]}
            response = authenticated_client.post(url, equation_data, format='json')
            
            assert response.status_code == status.HTTP_200_OK
            assert 'solution' in response.data
            
            # Vérifier que la solution attendue est dans les résultats
            solution = response.data['solution']
            expected = test_case["expected_solution"]
            
            if isinstance(solution, list) and isinstance(expected, list):
                assert set(solution) == set(expected)
            else:
                assert solution in expected or expected in solution
    
    def test_equation_history_saved(self, authenticated_client, user):
        """Test que l'historique des équations est sauvegardé"""
        equation_data = {"equation": "x + 1 = 5"}
        
        url = reverse('solve-equation')
        response = authenticated_client.post(url, equation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        
        # Vérifier que l'équation est sauvegardée dans l'historique
        history_url = reverse('equation-history')
        history_response = authenticated_client.get(history_url)
        
        assert history_response.status_code == status.HTTP_200_OK
        assert len(history_response.data) > 0
        assert any(eq['equation_text'] == "x + 1 = 5" for eq in history_response.data)
    
    @pytest.mark.slow
    def test_performance_large_equation(self, authenticated_client):
        """Test de performance avec équation complexe"""
        import time
        
        equation_data = {"equation": "x^10 + 5x^9 - 3x^8 + 2x^7 - x^6 + 4x^5 - 2x^4 + x^3 - 3x^2 + 2x - 1 = 0"}
        
        url = reverse('solve-equation')
        start_time = time.time()
        response = authenticated_client.post(url, equation_data, format='json')
        end_time = time.time()
        
        # Le test ne doit pas prendre plus de 10 secondes
        assert (end_time - start_time) < 10
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]


@pytest.mark.math
@pytest.mark.unit
class TestSymPyIntegration:
    """Tests pour l'intégration avec SymPy"""
    
    def test_sympy_solve_linear(self):
        """Test direct de SymPy pour équations linéaires"""
        x = sp.Symbol('x')
        equation = sp.Eq(2*x + 3, 11)
        solution = sp.solve(equation, x)
        
        assert solution == [4]
    
    def test_sympy_solve_quadratic(self):
        """Test direct de SymPy pour équations quadratiques"""
        x = sp.Symbol('x')
        equation = sp.Eq(x**2 - 4, 0)
        solution = sp.solve(equation, x)
        
        assert set(solution) == {-2, 2}
    
    def test_sympy_expand_expression(self):
        """Test d'expansion d'expression avec SymPy"""
        x = sp.Symbol('x')
        expr = (x + 2)**2
        expanded = sp.expand(expr)
        
        assert expanded == x**2 + 4*x + 4
    
    def test_sympy_factor_expression(self):
        """Test de factorisation avec SymPy"""
        x = sp.Symbol('x')
        expr = x**2 + 5*x + 6
        factored = sp.factor(expr)
        
        assert factored == (x + 2)*(x + 3)
    
    def test_sympy_simplify_expression(self):
        """Test de simplification avec SymPy"""
        x = sp.Symbol('x')
        expr = (x**2 - 1)/(x - 1)
        simplified = sp.simplify(expr)
        
        assert simplified == x + 1
