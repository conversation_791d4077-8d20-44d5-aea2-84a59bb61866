from django.db import models
from django.contrib.auth.models import AbstractUser
from django.conf import settings

class User(AbstractUser):
    """
    Modèle utilisateur personnalisé pour HandyMath
    """
    ROLE_CHOICES = (
        ('student', 'Étudiant'),
        ('admin', 'Administrateur'),
    )

    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='student')
    bio = models.TextField(blank=True)
    profile_picture = models.ImageField(upload_to='profile_pics/', blank=True, null=True)

    class Meta:
        verbose_name = 'Utilisateur'
        verbose_name_plural = 'Utilisateurs'

    def __str__(self):
        return self.username

class Course(models.Model):
    """
    Modèle pour les cours
    """
    LEVEL_CHOICES = (
        ('beginner', 'Débutant'),
        ('intermediate', 'Intermédiaire'),
        ('advanced', 'Avancé'),
    )

    STATUS_CHOICES = (
        ('draft', 'Brouillon'),
        ('published', 'Publié'),
        ('archived', 'Archivé'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default='beginner')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='published')
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='unlocks')
    estimated_duration = models.IntegerField(default=60, help_text="Durée estimée en minutes")
    thumbnail = models.CharField(max_length=10, default='📚')  # Emoji pour l'icône
    order = models.IntegerField(default=0)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Cours'
        verbose_name_plural = 'Cours'
        ordering = ['order', 'title']

    def __str__(self):
        return self.title

    def get_progress_for_user(self, user):
        """Calculer le progrès d'un utilisateur dans ce cours"""
        # Compter seulement les leçons publiées
        total_lessons = Lesson.objects.filter(
            chapter__course=self,
            is_published=True
        ).count()

        if total_lessons == 0:
            return 0

        completed_lessons = LessonProgress.objects.filter(
            user=user,
            lesson__chapter__course=self,
            lesson__is_published=True,
            completed=True
        ).count()

        return (completed_lessons / total_lessons) * 100

    def is_accessible_for_user(self, user):
        """Vérifier si l'utilisateur peut accéder à ce cours"""
        # Vérifier les prérequis
        for prerequisite in self.prerequisites.all():
            if prerequisite.get_progress_for_user(user) < 80:  # 80% minimum
                return False
        return True

class Equation(models.Model):
    """
    Modèle pour les équations
    """
    equation_text = models.CharField(max_length=500)
    solution_text = models.TextField(blank=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='equations')
    created_at = models.DateTimeField(auto_now_add=True)
    validated = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Équation'
        verbose_name_plural = 'Équations'

    def __str__(self):
        return self.equation_text

class Exercise(models.Model):
    """
    Modèle pour les exercices
    """
    DIFFICULTY_CHOICES = (
        ('easy', 'Facile'),
        ('medium', 'Moyen'),
        ('hard', 'Difficile'),
    )

    TYPE_CHOICES = (
        ('equation', 'Résolution d\'équation'),
        ('multiple_choice', 'Choix multiple'),
        ('calculation', 'Calcul'),
        ('proof', 'Démonstration'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField()
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='exercises')
    difficulty = models.CharField(max_length=20, choices=DIFFICULTY_CHOICES, default='medium')
    exercise_type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='equation')
    question = models.TextField()
    correct_answer = models.TextField()
    explanation = models.TextField(blank=True)
    points = models.IntegerField(default=10)
    time_limit = models.IntegerField(default=300, help_text="Temps limite en secondes")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Exercice'
        verbose_name_plural = 'Exercices'

    def __str__(self):
        return self.title

class EquationImage(models.Model):
    """
    Modèle pour stocker les images d'équations
    """
    image = models.ImageField(upload_to='equation_images/')
    recognized_text = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='equation_images')

    def __str__(self):
        return f"Image d'équation {self.id}"


class ExerciseChoice(models.Model):
    """
    Modèle pour les choix des exercices à choix multiples
    """
    exercise = models.ForeignKey(Exercise, on_delete=models.CASCADE, related_name='choices')
    choice_text = models.CharField(max_length=200)
    is_correct = models.BooleanField(default=False)
    order = models.IntegerField(default=0)

    class Meta:
        verbose_name = 'Choix d\'exercice'
        verbose_name_plural = 'Choix d\'exercices'
        ordering = ['order']

    def __str__(self):
        return f"{self.exercise.title} - {self.choice_text}"


class ExerciseAttempt(models.Model):
    """
    Modèle pour les tentatives d'exercices
    """
    STATUS_CHOICES = (
        ('in_progress', 'En cours'),
        ('completed', 'Terminé'),
        ('abandoned', 'Abandonné'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='exercise_attempts')
    exercise = models.ForeignKey(Exercise, on_delete=models.CASCADE, related_name='attempts')
    user_answer = models.TextField(blank=True)
    is_correct = models.BooleanField(default=False)
    points_earned = models.IntegerField(default=0)
    time_taken = models.IntegerField(default=0, help_text="Temps pris en secondes")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='in_progress')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Tentative d\'exercice'
        verbose_name_plural = 'Tentatives d\'exercices'
        unique_together = ['user', 'exercise']

    def __str__(self):
        return f"{self.user.username} - {self.exercise.title}"


class Badge(models.Model):
    """
    Modèle pour les badges de gamification
    """
    CATEGORY_CHOICES = (
        ('achievement', 'Accomplissement'),
        ('streak', 'Série'),
        ('mastery', 'Maîtrise'),
        ('social', 'Social'),
        ('special', 'Spécial'),
    )

    name = models.CharField(max_length=100)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='achievement')
    icon = models.CharField(max_length=10, default='🏆')  # Emoji pour l'icône
    points_required = models.IntegerField(default=0)
    exercises_required = models.IntegerField(default=0)
    streak_required = models.IntegerField(default=0)
    difficulty_required = models.CharField(max_length=20, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Badge'
        verbose_name_plural = 'Badges'

    def __str__(self):
        return f"{self.icon} {self.name}"


class UserBadge(models.Model):
    """
    Modèle pour les badges obtenus par les utilisateurs
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='badges')
    badge = models.ForeignKey(Badge, on_delete=models.CASCADE)
    earned_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Badge utilisateur'
        verbose_name_plural = 'Badges utilisateurs'
        unique_together = ['user', 'badge']

    def __str__(self):
        return f"{self.user.username} - {self.badge.name}"


class UserLevel(models.Model):
    """
    Modèle pour le système de niveaux
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='level')
    current_level = models.IntegerField(default=1)
    total_xp = models.IntegerField(default=0)
    xp_to_next_level = models.IntegerField(default=100)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Niveau utilisateur'
        verbose_name_plural = 'Niveaux utilisateurs'

    def __str__(self):
        return f"{self.user.username} - Niveau {self.current_level}"

    def add_xp(self, xp_amount):
        """Ajouter de l'XP et gérer les montées de niveau"""
        self.total_xp += xp_amount

        while self.total_xp >= self.xp_to_next_level:
            self.total_xp -= self.xp_to_next_level
            self.current_level += 1
            # Augmenter l'XP requis pour le niveau suivant (progression exponentielle)
            self.xp_to_next_level = int(100 * (1.2 ** (self.current_level - 1)))

        self.save()
        return self.current_level


class Streak(models.Model):
    """
    Modèle pour les séries de réussite
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='streak')
    current_streak = models.IntegerField(default=0)
    longest_streak = models.IntegerField(default=0)
    last_activity_date = models.DateField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Série utilisateur'
        verbose_name_plural = 'Séries utilisateurs'

    def __str__(self):
        return f"{self.user.username} - Série {self.current_streak}"

    def update_streak(self, success=True):
        """Mettre à jour la série de réussite"""
        from datetime import date, timedelta

        today = date.today()

        if self.last_activity_date:
            days_diff = (today - self.last_activity_date).days

            if days_diff == 0:
                # Même jour, ne pas modifier la série
                return self.current_streak
            elif days_diff == 1 and success:
                # Jour suivant avec succès, continuer la série
                self.current_streak += 1
            elif days_diff > 1 or not success:
                # Interruption de la série
                self.current_streak = 1 if success else 0
        else:
            # Première activité
            self.current_streak = 1 if success else 0

        # Mettre à jour le record
        if self.current_streak > self.longest_streak:
            self.longest_streak = self.current_streak

        self.last_activity_date = today
        self.save()
        return self.current_streak


class Chapter(models.Model):
    """
    Modèle pour les chapitres d'un cours
    """
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='chapters')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    order = models.IntegerField(default=0)
    is_published = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Chapitre'
        verbose_name_plural = 'Chapitres'
        ordering = ['order', 'title']
        unique_together = ['course', 'order']

    def __str__(self):
        return f"{self.course.title} - {self.title}"

    def get_progress_for_user(self, user):
        """Calculer le progrès d'un utilisateur dans ce chapitre"""
        total_lessons = self.lessons.filter(is_published=True).count()
        if total_lessons == 0:
            return 0

        completed_lessons = LessonProgress.objects.filter(
            user=user,
            lesson__chapter=self,
            lesson__is_published=True,
            completed=True
        ).count()

        return (completed_lessons / total_lessons) * 100


class Lesson(models.Model):
    """
    Modèle pour les leçons d'un chapitre
    """
    LESSON_TYPE_CHOICES = (
        ('theory', 'Théorie'),
        ('example', 'Exemple'),
        ('exercise', 'Exercice'),
        ('quiz', 'Quiz'),
        ('video', 'Vidéo'),
    )

    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, related_name='lessons')
    title = models.CharField(max_length=200)
    content = models.TextField()
    lesson_type = models.CharField(max_length=20, choices=LESSON_TYPE_CHOICES, default='theory')
    order = models.IntegerField(default=0)
    estimated_duration = models.IntegerField(default=10, help_text="Durée estimée en minutes")
    is_published = models.BooleanField(default=True)
    exercise = models.ForeignKey(Exercise, on_delete=models.SET_NULL, null=True, blank=True, related_name='lessons')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Leçon'
        verbose_name_plural = 'Leçons'
        ordering = ['order', 'title']
        unique_together = ['chapter', 'order']

    def __str__(self):
        return f"{self.chapter.title} - {self.title}"

    def is_accessible_for_user(self, user):
        """Vérifier si l'utilisateur peut accéder à cette leçon"""
        # Vérifier d'abord si l'utilisateur peut accéder au cours
        if not self.chapter.course.is_accessible_for_user(user):
            return False

        # La première leçon d'un chapitre est toujours accessible
        if self.order == 0:
            return True

        # Vérifier que les leçons précédentes dans le même chapitre sont terminées
        previous_lessons = Lesson.objects.filter(
            chapter=self.chapter,
            order__lt=self.order,
            is_published=True
        ).order_by('order')

        for lesson in previous_lessons:
            progress = LessonProgress.objects.filter(
                user=user,
                lesson=lesson,
                completed=True
            ).exists()
            if not progress:
                return False

        return True


class LessonProgress(models.Model):
    """
    Modèle pour le suivi de progression des leçons
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='lesson_progress')
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='progress')
    completed = models.BooleanField(default=False)
    time_spent = models.IntegerField(default=0, help_text="Temps passé en secondes")
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Progression de leçon'
        verbose_name_plural = 'Progressions de leçons'
        unique_together = ['user', 'lesson']

    def __str__(self):
        return f"{self.user.username} - {self.lesson.title}"

    def mark_completed(self):
        """Marquer la leçon comme terminée"""
        from django.utils import timezone
        self.completed = True
        self.completed_at = timezone.now()
        self.save()


class CourseEnrollment(models.Model):
    """
    Modèle pour l'inscription aux cours
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='enrollments')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrollments')
    enrolled_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Inscription au cours'
        verbose_name_plural = 'Inscriptions aux cours'
        unique_together = ['user', 'course']

    def __str__(self):
        return f"{self.user.username} - {self.course.title}"

    def get_progress_percentage(self):
        """Calculer le pourcentage de progression"""
        return self.course.get_progress_for_user(self.user)

    def mark_completed(self):
        """Marquer le cours comme terminé"""
        from django.utils import timezone
        self.completed_at = timezone.now()
        self.save()


class LearningPath(models.Model):
    """
    Modèle pour les parcours d'apprentissage
    """
    DIFFICULTY_CHOICES = (
        ('beginner', 'Débutant'),
        ('intermediate', 'Intermédiaire'),
        ('advanced', 'Avancé'),
        ('expert', 'Expert'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField()
    difficulty = models.CharField(max_length=20, choices=DIFFICULTY_CHOICES, default='beginner')
    courses = models.ManyToManyField(Course, through='LearningPathCourse', related_name='learning_paths')
    estimated_duration = models.IntegerField(default=300, help_text="Durée estimée en minutes")
    thumbnail = models.CharField(max_length=10, default='🎯')
    is_featured = models.BooleanField(default=False)
    is_published = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Parcours d\'apprentissage'
        verbose_name_plural = 'Parcours d\'apprentissage'

    def __str__(self):
        return self.title

    def get_progress_for_user(self, user):
        """Calculer le progrès d'un utilisateur dans ce parcours"""
        path_courses = self.learningpathcourse_set.all()
        if not path_courses:
            return 0

        total_progress = 0
        for path_course in path_courses:
            course_progress = path_course.course.get_progress_for_user(user)
            total_progress += course_progress

        return total_progress / len(path_courses)


class LearningPathCourse(models.Model):
    """
    Modèle intermédiaire pour l'ordre des cours dans un parcours
    """
    learning_path = models.ForeignKey(LearningPath, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    order = models.IntegerField(default=0)
    is_optional = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Cours du parcours'
        verbose_name_plural = 'Cours des parcours'
        ordering = ['order']
        unique_together = ['learning_path', 'course']

    def __str__(self):
        return f"{self.learning_path.title} - {self.course.title}"


class ContactMessage(models.Model):
    """
    Modèle pour gérer les messages de contact envoyés par les utilisateurs
    """
    CATEGORY_CHOICES = [
        ('question', 'Question générale'),
        ('bug', 'Signaler un bug'),
        ('feature', 'Demande de fonctionnalité'),
        ('help', 'Aide technique'),
        ('other', 'Autre'),
    ]

    STATUS_CHOICES = [
        ('new', 'Nouveau'),
        ('in_progress', 'En cours'),
        ('resolved', 'Résolu'),
        ('closed', 'Fermé'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Basse'),
        ('medium', 'Moyenne'),
        ('high', 'Haute'),
        ('urgent', 'Urgente'),
    ]

    # Informations du message
    name = models.CharField(max_length=100, verbose_name="Nom")
    email = models.EmailField(verbose_name="Email")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='question', verbose_name="Catégorie")
    subject = models.CharField(max_length=200, verbose_name="Sujet")
    message = models.TextField(verbose_name="Message")

    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Dernière modification")

    # Gestion administrative
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new', verbose_name="Statut")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium', verbose_name="Priorité")
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   limit_choices_to={'is_staff': True}, verbose_name="Assigné à")
    admin_notes = models.TextField(blank=True, verbose_name="Notes administrateur")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="Date de résolution")

    # Informations techniques
    user_agent = models.TextField(blank=True, verbose_name="User Agent")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="Adresse IP")

    class Meta:
        verbose_name = "Message de Contact"
        verbose_name_plural = "Messages de Contact"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['category', 'priority']),
        ]

    def __str__(self):
        return f"{self.name} - {self.subject} ({self.get_status_display()})"

    def mark_as_resolved(self, admin_user=None):
        """Marquer le message comme résolu"""
        from django.utils import timezone
        self.status = 'resolved'
        self.resolved_at = timezone.now()
        if admin_user:
            self.assigned_to = admin_user
        self.save()

    def get_priority_color(self):
        """Retourner la couleur selon la priorité"""
        colors = {
            'low': 'green',
            'medium': 'yellow',
            'high': 'orange',
            'urgent': 'red'
        }
        return colors.get(self.priority, 'gray')

    def get_status_color(self):
        """Retourner la couleur selon le statut"""
        colors = {
            'new': 'blue',
            'in_progress': 'yellow',
            'resolved': 'green',
            'closed': 'gray'
        }
        return colors.get(self.status, 'gray')

    @property
    def is_overdue(self):
        """Vérifier si le message est en retard (plus de 48h sans réponse)"""
        from django.utils import timezone
        if self.status in ['resolved', 'closed']:
            return False
        return (timezone.now() - self.created_at).days > 2









