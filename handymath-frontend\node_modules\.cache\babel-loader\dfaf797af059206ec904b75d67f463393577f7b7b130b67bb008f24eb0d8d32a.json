{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createRangeClass } from '../../factoriesAny.js';\nexport var RangeDependencies = {\n  createRangeClass\n};", "map": {"version": 3, "names": ["createRangeClass", "RangeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRangeClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createRangeClass } from '../../factoriesAny.js';\nexport var RangeDependencies = {\n  createRangeClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}