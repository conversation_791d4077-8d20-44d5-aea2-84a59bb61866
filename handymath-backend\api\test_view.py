from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
import traceback
import sys

@csrf_exempt
def test_view(request):
    """
    Une vue simple avec gestion d'erreur explicite pour diagnostiquer les problèmes.
    """
    try:
        # Test simple
        return HttpResponse("Test réussi! Le serveur fonctionne correctement.", content_type="text/plain")
    except Exception as e:
        # Capture et affiche l'erreur complète
        error_info = {
            "error": str(e),
            "traceback": traceback.format_exc()
        }
        return HttpResponse(f"ERREUR: {str(e)}\n\nTRACEBACK:\n{traceback.format_exc()}", 
                           content_type="text/plain", 
                           status=500)
