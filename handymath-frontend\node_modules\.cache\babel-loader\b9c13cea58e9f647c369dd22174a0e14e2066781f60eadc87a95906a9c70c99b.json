{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { FractionDependencies } from './dependenciesFractionClass.generated.js';\nimport { createTyped } from '../../factoriesAny.js';\nexport var typedDependencies = {\n  BigNumberDependencies,\n  ComplexDependencies,\n  DenseMatrixDependencies,\n  FractionDependencies,\n  createTyped\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "ComplexDependencies", "DenseMatrixDependencies", "FractionDependencies", "createTyped", "typedDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesTyped.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { FractionDependencies } from './dependenciesFractionClass.generated.js';\nimport { createTyped } from '../../factoriesAny.js';\nexport var typedDependencies = {\n  BigNumberDependencies,\n  ComplexDependencies,\n  DenseMatrixDependencies,\n  FractionDependencies,\n  createTyped\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BL,qBAAqB;EACrBC,mBAAmB;EACnBC,uBAAuB;EACvBC,oBAAoB;EACpBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}