{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\VisualizerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport * as math from 'mathjs';\nimport StudentLayout from '../components/StudentLayout';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizerPage = () => {\n  _s();\n  const [functionInput, setFunctionInput] = useState('x^2');\n  const [error, setError] = useState(null);\n  const canvasRef = useRef(null);\n\n  // Fonction sécurisée pour évaluer les expressions mathématiques\n  const evaluateFunction = (expr, variables) => {\n    try {\n      // Compiler l'expression une seule fois pour de meilleures performances\n      const compiledExpr = math.compile(expr);\n      return compiledExpr.evaluate(variables);\n    } catch (err) {\n      console.error(\"Erreur d'évaluation:\", err);\n      setError(`Erreur: ${err instanceof Error ? err.message : String(err)}`);\n      return NaN;\n    }\n  };\n\n  // Rendu 2D\n  const renderFunction2D = () => {\n    setError(null);\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    // Nettoyer le canvas existant\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Définir les dimensions du canvas\n    const width = canvas.width;\n    const height = canvas.height;\n\n    // Effacer le canvas\n    ctx.fillStyle = '#f8fafc';\n    ctx.fillRect(0, 0, width, height);\n\n    // Dessiner les axes\n    ctx.strokeStyle = '#94a3b8';\n    ctx.beginPath();\n    ctx.moveTo(0, height / 2);\n    ctx.lineTo(width, height / 2);\n    ctx.moveTo(width / 2, 0);\n    ctx.lineTo(width / 2, height);\n    ctx.stroke();\n\n    // Dessiner la grille\n    ctx.strokeStyle = '#e2e8f0';\n    ctx.beginPath();\n    // Lignes verticales\n    for (let x = 0; x <= width; x += width / 20) {\n      ctx.moveTo(x, 0);\n      ctx.lineTo(x, height);\n    }\n    // Lignes horizontales\n    for (let y = 0; y <= height; y += height / 20) {\n      ctx.moveTo(0, y);\n      ctx.lineTo(width, y);\n    }\n    ctx.stroke();\n\n    // Dessiner la fonction\n    ctx.strokeStyle = '#6366f1';\n    ctx.lineWidth = 2;\n    ctx.beginPath();\n    try {\n      // Échelle: combien de pixels par unité\n      const scaleX = width / 20;\n      const scaleY = height / 20;\n      const offsetX = width / 2;\n      const offsetY = height / 2;\n      let isFirstPoint = true;\n\n      // Dessiner la fonction point par point\n      for (let pixelX = 0; pixelX <= width; pixelX++) {\n        // Convertir les coordonnées du canvas en coordonnées mathématiques\n        const x = (pixelX - offsetX) / scaleX;\n\n        // Évaluer la fonction\n        const y = evaluateFunction(functionInput, {\n          x\n        });\n\n        // Vérifier si y est un nombre valide\n        if (isNaN(y) || !isFinite(y)) continue;\n\n        // Convertir y en coordonnées du canvas\n        const pixelY = offsetY - y * scaleY;\n\n        // Vérifier si le point est dans les limites du canvas\n        if (pixelY < -1000 || pixelY > height + 1000) continue;\n        if (isFirstPoint) {\n          ctx.moveTo(pixelX, pixelY);\n          isFirstPoint = false;\n        } else {\n          ctx.lineTo(pixelX, pixelY);\n        }\n      }\n      ctx.stroke();\n    } catch (error) {\n      console.error(\"Erreur lors de l'évaluation de la fonction:\", error);\n      setError(`Erreur: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  };\n\n  // Effectuer le rendu lorsque la fonction change\n  useEffect(() => {\n    renderFunction2D();\n  }, [functionInput]);\n\n  // Exemples de fonctions\n  const functionExamples = [{\n    label: 'Polynôme',\n    value: 'x^2 + 2*x - 1'\n  }, {\n    label: 'Trigonométrique',\n    value: 'sin(x) * cos(x)'\n  }, {\n    label: 'Exponentielle',\n    value: 'exp(-x^2)'\n  }, {\n    label: 'Logarithmique',\n    value: 'log(abs(x) + 0.1)'\n  }, {\n    label: 'Rationnelle',\n    value: '1 / (1 + x^2)'\n  }];\n  return /*#__PURE__*/_jsxDEV(StudentLayout, {\n    title: \"Visualiseur de fonctions\",\n    subtitle: \"Visualisez et explorez des fonctions math\\xE9matiques\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"function\",\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Entrez votre fonction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"function\",\n              value: functionInput,\n              onChange: e => setFunctionInput(e.target.value),\n              placeholder: \"Ex: x^2, sin(x), cos(x)\",\n              className: \"flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:ring-secondary-500 focus:border-secondary-500 dark:bg-gray-700 dark:text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: renderFunction2D,\n              className: \"bg-secondary-500 hover:bg-secondary-600 text-white font-medium py-3 px-6 rounded-r-lg transition-colors\",\n              children: \"Visualiser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 11\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-red-500 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Exemples de fonctions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2\",\n              children: functionExamples.map((example, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setFunctionInput(example.value),\n                className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600\",\n                children: [example.label, \": \", example.value]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-sm text-gray-600 dark:text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: \"Syntaxe support\\xE9e:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc list-inside mt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Op\\xE9rateurs: +, -, *, /, ^\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Fonctions: sin, cos, tan, exp, log, sqrt, abs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Constantes: pi, e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Variable: x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n              ref: canvasRef,\n              width: \"600\",\n              height: \"400\",\n              className: \"bg-gray-50 dark:bg-gray-900\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold mb-3\",\n            children: \"Informations math\\xE9matiques\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium mb-2\",\n                children: \"Analyse de la fonction\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 dark:text-gray-300\",\n                children: functionInput.includes('log') || functionInput.includes('sqrt') ? 'Attention aux domaines de définition (log, sqrt)' : 'Fonction définie sur ℝ'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium mb-2\",\n                children: \"Conseils\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 dark:text-gray-300\",\n                children: \"Essayez de combiner diff\\xE9rentes fonctions pour obtenir des courbes int\\xE9ressantes.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizerPage, \"IvnkM++wQPSn5ah6971Ns9q+7Hk=\");\n_c = VisualizerPage;\nexport default VisualizerPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "math", "StudentLayout", "jsxDEV", "_jsxDEV", "VisualizerPage", "_s", "functionInput", "setFunctionInput", "error", "setError", "canvasRef", "evaluateFunction", "expr", "variables", "compiledExpr", "compile", "evaluate", "err", "console", "Error", "message", "String", "NaN", "renderFunction2D", "canvas", "current", "ctx", "getContext", "width", "height", "fillStyle", "fillRect", "strokeStyle", "beginPath", "moveTo", "lineTo", "stroke", "x", "y", "lineWidth", "scaleX", "scaleY", "offsetX", "offsetY", "isFirstPoint", "pixelX", "isNaN", "isFinite", "pixelY", "functionExamples", "label", "value", "title", "subtitle", "children", "className", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "onChange", "e", "target", "placeholder", "onClick", "map", "example", "index", "ref", "includes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/VisualizerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport * as math from 'mathjs';\nimport StudentLayout from '../components/StudentLayout';\n\nconst VisualizerPage: React.FC = () => {\n  const [functionInput, setFunctionInput] = useState('x^2');\n  const [error, setError] = useState<string | null>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  // Fonction sécurisée pour évaluer les expressions mathématiques\n  const evaluateFunction = (expr: string, variables: Record<string, number>) => {\n    try {\n      // Compiler l'expression une seule fois pour de meilleures performances\n      const compiledExpr = math.compile(expr);\n      return compiledExpr.evaluate(variables);\n    } catch (err) {\n      console.error(\"Erreur d'évaluation:\", err);\n      setError(`Erreur: ${err instanceof Error ? err.message : String(err)}`);\n      return NaN;\n    }\n  };\n\n  // Rendu 2D\n  const renderFunction2D = () => {\n    setError(null);\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    // Nettoyer le canvas existant\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Définir les dimensions du canvas\n    const width = canvas.width;\n    const height = canvas.height;\n\n    // Effacer le canvas\n    ctx.fillStyle = '#f8fafc';\n    ctx.fillRect(0, 0, width, height);\n\n    // Dessiner les axes\n    ctx.strokeStyle = '#94a3b8';\n    ctx.beginPath();\n    ctx.moveTo(0, height / 2);\n    ctx.lineTo(width, height / 2);\n    ctx.moveTo(width / 2, 0);\n    ctx.lineTo(width / 2, height);\n    ctx.stroke();\n\n    // Dessiner la grille\n    ctx.strokeStyle = '#e2e8f0';\n    ctx.beginPath();\n    // Lignes verticales\n    for (let x = 0; x <= width; x += width / 20) {\n      ctx.moveTo(x, 0);\n      ctx.lineTo(x, height);\n    }\n    // Lignes horizontales\n    for (let y = 0; y <= height; y += height / 20) {\n      ctx.moveTo(0, y);\n      ctx.lineTo(width, y);\n    }\n    ctx.stroke();\n\n    // Dessiner la fonction\n    ctx.strokeStyle = '#6366f1';\n    ctx.lineWidth = 2;\n    ctx.beginPath();\n\n    try {\n      // Échelle: combien de pixels par unité\n      const scaleX = width / 20;\n      const scaleY = height / 20;\n      const offsetX = width / 2;\n      const offsetY = height / 2;\n\n      let isFirstPoint = true;\n\n      // Dessiner la fonction point par point\n      for (let pixelX = 0; pixelX <= width; pixelX++) {\n        // Convertir les coordonnées du canvas en coordonnées mathématiques\n        const x = (pixelX - offsetX) / scaleX;\n\n        // Évaluer la fonction\n        const y = evaluateFunction(functionInput, { x });\n\n        // Vérifier si y est un nombre valide\n        if (isNaN(y) || !isFinite(y)) continue;\n\n        // Convertir y en coordonnées du canvas\n        const pixelY = offsetY - y * scaleY;\n\n        // Vérifier si le point est dans les limites du canvas\n        if (pixelY < -1000 || pixelY > height + 1000) continue;\n\n        if (isFirstPoint) {\n          ctx.moveTo(pixelX, pixelY);\n          isFirstPoint = false;\n        } else {\n          ctx.lineTo(pixelX, pixelY);\n        }\n      }\n      ctx.stroke();\n    } catch (error) {\n      console.error(\"Erreur lors de l'évaluation de la fonction:\", error);\n      setError(`Erreur: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  };\n\n  // Effectuer le rendu lorsque la fonction change\n  useEffect(() => {\n    renderFunction2D();\n  }, [functionInput]);\n\n  // Exemples de fonctions\n  const functionExamples = [\n    { label: 'Polynôme', value: 'x^2 + 2*x - 1' },\n    { label: 'Trigonométrique', value: 'sin(x) * cos(x)' },\n    { label: 'Exponentielle', value: 'exp(-x^2)' },\n    { label: 'Logarithmique', value: 'log(abs(x) + 0.1)' },\n    { label: 'Rationnelle', value: '1 / (1 + x^2)' }\n  ];\n\n  return (\n    <StudentLayout\n      title=\"Visualiseur de fonctions\"\n      subtitle=\"Visualisez et explorez des fonctions mathématiques\"\n    >\n      <div className=\"space-y-6\">\n        <div className=\"max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n        <div className=\"mb-6\">\n          <label htmlFor=\"function\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Entrez votre fonction\n          </label>\n          <div className=\"flex\">\n            <input\n              type=\"text\"\n              id=\"function\"\n              value={functionInput}\n              onChange={(e) => setFunctionInput(e.target.value)}\n              placeholder=\"Ex: x^2, sin(x), cos(x)\"\n              className=\"flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:ring-secondary-500 focus:border-secondary-500 dark:bg-gray-700 dark:text-white\"\n            />\n            <button\n              onClick={renderFunction2D}\n              className=\"bg-secondary-500 hover:bg-secondary-600 text-white font-medium py-3 px-6 rounded-r-lg transition-colors\"\n            >\n              Visualiser\n            </button>\n          </div>\n\n          {error && (\n            <div className=\"mt-2 text-red-500 text-sm\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"mt-4\">\n            <p className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Exemples de fonctions:\n            </p>\n            <div className=\"flex flex-wrap gap-2\">\n              {functionExamples.map((example, index) => (\n                <button\n                  key={index}\n                  onClick={() => setFunctionInput(example.value)}\n                  className=\"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600\"\n                >\n                  {example.label}: {example.value}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"mt-4 text-sm text-gray-600 dark:text-gray-400\">\n            <p className=\"font-medium\">Syntaxe supportée:</p>\n            <ul className=\"list-disc list-inside mt-1\">\n              <li>Opérateurs: +, -, *, /, ^</li>\n              <li>Fonctions: sin, cos, tan, exp, log, sqrt, abs</li>\n              <li>Constantes: pi, e</li>\n              <li>Variable: x</li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"flex justify-center\">\n          <div className=\"border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden\">\n            <canvas\n              ref={canvasRef}\n              width=\"600\"\n              height=\"400\"\n              className=\"bg-gray-50 dark:bg-gray-900\"\n            />\n          </div>\n        </div>\n\n        <div className=\"mt-6\">\n          <h3 className=\"text-xl font-semibold mb-3\">Informations mathématiques</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"p-4 bg-gray-100 dark:bg-gray-700 rounded-lg\">\n              <h4 className=\"font-medium mb-2\">Analyse de la fonction</h4>\n              <p className=\"text-gray-700 dark:text-gray-300\">\n                {functionInput.includes('log') || functionInput.includes('sqrt')\n                  ? 'Attention aux domaines de définition (log, sqrt)'\n                  : 'Fonction définie sur ℝ'}\n              </p>\n            </div>\n            <div className=\"p-4 bg-gray-100 dark:bg-gray-700 rounded-lg\">\n              <h4 className=\"font-medium mb-2\">Conseils</h4>\n              <p className=\"text-gray-700 dark:text-gray-300\">\n                Essayez de combiner différentes fonctions pour obtenir des courbes intéressantes.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n      </div>\n    </StudentLayout>\n  );\n};\n\nexport default VisualizerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAE1D,OAAO,KAAKC,IAAI,MAAM,QAAQ;AAC9B,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMa,SAAS,GAAGX,MAAM,CAAoB,IAAI,CAAC;;EAEjD;EACA,MAAMY,gBAAgB,GAAGA,CAACC,IAAY,EAAEC,SAAiC,KAAK;IAC5E,IAAI;MACF;MACA,MAAMC,YAAY,GAAGd,IAAI,CAACe,OAAO,CAACH,IAAI,CAAC;MACvC,OAAOE,YAAY,CAACE,QAAQ,CAACH,SAAS,CAAC;IACzC,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,sBAAsB,EAAES,GAAG,CAAC;MAC1CR,QAAQ,CAAC,WAAWQ,GAAG,YAAYE,KAAK,GAAGF,GAAG,CAACG,OAAO,GAAGC,MAAM,CAACJ,GAAG,CAAC,EAAE,CAAC;MACvE,OAAOK,GAAG;IACZ;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,QAAQ,CAAC,IAAI,CAAC;IACd,MAAMe,MAAM,GAAGd,SAAS,CAACe,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;;IAEb;IACA,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;;IAEV;IACA,MAAME,KAAK,GAAGJ,MAAM,CAACI,KAAK;IAC1B,MAAMC,MAAM,GAAGL,MAAM,CAACK,MAAM;;IAE5B;IACAH,GAAG,CAACI,SAAS,GAAG,SAAS;IACzBJ,GAAG,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEH,KAAK,EAAEC,MAAM,CAAC;;IAEjC;IACAH,GAAG,CAACM,WAAW,GAAG,SAAS;IAC3BN,GAAG,CAACO,SAAS,CAAC,CAAC;IACfP,GAAG,CAACQ,MAAM,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC;IACzBH,GAAG,CAACS,MAAM,CAACP,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAC7BH,GAAG,CAACQ,MAAM,CAACN,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IACxBF,GAAG,CAACS,MAAM,CAACP,KAAK,GAAG,CAAC,EAAEC,MAAM,CAAC;IAC7BH,GAAG,CAACU,MAAM,CAAC,CAAC;;IAEZ;IACAV,GAAG,CAACM,WAAW,GAAG,SAAS;IAC3BN,GAAG,CAACO,SAAS,CAAC,CAAC;IACf;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIT,KAAK,EAAES,CAAC,IAAIT,KAAK,GAAG,EAAE,EAAE;MAC3CF,GAAG,CAACQ,MAAM,CAACG,CAAC,EAAE,CAAC,CAAC;MAChBX,GAAG,CAACS,MAAM,CAACE,CAAC,EAAER,MAAM,CAAC;IACvB;IACA;IACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIT,MAAM,EAAES,CAAC,IAAIT,MAAM,GAAG,EAAE,EAAE;MAC7CH,GAAG,CAACQ,MAAM,CAAC,CAAC,EAAEI,CAAC,CAAC;MAChBZ,GAAG,CAACS,MAAM,CAACP,KAAK,EAAEU,CAAC,CAAC;IACtB;IACAZ,GAAG,CAACU,MAAM,CAAC,CAAC;;IAEZ;IACAV,GAAG,CAACM,WAAW,GAAG,SAAS;IAC3BN,GAAG,CAACa,SAAS,GAAG,CAAC;IACjBb,GAAG,CAACO,SAAS,CAAC,CAAC;IAEf,IAAI;MACF;MACA,MAAMO,MAAM,GAAGZ,KAAK,GAAG,EAAE;MACzB,MAAMa,MAAM,GAAGZ,MAAM,GAAG,EAAE;MAC1B,MAAMa,OAAO,GAAGd,KAAK,GAAG,CAAC;MACzB,MAAMe,OAAO,GAAGd,MAAM,GAAG,CAAC;MAE1B,IAAIe,YAAY,GAAG,IAAI;;MAEvB;MACA,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAIjB,KAAK,EAAEiB,MAAM,EAAE,EAAE;QAC9C;QACA,MAAMR,CAAC,GAAG,CAACQ,MAAM,GAAGH,OAAO,IAAIF,MAAM;;QAErC;QACA,MAAMF,CAAC,GAAG3B,gBAAgB,CAACL,aAAa,EAAE;UAAE+B;QAAE,CAAC,CAAC;;QAEhD;QACA,IAAIS,KAAK,CAACR,CAAC,CAAC,IAAI,CAACS,QAAQ,CAACT,CAAC,CAAC,EAAE;;QAE9B;QACA,MAAMU,MAAM,GAAGL,OAAO,GAAGL,CAAC,GAAGG,MAAM;;QAEnC;QACA,IAAIO,MAAM,GAAG,CAAC,IAAI,IAAIA,MAAM,GAAGnB,MAAM,GAAG,IAAI,EAAE;QAE9C,IAAIe,YAAY,EAAE;UAChBlB,GAAG,CAACQ,MAAM,CAACW,MAAM,EAAEG,MAAM,CAAC;UAC1BJ,YAAY,GAAG,KAAK;QACtB,CAAC,MAAM;UACLlB,GAAG,CAACS,MAAM,CAACU,MAAM,EAAEG,MAAM,CAAC;QAC5B;MACF;MACAtB,GAAG,CAACU,MAAM,CAAC,CAAC;IACd,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEC,QAAQ,CAAC,WAAWD,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACY,OAAO,GAAGC,MAAM,CAACb,KAAK,CAAC,EAAE,CAAC;IAC/E;EACF,CAAC;;EAED;EACAV,SAAS,CAAC,MAAM;IACdyB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM2C,gBAAgB,GAAG,CACvB;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC7C;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC9C;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAoB,CAAC,EACtD;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACjD;EAED,oBACEhD,OAAA,CAACF,aAAa;IACZmD,KAAK,EAAC,0BAA0B;IAChCC,QAAQ,EAAC,uDAAoD;IAAAC,QAAA,eAE7DnD,OAAA;MAAKoD,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxBnD,OAAA;QAAKoD,SAAS,EAAC,sEAAsE;QAAAD,QAAA,gBACrFnD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACnBnD,OAAA;YAAOqD,OAAO,EAAC,UAAU;YAACD,SAAS,EAAC,iEAAiE;YAAAD,QAAA,EAAC;UAEtG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBnD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACbX,KAAK,EAAE7C,aAAc;cACrByD,QAAQ,EAAGC,CAAC,IAAKzD,gBAAgB,CAACyD,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;cAClDe,WAAW,EAAC,yBAAyB;cACrCX,SAAS,EAAC;YAA0J;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrK,CAAC,eACFzD,OAAA;cACEgE,OAAO,EAAE5C,gBAAiB;cAC1BgC,SAAS,EAAC,yGAAyG;cAAAD,QAAA,EACpH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELpD,KAAK,iBACJL,OAAA;YAAKoD,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EACvC9C;UAAK;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBnD,OAAA;cAAGoD,SAAS,EAAC,2DAA2D;cAAAD,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzD,OAAA;cAAKoD,SAAS,EAAC,sBAAsB;cAAAD,QAAA,EAClCL,gBAAgB,CAACmB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnCnE,OAAA;gBAEEgE,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAAC8D,OAAO,CAAClB,KAAK,CAAE;gBAC/CI,SAAS,EAAC,iGAAiG;gBAAAD,QAAA,GAE1Ge,OAAO,CAACnB,KAAK,EAAC,IAAE,EAACmB,OAAO,CAAClB,KAAK;cAAA,GAJ1BmB,KAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAKoD,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DnD,OAAA;cAAGoD,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjDzD,OAAA;cAAIoD,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACxCnD,OAAA;gBAAAmD,QAAA,EAAI;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCzD,OAAA;gBAAAmD,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDzD,OAAA;gBAAAmD,QAAA,EAAI;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BzD,OAAA;gBAAAmD,QAAA,EAAI;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKoD,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClCnD,OAAA;YAAKoD,SAAS,EAAC,wEAAwE;YAAAD,QAAA,eACrFnD,OAAA;cACEoE,GAAG,EAAE7D,SAAU;cACfkB,KAAK,EAAC,KAAK;cACXC,MAAM,EAAC,KAAK;cACZ0B,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACnBnD,OAAA;YAAIoD,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EzD,OAAA;YAAKoD,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDnD,OAAA;cAAKoD,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1DnD,OAAA;gBAAIoD,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DzD,OAAA;gBAAGoD,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAC5ChD,aAAa,CAACkE,QAAQ,CAAC,KAAK,CAAC,IAAIlE,aAAa,CAACkE,QAAQ,CAAC,MAAM,CAAC,GAC5D,kDAAkD,GAClD;cAAwB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1DnD,OAAA;gBAAIoD,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CzD,OAAA;gBAAGoD,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAC;cAEhD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACvD,EAAA,CAvNID,cAAwB;AAAAqE,EAAA,GAAxBrE,cAAwB;AAyN9B,eAAeA,cAAc;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}