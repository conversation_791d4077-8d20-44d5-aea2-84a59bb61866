{"ast": null, "code": "export var sinhDocs = {\n  name: 'sinh',\n  category: 'Trigonometry',\n  syntax: ['sinh(x)'],\n  description: 'Compute the hyperbolic sine of x in radians.',\n  examples: ['sinh(0.5)'],\n  seealso: ['cosh', 'tanh']\n};", "map": {"version": 3, "names": ["sinhDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/sinh.js"], "sourcesContent": ["export var sinhDocs = {\n  name: 'sinh',\n  category: 'Trigonometry',\n  syntax: ['sinh(x)'],\n  description: 'Compute the hyperbolic sine of x in radians.',\n  examples: ['sinh(0.5)'],\n  seealso: ['cosh', 'tanh']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,CAAC,WAAW,CAAC;EACvBC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}