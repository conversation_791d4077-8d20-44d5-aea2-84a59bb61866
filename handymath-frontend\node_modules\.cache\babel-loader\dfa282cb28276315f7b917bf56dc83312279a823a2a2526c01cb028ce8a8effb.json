{"ast": null, "code": "/**\n * Build a bigint logarithm function from a number logarithm,\n * still returning a number. The idea is that 15 hexadecimal digits\n * (60 bits) saturates the mantissa of the log, and each additional hex\n * digit effectively just adds the log of 16 to the resulting value. So\n * convert the most significant 15 hex digits to a number and take its\n * log, and then add the log of 16 for each additional hex digit that\n * was in the bigint.\n * For negative numbers (complex logarithms), following the bignum\n * implementation, it just downgrades to number and uses the complex result.\n * @param {number} log16  the log of 16\n * @param {(number) -> number} numberLog  the logarithm function for numbers\n * @param {ConfigurationObject} config  the mathjs configuration\n * @param {(number) -> Complex} cplx  the associated Complex log\n * @returns {(bigint) -> number}   the corresponding logarithm for bigints\n */\nexport function promoteLogarithm(log16, numberLog, config, cplx) {\n  return function (b) {\n    if (b > 0 || config.predictable) {\n      if (b <= 0) return NaN;\n      var s = b.toString(16);\n      var s15 = s.substring(0, 15);\n      return log16 * (s.length - s15.length) + numberLog(Number('0x' + s15));\n    }\n    return cplx(b.toNumber());\n  };\n}", "map": {"version": 3, "names": ["promoteLogarithm", "log16", "numberLog", "config", "cplx", "b", "predictable", "NaN", "s", "toString", "s15", "substring", "length", "Number", "toNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/bigint.js"], "sourcesContent": ["/**\n * Build a bigint logarithm function from a number logarithm,\n * still returning a number. The idea is that 15 hexadecimal digits\n * (60 bits) saturates the mantissa of the log, and each additional hex\n * digit effectively just adds the log of 16 to the resulting value. So\n * convert the most significant 15 hex digits to a number and take its\n * log, and then add the log of 16 for each additional hex digit that\n * was in the bigint.\n * For negative numbers (complex logarithms), following the bignum\n * implementation, it just downgrades to number and uses the complex result.\n * @param {number} log16  the log of 16\n * @param {(number) -> number} numberLog  the logarithm function for numbers\n * @param {ConfigurationObject} config  the mathjs configuration\n * @param {(number) -> Complex} cplx  the associated Complex log\n * @returns {(bigint) -> number}   the corresponding logarithm for bigints\n */\nexport function promoteLogarithm(log16, numberLog, config, cplx) {\n  return function (b) {\n    if (b > 0 || config.predictable) {\n      if (b <= 0) return NaN;\n      var s = b.toString(16);\n      var s15 = s.substring(0, 15);\n      return log16 * (s.length - s15.length) + numberLog(Number('0x' + s15));\n    }\n    return cplx(b.toNumber());\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAC/D,OAAO,UAAUC,CAAC,EAAE;IAClB,IAAIA,CAAC,GAAG,CAAC,IAAIF,MAAM,CAACG,WAAW,EAAE;MAC/B,IAAID,CAAC,IAAI,CAAC,EAAE,OAAOE,GAAG;MACtB,IAAIC,CAAC,GAAGH,CAAC,CAACI,QAAQ,CAAC,EAAE,CAAC;MACtB,IAAIC,GAAG,GAAGF,CAAC,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MAC5B,OAAOV,KAAK,IAAIO,CAAC,CAACI,MAAM,GAAGF,GAAG,CAACE,MAAM,CAAC,GAAGV,SAAS,CAACW,MAAM,CAAC,IAAI,GAAGH,GAAG,CAAC,CAAC;IACxE;IACA,OAAON,IAAI,CAACC,CAAC,CAACS,QAAQ,CAAC,CAAC,CAAC;EAC3B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}