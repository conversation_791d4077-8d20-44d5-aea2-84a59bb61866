import React from 'react';
import { Link, useLocation } from 'react-router-dom';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: string;
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items, className = '' }) => {
  const location = useLocation();
  
  // Génération automatique des breadcrumbs basée sur l'URL si aucun item n'est fourni
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(segment => segment !== '');
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Accueil', path: '/', icon: '🏠' }
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Mapping des segments vers des labels lisibles
      const segmentLabels: { [key: string]: string } = {
        'admin': 'Administration',
        'dashboard': 'Tableau de bord',
        'users': 'Utilisateurs',
        'courses': 'Cours',
        'exercises': 'Exercices',
        'analytics': 'Analytics',
        'etudiant': 'Étudiant',
        'solver': 'Résolveur',
        'visualizer': 'Visualiseur',
        'progress': 'Progression',
        'profile': 'Profil',
        'settings': 'Paramètres',
        'contact': 'Contact',
        'about': 'À propos',
        'login': 'Connexion',
        'register': 'Inscription'
      };

      const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
      
      // Ne pas ajouter de lien pour le dernier segment (page actuelle)
      const isLast = index === pathSegments.length - 1;
      
      breadcrumbs.push({
        label,
        path: isLast ? undefined : currentPath,
        icon: getIconForSegment(segment)
      });
    });

    return breadcrumbs;
  };

  const getIconForSegment = (segment: string): string => {
    const icons: { [key: string]: string } = {
      'admin': '👑',
      'dashboard': '📊',
      'users': '👥',
      'courses': '📚',
      'exercises': '📝',
      'analytics': '📈',
      'etudiant': '🎓',
      'solver': '🧮',
      'visualizer': '📊',
      'progress': '📈',
      'profile': '👤',
      'settings': '⚙️',
      'contact': '📞',
      'about': 'ℹ️',
      'login': '🔐',
      'register': '📝'
    };
    
    return icons[segment] || '';
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  if (breadcrumbItems.length <= 1) {
    return null; // Ne pas afficher les breadcrumbs s'il n'y a qu'un seul élément
  }

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <svg
                className="w-4 h-4 text-gray-400 mx-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}
            
            {item.path ? (
              <Link
                to={item.path}
                className="flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              >
                {item.icon && <span className="mr-1">{item.icon}</span>}
                {item.label}
              </Link>
            ) : (
              <span className="flex items-center text-gray-900 dark:text-white font-medium">
                {item.icon && <span className="mr-1">{item.icon}</span>}
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
