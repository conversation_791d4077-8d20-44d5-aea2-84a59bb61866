{"ast": null, "code": "export var absDocs = {\n  name: 'abs',\n  category: 'Arithmetic',\n  syntax: ['abs(x)'],\n  description: 'Compute the absolute value.',\n  examples: ['abs(3.5)', 'abs(-4.2)'],\n  seealso: ['sign']\n};", "map": {"version": 3, "names": ["absDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/abs.js"], "sourcesContent": ["export var absDocs = {\n  name: 'abs',\n  category: 'Arithmetic',\n  syntax: ['abs(x)'],\n  description: 'Compute the absolute value.',\n  examples: ['abs(3.5)', 'abs(-4.2)'],\n  seealso: ['sign']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,6BAA6B;EAC1CC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;EACnCC,OAAO,EAAE,CAAC,MAAM;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}