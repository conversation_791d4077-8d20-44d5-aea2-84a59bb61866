{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromColumns';\nvar dependencies = ['typed', 'matrix', 'flatten', 'size'];\nexport var createMatrixFromColumns = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    flatten,\n    size\n  } = _ref;\n  /**\n   * Create a dense matrix from vectors as individual columns.\n   * If you pass row vectors, they will be transposed (but not conjugated!)\n   *\n   * Syntax:\n   *\n   *    math.matrixFromColumns(...arr)\n   *    math.matrixFromColumns(col1, col2)\n   *    math.matrixFromColumns(col1, col2, col3)\n   *\n   * Examples:\n   *\n   *    math.matrixFromColumns([1, 2, 3], [[4],[5],[6]])\n   *    math.matrixFromColumns(...vectors)\n   *\n   * See also:\n   *\n   *    matrix, matrixFromRows, matrixFromFunction, zeros\n   *\n   * @param {... Array | Matrix} cols Multiple columns\n   * @return { number[][] | Matrix } if at least one of the arguments is an array, an array will be returned\n   */\n  return typed(name, {\n    '...Array': function Array(arr) {\n      return _createArray(arr);\n    },\n    '...Matrix': function Matrix(arr) {\n      return matrix(_createArray(arr.map(m => m.toArray())));\n    }\n\n    // TODO implement this properly for SparseMatrix\n  });\n  function _createArray(arr) {\n    if (arr.length === 0) throw new TypeError('At least one column is needed to construct a matrix.');\n    var N = checkVectorTypeAndReturnLength(arr[0]);\n\n    // create an array with empty rows\n    var result = [];\n    for (var i = 0; i < N; i++) {\n      result[i] = [];\n    }\n\n    // loop columns\n    for (var col of arr) {\n      var colLength = checkVectorTypeAndReturnLength(col);\n      if (colLength !== N) {\n        throw new TypeError('The vectors had different length: ' + (N | 0) + ' ≠ ' + (colLength | 0));\n      }\n      var f = flatten(col);\n\n      // push a value to each row\n      for (var _i = 0; _i < N; _i++) {\n        result[_i].push(f[_i]);\n      }\n    }\n    return result;\n  }\n  function checkVectorTypeAndReturnLength(vec) {\n    var s = size(vec);\n    if (s.length === 1) {\n      // 1D vector\n      return s[0];\n    } else if (s.length === 2) {\n      // 2D vector\n      if (s[0] === 1) {\n        // row vector\n        return s[1];\n      } else if (s[1] === 1) {\n        // col vector\n        return s[0];\n      } else {\n        throw new TypeError('At least one of the arguments is not a vector.');\n      }\n    } else {\n      throw new TypeError('Only one- or two-dimensional vectors are supported.');\n    }\n  }\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatrixFromColumns", "_ref", "typed", "matrix", "flatten", "size", "Array", "arr", "_createArray", "Matrix", "map", "m", "toArray", "length", "TypeError", "N", "checkVectorTypeAndReturnLength", "result", "i", "col", "co<PERSON><PERSON><PERSON><PERSON>", "f", "_i", "push", "vec", "s"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/matrix/matrixFromColumns.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromColumns';\nvar dependencies = ['typed', 'matrix', 'flatten', 'size'];\nexport var createMatrixFromColumns = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    flatten,\n    size\n  } = _ref;\n  /**\n   * Create a dense matrix from vectors as individual columns.\n   * If you pass row vectors, they will be transposed (but not conjugated!)\n   *\n   * Syntax:\n   *\n   *    math.matrixFromColumns(...arr)\n   *    math.matrixFromColumns(col1, col2)\n   *    math.matrixFromColumns(col1, col2, col3)\n   *\n   * Examples:\n   *\n   *    math.matrixFromColumns([1, 2, 3], [[4],[5],[6]])\n   *    math.matrixFromColumns(...vectors)\n   *\n   * See also:\n   *\n   *    matrix, matrixFromRows, matrixFromFunction, zeros\n   *\n   * @param {... Array | Matrix} cols Multiple columns\n   * @return { number[][] | Matrix } if at least one of the arguments is an array, an array will be returned\n   */\n  return typed(name, {\n    '...Array': function Array(arr) {\n      return _createArray(arr);\n    },\n    '...Matrix': function Matrix(arr) {\n      return matrix(_createArray(arr.map(m => m.toArray())));\n    }\n\n    // TODO implement this properly for SparseMatrix\n  });\n  function _createArray(arr) {\n    if (arr.length === 0) throw new TypeError('At least one column is needed to construct a matrix.');\n    var N = checkVectorTypeAndReturnLength(arr[0]);\n\n    // create an array with empty rows\n    var result = [];\n    for (var i = 0; i < N; i++) {\n      result[i] = [];\n    }\n\n    // loop columns\n    for (var col of arr) {\n      var colLength = checkVectorTypeAndReturnLength(col);\n      if (colLength !== N) {\n        throw new TypeError('The vectors had different length: ' + (N | 0) + ' ≠ ' + (colLength | 0));\n      }\n      var f = flatten(col);\n\n      // push a value to each row\n      for (var _i = 0; _i < N; _i++) {\n        result[_i].push(f[_i]);\n      }\n    }\n    return result;\n  }\n  function checkVectorTypeAndReturnLength(vec) {\n    var s = size(vec);\n    if (s.length === 1) {\n      // 1D vector\n      return s[0];\n    } else if (s.length === 2) {\n      // 2D vector\n      if (s[0] === 1) {\n        // row vector\n        return s[1];\n      } else if (s[1] === 1) {\n        // col vector\n        return s[0];\n      } else {\n        throw new TypeError('At least one of the arguments is not a vector.');\n      }\n    } else {\n      throw new TypeError('Only one- or two-dimensional vectors are supported.');\n    }\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,IAAIC,IAAI,GAAG,mBAAmB;AAC9B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;AACzD,OAAO,IAAIC,uBAAuB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACtF,IAAI;IACFC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGJ,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjB,UAAU,EAAE,SAASQ,KAAKA,CAACC,GAAG,EAAE;MAC9B,OAAOC,YAAY,CAACD,GAAG,CAAC;IAC1B,CAAC;IACD,WAAW,EAAE,SAASE,MAAMA,CAACF,GAAG,EAAE;MAChC,OAAOJ,MAAM,CAACK,YAAY,CAACD,GAAG,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD;;IAEA;EACF,CAAC,CAAC;EACF,SAASJ,YAAYA,CAACD,GAAG,EAAE;IACzB,IAAIA,GAAG,CAACM,MAAM,KAAK,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,sDAAsD,CAAC;IACjG,IAAIC,CAAC,GAAGC,8BAA8B,CAACT,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAIU,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1BD,MAAM,CAACC,CAAC,CAAC,GAAG,EAAE;IAChB;;IAEA;IACA,KAAK,IAAIC,GAAG,IAAIZ,GAAG,EAAE;MACnB,IAAIa,SAAS,GAAGJ,8BAA8B,CAACG,GAAG,CAAC;MACnD,IAAIC,SAAS,KAAKL,CAAC,EAAE;QACnB,MAAM,IAAID,SAAS,CAAC,oCAAoC,IAAIC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,IAAIK,SAAS,GAAG,CAAC,CAAC,CAAC;MAC/F;MACA,IAAIC,CAAC,GAAGjB,OAAO,CAACe,GAAG,CAAC;;MAEpB;MACA,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,CAAC,EAAEO,EAAE,EAAE,EAAE;QAC7BL,MAAM,CAACK,EAAE,CAAC,CAACC,IAAI,CAACF,CAAC,CAACC,EAAE,CAAC,CAAC;MACxB;IACF;IACA,OAAOL,MAAM;EACf;EACA,SAASD,8BAA8BA,CAACQ,GAAG,EAAE;IAC3C,IAAIC,CAAC,GAAGpB,IAAI,CAACmB,GAAG,CAAC;IACjB,IAAIC,CAAC,CAACZ,MAAM,KAAK,CAAC,EAAE;MAClB;MACA,OAAOY,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,MAAM,IAAIA,CAAC,CAACZ,MAAM,KAAK,CAAC,EAAE;MACzB;MACA,IAAIY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACd;QACA,OAAOA,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,MAAM,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACrB;QACA,OAAOA,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,MAAM;QACL,MAAM,IAAIX,SAAS,CAAC,gDAAgD,CAAC;MACvE;IACF,CAAC,MAAM;MACL,MAAM,IAAIA,SAAS,CAAC,qDAAqD,CAAC;IAC5E;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}