{"ast": null, "code": "export var combinationsDocs = {\n  name: 'combinations',\n  category: 'Probability',\n  syntax: ['combinations(n, k)'],\n  description: 'Compute the number of combinations of n items taken k at a time',\n  examples: ['combinations(7, 5)'],\n  seealso: ['combinationsWithRep', 'permutations', 'factorial']\n};", "map": {"version": 3, "names": ["combinationsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/combinations.js"], "sourcesContent": ["export var combinationsDocs = {\n  name: 'combinations',\n  category: 'Probability',\n  syntax: ['combinations(n, k)'],\n  description: 'Compute the number of combinations of n items taken k at a time',\n  examples: ['combinations(7, 5)'],\n  seealso: ['combinationsWithRep', 'permutations', 'factorial']\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,oBAAoB,CAAC;EAC9BC,WAAW,EAAE,iEAAiE;EAC9EC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;EAChCC,OAAO,EAAE,CAAC,qBAAqB,EAAE,cAAc,EAAE,WAAW;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}