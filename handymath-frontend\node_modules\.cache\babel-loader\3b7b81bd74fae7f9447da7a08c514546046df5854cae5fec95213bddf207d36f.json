{"ast": null, "code": "export var isPositiveDocs = {\n  name: 'isPositive',\n  category: 'Utils',\n  syntax: ['isPositive(x)'],\n  description: 'Test whether a value is positive: larger than zero.',\n  examples: ['isPositive(2)', 'isPositive(0)', 'isPositive(-4)', 'isPositive([3, 0.5, -2])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isZero']\n};", "map": {"version": 3, "names": ["isPositiveDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isPositive.js"], "sourcesContent": ["export var isPositiveDocs = {\n  name: 'isPositive',\n  category: 'Utils',\n  syntax: ['isPositive(x)'],\n  description: 'Test whether a value is positive: larger than zero.',\n  examples: ['isPositive(2)', 'isPositive(0)', 'isPositive(-4)', 'isPositive([3, 0.5, -2])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isZero']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,eAAe,CAAC;EACzBC,WAAW,EAAE,qDAAqD;EAClEC,QAAQ,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,0BAA0B,CAAC;EAC1FC,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}