{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSmaller } from '../../factoriesAny.js';\nexport var smallerDependencies = {\n  DenseMatrixDependencies,\n  SparseMatrixDependencies,\n  bignumberDependencies,\n  concatDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createSmaller\n};", "map": {"version": 3, "names": ["DenseMatrixDependencies", "SparseMatrixDependencies", "bignumberDependencies", "concatDependencies", "matrixDependencies", "typedDependencies", "createSmaller", "smallerDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSmaller.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSmaller } from '../../factoriesAny.js';\nexport var smallerDependencies = {\n  DenseMatrixDependencies,\n  SparseMatrixDependencies,\n  bignumberDependencies,\n  concatDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createSmaller\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,mBAAmB,GAAG;EAC/BP,uBAAuB;EACvBC,wBAAwB;EACxBC,qBAAqB;EACrBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}