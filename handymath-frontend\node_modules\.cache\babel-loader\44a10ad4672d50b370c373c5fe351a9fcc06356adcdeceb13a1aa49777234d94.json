{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { unaryMinusNumber } from '../../plain/number/index.js';\nvar name = 'unaryMinus';\nvar dependencies = ['typed'];\nexport var createUnaryMinus = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Inverse the sign of a value, apply a unary minus operation.\n   *\n   * For matrices, the function is evaluated element wise. Boolean values and\n   * strings will be converted to a number. For complex numbers, both real and\n   * complex value are inverted.\n   *\n   * Syntax:\n   *\n   *    math.unaryMinus(x)\n   *\n   * Examples:\n   *\n   *    math.unaryMinus(3.5)      // returns -3.5\n   *    math.unaryMinus(-4.2)     // returns 4.2\n   *\n   * See also:\n   *\n   *    add, subtract, unaryPlus\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit | Array | Matrix} x Number to be inverted.\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit | Array | Matrix} Returns the value with inverted sign.\n   */\n  return typed(name, {\n    number: unaryMinusNumber,\n    'Complex | BigNumber | Fraction': x => x.neg(),\n    bigint: x => -x,\n    Unit: typed.referToSelf(self => x => {\n      var res = x.clone();\n      res.value = typed.find(self, res.valueType())(x.value);\n      return res;\n    }),\n    // deep map collection, skip zeros since unaryMinus(0) = 0\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self, true))\n\n    // TODO: add support for string\n  });\n});", "map": {"version": 3, "names": ["factory", "deepMap", "unaryMinusNumber", "name", "dependencies", "createUnaryMinus", "_ref", "typed", "number", "x", "neg", "bigint", "Unit", "referToSelf", "self", "res", "clone", "value", "find", "valueType"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/arithmetic/unaryMinus.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { unaryMinusNumber } from '../../plain/number/index.js';\nvar name = 'unaryMinus';\nvar dependencies = ['typed'];\nexport var createUnaryMinus = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Inverse the sign of a value, apply a unary minus operation.\n   *\n   * For matrices, the function is evaluated element wise. Boolean values and\n   * strings will be converted to a number. For complex numbers, both real and\n   * complex value are inverted.\n   *\n   * Syntax:\n   *\n   *    math.unaryMinus(x)\n   *\n   * Examples:\n   *\n   *    math.unaryMinus(3.5)      // returns -3.5\n   *    math.unaryMinus(-4.2)     // returns 4.2\n   *\n   * See also:\n   *\n   *    add, subtract, unaryPlus\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit | Array | Matrix} x Number to be inverted.\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit | Array | Matrix} Returns the value with inverted sign.\n   */\n  return typed(name, {\n    number: unaryMinusNumber,\n    'Complex | BigNumber | Fraction': x => x.neg(),\n    bigint: x => -x,\n    Unit: typed.referToSelf(self => x => {\n      var res = x.clone();\n      res.value = typed.find(self, res.valueType())(x.value);\n      return res;\n    }),\n    // deep map collection, skip zeros since unaryMinus(0) = 0\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self, true))\n\n    // TODO: add support for string\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,IAAIC,IAAI,GAAG,YAAY;AACvB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,gBAAgB,GAAG,eAAeL,OAAO,CAACG,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC/E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBK,MAAM,EAAEN,gBAAgB;IACxB,gCAAgC,EAAEO,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC;IAC9CC,MAAM,EAAEF,CAAC,IAAI,CAACA,CAAC;IACfG,IAAI,EAAEL,KAAK,CAACM,WAAW,CAACC,IAAI,IAAIL,CAAC,IAAI;MACnC,IAAIM,GAAG,GAAGN,CAAC,CAACO,KAAK,CAAC,CAAC;MACnBD,GAAG,CAACE,KAAK,GAAGV,KAAK,CAACW,IAAI,CAACJ,IAAI,EAAEC,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC,CAACV,CAAC,CAACQ,KAAK,CAAC;MACtD,OAAOF,GAAG;IACZ,CAAC,CAAC;IACF;IACA,gBAAgB,EAAER,KAAK,CAACM,WAAW,CAACC,IAAI,IAAIL,CAAC,IAAIR,OAAO,CAACQ,CAAC,EAAEK,IAAI,EAAE,IAAI,CAAC;;IAEvE;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}