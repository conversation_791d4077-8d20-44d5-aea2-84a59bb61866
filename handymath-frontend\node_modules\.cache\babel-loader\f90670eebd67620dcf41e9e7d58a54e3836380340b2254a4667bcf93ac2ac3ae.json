{"ast": null, "code": "export var sqrtDocs = {\n  name: 'sqrt',\n  category: 'Arithmetic',\n  syntax: ['sqrt(x)'],\n  description: 'Compute the square root value. If x = y * y, then y is the square root of x.',\n  examples: ['sqrt(25)', '5 * 5', 'sqrt(-1)'],\n  seealso: ['square', 'sqrtm', 'multiply', 'nthRoot', 'nthRoots', 'pow']\n};", "map": {"version": 3, "names": ["sqrtDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/sqrt.js"], "sourcesContent": ["export var sqrtDocs = {\n  name: 'sqrt',\n  category: 'Arithmetic',\n  syntax: ['sqrt(x)'],\n  description: 'Compute the square root value. If x = y * y, then y is the square root of x.',\n  examples: ['sqrt(25)', '5 * 5', 'sqrt(-1)'],\n  seealso: ['square', 'sqrtm', 'multiply', 'nthRoot', 'nthRoots', 'pow']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,8EAA8E;EAC3FC,QAAQ,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;EAC3CC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}