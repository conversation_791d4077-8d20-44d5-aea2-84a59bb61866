{"ast": null, "code": "export var parseDocs = {\n  name: 'parse',\n  category: 'Expression',\n  syntax: ['parse(expr)', 'parse(expr, options)', 'parse([expr1, expr2, expr3, ...])', 'parse([expr1, expr2, expr3, ...], options)'],\n  description: 'Parse an expression. Returns a node tree, which can be evaluated by invoking node.evaluate() or transformed into a functional object via node.compile().',\n  examples: ['node1 = parse(\"sqrt(3^2 + 4^2)\")', 'node1.evaluate()', 'code1 = node1.compile()', 'code1.evaluate()', 'scope = {a: 3, b: 4}', 'node2 = parse(\"a * b\")', 'node2.evaluate(scope)', 'code2 = node2.compile()', 'code2.evaluate(scope)'],\n  seealso: ['parser', 'evaluate', 'compile']\n};", "map": {"version": 3, "names": ["parseDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/expression/parse.js"], "sourcesContent": ["export var parseDocs = {\n  name: 'parse',\n  category: 'Expression',\n  syntax: ['parse(expr)', 'parse(expr, options)', 'parse([expr1, expr2, expr3, ...])', 'parse([expr1, expr2, expr3, ...], options)'],\n  description: 'Parse an expression. Returns a node tree, which can be evaluated by invoking node.evaluate() or transformed into a functional object via node.compile().',\n  examples: ['node1 = parse(\"sqrt(3^2 + 4^2)\")', 'node1.evaluate()', 'code1 = node1.compile()', 'code1.evaluate()', 'scope = {a: 3, b: 4}', 'node2 = parse(\"a * b\")', 'node2.evaluate(scope)', 'code2 = node2.compile()', 'code2.evaluate(scope)'],\n  seealso: ['parser', 'evaluate', 'compile']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,aAAa,EAAE,sBAAsB,EAAE,mCAAmC,EAAE,4CAA4C,CAAC;EAClIC,WAAW,EAAE,0JAA0J;EACvKC,QAAQ,EAAE,CAAC,kCAAkC,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,uBAAuB,EAAE,yBAAyB,EAAE,uBAAuB,CAAC;EAChPC,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}