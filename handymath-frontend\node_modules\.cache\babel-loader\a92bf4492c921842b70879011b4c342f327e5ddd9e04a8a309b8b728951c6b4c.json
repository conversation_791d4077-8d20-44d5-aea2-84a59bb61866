{"ast": null, "code": "export var schurDocs = {\n  name: 'schur',\n  category: 'Algebra',\n  syntax: ['schur(A)'],\n  description: 'Performs a real Schur decomposition of the real matrix A = UTU\\'',\n  examples: ['schur([[1, 0], [-4, 3]])', 'A = [[1, 0], [-4, 3]]', 'schur(A)'],\n  seealso: ['lyap', 'sylvester']\n};", "map": {"version": 3, "names": ["schur<PERSON><PERSON><PERSON>", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/schur.js"], "sourcesContent": ["export var schurDocs = {\n  name: 'schur',\n  category: 'Algebra',\n  syntax: ['schur(A)'],\n  description: 'Performs a real Schur decomposition of the real matrix A = UTU\\'',\n  examples: ['schur([[1, 0], [-4, 3]])', 'A = [[1, 0], [-4, 3]]', 'schur(A)'],\n  seealso: ['lyap', 'sylvester']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,CAAC,0BAA0B,EAAE,uBAAuB,EAAE,UAAU,CAAC;EAC3EC,OAAO,EAAE,CAAC,MAAM,EAAE,WAAW;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}