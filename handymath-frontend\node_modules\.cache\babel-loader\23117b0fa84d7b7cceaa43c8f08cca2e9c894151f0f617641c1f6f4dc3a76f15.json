{"ast": null, "code": "export var concatDocs = {\n  name: 'concat',\n  category: 'Matrix',\n  syntax: ['concat(A, B, C, ...)', 'concat(A, B, C, ..., dim)'],\n  description: 'Concatenate matrices. By default, the matrices are concatenated by the last dimension. The dimension on which to concatenate can be provided as last argument.',\n  examples: ['A = [1, 2; 5, 6]', 'B = [3, 4; 7, 8]', 'concat(A, B)', 'concat(A, B, 1)', 'concat(A, B, 2)'],\n  seealso: ['det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["concatDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/concat.js"], "sourcesContent": ["export var concatDocs = {\n  name: 'concat',\n  category: 'Matrix',\n  syntax: ['concat(A, B, C, ...)', 'concat(A, B, C, ..., dim)'],\n  description: 'Concatenate matrices. By default, the matrices are concatenated by the last dimension. The dimension on which to concatenate can be provided as last argument.',\n  examples: ['A = [1, 2; 5, 6]', 'B = [3, 4; 7, 8]', 'concat(A, B)', 'concat(A, B, 1)', 'concat(A, B, 2)'],\n  seealso: ['det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,sBAAsB,EAAE,2BAA2B,CAAC;EAC7DC,WAAW,EAAE,gKAAgK;EAC7KC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;EACxGC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AACzH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}