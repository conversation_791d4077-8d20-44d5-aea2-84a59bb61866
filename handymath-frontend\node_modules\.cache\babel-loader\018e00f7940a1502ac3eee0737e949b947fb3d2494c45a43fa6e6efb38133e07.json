{"ast": null, "code": "export var sortDocs = {\n  name: 'sort',\n  category: 'Matrix',\n  syntax: ['sort(x)', 'sort(x, compare)'],\n  description: 'Sort the items in a matrix. Compare can be a string \"asc\", \"desc\", \"natural\", or a custom sort function.',\n  examples: ['sort([5, 10, 1])', 'sort([\"C\", \"B\", \"A\", \"D\"], \"natural\")', 'sortByLength(a, b) = size(a)[1] - size(b)[1]', 'sort([\"Langdon\", \"Tom\", \"Sara\"], sortByLength)', 'sort([\"10\", \"1\", \"2\"], \"natural\")'],\n  seealso: ['map', 'filter', 'forEach']\n};", "map": {"version": 3, "names": ["sortDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/sort.js"], "sourcesContent": ["export var sortDocs = {\n  name: 'sort',\n  category: 'Matrix',\n  syntax: ['sort(x)', 'sort(x, compare)'],\n  description: 'Sort the items in a matrix. Compare can be a string \"asc\", \"desc\", \"natural\", or a custom sort function.',\n  examples: ['sort([5, 10, 1])', 'sort([\"C\", \"B\", \"A\", \"D\"], \"natural\")', 'sortByLength(a, b) = size(a)[1] - size(b)[1]', 'sort([\"Langdon\", \"Tom\", \"Sara\"], sortByLength)', 'sort([\"10\", \"1\", \"2\"], \"natural\")'],\n  seealso: ['map', 'filter', 'forEach']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;EACvCC,WAAW,EAAE,0GAA0G;EACvHC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,uCAAuC,EAAE,8CAA8C,EAAE,gDAAgD,EAAE,mCAAmC,CAAC;EAC9MC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}