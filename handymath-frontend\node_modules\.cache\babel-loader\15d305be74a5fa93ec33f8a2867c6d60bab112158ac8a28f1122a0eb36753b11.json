{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { isNaNNumber } from '../../plain/number/index.js';\nvar name = 'isNaN';\nvar dependencies = ['typed'];\nexport var createIsNaN = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is NaN (not a number).\n   * The function supports types `number`, `BigNumber`, `Fraction`, `Unit` and `Complex`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isNaN(x)\n   *\n   * Examples:\n   *\n   *    math.isNaN(3)                     // returns false\n   *    math.isNaN(NaN)                   // returns true\n   *    math.isNaN(0)                     // returns false\n   *    math.isNaN(math.bignumber(NaN))   // returns true\n   *    math.isNaN(math.bignumber(0))     // returns false\n   *    math.isNaN(math.fraction(-2, 5))  // returns false\n   *    math.isNaN('-2')                  // returns false\n   *    math.isNaN([2, 0, -3, NaN])       // returns [false, false, false, true]\n   *\n   * See also:\n   *\n   *    isNumeric, isNegative, isPositive, isZero, isInteger\n   *\n   * @param {number | BigNumber | bigint | Fraction | Unit | Array | Matrix} x  Value to be tested\n   * @return {boolean}  Returns true when `x` is NaN.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: isNaNNumber,\n    BigNumber: function BigNumber(x) {\n      return x.isNaN();\n    },\n    bigint: function bigint(x) {\n      return false;\n    },\n    Fraction: function Fraction(x) {\n      return false;\n    },\n    Complex: function Complex(x) {\n      return x.isNaN();\n    },\n    Unit: function Unit(x) {\n      return Number.isNaN(x.value);\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "factory", "isNaNNumber", "name", "dependencies", "createIsNaN", "_ref", "typed", "number", "BigNumber", "x", "isNaN", "bigint", "Fraction", "Complex", "Unit", "Number", "value", "referToSelf", "self"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/isNaN.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { isNaNNumber } from '../../plain/number/index.js';\nvar name = 'isNaN';\nvar dependencies = ['typed'];\nexport var createIsNaN = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is NaN (not a number).\n   * The function supports types `number`, `BigNumber`, `Fraction`, `Unit` and `Complex`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isNaN(x)\n   *\n   * Examples:\n   *\n   *    math.isNaN(3)                     // returns false\n   *    math.isNaN(NaN)                   // returns true\n   *    math.isNaN(0)                     // returns false\n   *    math.isNaN(math.bignumber(NaN))   // returns true\n   *    math.isNaN(math.bignumber(0))     // returns false\n   *    math.isNaN(math.fraction(-2, 5))  // returns false\n   *    math.isNaN('-2')                  // returns false\n   *    math.isNaN([2, 0, -3, NaN])       // returns [false, false, false, true]\n   *\n   * See also:\n   *\n   *    isNumeric, isNegative, isPositive, isZero, isInteger\n   *\n   * @param {number | BigNumber | bigint | Fraction | Unit | Array | Matrix} x  Value to be tested\n   * @return {boolean}  Returns true when `x` is NaN.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: isNaNNumber,\n    BigNumber: function BigNumber(x) {\n      return x.isNaN();\n    },\n    bigint: function bigint(x) {\n      return false;\n    },\n    Fraction: function Fraction(x) {\n      return false;\n    },\n    Complex: function Complex(x) {\n      return x.isNaN();\n    },\n    Unit: function Unit(x) {\n      return Number.isNaN(x.value);\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,IAAIC,IAAI,GAAG,OAAO;AAClB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,WAAW,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC1E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBK,MAAM,EAAEN,WAAW;IACnBO,SAAS,EAAE,SAASA,SAASA,CAACC,CAAC,EAAE;MAC/B,OAAOA,CAAC,CAACC,KAAK,CAAC,CAAC;IAClB,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACF,CAAC,EAAE;MACzB,OAAO,KAAK;IACd,CAAC;IACDG,QAAQ,EAAE,SAASA,QAAQA,CAACH,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd,CAAC;IACDI,OAAO,EAAE,SAASA,OAAOA,CAACJ,CAAC,EAAE;MAC3B,OAAOA,CAAC,CAACC,KAAK,CAAC,CAAC;IAClB,CAAC;IACDI,IAAI,EAAE,SAASA,IAAIA,CAACL,CAAC,EAAE;MACrB,OAAOM,MAAM,CAACL,KAAK,CAACD,CAAC,CAACO,KAAK,CAAC;IAC9B,CAAC;IACD,gBAAgB,EAAEV,KAAK,CAACW,WAAW,CAACC,IAAI,IAAIT,CAAC,IAAIV,OAAO,CAACU,CAAC,EAAES,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}