{"ast": null, "code": "import { isBigNumber, isNumber, isObject } from './is.js';\n\n/**\n * @typedef {{sign: '+' | '-' | '', coefficients: number[], exponent: number}} SplitValue\n */\n\n/**\n * Check if a number is integer\n * @param {number | boolean} value\n * @return {boolean} isInteger\n */\nexport function isInteger(value) {\n  if (typeof value === 'boolean') {\n    return true;\n  }\n  return isFinite(value) ? value === Math.round(value) : false;\n}\n\n/**\n * Ensure the number type is compatible with the provided value.\n * If not, return 'number' instead.\n *\n * For example:\n *\n *     safeNumberType('2.3', { number: 'bigint', numberFallback: 'number' })\n *\n * will return 'number' and not 'bigint' because trying to create a bigint with\n * value 2.3 would throw an exception.\n *\n * @param {string} numberStr\n * @param {{\n *   number: 'number' | 'BigNumber' | 'bigint' | 'Fraction'\n *   numberFallback: 'number' | 'BigNumber'\n * }} config\n * @returns {'number' | 'BigNumber' | 'bigint' | 'Fraction'}\n */\nexport function safeNumberType(numberStr, config) {\n  if (config.number === 'bigint') {\n    try {\n      BigInt(numberStr);\n    } catch (_unused) {\n      return config.numberFallback;\n    }\n  }\n  return config.number;\n}\n\n/**\n * Calculate the sign of a number\n * @param {number} x\n * @returns {number}\n */\nexport var sign = Math.sign || function (x) {\n  if (x > 0) {\n    return 1;\n  } else if (x < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\n/**\n * Calculate the base-2 logarithm of a number\n * @param {number} x\n * @returns {number}\n */\nexport var log2 = Math.log2 || function log2(x) {\n  return Math.log(x) / Math.LN2;\n};\n\n/**\n * Calculate the base-10 logarithm of a number\n * @param {number} x\n * @returns {number}\n */\nexport var log10 = Math.log10 || function log10(x) {\n  return Math.log(x) / Math.LN10;\n};\n\n/**\n * Calculate the natural logarithm of a number + 1\n * @param {number} x\n * @returns {number}\n */\nexport var log1p = Math.log1p || function (x) {\n  return Math.log(x + 1);\n};\n\n/**\n * Calculate cubic root for a number\n *\n * Code from es6-shim.js:\n *   https://github.com/paulmillr/es6-shim/blob/master/es6-shim.js#L1564-L1577\n *\n * @param {number} x\n * @returns {number} Returns the cubic root of x\n */\nexport var cbrt = Math.cbrt || function cbrt(x) {\n  if (x === 0) {\n    return x;\n  }\n  var negate = x < 0;\n  var result;\n  if (negate) {\n    x = -x;\n  }\n  if (isFinite(x)) {\n    result = Math.exp(Math.log(x) / 3);\n    // from https://en.wikipedia.org/wiki/Cube_root#Numerical_methods\n    result = (x / (result * result) + 2 * result) / 3;\n  } else {\n    result = x;\n  }\n  return negate ? -result : result;\n};\n\n/**\n * Calculates exponentiation minus 1\n * @param {number} x\n * @return {number} res\n */\nexport var expm1 = Math.expm1 || function expm1(x) {\n  return x >= 2e-4 || x <= -2e-4 ? Math.exp(x) - 1 : x + x * x / 2 + x * x * x / 6;\n};\n\n/**\n * Formats a number in a given base\n * @param {number} n\n * @param {number} base\n * @param {number} size\n * @returns {string}\n */\nfunction formatNumberToBase(n, base, size) {\n  var prefixes = {\n    2: '0b',\n    8: '0o',\n    16: '0x'\n  };\n  var prefix = prefixes[base];\n  var suffix = '';\n  if (size) {\n    if (size < 1) {\n      throw new Error('size must be in greater than 0');\n    }\n    if (!isInteger(size)) {\n      throw new Error('size must be an integer');\n    }\n    if (n > 2 ** (size - 1) - 1 || n < -(2 ** (size - 1))) {\n      throw new Error(\"Value must be in range [-2^\".concat(size - 1, \", 2^\").concat(size - 1, \"-1]\"));\n    }\n    if (!isInteger(n)) {\n      throw new Error('Value must be an integer');\n    }\n    if (n < 0) {\n      n = n + 2 ** size;\n    }\n    suffix = \"i\".concat(size);\n  }\n  var sign = '';\n  if (n < 0) {\n    n = -n;\n    sign = '-';\n  }\n  return \"\".concat(sign).concat(prefix).concat(n.toString(base)).concat(suffix);\n}\n\n/**\n * Convert a number to a formatted string representation.\n *\n * Syntax:\n *\n *    format(value)\n *    format(value, options)\n *    format(value, precision)\n *    format(value, fn)\n *\n * Where:\n *\n *    {number} value   The value to be formatted\n *    {Object} options An object with formatting options. Available options:\n *                     {string} notation\n *                         Number notation. Choose from:\n *                         'fixed'          Always use regular number notation.\n *                                          For example '123.40' and '14000000'\n *                         'exponential'    Always use exponential notation.\n *                                          For example '1.234e+2' and '1.4e+7'\n *                         'engineering'    Always use engineering notation.\n *                                          For example '123.4e+0' and '14.0e+6'\n *                         'auto' (default) Regular number notation for numbers\n *                                          having an absolute value between\n *                                          `lowerExp` and `upperExp` bounds, and\n *                                          uses exponential notation elsewhere.\n *                                          Lower bound is included, upper bound\n *                                          is excluded.\n *                                          For example '123.4' and '1.4e7'.\n *                         'bin', 'oct, or\n *                         'hex'            Format the number using binary, octal,\n *                                          or hexadecimal notation.\n *                                          For example '0b1101' and '0x10fe'.\n *                     {number} wordSize    The word size in bits to use for formatting\n *                                          in binary, octal, or hexadecimal notation.\n *                                          To be used only with 'bin', 'oct', or 'hex'\n *                                          values for 'notation' option. When this option\n *                                          is defined the value is formatted as a signed\n *                                          twos complement integer of the given word size\n *                                          and the size suffix is appended to the output.\n *                                          For example\n *                                          format(-1, {notation: 'hex', wordSize: 8}) === '0xffi8'.\n *                                          Default value is undefined.\n *                     {number} precision   A number between 0 and 16 to round\n *                                          the digits of the number.\n *                                          In case of notations 'exponential',\n *                                          'engineering', and 'auto',\n *                                          `precision` defines the total\n *                                          number of significant digits returned.\n *                                          In case of notation 'fixed',\n *                                          `precision` defines the number of\n *                                          significant digits after the decimal\n *                                          point.\n *                                          `precision` is undefined by default,\n *                                          not rounding any digits.\n *                     {number} lowerExp    Exponent determining the lower boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `-3`.\n *                     {number} upperExp    Exponent determining the upper boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `5`.\n *    {Function} fn    A custom formatting function. Can be used to override the\n *                     built-in notations. Function `fn` is called with `value` as\n *                     parameter and must return a string. Is useful for example to\n *                     format all values inside a matrix in a particular way.\n *\n * Examples:\n *\n *    format(6.4)                                        // '6.4'\n *    format(1240000)                                    // '1.24e6'\n *    format(1/3)                                        // '0.3333333333333333'\n *    format(1/3, 3)                                     // '0.333'\n *    format(21385, 2)                                   // '21000'\n *    format(12.071, {notation: 'fixed'})                // '12'\n *    format(2.3,    {notation: 'fixed', precision: 2})  // '2.30'\n *    format(52.8,   {notation: 'exponential'})          // '5.28e+1'\n *    format(12345678, {notation: 'engineering'})        // '12.345678e+6'\n *\n * @param {number} value\n * @param {Object | Function | number} [options]\n * @return {string} str The formatted value\n */\nexport function format(value, options) {\n  if (typeof options === 'function') {\n    // handle format(value, fn)\n    return options(value);\n  }\n\n  // handle special cases\n  if (value === Infinity) {\n    return 'Infinity';\n  } else if (value === -Infinity) {\n    return '-Infinity';\n  } else if (isNaN(value)) {\n    return 'NaN';\n  }\n  var {\n    notation,\n    precision,\n    wordSize\n  } = normalizeFormatOptions(options);\n\n  // handle the various notations\n  switch (notation) {\n    case 'fixed':\n      return toFixed(value, precision);\n    case 'exponential':\n      return toExponential(value, precision);\n    case 'engineering':\n      return toEngineering(value, precision);\n    case 'bin':\n      return formatNumberToBase(value, 2, wordSize);\n    case 'oct':\n      return formatNumberToBase(value, 8, wordSize);\n    case 'hex':\n      return formatNumberToBase(value, 16, wordSize);\n    case 'auto':\n      // remove trailing zeros after the decimal point\n      return toPrecision(value, precision, options).replace(/((\\.\\d*?)(0+))($|e)/, function () {\n        var digits = arguments[2];\n        var e = arguments[4];\n        return digits !== '.' ? digits + e : e;\n      });\n    default:\n      throw new Error('Unknown notation \"' + notation + '\". ' + 'Choose \"auto\", \"exponential\", \"fixed\", \"bin\", \"oct\", or \"hex.');\n  }\n}\n\n/**\n * Normalize format options into an object:\n *   {\n *     notation: string,\n *     precision: number | undefined,\n *     wordSize: number | undefined\n *   }\n */\nexport function normalizeFormatOptions(options) {\n  // default values for options\n  var notation = 'auto';\n  var precision;\n  var wordSize;\n  if (options !== undefined) {\n    if (isNumber(options)) {\n      precision = options;\n    } else if (isBigNumber(options)) {\n      precision = options.toNumber();\n    } else if (isObject(options)) {\n      if (options.precision !== undefined) {\n        precision = _toNumberOrThrow(options.precision, () => {\n          throw new Error('Option \"precision\" must be a number or BigNumber');\n        });\n      }\n      if (options.wordSize !== undefined) {\n        wordSize = _toNumberOrThrow(options.wordSize, () => {\n          throw new Error('Option \"wordSize\" must be a number or BigNumber');\n        });\n      }\n      if (options.notation) {\n        notation = options.notation;\n      }\n    } else {\n      throw new Error('Unsupported type of options, number, BigNumber, or object expected');\n    }\n  }\n  return {\n    notation,\n    precision,\n    wordSize\n  };\n}\n\n/**\n * Split a number into sign, coefficients, and exponent\n * @param {number | string} value\n * @return {SplitValue}\n *              Returns an object containing sign, coefficients, and exponent\n */\nexport function splitNumber(value) {\n  // parse the input value\n  var match = String(value).toLowerCase().match(/^(-?)(\\d+\\.?\\d*)(e([+-]?\\d+))?$/);\n  if (!match) {\n    throw new SyntaxError('Invalid number ' + value);\n  }\n  var sign = match[1];\n  var digits = match[2];\n  var exponent = parseFloat(match[4] || '0');\n  var dot = digits.indexOf('.');\n  exponent += dot !== -1 ? dot - 1 : digits.length - 1;\n  var coefficients = digits.replace('.', '') // remove the dot (must be removed before removing leading zeros)\n  .replace(/^0*/, function (zeros) {\n    // remove leading zeros, add their count to the exponent\n    exponent -= zeros.length;\n    return '';\n  }).replace(/0*$/, '') // remove trailing zeros\n  .split('').map(function (d) {\n    return parseInt(d);\n  });\n  if (coefficients.length === 0) {\n    coefficients.push(0);\n    exponent++;\n  }\n  return {\n    sign,\n    coefficients,\n    exponent\n  };\n}\n\n/**\n * Format a number in engineering notation. Like '1.23e+6', '2.3e+0', '3.500e-3'\n * @param {number | string} value\n * @param {number} [precision]        Optional number of significant figures to return.\n */\nexport function toEngineering(value, precision) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n  var split = splitNumber(value);\n  var rounded = roundDigits(split, precision);\n  var e = rounded.exponent;\n  var c = rounded.coefficients;\n\n  // find nearest lower multiple of 3 for exponent\n  var newExp = e % 3 === 0 ? e : e < 0 ? e - 3 - e % 3 : e - e % 3;\n  if (isNumber(precision)) {\n    // add zeroes to give correct sig figs\n    while (precision > c.length || e - newExp + 1 > c.length) {\n      c.push(0);\n    }\n  } else {\n    // concatenate coefficients with necessary zeros\n    // add zeros if necessary (for example: 1e+8 -> 100e+6)\n    var missingZeros = Math.abs(e - newExp) - (c.length - 1);\n    for (var i = 0; i < missingZeros; i++) {\n      c.push(0);\n    }\n  }\n\n  // find difference in exponents\n  var expDiff = Math.abs(e - newExp);\n  var decimalIdx = 1;\n\n  // push decimal index over by expDiff times\n  while (expDiff > 0) {\n    decimalIdx++;\n    expDiff--;\n  }\n\n  // if all coefficient values are zero after the decimal point and precision is unset, don't add a decimal value.\n  // otherwise concat with the rest of the coefficients\n  var decimals = c.slice(decimalIdx).join('');\n  var decimalVal = isNumber(precision) && decimals.length || decimals.match(/[1-9]/) ? '.' + decimals : '';\n  var str = c.slice(0, decimalIdx).join('') + decimalVal + 'e' + (e >= 0 ? '+' : '') + newExp.toString();\n  return rounded.sign + str;\n}\n\n/**\n * Format a number with fixed notation.\n * @param {number | string} value\n * @param {number} [precision=undefined]  Optional number of decimals after the\n *                                        decimal point. null by default.\n */\nexport function toFixed(value, precision) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n  var splitValue = splitNumber(value);\n  var rounded = typeof precision === 'number' ? roundDigits(splitValue, splitValue.exponent + 1 + precision) : splitValue;\n  var c = rounded.coefficients;\n  var p = rounded.exponent + 1; // exponent may have changed\n\n  // append zeros if needed\n  var pp = p + (precision || 0);\n  if (c.length < pp) {\n    c = c.concat(zeros(pp - c.length));\n  }\n\n  // prepend zeros if needed\n  if (p < 0) {\n    c = zeros(-p + 1).concat(c);\n    p = 1;\n  }\n\n  // insert a dot if needed\n  if (p < c.length) {\n    c.splice(p, 0, p === 0 ? '0.' : '.');\n  }\n  return rounded.sign + c.join('');\n}\n\n/**\n * Format a number in exponential notation. Like '1.23e+5', '2.3e+0', '3.500e-3'\n * @param {number | string} value\n * @param {number} [precision]  Number of digits in formatted output.\n *                              If not provided, the maximum available digits\n *                              is used.\n */\nexport function toExponential(value, precision) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n\n  // round if needed, else create a clone\n  var split = splitNumber(value);\n  var rounded = precision ? roundDigits(split, precision) : split;\n  var c = rounded.coefficients;\n  var e = rounded.exponent;\n\n  // append zeros if needed\n  if (c.length < precision) {\n    c = c.concat(zeros(precision - c.length));\n  }\n\n  // format as `C.CCCe+EEE` or `C.CCCe-EEE`\n  var first = c.shift();\n  return rounded.sign + first + (c.length > 0 ? '.' + c.join('') : '') + 'e' + (e >= 0 ? '+' : '') + e;\n}\n\n/**\n * Format a number with a certain precision\n * @param {number | string} value\n * @param {number} [precision=undefined] Optional number of digits.\n * @param {{lowerExp: number | undefined, upperExp: number | undefined}} [options]\n *                                       By default:\n *                                         lowerExp = -3 (incl)\n *                                         upper = +5 (excl)\n * @return {string}\n */\nexport function toPrecision(value, precision, options) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n\n  // determine lower and upper bound for exponential notation.\n  var lowerExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.lowerExp, -3);\n  var upperExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.upperExp, 5);\n  var split = splitNumber(value);\n  var rounded = precision ? roundDigits(split, precision) : split;\n  if (rounded.exponent < lowerExp || rounded.exponent >= upperExp) {\n    // exponential notation\n    return toExponential(value, precision);\n  } else {\n    var c = rounded.coefficients;\n    var e = rounded.exponent;\n\n    // append trailing zeros\n    if (c.length < precision) {\n      c = c.concat(zeros(precision - c.length));\n    }\n\n    // append trailing zeros\n    // TODO: simplify the next statement\n    c = c.concat(zeros(e - c.length + 1 + (c.length < precision ? precision - c.length : 0)));\n\n    // prepend zeros\n    c = zeros(-e).concat(c);\n    var dot = e > 0 ? e : 0;\n    if (dot < c.length - 1) {\n      c.splice(dot + 1, 0, '.');\n    }\n    return rounded.sign + c.join('');\n  }\n}\n\n/**\n * Round the number of digits of a number *\n * @param {SplitValue} split       A value split with .splitNumber(value)\n * @param {number} precision  A positive integer\n * @return {SplitValue}\n *              Returns an object containing sign, coefficients, and exponent\n *              with rounded digits\n */\nexport function roundDigits(split, precision) {\n  // create a clone\n  var rounded = {\n    sign: split.sign,\n    coefficients: split.coefficients,\n    exponent: split.exponent\n  };\n  var c = rounded.coefficients;\n\n  // prepend zeros if needed\n  while (precision <= 0) {\n    c.unshift(0);\n    rounded.exponent++;\n    precision++;\n  }\n  if (c.length > precision) {\n    var removed = c.splice(precision, c.length - precision);\n    if (removed[0] >= 5) {\n      var i = precision - 1;\n      c[i]++;\n      while (c[i] === 10) {\n        c.pop();\n        if (i === 0) {\n          c.unshift(0);\n          rounded.exponent++;\n          i++;\n        }\n        i--;\n        c[i]++;\n      }\n    }\n  }\n  return rounded;\n}\n\n/**\n * Create an array filled with zeros.\n * @param {number} length\n * @return {Array}\n */\nfunction zeros(length) {\n  var arr = [];\n  for (var i = 0; i < length; i++) {\n    arr.push(0);\n  }\n  return arr;\n}\n\n/**\n * Count the number of significant digits of a number.\n *\n * For example:\n *   2.34 returns 3\n *   0.0034 returns 2\n *   120.5e+30 returns 4\n *\n * @param {number} value\n * @return {number} digits   Number of significant digits\n */\nexport function digits(value) {\n  return value.toExponential().replace(/e.*$/, '') // remove exponential notation\n  .replace(/^0\\.?0*|\\./, '') // remove decimal point and leading zeros\n  .length;\n}\n\n/**\n * Compares two floating point numbers.\n * @param {number} a - First value to compare\n * @param {number} b - Second value to compare\n * @param {number} [relTol=1e-09] - The relative tolerance, indicating the maximum allowed difference relative to the larger absolute value. Must be greater than 0.\n * @param {number} [absTol=1e-12] - The minimum absolute tolerance, useful for comparisons near zero. Must be at least 0.\n * @return {boolean} whether the two numbers are nearly equal\n *\n * @throws {Error} If `relTol` is less than or equal to 0.\n * @throws {Error} If `absTol` is less than 0.\n *\n * @example\n * nearlyEqual(1.000000001, 1.0, 1e-8);            // true\n * nearlyEqual(1.000000002, 1.0, 0);            // false\n * nearlyEqual(1.0, 1.009, undefined, 0.01);       // true\n * nearlyEqual(0.000000001, 0.0, undefined, 1e-8); // true\n */\nexport function nearlyEqual(a, b) {\n  var relTol = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1e-8;\n  var absTol = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  if (relTol <= 0) {\n    throw new Error('Relative tolerance must be greater than 0');\n  }\n  if (absTol < 0) {\n    throw new Error('Absolute tolerance must be at least 0');\n  }\n\n  // NaN\n  if (isNaN(a) || isNaN(b)) {\n    return false;\n  }\n  if (!isFinite(a) || !isFinite(b)) {\n    return a === b;\n  }\n  if (a === b) {\n    return true;\n  }\n\n  // abs(a-b) <= max(rel_tol * max(abs(a), abs(b)), abs_tol)\n  return Math.abs(a - b) <= Math.max(relTol * Math.max(Math.abs(a), Math.abs(b)), absTol);\n}\n\n/**\n * Calculate the hyperbolic arccos of a number\n * @param {number} x\n * @return {number}\n */\nexport var acosh = Math.acosh || function (x) {\n  return Math.log(Math.sqrt(x * x - 1) + x);\n};\nexport var asinh = Math.asinh || function (x) {\n  return Math.log(Math.sqrt(x * x + 1) + x);\n};\n\n/**\n * Calculate the hyperbolic arctangent of a number\n * @param {number} x\n * @return {number}\n */\nexport var atanh = Math.atanh || function (x) {\n  return Math.log((1 + x) / (1 - x)) / 2;\n};\n\n/**\n * Calculate the hyperbolic cosine of a number\n * @param {number} x\n * @returns {number}\n */\nexport var cosh = Math.cosh || function (x) {\n  return (Math.exp(x) + Math.exp(-x)) / 2;\n};\n\n/**\n * Calculate the hyperbolic sine of a number\n * @param {number} x\n * @returns {number}\n */\nexport var sinh = Math.sinh || function (x) {\n  return (Math.exp(x) - Math.exp(-x)) / 2;\n};\n\n/**\n * Calculate the hyperbolic tangent of a number\n * @param {number} x\n * @returns {number}\n */\nexport var tanh = Math.tanh || function (x) {\n  var e = Math.exp(2 * x);\n  return (e - 1) / (e + 1);\n};\n\n/**\n * Returns a value with the magnitude of x and the sign of y.\n * @param {number} x\n * @param {number} y\n * @returns {number}\n */\nexport function copysign(x, y) {\n  var signx = x > 0 ? true : x < 0 ? false : 1 / x === Infinity;\n  var signy = y > 0 ? true : y < 0 ? false : 1 / y === Infinity;\n  return signx ^ signy ? -x : x;\n}\nfunction _toNumberOrThrow(value, onError) {\n  if (isNumber(value)) {\n    return value;\n  } else if (isBigNumber(value)) {\n    return value.toNumber();\n  } else {\n    onError();\n  }\n}\nfunction _toNumberOrDefault(value, defaultValue) {\n  if (isNumber(value)) {\n    return value;\n  } else if (isBigNumber(value)) {\n    return value.toNumber();\n  } else {\n    return defaultValue;\n  }\n}", "map": {"version": 3, "names": ["isBigNumber", "isNumber", "isObject", "isInteger", "value", "isFinite", "Math", "round", "safeNumberType", "numberStr", "config", "number", "BigInt", "_unused", "numberFallback", "sign", "x", "log2", "log", "LN2", "log10", "LN10", "log1p", "cbrt", "negate", "result", "exp", "expm1", "formatNumberToBase", "n", "base", "size", "prefixes", "prefix", "suffix", "Error", "concat", "toString", "format", "options", "Infinity", "isNaN", "notation", "precision", "wordSize", "normalizeFormatOptions", "toFixed", "toExponential", "toEngineering", "toPrecision", "replace", "digits", "arguments", "e", "undefined", "toNumber", "_toNumberOrThrow", "splitNumber", "match", "String", "toLowerCase", "SyntaxError", "exponent", "parseFloat", "dot", "indexOf", "length", "coefficients", "zeros", "split", "map", "d", "parseInt", "push", "rounded", "roundDigits", "c", "newExp", "<PERSON><PERSON><PERSON><PERSON>", "abs", "i", "expDiff", "decimalIdx", "decimals", "slice", "join", "decimalVal", "str", "splitValue", "p", "pp", "splice", "first", "shift", "lowerExp", "_toNumberOrDefault", "upperExp", "unshift", "removed", "pop", "arr", "nearlyEqual", "a", "b", "relTol", "absTol", "max", "acosh", "sqrt", "asinh", "atanh", "cosh", "sinh", "tanh", "copysign", "y", "signx", "signy", "onError", "defaultValue"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/number.js"], "sourcesContent": ["import { isBigNumber, isNumber, isObject } from './is.js';\n\n/**\n * @typedef {{sign: '+' | '-' | '', coefficients: number[], exponent: number}} SplitValue\n */\n\n/**\n * Check if a number is integer\n * @param {number | boolean} value\n * @return {boolean} isInteger\n */\nexport function isInteger(value) {\n  if (typeof value === 'boolean') {\n    return true;\n  }\n  return isFinite(value) ? value === Math.round(value) : false;\n}\n\n/**\n * Ensure the number type is compatible with the provided value.\n * If not, return 'number' instead.\n *\n * For example:\n *\n *     safeNumberType('2.3', { number: 'bigint', numberFallback: 'number' })\n *\n * will return 'number' and not 'bigint' because trying to create a bigint with\n * value 2.3 would throw an exception.\n *\n * @param {string} numberStr\n * @param {{\n *   number: 'number' | 'BigNumber' | 'bigint' | 'Fraction'\n *   numberFallback: 'number' | 'BigNumber'\n * }} config\n * @returns {'number' | 'BigNumber' | 'bigint' | 'Fraction'}\n */\nexport function safeNumberType(numberStr, config) {\n  if (config.number === 'bigint') {\n    try {\n      BigInt(numberStr);\n    } catch (_unused) {\n      return config.numberFallback;\n    }\n  }\n  return config.number;\n}\n\n/**\n * Calculate the sign of a number\n * @param {number} x\n * @returns {number}\n */\nexport var sign = Math.sign || function (x) {\n  if (x > 0) {\n    return 1;\n  } else if (x < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\n/**\n * Calculate the base-2 logarithm of a number\n * @param {number} x\n * @returns {number}\n */\nexport var log2 = Math.log2 || function log2(x) {\n  return Math.log(x) / Math.LN2;\n};\n\n/**\n * Calculate the base-10 logarithm of a number\n * @param {number} x\n * @returns {number}\n */\nexport var log10 = Math.log10 || function log10(x) {\n  return Math.log(x) / Math.LN10;\n};\n\n/**\n * Calculate the natural logarithm of a number + 1\n * @param {number} x\n * @returns {number}\n */\nexport var log1p = Math.log1p || function (x) {\n  return Math.log(x + 1);\n};\n\n/**\n * Calculate cubic root for a number\n *\n * Code from es6-shim.js:\n *   https://github.com/paulmillr/es6-shim/blob/master/es6-shim.js#L1564-L1577\n *\n * @param {number} x\n * @returns {number} Returns the cubic root of x\n */\nexport var cbrt = Math.cbrt || function cbrt(x) {\n  if (x === 0) {\n    return x;\n  }\n  var negate = x < 0;\n  var result;\n  if (negate) {\n    x = -x;\n  }\n  if (isFinite(x)) {\n    result = Math.exp(Math.log(x) / 3);\n    // from https://en.wikipedia.org/wiki/Cube_root#Numerical_methods\n    result = (x / (result * result) + 2 * result) / 3;\n  } else {\n    result = x;\n  }\n  return negate ? -result : result;\n};\n\n/**\n * Calculates exponentiation minus 1\n * @param {number} x\n * @return {number} res\n */\nexport var expm1 = Math.expm1 || function expm1(x) {\n  return x >= 2e-4 || x <= -2e-4 ? Math.exp(x) - 1 : x + x * x / 2 + x * x * x / 6;\n};\n\n/**\n * Formats a number in a given base\n * @param {number} n\n * @param {number} base\n * @param {number} size\n * @returns {string}\n */\nfunction formatNumberToBase(n, base, size) {\n  var prefixes = {\n    2: '0b',\n    8: '0o',\n    16: '0x'\n  };\n  var prefix = prefixes[base];\n  var suffix = '';\n  if (size) {\n    if (size < 1) {\n      throw new Error('size must be in greater than 0');\n    }\n    if (!isInteger(size)) {\n      throw new Error('size must be an integer');\n    }\n    if (n > 2 ** (size - 1) - 1 || n < -(2 ** (size - 1))) {\n      throw new Error(\"Value must be in range [-2^\".concat(size - 1, \", 2^\").concat(size - 1, \"-1]\"));\n    }\n    if (!isInteger(n)) {\n      throw new Error('Value must be an integer');\n    }\n    if (n < 0) {\n      n = n + 2 ** size;\n    }\n    suffix = \"i\".concat(size);\n  }\n  var sign = '';\n  if (n < 0) {\n    n = -n;\n    sign = '-';\n  }\n  return \"\".concat(sign).concat(prefix).concat(n.toString(base)).concat(suffix);\n}\n\n/**\n * Convert a number to a formatted string representation.\n *\n * Syntax:\n *\n *    format(value)\n *    format(value, options)\n *    format(value, precision)\n *    format(value, fn)\n *\n * Where:\n *\n *    {number} value   The value to be formatted\n *    {Object} options An object with formatting options. Available options:\n *                     {string} notation\n *                         Number notation. Choose from:\n *                         'fixed'          Always use regular number notation.\n *                                          For example '123.40' and '14000000'\n *                         'exponential'    Always use exponential notation.\n *                                          For example '1.234e+2' and '1.4e+7'\n *                         'engineering'    Always use engineering notation.\n *                                          For example '123.4e+0' and '14.0e+6'\n *                         'auto' (default) Regular number notation for numbers\n *                                          having an absolute value between\n *                                          `lowerExp` and `upperExp` bounds, and\n *                                          uses exponential notation elsewhere.\n *                                          Lower bound is included, upper bound\n *                                          is excluded.\n *                                          For example '123.4' and '1.4e7'.\n *                         'bin', 'oct, or\n *                         'hex'            Format the number using binary, octal,\n *                                          or hexadecimal notation.\n *                                          For example '0b1101' and '0x10fe'.\n *                     {number} wordSize    The word size in bits to use for formatting\n *                                          in binary, octal, or hexadecimal notation.\n *                                          To be used only with 'bin', 'oct', or 'hex'\n *                                          values for 'notation' option. When this option\n *                                          is defined the value is formatted as a signed\n *                                          twos complement integer of the given word size\n *                                          and the size suffix is appended to the output.\n *                                          For example\n *                                          format(-1, {notation: 'hex', wordSize: 8}) === '0xffi8'.\n *                                          Default value is undefined.\n *                     {number} precision   A number between 0 and 16 to round\n *                                          the digits of the number.\n *                                          In case of notations 'exponential',\n *                                          'engineering', and 'auto',\n *                                          `precision` defines the total\n *                                          number of significant digits returned.\n *                                          In case of notation 'fixed',\n *                                          `precision` defines the number of\n *                                          significant digits after the decimal\n *                                          point.\n *                                          `precision` is undefined by default,\n *                                          not rounding any digits.\n *                     {number} lowerExp    Exponent determining the lower boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `-3`.\n *                     {number} upperExp    Exponent determining the upper boundary\n *                                          for formatting a value with an exponent\n *                                          when `notation='auto`.\n *                                          Default value is `5`.\n *    {Function} fn    A custom formatting function. Can be used to override the\n *                     built-in notations. Function `fn` is called with `value` as\n *                     parameter and must return a string. Is useful for example to\n *                     format all values inside a matrix in a particular way.\n *\n * Examples:\n *\n *    format(6.4)                                        // '6.4'\n *    format(1240000)                                    // '1.24e6'\n *    format(1/3)                                        // '0.3333333333333333'\n *    format(1/3, 3)                                     // '0.333'\n *    format(21385, 2)                                   // '21000'\n *    format(12.071, {notation: 'fixed'})                // '12'\n *    format(2.3,    {notation: 'fixed', precision: 2})  // '2.30'\n *    format(52.8,   {notation: 'exponential'})          // '5.28e+1'\n *    format(12345678, {notation: 'engineering'})        // '12.345678e+6'\n *\n * @param {number} value\n * @param {Object | Function | number} [options]\n * @return {string} str The formatted value\n */\nexport function format(value, options) {\n  if (typeof options === 'function') {\n    // handle format(value, fn)\n    return options(value);\n  }\n\n  // handle special cases\n  if (value === Infinity) {\n    return 'Infinity';\n  } else if (value === -Infinity) {\n    return '-Infinity';\n  } else if (isNaN(value)) {\n    return 'NaN';\n  }\n  var {\n    notation,\n    precision,\n    wordSize\n  } = normalizeFormatOptions(options);\n\n  // handle the various notations\n  switch (notation) {\n    case 'fixed':\n      return toFixed(value, precision);\n    case 'exponential':\n      return toExponential(value, precision);\n    case 'engineering':\n      return toEngineering(value, precision);\n    case 'bin':\n      return formatNumberToBase(value, 2, wordSize);\n    case 'oct':\n      return formatNumberToBase(value, 8, wordSize);\n    case 'hex':\n      return formatNumberToBase(value, 16, wordSize);\n    case 'auto':\n      // remove trailing zeros after the decimal point\n      return toPrecision(value, precision, options).replace(/((\\.\\d*?)(0+))($|e)/, function () {\n        var digits = arguments[2];\n        var e = arguments[4];\n        return digits !== '.' ? digits + e : e;\n      });\n    default:\n      throw new Error('Unknown notation \"' + notation + '\". ' + 'Choose \"auto\", \"exponential\", \"fixed\", \"bin\", \"oct\", or \"hex.');\n  }\n}\n\n/**\n * Normalize format options into an object:\n *   {\n *     notation: string,\n *     precision: number | undefined,\n *     wordSize: number | undefined\n *   }\n */\nexport function normalizeFormatOptions(options) {\n  // default values for options\n  var notation = 'auto';\n  var precision;\n  var wordSize;\n  if (options !== undefined) {\n    if (isNumber(options)) {\n      precision = options;\n    } else if (isBigNumber(options)) {\n      precision = options.toNumber();\n    } else if (isObject(options)) {\n      if (options.precision !== undefined) {\n        precision = _toNumberOrThrow(options.precision, () => {\n          throw new Error('Option \"precision\" must be a number or BigNumber');\n        });\n      }\n      if (options.wordSize !== undefined) {\n        wordSize = _toNumberOrThrow(options.wordSize, () => {\n          throw new Error('Option \"wordSize\" must be a number or BigNumber');\n        });\n      }\n      if (options.notation) {\n        notation = options.notation;\n      }\n    } else {\n      throw new Error('Unsupported type of options, number, BigNumber, or object expected');\n    }\n  }\n  return {\n    notation,\n    precision,\n    wordSize\n  };\n}\n\n/**\n * Split a number into sign, coefficients, and exponent\n * @param {number | string} value\n * @return {SplitValue}\n *              Returns an object containing sign, coefficients, and exponent\n */\nexport function splitNumber(value) {\n  // parse the input value\n  var match = String(value).toLowerCase().match(/^(-?)(\\d+\\.?\\d*)(e([+-]?\\d+))?$/);\n  if (!match) {\n    throw new SyntaxError('Invalid number ' + value);\n  }\n  var sign = match[1];\n  var digits = match[2];\n  var exponent = parseFloat(match[4] || '0');\n  var dot = digits.indexOf('.');\n  exponent += dot !== -1 ? dot - 1 : digits.length - 1;\n  var coefficients = digits.replace('.', '') // remove the dot (must be removed before removing leading zeros)\n  .replace(/^0*/, function (zeros) {\n    // remove leading zeros, add their count to the exponent\n    exponent -= zeros.length;\n    return '';\n  }).replace(/0*$/, '') // remove trailing zeros\n  .split('').map(function (d) {\n    return parseInt(d);\n  });\n  if (coefficients.length === 0) {\n    coefficients.push(0);\n    exponent++;\n  }\n  return {\n    sign,\n    coefficients,\n    exponent\n  };\n}\n\n/**\n * Format a number in engineering notation. Like '1.23e+6', '2.3e+0', '3.500e-3'\n * @param {number | string} value\n * @param {number} [precision]        Optional number of significant figures to return.\n */\nexport function toEngineering(value, precision) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n  var split = splitNumber(value);\n  var rounded = roundDigits(split, precision);\n  var e = rounded.exponent;\n  var c = rounded.coefficients;\n\n  // find nearest lower multiple of 3 for exponent\n  var newExp = e % 3 === 0 ? e : e < 0 ? e - 3 - e % 3 : e - e % 3;\n  if (isNumber(precision)) {\n    // add zeroes to give correct sig figs\n    while (precision > c.length || e - newExp + 1 > c.length) {\n      c.push(0);\n    }\n  } else {\n    // concatenate coefficients with necessary zeros\n    // add zeros if necessary (for example: 1e+8 -> 100e+6)\n    var missingZeros = Math.abs(e - newExp) - (c.length - 1);\n    for (var i = 0; i < missingZeros; i++) {\n      c.push(0);\n    }\n  }\n\n  // find difference in exponents\n  var expDiff = Math.abs(e - newExp);\n  var decimalIdx = 1;\n\n  // push decimal index over by expDiff times\n  while (expDiff > 0) {\n    decimalIdx++;\n    expDiff--;\n  }\n\n  // if all coefficient values are zero after the decimal point and precision is unset, don't add a decimal value.\n  // otherwise concat with the rest of the coefficients\n  var decimals = c.slice(decimalIdx).join('');\n  var decimalVal = isNumber(precision) && decimals.length || decimals.match(/[1-9]/) ? '.' + decimals : '';\n  var str = c.slice(0, decimalIdx).join('') + decimalVal + 'e' + (e >= 0 ? '+' : '') + newExp.toString();\n  return rounded.sign + str;\n}\n\n/**\n * Format a number with fixed notation.\n * @param {number | string} value\n * @param {number} [precision=undefined]  Optional number of decimals after the\n *                                        decimal point. null by default.\n */\nexport function toFixed(value, precision) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n  var splitValue = splitNumber(value);\n  var rounded = typeof precision === 'number' ? roundDigits(splitValue, splitValue.exponent + 1 + precision) : splitValue;\n  var c = rounded.coefficients;\n  var p = rounded.exponent + 1; // exponent may have changed\n\n  // append zeros if needed\n  var pp = p + (precision || 0);\n  if (c.length < pp) {\n    c = c.concat(zeros(pp - c.length));\n  }\n\n  // prepend zeros if needed\n  if (p < 0) {\n    c = zeros(-p + 1).concat(c);\n    p = 1;\n  }\n\n  // insert a dot if needed\n  if (p < c.length) {\n    c.splice(p, 0, p === 0 ? '0.' : '.');\n  }\n  return rounded.sign + c.join('');\n}\n\n/**\n * Format a number in exponential notation. Like '1.23e+5', '2.3e+0', '3.500e-3'\n * @param {number | string} value\n * @param {number} [precision]  Number of digits in formatted output.\n *                              If not provided, the maximum available digits\n *                              is used.\n */\nexport function toExponential(value, precision) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n\n  // round if needed, else create a clone\n  var split = splitNumber(value);\n  var rounded = precision ? roundDigits(split, precision) : split;\n  var c = rounded.coefficients;\n  var e = rounded.exponent;\n\n  // append zeros if needed\n  if (c.length < precision) {\n    c = c.concat(zeros(precision - c.length));\n  }\n\n  // format as `C.CCCe+EEE` or `C.CCCe-EEE`\n  var first = c.shift();\n  return rounded.sign + first + (c.length > 0 ? '.' + c.join('') : '') + 'e' + (e >= 0 ? '+' : '') + e;\n}\n\n/**\n * Format a number with a certain precision\n * @param {number | string} value\n * @param {number} [precision=undefined] Optional number of digits.\n * @param {{lowerExp: number | undefined, upperExp: number | undefined}} [options]\n *                                       By default:\n *                                         lowerExp = -3 (incl)\n *                                         upper = +5 (excl)\n * @return {string}\n */\nexport function toPrecision(value, precision, options) {\n  if (isNaN(value) || !isFinite(value)) {\n    return String(value);\n  }\n\n  // determine lower and upper bound for exponential notation.\n  var lowerExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.lowerExp, -3);\n  var upperExp = _toNumberOrDefault(options === null || options === void 0 ? void 0 : options.upperExp, 5);\n  var split = splitNumber(value);\n  var rounded = precision ? roundDigits(split, precision) : split;\n  if (rounded.exponent < lowerExp || rounded.exponent >= upperExp) {\n    // exponential notation\n    return toExponential(value, precision);\n  } else {\n    var c = rounded.coefficients;\n    var e = rounded.exponent;\n\n    // append trailing zeros\n    if (c.length < precision) {\n      c = c.concat(zeros(precision - c.length));\n    }\n\n    // append trailing zeros\n    // TODO: simplify the next statement\n    c = c.concat(zeros(e - c.length + 1 + (c.length < precision ? precision - c.length : 0)));\n\n    // prepend zeros\n    c = zeros(-e).concat(c);\n    var dot = e > 0 ? e : 0;\n    if (dot < c.length - 1) {\n      c.splice(dot + 1, 0, '.');\n    }\n    return rounded.sign + c.join('');\n  }\n}\n\n/**\n * Round the number of digits of a number *\n * @param {SplitValue} split       A value split with .splitNumber(value)\n * @param {number} precision  A positive integer\n * @return {SplitValue}\n *              Returns an object containing sign, coefficients, and exponent\n *              with rounded digits\n */\nexport function roundDigits(split, precision) {\n  // create a clone\n  var rounded = {\n    sign: split.sign,\n    coefficients: split.coefficients,\n    exponent: split.exponent\n  };\n  var c = rounded.coefficients;\n\n  // prepend zeros if needed\n  while (precision <= 0) {\n    c.unshift(0);\n    rounded.exponent++;\n    precision++;\n  }\n  if (c.length > precision) {\n    var removed = c.splice(precision, c.length - precision);\n    if (removed[0] >= 5) {\n      var i = precision - 1;\n      c[i]++;\n      while (c[i] === 10) {\n        c.pop();\n        if (i === 0) {\n          c.unshift(0);\n          rounded.exponent++;\n          i++;\n        }\n        i--;\n        c[i]++;\n      }\n    }\n  }\n  return rounded;\n}\n\n/**\n * Create an array filled with zeros.\n * @param {number} length\n * @return {Array}\n */\nfunction zeros(length) {\n  var arr = [];\n  for (var i = 0; i < length; i++) {\n    arr.push(0);\n  }\n  return arr;\n}\n\n/**\n * Count the number of significant digits of a number.\n *\n * For example:\n *   2.34 returns 3\n *   0.0034 returns 2\n *   120.5e+30 returns 4\n *\n * @param {number} value\n * @return {number} digits   Number of significant digits\n */\nexport function digits(value) {\n  return value.toExponential().replace(/e.*$/, '') // remove exponential notation\n  .replace(/^0\\.?0*|\\./, '') // remove decimal point and leading zeros\n  .length;\n}\n\n/**\n * Compares two floating point numbers.\n * @param {number} a - First value to compare\n * @param {number} b - Second value to compare\n * @param {number} [relTol=1e-09] - The relative tolerance, indicating the maximum allowed difference relative to the larger absolute value. Must be greater than 0.\n * @param {number} [absTol=1e-12] - The minimum absolute tolerance, useful for comparisons near zero. Must be at least 0.\n * @return {boolean} whether the two numbers are nearly equal\n *\n * @throws {Error} If `relTol` is less than or equal to 0.\n * @throws {Error} If `absTol` is less than 0.\n *\n * @example\n * nearlyEqual(1.000000001, 1.0, 1e-8);            // true\n * nearlyEqual(1.000000002, 1.0, 0);            // false\n * nearlyEqual(1.0, 1.009, undefined, 0.01);       // true\n * nearlyEqual(0.000000001, 0.0, undefined, 1e-8); // true\n */\nexport function nearlyEqual(a, b) {\n  var relTol = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1e-8;\n  var absTol = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  if (relTol <= 0) {\n    throw new Error('Relative tolerance must be greater than 0');\n  }\n  if (absTol < 0) {\n    throw new Error('Absolute tolerance must be at least 0');\n  }\n\n  // NaN\n  if (isNaN(a) || isNaN(b)) {\n    return false;\n  }\n  if (!isFinite(a) || !isFinite(b)) {\n    return a === b;\n  }\n  if (a === b) {\n    return true;\n  }\n\n  // abs(a-b) <= max(rel_tol * max(abs(a), abs(b)), abs_tol)\n  return Math.abs(a - b) <= Math.max(relTol * Math.max(Math.abs(a), Math.abs(b)), absTol);\n}\n\n/**\n * Calculate the hyperbolic arccos of a number\n * @param {number} x\n * @return {number}\n */\nexport var acosh = Math.acosh || function (x) {\n  return Math.log(Math.sqrt(x * x - 1) + x);\n};\nexport var asinh = Math.asinh || function (x) {\n  return Math.log(Math.sqrt(x * x + 1) + x);\n};\n\n/**\n * Calculate the hyperbolic arctangent of a number\n * @param {number} x\n * @return {number}\n */\nexport var atanh = Math.atanh || function (x) {\n  return Math.log((1 + x) / (1 - x)) / 2;\n};\n\n/**\n * Calculate the hyperbolic cosine of a number\n * @param {number} x\n * @returns {number}\n */\nexport var cosh = Math.cosh || function (x) {\n  return (Math.exp(x) + Math.exp(-x)) / 2;\n};\n\n/**\n * Calculate the hyperbolic sine of a number\n * @param {number} x\n * @returns {number}\n */\nexport var sinh = Math.sinh || function (x) {\n  return (Math.exp(x) - Math.exp(-x)) / 2;\n};\n\n/**\n * Calculate the hyperbolic tangent of a number\n * @param {number} x\n * @returns {number}\n */\nexport var tanh = Math.tanh || function (x) {\n  var e = Math.exp(2 * x);\n  return (e - 1) / (e + 1);\n};\n\n/**\n * Returns a value with the magnitude of x and the sign of y.\n * @param {number} x\n * @param {number} y\n * @returns {number}\n */\nexport function copysign(x, y) {\n  var signx = x > 0 ? true : x < 0 ? false : 1 / x === Infinity;\n  var signy = y > 0 ? true : y < 0 ? false : 1 / y === Infinity;\n  return signx ^ signy ? -x : x;\n}\nfunction _toNumberOrThrow(value, onError) {\n  if (isNumber(value)) {\n    return value;\n  } else if (isBigNumber(value)) {\n    return value.toNumber();\n  } else {\n    onError();\n  }\n}\nfunction _toNumberOrDefault(value, defaultValue) {\n  if (isNumber(value)) {\n    return value;\n  } else if (isBigNumber(value)) {\n    return value.toNumber();\n  } else {\n    return defaultValue;\n  }\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,SAAS;;AAEzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;IAC9B,OAAO,IAAI;EACb;EACA,OAAOC,QAAQ,CAACD,KAAK,CAAC,GAAGA,KAAK,KAAKE,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC,GAAG,KAAK;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,cAAcA,CAACC,SAAS,EAAEC,MAAM,EAAE;EAChD,IAAIA,MAAM,CAACC,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAI;MACFC,MAAM,CAACH,SAAS,CAAC;IACnB,CAAC,CAAC,OAAOI,OAAO,EAAE;MAChB,OAAOH,MAAM,CAACI,cAAc;IAC9B;EACF;EACA,OAAOJ,MAAM,CAACC,MAAM;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,IAAI,GAAGT,IAAI,CAACS,IAAI,IAAI,UAAUC,CAAC,EAAE;EAC1C,IAAIA,CAAC,GAAG,CAAC,EAAE;IACT,OAAO,CAAC;EACV,CAAC,MAAM,IAAIA,CAAC,GAAG,CAAC,EAAE;IAChB,OAAO,CAAC,CAAC;EACX,CAAC,MAAM;IACL,OAAO,CAAC;EACV;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,IAAI,GAAGX,IAAI,CAACW,IAAI,IAAI,SAASA,IAAIA,CAACD,CAAC,EAAE;EAC9C,OAAOV,IAAI,CAACY,GAAG,CAACF,CAAC,CAAC,GAAGV,IAAI,CAACa,GAAG;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,KAAK,GAAGd,IAAI,CAACc,KAAK,IAAI,SAASA,KAAKA,CAACJ,CAAC,EAAE;EACjD,OAAOV,IAAI,CAACY,GAAG,CAACF,CAAC,CAAC,GAAGV,IAAI,CAACe,IAAI;AAChC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,KAAK,GAAGhB,IAAI,CAACgB,KAAK,IAAI,UAAUN,CAAC,EAAE;EAC5C,OAAOV,IAAI,CAACY,GAAG,CAACF,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,IAAI,GAAGjB,IAAI,CAACiB,IAAI,IAAI,SAASA,IAAIA,CAACP,CAAC,EAAE;EAC9C,IAAIA,CAAC,KAAK,CAAC,EAAE;IACX,OAAOA,CAAC;EACV;EACA,IAAIQ,MAAM,GAAGR,CAAC,GAAG,CAAC;EAClB,IAAIS,MAAM;EACV,IAAID,MAAM,EAAE;IACVR,CAAC,GAAG,CAACA,CAAC;EACR;EACA,IAAIX,QAAQ,CAACW,CAAC,CAAC,EAAE;IACfS,MAAM,GAAGnB,IAAI,CAACoB,GAAG,CAACpB,IAAI,CAACY,GAAG,CAACF,CAAC,CAAC,GAAG,CAAC,CAAC;IAClC;IACAS,MAAM,GAAG,CAACT,CAAC,IAAIS,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAGA,MAAM,IAAI,CAAC;EACnD,CAAC,MAAM;IACLA,MAAM,GAAGT,CAAC;EACZ;EACA,OAAOQ,MAAM,GAAG,CAACC,MAAM,GAAGA,MAAM;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,KAAK,GAAGrB,IAAI,CAACqB,KAAK,IAAI,SAASA,KAAKA,CAACX,CAAC,EAAE;EACjD,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,CAAC,IAAI,GAAGV,IAAI,CAACoB,GAAG,CAACV,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;AAClF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,kBAAkBA,CAACC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACzC,IAAIC,QAAQ,GAAG;IACb,CAAC,EAAE,IAAI;IACP,CAAC,EAAE,IAAI;IACP,EAAE,EAAE;EACN,CAAC;EACD,IAAIC,MAAM,GAAGD,QAAQ,CAACF,IAAI,CAAC;EAC3B,IAAII,MAAM,GAAG,EAAE;EACf,IAAIH,IAAI,EAAE;IACR,IAAIA,IAAI,GAAG,CAAC,EAAE;MACZ,MAAM,IAAII,KAAK,CAAC,gCAAgC,CAAC;IACnD;IACA,IAAI,CAAChC,SAAS,CAAC4B,IAAI,CAAC,EAAE;MACpB,MAAM,IAAII,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAIN,CAAC,GAAG,CAAC,KAAKE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIF,CAAC,GAAG,EAAE,CAAC,KAAKE,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;MACrD,MAAM,IAAII,KAAK,CAAC,6BAA6B,CAACC,MAAM,CAACL,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,CAACK,MAAM,CAACL,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IACjG;IACA,IAAI,CAAC5B,SAAS,CAAC0B,CAAC,CAAC,EAAE;MACjB,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;IAC7C;IACA,IAAIN,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIE,IAAI;IACnB;IACAG,MAAM,GAAG,GAAG,CAACE,MAAM,CAACL,IAAI,CAAC;EAC3B;EACA,IAAIhB,IAAI,GAAG,EAAE;EACb,IAAIc,CAAC,GAAG,CAAC,EAAE;IACTA,CAAC,GAAG,CAACA,CAAC;IACNd,IAAI,GAAG,GAAG;EACZ;EACA,OAAO,EAAE,CAACqB,MAAM,CAACrB,IAAI,CAAC,CAACqB,MAAM,CAACH,MAAM,CAAC,CAACG,MAAM,CAACP,CAAC,CAACQ,QAAQ,CAACP,IAAI,CAAC,CAAC,CAACM,MAAM,CAACF,MAAM,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,MAAMA,CAAClC,KAAK,EAAEmC,OAAO,EAAE;EACrC,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;IACjC;IACA,OAAOA,OAAO,CAACnC,KAAK,CAAC;EACvB;;EAEA;EACA,IAAIA,KAAK,KAAKoC,QAAQ,EAAE;IACtB,OAAO,UAAU;EACnB,CAAC,MAAM,IAAIpC,KAAK,KAAK,CAACoC,QAAQ,EAAE;IAC9B,OAAO,WAAW;EACpB,CAAC,MAAM,IAAIC,KAAK,CAACrC,KAAK,CAAC,EAAE;IACvB,OAAO,KAAK;EACd;EACA,IAAI;IACFsC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGC,sBAAsB,CAACN,OAAO,CAAC;;EAEnC;EACA,QAAQG,QAAQ;IACd,KAAK,OAAO;MACV,OAAOI,OAAO,CAAC1C,KAAK,EAAEuC,SAAS,CAAC;IAClC,KAAK,aAAa;MAChB,OAAOI,aAAa,CAAC3C,KAAK,EAAEuC,SAAS,CAAC;IACxC,KAAK,aAAa;MAChB,OAAOK,aAAa,CAAC5C,KAAK,EAAEuC,SAAS,CAAC;IACxC,KAAK,KAAK;MACR,OAAOf,kBAAkB,CAACxB,KAAK,EAAE,CAAC,EAAEwC,QAAQ,CAAC;IAC/C,KAAK,KAAK;MACR,OAAOhB,kBAAkB,CAACxB,KAAK,EAAE,CAAC,EAAEwC,QAAQ,CAAC;IAC/C,KAAK,KAAK;MACR,OAAOhB,kBAAkB,CAACxB,KAAK,EAAE,EAAE,EAAEwC,QAAQ,CAAC;IAChD,KAAK,MAAM;MACT;MACA,OAAOK,WAAW,CAAC7C,KAAK,EAAEuC,SAAS,EAAEJ,OAAO,CAAC,CAACW,OAAO,CAAC,qBAAqB,EAAE,YAAY;QACvF,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzB,IAAIC,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC;QACpB,OAAOD,MAAM,KAAK,GAAG,GAAGA,MAAM,GAAGE,CAAC,GAAGA,CAAC;MACxC,CAAC,CAAC;IACJ;MACE,MAAM,IAAIlB,KAAK,CAAC,oBAAoB,GAAGO,QAAQ,GAAG,KAAK,GAAG,+DAA+D,CAAC;EAC9H;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,sBAAsBA,CAACN,OAAO,EAAE;EAC9C;EACA,IAAIG,QAAQ,GAAG,MAAM;EACrB,IAAIC,SAAS;EACb,IAAIC,QAAQ;EACZ,IAAIL,OAAO,KAAKe,SAAS,EAAE;IACzB,IAAIrD,QAAQ,CAACsC,OAAO,CAAC,EAAE;MACrBI,SAAS,GAAGJ,OAAO;IACrB,CAAC,MAAM,IAAIvC,WAAW,CAACuC,OAAO,CAAC,EAAE;MAC/BI,SAAS,GAAGJ,OAAO,CAACgB,QAAQ,CAAC,CAAC;IAChC,CAAC,MAAM,IAAIrD,QAAQ,CAACqC,OAAO,CAAC,EAAE;MAC5B,IAAIA,OAAO,CAACI,SAAS,KAAKW,SAAS,EAAE;QACnCX,SAAS,GAAGa,gBAAgB,CAACjB,OAAO,CAACI,SAAS,EAAE,MAAM;UACpD,MAAM,IAAIR,KAAK,CAAC,kDAAkD,CAAC;QACrE,CAAC,CAAC;MACJ;MACA,IAAII,OAAO,CAACK,QAAQ,KAAKU,SAAS,EAAE;QAClCV,QAAQ,GAAGY,gBAAgB,CAACjB,OAAO,CAACK,QAAQ,EAAE,MAAM;UAClD,MAAM,IAAIT,KAAK,CAAC,iDAAiD,CAAC;QACpE,CAAC,CAAC;MACJ;MACA,IAAII,OAAO,CAACG,QAAQ,EAAE;QACpBA,QAAQ,GAAGH,OAAO,CAACG,QAAQ;MAC7B;IACF,CAAC,MAAM;MACL,MAAM,IAAIP,KAAK,CAAC,oEAAoE,CAAC;IACvF;EACF;EACA,OAAO;IACLO,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASa,WAAWA,CAACrD,KAAK,EAAE;EACjC;EACA,IAAIsD,KAAK,GAAGC,MAAM,CAACvD,KAAK,CAAC,CAACwD,WAAW,CAAC,CAAC,CAACF,KAAK,CAAC,iCAAiC,CAAC;EAChF,IAAI,CAACA,KAAK,EAAE;IACV,MAAM,IAAIG,WAAW,CAAC,iBAAiB,GAAGzD,KAAK,CAAC;EAClD;EACA,IAAIW,IAAI,GAAG2C,KAAK,CAAC,CAAC,CAAC;EACnB,IAAIP,MAAM,GAAGO,KAAK,CAAC,CAAC,CAAC;EACrB,IAAII,QAAQ,GAAGC,UAAU,CAACL,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;EAC1C,IAAIM,GAAG,GAAGb,MAAM,CAACc,OAAO,CAAC,GAAG,CAAC;EAC7BH,QAAQ,IAAIE,GAAG,KAAK,CAAC,CAAC,GAAGA,GAAG,GAAG,CAAC,GAAGb,MAAM,CAACe,MAAM,GAAG,CAAC;EACpD,IAAIC,YAAY,GAAGhB,MAAM,CAACD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAAA,CAC1CA,OAAO,CAAC,KAAK,EAAE,UAAUkB,KAAK,EAAE;IAC/B;IACAN,QAAQ,IAAIM,KAAK,CAACF,MAAM;IACxB,OAAO,EAAE;EACX,CAAC,CAAC,CAAChB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;EAAA,CACrBmB,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;IAC1B,OAAOC,QAAQ,CAACD,CAAC,CAAC;EACpB,CAAC,CAAC;EACF,IAAIJ,YAAY,CAACD,MAAM,KAAK,CAAC,EAAE;IAC7BC,YAAY,CAACM,IAAI,CAAC,CAAC,CAAC;IACpBX,QAAQ,EAAE;EACZ;EACA,OAAO;IACL/C,IAAI;IACJoD,YAAY;IACZL;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASd,aAAaA,CAAC5C,KAAK,EAAEuC,SAAS,EAAE;EAC9C,IAAIF,KAAK,CAACrC,KAAK,CAAC,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;IACpC,OAAOuD,MAAM,CAACvD,KAAK,CAAC;EACtB;EACA,IAAIiE,KAAK,GAAGZ,WAAW,CAACrD,KAAK,CAAC;EAC9B,IAAIsE,OAAO,GAAGC,WAAW,CAACN,KAAK,EAAE1B,SAAS,CAAC;EAC3C,IAAIU,CAAC,GAAGqB,OAAO,CAACZ,QAAQ;EACxB,IAAIc,CAAC,GAAGF,OAAO,CAACP,YAAY;;EAE5B;EACA,IAAIU,MAAM,GAAGxB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;EAChE,IAAIpD,QAAQ,CAAC0C,SAAS,CAAC,EAAE;IACvB;IACA,OAAOA,SAAS,GAAGiC,CAAC,CAACV,MAAM,IAAIb,CAAC,GAAGwB,MAAM,GAAG,CAAC,GAAGD,CAAC,CAACV,MAAM,EAAE;MACxDU,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IACX;EACF,CAAC,MAAM;IACL;IACA;IACA,IAAIK,YAAY,GAAGxE,IAAI,CAACyE,GAAG,CAAC1B,CAAC,GAAGwB,MAAM,CAAC,IAAID,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IACxD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;MACrCJ,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IACX;EACF;;EAEA;EACA,IAAIQ,OAAO,GAAG3E,IAAI,CAACyE,GAAG,CAAC1B,CAAC,GAAGwB,MAAM,CAAC;EAClC,IAAIK,UAAU,GAAG,CAAC;;EAElB;EACA,OAAOD,OAAO,GAAG,CAAC,EAAE;IAClBC,UAAU,EAAE;IACZD,OAAO,EAAE;EACX;;EAEA;EACA;EACA,IAAIE,QAAQ,GAAGP,CAAC,CAACQ,KAAK,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC;EAC3C,IAAIC,UAAU,GAAGrF,QAAQ,CAAC0C,SAAS,CAAC,IAAIwC,QAAQ,CAACjB,MAAM,IAAIiB,QAAQ,CAACzB,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,GAAGyB,QAAQ,GAAG,EAAE;EACxG,IAAII,GAAG,GAAGX,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAEF,UAAU,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC,GAAGC,UAAU,GAAG,GAAG,IAAIjC,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGwB,MAAM,CAACxC,QAAQ,CAAC,CAAC;EACtG,OAAOqC,OAAO,CAAC3D,IAAI,GAAGwE,GAAG;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASzC,OAAOA,CAAC1C,KAAK,EAAEuC,SAAS,EAAE;EACxC,IAAIF,KAAK,CAACrC,KAAK,CAAC,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;IACpC,OAAOuD,MAAM,CAACvD,KAAK,CAAC;EACtB;EACA,IAAIoF,UAAU,GAAG/B,WAAW,CAACrD,KAAK,CAAC;EACnC,IAAIsE,OAAO,GAAG,OAAO/B,SAAS,KAAK,QAAQ,GAAGgC,WAAW,CAACa,UAAU,EAAEA,UAAU,CAAC1B,QAAQ,GAAG,CAAC,GAAGnB,SAAS,CAAC,GAAG6C,UAAU;EACvH,IAAIZ,CAAC,GAAGF,OAAO,CAACP,YAAY;EAC5B,IAAIsB,CAAC,GAAGf,OAAO,CAACZ,QAAQ,GAAG,CAAC,CAAC,CAAC;;EAE9B;EACA,IAAI4B,EAAE,GAAGD,CAAC,IAAI9C,SAAS,IAAI,CAAC,CAAC;EAC7B,IAAIiC,CAAC,CAACV,MAAM,GAAGwB,EAAE,EAAE;IACjBd,CAAC,GAAGA,CAAC,CAACxC,MAAM,CAACgC,KAAK,CAACsB,EAAE,GAAGd,CAAC,CAACV,MAAM,CAAC,CAAC;EACpC;;EAEA;EACA,IAAIuB,CAAC,GAAG,CAAC,EAAE;IACTb,CAAC,GAAGR,KAAK,CAAC,CAACqB,CAAC,GAAG,CAAC,CAAC,CAACrD,MAAM,CAACwC,CAAC,CAAC;IAC3Ba,CAAC,GAAG,CAAC;EACP;;EAEA;EACA,IAAIA,CAAC,GAAGb,CAAC,CAACV,MAAM,EAAE;IAChBU,CAAC,CAACe,MAAM,CAACF,CAAC,EAAE,CAAC,EAAEA,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EACtC;EACA,OAAOf,OAAO,CAAC3D,IAAI,GAAG6D,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAStC,aAAaA,CAAC3C,KAAK,EAAEuC,SAAS,EAAE;EAC9C,IAAIF,KAAK,CAACrC,KAAK,CAAC,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;IACpC,OAAOuD,MAAM,CAACvD,KAAK,CAAC;EACtB;;EAEA;EACA,IAAIiE,KAAK,GAAGZ,WAAW,CAACrD,KAAK,CAAC;EAC9B,IAAIsE,OAAO,GAAG/B,SAAS,GAAGgC,WAAW,CAACN,KAAK,EAAE1B,SAAS,CAAC,GAAG0B,KAAK;EAC/D,IAAIO,CAAC,GAAGF,OAAO,CAACP,YAAY;EAC5B,IAAId,CAAC,GAAGqB,OAAO,CAACZ,QAAQ;;EAExB;EACA,IAAIc,CAAC,CAACV,MAAM,GAAGvB,SAAS,EAAE;IACxBiC,CAAC,GAAGA,CAAC,CAACxC,MAAM,CAACgC,KAAK,CAACzB,SAAS,GAAGiC,CAAC,CAACV,MAAM,CAAC,CAAC;EAC3C;;EAEA;EACA,IAAI0B,KAAK,GAAGhB,CAAC,CAACiB,KAAK,CAAC,CAAC;EACrB,OAAOnB,OAAO,CAAC3D,IAAI,GAAG6E,KAAK,IAAIhB,CAAC,CAACV,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGU,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,IAAIhC,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGA,CAAC;AACtG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASJ,WAAWA,CAAC7C,KAAK,EAAEuC,SAAS,EAAEJ,OAAO,EAAE;EACrD,IAAIE,KAAK,CAACrC,KAAK,CAAC,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;IACpC,OAAOuD,MAAM,CAACvD,KAAK,CAAC;EACtB;;EAEA;EACA,IAAI0F,QAAQ,GAAGC,kBAAkB,CAACxD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuD,QAAQ,EAAE,CAAC,CAAC,CAAC;EACzG,IAAIE,QAAQ,GAAGD,kBAAkB,CAACxD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACyD,QAAQ,EAAE,CAAC,CAAC;EACxG,IAAI3B,KAAK,GAAGZ,WAAW,CAACrD,KAAK,CAAC;EAC9B,IAAIsE,OAAO,GAAG/B,SAAS,GAAGgC,WAAW,CAACN,KAAK,EAAE1B,SAAS,CAAC,GAAG0B,KAAK;EAC/D,IAAIK,OAAO,CAACZ,QAAQ,GAAGgC,QAAQ,IAAIpB,OAAO,CAACZ,QAAQ,IAAIkC,QAAQ,EAAE;IAC/D;IACA,OAAOjD,aAAa,CAAC3C,KAAK,EAAEuC,SAAS,CAAC;EACxC,CAAC,MAAM;IACL,IAAIiC,CAAC,GAAGF,OAAO,CAACP,YAAY;IAC5B,IAAId,CAAC,GAAGqB,OAAO,CAACZ,QAAQ;;IAExB;IACA,IAAIc,CAAC,CAACV,MAAM,GAAGvB,SAAS,EAAE;MACxBiC,CAAC,GAAGA,CAAC,CAACxC,MAAM,CAACgC,KAAK,CAACzB,SAAS,GAAGiC,CAAC,CAACV,MAAM,CAAC,CAAC;IAC3C;;IAEA;IACA;IACAU,CAAC,GAAGA,CAAC,CAACxC,MAAM,CAACgC,KAAK,CAACf,CAAC,GAAGuB,CAAC,CAACV,MAAM,GAAG,CAAC,IAAIU,CAAC,CAACV,MAAM,GAAGvB,SAAS,GAAGA,SAAS,GAAGiC,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEzF;IACAU,CAAC,GAAGR,KAAK,CAAC,CAACf,CAAC,CAAC,CAACjB,MAAM,CAACwC,CAAC,CAAC;IACvB,IAAIZ,GAAG,GAAGX,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC;IACvB,IAAIW,GAAG,GAAGY,CAAC,CAACV,MAAM,GAAG,CAAC,EAAE;MACtBU,CAAC,CAACe,MAAM,CAAC3B,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;IAC3B;IACA,OAAOU,OAAO,CAAC3D,IAAI,GAAG6D,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC;EAClC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASV,WAAWA,CAACN,KAAK,EAAE1B,SAAS,EAAE;EAC5C;EACA,IAAI+B,OAAO,GAAG;IACZ3D,IAAI,EAAEsD,KAAK,CAACtD,IAAI;IAChBoD,YAAY,EAAEE,KAAK,CAACF,YAAY;IAChCL,QAAQ,EAAEO,KAAK,CAACP;EAClB,CAAC;EACD,IAAIc,CAAC,GAAGF,OAAO,CAACP,YAAY;;EAE5B;EACA,OAAOxB,SAAS,IAAI,CAAC,EAAE;IACrBiC,CAAC,CAACqB,OAAO,CAAC,CAAC,CAAC;IACZvB,OAAO,CAACZ,QAAQ,EAAE;IAClBnB,SAAS,EAAE;EACb;EACA,IAAIiC,CAAC,CAACV,MAAM,GAAGvB,SAAS,EAAE;IACxB,IAAIuD,OAAO,GAAGtB,CAAC,CAACe,MAAM,CAAChD,SAAS,EAAEiC,CAAC,CAACV,MAAM,GAAGvB,SAAS,CAAC;IACvD,IAAIuD,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MACnB,IAAIlB,CAAC,GAAGrC,SAAS,GAAG,CAAC;MACrBiC,CAAC,CAACI,CAAC,CAAC,EAAE;MACN,OAAOJ,CAAC,CAACI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClBJ,CAAC,CAACuB,GAAG,CAAC,CAAC;QACP,IAAInB,CAAC,KAAK,CAAC,EAAE;UACXJ,CAAC,CAACqB,OAAO,CAAC,CAAC,CAAC;UACZvB,OAAO,CAACZ,QAAQ,EAAE;UAClBkB,CAAC,EAAE;QACL;QACAA,CAAC,EAAE;QACHJ,CAAC,CAACI,CAAC,CAAC,EAAE;MACR;IACF;EACF;EACA,OAAON,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASN,KAAKA,CAACF,MAAM,EAAE;EACrB,IAAIkC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,EAAEc,CAAC,EAAE,EAAE;IAC/BoB,GAAG,CAAC3B,IAAI,CAAC,CAAC,CAAC;EACb;EACA,OAAO2B,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASjD,MAAMA,CAAC/C,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAAC2C,aAAa,CAAC,CAAC,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;EAAA,CAChDA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;EAAA,CAC1BgB,MAAM;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIC,MAAM,GAAGpD,SAAS,CAACc,MAAM,GAAG,CAAC,IAAId,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACrF,IAAIqD,MAAM,GAAGrD,SAAS,CAACc,MAAM,GAAG,CAAC,IAAId,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAIoD,MAAM,IAAI,CAAC,EAAE;IACf,MAAM,IAAIrE,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EACA,IAAIsE,MAAM,GAAG,CAAC,EAAE;IACd,MAAM,IAAItE,KAAK,CAAC,uCAAuC,CAAC;EAC1D;;EAEA;EACA,IAAIM,KAAK,CAAC6D,CAAC,CAAC,IAAI7D,KAAK,CAAC8D,CAAC,CAAC,EAAE;IACxB,OAAO,KAAK;EACd;EACA,IAAI,CAAClG,QAAQ,CAACiG,CAAC,CAAC,IAAI,CAACjG,QAAQ,CAACkG,CAAC,CAAC,EAAE;IAChC,OAAOD,CAAC,KAAKC,CAAC;EAChB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;;EAEA;EACA,OAAOjG,IAAI,CAACyE,GAAG,CAACuB,CAAC,GAAGC,CAAC,CAAC,IAAIjG,IAAI,CAACoG,GAAG,CAACF,MAAM,GAAGlG,IAAI,CAACoG,GAAG,CAACpG,IAAI,CAACyE,GAAG,CAACuB,CAAC,CAAC,EAAEhG,IAAI,CAACyE,GAAG,CAACwB,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,KAAK,GAAGrG,IAAI,CAACqG,KAAK,IAAI,UAAU3F,CAAC,EAAE;EAC5C,OAAOV,IAAI,CAACY,GAAG,CAACZ,IAAI,CAACsG,IAAI,CAAC5F,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC,CAAC;AAC3C,CAAC;AACD,OAAO,IAAI6F,KAAK,GAAGvG,IAAI,CAACuG,KAAK,IAAI,UAAU7F,CAAC,EAAE;EAC5C,OAAOV,IAAI,CAACY,GAAG,CAACZ,IAAI,CAACsG,IAAI,CAAC5F,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC,CAAC;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI8F,KAAK,GAAGxG,IAAI,CAACwG,KAAK,IAAI,UAAU9F,CAAC,EAAE;EAC5C,OAAOV,IAAI,CAACY,GAAG,CAAC,CAAC,CAAC,GAAGF,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,CAAC,GAAG,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI+F,IAAI,GAAGzG,IAAI,CAACyG,IAAI,IAAI,UAAU/F,CAAC,EAAE;EAC1C,OAAO,CAACV,IAAI,CAACoB,GAAG,CAACV,CAAC,CAAC,GAAGV,IAAI,CAACoB,GAAG,CAAC,CAACV,CAAC,CAAC,IAAI,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgG,IAAI,GAAG1G,IAAI,CAAC0G,IAAI,IAAI,UAAUhG,CAAC,EAAE;EAC1C,OAAO,CAACV,IAAI,CAACoB,GAAG,CAACV,CAAC,CAAC,GAAGV,IAAI,CAACoB,GAAG,CAAC,CAACV,CAAC,CAAC,IAAI,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIiG,IAAI,GAAG3G,IAAI,CAAC2G,IAAI,IAAI,UAAUjG,CAAC,EAAE;EAC1C,IAAIqC,CAAC,GAAG/C,IAAI,CAACoB,GAAG,CAAC,CAAC,GAAGV,CAAC,CAAC;EACvB,OAAO,CAACqC,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6D,QAAQA,CAAClG,CAAC,EAAEmG,CAAC,EAAE;EAC7B,IAAIC,KAAK,GAAGpG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,CAAC,KAAKwB,QAAQ;EAC7D,IAAI6E,KAAK,GAAGF,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,CAAC,KAAK3E,QAAQ;EAC7D,OAAO4E,KAAK,GAAGC,KAAK,GAAG,CAACrG,CAAC,GAAGA,CAAC;AAC/B;AACA,SAASwC,gBAAgBA,CAACpD,KAAK,EAAEkH,OAAO,EAAE;EACxC,IAAIrH,QAAQ,CAACG,KAAK,CAAC,EAAE;IACnB,OAAOA,KAAK;EACd,CAAC,MAAM,IAAIJ,WAAW,CAACI,KAAK,CAAC,EAAE;IAC7B,OAAOA,KAAK,CAACmD,QAAQ,CAAC,CAAC;EACzB,CAAC,MAAM;IACL+D,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASvB,kBAAkBA,CAAC3F,KAAK,EAAEmH,YAAY,EAAE;EAC/C,IAAItH,QAAQ,CAACG,KAAK,CAAC,EAAE;IACnB,OAAOA,KAAK;EACd,CAAC,MAAM,IAAIJ,WAAW,CAACI,KAAK,CAAC,EAAE;IAC7B,OAAOA,KAAK,CAACmD,QAAQ,CAAC,CAAC;EACzB,CAAC,MAAM;IACL,OAAOgE,YAAY;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}