{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { createResize } from '../../factoriesAny.js';\nexport var resizeDependencies = {\n  matrixDependencies,\n  createResize\n};", "map": {"version": 3, "names": ["matrixDependencies", "createResize", "resizeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesResize.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { createResize } from '../../factoriesAny.js';\nexport var resizeDependencies = {\n  matrixDependencies,\n  createResize\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BF,kBAAkB;EAClBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}