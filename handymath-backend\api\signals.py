from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Equation, EquationImage
from .math_utils import solve_equation

@receiver(post_save, sender=EquationImage)
def process_equation_image(sender, instance, created, **kwargs):
    """
    Signal pour traiter automatiquement les images d'équations nouvellement téléchargées
    """
    if created and not instance.recognized_text:
        # Logique pour reconnaître le texte de l'équation à partir de l'image
        # Cette logique serait implémentée dans math_utils.py
        pass

@receiver(post_save, sender=Equation)
def solve_equation_on_create(sender, instance, created, **kwargs):
    """
    Signal pour résoudre automatiquement les équations nouvellement créées
    """
    if created and not instance.solution_text:
        # Résoudre l'équation
        try:
            solution = solve_equation(instance.equation_text)
            instance.solution_text = solution
            instance.save(update_fields=['solution_text'])
        except Exception as e:
            # <PERSON><PERSON>rer les erreurs de résolution
            print(f"Erreur lors de la résolution de l'équation: {e}")