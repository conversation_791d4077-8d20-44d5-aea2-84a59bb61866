{"ast": null, "code": "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n  //\n  // The following constants are related to IEEE 754 limits.\n  //\n\n  var width = 256,\n    // each RC4 output is 0 <= x < 256\n    chunks = 6,\n    // at least six RC4 outputs for each double\n    digits = 52,\n    // there are 52 significant digits in a double\n    rngname = 'random',\n    // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto; // node.js crypto module, initialized at the bottom.\n\n  //\n  // seedrandom()\n  // This is the seedrandom function described above.\n  //\n  function seedrandom(seed, options, callback) {\n    var key = [];\n    options = options == true ? {\n      entropy: true\n    } : options || {};\n\n    // Flatten the seed string or build one from local entropy if needed.\n    var shortseed = mixkey(flatten(options.entropy ? [seed, tostring(pool)] : seed == null ? autoseed() : seed, 3), key);\n\n    // Use the seed to initialize an ARC4 generator.\n    var arc4 = new ARC4(key);\n\n    // This function returns a random double in [0, 1) that contains\n    // randomness in every bit of the mantissa of the IEEE 754 value.\n    var prng = function () {\n      var n = arc4.g(chunks),\n        // Start with a numerator n < 2 ^ 48\n        d = startdenom,\n        //   and denominator d = 2 ^ 48.\n        x = 0; //   and no 'extra last byte'.\n      while (n < significance) {\n        // Fill up all significant digits by\n        n = (n + x) * width; //   shifting numerator and\n        d *= width; //   denominator and generating a\n        x = arc4.g(1); //   new least-significant-byte.\n      }\n      while (n >= overflow) {\n        // To avoid rounding up, before adding\n        n /= 2; //   last byte, shift everything\n        d /= 2; //   right using integer math until\n        x >>>= 1; //   we have exactly the desired bits.\n      }\n      return (n + x) / d; // Form the number within [0, 1).\n    };\n    prng.int32 = function () {\n      return arc4.g(4) | 0;\n    };\n    prng.quick = function () {\n      return arc4.g(4) / 0x100000000;\n    };\n    prng.double = prng;\n\n    // Mix the randomness into accumulated entropy.\n    mixkey(tostring(arc4.S), pool);\n\n    // Calling convention: what to return as a function of prng, seed, is_math.\n    return (options.pass || callback || function (prng, seed, is_math_call, state) {\n      if (state) {\n        // Load the arc4 state from the given state if it has an S array.\n        if (state.S) {\n          copy(state, arc4);\n        }\n        // Only provide the .state method if requested via options.state.\n        prng.state = function () {\n          return copy(arc4, {});\n        };\n      }\n\n      // If called as a method of Math (Math.seedrandom()), mutate\n      // Math.random because that is how seedrandom.js has worked since v1.0.\n      if (is_math_call) {\n        math[rngname] = prng;\n        return seed;\n      }\n\n      // Otherwise, it is a newer calling convention, so return the\n      // prng directly.\n      else return prng;\n    })(prng, shortseed, 'global' in options ? options.global : this == math, options.state);\n  }\n\n  //\n  // ARC4\n  //\n  // An ARC4 implementation.  The constructor takes a key in the form of\n  // an array of at most (width) integers that should be 0 <= x < (width).\n  //\n  // The g(count) method returns a pseudorandom integer that concatenates\n  // the next (count) outputs from ARC4.  Its return value is a number x\n  // that is in the range 0 <= x < (width ^ count).\n  //\n  function ARC4(key) {\n    var t,\n      keylen = key.length,\n      me = this,\n      i = 0,\n      j = me.i = me.j = 0,\n      s = me.S = [];\n\n    // The empty key [] is treated as [0].\n    if (!keylen) {\n      key = [keylen++];\n    }\n\n    // Set up S using the standard key scheduling algorithm.\n    while (i < width) {\n      s[i] = i++;\n    }\n    for (i = 0; i < width; i++) {\n      s[i] = s[j = mask & j + key[i % keylen] + (t = s[i])];\n      s[j] = t;\n    }\n\n    // The \"g\" method returns the next (count) outputs as one number.\n    (me.g = function (count) {\n      // Using instance members instead of closure state nearly doubles speed.\n      var t,\n        r = 0,\n        i = me.i,\n        j = me.j,\n        s = me.S;\n      while (count--) {\n        t = s[i = mask & i + 1];\n        r = r * width + s[mask & (s[i] = s[j = mask & j + t]) + (s[j] = t)];\n      }\n      me.i = i;\n      me.j = j;\n      return r;\n      // For robust unpredictability, the function call below automatically\n      // discards an initial batch of values.  This is called RC4-drop[256].\n      // See http://google.com/search?q=rsa+fluhrer+response&btnI\n    })(width);\n  }\n\n  //\n  // copy()\n  // Copies internal state of ARC4 to or from a plain object.\n  //\n  function copy(f, t) {\n    t.i = f.i;\n    t.j = f.j;\n    t.S = f.S.slice();\n    return t;\n  }\n  ;\n\n  //\n  // flatten()\n  // Converts an object tree to nested arrays of strings.\n  //\n  function flatten(obj, depth) {\n    var result = [],\n      typ = typeof obj,\n      prop;\n    if (depth && typ == 'object') {\n      for (prop in obj) {\n        try {\n          result.push(flatten(obj[prop], depth - 1));\n        } catch (e) {}\n      }\n    }\n    return result.length ? result : typ == 'string' ? obj : obj + '\\0';\n  }\n\n  //\n  // mixkey()\n  // Mixes a string seed into a key that is an array of integers, and\n  // returns a shortened string seed that is equivalent to the result key.\n  //\n  function mixkey(seed, key) {\n    var stringseed = seed + '',\n      smear,\n      j = 0;\n    while (j < stringseed.length) {\n      key[mask & j] = mask & (smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++);\n    }\n    return tostring(key);\n  }\n\n  //\n  // autoseed()\n  // Returns an object for autoseeding, using window.crypto and Node crypto\n  // module if available.\n  //\n  function autoseed() {\n    try {\n      var out;\n      if (nodecrypto && (out = nodecrypto.randomBytes)) {\n        // The use of 'out' to remember randomBytes makes tight minified code.\n        out = out(width);\n      } else {\n        out = new Uint8Array(width);\n        (global.crypto || global.msCrypto).getRandomValues(out);\n      }\n      return tostring(out);\n    } catch (e) {\n      var browser = global.navigator,\n        plugins = browser && browser.plugins;\n      return [+new Date(), global, plugins, global.screen, tostring(pool)];\n    }\n  }\n\n  //\n  // tostring()\n  // Converts an array of charcodes to a string\n  //\n  function tostring(a) {\n    return String.fromCharCode.apply(0, a);\n  }\n\n  //\n  // When seedrandom.js is loaded, we immediately mix a few bits\n  // from the built-in RNG into the entropy pool.  Because we do\n  // not want to interfere with deterministic PRNG state later,\n  // seedrandom will not call math.random on its own again after\n  // initialization.\n  //\n  mixkey(math.random(), pool);\n\n  //\n  // Nodejs and AMD support: export the implementation as a module using\n  // either convention.\n  //\n  if (typeof module == 'object' && module.exports) {\n    module.exports = seedrandom;\n    // When in node.js, try using crypto package for autoseeding.\n    try {\n      nodecrypto = require('crypto');\n    } catch (ex) {}\n  } else if (typeof define == 'function' && define.amd) {\n    define(function () {\n      return seedrandom;\n    });\n  } else {\n    // When included as a plain script, set up Math.seedrandom global.\n    math['seed' + rngname] = seedrandom;\n  }\n\n  // End anonymous scope, and pass initial values.\n})(\n// global: `self` in browsers (including strict mode and web workers),\n// otherwise `this` in Node and other environments\ntypeof self !== 'undefined' ? self : this, [],\n// pool: entropy pool starts empty\nMath // math: package containing random, pow, and seedrandom\n);", "map": {"version": 3, "names": ["global", "pool", "math", "width", "chunks", "digits", "rngname", "startdenom", "pow", "significance", "overflow", "mask", "nodecrypto", "seedrandom", "seed", "options", "callback", "key", "entropy", "shortseed", "mixkey", "flatten", "tostring", "autoseed", "arc4", "ARC4", "prng", "n", "g", "d", "x", "int32", "quick", "double", "S", "pass", "is_math_call", "state", "copy", "t", "keylen", "length", "me", "i", "j", "s", "count", "r", "f", "slice", "obj", "depth", "result", "typ", "prop", "push", "e", "stringseed", "smear", "charCodeAt", "out", "randomBytes", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "browser", "navigator", "plugins", "Date", "screen", "a", "String", "fromCharCode", "apply", "random", "module", "exports", "require", "ex", "define", "amd", "self", "Math"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/seedrandom/seedrandom.js"], "sourcesContent": ["/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC,UAAUA,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC/B;EACA;EACA;;EAEA,IAAIC,KAAK,GAAG,GAAG;IAAS;IACpBC,MAAM,GAAG,CAAC;IAAU;IACpBC,MAAM,GAAG,EAAE;IAAS;IACpBC,OAAO,GAAG,QAAQ;IAAE;IACpBC,UAAU,GAAGL,IAAI,CAACM,GAAG,CAACL,KAAK,EAAEC,MAAM,CAAC;IACpCK,YAAY,GAAGP,IAAI,CAACM,GAAG,CAAC,CAAC,EAAEH,MAAM,CAAC;IAClCK,QAAQ,GAAGD,YAAY,GAAG,CAAC;IAC3BE,IAAI,GAAGR,KAAK,GAAG,CAAC;IAChBS,UAAU,CAAC,CAAS;;EAExB;EACA;EACA;EACA;EACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC3C,IAAIC,GAAG,GAAG,EAAE;IACZF,OAAO,GAAIA,OAAO,IAAI,IAAI,GAAI;MAAEG,OAAO,EAAE;IAAK,CAAC,GAAIH,OAAO,IAAI,CAAC,CAAE;;IAEjE;IACA,IAAII,SAAS,GAAGC,MAAM,CAACC,OAAO,CAC5BN,OAAO,CAACG,OAAO,GAAG,CAACJ,IAAI,EAAEQ,QAAQ,CAACrB,IAAI,CAAC,CAAC,GACvCa,IAAI,IAAI,IAAI,GAAIS,QAAQ,CAAC,CAAC,GAAGT,IAAI,EAAE,CAAC,CAAC,EAAEG,GAAG,CAAC;;IAE9C;IACA,IAAIO,IAAI,GAAG,IAAIC,IAAI,CAACR,GAAG,CAAC;;IAExB;IACA;IACA,IAAIS,IAAI,GAAG,SAAAA,CAAA,EAAW;MACpB,IAAIC,CAAC,GAAGH,IAAI,CAACI,CAAC,CAACxB,MAAM,CAAC;QAAc;QAChCyB,CAAC,GAAGtB,UAAU;QAAkB;QAChCuB,CAAC,GAAG,CAAC,CAAC,CAA0B;MACpC,OAAOH,CAAC,GAAGlB,YAAY,EAAE;QAAW;QAClCkB,CAAC,GAAG,CAACA,CAAC,GAAGG,CAAC,IAAI3B,KAAK,CAAC,CAAc;QAClC0B,CAAC,IAAI1B,KAAK,CAAC,CAAuB;QAClC2B,CAAC,GAAGN,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAoB;MACpC;MACA,OAAOD,CAAC,IAAIjB,QAAQ,EAAE;QAAc;QAClCiB,CAAC,IAAI,CAAC,CAAC,CAA2B;QAClCE,CAAC,IAAI,CAAC,CAAC,CAA2B;QAClCC,CAAC,MAAM,CAAC,CAAC,CAAyB;MACpC;MACA,OAAO,CAACH,CAAC,GAAGG,CAAC,IAAID,CAAC,CAAC,CAAiB;IACtC,CAAC;IAEDH,IAAI,CAACK,KAAK,GAAG,YAAW;MAAE,OAAOP,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAAE,CAAC;IACjDF,IAAI,CAACM,KAAK,GAAG,YAAW;MAAE,OAAOR,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW;IAAE,CAAC;IAC3DF,IAAI,CAACO,MAAM,GAAGP,IAAI;;IAElB;IACAN,MAAM,CAACE,QAAQ,CAACE,IAAI,CAACU,CAAC,CAAC,EAAEjC,IAAI,CAAC;;IAE9B;IACA,OAAO,CAACc,OAAO,CAACoB,IAAI,IAAInB,QAAQ,IAC5B,UAASU,IAAI,EAAEZ,IAAI,EAAEsB,YAAY,EAAEC,KAAK,EAAE;MACxC,IAAIA,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACH,CAAC,EAAE;UAAEI,IAAI,CAACD,KAAK,EAAEb,IAAI,CAAC;QAAE;QAClC;QACAE,IAAI,CAACW,KAAK,GAAG,YAAW;UAAE,OAAOC,IAAI,CAACd,IAAI,EAAE,CAAC,CAAC,CAAC;QAAE,CAAC;MACpD;;MAEA;MACA;MACA,IAAIY,YAAY,EAAE;QAAElC,IAAI,CAACI,OAAO,CAAC,GAAGoB,IAAI;QAAE,OAAOZ,IAAI;MAAE;;MAEvD;MACA;MAAA,KACK,OAAOY,IAAI;IAClB,CAAC,EACLA,IAAI,EACJP,SAAS,EACT,QAAQ,IAAIJ,OAAO,GAAGA,OAAO,CAACf,MAAM,GAAI,IAAI,IAAIE,IAAK,EACrDa,OAAO,CAACsB,KAAK,CAAC;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASZ,IAAIA,CAACR,GAAG,EAAE;IACjB,IAAIsB,CAAC;MAAEC,MAAM,GAAGvB,GAAG,CAACwB,MAAM;MACtBC,EAAE,GAAG,IAAI;MAAEC,CAAC,GAAG,CAAC;MAAEC,CAAC,GAAGF,EAAE,CAACC,CAAC,GAAGD,EAAE,CAACE,CAAC,GAAG,CAAC;MAAEC,CAAC,GAAGH,EAAE,CAACR,CAAC,GAAG,EAAE;;IAExD;IACA,IAAI,CAACM,MAAM,EAAE;MAAEvB,GAAG,GAAG,CAACuB,MAAM,EAAE,CAAC;IAAE;;IAEjC;IACA,OAAOG,CAAC,GAAGxC,KAAK,EAAE;MAChB0C,CAAC,CAACF,CAAC,CAAC,GAAGA,CAAC,EAAE;IACZ;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxC,KAAK,EAAEwC,CAAC,EAAE,EAAE;MAC1BE,CAAC,CAACF,CAAC,CAAC,GAAGE,CAAC,CAACD,CAAC,GAAGjC,IAAI,GAAIiC,CAAC,GAAG3B,GAAG,CAAC0B,CAAC,GAAGH,MAAM,CAAC,IAAID,CAAC,GAAGM,CAAC,CAACF,CAAC,CAAC,CAAE,CAAC;MACvDE,CAAC,CAACD,CAAC,CAAC,GAAGL,CAAC;IACV;;IAEA;IACA,CAACG,EAAE,CAACd,CAAC,GAAG,UAASkB,KAAK,EAAE;MACtB;MACA,IAAIP,CAAC;QAAEQ,CAAC,GAAG,CAAC;QACRJ,CAAC,GAAGD,EAAE,CAACC,CAAC;QAAEC,CAAC,GAAGF,EAAE,CAACE,CAAC;QAAEC,CAAC,GAAGH,EAAE,CAACR,CAAC;MAChC,OAAOY,KAAK,EAAE,EAAE;QACdP,CAAC,GAAGM,CAAC,CAACF,CAAC,GAAGhC,IAAI,GAAIgC,CAAC,GAAG,CAAE,CAAC;QACzBI,CAAC,GAAGA,CAAC,GAAG5C,KAAK,GAAG0C,CAAC,CAAClC,IAAI,GAAI,CAACkC,CAAC,CAACF,CAAC,CAAC,GAAGE,CAAC,CAACD,CAAC,GAAGjC,IAAI,GAAIiC,CAAC,GAAGL,CAAE,CAAC,KAAKM,CAAC,CAACD,CAAC,CAAC,GAAGL,CAAC,CAAE,CAAC;MACzE;MACAG,EAAE,CAACC,CAAC,GAAGA,CAAC;MAAED,EAAE,CAACE,CAAC,GAAGA,CAAC;MAClB,OAAOG,CAAC;MACR;MACA;MACA;IACF,CAAC,EAAE5C,KAAK,CAAC;EACX;;EAEA;EACA;EACA;EACA;EACA,SAASmC,IAAIA,CAACU,CAAC,EAAET,CAAC,EAAE;IAClBA,CAAC,CAACI,CAAC,GAAGK,CAAC,CAACL,CAAC;IACTJ,CAAC,CAACK,CAAC,GAAGI,CAAC,CAACJ,CAAC;IACTL,CAAC,CAACL,CAAC,GAAGc,CAAC,CAACd,CAAC,CAACe,KAAK,CAAC,CAAC;IACjB,OAAOV,CAAC;EACV;EAAC;;EAED;EACA;EACA;EACA;EACA,SAASlB,OAAOA,CAAC6B,GAAG,EAAEC,KAAK,EAAE;IAC3B,IAAIC,MAAM,GAAG,EAAE;MAAEC,GAAG,GAAI,OAAOH,GAAI;MAAEI,IAAI;IACzC,IAAIH,KAAK,IAAIE,GAAG,IAAI,QAAQ,EAAE;MAC5B,KAAKC,IAAI,IAAIJ,GAAG,EAAE;QAChB,IAAI;UAAEE,MAAM,CAACG,IAAI,CAAClC,OAAO,CAAC6B,GAAG,CAACI,IAAI,CAAC,EAAEH,KAAK,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,CAAC,OAAOK,CAAC,EAAE,CAAC;MACjE;IACF;IACA,OAAQJ,MAAM,CAACX,MAAM,GAAGW,MAAM,GAAGC,GAAG,IAAI,QAAQ,GAAGH,GAAG,GAAGA,GAAG,GAAG,IAAI;EACrE;;EAEA;EACA;EACA;EACA;EACA;EACA,SAAS9B,MAAMA,CAACN,IAAI,EAAEG,GAAG,EAAE;IACzB,IAAIwC,UAAU,GAAG3C,IAAI,GAAG,EAAE;MAAE4C,KAAK;MAAEd,CAAC,GAAG,CAAC;IACxC,OAAOA,CAAC,GAAGa,UAAU,CAAChB,MAAM,EAAE;MAC5BxB,GAAG,CAACN,IAAI,GAAGiC,CAAC,CAAC,GACXjC,IAAI,GAAI,CAAC+C,KAAK,IAAIzC,GAAG,CAACN,IAAI,GAAGiC,CAAC,CAAC,GAAG,EAAE,IAAIa,UAAU,CAACE,UAAU,CAACf,CAAC,EAAE,CAAE;IACvE;IACA,OAAOtB,QAAQ,CAACL,GAAG,CAAC;EACtB;;EAEA;EACA;EACA;EACA;EACA;EACA,SAASM,QAAQA,CAAA,EAAG;IAClB,IAAI;MACF,IAAIqC,GAAG;MACP,IAAIhD,UAAU,KAAKgD,GAAG,GAAGhD,UAAU,CAACiD,WAAW,CAAC,EAAE;QAChD;QACAD,GAAG,GAAGA,GAAG,CAACzD,KAAK,CAAC;MAClB,CAAC,MAAM;QACLyD,GAAG,GAAG,IAAIE,UAAU,CAAC3D,KAAK,CAAC;QAC3B,CAACH,MAAM,CAAC+D,MAAM,IAAI/D,MAAM,CAACgE,QAAQ,EAAEC,eAAe,CAACL,GAAG,CAAC;MACzD;MACA,OAAOtC,QAAQ,CAACsC,GAAG,CAAC;IACtB,CAAC,CAAC,OAAOJ,CAAC,EAAE;MACV,IAAIU,OAAO,GAAGlE,MAAM,CAACmE,SAAS;QAC1BC,OAAO,GAAGF,OAAO,IAAIA,OAAO,CAACE,OAAO;MACxC,OAAO,CAAC,CAAC,IAAIC,IAAI,CAAD,CAAC,EAAErE,MAAM,EAAEoE,OAAO,EAAEpE,MAAM,CAACsE,MAAM,EAAEhD,QAAQ,CAACrB,IAAI,CAAC,CAAC;IACpE;EACF;;EAEA;EACA;EACA;EACA;EACA,SAASqB,QAAQA,CAACiD,CAAC,EAAE;IACnB,OAAOC,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,CAAC,EAAEH,CAAC,CAAC;EACxC;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACAnD,MAAM,CAAClB,IAAI,CAACyE,MAAM,CAAC,CAAC,EAAE1E,IAAI,CAAC;;EAE3B;EACA;EACA;EACA;EACA,IAAK,OAAO2E,MAAM,IAAK,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAE;IACjDD,MAAM,CAACC,OAAO,GAAGhE,UAAU;IAC3B;IACA,IAAI;MACFD,UAAU,GAAGkE,OAAO,CAAC,QAAQ,CAAC;IAChC,CAAC,CAAC,OAAOC,EAAE,EAAE,CAAC;EAChB,CAAC,MAAM,IAAK,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACtDD,MAAM,CAAC,YAAW;MAAE,OAAOnE,UAAU;IAAE,CAAC,CAAC;EAC3C,CAAC,MAAM;IACL;IACAX,IAAI,CAAC,MAAM,GAAGI,OAAO,CAAC,GAAGO,UAAU;EACrC;;EAGA;AACA,CAAC;AACC;AACA;AACC,OAAOqE,IAAI,KAAK,WAAW,GAAIA,IAAI,GAAG,IAAI,EAC3C,EAAE;AAAM;AACRC,IAAI,CAAI;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}