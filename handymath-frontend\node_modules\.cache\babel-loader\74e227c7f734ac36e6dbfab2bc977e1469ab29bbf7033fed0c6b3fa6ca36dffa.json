{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * This function determines if j is a leaf of the ith row subtree.\n * Consider A(i,j), node j in ith row subtree and return lca(jprev,j)\n *\n * @param {Number}  i               The ith row subtree\n * @param {Number}  j               The node to test\n * @param {Array}   w               The workspace array\n * @param {Number}  first           The index offset within the workspace for the first array\n * @param {Number}  maxfirst        The index offset within the workspace for the maxfirst array\n * @param {Number}  prevleaf        The index offset within the workspace for the prevleaf array\n * @param {Number}  ancestor        The index offset within the workspace for the ancestor array\n *\n * @return {Object}\n */\nexport function csLeaf(i, j, w, first, maxfirst, prevleaf, ancestor) {\n  var s, sparent;\n\n  // our result\n  var jleaf = 0;\n  var q;\n\n  // check j is a leaf\n  if (i <= j || w[first + j] <= w[maxfirst + i]) {\n    return -1;\n  }\n  // update max first[j] seen so far\n  w[maxfirst + i] = w[first + j];\n  // jprev = previous leaf of ith subtree\n  var jprev = w[prevleaf + i];\n  w[prevleaf + i] = j;\n\n  // check j is first or subsequent leaf\n  if (jprev === -1) {\n    // 1st leaf, q = root of ith subtree\n    jleaf = 1;\n    q = i;\n  } else {\n    // update jleaf\n    jleaf = 2;\n    // q = least common ancester (jprev,j)\n    for (q = jprev; q !== w[ancestor + q]; q = w[ancestor + q]);\n    for (s = jprev; s !== q; s = sparent) {\n      // path compression\n      sparent = w[ancestor + s];\n      w[ancestor + s] = q;\n    }\n  }\n  return {\n    jleaf,\n    q\n  };\n}", "map": {"version": 3, "names": ["csLeaf", "i", "j", "w", "first", "maxfirst", "prevleaf", "ancestor", "s", "sparent", "j<PERSON>", "q", "jprev"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csLeaf.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * This function determines if j is a leaf of the ith row subtree.\n * Consider A(i,j), node j in ith row subtree and return lca(jprev,j)\n *\n * @param {Number}  i               The ith row subtree\n * @param {Number}  j               The node to test\n * @param {Array}   w               The workspace array\n * @param {Number}  first           The index offset within the workspace for the first array\n * @param {Number}  maxfirst        The index offset within the workspace for the maxfirst array\n * @param {Number}  prevleaf        The index offset within the workspace for the prevleaf array\n * @param {Number}  ancestor        The index offset within the workspace for the ancestor array\n *\n * @return {Object}\n */\nexport function csLeaf(i, j, w, first, maxfirst, prevleaf, ancestor) {\n  var s, sparent;\n\n  // our result\n  var jleaf = 0;\n  var q;\n\n  // check j is a leaf\n  if (i <= j || w[first + j] <= w[maxfirst + i]) {\n    return -1;\n  }\n  // update max first[j] seen so far\n  w[maxfirst + i] = w[first + j];\n  // jprev = previous leaf of ith subtree\n  var jprev = w[prevleaf + i];\n  w[prevleaf + i] = j;\n\n  // check j is first or subsequent leaf\n  if (jprev === -1) {\n    // 1st leaf, q = root of ith subtree\n    jleaf = 1;\n    q = i;\n  } else {\n    // update jleaf\n    jleaf = 2;\n    // q = least common ancester (jprev,j)\n    for (q = jprev; q !== w[ancestor + q]; q = w[ancestor + q]);\n    for (s = jprev; s !== q; s = sparent) {\n      // path compression\n      sparent = w[ancestor + s];\n      w[ancestor + s] = q;\n    }\n  }\n  return {\n    jleaf,\n    q\n  };\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACnE,IAAIC,CAAC,EAAEC,OAAO;;EAEd;EACA,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,CAAC;;EAEL;EACA,IAAIV,CAAC,IAAIC,CAAC,IAAIC,CAAC,CAACC,KAAK,GAAGF,CAAC,CAAC,IAAIC,CAAC,CAACE,QAAQ,GAAGJ,CAAC,CAAC,EAAE;IAC7C,OAAO,CAAC,CAAC;EACX;EACA;EACAE,CAAC,CAACE,QAAQ,GAAGJ,CAAC,CAAC,GAAGE,CAAC,CAACC,KAAK,GAAGF,CAAC,CAAC;EAC9B;EACA,IAAIU,KAAK,GAAGT,CAAC,CAACG,QAAQ,GAAGL,CAAC,CAAC;EAC3BE,CAAC,CAACG,QAAQ,GAAGL,CAAC,CAAC,GAAGC,CAAC;;EAEnB;EACA,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB;IACAF,KAAK,GAAG,CAAC;IACTC,CAAC,GAAGV,CAAC;EACP,CAAC,MAAM;IACL;IACAS,KAAK,GAAG,CAAC;IACT;IACA,KAAKC,CAAC,GAAGC,KAAK,EAAED,CAAC,KAAKR,CAAC,CAACI,QAAQ,GAAGI,CAAC,CAAC,EAAEA,CAAC,GAAGR,CAAC,CAACI,QAAQ,GAAGI,CAAC,CAAC,CAAC;IAC3D,KAAKH,CAAC,GAAGI,KAAK,EAAEJ,CAAC,KAAKG,CAAC,EAAEH,CAAC,GAAGC,OAAO,EAAE;MACpC;MACAA,OAAO,GAAGN,CAAC,CAACI,QAAQ,GAAGC,CAAC,CAAC;MACzBL,CAAC,CAACI,QAAQ,GAAGC,CAAC,CAAC,GAAGG,CAAC;IACrB;EACF;EACA,OAAO;IACLD,KAAK;IACLC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}