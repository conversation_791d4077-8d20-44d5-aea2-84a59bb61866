{"ast": null, "code": "// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n(function (global, module, define) {\n  function Alea(seed) {\n    var me = this,\n      mash = Mash();\n    me.next = function () {\n      var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n      me.s0 = me.s1;\n      me.s1 = me.s2;\n      return me.s2 = t - (me.c = t | 0);\n    };\n\n    // Apply the seeding algorithm from Baagoe.\n    me.c = 1;\n    me.s0 = mash(' ');\n    me.s1 = mash(' ');\n    me.s2 = mash(' ');\n    me.s0 -= mash(seed);\n    if (me.s0 < 0) {\n      me.s0 += 1;\n    }\n    me.s1 -= mash(seed);\n    if (me.s1 < 0) {\n      me.s1 += 1;\n    }\n    me.s2 -= mash(seed);\n    if (me.s2 < 0) {\n      me.s2 += 1;\n    }\n    mash = null;\n  }\n  function copy(f, t) {\n    t.c = f.c;\n    t.s0 = f.s0;\n    t.s1 = f.s1;\n    t.s2 = f.s2;\n    return t;\n  }\n  function impl(seed, opts) {\n    var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n    prng.int32 = function () {\n      return xg.next() * 0x100000000 | 0;\n    };\n    prng.double = function () {\n      return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n    };\n    prng.quick = prng;\n    if (state) {\n      if (typeof state == 'object') copy(state, xg);\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n    return prng;\n  }\n  function Mash() {\n    var n = 0xefc8249d;\n    var mash = function (data) {\n      data = String(data);\n      for (var i = 0; i < data.length; i++) {\n        n += data.charCodeAt(i);\n        var h = 0.02519603282416938 * n;\n        n = h >>> 0;\n        h -= n;\n        h *= n;\n        n = h >>> 0;\n        h -= n;\n        n += h * 0x100000000; // 2^32\n      }\n      return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n    };\n    return mash;\n  }\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.alea = impl;\n  }\n})(this, typeof module == 'object' && module,\n// present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "Alea", "seed", "me", "mash", "<PERSON><PERSON>", "next", "t", "s0", "c", "s1", "s2", "copy", "f", "impl", "opts", "xg", "state", "prng", "int32", "double", "quick", "n", "data", "String", "i", "length", "charCodeAt", "h", "exports", "amd", "alea"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/seedrandom/lib/alea.js"], "sourcesContent": ["// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA,CAAC,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAElC,SAASC,IAAIA,CAACC,IAAI,EAAE;IAClB,IAAIC,EAAE,GAAG,IAAI;MAAEC,IAAI,GAAGC,IAAI,CAAC,CAAC;IAE5BF,EAAE,CAACG,IAAI,GAAG,YAAW;MACnB,IAAIC,CAAC,GAAG,OAAO,GAAGJ,EAAE,CAACK,EAAE,GAAGL,EAAE,CAACM,CAAC,GAAG,sBAAsB,CAAC,CAAC;MACzDN,EAAE,CAACK,EAAE,GAAGL,EAAE,CAACO,EAAE;MACbP,EAAE,CAACO,EAAE,GAAGP,EAAE,CAACQ,EAAE;MACb,OAAOR,EAAE,CAACQ,EAAE,GAAGJ,CAAC,IAAIJ,EAAE,CAACM,CAAC,GAAGF,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;;IAED;IACAJ,EAAE,CAACM,CAAC,GAAG,CAAC;IACRN,EAAE,CAACK,EAAE,GAAGJ,IAAI,CAAC,GAAG,CAAC;IACjBD,EAAE,CAACO,EAAE,GAAGN,IAAI,CAAC,GAAG,CAAC;IACjBD,EAAE,CAACQ,EAAE,GAAGP,IAAI,CAAC,GAAG,CAAC;IACjBD,EAAE,CAACK,EAAE,IAAIJ,IAAI,CAACF,IAAI,CAAC;IACnB,IAAIC,EAAE,CAACK,EAAE,GAAG,CAAC,EAAE;MAAEL,EAAE,CAACK,EAAE,IAAI,CAAC;IAAE;IAC7BL,EAAE,CAACO,EAAE,IAAIN,IAAI,CAACF,IAAI,CAAC;IACnB,IAAIC,EAAE,CAACO,EAAE,GAAG,CAAC,EAAE;MAAEP,EAAE,CAACO,EAAE,IAAI,CAAC;IAAE;IAC7BP,EAAE,CAACQ,EAAE,IAAIP,IAAI,CAACF,IAAI,CAAC;IACnB,IAAIC,EAAE,CAACQ,EAAE,GAAG,CAAC,EAAE;MAAER,EAAE,CAACQ,EAAE,IAAI,CAAC;IAAE;IAC7BP,IAAI,GAAG,IAAI;EACb;EAEA,SAASQ,IAAIA,CAACC,CAAC,EAAEN,CAAC,EAAE;IAClBA,CAAC,CAACE,CAAC,GAAGI,CAAC,CAACJ,CAAC;IACTF,CAAC,CAACC,EAAE,GAAGK,CAAC,CAACL,EAAE;IACXD,CAAC,CAACG,EAAE,GAAGG,CAAC,CAACH,EAAE;IACXH,CAAC,CAACI,EAAE,GAAGE,CAAC,CAACF,EAAE;IACX,OAAOJ,CAAC;EACV;EAEA,SAASO,IAAIA,CAACZ,IAAI,EAAEa,IAAI,EAAE;IACxB,IAAIC,EAAE,GAAG,IAAIf,IAAI,CAACC,IAAI,CAAC;MACnBe,KAAK,GAAGF,IAAI,IAAIA,IAAI,CAACE,KAAK;MAC1BC,IAAI,GAAGF,EAAE,CAACV,IAAI;IAClBY,IAAI,CAACC,KAAK,GAAG,YAAW;MAAE,OAAQH,EAAE,CAACV,IAAI,CAAC,CAAC,GAAG,WAAW,GAAI,CAAC;IAAE,CAAC;IACjEY,IAAI,CAACE,MAAM,GAAG,YAAW;MACvB,OAAOF,IAAI,CAAC,CAAC,GAAG,CAACA,IAAI,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAsB,CAAC,CAAC;IACpE,CAAC;IACDA,IAAI,CAACG,KAAK,GAAGH,IAAI;IACjB,IAAID,KAAK,EAAE;MACT,IAAI,OAAOA,KAAM,IAAI,QAAQ,EAAEL,IAAI,CAACK,KAAK,EAAED,EAAE,CAAC;MAC9CE,IAAI,CAACD,KAAK,GAAG,YAAW;QAAE,OAAOL,IAAI,CAACI,EAAE,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC;IAClD;IACA,OAAOE,IAAI;EACb;EAEA,SAASb,IAAIA,CAAA,EAAG;IACd,IAAIiB,CAAC,GAAG,UAAU;IAElB,IAAIlB,IAAI,GAAG,SAAAA,CAASmB,IAAI,EAAE;MACxBA,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC;MACnB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACpCH,CAAC,IAAIC,IAAI,CAACI,UAAU,CAACF,CAAC,CAAC;QACvB,IAAIG,CAAC,GAAG,mBAAmB,GAAGN,CAAC;QAC/BA,CAAC,GAAGM,CAAC,KAAK,CAAC;QACXA,CAAC,IAAIN,CAAC;QACNM,CAAC,IAAIN,CAAC;QACNA,CAAC,GAAGM,CAAC,KAAK,CAAC;QACXA,CAAC,IAAIN,CAAC;QACNA,CAAC,IAAIM,CAAC,GAAG,WAAW,CAAC,CAAC;MACxB;MACA,OAAO,CAACN,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC;IAC7C,CAAC;IAED,OAAOlB,IAAI;EACb;EAGA,IAAIL,MAAM,IAAIA,MAAM,CAAC8B,OAAO,EAAE;IAC5B9B,MAAM,CAAC8B,OAAO,GAAGf,IAAI;EACvB,CAAC,MAAM,IAAId,MAAM,IAAIA,MAAM,CAAC8B,GAAG,EAAE;IAC/B9B,MAAM,CAAC,YAAW;MAAE,OAAOc,IAAI;IAAE,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAI,CAACiB,IAAI,GAAGjB,IAAI;EAClB;AAEA,CAAC,EACC,IAAI,EACH,OAAOf,MAAM,IAAK,QAAQ,IAAIA,MAAM;AAAK;AACzC,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAAG;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}