#!/usr/bin/env python
"""
Script de test pour déboguer la reconnaissance OCR
"""
import os
import sys
import django
from PIL import Image, ImageDraw, ImageFont
import io
import base64

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

def create_test_equation_image(equation_text="2x + 3 = 7", size=(400, 100)):
    """Créer une image de test avec une équation"""
    # Créer une image blanche
    img = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(img)
    
    # Essayer d'utiliser une police système
    try:
        # Taille de police plus grande pour une meilleure reconnaissance
        font = ImageFont.truetype("arial.ttf", 36)
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 36)
        except:
            # Utiliser la police par défaut si aucune police n'est trouvée
            font = ImageFont.load_default()
    
    # Calculer la position pour centrer le texte
    bbox = draw.textbbox((0, 0), equation_text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # Dessiner le texte en noir
    draw.text((x, y), equation_text, fill='black', font=font)
    
    return img

def test_ocr_functionality():
    """Tester la fonctionnalité OCR"""
    print("=== Test de la reconnaissance OCR ===")
    
    # Créer une image de test
    test_equations = [
        "2x + 3 = 7",
        "x^2 - 4 = 0", 
        "3x = 15",
        "x + 5 = 10"
    ]
    
    for equation in test_equations:
        print(f"\n--- Test avec l'équation: {equation} ---")
        
        # Créer l'image
        img = create_test_equation_image(equation)
        
        # Sauvegarder temporairement l'image
        temp_path = f"temp_equation_{equation.replace(' ', '_').replace('=', 'eq').replace('+', 'plus').replace('^', 'pow')}.png"
        img.save(temp_path)
        print(f"Image sauvegardée: {temp_path}")
        
        # Tester avec la fonction OCR du projet
        try:
            from api.utils.ocr import recognize_equation
            result = recognize_equation(temp_path)
            print(f"Résultat OCR (fonction du projet): '{result}'")
        except Exception as e:
            print(f"Erreur avec la fonction du projet: {e}")
        
        # Tester avec pytesseract directement
        try:
            import pytesseract
            
            # Configuration basique
            basic_result = pytesseract.image_to_string(img)
            print(f"Résultat OCR (basique): '{basic_result.strip()}'")
            
            # Configuration pour les mathématiques
            math_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '
            math_result = pytesseract.image_to_string(img, config=math_config)
            print(f"Résultat OCR (config math): '{math_result.strip()}'")
            
            # Configuration plus permissive
            permissive_config = r'--oem 3 --psm 8'
            permissive_result = pytesseract.image_to_string(img, config=permissive_config)
            print(f"Résultat OCR (permissif): '{permissive_result.strip()}'")
            
        except Exception as e:
            print(f"Erreur avec pytesseract: {e}")
        
        # Nettoyer le fichier temporaire
        try:
            os.remove(temp_path)
        except:
            pass

def test_preprocessing():
    """Tester le préprocessing d'image"""
    print("\n=== Test du préprocessing d'image ===")
    
    # Créer une image de test
    img = create_test_equation_image("x + 2 = 5")
    
    # Tester le préprocessing
    try:
        from api.views import preprocess_image_for_ocr
        processed_img = preprocess_image_for_ocr(img)
        
        # Sauvegarder les images pour comparaison
        img.save("original_test.png")
        processed_img.save("processed_test.png")
        
        print("Images sauvegardées: original_test.png et processed_test.png")
        
        # Tester OCR sur l'image préprocessée
        import pytesseract
        result = pytesseract.image_to_string(processed_img)
        print(f"Résultat OCR sur image préprocessée: '{result.strip()}'")
        
    except Exception as e:
        print(f"Erreur lors du préprocessing: {e}")

if __name__ == "__main__":
    test_ocr_functionality()
    test_preprocessing()
    print("\n=== Test terminé ===")
