{"ast": null, "code": "export var compileDocs = {\n  name: 'compile',\n  category: 'Expression',\n  syntax: ['compile(expr) ', 'compile([expr1, expr2, expr3, ...])'],\n  description: 'Parse and compile an expression. Returns a an object with a function evaluate([scope]) to evaluate the compiled expression.',\n  examples: ['code1 = compile(\"sqrt(3^2 + 4^2)\")', 'code1.evaluate() ', 'code2 = compile(\"a * b\")', 'code2.evaluate({a: 3, b: 4})'],\n  seealso: ['parser', 'parse', 'evaluate']\n};", "map": {"version": 3, "names": ["compileDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/expression/compile.js"], "sourcesContent": ["export var compileDocs = {\n  name: 'compile',\n  category: 'Expression',\n  syntax: ['compile(expr) ', 'compile([expr1, expr2, expr3, ...])'],\n  description: 'Parse and compile an expression. Returns a an object with a function evaluate([scope]) to evaluate the compiled expression.',\n  examples: ['code1 = compile(\"sqrt(3^2 + 4^2)\")', 'code1.evaluate() ', 'code2 = compile(\"a * b\")', 'code2.evaluate({a: 3, b: 4})'],\n  seealso: ['parser', 'parse', 'evaluate']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;EACjEC,WAAW,EAAE,6HAA6H;EAC1IC,QAAQ,EAAE,CAAC,oCAAoC,EAAE,mBAAmB,EAAE,0BAA0B,EAAE,8BAA8B,CAAC;EACjIC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}