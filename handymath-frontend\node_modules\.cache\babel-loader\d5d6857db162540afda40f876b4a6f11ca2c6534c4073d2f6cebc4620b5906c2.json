{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSqrt } from '../../factoriesAny.js';\nexport var sqrtDependencies = {\n  ComplexDependencies,\n  typedDependencies,\n  createSqrt\n};", "map": {"version": 3, "names": ["ComplexDependencies", "typedDependencies", "createSqrt", "sqrtDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSqrt.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSqrt } from '../../factoriesAny.js';\nexport var sqrtDependencies = {\n  ComplexDependencies,\n  typedDependencies,\n  createSqrt\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BH,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}