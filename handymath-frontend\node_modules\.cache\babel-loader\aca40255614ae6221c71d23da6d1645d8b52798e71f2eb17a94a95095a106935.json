{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { simplifyDependencies } from './dependenciesSimplify.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSymbolicEqual } from '../../factoriesAny.js';\nexport var symbolicEqualDependencies = {\n  OperatorNodeDependencies,\n  parseDependencies,\n  simplifyDependencies,\n  typedDependencies,\n  createSymbolicEqual\n};", "map": {"version": 3, "names": ["OperatorNodeDependencies", "parseDependencies", "simplifyDependencies", "typedDependencies", "createSymbolicEqual", "symbolicEqualDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSymbolicEqual.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { simplifyDependencies } from './dependenciesSimplify.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSymbolicEqual } from '../../factoriesAny.js';\nexport var symbolicEqualDependencies = {\n  OperatorNodeDependencies,\n  parseDependencies,\n  simplifyDependencies,\n  typedDependencies,\n  createSymbolicEqual\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,OAAO,IAAIC,yBAAyB,GAAG;EACrCL,wBAAwB;EACxBC,iBAAiB;EACjBC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}