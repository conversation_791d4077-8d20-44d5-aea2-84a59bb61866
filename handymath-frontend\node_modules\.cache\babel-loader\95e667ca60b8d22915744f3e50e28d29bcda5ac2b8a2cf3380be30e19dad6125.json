{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csMarked } from './csMarked.js';\nimport { csMark } from './csMark.js';\nimport { csUnflip } from './csUnflip.js';\n\n/**\n * Depth-first search computes the nonzero pattern xi of the directed graph G (Matrix) starting\n * at nodes in B (see csReach()).\n *\n * @param {Number}  j               The starting node for the DFS algorithm\n * @param {Matrix}  g               The G matrix to search, ptr array modified, then restored\n * @param {Number}  top             Start index in stack xi[top..n-1]\n * @param {Number}  k               The kth column in B\n * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n * @param {Array}   pinv            The inverse row permutation vector, must be null for L * x = b\n *\n * @return {Number}                 New value of top\n */\nexport function csDfs(j, g, top, xi, pinv) {\n  // g arrays\n  var index = g._index;\n  var ptr = g._ptr;\n  var size = g._size;\n  // columns\n  var n = size[1];\n  // vars\n  var i, p, p2;\n  // initialize head\n  var head = 0;\n  // initialize the recursion stack\n  xi[0] = j;\n  // loop\n  while (head >= 0) {\n    // get j from the top of the recursion stack\n    j = xi[head];\n    // apply permutation vector\n    var jnew = pinv ? pinv[j] : j;\n    // check node j is marked\n    if (!csMarked(ptr, j)) {\n      // mark node j as visited\n      csMark(ptr, j);\n      // update stack (last n entries in xi)\n      xi[n + head] = jnew < 0 ? 0 : csUnflip(ptr[jnew]);\n    }\n    // node j done if no unvisited neighbors\n    var done = 1;\n    // examine all neighbors of j, stack (last n entries in xi)\n    for (p = xi[n + head], p2 = jnew < 0 ? 0 : csUnflip(ptr[jnew + 1]); p < p2; p++) {\n      // consider neighbor node i\n      i = index[p];\n      // check we have visited node i, skip it\n      if (csMarked(ptr, i)) {\n        continue;\n      }\n      // pause depth-first search of node j, update stack (last n entries in xi)\n      xi[n + head] = p;\n      // start dfs at node i\n      xi[++head] = i;\n      // node j is not done\n      done = 0;\n      // break, to start dfs(i)\n      break;\n    }\n    // check depth-first search at node j is done\n    if (done) {\n      // remove j from the recursion stack\n      head--;\n      // and place in the output stack\n      xi[--top] = j;\n    }\n  }\n  return top;\n}", "map": {"version": 3, "names": ["csMarked", "csMark", "csUnflip", "csDfs", "j", "g", "top", "xi", "pinv", "index", "_index", "ptr", "_ptr", "size", "_size", "n", "i", "p", "p2", "head", "jnew", "done"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csDfs.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csMarked } from './csMarked.js';\nimport { csMark } from './csMark.js';\nimport { csUnflip } from './csUnflip.js';\n\n/**\n * Depth-first search computes the nonzero pattern xi of the directed graph G (Matrix) starting\n * at nodes in B (see csReach()).\n *\n * @param {Number}  j               The starting node for the DFS algorithm\n * @param {Matrix}  g               The G matrix to search, ptr array modified, then restored\n * @param {Number}  top             Start index in stack xi[top..n-1]\n * @param {Number}  k               The kth column in B\n * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n * @param {Array}   pinv            The inverse row permutation vector, must be null for L * x = b\n *\n * @return {Number}                 New value of top\n */\nexport function csDfs(j, g, top, xi, pinv) {\n  // g arrays\n  var index = g._index;\n  var ptr = g._ptr;\n  var size = g._size;\n  // columns\n  var n = size[1];\n  // vars\n  var i, p, p2;\n  // initialize head\n  var head = 0;\n  // initialize the recursion stack\n  xi[0] = j;\n  // loop\n  while (head >= 0) {\n    // get j from the top of the recursion stack\n    j = xi[head];\n    // apply permutation vector\n    var jnew = pinv ? pinv[j] : j;\n    // check node j is marked\n    if (!csMarked(ptr, j)) {\n      // mark node j as visited\n      csMark(ptr, j);\n      // update stack (last n entries in xi)\n      xi[n + head] = jnew < 0 ? 0 : csUnflip(ptr[jnew]);\n    }\n    // node j done if no unvisited neighbors\n    var done = 1;\n    // examine all neighbors of j, stack (last n entries in xi)\n    for (p = xi[n + head], p2 = jnew < 0 ? 0 : csUnflip(ptr[jnew + 1]); p < p2; p++) {\n      // consider neighbor node i\n      i = index[p];\n      // check we have visited node i, skip it\n      if (csMarked(ptr, i)) {\n        continue;\n      }\n      // pause depth-first search of node j, update stack (last n entries in xi)\n      xi[n + head] = p;\n      // start dfs at node i\n      xi[++head] = i;\n      // node j is not done\n      done = 0;\n      // break, to start dfs(i)\n      break;\n    }\n    // check depth-first search at node j is done\n    if (done) {\n      // remove j from the recursion stack\n      head--;\n      // and place in the output stack\n      xi[--top] = j;\n    }\n  }\n  return top;\n}"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,eAAe;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAEC,EAAE,EAAEC,IAAI,EAAE;EACzC;EACA,IAAIC,KAAK,GAAGJ,CAAC,CAACK,MAAM;EACpB,IAAIC,GAAG,GAAGN,CAAC,CAACO,IAAI;EAChB,IAAIC,IAAI,GAAGR,CAAC,CAACS,KAAK;EAClB;EACA,IAAIC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;EACf;EACA,IAAIG,CAAC,EAAEC,CAAC,EAAEC,EAAE;EACZ;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ;EACAZ,EAAE,CAAC,CAAC,CAAC,GAAGH,CAAC;EACT;EACA,OAAOe,IAAI,IAAI,CAAC,EAAE;IAChB;IACAf,CAAC,GAAGG,EAAE,CAACY,IAAI,CAAC;IACZ;IACA,IAAIC,IAAI,GAAGZ,IAAI,GAAGA,IAAI,CAACJ,CAAC,CAAC,GAAGA,CAAC;IAC7B;IACA,IAAI,CAACJ,QAAQ,CAACW,GAAG,EAAEP,CAAC,CAAC,EAAE;MACrB;MACAH,MAAM,CAACU,GAAG,EAAEP,CAAC,CAAC;MACd;MACAG,EAAE,CAACQ,CAAC,GAAGI,IAAI,CAAC,GAAGC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGlB,QAAQ,CAACS,GAAG,CAACS,IAAI,CAAC,CAAC;IACnD;IACA;IACA,IAAIC,IAAI,GAAG,CAAC;IACZ;IACA,KAAKJ,CAAC,GAAGV,EAAE,CAACQ,CAAC,GAAGI,IAAI,CAAC,EAAED,EAAE,GAAGE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGlB,QAAQ,CAACS,GAAG,CAACS,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEH,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;MAC/E;MACAD,CAAC,GAAGP,KAAK,CAACQ,CAAC,CAAC;MACZ;MACA,IAAIjB,QAAQ,CAACW,GAAG,EAAEK,CAAC,CAAC,EAAE;QACpB;MACF;MACA;MACAT,EAAE,CAACQ,CAAC,GAAGI,IAAI,CAAC,GAAGF,CAAC;MAChB;MACAV,EAAE,CAAC,EAAEY,IAAI,CAAC,GAAGH,CAAC;MACd;MACAK,IAAI,GAAG,CAAC;MACR;MACA;IACF;IACA;IACA,IAAIA,IAAI,EAAE;MACR;MACAF,IAAI,EAAE;MACN;MACAZ,EAAE,CAAC,EAAED,GAAG,CAAC,GAAGF,CAAC;IACf;EACF;EACA,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}