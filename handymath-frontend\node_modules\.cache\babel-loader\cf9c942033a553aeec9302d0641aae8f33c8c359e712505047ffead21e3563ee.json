{"ast": null, "code": "export var xorDocs = {\n  name: 'xor',\n  category: 'Logical',\n  syntax: ['x xor y', 'xor(x, y)'],\n  description: 'Logical exclusive or, xor. Test whether one and only one value is defined with a nonzero/nonempty value.',\n  examples: ['true xor false', 'false xor false', 'true xor true', '0 xor 4'],\n  seealso: ['not', 'and', 'or']\n};", "map": {"version": 3, "names": ["xorDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/logical/xor.js"], "sourcesContent": ["export var xorDocs = {\n  name: 'xor',\n  category: 'Logical',\n  syntax: ['x xor y', 'xor(x, y)'],\n  description: 'Logical exclusive or, xor. Test whether one and only one value is defined with a nonzero/nonempty value.',\n  examples: ['true xor false', 'false xor false', 'true xor true', '0 xor 4'],\n  seealso: ['not', 'and', 'or']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;EAChCC,WAAW,EAAE,0GAA0G;EACvHC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,eAAe,EAAE,SAAS,CAAC;EAC3EC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}