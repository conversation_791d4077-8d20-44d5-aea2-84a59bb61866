#!/usr/bin/env python3
"""
Script de nettoyage pour supprimer les fichiers temporaires et de debug
"""

import os
import shutil
from pathlib import Path

def cleanup_backend():
    """Nettoyer le backend"""
    backend_path = Path("handymath-backend")
    
    # Fichiers de debug et test temporaires à supprimer
    files_to_remove = [
        # Scripts de debug temporaires
        "check_existing_users_progression.py",
        "debug_enrollment_issues.py", 
        "debug_lesson_logic.py",
        "debug_progression_issues.py",
        "diagnose_progression_stability.py",
        "final_test_progression.py",
        "find_45_percent_issue.py",
        "fix_enrollment_issues.py",
        "fix_lesson_data.py",
        "test_course_detail_frontend.py",
        "test_final_fixes.py",
        "test_frontend_api.py",
        "test_lesson_access.py",
        "test_progression_stability.py",
        
        # Fichiers OCR de test
        "test_api_ocr.py",
        "test_final_ocr.py", 
        "test_ocr_debug.py",
        "test_ocr_simple.py",
        "original_test.png",
        "processed_test.png",
        "test_api_image.png",
        "test_processed.png",
        "test_simple_equation.png",
        "test_view_processed.png",
        
        # Fichiers de contact de test
        "test_contact_api.py",
        "test_contact_page.html",
        "test_contact_simple.py",
        
        # Rapports temporaires
        "CAPTURES_POUR_RAPPORT.md",
        "GUIDE_CAPTURES_TESTS.md", 
        "RAPPORT_CORRECTION_OCR.md",
        "RAPPORT_TESTS_PYTEST.md",
        "generer_captures_tests.bat",
        "rapport_tests_handymath.html",
    ]
    
    # Dossiers à supprimer
    dirs_to_remove = [
        "htmlcov",  # Couverture de code
        "admin",    # Dossier admin vide ou inutile
    ]
    
    print("🧹 NETTOYAGE DU BACKEND")
    print("=" * 30)
    
    # Supprimer les fichiers
    for file_name in files_to_remove:
        file_path = backend_path / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"✅ Supprimé: {file_name}")
            except Exception as e:
                print(f"❌ Erreur lors de la suppression de {file_name}: {e}")
        else:
            print(f"⚠️ Fichier non trouvé: {file_name}")
    
    # Supprimer les dossiers
    for dir_name in dirs_to_remove:
        dir_path = backend_path / dir_name
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ Dossier supprimé: {dir_name}")
            except Exception as e:
                print(f"❌ Erreur lors de la suppression du dossier {dir_name}: {e}")
        else:
            print(f"⚠️ Dossier non trouvé: {dir_name}")

def cleanup_frontend():
    """Nettoyer le frontend"""
    frontend_path = Path("handymath-frontend")
    
    print(f"\n🧹 NETTOYAGE DU FRONTEND")
    print("=" * 30)
    
    # Vérifier s'il y a des fichiers temporaires dans le frontend
    temp_patterns = [
        "*.tmp",
        "*.log", 
        ".DS_Store",
        "Thumbs.db"
    ]
    
    found_temp_files = []
    for pattern in temp_patterns:
        found_temp_files.extend(frontend_path.glob(f"**/{pattern}"))
    
    if found_temp_files:
        for temp_file in found_temp_files:
            try:
                temp_file.unlink()
                print(f"✅ Supprimé: {temp_file.relative_to(frontend_path)}")
            except Exception as e:
                print(f"❌ Erreur: {e}")
    else:
        print("✅ Aucun fichier temporaire trouvé")

def cleanup_root():
    """Nettoyer la racine du projet"""
    root_path = Path(".")
    
    print(f"\n🧹 NETTOYAGE DE LA RACINE")
    print("=" * 30)
    
    # Fichiers à supprimer à la racine
    root_files_to_remove = [
        "cleanup_script.py",  # Ce script lui-même après exécution
    ]
    
    # Ne pas supprimer ce script maintenant, juste l'indiquer
    print("📝 Script de nettoyage créé (à supprimer manuellement après utilisation)")

def analyze_code_quality():
    """Analyser la qualité du code"""
    print(f"\n📊 ANALYSE DE LA QUALITÉ DU CODE")
    print("=" * 40)
    
    backend_path = Path("handymath-backend")
    frontend_path = Path("handymath-frontend/src")
    
    # Compter les fichiers Python
    py_files = list(backend_path.glob("**/*.py"))
    py_files = [f for f in py_files if "__pycache__" not in str(f) and "migrations" not in str(f)]
    
    # Compter les fichiers TypeScript/JavaScript
    ts_files = list(frontend_path.glob("**/*.tsx")) + list(frontend_path.glob("**/*.ts"))
    
    print(f"📁 Backend:")
    print(f"   🐍 Fichiers Python: {len(py_files)}")
    print(f"   📦 Modèles principaux: api/models.py, api/views.py, api/serializers.py")
    
    print(f"\n📁 Frontend:")
    print(f"   ⚛️ Fichiers TypeScript/React: {len(ts_files)}")
    print(f"   🎨 Pages principales: pages/, components/")
    
    # Vérifier les imports inutilisés dans les fichiers principaux
    main_files = [
        "handymath-backend/api/views.py",
        "handymath-backend/api/models.py", 
        "handymath-backend/api/serializers.py"
    ]
    
    print(f"\n🔍 VÉRIFICATION DES IMPORTS")
    print("-" * 30)
    
    for file_path in main_files:
        if Path(file_path).exists():
            print(f"📄 {Path(file_path).name}: À vérifier manuellement")

def main():
    """Fonction principale"""
    print("🧹 SCRIPT DE NETTOYAGE HANDYMATH")
    print("=" * 50)
    
    try:
        # 1. Nettoyer le backend
        cleanup_backend()
        
        # 2. Nettoyer le frontend  
        cleanup_frontend()
        
        # 3. Nettoyer la racine
        cleanup_root()
        
        # 4. Analyser la qualité du code
        analyze_code_quality()
        
        print(f"\n✅ NETTOYAGE TERMINÉ")
        print("=" * 20)
        print("📋 Actions recommandées:")
        print("   1. Vérifier que l'application fonctionne toujours")
        print("   2. Faire un commit des changements")
        print("   3. Supprimer ce script de nettoyage")
        print("   4. Vérifier les imports inutilisés dans les fichiers principaux")
        
    except Exception as e:
        print(f"❌ Erreur lors du nettoyage: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
