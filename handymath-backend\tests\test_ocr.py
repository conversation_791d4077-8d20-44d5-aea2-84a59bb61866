"""
Tests pour la reconnaissance OCR d'équations manuscrites
"""
import pytest
from unittest.mock import patch, MagicMock, mock_open
from PIL import Image
import io
import base64

from api.utils.ocr import extract_text_from_image, preprocess_image, validate_image


@pytest.mark.unit
class TestOCRUtils:
    """Tests unitaires pour les utilitaires OCR"""
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_simple_equation(self, mock_tesseract):
        """Test d'extraction de texte simple"""
        mock_tesseract.return_value = "2x + 3 = 11"
        
        # Créer une image de test simple
        img = Image.new('RGB', (100, 50), color='white')
        
        result = extract_text_from_image(img)
        
        assert result == "2x + 3 = 11"
        mock_tesseract.assert_called_once()
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_complex_equation(self, mock_tesseract):
        """Test d'extraction d'équation complexe"""
        mock_tesseract.return_value = "x^2 + 5x + 6 = 0"
        
        img = Image.new('RGB', (200, 100), color='white')
        result = extract_text_from_image(img)
        
        assert result == "x^2 + 5x + 6 = 0"
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_with_noise(self, mock_tesseract):
        """Test d'extraction avec bruit dans l'image"""
        mock_tesseract.return_value = "2x + 3 = 11 noise text"
        
        img = Image.new('RGB', (150, 75), color='white')
        result = extract_text_from_image(img, clean_text=True)
        
        # Vérifier que le nettoyage a été appliqué
        assert "2x + 3 = 11" in result
    
    def test_preprocess_image_resize(self):
        """Test de redimensionnement d'image"""
        img = Image.new('RGB', (50, 25), color='white')
        processed = preprocess_image(img, target_size=(200, 100))
        
        assert processed.size == (200, 100)
    
    def test_preprocess_image_grayscale(self):
        """Test de conversion en niveaux de gris"""
        img = Image.new('RGB', (100, 50), color='red')
        processed = preprocess_image(img, grayscale=True)
        
        assert processed.mode == 'L'  # Mode grayscale
    
    def test_preprocess_image_contrast(self):
        """Test d'amélioration du contraste"""
        img = Image.new('RGB', (100, 50), color='gray')
        processed = preprocess_image(img, enhance_contrast=True)
        
        assert processed is not None
        assert processed.size == img.size
    
    def test_validate_image_valid_formats(self):
        """Test de validation des formats d'image valides"""
        valid_formats = ['JPEG', 'PNG', 'BMP', 'TIFF']
        
        for fmt in valid_formats:
            img = Image.new('RGB', (100, 50), color='white')
            img.format = fmt
            assert validate_image(img) is True
    
    def test_validate_image_invalid_format(self):
        """Test de validation avec format invalide"""
        img = Image.new('RGB', (100, 50), color='white')
        img.format = 'GIF'  # Format non supporté
        assert validate_image(img) is False
    
    def test_validate_image_size_limits(self):
        """Test de validation des limites de taille"""
        # Image trop petite
        small_img = Image.new('RGB', (10, 5), color='white')
        assert validate_image(small_img, min_size=(50, 25)) is False
        
        # Image trop grande
        large_img = Image.new('RGB', (5000, 5000), color='white')
        assert validate_image(large_img, max_size=(1000, 1000)) is False
        
        # Image de taille correcte
        good_img = Image.new('RGB', (200, 100), color='white')
        assert validate_image(good_img, min_size=(50, 25), max_size=(1000, 1000)) is True


@pytest.mark.integration
class TestOCRIntegration:
    """Tests d'intégration pour l'OCR"""
    
    def create_test_image_base64(self, text="2x+3=11", size=(200, 100)):
        """Créer une image de test encodée en base64"""
        img = Image.new('RGB', size, color='white')
        # Ici, on simulerait l'ajout de texte sur l'image
        # Pour les tests, on retourne juste l'image vide
        
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_data = buffer.getvalue()
        return base64.b64encode(img_data).decode('utf-8')
    
    @patch('api.utils.ocr.extract_text_from_image')
    def test_ocr_pipeline_success(self, mock_extract):
        """Test du pipeline OCR complet"""
        mock_extract.return_value = "x + 5 = 10"
        
        image_data = self.create_test_image_base64()
        
        # Simuler le processus complet
        from api.utils.ocr import process_image_ocr
        result = process_image_ocr(image_data)
        
        assert 'text' in result
        assert 'confidence' in result
        assert result['text'] == "x + 5 = 10"
    
    @patch('api.utils.ocr.extract_text_from_image')
    def test_ocr_with_preprocessing(self, mock_extract):
        """Test OCR avec préprocessing"""
        mock_extract.return_value = "2x - 7 = 3"
        
        image_data = self.create_test_image_base64()
        
        from api.utils.ocr import process_image_ocr
        result = process_image_ocr(
            image_data, 
            preprocess=True,
            enhance_contrast=True,
            grayscale=True
        )
        
        assert result['text'] == "2x - 7 = 3"
    
    def test_ocr_error_handling_invalid_image(self):
        """Test de gestion d'erreur avec image invalide"""
        invalid_image_data = "invalid_base64_data"
        
        from api.utils.ocr import process_image_ocr
        
        with pytest.raises(ValueError):
            process_image_ocr(invalid_image_data)
    
    @patch('pytesseract.image_to_string')
    def test_ocr_error_handling_tesseract_failure(self, mock_tesseract):
        """Test de gestion d'erreur quand Tesseract échoue"""
        mock_tesseract.side_effect = Exception("Tesseract error")
        
        image_data = self.create_test_image_base64()
        
        from api.utils.ocr import process_image_ocr
        
        with pytest.raises(Exception):
            process_image_ocr(image_data)


@pytest.mark.django_db
class TestOCRAPI:
    """Tests pour l'API OCR"""
    
    def test_ocr_endpoint_with_valid_image(self, authenticated_client):
        """Test de l'endpoint OCR avec image valide"""
        # Créer une image de test
        img = Image.new('RGB', (200, 100), color='white')
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        data = {
            'image': f'data:image/png;base64,{img_data}'
        }
        
        with patch('api.utils.ocr.extract_text_from_image') as mock_extract:
            mock_extract.return_value = "x + 1 = 5"
            
            from django.urls import reverse
            url = reverse('ocr-recognize')
            response = authenticated_client.post(url, data, format='json')
            
            assert response.status_code == 200
            assert 'text' in response.data
            assert response.data['text'] == "x + 1 = 5"
    
    def test_ocr_endpoint_with_invalid_image(self, authenticated_client):
        """Test de l'endpoint OCR avec image invalide"""
        data = {
            'image': 'invalid_image_data'
        }
        
        from django.urls import reverse
        url = reverse('ocr-recognize')
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == 400
        assert 'error' in response.data
    
    def test_ocr_solve_endpoint(self, authenticated_client):
        """Test de l'endpoint OCR + résolution"""
        img = Image.new('RGB', (200, 100), color='white')
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        data = {
            'image': f'data:image/png;base64,{img_data}',
            'solve': True
        }
        
        with patch('api.utils.ocr.extract_text_from_image') as mock_extract:
            mock_extract.return_value = "x + 2 = 7"
            
            from django.urls import reverse
            url = reverse('ocr-solve')
            response = authenticated_client.post(url, data, format='json')
            
            assert response.status_code == 200
            assert 'text' in response.data
            assert 'solution' in response.data
            assert 'steps' in response.data


@pytest.mark.slow
class TestOCRPerformance:
    """Tests de performance pour l'OCR"""
    
    @patch('pytesseract.image_to_string')
    def test_ocr_processing_time(self, mock_tesseract):
        """Test du temps de traitement OCR"""
        import time
        
        mock_tesseract.return_value = "x^2 + 3x - 4 = 0"
        
        # Simuler une image de taille réaliste
        img = Image.new('RGB', (800, 600), color='white')
        
        start_time = time.time()
        result = extract_text_from_image(img)
        end_time = time.time()
        
        # Le traitement ne doit pas prendre plus de 5 secondes
        assert (end_time - start_time) < 5
        assert result == "x^2 + 3x - 4 = 0"
    
    def test_multiple_images_processing(self):
        """Test de traitement de plusieurs images"""
        images = []
        for i in range(5):
            img = Image.new('RGB', (200, 100), color='white')
            images.append(img)
        
        with patch('pytesseract.image_to_string') as mock_tesseract:
            mock_tesseract.return_value = f"x + {i} = {i+5}"
            
            start_time = time.time()
            results = []
            for img in images:
                result = extract_text_from_image(img)
                results.append(result)
            end_time = time.time()
            
            # Le traitement de 5 images ne doit pas prendre plus de 10 secondes
            assert (end_time - start_time) < 10
            assert len(results) == 5


@pytest.mark.unit
class TestOCRTextCleaning:
    """Tests pour le nettoyage du texte OCR"""
    
    def test_clean_ocr_text_basic(self):
        """Test de nettoyage de texte OCR basique"""
        from api.utils.ocr import clean_ocr_text
        
        dirty_text = "2x + 3 = 11 extra text"
        clean_text = clean_ocr_text(dirty_text)
        
        assert "2x + 3 = 11" in clean_text
        assert "extra text" not in clean_text
    
    def test_clean_ocr_text_special_characters(self):
        """Test de nettoyage des caractères spéciaux"""
        from api.utils.ocr import clean_ocr_text
        
        dirty_text = "2x + 3 = 11 @#$%"
        clean_text = clean_ocr_text(dirty_text)
        
        assert clean_text == "2x + 3 = 11"
    
    def test_clean_ocr_text_multiple_equations(self):
        """Test de nettoyage avec plusieurs équations"""
        from api.utils.ocr import clean_ocr_text
        
        dirty_text = "2x + 3 = 11\nx - 5 = 2\nrandom text"
        clean_text = clean_ocr_text(dirty_text, extract_first_equation=True)
        
        assert clean_text == "2x + 3 = 11"
    
    def test_normalize_equation_format(self):
        """Test de normalisation du format d'équation"""
        from api.utils.ocr import normalize_equation_format
        
        test_cases = [
            ("2 x + 3 = 11", "2x + 3 = 11"),
            ("x ^ 2 - 4 = 0", "x^2 - 4 = 0"),
            ("x  +  5  =  10", "x + 5 = 10")
        ]
        
        for input_text, expected in test_cases:
            result = normalize_equation_format(input_text)
            assert result == expected
