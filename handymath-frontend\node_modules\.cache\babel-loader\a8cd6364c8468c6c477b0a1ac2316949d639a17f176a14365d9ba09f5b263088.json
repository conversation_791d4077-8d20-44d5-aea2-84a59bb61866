{"ast": null, "code": "import { hasOwnProperty } from './object.js';\n\n/**\n * Get a property of a plain object\n * Throws an error in case the object is not a plain object or the\n * property is not defined on the object itself\n * @param {Object} object\n * @param {string} prop\n * @return {*} Returns the property value when safe\n */\nfunction getSafeProperty(object, prop) {\n  // only allow getting safe properties of a plain object\n  if (isSafeProperty(object, prop)) {\n    return object[prop];\n  }\n  if (typeof object[prop] === 'function' && isSafeMethod(object, prop)) {\n    throw new Error('Cannot access method \"' + prop + '\" as a property');\n  }\n  throw new Error('No access to property \"' + prop + '\"');\n}\n\n/**\n * Set a property on a plain object.\n * Throws an error in case the object is not a plain object or the\n * property would override an inherited property like .constructor or .toString\n * @param {Object} object\n * @param {string} prop\n * @param {*} value\n * @return {*} Returns the value\n */\n// TODO: merge this function into access.js?\nfunction setSafeProperty(object, prop, value) {\n  // only allow setting safe properties of a plain object\n  if (isSafeProperty(object, prop)) {\n    object[prop] = value;\n    return value;\n  }\n  throw new Error('No access to property \"' + prop + '\"');\n}\n\n/**\n * Test whether a property is safe to use on an object or Array.\n * For example .toString and .constructor are not safe\n * @param {Object | Array} object\n * @param {string} prop\n * @return {boolean} Returns true when safe\n */\nfunction isSafeProperty(object, prop) {\n  if (!isPlainObject(object) && !Array.isArray(object)) {\n    return false;\n  }\n  // SAFE: whitelisted\n  // e.g length\n  if (hasOwnProperty(safeNativeProperties, prop)) {\n    return true;\n  }\n  // UNSAFE: inherited from Object prototype\n  // e.g constructor\n  if (prop in Object.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Object.prototype is a root object\n    return false;\n  }\n  // UNSAFE: inherited from Function prototype\n  // e.g call, apply\n  if (prop in Function.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Function.prototype is a root object\n    return false;\n  }\n  return true;\n}\n\n/**\n * Validate whether a method is safe.\n * Throws an error when that's not the case.\n * @param {Object} object\n * @param {string} method\n * @return {function} Returns the method when valid\n */\nfunction getSafeMethod(object, method) {\n  if (!isSafeMethod(object, method)) {\n    throw new Error('No access to method \"' + method + '\"');\n  }\n  return object[method];\n}\n\n/**\n * Check whether a method is safe.\n * Throws an error when that's not the case (for example for `constructor`).\n * @param {Object} object\n * @param {string} method\n * @return {boolean} Returns true when safe, false otherwise\n */\nfunction isSafeMethod(object, method) {\n  if (object === null || object === undefined || typeof object[method] !== 'function') {\n    return false;\n  }\n  // UNSAFE: ghosted\n  // e.g overridden toString\n  // Note that IE10 doesn't support __proto__ and we can't do this check there.\n  if (hasOwnProperty(object, method) && Object.getPrototypeOf && method in Object.getPrototypeOf(object)) {\n    return false;\n  }\n  // SAFE: whitelisted\n  // e.g toString\n  if (hasOwnProperty(safeNativeMethods, method)) {\n    return true;\n  }\n  // UNSAFE: inherited from Object prototype\n  // e.g constructor\n  if (method in Object.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Object.prototype is a root object\n    return false;\n  }\n  // UNSAFE: inherited from Function prototype\n  // e.g call, apply\n  if (method in Function.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Function.prototype is a root object\n    return false;\n  }\n  return true;\n}\nfunction isPlainObject(object) {\n  return typeof object === 'object' && object && object.constructor === Object;\n}\nvar safeNativeProperties = {\n  length: true,\n  name: true\n};\nvar safeNativeMethods = {\n  toString: true,\n  valueOf: true,\n  toLocaleString: true\n};\nexport { getSafeProperty };\nexport { setSafeProperty };\nexport { isSafeProperty };\nexport { getSafeMethod };\nexport { isSafeMethod };\nexport { isPlainObject };", "map": {"version": 3, "names": ["hasOwnProperty", "getSafeProperty", "object", "prop", "isSafeProperty", "isSafeMethod", "Error", "setSafeProperty", "value", "isPlainObject", "Array", "isArray", "safeNativeProperties", "Object", "prototype", "Function", "getSafeMethod", "method", "undefined", "getPrototypeOf", "safeNativeMethods", "constructor", "length", "name", "toString", "valueOf", "toLocaleString"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/customs.js"], "sourcesContent": ["import { hasOwnProperty } from './object.js';\n\n/**\n * Get a property of a plain object\n * Throws an error in case the object is not a plain object or the\n * property is not defined on the object itself\n * @param {Object} object\n * @param {string} prop\n * @return {*} Returns the property value when safe\n */\nfunction getSafeProperty(object, prop) {\n  // only allow getting safe properties of a plain object\n  if (isSafeProperty(object, prop)) {\n    return object[prop];\n  }\n  if (typeof object[prop] === 'function' && isSafeMethod(object, prop)) {\n    throw new Error('Cannot access method \"' + prop + '\" as a property');\n  }\n  throw new Error('No access to property \"' + prop + '\"');\n}\n\n/**\n * Set a property on a plain object.\n * Throws an error in case the object is not a plain object or the\n * property would override an inherited property like .constructor or .toString\n * @param {Object} object\n * @param {string} prop\n * @param {*} value\n * @return {*} Returns the value\n */\n// TODO: merge this function into access.js?\nfunction setSafeProperty(object, prop, value) {\n  // only allow setting safe properties of a plain object\n  if (isSafeProperty(object, prop)) {\n    object[prop] = value;\n    return value;\n  }\n  throw new Error('No access to property \"' + prop + '\"');\n}\n\n/**\n * Test whether a property is safe to use on an object or Array.\n * For example .toString and .constructor are not safe\n * @param {Object | Array} object\n * @param {string} prop\n * @return {boolean} Returns true when safe\n */\nfunction isSafeProperty(object, prop) {\n  if (!isPlainObject(object) && !Array.isArray(object)) {\n    return false;\n  }\n  // SAFE: whitelisted\n  // e.g length\n  if (hasOwnProperty(safeNativeProperties, prop)) {\n    return true;\n  }\n  // UNSAFE: inherited from Object prototype\n  // e.g constructor\n  if (prop in Object.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Object.prototype is a root object\n    return false;\n  }\n  // UNSAFE: inherited from Function prototype\n  // e.g call, apply\n  if (prop in Function.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Function.prototype is a root object\n    return false;\n  }\n  return true;\n}\n\n/**\n * Validate whether a method is safe.\n * Throws an error when that's not the case.\n * @param {Object} object\n * @param {string} method\n * @return {function} Returns the method when valid\n */\nfunction getSafeMethod(object, method) {\n  if (!isSafeMethod(object, method)) {\n    throw new Error('No access to method \"' + method + '\"');\n  }\n  return object[method];\n}\n\n/**\n * Check whether a method is safe.\n * Throws an error when that's not the case (for example for `constructor`).\n * @param {Object} object\n * @param {string} method\n * @return {boolean} Returns true when safe, false otherwise\n */\nfunction isSafeMethod(object, method) {\n  if (object === null || object === undefined || typeof object[method] !== 'function') {\n    return false;\n  }\n  // UNSAFE: ghosted\n  // e.g overridden toString\n  // Note that IE10 doesn't support __proto__ and we can't do this check there.\n  if (hasOwnProperty(object, method) && Object.getPrototypeOf && method in Object.getPrototypeOf(object)) {\n    return false;\n  }\n  // SAFE: whitelisted\n  // e.g toString\n  if (hasOwnProperty(safeNativeMethods, method)) {\n    return true;\n  }\n  // UNSAFE: inherited from Object prototype\n  // e.g constructor\n  if (method in Object.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Object.prototype is a root object\n    return false;\n  }\n  // UNSAFE: inherited from Function prototype\n  // e.g call, apply\n  if (method in Function.prototype) {\n    // 'in' is used instead of hasOwnProperty for nodejs v0.10\n    // which is inconsistent on root prototypes. It is safe\n    // here because Function.prototype is a root object\n    return false;\n  }\n  return true;\n}\nfunction isPlainObject(object) {\n  return typeof object === 'object' && object && object.constructor === Object;\n}\nvar safeNativeProperties = {\n  length: true,\n  name: true\n};\nvar safeNativeMethods = {\n  toString: true,\n  valueOf: true,\n  toLocaleString: true\n};\nexport { getSafeProperty };\nexport { setSafeProperty };\nexport { isSafeProperty };\nexport { getSafeMethod };\nexport { isSafeMethod };\nexport { isPlainObject };"], "mappings": "AAAA,SAASA,cAAc,QAAQ,aAAa;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACrC;EACA,IAAIC,cAAc,CAACF,MAAM,EAAEC,IAAI,CAAC,EAAE;IAChC,OAAOD,MAAM,CAACC,IAAI,CAAC;EACrB;EACA,IAAI,OAAOD,MAAM,CAACC,IAAI,CAAC,KAAK,UAAU,IAAIE,YAAY,CAACH,MAAM,EAAEC,IAAI,CAAC,EAAE;IACpE,MAAM,IAAIG,KAAK,CAAC,wBAAwB,GAAGH,IAAI,GAAG,iBAAiB,CAAC;EACtE;EACA,MAAM,IAAIG,KAAK,CAAC,yBAAyB,GAAGH,IAAI,GAAG,GAAG,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAACL,MAAM,EAAEC,IAAI,EAAEK,KAAK,EAAE;EAC5C;EACA,IAAIJ,cAAc,CAACF,MAAM,EAAEC,IAAI,CAAC,EAAE;IAChCD,MAAM,CAACC,IAAI,CAAC,GAAGK,KAAK;IACpB,OAAOA,KAAK;EACd;EACA,MAAM,IAAIF,KAAK,CAAC,yBAAyB,GAAGH,IAAI,GAAG,GAAG,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACF,MAAM,EAAEC,IAAI,EAAE;EACpC,IAAI,CAACM,aAAa,CAACP,MAAM,CAAC,IAAI,CAACQ,KAAK,CAACC,OAAO,CAACT,MAAM,CAAC,EAAE;IACpD,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIF,cAAc,CAACY,oBAAoB,EAAET,IAAI,CAAC,EAAE;IAC9C,OAAO,IAAI;EACb;EACA;EACA;EACA,IAAIA,IAAI,IAAIU,MAAM,CAACC,SAAS,EAAE;IAC5B;IACA;IACA;IACA,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIX,IAAI,IAAIY,QAAQ,CAACD,SAAS,EAAE;IAC9B;IACA;IACA;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACd,MAAM,EAAEe,MAAM,EAAE;EACrC,IAAI,CAACZ,YAAY,CAACH,MAAM,EAAEe,MAAM,CAAC,EAAE;IACjC,MAAM,IAAIX,KAAK,CAAC,uBAAuB,GAAGW,MAAM,GAAG,GAAG,CAAC;EACzD;EACA,OAAOf,MAAM,CAACe,MAAM,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASZ,YAAYA,CAACH,MAAM,EAAEe,MAAM,EAAE;EACpC,IAAIf,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKgB,SAAS,IAAI,OAAOhB,MAAM,CAACe,MAAM,CAAC,KAAK,UAAU,EAAE;IACnF,OAAO,KAAK;EACd;EACA;EACA;EACA;EACA,IAAIjB,cAAc,CAACE,MAAM,EAAEe,MAAM,CAAC,IAAIJ,MAAM,CAACM,cAAc,IAAIF,MAAM,IAAIJ,MAAM,CAACM,cAAc,CAACjB,MAAM,CAAC,EAAE;IACtG,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIF,cAAc,CAACoB,iBAAiB,EAAEH,MAAM,CAAC,EAAE;IAC7C,OAAO,IAAI;EACb;EACA;EACA;EACA,IAAIA,MAAM,IAAIJ,MAAM,CAACC,SAAS,EAAE;IAC9B;IACA;IACA;IACA,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIG,MAAM,IAAIF,QAAQ,CAACD,SAAS,EAAE;IAChC;IACA;IACA;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,SAASL,aAAaA,CAACP,MAAM,EAAE;EAC7B,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACmB,WAAW,KAAKR,MAAM;AAC9E;AACA,IAAID,oBAAoB,GAAG;EACzBU,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE;AACR,CAAC;AACD,IAAIH,iBAAiB,GAAG;EACtBI,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE;AAClB,CAAC;AACD,SAASzB,eAAe;AACxB,SAASM,eAAe;AACxB,SAASH,cAAc;AACvB,SAASY,aAAa;AACtB,SAASX,YAAY;AACrB,SAASI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}