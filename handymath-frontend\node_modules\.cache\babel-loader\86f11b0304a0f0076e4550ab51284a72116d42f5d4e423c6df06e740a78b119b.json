{"ast": null, "code": "export var factorialDocs = {\n  name: 'factorial',\n  category: 'Probability',\n  syntax: ['n!', 'factorial(n)'],\n  description: 'Compute the factorial of a value',\n  examples: ['5!', '5 * 4 * 3 * 2 * 1', '3!'],\n  seealso: ['combinations', 'combinationsWithRep', 'permutations', 'gamma']\n};", "map": {"version": 3, "names": ["factorialDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/factorial.js"], "sourcesContent": ["export var factorialDocs = {\n  name: 'factorial',\n  category: 'Probability',\n  syntax: ['n!', 'factorial(n)'],\n  description: 'Compute the factorial of a value',\n  examples: ['5!', '5 * 4 * 3 * 2 * 1', '3!'],\n  seealso: ['combinations', 'combinationsWithRep', 'permutations', 'gamma']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC;EAC9BC,WAAW,EAAE,kCAAkC;EAC/CC,QAAQ,EAAE,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAAC;EAC3CC,OAAO,EAAE,CAAC,cAAc,EAAE,qBAAqB,EAAE,cAAc,EAAE,OAAO;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}