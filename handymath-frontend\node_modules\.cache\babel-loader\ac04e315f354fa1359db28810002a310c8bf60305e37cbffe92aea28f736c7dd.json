{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createQuantumOfCirculation } from '../../factoriesAny.js';\nexport var quantumOfCirculationDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createQuantumOfCirculation\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createQuantumOfCirculation", "quantumOfCirculationDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesQuantumOfCirculation.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createQuantumOfCirculation } from '../../factoriesAny.js';\nexport var quantumOfCirculationDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createQuantumOfCirculation\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,0BAA0B,QAAQ,uBAAuB;AAClE,OAAO,IAAIC,gCAAgC,GAAG;EAC5CH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}