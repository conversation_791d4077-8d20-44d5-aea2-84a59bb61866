"""
Tests simples pour vérifier que pytest fonctionne
"""
import pytest
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.test import TestCase
from django.contrib.auth.models import User


@pytest.mark.django_db
class TestBasicFunctionality:
    """Tests de base pour vérifier le fonctionnement"""
    
    def test_django_setup(self):
        """Test que Django est correctement configuré"""
        from django.conf import settings
        assert settings.INSTALLED_APPS is not None
        assert 'django.contrib.auth' in settings.INSTALLED_APPS
    
    def test_user_creation(self):
        """Test de création d'utilisateur"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        assert user.id is not None
        assert user.username == 'testuser'
        assert user.email == '<EMAIL>'
        assert user.check_password('testpassword123')
    
    def test_admin_user_creation(self):
        """Test de création d'administrateur"""
        admin = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpassword123'
        )
        
        assert admin.is_staff is True
        assert admin.is_superuser is True
        assert admin.username == 'admin'
    
    def test_user_authentication(self):
        """Test d'authentification utilisateur"""
        user = User.objects.create_user(
            username='authuser',
            password='authpassword123'
        )
        
        # Test avec bon mot de passe
        assert user.check_password('authpassword123') is True
        
        # Test avec mauvais mot de passe
        assert user.check_password('wrongpassword') is False


@pytest.mark.unit
class TestMathFunctions:
    """Tests unitaires pour les fonctions mathématiques"""
    
    def test_basic_math(self):
        """Test de calculs mathématiques de base"""
        assert 2 + 2 == 4
        assert 5 * 3 == 15
        assert 10 / 2 == 5
    
    def test_sympy_import(self):
        """Test que SymPy peut être importé"""
        try:
            import sympy as sp
            x = sp.Symbol('x')
            equation = sp.Eq(x + 1, 5)
            solution = sp.solve(equation, x)
            assert solution == [4]
        except ImportError:
            pytest.skip("SymPy non installé")
    
    def test_equation_parsing(self):
        """Test de parsing d'équation simple"""
        equation_text = "2x + 3 = 11"
        
        # Test que l'équation contient les éléments attendus
        assert 'x' in equation_text
        assert '=' in equation_text
        assert '2' in equation_text
        assert '3' in equation_text
        assert '11' in equation_text


@pytest.mark.unit
class TestUtilityFunctions:
    """Tests pour les fonctions utilitaires"""
    
    def test_string_operations(self):
        """Test d'opérations sur les chaînes"""
        test_string = "2x + 3 = 11"
        
        # Test de nettoyage basique
        cleaned = test_string.strip()
        assert cleaned == "2x + 3 = 11"
        
        # Test de division
        parts = test_string.split('=')
        assert len(parts) == 2
        assert parts[0].strip() == "2x + 3"
        assert parts[1].strip() == "11"
    
    def test_validation_functions(self):
        """Test de fonctions de validation"""
        
        def is_valid_equation(equation):
            """Fonction simple de validation d'équation"""
            return '=' in equation and len(equation.strip()) > 0
        
        # Tests de validation
        assert is_valid_equation("x + 1 = 5") is True
        assert is_valid_equation("2x + 3 = 11") is True
        assert is_valid_equation("") is False
        assert is_valid_equation("x + 1") is False
    
    def test_list_operations(self):
        """Test d'opérations sur les listes"""
        equations = [
            "x + 1 = 5",
            "2x + 3 = 11", 
            "x^2 - 4 = 0"
        ]
        
        assert len(equations) == 3
        assert "x + 1 = 5" in equations
        
        # Test de filtrage
        linear_equations = [eq for eq in equations if '^' not in eq]
        assert len(linear_equations) == 2


@pytest.mark.integration
class TestDatabaseOperations:
    """Tests d'intégration avec la base de données"""
    
    @pytest.mark.django_db
    def test_user_crud_operations(self):
        """Test des opérations CRUD sur les utilisateurs"""
        
        # CREATE
        user = User.objects.create_user(
            username='cruduser',
            email='<EMAIL>',
            password='crudpassword123'
        )
        assert user.id is not None
        
        # READ
        retrieved_user = User.objects.get(username='cruduser')
        assert retrieved_user.email == '<EMAIL>'
        
        # UPDATE
        retrieved_user.first_name = 'CRUD'
        retrieved_user.save()
        
        updated_user = User.objects.get(username='cruduser')
        assert updated_user.first_name == 'CRUD'
        
        # DELETE
        user_id = updated_user.id
        updated_user.delete()
        
        with pytest.raises(User.DoesNotExist):
            User.objects.get(id=user_id)
    
    @pytest.mark.django_db
    def test_multiple_users(self):
        """Test avec plusieurs utilisateurs"""
        users = []
        for i in range(3):
            user = User.objects.create_user(
                username=f'user{i}',
                email=f'user{i}@example.com',
                password='password123'
            )
            users.append(user)
        
        # Vérifier que tous les utilisateurs sont créés
        assert len(users) == 3
        assert User.objects.filter(username__startswith='user').count() >= 3
        
        # Vérifier l'unicité des noms d'utilisateur
        usernames = [user.username for user in users]
        assert len(set(usernames)) == 3


class TestEnvironment:
    """Tests pour vérifier l'environnement"""
    
    def test_python_version(self):
        """Test de la version Python"""
        import sys
        assert sys.version_info >= (3, 8)
    
    def test_django_version(self):
        """Test de la version Django"""
        import django
        version = django.get_version()
        assert version is not None
        print(f"Django version: {version}")
    
    def test_required_packages(self):
        """Test que les packages requis sont installés"""
        required_packages = [
            'django',
            'djangorestframework', 
            'sympy',
            'pytest'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                pytest.fail(f"Package requis manquant: {package}")
    
    def test_settings_configuration(self):
        """Test de la configuration des settings"""
        from django.conf import settings
        
        # Vérifier les settings essentiels
        assert hasattr(settings, 'DATABASES')
        assert hasattr(settings, 'INSTALLED_APPS')
        assert hasattr(settings, 'SECRET_KEY')
        
        # Vérifier que nos apps sont installées
        assert 'api' in settings.INSTALLED_APPS
        assert 'rest_framework' in settings.INSTALLED_APPS
