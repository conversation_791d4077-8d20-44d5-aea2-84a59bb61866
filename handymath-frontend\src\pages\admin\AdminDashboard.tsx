import React, { useState, useEffect } from 'react'; import { motion } from 'framer-motion'; import api from '../../services/api'; interface AdminStats { overview: { total_users: number; total_students: number; total_teachers: number; total_admins: number; total_equations: number; total_courses: number; total_exercises: number; active_users: number; recent_equations: number; recent_users: number; }; top_users: Array<{ id: number; username: string; first_name: string; last_name: string; role: string; equation_count: number; date_joined: string; }>; daily_stats: Array<{ date: string; equations: number; new_users: number; }>; course_levels: Array<{ level: string; count: number; }>; popular_equations: Array<{ equation_text: string; count: number; }>; growth_rate: { users: number; equations: number; }; } interface User { id: number; username: string; email: string; first_name: string; last_name: string; role: string; is_active: boolean; is_staff: boolean; date_joined: string; last_activity: string; equation_count: number; } interface SystemHealth { timestamp: string; system_info: { cpu_percent: number; memory_total: number; memory_used: number; memory_percent: number; disk_total: number; disk_used: number; disk_percent: number; }; health_checks: Array<{ name: string; status: string; value: string; message: string; }>; database: { queries_count: number; status: string; }; } const AdminDashboard: React.FC = () => { const [activeTab, setActiveTab] = useState('overview'); const [adminStats, setAdminStats] = useState<AdminStats | null>(null); const [users, setUsers] = useState<User[]>([]); const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null); const [loading, setLoading] = useState(false); const [error, setError] = useState(''); useEffect(() => { fetchAdminData(); }, []); const fetchAdminData = async () => { setLoading(true); try { const [statsResponse, usersResponse, healthResponse] = await Promise.all([ api.get('/admin/dashboard/'), api.get('/admin/users/'), api.get('/admin/system-health/') ]); setAdminStats(statsResponse.data); setUsers(usersResponse.data.users); setSystemHealth(healthResponse.data); } catch (err: any) { setError(err.response?.data?.error || 'Erreur lors du chargement des données'); } finally { setLoading(false); } }; const toggleUserStatus = async (userId: number) => { try { await api.post(`/admin/users/${userId}/toggle/`); const usersResponse = await api.get('/admin/users/'); setUsers(usersResponse.data.users); } catch (err: any) { setError(err.response?.data?.error || 'Erreur lors de la modification'); } }; const formatBytes = (bytes: number) => { if (bytes === 0) return '0 Bytes'; const k = 1024; const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]; }; const getStatusColor = (status: string) => { switch (status) { case 'healthy': return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400'; case 'warning': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400'; case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400'; default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400'; } }; const getRoleColor = (role: string) => { switch (role) { case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'; case 'teacher': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'; case 'student': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'; default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400'; } }; if (loading) { return ( <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center"> <div className="text-center"> <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div> <p className="mt-4 text-gray-600 dark:text-gray-400">Chargement du dashboard administrateur...</p> </div> </div> ); } return ( <div className="min-h-screen bg-gray-50 dark:bg-gray-900"> <header className="bg-white dark:bg-gray-800 shadow"> <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <div className="flex justify-between items-center py-6"> <div className="flex items-center"> <h1 className="text-3xl font-bold text-gray-900 dark:text-white"> Dashboard Administrateur </h1> </div> <div className="flex items-center space-x-4"> <span className="text-sm text-gray-500 dark:text-gray-400"> Dernière mise à jour: {new Date().toLocaleTimeString('fr-FR')} </span> <button onClick={fetchAdminData} className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200" > Actualiser </button> </div> </div> </div> </header> <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"> {error && ( <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="mb-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md p-4" > <div className="flex"> <div className="text-red-400"></div> <div className="ml-3"> <div className="text-sm text-red-600 dark:text-red-400">{error}</div> </div> </div> </motion.div> )} <div className="mb-6"> <nav className="flex space-x-8 border-b border-gray-200 dark:border-gray-700"> {[ { id: 'overview', label: 'Vue d\'ensemble' }, { id: 'users', label: 'Utilisateurs' }, { id: 'system', label: ' Système' }, { id: 'analytics', label: 'Analytics' } ].map((tab) => ( <button key={tab.id} className={`${ activeTab === tab.id ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200`} onClick={() => setActiveTab(tab.id)} > {tab.label} </button> ))} </nav> </div> <div className="space-y-6"> {activeTab === 'overview' && adminStats && ( <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6" > <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> {[ { title: 'Total Utilisateurs', value: adminStats.overview.total_users, change: `+${adminStats.overview.recent_users}`, changeLabel: 'cette semaine', icon: '', color: 'blue' }, { title: 'Équations Résolues', value: adminStats.overview.total_equations, change: `+${adminStats.overview.recent_equations}`, changeLabel: 'cette semaine', icon: '', color: 'green' }, { title: 'Cours Disponibles', value: adminStats.overview.total_courses, change: adminStats.overview.total_exercises, changeLabel: 'exercices', icon: '', color: 'purple' }, { title: 'Utilisateurs Actifs', value: adminStats.overview.active_users, change: `${Math.round((adminStats.overview.active_users / adminStats.overview.total_users) * 100)}%`, changeLabel: 'd\'engagement', icon: '', color: 'orange' } ].map((stat, index) => ( <motion.div key={stat.title} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.1 }} className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" > <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="text-2xl">{stat.icon}</div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate"> {stat.title} </dt> <dd className="text-lg font-medium text-gray-900 dark:text-white"> {stat.value.toLocaleString()} </dd> </dl> </div> </div> </div> <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3"> <div className="text-sm"> <span className={`font-medium ${ stat.color === 'blue' ? 'text-blue-600 dark:text-blue-400' : stat.color === 'green' ? 'text-green-600 dark:text-green-400' : stat.color === 'purple' ? 'text-purple-600 dark:text-purple-400' : 'text-orange-600 dark:text-orange-400' }`}> {typeof stat.change === 'number' ? stat.change.toLocaleString() : stat.change} </span> <span className="text-gray-500 dark:text-gray-400"> {stat.changeLabel}</span> </div> </div> </motion.div> ))} </div> {/* Top utilisateurs */} {adminStats.top_users?.length > 0 && ( <div className="bg-white dark:bg-gray-800 shadow rounded-lg"> <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700"> <h3 className="text-lg font-medium text-gray-900 dark:text-white">Top Utilisateurs</h3> </div> <div className="p-6"> <div className="space-y-4"> {adminStats.top_users.slice(0, 5).map((user, index) => ( <div key={user.id} className="flex items-center justify-between"> <div className="flex items-center"> <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3"> {index + 1} </div> <div> <p className="text-sm font-medium text-gray-900 dark:text-white"> {user.first_name} {user.last_name} </p> <p className="text-xs text-gray-500 dark:text-gray-400">@{user.username}</p> </div> </div> <div className="text-right"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}> {user.role} </span> <p className="text-sm text-gray-500 dark:text-gray-400 mt-1"> {user.equation_count} équations </p> </div> </div> ))} </div> </div> </div> )} {/* Équations populaires */} {adminStats.popular_equations?.length > 0 && ( <div className="bg-white dark:bg-gray-800 shadow rounded-lg"> <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700"> <h3 className="text-lg font-medium text-gray-900 dark:text-white"> Équations Populaires</h3> </div> <div className="p-6"> <div className="space-y-3"> {adminStats.popular_equations.slice(0, 5).map((eq, index) => ( <div key={index} className="flex items-center justify-between"> <div className="flex-1"> <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded"> {eq.equation_text} </code> </div> <div className="ml-4"> <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"> {eq.count} fois </span> </div> </div> ))} </div> </div> </div> )} </motion.div> )} {activeTab === 'users' && ( <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6" > <div className="bg-white dark:bg-gray-800 shadow rounded-lg"> <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700"> <div className="flex items-center justify-between"> <h3 className="text-lg font-medium text-gray-900 dark:text-white">Gestion des Utilisateurs</h3> <div className="text-sm text-gray-500 dark:text-gray-400"> {users.length} utilisateurs au total </div> </div> </div> <div className="p-6"> <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"> <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600"> <thead className="bg-gray-50 dark:bg-gray-700"> <tr> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"> Utilisateur </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"> Rôle </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"> Activité </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"> Statut </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"> Actions </th> </tr> </thead> <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600"> {users.slice(0, 10).map((user) => ( <tr key={user.id}> <td className="px-6 py-4 whitespace-nowrap"> <div className="flex items-center"> <div> <div className="text-sm font-medium text-gray-900 dark:text-white"> {user.first_name} {user.last_name} </div> <div className="text-sm text-gray-500 dark:text-gray-400"> {user.email} </div> </div> </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}> {user.role} </span> </td> <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"> {user.equation_count} équations </td> <td className="px-6 py-4 whitespace-nowrap"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ user.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' }`}> {user.is_active ? 'Actif' : 'Inactif'} </span> </td> <td className="px-6 py-4 whitespace-nowrap text-sm font-medium"> <button onClick={() => toggleUserStatus(user.id)} className={`${ user.is_active ? 'text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300' : 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300' } transition-colors duration-200`} > {user.is_active ? 'Désactiver' : 'Activer'} </button> </td> </tr> ))} </tbody> </table> </div> </div> </div> </motion.div> )} {activeTab === 'system' && systemHealth && ( <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6" > {/* Informations système */} <div className="grid grid-cols-1 md:grid-cols-3 gap-6"> <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="text-2xl"> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate"> CPU Usage </dt> <dd className="text-lg font-medium text-gray-900 dark:text-white"> {systemHealth.system_info.cpu_percent.toFixed(1)}% </dd> </dl> </div> </div> </div> <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3"> <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"> <div className={`h-2 rounded-full ${ systemHealth.system_info.cpu_percent < 70 ? 'bg-green-600' : systemHealth.system_info.cpu_percent < 90 ? 'bg-yellow-600' : 'bg-red-600' }`} style={{ width: `${systemHealth.system_info.cpu_percent}%` }} ></div> </div> </div> </div> <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="text-2xl"> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate"> Memory Usage </dt> <dd className="text-lg font-medium text-gray-900 dark:text-white"> {systemHealth.system_info.memory_percent.toFixed(1)}% </dd> </dl> </div> </div> </div> <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3"> <div className="text-sm text-gray-500 dark:text-gray-400"> {formatBytes(systemHealth.system_info.memory_used)} / {formatBytes(systemHealth.system_info.memory_total)} </div> </div> </div> <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="text-2xl"> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate"> Disk Usage </dt> <dd className="text-lg font-medium text-gray-900 dark:text-white"> {systemHealth.system_info.disk_percent.toFixed(1)}% </dd> </dl> </div> </div> </div> <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3"> <div className="text-sm text-gray-500 dark:text-gray-400"> {formatBytes(systemHealth.system_info.disk_used)} / {formatBytes(systemHealth.system_info.disk_total)} </div> </div> </div> </div> {/* Vérifications de santé */} <div className="bg-white dark:bg-gray-800 shadow rounded-lg"> <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700"> <h3 className="text-lg font-medium text-gray-900 dark:text-white"> Vérifications de Santé</h3> </div> <div className="p-6"> <div className="space-y-4"> {systemHealth.health_checks.map((check, index) => ( <div key={index} className="flex items-center justify-between"> <div className="flex items-center"> <div className={`w-3 h-3 rounded-full mr-3 ${ check.status === 'healthy' ? 'bg-green-400' : check.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400' }`}></div> <div> <p className="text-sm font-medium text-gray-900 dark:text-white">{check.name}</p> <p className="text-xs text-gray-500 dark:text-gray-400">{check.message}</p> </div> </div> <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(check.status)}`}> {check.value} </span> </div> ))} </div> </div> </div> </motion.div> )} {activeTab === 'analytics' && adminStats && ( <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6" > <div className="bg-white dark:bg-gray-800 shadow rounded-lg"> <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700"> <h3 className="text-lg font-medium text-gray-900 dark:text-white">Analytics Avancées</h3> </div> <div className="p-6"> <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> {/* Répartition par niveau de cours */} {adminStats.course_levels?.length > 0 && ( <div> <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Répartition des cours par niveau</h4> <div className="space-y-3"> {adminStats.course_levels.map((level, index) => ( <div key={index} className="flex items-center justify-between"> <span className="text-sm text-gray-600 dark:text-gray-300 capitalize">{level.level}</span> <div className="flex items-center"> <div className="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2"> <div className="bg-primary-600 h-2 rounded-full" style={{ width: `${(level.count / Math.max(...adminStats.course_levels.map(l => l.count))) * 100}%` }} ></div> </div> <span className="text-sm font-medium text-gray-900 dark:text-white">{level.count}</span> </div> </div> ))} </div> </div> )} {/* Taux de croissance */} <div> <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Taux de croissance (7 jours)</h4> <div className="space-y-3"> <div className="flex items-center justify-between"> <span className="text-sm text-gray-600 dark:text-gray-300">Nouveaux utilisateurs</span> <span className={`text-sm font-medium ${ adminStats.growth_rate.users > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400' }`}> +{adminStats.growth_rate.users.toFixed(1)}% </span> </div> <div className="flex items-center justify-between"> <span className="text-sm text-gray-600 dark:text-gray-300">Nouvelles équations</span> <span className={`text-sm font-medium ${ adminStats.growth_rate.equations > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400' }`}> +{adminStats.growth_rate.equations.toFixed(1)}% </span> </div> </div> </div> </div> {/* Activité quotidienne */} {adminStats.daily_stats?.length > 0 && ( <div className="mt-8"> <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Activité des 7 derniers jours</h4> <div className="grid grid-cols-7 gap-2"> {adminStats.daily_stats.reverse().map((day, index) => ( <div key={index} className="text-center"> <div className="text-xs text-gray-500 dark:text-gray-400 mb-1"> {new Date(day.date).toLocaleDateString('fr-FR', { weekday: 'short' })} </div> <div className="bg-gray-100 dark:bg-gray-700 rounded p-2"> <div className="text-sm font-medium text-gray-900 dark:text-white"> {day.equations} </div> <div className="text-xs text-gray-500 dark:text-gray-400"> équations </div> </div> </div> ))} </div> </div> )} </div> </div> </motion.div> )} </div> </main> </div> ); }; export default AdminDashboard;