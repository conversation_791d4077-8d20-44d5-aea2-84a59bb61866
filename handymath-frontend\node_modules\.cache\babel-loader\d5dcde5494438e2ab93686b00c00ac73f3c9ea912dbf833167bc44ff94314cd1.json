{"ast": null, "code": "export var bigintDocs = {\n  name: 'bigint',\n  category: 'Construction',\n  syntax: ['bigint(x)'],\n  description: 'Create a bigint, an integer with an arbitrary number of digits, from a number or string.',\n  examples: ['123123123123123123 # a large number will lose digits', 'bigint(\"123123123123123123\")', 'bignumber([\"1\", \"3\", \"5\"])'],\n  seealso: ['boolean', 'bignumber', 'number', 'complex', 'fraction', 'index', 'matrix', 'string', 'unit']\n};", "map": {"version": 3, "names": ["bigintDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/bigint.js"], "sourcesContent": ["export var bigintDocs = {\n  name: 'bigint',\n  category: 'Construction',\n  syntax: ['bigint(x)'],\n  description: 'Create a bigint, an integer with an arbitrary number of digits, from a number or string.',\n  examples: ['123123123123123123 # a large number will lose digits', 'bigint(\"123123123123123123\")', 'bignumber([\"1\", \"3\", \"5\"])'],\n  seealso: ['boolean', 'bignumber', 'number', 'complex', 'fraction', 'index', 'matrix', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,0FAA0F;EACvGC,QAAQ,EAAE,CAAC,sDAAsD,EAAE,8BAA8B,EAAE,4BAA4B,CAAC;EAChIC,OAAO,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AACxG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}