{"ast": null, "code": "export var kronDocs = {\n  name: 'kron',\n  category: 'Matrix',\n  syntax: ['kron(x, y)'],\n  description: 'Calculates the <PERSON>ronecker product of 2 matrices or vectors.',\n  examples: ['kron([[1, 0], [0, 1]], [[1, 2], [3, 4]])', 'kron([1,1], [2,3,4])'],\n  seealso: ['multiply', 'dot', 'cross']\n};", "map": {"version": 3, "names": ["kronDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/kron.js"], "sourcesContent": ["export var kronDocs = {\n  name: 'kron',\n  category: 'Matrix',\n  syntax: ['kron(x, y)'],\n  description: 'Calculates the <PERSON>ronecker product of 2 matrices or vectors.',\n  examples: ['kron([[1, 0], [0, 1]], [[1, 2], [3, 4]])', 'kron([1,1], [2,3,4])'],\n  seealso: ['multiply', 'dot', 'cross']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,CAAC,0CAA0C,EAAE,sBAAsB,CAAC;EAC9EC,OAAO,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}