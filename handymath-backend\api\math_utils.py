import sympy as sp
from sympy import symbols, solve, Eq, simplify, expand, factor, collect
from sympy.parsing.sympy_parser import parse_expr, standard_transformations, implicit_multiplication_application

# Configuration du parser SymPy pour une meilleure interprétation des équations
transformations = standard_transformations + (implicit_multiplication_application,)

def parse_equation(equation_str):
    """
    Parse une chaîne d'équation en objet SymPy Eq.
    Gère les cas où l'équation est déjà sous forme d'égalité ou non.
    """
    try:
        if '=' in equation_str:
            left_side, right_side = equation_str.split('=', 1)
            left_expr = parse_expr(left_side.strip(), transformations=transformations)
            right_expr = parse_expr(right_side.strip(), transformations=transformations)
            return Eq(left_expr, right_expr)
        else:
            # Si pas de signe égal, on suppose que l'expression est égale à 0
            expr = parse_expr(equation_str.strip(), transformations=transformations)
            return Eq(expr, 0)
    except Exception as e:
        raise ValueError(f"Erreur lors de l'analyse de l'équation: {str(e)}")

def solve_equation(equation_str):
    """
    Résout une équation et retourne la solution et les étapes.
    """
    try:
        # Identifier la variable (supposons que c'est x, y ou z)
        variables = ['x', 'y', 'z']
        var_symbols = {v: symbols(v) for v in variables}
        
        # Analyser l'équation
        eq = parse_equation(equation_str)
        
        # Déterminer quelle variable est utilisée
        used_vars = [v for v in variables if var_symbols[v] in eq.free_symbols]
        
        if not used_vars:
            return "Aucune variable trouvée dans l'équation", []
        
        # Utiliser la première variable trouvée
        var = var_symbols[used_vars[0]]
        
        # Résoudre l'équation
        solution = solve(eq, var)
        
        # Générer les étapes de résolution
        steps = generate_solution_steps(eq, var)
        
        return str(solution), steps
    except Exception as e:
        raise ValueError(f"Erreur lors de la résolution: {str(e)}")

def generate_solution_steps(eq, var):
    """
    Génère les étapes de résolution d'une équation.
    """
    steps = []
    
    # Étape 1: Réorganiser l'équation
    steps.append(f"Équation initiale: {eq}")
    
    # Étape 2: Déplacer tous les termes à gauche
    eq_rearranged = Eq(eq.lhs - eq.rhs, 0)
    steps.append(f"Réorganisation: {eq_rearranged}")
    
    # Étape 3: Simplifier l'expression
    expr = eq_rearranged.lhs
    expr_simplified = simplify(expr)
    steps.append(f"Simplification: {expr_simplified} = 0")
    
    # Étape 4: Développer si nécessaire
    expr_expanded = expand(expr_simplified)
    if expr_expanded != expr_simplified:
        steps.append(f"Développement: {expr_expanded} = 0")
    
    # Étape 5: Factoriser si possible
    expr_factored = factor(expr_expanded)
    if expr_factored != expr_expanded:
        steps.append(f"Factorisation: {expr_factored} = 0")
    
    # Étape 6: Collecter les termes par puissance de la variable
    expr_collected = collect(expr_factored, var)
    if expr_collected != expr_factored:
        steps.append(f"Regroupement des termes: {expr_collected} = 0")
    
    # Étape 7: Résolution
    solution = solve(eq, var)
    steps.append(f"Solution: {var} = {solution}")
    
    return steps

def analyze_function(function_str, var_name='x'):
    """
    Analyse une fonction mathématique et retourne des informations utiles.
    """
    try:
        var = symbols(var_name)
        expr = parse_expr(function_str, transformations=transformations)
        
        # Dérivée
        derivative = sp.diff(expr, var)
        
        # Points critiques (où la dérivée est nulle)
        critical_points = solve(derivative, var)
        
        # Deuxième dérivée pour l'analyse de concavité
        second_derivative = sp.diff(derivative, var)
        
        # Limites
        limit_neg_inf = sp.limit(expr, var, -sp.oo)
        limit_pos_inf = sp.limit(expr, var, sp.oo)
        
        return {
            'function': str(expr),
            'derivative': str(derivative),
            'second_derivative': str(second_derivative),
            'critical_points': str(critical_points),
            'limit_neg_inf': str(limit_neg_inf),
            'limit_pos_inf': str(limit_pos_inf)
        }
    except Exception as e:
        raise ValueError(f"Erreur lors de l'analyse de la fonction: {str(e)}")

def integrate_function(function_str, var_name='x', lower_bound=None, upper_bound=None):
    """
    Intègre une fonction mathématique.
    """
    try:
        var = symbols(var_name)
        expr = parse_expr(function_str, transformations=transformations)
        
        if lower_bound is not None and upper_bound is not None:
            # Intégrale définie
            lower = parse_expr(str(lower_bound), transformations=transformations)
            upper = parse_expr(str(upper_bound), transformations=transformations)
            result = sp.integrate(expr, (var, lower, upper))
            return str(result)
        else:
            # Intégrale indéfinie
            result = sp.integrate(expr, var)
            return str(result)
    except Exception as e:
        raise ValueError(f"Erreur lors de l'intégration: {str(e)}")

def solve_system_of_equations(equations_list):
    """
    Résout un système d'équations.
    """
    try:
        # Analyser chaque équation
        parsed_equations = [parse_equation(eq) for eq in equations_list]
        
        # Collecter toutes les variables
        all_symbols = set()
        for eq in parsed_equations:
            all_symbols.update(eq.free_symbols)
        
        # Résoudre le système
        solution = sp.solve(parsed_equations, list(all_symbols))
        
        return str(solution)
    except Exception as e:
        raise ValueError(f"Erreur lors de la résolution du système: {str(e)}")
