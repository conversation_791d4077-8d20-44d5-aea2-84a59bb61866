{"ast": null, "code": "export var compareNaturalDocs = {\n  name: 'compareNatural',\n  category: 'Relational',\n  syntax: ['compareNatural(x, y)'],\n  description: 'Compare two values of any type in a deterministic, natural way. ' + 'Returns 1 when x > y, -1 when x < y, and 0 when x == y.',\n  examples: ['compareNatural(2, 3)', 'compareNatural(3, 2)', 'compareNatural(2, 2)', 'compareNatural(5cm, 40mm)', 'compareNatural(\"2\", \"10\")', 'compareNatural(2 + 3i, 2 + 4i)', 'compareNatural([1, 2, 4], [1, 2, 3])', 'compareNatural([1, 5], [1, 2, 3])', 'compareNatural([1, 2], [1, 2])', 'compareNatural({a: 2}, {a: 4})'],\n  seealso: ['equal', 'unequal', 'smaller', 'smallerEq', 'largerEq', 'compare', 'compareText']\n};", "map": {"version": 3, "names": ["compareNaturalDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/compareNatural.js"], "sourcesContent": ["export var compareNaturalDocs = {\n  name: 'compareNatural',\n  category: 'Relational',\n  syntax: ['compareNatural(x, y)'],\n  description: 'Compare two values of any type in a deterministic, natural way. ' + 'Returns 1 when x > y, -1 when x < y, and 0 when x == y.',\n  examples: ['compareNatural(2, 3)', 'compareNatural(3, 2)', 'compareNatural(2, 2)', 'compareNatural(5cm, 40mm)', 'compareNatural(\"2\", \"10\")', 'compareNatural(2 + 3i, 2 + 4i)', 'compareNatural([1, 2, 4], [1, 2, 3])', 'compareNatural([1, 5], [1, 2, 3])', 'compareNatural([1, 2], [1, 2])', 'compareNatural({a: 2}, {a: 4})'],\n  seealso: ['equal', 'unequal', 'smaller', 'smallerEq', 'largerEq', 'compare', 'compareText']\n};"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG;EAC9BC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,sBAAsB,CAAC;EAChCC,WAAW,EAAE,kEAAkE,GAAG,yDAAyD;EAC3IC,QAAQ,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,2BAA2B,EAAE,2BAA2B,EAAE,gCAAgC,EAAE,sCAAsC,EAAE,mCAAmC,EAAE,gCAAgC,EAAE,gCAAgC,CAAC;EAC/TC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa;AAC5F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}