{"ast": null, "code": "export var InfinityDocs = {\n  name: 'Infinity',\n  category: 'Constants',\n  syntax: ['Infinity'],\n  description: 'Infinity, a number which is larger than the maximum number that can be handled by a floating point number.',\n  examples: ['Infinity', '1 / 0'],\n  seealso: []\n};", "map": {"version": 3, "names": ["InfinityDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/Infinity.js"], "sourcesContent": ["export var InfinityDocs = {\n  name: 'Infinity',\n  category: 'Constants',\n  syntax: ['Infinity'],\n  description: 'Infinity, a number which is larger than the maximum number that can be handled by a floating point number.',\n  examples: ['Infinity', '1 / 0'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,4GAA4G;EACzHC,QAAQ,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAC/BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}