{"ast": null, "code": "export var lyapDocs = {\n  name: 'lyap',\n  category: 'Algebra',\n  syntax: ['lyap(A,Q)'],\n  description: 'Solves the Continuous-time Lyapunov equation AP+PA\\'+Q=0 for P',\n  examples: ['lyap([[-2, 0], [1, -4]], [[3, 1], [1, 3]])', 'A = [[-2, 0], [1, -4]]', 'Q = [[3, 1], [1, 3]]', 'lyap(A,Q)'],\n  seealso: ['schur', 'sylvester']\n};", "map": {"version": 3, "names": ["lyapDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/lyap.js"], "sourcesContent": ["export var lyapDocs = {\n  name: 'lyap',\n  category: 'Algebra',\n  syntax: ['lyap(A,Q)'],\n  description: 'Solves the Continuous-time Lyapunov equation AP+PA\\'+Q=0 for P',\n  examples: ['lyap([[-2, 0], [1, -4]], [[3, 1], [1, 3]])', 'A = [[-2, 0], [1, -4]]', 'Q = [[3, 1], [1, 3]]', 'lyap(A,Q)'],\n  seealso: ['schur', 'sylvester']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,gEAAgE;EAC7EC,QAAQ,EAAE,CAAC,4CAA4C,EAAE,wBAAwB,EAAE,sBAAsB,EAAE,WAAW,CAAC;EACvHC,OAAO,EAAE,CAAC,OAAO,EAAE,WAAW;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}