{"ast": null, "code": "export var symbolicEqualDocs = {\n  name: 'symbolicEqual',\n  category: 'Algebra',\n  syntax: ['symbolicEqual(expr1, expr2)', 'symbolicEqual(expr1, expr2, options)'],\n  description: 'Returns true if the difference of the expressions simplifies to 0',\n  examples: ['symbolicEqual(\"x*y\",\"y*x\")', 'symbolicEqual(\"abs(x^2)\", \"x^2\")', 'symbolicEqual(\"abs(x)\", \"x\", {context: {abs: {trivial: true}}})'],\n  seealso: ['simplify', 'evaluate']\n};", "map": {"version": 3, "names": ["symbolicEqualDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/symbolicEqual.js"], "sourcesContent": ["export var symbolicEqualDocs = {\n  name: 'symbolicEqual',\n  category: 'Algebra',\n  syntax: ['symbolicEqual(expr1, expr2)', 'symbolicEqual(expr1, expr2, options)'],\n  description: 'Returns true if the difference of the expressions simplifies to 0',\n  examples: ['symbolicEqual(\"x*y\",\"y*x\")', 'symbolicEqual(\"abs(x^2)\", \"x^2\")', 'symbolicEqual(\"abs(x)\", \"x\", {context: {abs: {trivial: true}}})'],\n  seealso: ['simplify', 'evaluate']\n};"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG;EAC7BC,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,6BAA6B,EAAE,sCAAsC,CAAC;EAC/EC,WAAW,EAAE,mEAAmE;EAChFC,QAAQ,EAAE,CAAC,4BAA4B,EAAE,kCAAkC,EAAE,iEAAiE,CAAC;EAC/IC,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}