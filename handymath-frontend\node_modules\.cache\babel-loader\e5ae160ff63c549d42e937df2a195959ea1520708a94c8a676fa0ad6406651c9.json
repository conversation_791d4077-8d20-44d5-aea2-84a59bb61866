{"ast": null, "code": "export var unequalDocs = {\n  name: 'unequal',\n  category: 'Relational',\n  syntax: ['x != y', 'unequal(x, y)'],\n  description: 'Check unequality of two values. Returns true if the values are unequal, and false if they are equal.',\n  examples: ['2+2 != 3', '2+2 != 4', 'a = 3.2', 'b = 6-2.8', 'a != b', '50cm != 0.5m', '5 cm != 2 inch'],\n  seealso: ['equal', 'smaller', 'larger', 'smallerEq', 'largerEq', 'compare', 'deepEqual']\n};", "map": {"version": 3, "names": ["unequalDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/unequal.js"], "sourcesContent": ["export var unequalDocs = {\n  name: 'unequal',\n  category: 'Relational',\n  syntax: ['x != y', 'unequal(x, y)'],\n  description: 'Check unequality of two values. Returns true if the values are unequal, and false if they are equal.',\n  examples: ['2+2 != 3', '2+2 != 4', 'a = 3.2', 'b = 6-2.8', 'a != b', '50cm != 0.5m', '5 cm != 2 inch'],\n  seealso: ['equal', 'smaller', 'larger', 'smallerEq', 'largerEq', 'compare', 'deepEqual']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,EAAE,eAAe,CAAC;EACnCC,WAAW,EAAE,sGAAsG;EACnHC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,CAAC;EACtGC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}