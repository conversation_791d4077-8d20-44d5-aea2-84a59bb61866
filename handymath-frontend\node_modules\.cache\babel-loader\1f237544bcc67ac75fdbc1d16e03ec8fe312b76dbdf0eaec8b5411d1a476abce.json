{"ast": null, "code": "// creating all factories here in a separate file is needed to get tree-shaking working\nimport * as allFactories from '../factoriesAny.js';\nexport var all = allFactories;", "map": {"version": 3, "names": ["allFactories", "all"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/allFactoriesAny.js"], "sourcesContent": ["// creating all factories here in a separate file is needed to get tree-shaking working\nimport * as allFactories from '../factoriesAny.js';\nexport var all = allFactories;"], "mappings": "AAAA;AACA,OAAO,KAAKA,YAAY,MAAM,oBAAoB;AAClD,OAAO,IAAIC,GAAG,GAAGD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}