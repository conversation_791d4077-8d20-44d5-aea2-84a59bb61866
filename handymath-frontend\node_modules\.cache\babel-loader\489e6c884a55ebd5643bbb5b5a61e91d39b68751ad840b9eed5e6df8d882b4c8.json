{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { deepMap } from '../../../utils/collection.js';\nvar name = 'fraction';\nvar dependencies = ['typed', 'Fraction'];\nexport var createFraction = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Fraction\n  } = _ref;\n  /**\n   * Create a fraction or convert a value to a fraction.\n   *\n   * With one numeric argument, produces the closest rational approximation to the\n   * input.\n   * With two arguments, the first is the numerator and the second is the denominator,\n   * and creates the corresponding fraction. Both numerator and denominator must be\n   * integers.\n   * With one object argument, looks for the integer numerator as the value of property\n   * 'n' and the integer denominator as the value of property 'd'.\n   * With a matrix argument, creates a matrix of the same shape with entries\n   * converted into fractions.\n   *\n   * Syntax:\n   *     math.fraction(value)\n   *     math.fraction(numerator, denominator)\n   *     math.fraction({n: numerator, d: denominator})\n   *     math.fraction(matrix: Array | Matrix)\n   *\n   * Examples:\n   *\n   *     math.fraction(6.283)             // returns Fraction 6283/1000\n   *     math.fraction(1, 3)              // returns Fraction 1/3\n   *     math.fraction('2/3')             // returns Fraction 2/3\n   *     math.fraction({n: 2, d: 3})      // returns Fraction 2/3\n   *     math.fraction([0.2, 0.25, 1.25]) // returns Array [1/5, 1/4, 5/4]\n   *     math.fraction(4, 5.1)            // throws Error: Parameters must be integer\n   *\n   * See also:\n   *\n   *    bignumber, number, string, unit\n   *\n   * @param {number | string | Fraction | BigNumber | bigint | Unit | Array | Matrix} [args]\n   *            Arguments specifying the value, or numerator and denominator of\n   *            the fraction\n   * @return {Fraction | Array | Matrix} Returns a fraction\n   */\n  return typed('fraction', {\n    number: function number(x) {\n      if (!isFinite(x) || isNaN(x)) {\n        throw new Error(x + ' cannot be represented as a fraction');\n      }\n      return new Fraction(x);\n    },\n    string: function string(x) {\n      return new Fraction(x);\n    },\n    'number, number': function number_number(numerator, denominator) {\n      return new Fraction(numerator, denominator);\n    },\n    'bigint, bigint': function bigint_bigint(numerator, denominator) {\n      return new Fraction(numerator, denominator);\n    },\n    null: function _null(x) {\n      return new Fraction(0);\n    },\n    BigNumber: function BigNumber(x) {\n      return new Fraction(x.toString());\n    },\n    bigint: function bigint(x) {\n      return new Fraction(x.toString());\n    },\n    Fraction: function Fraction(x) {\n      return x; // fractions are immutable\n    },\n    Unit: typed.referToSelf(self => x => {\n      var clone = x.clone();\n      clone.value = self(x.value);\n      return clone;\n    }),\n    Object: function Object(x) {\n      return new Fraction(x);\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["factory", "deepMap", "name", "dependencies", "createFraction", "_ref", "typed", "Fraction", "number", "x", "isFinite", "isNaN", "Error", "string", "number_number", "numerator", "denominator", "bigint_bigint", "null", "_null", "BigNumber", "toString", "bigint", "Unit", "referToSelf", "self", "clone", "value", "Object"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/fraction/function/fraction.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { deepMap } from '../../../utils/collection.js';\nvar name = 'fraction';\nvar dependencies = ['typed', 'Fraction'];\nexport var createFraction = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Fraction\n  } = _ref;\n  /**\n   * Create a fraction or convert a value to a fraction.\n   *\n   * With one numeric argument, produces the closest rational approximation to the\n   * input.\n   * With two arguments, the first is the numerator and the second is the denominator,\n   * and creates the corresponding fraction. Both numerator and denominator must be\n   * integers.\n   * With one object argument, looks for the integer numerator as the value of property\n   * 'n' and the integer denominator as the value of property 'd'.\n   * With a matrix argument, creates a matrix of the same shape with entries\n   * converted into fractions.\n   *\n   * Syntax:\n   *     math.fraction(value)\n   *     math.fraction(numerator, denominator)\n   *     math.fraction({n: numerator, d: denominator})\n   *     math.fraction(matrix: Array | Matrix)\n   *\n   * Examples:\n   *\n   *     math.fraction(6.283)             // returns Fraction 6283/1000\n   *     math.fraction(1, 3)              // returns Fraction 1/3\n   *     math.fraction('2/3')             // returns Fraction 2/3\n   *     math.fraction({n: 2, d: 3})      // returns Fraction 2/3\n   *     math.fraction([0.2, 0.25, 1.25]) // returns Array [1/5, 1/4, 5/4]\n   *     math.fraction(4, 5.1)            // throws Error: Parameters must be integer\n   *\n   * See also:\n   *\n   *    bignumber, number, string, unit\n   *\n   * @param {number | string | Fraction | BigNumber | bigint | Unit | Array | Matrix} [args]\n   *            Arguments specifying the value, or numerator and denominator of\n   *            the fraction\n   * @return {Fraction | Array | Matrix} Returns a fraction\n   */\n  return typed('fraction', {\n    number: function number(x) {\n      if (!isFinite(x) || isNaN(x)) {\n        throw new Error(x + ' cannot be represented as a fraction');\n      }\n      return new Fraction(x);\n    },\n    string: function string(x) {\n      return new Fraction(x);\n    },\n    'number, number': function number_number(numerator, denominator) {\n      return new Fraction(numerator, denominator);\n    },\n    'bigint, bigint': function bigint_bigint(numerator, denominator) {\n      return new Fraction(numerator, denominator);\n    },\n    null: function _null(x) {\n      return new Fraction(0);\n    },\n    BigNumber: function BigNumber(x) {\n      return new Fraction(x.toString());\n    },\n    bigint: function bigint(x) {\n      return new Fraction(x.toString());\n    },\n    Fraction: function Fraction(x) {\n      return x; // fractions are immutable\n    },\n    Unit: typed.referToSelf(self => x => {\n      var clone = x.clone();\n      clone.value = self(x.value);\n      return clone;\n    }),\n    Object: function Object(x) {\n      return new Fraction(x);\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,8BAA8B;AACtD,IAAIC,IAAI,GAAG,UAAU;AACrB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AACxC,OAAO,IAAIC,cAAc,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC7E,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAAC,UAAU,EAAE;IACvBE,MAAM,EAAE,SAASA,MAAMA,CAACC,CAAC,EAAE;MACzB,IAAI,CAACC,QAAQ,CAACD,CAAC,CAAC,IAAIE,KAAK,CAACF,CAAC,CAAC,EAAE;QAC5B,MAAM,IAAIG,KAAK,CAACH,CAAC,GAAG,sCAAsC,CAAC;MAC7D;MACA,OAAO,IAAIF,QAAQ,CAACE,CAAC,CAAC;IACxB,CAAC;IACDI,MAAM,EAAE,SAASA,MAAMA,CAACJ,CAAC,EAAE;MACzB,OAAO,IAAIF,QAAQ,CAACE,CAAC,CAAC;IACxB,CAAC;IACD,gBAAgB,EAAE,SAASK,aAAaA,CAACC,SAAS,EAAEC,WAAW,EAAE;MAC/D,OAAO,IAAIT,QAAQ,CAACQ,SAAS,EAAEC,WAAW,CAAC;IAC7C,CAAC;IACD,gBAAgB,EAAE,SAASC,aAAaA,CAACF,SAAS,EAAEC,WAAW,EAAE;MAC/D,OAAO,IAAIT,QAAQ,CAACQ,SAAS,EAAEC,WAAW,CAAC;IAC7C,CAAC;IACDE,IAAI,EAAE,SAASC,KAAKA,CAACV,CAAC,EAAE;MACtB,OAAO,IAAIF,QAAQ,CAAC,CAAC,CAAC;IACxB,CAAC;IACDa,SAAS,EAAE,SAASA,SAASA,CAACX,CAAC,EAAE;MAC/B,OAAO,IAAIF,QAAQ,CAACE,CAAC,CAACY,QAAQ,CAAC,CAAC,CAAC;IACnC,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACb,CAAC,EAAE;MACzB,OAAO,IAAIF,QAAQ,CAACE,CAAC,CAACY,QAAQ,CAAC,CAAC,CAAC;IACnC,CAAC;IACDd,QAAQ,EAAE,SAASA,QAAQA,CAACE,CAAC,EAAE;MAC7B,OAAOA,CAAC,CAAC,CAAC;IACZ,CAAC;IACDc,IAAI,EAAEjB,KAAK,CAACkB,WAAW,CAACC,IAAI,IAAIhB,CAAC,IAAI;MACnC,IAAIiB,KAAK,GAAGjB,CAAC,CAACiB,KAAK,CAAC,CAAC;MACrBA,KAAK,CAACC,KAAK,GAAGF,IAAI,CAAChB,CAAC,CAACkB,KAAK,CAAC;MAC3B,OAAOD,KAAK;IACd,CAAC,CAAC;IACFE,MAAM,EAAE,SAASA,MAAMA,CAACnB,CAAC,EAAE;MACzB,OAAO,IAAIF,QAAQ,CAACE,CAAC,CAAC;IACxB,CAAC;IACD,gBAAgB,EAAEH,KAAK,CAACkB,WAAW,CAACC,IAAI,IAAIhB,CAAC,IAAIR,OAAO,CAACQ,CAAC,EAAEgB,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}