# Generated by Django 5.2.1 on 2025-06-03 14:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0003_alter_user_options'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='course',
            options={'verbose_name': 'Cours', 'verbose_name_plural': 'Cours'},
        ),
        migrations.AlterModelOptions(
            name='equation',
            options={'verbose_name': 'Équation', 'verbose_name_plural': 'Équations'},
        ),
        migrations.AlterModelOptions(
            name='exercise',
            options={'verbose_name': 'Exercice', 'verbose_name_plural': 'Exercices'},
        ),
        migrations.RemoveField(
            model_name='course',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='equation',
            name='steps',
        ),
        migrations.AddField(
            model_name='course',
            name='level',
            field=models.CharField(choices=[('beginner', 'Débutant'), ('intermediate', 'Intermédiaire'), ('advanced', 'Avancé')], default='beginner', max_length=20),
        ),
        migrations.AlterField(
            model_name='course',
            name='instructor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='courses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='equation',
            name='equation_text',
            field=models.CharField(max_length=500),
        ),
    ]
