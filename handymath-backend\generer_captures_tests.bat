@echo off
echo ========================================
echo    GENERATION DES CAPTURES DE TESTS
echo         HANDYMATH - PROJET PFA
echo ========================================
echo.

REM Activer l'environnement virtuel
call venv_new\Scripts\activate

echo [1/8] Tests mathematiques...
echo.
echo COMMANDE: python -m pytest tests/test_math_basic.py -v
echo.
python -m pytest tests/test_math_basic.py -v
echo.
echo *** CAPTURE 1: Capturez cette sortie pour "Tests Mathematiques" ***
pause

echo.
echo [2/8] Tests API...
echo.
echo COMMANDE: python -m pytest tests/test_api_basic.py -v
echo.
python -m pytest tests/test_api_basic.py -v
echo.
echo *** CAPTURE 2: Capturez cette sortie pour "Tests API" ***
pause

echo.
echo [3/8] Couverture de code...
echo.
echo COMMANDE: python -m pytest tests/test_math_basic.py tests/test_api_basic.py --cov=api --cov-report=term
echo.
python -m pytest tests/test_math_basic.py tests/test_api_basic.py --cov=api --cov-report=term
echo.
echo *** CAPTURE 3: Capturez cette sortie pour "Couverture de Code" ***
pause

echo.
echo [4/8] Test specifique...
echo.
echo COMMANDE: python -m pytest tests/test_math_basic.py::TestEquationSolver::test_linear_equation_solver -v
echo.
python -m pytest tests/test_math_basic.py::TestEquationSolver::test_linear_equation_solver -v
echo.
echo *** CAPTURE 4: Capturez cette sortie pour "Test Specifique" ***
pause

echo.
echo [5/8] Resume global...
echo.
echo COMMANDE: python -m pytest tests/test_math_basic.py tests/test_api_basic.py -v --tb=short
echo.
python -m pytest tests/test_math_basic.py tests/test_api_basic.py -v --tb=short
echo.
echo *** CAPTURE 5: Capturez cette sortie pour "Resume Global" ***
pause

echo.
echo [6/8] Version pytest...
echo.
echo COMMANDE: python -m pytest --version
echo.
python -m pytest --version
echo.
echo *** CAPTURE 6: Capturez cette sortie pour "Configuration Pytest" ***
pause

echo.
echo [7/8] Structure des tests...
echo.
echo COMMANDE: dir tests
echo.
dir tests
echo.
echo *** CAPTURE 7: Capturez cette sortie pour "Structure des Tests" ***
pause

echo.
echo [8/8] Generation rapport HTML...
echo.
echo COMMANDE: python -m pytest tests/test_math_basic.py tests/test_api_basic.py --html=rapport_tests_handymath.html --self-contained-html
echo.
python -m pytest tests/test_math_basic.py tests/test_api_basic.py --html=rapport_tests_handymath.html --self-contained-html
echo.
echo *** CAPTURE 8: Capturez cette sortie pour "Generation Rapport HTML" ***
echo.
echo ========================================
echo   TOUTES LES CAPTURES SONT TERMINEES
echo ========================================
echo.
echo Fichiers generes:
echo - rapport_tests_handymath.html (rapport HTML interactif)
echo - GUIDE_CAPTURES_TESTS.md (guide detaille)
echo - RAPPORT_TESTS_PYTEST.md (rapport complet)
echo.
echo Ouvrez le rapport HTML dans votre navigateur:
echo file:///C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-backend/rapport_tests_handymath.html
echo.
pause
