{"ast": null, "code": "export var unaryPlusDocs = {\n  name: 'unaryPlus',\n  category: 'Operators',\n  syntax: ['+x', 'unaryPlus(x)'],\n  description: 'Converts booleans and strings to numbers.',\n  examples: ['+true', '+\"2\"'],\n  seealso: ['add', 'subtract', 'unaryMinus']\n};", "map": {"version": 3, "names": ["unaryPlusDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/unaryPlus.js"], "sourcesContent": ["export var unaryPlusDocs = {\n  name: 'unaryPlus',\n  category: 'Operators',\n  syntax: ['+x', 'unaryPlus(x)'],\n  description: 'Converts booleans and strings to numbers.',\n  examples: ['+true', '+\"2\"'],\n  seealso: ['add', 'subtract', 'unaryMinus']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC;EAC9BC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC3BC,OAAO,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}