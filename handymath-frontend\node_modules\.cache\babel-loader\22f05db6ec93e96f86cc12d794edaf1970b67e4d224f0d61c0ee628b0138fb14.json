{"ast": null, "code": "export var resolveDocs = {\n  name: 'resolve',\n  category: 'Algebra',\n  syntax: ['resolve(node, scope)'],\n  description: 'Recursively substitute variables in an expression tree.',\n  examples: ['resolve(parse(\"1 + x\"), { x: 7 })', 'resolve(parse(\"size(text)\"), { text: \"Hello World\" })', 'resolve(parse(\"x + y\"), { x: parse(\"3z\") })', 'resolve(parse(\"3x\"), { x: parse(\"y+z\"), z: parse(\"w^y\") })'],\n  seealso: ['simplify', 'evaluate'],\n  mayThrow: ['ReferenceError']\n};", "map": {"version": 3, "names": ["resolveDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also", "mayThrow"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/resolve.js"], "sourcesContent": ["export var resolveDocs = {\n  name: 'resolve',\n  category: 'Algebra',\n  syntax: ['resolve(node, scope)'],\n  description: 'Recursively substitute variables in an expression tree.',\n  examples: ['resolve(parse(\"1 + x\"), { x: 7 })', 'resolve(parse(\"size(text)\"), { text: \"Hello World\" })', 'resolve(parse(\"x + y\"), { x: parse(\"3z\") })', 'resolve(parse(\"3x\"), { x: parse(\"y+z\"), z: parse(\"w^y\") })'],\n  seealso: ['simplify', 'evaluate'],\n  mayThrow: ['ReferenceError']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,sBAAsB,CAAC;EAChCC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,CAAC,mCAAmC,EAAE,uDAAuD,EAAE,6CAA6C,EAAE,4DAA4D,CAAC;EACrNC,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACjCC,QAAQ,EAAE,CAAC,gBAAgB;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}