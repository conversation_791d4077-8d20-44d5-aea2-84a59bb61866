{"ast": null, "code": "export var compareDocs = {\n  name: 'compare',\n  category: 'Relational',\n  syntax: ['compare(x, y)'],\n  description: 'Compare two values. ' + 'Returns 1 when x > y, -1 when x < y, and 0 when x == y.',\n  examples: ['compare(2, 3)', 'compare(3, 2)', 'compare(2, 2)', 'compare(5cm, 40mm)', 'compare(2, [1, 2, 3])'],\n  seealso: ['equal', 'unequal', 'smaller', 'smallerEq', 'largerEq', 'compareNatural', 'compareText']\n};", "map": {"version": 3, "names": ["compareDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/compare.js"], "sourcesContent": ["export var compareDocs = {\n  name: 'compare',\n  category: 'Relational',\n  syntax: ['compare(x, y)'],\n  description: 'Compare two values. ' + 'Returns 1 when x > y, -1 when x < y, and 0 when x == y.',\n  examples: ['compare(2, 3)', 'compare(3, 2)', 'compare(2, 2)', 'compare(5cm, 40mm)', 'compare(2, [1, 2, 3])'],\n  seealso: ['equal', 'unequal', 'smaller', 'smallerEq', 'largerEq', 'compareNatural', 'compareText']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,eAAe,CAAC;EACzBC,WAAW,EAAE,sBAAsB,GAAG,yDAAyD;EAC/FC,QAAQ,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;EAC5GC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa;AACnG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}