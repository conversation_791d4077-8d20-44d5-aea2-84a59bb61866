{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createThomsonCrossSection } from '../../factoriesAny.js';\nexport var thomsonCrossSectionDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createThomsonCrossSection\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createThomsonCrossSection", "thomsonCrossSectionDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesThomsonCrossSection.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createThomsonCrossSection } from '../../factoriesAny.js';\nexport var thomsonCrossSectionDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createThomsonCrossSection\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,yBAAyB,QAAQ,uBAAuB;AACjE,OAAO,IAAIC,+BAA+B,GAAG;EAC3CH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}