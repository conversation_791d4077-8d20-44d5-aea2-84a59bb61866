{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo05xSfSf';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo05xSfSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked MAX(NNZA, NNZB) times\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 || B(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo05xSfSf(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspaces\n    var xa = cvalues ? [] : undefined;\n    var xb = cvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var wa = [];\n    var wb = [];\n\n    // vars\n    var i, j, k, k1;\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // loop values A(:,j)\n      for (k = aptr[j], k1 = aptr[j + 1]; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // push index\n        cindex.push(i);\n        // update workspace\n        wa[i] = mark;\n        // check we need to process values\n        if (xa) {\n          xa[i] = avalues[k];\n        }\n      }\n      // loop values B(:,j)\n      for (k = bptr[j], k1 = bptr[j + 1]; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // check row existed in A\n        if (wa[i] !== mark) {\n          // push index\n          cindex.push(i);\n        }\n        // update workspace\n        wb[i] = mark;\n        // check we need to process values\n        if (xb) {\n          xb[i] = bvalues[k];\n        }\n      }\n      // check we need to process values (non pattern matrix)\n      if (cvalues) {\n        // initialize first index in j\n        k = cptr[j];\n        // loop index in j\n        while (k < cindex.length) {\n          // row\n          i = cindex[k];\n          // marks\n          var wai = wa[i];\n          var wbi = wb[i];\n          // check Aij or Bij are nonzero\n          if (wai === mark || wbi === mark) {\n            // matrix values @ i,j\n            var va = wai === mark ? xa[i] : zero;\n            var vb = wbi === mark ? xb[i] : zero;\n            // Cij\n            var vc = cf(va, vb);\n            // check for zero\n            if (!eq(vc, zero)) {\n              // push value\n              cvalues.push(vc);\n              // increment pointer\n              k++;\n            } else {\n              // remove value @ i, do not increment pointer\n              cindex.splice(k, 1);\n            }\n          }\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo05xSfSf", "_ref", "typed", "equalScalar", "matAlgo05xSfSf", "a", "b", "callback", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bvalues", "bindex", "bptr", "bsize", "bdt", "length", "RangeError", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "xa", "xb", "wa", "wb", "i", "j", "k", "k1", "mark", "push", "wai", "wbi", "va", "vb", "vc", "splice", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo05xSfSf.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo05xSfSf';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo05xSfSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked MAX(NNZA, NNZB) times\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 || B(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo05xSfSf(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspaces\n    var xa = cvalues ? [] : undefined;\n    var xb = cvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var wa = [];\n    var wb = [];\n\n    // vars\n    var i, j, k, k1;\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // loop values A(:,j)\n      for (k = aptr[j], k1 = aptr[j + 1]; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // push index\n        cindex.push(i);\n        // update workspace\n        wa[i] = mark;\n        // check we need to process values\n        if (xa) {\n          xa[i] = avalues[k];\n        }\n      }\n      // loop values B(:,j)\n      for (k = bptr[j], k1 = bptr[j + 1]; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // check row existed in A\n        if (wa[i] !== mark) {\n          // push index\n          cindex.push(i);\n        }\n        // update workspace\n        wb[i] = mark;\n        // check we need to process values\n        if (xb) {\n          xb[i] = bvalues[k];\n        }\n      }\n      // check we need to process values (non pattern matrix)\n      if (cvalues) {\n        // initialize first index in j\n        k = cptr[j];\n        // loop index in j\n        while (k < cindex.length) {\n          // row\n          i = cindex[k];\n          // marks\n          var wai = wa[i];\n          var wbi = wb[i];\n          // check Aij or Bij are nonzero\n          if (wai === mark || wbi === mark) {\n            // matrix values @ i,j\n            var va = wai === mark ? xa[i] : zero;\n            var vb = wbi === mark ? xb[i] : zero;\n            // Cij\n            var vc = cf(va, vb);\n            // check for zero\n            if (!eq(vc, zero)) {\n              // push value\n              cvalues.push(vc);\n              // increment pointer\n              k++;\n            } else {\n              // remove value @ i, do not increment pointer\n              cindex.splice(k, 1);\n            }\n          }\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,oBAAoB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC7C;IACA,IAAIC,OAAO,GAAGH,CAAC,CAACI,OAAO;IACvB,IAAIC,MAAM,GAAGL,CAAC,CAACM,MAAM;IACrB,IAAIC,IAAI,GAAGP,CAAC,CAACQ,IAAI;IACjB,IAAIC,KAAK,GAAGT,CAAC,CAACU,KAAK;IACnB,IAAIC,GAAG,GAAGX,CAAC,CAACY,SAAS,IAAIZ,CAAC,CAACa,KAAK,KAAKC,SAAS,GAAGd,CAAC,CAACY,SAAS,GAAGZ,CAAC,CAACe,WAAW,CAAC,CAAC;IAC9E;IACA,IAAIC,OAAO,GAAGf,CAAC,CAACG,OAAO;IACvB,IAAIa,MAAM,GAAGhB,CAAC,CAACK,MAAM;IACrB,IAAIY,IAAI,GAAGjB,CAAC,CAACO,IAAI;IACjB,IAAIW,KAAK,GAAGlB,CAAC,CAACS,KAAK;IACnB,IAAIU,GAAG,GAAGnB,CAAC,CAACW,SAAS,IAAIX,CAAC,CAACY,KAAK,KAAKC,SAAS,GAAGb,CAAC,CAACW,SAAS,GAAGX,CAAC,CAACc,WAAW,CAAC,CAAC;;IAE9E;IACA,IAAIN,KAAK,CAACY,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;MACjC,MAAM,IAAI7B,cAAc,CAACiB,KAAK,CAACY,MAAM,EAAEF,KAAK,CAACE,MAAM,CAAC;IACtD;;IAEA;IACA,IAAIZ,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,IAAIV,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIG,UAAU,CAAC,gCAAgC,GAAGb,KAAK,GAAG,yBAAyB,GAAGU,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAII,IAAI,GAAGd,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIe,OAAO,GAAGf,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIgB,EAAE;IACN;IACA,IAAIC,EAAE,GAAG5B,WAAW;IACpB;IACA,IAAI6B,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAG1B,QAAQ;;IAEjB;IACA,IAAI,OAAOS,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKS,GAAG,IAAIT,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAc,EAAE,GAAGd,GAAG;MACR;MACAe,EAAE,GAAG7B,KAAK,CAACgC,IAAI,CAAC/B,WAAW,EAAE,CAAC2B,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAG9B,KAAK,CAACiC,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAG,EAAE,GAAG/B,KAAK,CAACgC,IAAI,CAAC3B,QAAQ,EAAE,CAACuB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAG5B,OAAO,IAAIa,OAAO,GAAG,EAAE,GAAGF,SAAS;IACjD,IAAIkB,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,IAAIC,EAAE,GAAGH,OAAO,GAAG,EAAE,GAAGjB,SAAS;IACjC,IAAIqB,EAAE,GAAGJ,OAAO,GAAG,EAAE,GAAGjB,SAAS;IACjC;IACA,IAAIsB,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;;IAEX;IACA,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE;;IAEf;IACA,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,OAAO,EAAEe,CAAC,EAAE,EAAE;MAC5B;MACAN,IAAI,CAACM,CAAC,CAAC,GAAGP,MAAM,CAACX,MAAM;MACvB;MACA,IAAIqB,IAAI,GAAGH,CAAC,GAAG,CAAC;MAChB;MACA,KAAKC,CAAC,GAAGjC,IAAI,CAACgC,CAAC,CAAC,EAAEE,EAAE,GAAGlC,IAAI,CAACgC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;QAC/C;QACAF,CAAC,GAAGjC,MAAM,CAACmC,CAAC,CAAC;QACb;QACAR,MAAM,CAACW,IAAI,CAACL,CAAC,CAAC;QACd;QACAF,EAAE,CAACE,CAAC,CAAC,GAAGI,IAAI;QACZ;QACA,IAAIR,EAAE,EAAE;UACNA,EAAE,CAACI,CAAC,CAAC,GAAGnC,OAAO,CAACqC,CAAC,CAAC;QACpB;MACF;MACA;MACA,KAAKA,CAAC,GAAGtB,IAAI,CAACqB,CAAC,CAAC,EAAEE,EAAE,GAAGvB,IAAI,CAACqB,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;QAC/C;QACAF,CAAC,GAAGrB,MAAM,CAACuB,CAAC,CAAC;QACb;QACA,IAAIJ,EAAE,CAACE,CAAC,CAAC,KAAKI,IAAI,EAAE;UAClB;UACAV,MAAM,CAACW,IAAI,CAACL,CAAC,CAAC;QAChB;QACA;QACAD,EAAE,CAACC,CAAC,CAAC,GAAGI,IAAI;QACZ;QACA,IAAIP,EAAE,EAAE;UACNA,EAAE,CAACG,CAAC,CAAC,GAAGtB,OAAO,CAACwB,CAAC,CAAC;QACpB;MACF;MACA;MACA,IAAIT,OAAO,EAAE;QACX;QACAS,CAAC,GAAGP,IAAI,CAACM,CAAC,CAAC;QACX;QACA,OAAOC,CAAC,GAAGR,MAAM,CAACX,MAAM,EAAE;UACxB;UACAiB,CAAC,GAAGN,MAAM,CAACQ,CAAC,CAAC;UACb;UACA,IAAII,GAAG,GAAGR,EAAE,CAACE,CAAC,CAAC;UACf,IAAIO,GAAG,GAAGR,EAAE,CAACC,CAAC,CAAC;UACf;UACA,IAAIM,GAAG,KAAKF,IAAI,IAAIG,GAAG,KAAKH,IAAI,EAAE;YAChC;YACA,IAAII,EAAE,GAAGF,GAAG,KAAKF,IAAI,GAAGR,EAAE,CAACI,CAAC,CAAC,GAAGX,IAAI;YACpC,IAAIoB,EAAE,GAAGF,GAAG,KAAKH,IAAI,GAAGP,EAAE,CAACG,CAAC,CAAC,GAAGX,IAAI;YACpC;YACA,IAAIqB,EAAE,GAAGpB,EAAE,CAACkB,EAAE,EAAEC,EAAE,CAAC;YACnB;YACA,IAAI,CAACrB,EAAE,CAACsB,EAAE,EAAErB,IAAI,CAAC,EAAE;cACjB;cACAI,OAAO,CAACY,IAAI,CAACK,EAAE,CAAC;cAChB;cACAR,CAAC,EAAE;YACL,CAAC,MAAM;cACL;cACAR,MAAM,CAACiB,MAAM,CAACT,CAAC,EAAE,CAAC,CAAC;YACrB;UACF;QACF;MACF;IACF;IACA;IACAP,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACX,MAAM;;IAE7B;IACA,OAAOrB,CAAC,CAACkD,kBAAkB,CAAC;MAC1BC,MAAM,EAAEpB,OAAO;MACfqB,KAAK,EAAEpB,MAAM;MACbqB,GAAG,EAAEpB,IAAI;MACTqB,IAAI,EAAE,CAAC/B,IAAI,EAAEC,OAAO,CAAC;MACrB+B,QAAQ,EAAE5C,GAAG,KAAKX,CAAC,CAACY,SAAS,IAAIQ,GAAG,KAAKnB,CAAC,CAACW,SAAS,GAAGa,EAAE,GAAGX;IAC9D,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}