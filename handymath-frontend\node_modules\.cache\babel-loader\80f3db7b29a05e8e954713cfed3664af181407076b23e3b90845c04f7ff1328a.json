{"ast": null, "code": "import { IndexError } from '../../../error/IndexError.js';\n\n/**\n * Transform zero-based indices to one-based indices in errors\n * @param {Error} err\n * @returns {Error | IndexError} Returns the transformed error\n */\nexport function errorTransform(err) {\n  if (err && err.isIndexError) {\n    return new IndexError(err.index + 1, err.min + 1, err.max !== undefined ? err.max + 1 : undefined);\n  }\n  return err;\n}", "map": {"version": 3, "names": ["IndexError", "errorTransform", "err", "isIndexError", "index", "min", "max", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/transform/utils/errorTransform.js"], "sourcesContent": ["import { IndexError } from '../../../error/IndexError.js';\n\n/**\n * Transform zero-based indices to one-based indices in errors\n * @param {Error} err\n * @returns {Error | IndexError} Returns the transformed error\n */\nexport function errorTransform(err) {\n  if (err && err.isIndexError) {\n    return new IndexError(err.index + 1, err.min + 1, err.max !== undefined ? err.max + 1 : undefined);\n  }\n  return err;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;;AAEzD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,GAAG,EAAE;EAClC,IAAIA,GAAG,IAAIA,GAAG,CAACC,YAAY,EAAE;IAC3B,OAAO,IAAIH,UAAU,CAACE,GAAG,CAACE,KAAK,GAAG,CAAC,EAAEF,GAAG,CAACG,GAAG,GAAG,CAAC,EAAEH,GAAG,CAACI,GAAG,KAAKC,SAAS,GAAGL,GAAG,CAACI,GAAG,GAAG,CAAC,GAAGC,SAAS,CAAC;EACpG;EACA,OAAOL,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}