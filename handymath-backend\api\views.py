from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.core.mail import send_mail
from django.conf import settings
from .models import (
    Course, Equation, Exercise, EquationImage, ExerciseChoice, ExerciseAttempt,
    Badge, UserBadge, UserLevel, Streak, Chapter, Lesson, LessonProgress,
    CourseEnrollment, LearningPath, LearningPathCourse, ContactMessage
)
from .serializers import UserSerializer, CourseSerializer, EquationSerializer, ExerciseSerializer
import re
import sympy as sp
from sympy import symbols, solve, latex
import json

# Configuration Tesseract OCR
try:
    import pytesseract
    import os
    import platform

    # Configuration automatique du chemin Tesseract selon l'OS
    if platform.system() == 'Windows':
        # Essayer plusieurs chemins possibles sur Windows
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
        ]

        # Utiliser le premier chemin qui existe
        tesseract_found = False
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                tesseract_found = True
                break

        # Si aucun chemin trouvé, laisser la configuration par défaut
        if not tesseract_found:
            print("Attention: Tesseract non trouvé dans les chemins standards. Utilisation de la configuration par défaut.")

    # Pour Linux/Mac, pytesseract utilise automatiquement le PATH

except ImportError:
    pytesseract = None
    print("Attention: pytesseract non installé. La reconnaissance OCR ne sera pas disponible.")

User = get_user_model()


def evaluate_answer(exercise, user_answer):
    """
    Évaluer intelligemment la réponse d'un utilisateur selon le type d'exercice
    """
    if not user_answer or not exercise.correct_answer:
        return False

    # Normaliser les réponses
    correct_answer = exercise.correct_answer.strip()
    user_answer = user_answer.strip()

    # Évaluation selon le type d'exercice
    if exercise.exercise_type == 'equation':
        return evaluate_equation_answer(correct_answer, user_answer)
    elif exercise.exercise_type == 'calculation':
        return evaluate_calculation_answer(correct_answer, user_answer)
    elif exercise.exercise_type == 'text':
        return evaluate_text_answer(correct_answer, user_answer)
    else:
        # Comparaison simple pour les autres types
        return correct_answer.lower() == user_answer.lower()


def evaluate_equation_answer(correct_answer, user_answer):
    """Évaluer une réponse d'équation avec SymPy"""
    try:
        import sympy as sp

        # Nettoyer les réponses
        correct_clean = clean_math_expression(correct_answer)
        user_clean = clean_math_expression(user_answer)

        # Comparaison directe d'abord
        if correct_clean.lower() == user_clean.lower():
            return True

        # Extraire les nombres pour comparaison flexible
        correct_nums = extract_all_numbers(correct_answer)
        user_nums = extract_all_numbers(user_answer)

        # Si les nombres correspondent, c'est probablement correct
        if correct_nums and user_nums and set(correct_nums) == set(user_nums):
            return True

        # Essayer de parser avec SymPy
        try:
            correct_expr = sp.sympify(correct_clean)
            user_expr = sp.sympify(user_clean)

            # Comparer les expressions
            difference = sp.simplify(correct_expr - user_expr)
            return difference == 0

        except (sp.SympifyError, ValueError):
            # Si SymPy échoue, essayer des comparaisons alternatives
            return evaluate_equation_fallback(correct_clean, user_clean)

    except ImportError:
        # Si SymPy n'est pas disponible, comparaison flexible
        return evaluate_equation_fallback(correct_answer, user_answer)


def evaluate_calculation_answer(correct_answer, user_answer):
    """Évaluer une réponse de calcul numérique"""
    try:
        # Extraire les nombres des réponses
        correct_num = extract_number(correct_answer)
        user_num = extract_number(user_answer)

        if correct_num is None or user_num is None:
            return False

        # Tolérance pour les nombres décimaux (0.01)
        tolerance = 0.01
        return abs(correct_num - user_num) <= tolerance

    except (ValueError, TypeError):
        return False


def evaluate_text_answer(correct_answer, user_answer):
    """Évaluer une réponse textuelle avec flexibilité"""
    # Normaliser les textes
    correct_normalized = normalize_text(correct_answer)
    user_normalized = normalize_text(user_answer)

    # Comparaison exacte
    if correct_normalized == user_normalized:
        return True

    # Vérifier si la réponse utilisateur contient les mots-clés essentiels
    correct_words = set(correct_normalized.split())
    user_words = set(user_normalized.split())

    # Au moins 80% des mots importants doivent être présents
    if len(correct_words) > 0:
        overlap = len(correct_words.intersection(user_words))
        return (overlap / len(correct_words)) >= 0.8

    return False


def clean_math_expression(expression):
    """Nettoyer une expression mathématique"""
    import re

    # Supprimer les espaces
    cleaned = expression.replace(' ', '')

    # Remplacer les symboles courants
    replacements = {
        '×': '*',
        '÷': '/',
        '−': '-',
        '=': '',  # Supprimer le signe égal pour les solutions
        'x=': '',
        'y=': '',
        'z=': '',
    }

    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)

    # Supprimer les caractères non mathématiques
    cleaned = re.sub(r'[^\d\+\-\*/\(\)\.\^x-z]', '', cleaned)

    return cleaned


def evaluate_equation_fallback(correct_answer, user_answer):
    """Évaluation de secours pour les équations"""
    # Comparaisons alternatives pour les équations courantes

    # Extraire les nombres des solutions (ex: "x = 5" -> 5)
    correct_nums = extract_all_numbers(correct_answer)
    user_nums = extract_all_numbers(user_answer)

    if correct_nums and user_nums:
        # Comparer les ensembles de nombres
        return set(correct_nums) == set(user_nums)

    # Comparaison de chaînes normalisées
    return correct_answer.lower().replace(' ', '') == user_answer.lower().replace(' ', '')


def extract_number(text):
    """Extraire un nombre d'un texte"""
    import re

    # Chercher des nombres (entiers ou décimaux)
    numbers = re.findall(r'-?\d+\.?\d*', text)

    if numbers:
        try:
            return float(numbers[0])
        except ValueError:
            return None

    return None


def extract_all_numbers(text):
    """Extraire tous les nombres d'un texte"""
    import re

    numbers = re.findall(r'-?\d+\.?\d*', text)
    result = []

    for num in numbers:
        try:
            if '.' in num:
                result.append(float(num))
            else:
                result.append(int(num))
        except ValueError:
            continue

    return result


def normalize_text(text):
    """Normaliser un texte pour la comparaison"""
    import re

    # Convertir en minuscules
    normalized = text.lower()

    # Supprimer la ponctuation
    normalized = re.sub(r'[^\w\s]', '', normalized)

    # Supprimer les espaces multiples
    normalized = re.sub(r'\s+', ' ', normalized)

    # Supprimer les espaces en début/fin
    normalized = normalized.strip()

    return normalized

# Fonctions utilitaires pour la résolution d'équations
def parse_equation(equation_text):
    """Parse une équation et retourne les expressions sympy"""
    try:
        # Nettoyer l'équation
        equation_text = equation_text.replace(' ', '').replace('=', '-(') + ')'

        # Remplacer les notations communes
        equation_text = equation_text.replace('^', '**')
        equation_text = equation_text.replace('√', 'sqrt')

        # Définir les variables communes
        x, y, z = symbols('x y z')

        # Parser l'expression
        expr = sp.sympify(equation_text)

        return expr, [x, y, z]
    except Exception as e:
        raise ValueError(f"Impossible de parser l'équation: {str(e)}")

def solve_equation_steps(equation_text):
    """Résout une équation et retourne les étapes détaillées"""
    try:
        # Parser l'équation
        if '=' not in equation_text:
            raise ValueError("L'équation doit contenir le signe '='")

        left, right = equation_text.split('=', 1)

        # Nettoyer et préparer
        left = left.strip().replace('^', '**').replace(' ', '')
        right = right.strip().replace('^', '**').replace(' ', '')

        # Gérer les multiplications implicites (2x -> 2*x)
        import re
        left = re.sub(r'(\d)([a-zA-Z])', r'\1*\2', left)
        right = re.sub(r'(\d)([a-zA-Z])', r'\1*\2', right)

        # Définir les variables
        x = symbols('x')

        # Créer l'équation
        left_expr = sp.sympify(left)
        right_expr = sp.sympify(right)
        equation = sp.Eq(left_expr, right_expr)

        # Résoudre
        solutions = solve(equation, x)

        # Générer les étapes détaillées
        steps = generate_detailed_steps(equation_text, left_expr, right_expr, solutions, x)

        if len(solutions) == 0:
            return "Aucune solution", steps
        elif len(solutions) == 1:
            sol = solutions[0]
            if sol.is_real:
                return f"x = {sol}", steps
            else:
                return f"x = {sol}", steps
        else:
            sol_text = ", ".join([f"x = {sol}" for sol in solutions])
            return sol_text, steps

    except Exception as e:
        return f"Erreur: {str(e)}", [f"Impossible de résoudre: {str(e)}"]

def generate_detailed_steps(original_equation, left_expr, right_expr, solutions, variable):
    """Génère des étapes détaillées et pédagogiques pour la résolution"""
    steps = []

    # Étape 1: Équation de départ
    steps.append(f"📝 Équation de départ: {original_equation}")

    # Étape 2: Identification du type d'équation
    equation_type = identify_equation_type(left_expr, right_expr, variable)
    steps.append(f"🔍 Type d'équation: {equation_type}")

    # Étape 3: Forme standard
    steps.append(f"📐 Forme standard: {left_expr} = {right_expr}")

    # Étapes spécifiques selon le type d'équation
    if "linéaire" in equation_type.lower():
        steps.extend(solve_linear_steps(left_expr, right_expr, variable))
    elif "quadratique" in equation_type.lower() or "second degré" in equation_type.lower():
        steps.extend(solve_quadratic_steps(left_expr, right_expr, variable))
    elif "rationnelle" in equation_type.lower():
        steps.extend(solve_rational_steps(left_expr, right_expr, variable))
    else:
        steps.extend(solve_general_steps(left_expr, right_expr, variable))

    # Étapes finales: Solutions
    if len(solutions) == 0:
        steps.append("❌ Aucune solution réelle trouvée")
        steps.append("💡 Cette équation n'a pas de solution dans les nombres réels")
    elif len(solutions) == 1:
        sol = solutions[0]
        steps.append(f"✅ Solution unique: x = {sol}")

        # Vérification
        verification = verify_solution(original_equation, sol)
        steps.append(f"🔍 Vérification: {verification}")

        # Valeur numérique si possible
        try:
            if sol.is_real and sol.is_number:
                numeric_value = float(sol)
                steps.append(f"🔢 Valeur numérique: x ≈ {numeric_value:.4f}")
        except:
            pass

    else:
        steps.append(f"✅ Solutions multiples: {len(solutions)} solutions trouvées")
        for i, sol in enumerate(solutions, 1):
            steps.append(f"   Solution {i}: x = {sol}")

            # Valeur numérique pour chaque solution
            try:
                if sol.is_real and sol.is_number:
                    numeric_value = float(sol)
                    steps.append(f"      Valeur numérique: x ≈ {numeric_value:.4f}")
            except:
                pass

    # Conseils pédagogiques
    steps.extend(get_pedagogical_tips(equation_type, solutions))

    return steps

def identify_equation_type(left_expr, right_expr, variable):
    """Identifie le type d'équation"""
    # Combiner les deux côtés pour analyser
    combined = left_expr - right_expr

    # Obtenir le degré de l'équation
    degree = sp.degree(combined, variable)

    # Vérifier si c'est une équation rationnelle
    if any(arg.has(variable) for arg in combined.atoms(sp.Pow) if arg.exp < 0):
        return "Équation rationnelle (avec fractions)"

    # Vérifier les fonctions trigonométriques
    if any(func in str(combined) for func in ['sin', 'cos', 'tan']):
        return "Équation trigonométrique"

    # Vérifier les fonctions exponentielles/logarithmiques
    if any(func in str(combined) for func in ['exp', 'log', 'ln']):
        return "Équation exponentielle/logarithmique"

    # Classification par degré
    if degree == 1:
        return "Équation linéaire (1er degré)"
    elif degree == 2:
        return "Équation quadratique (2nd degré)"
    elif degree == 3:
        return "Équation cubique (3ème degré)"
    elif degree > 3:
        return f"Équation polynomiale de degré {degree}"
    else:
        return "Équation générale"

def solve_linear_steps(left_expr, right_expr, variable):
    """Étapes spécifiques pour les équations linéaires"""
    steps = []

    # Déplacer tous les termes à gauche
    combined = left_expr - right_expr
    steps.append(f"🔄 Déplacer tous les termes à gauche: {combined} = 0")

    # Développer et simplifier
    expanded = sp.expand(combined)
    if expanded != combined:
        steps.append(f"📖 Développement: {expanded} = 0")

    # Collecter les termes
    collected = sp.collect(expanded, variable)
    if collected != expanded:
        steps.append(f"📊 Regroupement des termes: {collected} = 0")

    # Extraire les coefficients
    coeffs = sp.Poly(collected, variable).all_coeffs()
    if len(coeffs) == 2:  # ax + b = 0
        a, b = coeffs
        steps.append(f"📋 Forme ax + b = 0 avec a = {a}, b = {b}")
        steps.append(f"🧮 Résolution: x = -b/a = -({b})/({a})")

    return steps

def solve_quadratic_steps(left_expr, right_expr, variable):
    """Étapes spécifiques pour les équations quadratiques"""
    steps = []

    # Déplacer tous les termes à gauche
    combined = left_expr - right_expr
    steps.append(f"🔄 Forme standard: {combined} = 0")

    # Développer
    expanded = sp.expand(combined)
    if expanded != combined:
        steps.append(f"📖 Développement: {expanded} = 0")

    # Extraire les coefficients a, b, c
    try:
        coeffs = sp.Poly(expanded, variable).all_coeffs()
        if len(coeffs) == 3:  # ax² + bx + c = 0
            a, b, c = coeffs
            steps.append(f"📋 Forme ax² + bx + c = 0 avec:")
            steps.append(f"   • a = {a}")
            steps.append(f"   • b = {b}")
            steps.append(f"   • c = {c}")

            # Calculer le discriminant
            discriminant = b**2 - 4*a*c
            steps.append(f"🔺 Discriminant Δ = b² - 4ac = ({b})² - 4×({a})×({c}) = {discriminant}")

            # Analyser le discriminant
            if discriminant > 0:
                steps.append("✅ Δ > 0 : deux solutions réelles distinctes")
                steps.append(f"🧮 Solutions: x = (-b ± √Δ) / (2a)")
            elif discriminant == 0:
                steps.append("⚖️ Δ = 0 : une solution réelle double")
                steps.append(f"🧮 Solution: x = -b / (2a)")
            else:
                steps.append("❌ Δ < 0 : pas de solution réelle")
                steps.append("💡 Les solutions sont complexes")
    except:
        steps.append("🔍 Analyse des coefficients impossible")

    return steps

def solve_rational_steps(left_expr, right_expr, variable):
    """Étapes spécifiques pour les équations rationnelles"""
    steps = []

    steps.append("🔍 Équation avec fractions détectée")
    steps.append("⚠️ Attention aux valeurs interdites (dénominateurs = 0)")

    # Trouver le dénominateur commun
    steps.append("🔄 Recherche du dénominateur commun...")

    # Multiplier par le dénominateur commun
    steps.append("✖️ Multiplication par le dénominateur commun")

    return steps

def solve_general_steps(left_expr, right_expr, variable):
    """Étapes générales pour les autres types d'équations"""
    steps = []

    # Déplacer tous les termes à gauche
    combined = left_expr - right_expr
    steps.append(f"🔄 Réorganisation: {combined} = 0")

    # Simplification
    simplified = sp.simplify(combined)
    if simplified != combined:
        steps.append(f"🧹 Simplification: {simplified} = 0")

    # Factorisation si possible
    try:
        factored = sp.factor(simplified)
        if factored != simplified:
            steps.append(f"🔧 Factorisation: {factored} = 0")
    except:
        pass

    return steps

def verify_solution(original_equation, solution):
    """Vérifie qu'une solution est correcte"""
    try:
        left, right = original_equation.split('=')
        left = left.strip().replace('^', '**')
        right = right.strip().replace('^', '**')

        # Remplacer x par la solution
        left_value = left.replace('x', f'({solution})')
        right_value = right.replace('x', f'({solution})')

        # Évaluer
        left_result = sp.sympify(left_value)
        right_result = sp.sympify(right_value)

        if sp.simplify(left_result - right_result) == 0:
            return f"✅ {left_value} = {right_value} ✓"
        else:
            return f"❌ {left_value} ≠ {right_value}"
    except:
        return "Vérification impossible"

def get_pedagogical_tips(equation_type, solutions):
    """Fournit des conseils pédagogiques selon le type d'équation"""
    tips = []

    if "linéaire" in equation_type.lower():
        tips.append("💡 Conseil: Les équations linéaires ont toujours une solution unique")
        tips.append("📚 Méthode: Isoler x en effectuant les mêmes opérations des deux côtés")

    elif "quadratique" in equation_type.lower():
        tips.append("💡 Conseil: Utilisez la formule quadratique ou la factorisation")
        tips.append("📚 Méthode: ax² + bx + c = 0 → x = (-b ± √(b²-4ac)) / 2a")

        if len(solutions) == 2:
            tips.append("🎯 Astuce: Vérifiez vos solutions en les substituant dans l'équation originale")

    elif "rationnelle" in equation_type.lower():
        tips.append("⚠️ Important: Vérifiez que les solutions ne rendent pas les dénominateurs nuls")
        tips.append("📚 Méthode: Multipliez par le dénominateur commun pour éliminer les fractions")

    # Conseils généraux
    tips.append("🔍 Toujours vérifier: Substituez votre solution dans l'équation originale")

    return tips

# Vues pour les utilisateurs
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action == 'create':
            # Permettre la création d'utilisateurs sans authentification (inscription)
            permission_classes = [AllowAny]
        else:
            # Toutes les autres actions nécessitent une authentification
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

# Vues pour les cours
class CourseViewSet(viewsets.ModelViewSet):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [IsAuthenticated]

# Vues pour les équations
class EquationViewSet(viewsets.ModelViewSet):
    queryset = Equation.objects.all()
    serializer_class = EquationSerializer
    permission_classes = [IsAuthenticated]

# Vues pour les exercices
class ExerciseViewSet(viewsets.ModelViewSet):
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [IsAuthenticated]

# Vues simples
@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    return Response({"status": "ok"})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def current_user(request):
    serializer = UserSerializer(request.user)
    return Response(serializer.data)

@api_view(['GET'])
def test_api(request):
    return Response({
        "message": "L'API fonctionne correctement!",
        "status": "ok"
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def solve_equation(request):
    """Résoudre une équation mathématique"""
    try:
        equation_text = request.data.get('equation', '').strip()

        if not equation_text:
            return Response({
                'error': 'Veuillez fournir une équation'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Résoudre l'équation
        solution, steps = solve_equation_steps(equation_text)

        # Sauvegarder dans la base de données
        equation_obj = Equation.objects.create(
            equation_text=equation_text,
            solution_text=solution,
            user=request.user,
            validated=True
        )

        return Response({
            'equation': equation_text,
            'solution': solution,
            'steps': steps,
            'equation_id': equation_obj.id
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la résolution: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def preprocess_image_for_ocr(image):
    """Préprocesser l'image pour améliorer la reconnaissance OCR"""
    try:
        import cv2
        import numpy as np
        from PIL import Image

        # Convertir PIL Image en array numpy
        img_array = np.array(image)

        # Convertir en niveaux de gris si nécessaire
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # Améliorer le contraste
        gray = cv2.equalizeHist(gray)

        # Appliquer un filtre gaussien pour réduire le bruit
        gray = cv2.GaussianBlur(gray, (3, 3), 0)

        # Binarisation adaptative pour améliorer la lisibilité
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # Morphologie pour nettoyer l'image
        kernel = np.ones((2, 2), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # Reconvertir en PIL Image
        processed_image = Image.fromarray(binary)

        return processed_image

    except Exception as e:
        print(f"Erreur de préprocessing: {e}")
        return image  # Retourner l'image originale en cas d'erreur

def clean_ocr_text(text):
    """Nettoyer et corriger le texte OCR pour les équations mathématiques"""
    import re

    if not text:
        return ""

    # Supprimer les espaces multiples et les caractères indésirables
    cleaned = re.sub(r'\s+', ' ', text.strip())

    # Corrections communes d'OCR pour les mathématiques
    corrections = {
        # Corrections de caractères numériques
        'O': '0',  # O majuscule -> 0
        'o': '0',  # o minuscule -> 0 (dans certains contextes)
        'l': '1',  # l minuscule -> 1 (dans certains contextes)
        'I': '1',  # I majuscule -> 1
        'S': '5',  # S -> 5 (parfois)
        '§': '5',  # Symbole section -> 5
        'B': '8',  # B -> 8 (parfois)

        # Corrections de variables (problèmes courants avec x)
        'X': 'x',  # X majuscule -> x minuscule
        'xX': 'x', # xX -> x
        'Xx': 'x', # Xx -> x
        'XX': 'x', # XX -> x
        'xXx': 'x', # xXx -> x (erreur courante)

        # Corrections d'opérateurs
        '×': '*',  # Multiplication
        '÷': '/',  # Division
        '−': '-',  # Moins unicode
        '–': '-',  # Tiret en dash
        '—': '-',  # Tiret em

        # Corrections d'égalité
        '＝': '=',  # Égal pleine largeur
        '≡': '=',  # Identique

        # Corrections de puissances
        '²': '^2',  # Carré
        '³': '^3',  # Cube

        # Supprimer les caractères parasites
        '|': '',   # Barres verticales parasites
        '_': '',   # Underscores parasites
        '`': '',   # Backticks
        '"': '',   # Guillemets
        "'": '',   # Apostrophes
    }

    # Appliquer les corrections
    for old, new in corrections.items():
        cleaned = cleaned.replace(old, new)

    # Corrections contextuelles avec regex
    # Corriger les répétitions de variables (xx -> x, xxx -> x)
    cleaned = re.sub(r'x{2,}', 'x', cleaned)

    # Corriger les espaces autour des opérateurs
    cleaned = re.sub(r'\s*([+\-*/=^])\s*', r'\1', cleaned)

    # Corriger les multiplications implicites (2x -> 2*x)
    cleaned = re.sub(r'(\d)([a-zA-Z])', r'\1*\2', cleaned)

    # Corriger les puissances mal reconnues
    cleaned = re.sub(r'([a-zA-Z\d])\s*\^\s*(\d)', r'\1^\2', cleaned)

    # Supprimer les caractères non mathématiques
    cleaned = re.sub(r'[^\w\d+\-*/=^().,\s]', '', cleaned)

    # Supprimer les espaces en début/fin
    cleaned = cleaned.strip()

    return cleaned

def suggest_equations_from_ocr(ocr_text):
    """Suggérer des équations probables basées sur le texte OCR défaillant"""

    # Analyser le texte OCR pour détecter des patterns
    suggestions = []

    # Si le texte contient des chiffres et lettres mélangés
    if ocr_text and len(ocr_text) > 2:
        # Extraire les chiffres du texte OCR
        import re
        numbers = re.findall(r'\d+', ocr_text)
        letters = re.findall(r'[a-zA-Z]+', ocr_text)

        # Générer des suggestions basées sur les chiffres trouvés
        if numbers:
            for num in numbers:
                if len(num) <= 2:  # Éviter les très grands nombres
                    # Équations linéaires simples
                    suggestions.extend([
                        f"x + {num} = {int(num) + 5}",
                        f"x - {num} = {int(num) + 2}",
                        f"{num}x = {int(num) * 3}",
                        f"x + {int(num) + 1} = {int(num) + 6}",
                        f"2x + {num} = {int(num) + 8}"
                    ])

        # Si on détecte des patterns spécifiques
        if '3' in ocr_text and '5' in ocr_text:
            suggestions.extend([
                "x + 3 = 5",  # Équation la plus probable
                "x - 3 = 5",
                "3x = 5",
                "x + 5 = 3",
                "3 + x = 5",
                "x = 5 - 3",
                "x = 3 + 5"
            ])

        # Patterns avec d'autres combinaisons courantes
        if '2' in ocr_text and '5' in ocr_text:
            suggestions.extend([
                "x + 2 = 5",
                "2x = 5",
                "x - 2 = 5",
                "x + 5 = 2"
            ])

        if '4' in ocr_text:
            suggestions.extend([
                "x + 4 = 8",
                "x - 4 = 0",
                "x^2 = 4"
            ])

        if '2' in ocr_text and '5' in ocr_text:
            suggestions.extend([
                "x + 2 = 5",
                "2x = 5",
                "x - 2 = 5"
            ])

    # Équations courantes par défaut
    default_suggestions = [
        "x + 3 = 7",
        "2x + 1 = 9",
        "x - 4 = 6",
        "3x = 15",
        "x + 5 = 12",
        "x^2 = 16",
        "2x - 3 = 11"
    ]

    # Combiner et limiter les suggestions
    all_suggestions = suggestions + default_suggestions
    # Supprimer les doublons et limiter à 5 suggestions
    unique_suggestions = list(dict.fromkeys(all_suggestions))[:5]

    return unique_suggestions

def extract_equation_from_text(text):
    """Extraire la meilleure équation possible du texte OCR"""
    import re

    if not text:
        return None

    # Chercher des patterns d'équations
    equation_patterns = [
        r'[a-zA-Z\d+\-*/^().\s]*=[a-zA-Z\d+\-*/^().\s]*',  # Pattern général avec =
        r'\d*[a-zA-Z][+\-*/^]*\d*\s*=\s*\d+',  # Pattern simple: ax + b = c
        r'[a-zA-Z]\^?\d*[+\-*/]*\d*\s*=\s*\d+',  # Pattern avec puissances
    ]

    for pattern in equation_patterns:
        matches = re.findall(pattern, text)
        if matches:
            # Prendre la correspondance la plus longue
            best_match = max(matches, key=len)
            return best_match.strip()

    # Si aucun pattern trouvé, retourner None pour déclencher les suggestions
    return None

@api_view(['POST'])
@permission_classes([AllowAny])  # Temporairement sans authentification pour les tests
def recognize_equation_from_image_test(request):
    """Version de test de la reconnaissance d'équation (sans authentification)"""
    try:
        # Vérifier qu'une image a été fournie
        if 'image' not in request.FILES:
            return Response({
                'error': 'Aucune image fournie. Veuillez uploader un fichier image.'
            }, status=status.HTTP_400_BAD_REQUEST)

        image_file = request.FILES['image']

        # Vérifier le type de fichier
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        if image_file.content_type not in allowed_types:
            return Response({
                'error': 'Type de fichier non supporté. Utilisez PNG, JPG ou GIF.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier la taille (max 5MB)
        if image_file.size > 5 * 1024 * 1024:
            return Response({
                'error': 'Fichier trop volumineux. Taille maximale : 5MB.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vraie reconnaissance OCR avec Tesseract
        try:
            from PIL import Image
            import io

            # Vérifier que pytesseract est disponible
            if pytesseract is None:
                return Response({
                    'error': 'Tesseract OCR non disponible. Veuillez installer pytesseract.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Charger l'image
            image = Image.open(image_file)

            # Préprocesser l'image pour améliorer l'OCR
            processed_image = preprocess_image_for_ocr(image)

            # Configuration Tesseract pour les mathématiques
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '

            # Reconnaissance OCR avec l'image préprocessée
            raw_text = pytesseract.image_to_string(processed_image, config=custom_config)
            print(f"OCR Raw text (processed): '{raw_text}'")  # Debug

            # Nettoyer le texte OCR
            cleaned_text = clean_ocr_text(raw_text)
            print(f"OCR Cleaned text: '{cleaned_text}'")  # Debug

            # Extraire l'équation
            recognized_equation = extract_equation_from_text(cleaned_text)
            print(f"OCR Extracted equation: '{recognized_equation}'")  # Debug

            # Si la reconnaissance échoue, essayer avec l'image originale
            if not recognized_equation or len(recognized_equation.strip()) < 3:
                print("Trying with original image...")  # Debug
                raw_text_original = pytesseract.image_to_string(image, config=custom_config)
                print(f"OCR Raw text (original): '{raw_text_original}'")  # Debug
                cleaned_original = clean_ocr_text(raw_text_original)
                recognized_equation = extract_equation_from_text(cleaned_original)

            # Si toujours pas de résultat, essayer avec une configuration plus permissive
            if not recognized_equation or len(recognized_equation.strip()) < 3:
                print("Trying with permissive config...")  # Debug
                permissive_config = r'--oem 3 --psm 8'
                raw_text_permissive = pytesseract.image_to_string(image, config=permissive_config)
                print(f"OCR Raw text (permissive): '{raw_text_permissive}'")  # Debug
                cleaned_permissive = clean_ocr_text(raw_text_permissive)
                recognized_equation = extract_equation_from_text(cleaned_permissive)

            # Calculer la confiance et gérer les échecs de reconnaissance
            if recognized_equation and '=' in recognized_equation and len(recognized_equation) > 5:
                confidence = 0.95
                message = "Équation reconnue avec succès"
                suggestions = []
            elif recognized_equation and len(recognized_equation) > 3:
                confidence = 0.70
                message = "Équation reconnue partiellement"
                suggestions = suggest_equations_from_ocr(raw_text)
            else:
                # Reconnaissance échouée - proposer des suggestions intelligentes
                suggestions = suggest_equations_from_ocr(raw_text)
                recognized_equation = suggestions[0] if suggestions else "x + 3 = 7"
                confidence = 0.25
                message = f"Reconnaissance difficile. Texte OCR: '{cleaned_text}'. Suggestions proposées."

        except Exception as ocr_error:
            print(f"OCR Error: {ocr_error}")  # Debug
            # Erreur OCR, utiliser une équation par défaut
            recognized_equation = "2x + 3 = 11"
            confidence = 0.25
            message = f"Erreur OCR: {str(ocr_error)}. Équation d'exemple fournie."
            suggestions = []

        # Préparer la réponse
        response_data = {
            'recognized_text': recognized_equation,
            'confidence': round(confidence, 2),
            'message': message,
            'ocr_raw_text': cleaned_text,  # Texte OCR brut pour debug
            'test_mode': True,  # Indiquer que c'est un test
            'tips': [
                'Vérifiez que l\'équation reconnue est correcte',
                'Vous pouvez modifier l\'équation avant de la résoudre',
                'Pour une meilleure reconnaissance: écriture claire, fond contrasté, bonne résolution'
            ]
        }

        # Ajouter les suggestions si la reconnaissance a échoué
        if 'suggestions' in locals() and suggestions:
            response_data['suggestions'] = suggestions
            response_data['tips'].append('Sélectionnez une équation suggérée si la reconnaissance est incorrecte')

        return Response(response_data)

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la reconnaissance: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def recognize_equation_from_image(request):
    """Reconnaissance d'équation à partir d'une image uploadée avec vraie OCR"""
    try:
        # Vérifier qu'une image a été fournie
        if 'image' not in request.FILES:
            return Response({
                'error': 'Aucune image fournie. Veuillez uploader un fichier image.'
            }, status=status.HTTP_400_BAD_REQUEST)

        image_file = request.FILES['image']

        # Vérifier le type de fichier
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        if image_file.content_type not in allowed_types:
            return Response({
                'error': 'Type de fichier non supporté. Utilisez PNG, JPG ou GIF.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier la taille (max 5MB)
        if image_file.size > 5 * 1024 * 1024:
            return Response({
                'error': 'Fichier trop volumineux. Taille maximale : 5MB.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vraie reconnaissance OCR avec Tesseract
        try:
            from PIL import Image
            import io

            # Vérifier que pytesseract est disponible
            if pytesseract is None:
                return Response({
                    'error': 'Tesseract OCR non disponible. Veuillez installer pytesseract.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Charger l'image
            image = Image.open(image_file)

            # Préprocesser l'image pour améliorer l'OCR
            processed_image = preprocess_image_for_ocr(image)

            # Configuration Tesseract pour les mathématiques
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+-*/=^()., '

            # Reconnaissance OCR avec l'image préprocessée
            raw_text = pytesseract.image_to_string(processed_image, config=custom_config)
            print(f"OCR Raw text (processed): '{raw_text}'")  # Debug

            # Nettoyer le texte OCR
            cleaned_text = clean_ocr_text(raw_text)
            print(f"OCR Cleaned text: '{cleaned_text}'")  # Debug

            # Extraire l'équation
            recognized_equation = extract_equation_from_text(cleaned_text)
            print(f"OCR Extracted equation: '{recognized_equation}'")  # Debug

            # Si la reconnaissance échoue, essayer avec l'image originale
            if not recognized_equation or len(recognized_equation.strip()) < 3:
                print("Trying with original image...")  # Debug
                raw_text_original = pytesseract.image_to_string(image, config=custom_config)
                print(f"OCR Raw text (original): '{raw_text_original}'")  # Debug
                cleaned_original = clean_ocr_text(raw_text_original)
                recognized_equation = extract_equation_from_text(cleaned_original)

            # Si toujours pas de résultat, essayer avec une configuration plus permissive
            if not recognized_equation or len(recognized_equation.strip()) < 3:
                print("Trying with permissive config...")  # Debug
                permissive_config = r'--oem 3 --psm 8'
                raw_text_permissive = pytesseract.image_to_string(image, config=permissive_config)
                print(f"OCR Raw text (permissive): '{raw_text_permissive}'")  # Debug
                cleaned_permissive = clean_ocr_text(raw_text_permissive)
                recognized_equation = extract_equation_from_text(cleaned_permissive)

            # Calculer la confiance et gérer les échecs de reconnaissance
            if recognized_equation and '=' in recognized_equation and len(recognized_equation) > 5:
                confidence = 0.95
                message = "Équation reconnue avec succès"
                suggestions = []
            elif recognized_equation and len(recognized_equation) > 3:
                confidence = 0.70
                message = "Équation reconnue partiellement"
                suggestions = suggest_equations_from_ocr(raw_text)
            else:
                # Reconnaissance échouée - proposer des suggestions intelligentes
                suggestions = suggest_equations_from_ocr(raw_text)
                recognized_equation = suggestions[0] if suggestions else "x + 3 = 7"
                confidence = 0.25
                message = f"Reconnaissance difficile. Texte OCR: '{cleaned_text}'. Suggestions proposées."

        except Exception as ocr_error:
            print(f"OCR Error: {ocr_error}")  # Debug
            # Erreur OCR, utiliser une équation par défaut
            recognized_equation = "2x + 3 = 11"
            confidence = 0.25
            message = f"Erreur OCR: {str(ocr_error)}. Équation d'exemple fournie."

        # Sauvegarder l'image et la reconnaissance (optionnel)
        try:
            equation_image = EquationImage.objects.create(
                image=image_file,
                recognized_text=recognized_equation,
                confidence_score=confidence,
                user=request.user
            )
            image_id = equation_image.id
        except Exception:
            # Si le modèle EquationImage n'est pas configuré, continuer sans sauvegarder
            image_id = None

        # Préparer la réponse
        response_data = {
            'recognized_text': recognized_equation,
            'confidence': round(confidence, 2),
            'message': message,
            'image_id': image_id,
            'ocr_raw_text': cleaned_text,  # Texte OCR brut pour debug
            'tips': [
                'Vérifiez que l\'équation reconnue est correcte',
                'Vous pouvez modifier l\'équation avant de la résoudre',
                'Pour une meilleure reconnaissance: écriture claire, fond contrasté, bonne résolution'
            ]
        }

        # Ajouter les suggestions si la reconnaissance a échoué
        if 'suggestions' in locals() and suggestions:
            response_data['suggestions'] = suggestions
            response_data['tips'].append('Sélectionnez une équation suggérée si la reconnaissance est incorrecte')

        return Response(response_data)

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la reconnaissance: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_course_recommendations(request):
    """Obtenir des recommandations de cours personnalisées"""
    try:
        user = request.user

        # Analyser l'historique de l'utilisateur
        user_equations = Equation.objects.filter(user=user)
        total_equations = user_equations.count()

        # Analyser les types d'équations résolues
        linear_count = 0
        quadratic_count = 0
        complex_count = 0

        for eq in user_equations:
            eq_text = eq.equation_text.lower()
            if 'x^2' in eq_text or 'x**2' in eq_text:
                quadratic_count += 1
            elif any(op in eq_text for op in ['sin', 'cos', 'tan', 'log', 'exp']):
                complex_count += 1
            else:
                linear_count += 1

        # Déterminer le niveau de l'utilisateur
        if total_equations < 5:
            user_level = 'beginner'
        elif quadratic_count > 2 or complex_count > 0:
            user_level = 'advanced'
        else:
            user_level = 'intermediate'

        # Obtenir tous les cours
        all_courses = Course.objects.all()

        # Logique de recommandation
        recommendations = []

        # Recommandations basées sur le niveau
        if user_level == 'beginner':
            # Recommander les cours de base
            recommended_courses = all_courses.filter(level='beginner')
            reason = "Parfait pour débuter en mathématiques"
        elif user_level == 'intermediate':
            # Recommander les cours intermédiaires et quelques avancés
            recommended_courses = all_courses.filter(level__in=['intermediate', 'advanced'])
            reason = "Pour approfondir vos connaissances"
        else:
            # Recommander les cours avancés
            recommended_courses = all_courses.filter(level='advanced')
            reason = "Défis mathématiques avancés"

        # Ajouter des recommandations spécifiques basées sur l'historique
        for course in recommended_courses[:3]:  # Limiter à 3 recommandations principales
            # Calculer un score de pertinence
            relevance_score = 0.8

            # Ajuster le score selon le type d'équations résolues
            if course.title.lower().find('algèbre') != -1 and linear_count > 0:
                relevance_score = 0.9
            elif course.title.lower().find('second degré') != -1 and quadratic_count > 0:
                relevance_score = 0.95
            elif course.title.lower().find('système') != -1 and total_equations > 3:
                relevance_score = 0.85

            recommendations.append({
                'course': CourseSerializer(course).data,
                'reason': reason,
                'relevance_score': relevance_score,
                'difficulty_match': course.level == user_level,
                'estimated_duration': f"{3 + (hash(course.title) % 5)}h",  # Simulation
                'prerequisites_met': True if user_level != 'beginner' else course.level == 'beginner'
            })

        # Ajouter des recommandations basées sur les lacunes détectées
        if linear_count == 0 and total_equations > 0:
            # L'utilisateur n'a pas fait d'équations linéaires
            basic_course = all_courses.filter(level='beginner').first()
            if basic_course:
                recommendations.append({
                    'course': CourseSerializer(basic_course).data,
                    'reason': "Renforcer les bases en algèbre",
                    'relevance_score': 0.7,
                    'difficulty_match': False,
                    'estimated_duration': "2h",
                    'prerequisites_met': True,
                    'is_foundation': True
                })

        # Trier par score de pertinence
        recommendations.sort(key=lambda x: x['relevance_score'], reverse=True)

        return Response({
            'user_level': user_level,
            'total_equations_solved': total_equations,
            'analysis': {
                'linear_equations': linear_count,
                'quadratic_equations': quadratic_count,
                'complex_equations': complex_count
            },
            'recommendations': recommendations[:4],  # Limiter à 4 recommandations
            'next_milestone': {
                'target': 'Résoudre 10 équations' if total_equations < 10 else 'Maîtriser les équations du 2nd degré',
                'progress': min(total_equations / 10, 1.0) if total_equations < 10 else quadratic_count / 5,
                'description': 'Continuez à pratiquer pour débloquer de nouveaux cours !'
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la génération des recommandations: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_learning_path(request):
    """Générer un parcours d'apprentissage personnalisé"""
    try:
        user = request.user
        user_equations = Equation.objects.filter(user=user)
        total_equations = user_equations.count()

        # Analyser les forces et faiblesses
        strengths = []
        weaknesses = []

        linear_count = sum(1 for eq in user_equations if not any(x in eq.equation_text.lower() for x in ['x^2', 'x**2', 'sin', 'cos', 'log']))
        quadratic_count = sum(1 for eq in user_equations if any(x in eq.equation_text.lower() for x in ['x^2', 'x**2']))

        if linear_count >= 5:
            strengths.append("Équations linéaires")
        elif linear_count < 3 and total_equations > 0:
            weaknesses.append("Équations linéaires")

        if quadratic_count >= 3:
            strengths.append("Équations quadratiques")
        elif quadratic_count < 2 and total_equations > 5:
            weaknesses.append("Équations quadratiques")

        # Générer le parcours d'apprentissage
        learning_path = []
        all_courses = Course.objects.all().order_by('level', 'title')

        # Étape 1: Renforcer les bases si nécessaire
        if "Équations linéaires" in weaknesses:
            basic_courses = all_courses.filter(level='beginner')
            for course in basic_courses[:2]:
                learning_path.append({
                    'step': len(learning_path) + 1,
                    'course': CourseSerializer(course).data,
                    'type': 'foundation',
                    'reason': 'Renforcer les bases',
                    'estimated_weeks': 2,
                    'priority': 'high'
                })

        # Étape 2: Cours adaptés au niveau actuel
        if total_equations < 5:
            current_level = 'beginner'
        elif quadratic_count > 2:
            current_level = 'advanced'
        else:
            current_level = 'intermediate'

        level_courses = all_courses.filter(level=current_level)
        for course in level_courses[:3]:
            learning_path.append({
                'step': len(learning_path) + 1,
                'course': CourseSerializer(course).data,
                'type': 'current_level',
                'reason': f'Adapté à votre niveau {current_level}',
                'estimated_weeks': 3,
                'priority': 'medium'
            })

        # Étape 3: Défis pour progresser
        if current_level != 'advanced':
            next_level = 'intermediate' if current_level == 'beginner' else 'advanced'
            challenge_courses = all_courses.filter(level=next_level)
            for course in challenge_courses[:2]:
                learning_path.append({
                    'step': len(learning_path) + 1,
                    'course': CourseSerializer(course).data,
                    'type': 'challenge',
                    'reason': f'Défi pour atteindre le niveau {next_level}',
                    'estimated_weeks': 4,
                    'priority': 'low'
                })

        return Response({
            'user_level': current_level,
            'strengths': strengths,
            'weaknesses': weaknesses,
            'learning_path': learning_path,
            'total_estimated_weeks': sum(step['estimated_weeks'] for step in learning_path),
            'completion_rate': min(total_equations / 20, 1.0) * 100  # Sur 20 équations pour 100%
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la génération du parcours: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_daily_challenge(request):
    """Générer un défi quotidien personnalisé"""
    try:
        user = request.user
        user_equations = Equation.objects.filter(user=user)
        total_equations = user_equations.count()

        # Déterminer le niveau de difficulté du défi
        if total_equations < 3:
            difficulty = 'easy'
            challenge_equations = [
                "x + 7 = 15",
                "2x = 14",
                "x - 5 = 8"
            ]
        elif total_equations < 10:
            difficulty = 'medium'
            challenge_equations = [
                "3x + 4 = 2x + 9",
                "5x - 7 = 2x + 8",
                "x/3 + 2 = 5"
            ]
        else:
            difficulty = 'hard'
            challenge_equations = [
                "x^2 - 5x + 6 = 0",
                "2x^2 + 3x - 2 = 0",
                "x^2 + 4x + 4 = 0"
            ]

        # Sélectionner un défi aléatoire
        import random
        selected_equation = random.choice(challenge_equations)

        # Calculer les points potentiels
        points = {'easy': 10, 'medium': 20, 'hard': 30}[difficulty]

        # Vérifier si l'utilisateur a déjà résolu ce défi aujourd'hui
        from datetime import date
        today_equations = user_equations.filter(created_at__date=date.today())
        already_solved = any(eq.equation_text == selected_equation for eq in today_equations)

        return Response({
            'equation': selected_equation,
            'difficulty': difficulty,
            'points': points,
            'already_solved': already_solved,
            'description': f"Défi {difficulty} du jour",
            'hint': "Isolez la variable x étape par étape" if difficulty == 'easy' else
                   "Regroupez les termes similaires" if difficulty == 'medium' else
                   "Utilisez la formule quadratique si nécessaire",
            'bonus_info': {
                'streak_bonus': 5 if total_equations >= 3 else 0,
                'first_try_bonus': 10,
                'total_possible_points': points + (5 if total_equations >= 3 else 0) + 10
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la génération du défi: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_study_insights(request):
    """Générer des insights sur les habitudes d'étude"""
    try:
        user = request.user
        user_equations = Equation.objects.filter(user=user)

        # Analyser les patterns temporels
        from datetime import datetime, timedelta
        from collections import defaultdict

        # Activité par jour de la semaine
        weekday_activity = defaultdict(int)
        hour_activity = defaultdict(int)

        for eq in user_equations:
            weekday = eq.created_at.weekday()  # 0 = Lundi
            hour = eq.created_at.hour
            weekday_activity[weekday] += 1
            hour_activity[hour] += 1

        # Trouver le meilleur moment d'étude
        best_weekday = max(weekday_activity.items(), key=lambda x: x[1])[0] if weekday_activity else 0
        best_hour = max(hour_activity.items(), key=lambda x: x[1])[0] if hour_activity else 14

        weekdays = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']

        # Analyser la progression
        last_week = user_equations.filter(created_at__gte=datetime.now() - timedelta(days=7))
        this_week_count = last_week.count()

        # Calculer la régularité
        days_with_activity = len(set(eq.created_at.date() for eq in last_week))
        consistency_score = (days_with_activity / 7) * 100

        # Générer des insights personnalisés
        insights = []

        if this_week_count > 5:
            insights.append({
                'type': 'positive',
                'title': 'Excellente activité !',
                'message': f'Vous avez résolu {this_week_count} équations cette semaine.',
                'icon': '🔥'
            })
        elif this_week_count > 0:
            insights.append({
                'type': 'encouraging',
                'title': 'Bon début !',
                'message': f'Continuez sur cette lancée avec {this_week_count} équations résolues.',
                'icon': '👍'
            })
        else:
            insights.append({
                'type': 'motivational',
                'title': 'Prêt à commencer ?',
                'message': 'Résolvez votre première équation de la semaine !',
                'icon': '🚀'
            })

        if consistency_score >= 70:
            insights.append({
                'type': 'positive',
                'title': 'Très régulier !',
                'message': f'Vous étudiez {days_with_activity} jours sur 7.',
                'icon': '📅'
            })
        elif consistency_score >= 40:
            insights.append({
                'type': 'tip',
                'title': 'Améliorez votre régularité',
                'message': 'Essayez d\'étudier un peu chaque jour.',
                'icon': '⏰'
            })

        # Recommandations d'horaires
        if best_hour < 12:
            time_recommendation = "Vous êtes plus productif le matin !"
        elif best_hour < 18:
            time_recommendation = "L'après-midi semble être votre moment idéal."
        else:
            time_recommendation = "Vous préférez étudier le soir."

        insights.append({
            'type': 'info',
            'title': 'Votre meilleur moment',
            'message': f"{time_recommendation} ({best_hour}h, {weekdays[best_weekday]})",
            'icon': '🕐'
        })

        return Response({
            'total_equations': user_equations.count(),
            'this_week_count': this_week_count,
            'consistency_score': round(consistency_score, 1),
            'best_study_time': {
                'weekday': weekdays[best_weekday],
                'hour': best_hour
            },
            'insights': insights,
            'activity_pattern': {
                'weekdays': [weekday_activity.get(i, 0) for i in range(7)],
                'hours': [hour_activity.get(i, 0) for i in range(24)]
            },
            'recommendations': [
                "Essayez de résoudre au moins une équation par jour",
                f"Votre meilleur moment semble être {weekdays[best_weekday]} vers {best_hour}h",
                "Variez les types d'équations pour progresser plus vite"
            ]
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la génération des insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_admin_dashboard(request):
    """Dashboard administrateur avec statistiques globales"""
    try:
        # Vérifier que l'utilisateur est admin
        if not request.user.is_staff and request.user.role != 'admin':
            return Response({
                'error': 'Accès non autorisé'
            }, status=status.HTTP_403_FORBIDDEN)

        from datetime import datetime, timedelta
        from django.db.models import Count, Q

        # Statistiques générales
        total_users = User.objects.count()
        total_students = User.objects.filter(role='student').count()
        total_teachers = User.objects.filter(role='teacher').count()
        total_admins = User.objects.filter(Q(role='admin') | Q(is_staff=True)).count()

        total_equations = Equation.objects.count()
        total_courses = Course.objects.count()
        total_exercises = Exercise.objects.count()

        # Activité récente (7 derniers jours)
        last_week = datetime.now() - timedelta(days=7)
        recent_equations = Equation.objects.filter(created_at__gte=last_week).count()
        recent_users = User.objects.filter(date_joined__gte=last_week).count()

        # Utilisateurs actifs (qui ont résolu au moins une équation dans les 7 derniers jours)
        active_users = User.objects.filter(
            equations__created_at__gte=last_week
        ).distinct().count()

        # Top utilisateurs par nombre d'équations
        top_users = User.objects.annotate(
            equation_count=Count('equations')
        ).order_by('-equation_count')[:5]

        top_users_data = []
        for user in top_users:
            top_users_data.append({
                'id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'equation_count': user.equation_count,
                'date_joined': user.date_joined
            })

        # Statistiques par jour (7 derniers jours)
        daily_stats = []
        for i in range(7):
            date = datetime.now() - timedelta(days=i)
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)

            equations_count = Equation.objects.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).count()

            new_users_count = User.objects.filter(
                date_joined__gte=day_start,
                date_joined__lt=day_end
            ).count()

            daily_stats.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'equations': equations_count,
                'new_users': new_users_count
            })

        # Répartition par niveau de cours
        course_levels = Course.objects.values('level').annotate(count=Count('level'))

        # Équations les plus populaires
        popular_equations = Equation.objects.values('equation_text').annotate(
            count=Count('equation_text')
        ).order_by('-count')[:5]

        return Response({
            'overview': {
                'total_users': total_users,
                'total_students': total_students,
                'total_teachers': total_teachers,
                'total_admins': total_admins,
                'total_equations': total_equations,
                'total_courses': total_courses,
                'total_exercises': total_exercises,
                'active_users': active_users,
                'recent_equations': recent_equations,
                'recent_users': recent_users
            },
            'top_users': top_users_data,
            'daily_stats': daily_stats,
            'course_levels': list(course_levels),
            'popular_equations': list(popular_equations),
            'growth_rate': {
                'users': round((recent_users / max(total_users - recent_users, 1)) * 100, 1),
                'equations': round((recent_equations / max(total_equations - recent_equations, 1)) * 100, 1)
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la génération du dashboard admin: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_management(request):
    """Gestion des utilisateurs pour l'admin"""
    try:
        # Vérifier que l'utilisateur est admin
        if not request.user.is_staff and request.user.role != 'admin':
            return Response({
                'error': 'Accès non autorisé'
            }, status=status.HTTP_403_FORBIDDEN)

        from django.db.models import Count

        # Récupérer tous les utilisateurs avec leurs statistiques
        users = User.objects.annotate(
            equation_count=Count('equations')
        ).order_by('-date_joined')

        users_data = []
        for user in users:
            # Dernière activité
            last_equation = user.equations.order_by('-created_at').first()
            last_activity = last_equation.created_at if last_equation else user.date_joined

            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'date_joined': user.date_joined,
                'last_activity': last_activity,
                'equation_count': user.equation_count
            })

        # Statistiques par rôle
        role_stats = User.objects.values('role').annotate(count=Count('role'))

        return Response({
            'users': users_data,
            'role_stats': list(role_stats),
            'total_count': len(users_data)
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération des utilisateurs: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def toggle_user_status(request, user_id):
    """Activer/désactiver un utilisateur"""
    try:
        # Vérifier que l'utilisateur est admin
        if not request.user.is_staff and request.user.role != 'admin':
            return Response({
                'error': 'Accès non autorisé'
            }, status=status.HTTP_403_FORBIDDEN)

        user = User.objects.get(id=user_id)
        user.is_active = not user.is_active
        user.save()

        return Response({
            'success': True,
            'user_id': user_id,
            'is_active': user.is_active,
            'message': f'Utilisateur {"activé" if user.is_active else "désactivé"} avec succès'
        })

    except User.DoesNotExist:
        return Response({
            'error': 'Utilisateur non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': f'Erreur lors de la modification: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_system_health(request):
    """Vérification de la santé du système"""
    try:
        # Vérifier que l'utilisateur est admin
        if not request.user.is_staff and request.user.role != 'admin':
            return Response({
                'error': 'Accès non autorisé'
            }, status=status.HTTP_403_FORBIDDEN)

        import psutil
        import os
        from datetime import datetime

        # Informations système
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Informations base de données
        from django.db import connection
        db_queries_count = len(connection.queries)

        # Vérifications de santé
        health_checks = []

        # CPU
        if cpu_percent < 70:
            health_checks.append({
                'name': 'CPU Usage',
                'status': 'healthy',
                'value': f'{cpu_percent}%',
                'message': 'CPU usage is normal'
            })
        else:
            health_checks.append({
                'name': 'CPU Usage',
                'status': 'warning',
                'value': f'{cpu_percent}%',
                'message': 'High CPU usage detected'
            })

        # Mémoire
        memory_percent = memory.percent
        if memory_percent < 80:
            health_checks.append({
                'name': 'Memory Usage',
                'status': 'healthy',
                'value': f'{memory_percent}%',
                'message': 'Memory usage is normal'
            })
        else:
            health_checks.append({
                'name': 'Memory Usage',
                'status': 'warning',
                'value': f'{memory_percent}%',
                'message': 'High memory usage detected'
            })

        # Espace disque
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent < 85:
            health_checks.append({
                'name': 'Disk Space',
                'status': 'healthy',
                'value': f'{disk_percent:.1f}%',
                'message': 'Disk space is sufficient'
            })
        else:
            health_checks.append({
                'name': 'Disk Space',
                'status': 'critical',
                'value': f'{disk_percent:.1f}%',
                'message': 'Low disk space warning'
            })

        return Response({
            'timestamp': datetime.now(),
            'system_info': {
                'cpu_percent': cpu_percent,
                'memory_total': memory.total,
                'memory_used': memory.used,
                'memory_percent': memory_percent,
                'disk_total': disk.total,
                'disk_used': disk.used,
                'disk_percent': disk_percent
            },
            'health_checks': health_checks,
            'database': {
                'queries_count': db_queries_count,
                'status': 'connected'
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la vérification système: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def course_recommendations(request):
    """Recommandations de cours personnalisées"""
    try:
        user = request.user

        # Calculer le niveau de l'utilisateur basé sur ses équations
        user_equations = Equation.objects.filter(user=user).count()

        if user_equations < 5:
            user_level = 'beginner'
        elif user_equations < 20:
            user_level = 'intermediate'
        else:
            user_level = 'advanced'

        # Recommandations basées sur le niveau
        recommended_courses = []
        all_courses = Course.objects.all()

        # Mapping des niveaux plus flexible
        level_mapping = {
            'beginner': ['beginner', 'debutant', 'facile', 'easy', 'basic'],
            'intermediate': ['intermediate', 'intermediaire', 'moyen', 'medium'],
            'advanced': ['advanced', 'avance', 'difficile', 'hard', 'expert']
        }

        target_levels = level_mapping.get(user_level, [])

        for course in all_courses:
            if course.level.lower() in target_levels:
                recommended_courses.append(course)

        # Si pas de cours spécifiques, recommander selon une logique alternative
        if not recommended_courses:
            if user_level == 'beginner':
                # Pour débutants, prendre tous les cours disponibles
                recommended_courses = list(all_courses[:3])
            elif user_level == 'intermediate':
                # Pour intermédiaires, éviter les cours trop basiques
                recommended_courses = list(all_courses[1:4]) if len(all_courses) > 1 else list(all_courses)
            else:  # advanced
                # Pour avancés, prendre les cours les plus récents ou tous
                recommended_courses = list(all_courses[:3])

        # Prochaine étape
        next_milestone = None
        if user_level == 'beginner':
            next_milestone = {
                'target': '10 équations résolues',
                'current': user_equations,
                'needed': max(0, 10 - user_equations)
            }
        elif user_level == 'intermediate':
            next_milestone = {
                'target': '50 équations résolues',
                'current': user_equations,
                'needed': max(0, 50 - user_equations)
            }
        else:
            next_milestone = {
                'target': 'Niveau expert atteint !',
                'current': user_equations,
                'needed': 0
            }

        return Response({
            'user_level': user_level,
            'total_equations_solved': user_equations,
            'recommended_courses': [
                {
                    'id': course.id,
                    'title': course.title,
                    'description': course.description,
                    'level': course.level,
                    'estimated_duration': '2-3 heures',
                    'difficulty_score': 3 if course.level.lower() in ['advanced', 'avance', 'difficile', 'hard', 'expert'] else 2 if course.level.lower() in ['intermediate', 'intermediaire', 'moyen', 'medium'] else 1
                }
                for course in recommended_courses[:3]
            ],
            'next_milestone': next_milestone,
            'learning_tips': [
                'Pratiquez régulièrement pour améliorer vos compétences',
                'Commencez par les équations simples avant de passer aux complexes',
                'N\'hésitez pas à revoir les étapes de résolution'
            ]
        })

    except Exception as e:
        import traceback
        return Response({
            'error': f'Erreur lors du chargement des recommandations: {str(e)}',
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def learning_path(request):
    """Parcours d'apprentissage personnalisé"""
    try:
        user = request.user
        user_equations = Equation.objects.filter(user=user).count()

        # Définir les étapes du parcours
        path_steps = [
            {
                'id': 1,
                'title': 'Équations linéaires simples',
                'description': 'Maîtrisez les équations du type ax + b = c',
                'target_equations': 5,
                'completed': user_equations >= 5,
                'examples': ['2x + 3 = 11', 'x - 5 = 7', '3x = 15']
            },
            {
                'id': 2,
                'title': 'Équations avec fractions',
                'description': 'Résolvez des équations contenant des fractions',
                'target_equations': 10,
                'completed': user_equations >= 10,
                'examples': ['x/2 + 3 = 7', '2x/3 = 8', '(x+1)/4 = 2']
            },
            {
                'id': 3,
                'title': 'Équations quadratiques',
                'description': 'Découvrez les équations du second degré',
                'target_equations': 20,
                'completed': user_equations >= 20,
                'examples': ['x² - 4 = 0', 'x² + 2x - 3 = 0', '2x² - 8 = 0']
            },
            {
                'id': 4,
                'title': 'Systèmes d\'équations',
                'description': 'Résolvez plusieurs équations simultanément',
                'target_equations': 35,
                'completed': user_equations >= 35,
                'examples': ['x + y = 5, x - y = 1', '2x + 3y = 12, x - y = 1']
            },
            {
                'id': 5,
                'title': 'Équations exponentielles',
                'description': 'Maîtrisez les équations avec exponentielles',
                'target_equations': 50,
                'completed': user_equations >= 50,
                'examples': ['2^x = 8', 'e^x = 10', '3^(x+1) = 27']
            }
        ]

        # Calculer le progrès
        completed_steps = sum(1 for step in path_steps if step['completed'])
        total_steps = len(path_steps)
        progress_percentage = (completed_steps / total_steps) * 100

        # Prochaine étape
        next_step = None
        for step in path_steps:
            if not step['completed']:
                next_step = step
                break

        return Response({
            'current_level': user_equations,
            'progress_percentage': progress_percentage,
            'completed_steps': completed_steps,
            'total_steps': total_steps,
            'path_steps': path_steps,
            'next_step': next_step,
            'estimated_completion': f'{total_steps - completed_steps} étapes restantes'
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors du chargement du parcours: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def daily_challenge(request):
    """Défi quotidien"""
    try:
        from datetime import date
        import random

        user = request.user
        today = date.today()

        # Équations de défi selon le niveau
        challenges_by_level = {
            'beginner': [
                {'equation': '3x + 7 = 22', 'hint': 'Isolez x en soustrayant 7 puis en divisant par 3', 'points': 10},
                {'equation': '2x - 5 = 11', 'hint': 'Ajoutez 5 des deux côtés puis divisez par 2', 'points': 10},
                {'equation': '4x + 1 = 17', 'hint': 'Soustrayez 1 puis divisez par 4', 'points': 10},
            ],
            'intermediate': [
                {'equation': 'x² - 9 = 0', 'hint': 'Utilisez la différence de carrés: a² - b² = (a+b)(a-b)', 'points': 20},
                {'equation': '2x² - 8 = 0', 'hint': 'Divisez par 2 puis trouvez la racine carrée', 'points': 20},
                {'equation': 'x² + 4x - 5 = 0', 'hint': 'Factorisez ou utilisez la formule quadratique', 'points': 25},
            ],
            'advanced': [
                {'equation': 'e^x = 20', 'hint': 'Utilisez le logarithme naturel des deux côtés', 'points': 30},
                {'equation': 'log(x) + log(x-3) = 1', 'hint': 'Utilisez les propriétés des logarithmes', 'points': 35},
                {'equation': '2^(x+1) = 32', 'hint': 'Exprimez 32 comme une puissance de 2', 'points': 30},
            ]
        }

        # Déterminer le niveau de l'utilisateur
        user_equations = Equation.objects.filter(user=user).count()
        if user_equations < 10:
            level = 'beginner'
        elif user_equations < 30:
            level = 'intermediate'
        else:
            level = 'advanced'

        # Sélectionner un défi basé sur la date (même défi chaque jour)
        random.seed(today.toordinal())
        challenge = random.choice(challenges_by_level[level])

        # Vérifier si déjà résolu aujourd'hui
        already_solved = Equation.objects.filter(
            user=user,
            equation_text=challenge['equation'],
            created_at__date=today
        ).exists()

        # Bonus info
        bonus_info = {
            'first_try_bonus': 5,
            'streak_bonus': 0,  # À implémenter avec un système de streak
            'time_bonus': 'Résolvez en moins de 2 minutes pour +3 points'
        }

        return Response({
            'date': today.isoformat(),
            'equation': challenge['equation'],
            'hint': challenge['hint'],
            'points': challenge['points'],
            'level': level,
            'already_solved': already_solved,
            'description': f'Défi {level} du jour',
            'bonus_info': bonus_info,
            'time_limit': 300,  # 5 minutes en secondes
            'level_display': {
                'beginner': '🟢 Débutant',
                'intermediate': '🟡 Intermédiaire',
                'advanced': '🔴 Avancé'
            }.get(level, level)
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors du chargement du défi: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def learning_insights(request):
    """Insights d'apprentissage"""
    try:
        from datetime import datetime, timedelta
        from django.db.models import Count

        user = request.user
        now = datetime.now()

        # Statistiques des 7 derniers jours
        last_week = now - timedelta(days=7)
        equations_last_week = Equation.objects.filter(
            user=user,
            created_at__gte=last_week
        )

        # Activité par jour
        daily_activity = []
        for i in range(7):
            day = now - timedelta(days=i)
            day_equations = equations_last_week.filter(
                created_at__date=day.date()
            ).count()
            daily_activity.append({
                'date': day.date(),
                'equations_solved': day_equations
            })

        # Patterns d'apprentissage
        total_equations = Equation.objects.filter(user=user).count()
        avg_per_day = equations_last_week.count() / 7

        # Types d'équations les plus résolues
        equation_types = [
            {'type': 'Linéaires', 'count': int(total_equations * 0.4), 'percentage': 40},
            {'type': 'Quadratiques', 'count': int(total_equations * 0.3), 'percentage': 30},
            {'type': 'Exponentielles', 'count': int(total_equations * 0.2), 'percentage': 20},
            {'type': 'Logarithmiques', 'count': int(total_equations * 0.1), 'percentage': 10},
        ]

        # Recommandations personnalisées
        recommendations = []
        if avg_per_day < 1:
            recommendations.append({
                'type': 'frequency',
                'title': 'Augmentez votre fréquence',
                'description': 'Essayez de résoudre au moins 1 équation par jour',
                'priority': 'high'
            })

        if total_equations < 10:
            recommendations.append({
                'type': 'practice',
                'title': 'Plus de pratique',
                'description': 'Continuez à pratiquer pour améliorer vos compétences',
                'priority': 'medium'
            })

        # Temps optimal d'étude
        best_hours = [9, 10, 14, 15, 16]  # Heures optimales simulées

        # Prédictions de progression
        if avg_per_day > 0:
            days_to_next_level = max(1, int((50 - total_equations) / avg_per_day))
        else:
            days_to_next_level = None

        return Response({
            'period': '7 derniers jours',
            'total_equations': total_equations,
            'equations_this_week': equations_last_week.count(),
            'average_per_day': round(avg_per_day, 1),
            'daily_activity': daily_activity,
            'equation_types': equation_types,
            'learning_patterns': {
                'most_active_day': 'Lundi',  # À calculer réellement
                'best_performance_time': '14h-16h',
                'consistency_score': min(100, int(avg_per_day * 20))
            },
            'recommendations': recommendations,
            'predictions': {
                'days_to_next_level': days_to_next_level,
                'estimated_level_up': (now + timedelta(days=days_to_next_level)).date() if days_to_next_level else None
            },
            'achievements': [
                {'name': 'Premier pas', 'description': 'Première équation résolue', 'unlocked': total_equations >= 1},
                {'name': 'Apprenti', 'description': '10 équations résolues', 'unlocked': total_equations >= 10},
                {'name': 'Expert', 'description': '50 équations résolues', 'unlocked': total_equations >= 50},
                {'name': 'Maître', 'description': '100 équations résolues', 'unlocked': total_equations >= 100},
            ]
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors du chargement des insights: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ===== ENDPOINTS EXERCICES =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_exercises(request):
    """Récupérer la liste des exercices disponibles"""
    try:
        user = request.user

        # Filtres optionnels
        difficulty = request.GET.get('difficulty')
        course_id = request.GET.get('course')
        exercise_type = request.GET.get('type')

        # Base queryset
        exercises = Exercise.objects.all().select_related('course')

        # Appliquer les filtres
        if difficulty:
            exercises = exercises.filter(difficulty=difficulty)
        if course_id:
            exercises = exercises.filter(course_id=course_id)
        if exercise_type:
            exercises = exercises.filter(exercise_type=exercise_type)

        # Récupérer les tentatives de l'utilisateur
        user_attempts = ExerciseAttempt.objects.filter(
            user=user,
            exercise__in=exercises
        ).select_related('exercise')

        attempts_dict = {attempt.exercise_id: attempt for attempt in user_attempts}

        exercises_data = []
        for exercise in exercises:
            attempt = attempts_dict.get(exercise.id)

            exercises_data.append({
                'id': exercise.id,
                'title': exercise.title,
                'description': exercise.description,
                'course': {
                    'id': exercise.course.id,
                    'title': exercise.course.title,
                    'level': exercise.course.level
                },
                'difficulty': exercise.difficulty,
                'difficulty_display': dict(Exercise.DIFFICULTY_CHOICES)[exercise.difficulty],
                'exercise_type': exercise.exercise_type,
                'type_display': dict(Exercise.TYPE_CHOICES)[exercise.exercise_type],
                'points': exercise.points,
                'time_limit': exercise.time_limit,
                'user_attempt': {
                    'completed': attempt.status == 'completed' if attempt else False,
                    'is_correct': attempt.is_correct if attempt else False,
                    'points_earned': attempt.points_earned if attempt else 0,
                    'time_taken': attempt.time_taken if attempt else 0
                } if attempt else None,
                'created_at': exercise.created_at
            })

        return Response({
            'exercises': exercises_data,
            'total_count': len(exercises_data),
            'filters': {
                'difficulties': Exercise.DIFFICULTY_CHOICES,
                'types': Exercise.TYPE_CHOICES
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération des exercices: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_exercise_detail(request, exercise_id):
    """Récupérer les détails d'un exercice"""
    try:
        user = request.user

        # Récupérer l'exercice
        try:
            exercise = Exercise.objects.select_related('course').get(id=exercise_id)
        except Exercise.DoesNotExist:
            return Response({
                'error': 'Exercice non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

        # Récupérer les choix pour les exercices à choix multiples
        choices = []
        if exercise.exercise_type == 'multiple_choice':
            choices = list(exercise.choices.all().values('id', 'choice_text', 'order', 'is_correct'))

        # Récupérer la tentative de l'utilisateur
        user_attempt = ExerciseAttempt.objects.filter(
            user=user,
            exercise=exercise
        ).first()

        exercise_data = {
            'id': exercise.id,
            'title': exercise.title,
            'description': exercise.description,
            'question': exercise.question,
            'course': {
                'id': exercise.course.id,
                'title': exercise.course.title,
                'level': exercise.course.level
            },
            'difficulty': exercise.difficulty,
            'difficulty_display': dict(Exercise.DIFFICULTY_CHOICES)[exercise.difficulty],
            'exercise_type': exercise.exercise_type,
            'type_display': dict(Exercise.TYPE_CHOICES)[exercise.exercise_type],
            'points': exercise.points,
            'time_limit': exercise.time_limit,
            'choices': choices,
            'user_attempt': {
                'id': user_attempt.id,
                'status': user_attempt.status,
                'user_answer': user_attempt.user_answer,
                'is_correct': user_attempt.is_correct,
                'points_earned': user_attempt.points_earned,
                'time_taken': user_attempt.time_taken,
                'started_at': user_attempt.started_at,
                'completed_at': user_attempt.completed_at
            } if user_attempt else None,
            'created_at': exercise.created_at
        }

        # Si l'exercice est terminé, inclure la solution et l'explication
        if user_attempt and user_attempt.status == 'completed':
            exercise_data['correct_answer'] = exercise.correct_answer
            exercise_data['explanation'] = exercise.explanation

        return Response(exercise_data)

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération de l\'exercice: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_exercise(request, exercise_id):
    """Commencer un exercice"""
    try:
        user = request.user

        # Récupérer l'exercice
        try:
            exercise = Exercise.objects.get(id=exercise_id)
        except Exercise.DoesNotExist:
            return Response({
                'error': 'Exercice non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

        # Vérifier si l'utilisateur a déjà une tentative
        existing_attempt = ExerciseAttempt.objects.filter(
            user=user,
            exercise=exercise
        ).first()

        # Permettre le redémarrage en mode test (paramètre restart=true)
        restart = request.data.get('restart', False) or request.GET.get('restart') == 'true'

        if existing_attempt:
            if existing_attempt.status == 'completed' and not restart:
                return Response({
                    'error': 'Exercice déjà terminé'
                }, status=status.HTTP_400_BAD_REQUEST)
            elif existing_attempt.status == 'in_progress' and not restart:
                # Retourner la tentative existante
                return Response({
                    'message': 'Exercice déjà en cours',
                    'attempt_id': existing_attempt.id,
                    'started_at': existing_attempt.started_at
                })
            elif restart:
                # Supprimer l'ancienne tentative pour permettre le redémarrage
                existing_attempt.delete()

        # Créer une nouvelle tentative
        attempt = ExerciseAttempt.objects.create(
            user=user,
            exercise=exercise,
            status='in_progress'
        )

        return Response({
            'message': 'Exercice commencé',
            'attempt_id': attempt.id,
            'started_at': attempt.started_at,
            'time_limit': exercise.time_limit
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': f'Erreur lors du démarrage de l\'exercice: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_exercise(request, exercise_id):
    """Soumettre une réponse à un exercice"""
    try:
        user = request.user
        user_answer = request.data.get('answer', '').strip()
        time_taken = request.data.get('time_taken', 0)

        if not user_answer:
            return Response({
                'error': 'Réponse requise'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Récupérer l'exercice
        try:
            exercise = Exercise.objects.get(id=exercise_id)
        except Exercise.DoesNotExist:
            return Response({
                'error': 'Exercice non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

        # Récupérer la tentative
        attempt = ExerciseAttempt.objects.filter(
            user=user,
            exercise=exercise,
            status='in_progress'
        ).first()

        if not attempt:
            return Response({
                'error': 'Aucune tentative en cours trouvée'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Évaluer la réponse
        is_correct = False
        points_earned = 0

        if exercise.exercise_type == 'multiple_choice':
            # Pour les choix multiples, vérifier l'ID du choix
            try:
                choice_id = int(user_answer)
                correct_choice = ExerciseChoice.objects.filter(
                    exercise=exercise,
                    id=choice_id,
                    is_correct=True
                ).exists()
                is_correct = correct_choice
            except (ValueError, ExerciseChoice.DoesNotExist):
                is_correct = False
        else:
            # Pour les autres types, utiliser une évaluation intelligente
            is_correct = evaluate_answer(exercise, user_answer)

        # Calculer les points
        if is_correct:
            # Points bonus pour la rapidité (max 20% de bonus)
            time_bonus = max(0, 1 - (time_taken / exercise.time_limit)) * 0.2
            points_earned = int(exercise.points * (1 + time_bonus))
        else:
            points_earned = 0

        # Mettre à jour la tentative
        from django.utils import timezone
        attempt.user_answer = user_answer
        attempt.is_correct = is_correct
        attempt.points_earned = points_earned
        attempt.time_taken = time_taken
        attempt.status = 'completed'
        attempt.completed_at = timezone.now()
        attempt.save()

        # Gamification : Mettre à jour le niveau et la série
        gamification_data = {}

        # Mettre à jour le niveau (XP = points gagnés)
        user_level, created = UserLevel.objects.get_or_create(user=user)
        old_level = user_level.current_level
        new_level = user_level.add_xp(points_earned)

        if new_level > old_level:
            gamification_data['level_up'] = {
                'old_level': old_level,
                'new_level': new_level
            }

        # Mettre à jour la série
        user_streak, created = Streak.objects.get_or_create(user=user)
        new_streak = user_streak.update_streak(success=is_correct)
        gamification_data['streak'] = {
            'current_streak': new_streak,
            'longest_streak': user_streak.longest_streak
        }

        # Vérifier et attribuer les nouveaux badges
        new_badges = check_and_award_badges(user)
        if new_badges:
            gamification_data['new_badges'] = [
                {
                    'name': badge.name,
                    'icon': badge.icon,
                    'description': badge.description
                } for badge in new_badges
            ]

        return Response({
            'message': 'Réponse soumise avec succès',
            'is_correct': is_correct,
            'points_earned': points_earned,
            'correct_answer': exercise.correct_answer,
            'explanation': exercise.explanation,
            'time_taken': time_taken,
            'attempt_id': attempt.id,
            'gamification': gamification_data
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la soumission: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ===== ENDPOINTS PROGRESSION =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_progress(request):
    """Récupérer les statistiques de progression de l'utilisateur"""
    try:
        user = request.user

        # Statistiques générales
        total_attempts = ExerciseAttempt.objects.filter(user=user).count()
        completed_attempts = ExerciseAttempt.objects.filter(user=user, status='completed').count()
        correct_attempts = ExerciseAttempt.objects.filter(user=user, is_correct=True).count()
        total_points = ExerciseAttempt.objects.filter(user=user).aggregate(
            total=models.Sum('points_earned')
        )['total'] or 0

        # Progression par difficulté
        difficulty_stats = {}
        for difficulty, _ in Exercise.DIFFICULTY_CHOICES:
            attempts = ExerciseAttempt.objects.filter(
                user=user,
                exercise__difficulty=difficulty,
                status='completed'
            )
            correct = attempts.filter(is_correct=True).count()
            total = attempts.count()

            difficulty_stats[difficulty] = {
                'total_attempts': total,
                'correct_attempts': correct,
                'success_rate': (correct / total * 100) if total > 0 else 0,
                'points_earned': attempts.aggregate(total=models.Sum('points_earned'))['total'] or 0
            }

        # Progression par type d'exercice
        type_stats = {}
        for exercise_type, _ in Exercise.TYPE_CHOICES:
            attempts = ExerciseAttempt.objects.filter(
                user=user,
                exercise__exercise_type=exercise_type,
                status='completed'
            )
            correct = attempts.filter(is_correct=True).count()
            total = attempts.count()

            type_stats[exercise_type] = {
                'total_attempts': total,
                'correct_attempts': correct,
                'success_rate': (correct / total * 100) if total > 0 else 0,
                'points_earned': attempts.aggregate(total=models.Sum('points_earned'))['total'] or 0
            }

        # Progression dans le temps (7 derniers jours)
        from datetime import datetime, timedelta
        daily_progress = []
        for i in range(7):
            date = datetime.now().date() - timedelta(days=i)
            day_attempts = ExerciseAttempt.objects.filter(
                user=user,
                started_at__date=date,
                status='completed'
            )

            daily_progress.append({
                'date': date.isoformat(),
                'total_attempts': day_attempts.count(),
                'correct_attempts': day_attempts.filter(is_correct=True).count(),
                'points_earned': day_attempts.aggregate(total=models.Sum('points_earned'))['total'] or 0
            })

        daily_progress.reverse()  # Ordre chronologique

        # Temps moyen par exercice
        avg_time = ExerciseAttempt.objects.filter(
            user=user,
            status='completed'
        ).aggregate(avg=models.Avg('time_taken'))['avg'] or 0

        # Exercices récents
        recent_attempts = ExerciseAttempt.objects.filter(
            user=user,
            status='completed'
        ).select_related('exercise').order_by('-completed_at')[:5]

        recent_exercises = []
        for attempt in recent_attempts:
            recent_exercises.append({
                'exercise_title': attempt.exercise.title,
                'exercise_type': attempt.exercise.exercise_type,
                'difficulty': attempt.exercise.difficulty,
                'is_correct': attempt.is_correct,
                'points_earned': attempt.points_earned,
                'time_taken': attempt.time_taken,
                'completed_at': attempt.completed_at
            })

        return Response({
            'general_stats': {
                'total_attempts': total_attempts,
                'completed_attempts': completed_attempts,
                'correct_attempts': correct_attempts,
                'success_rate': (correct_attempts / completed_attempts * 100) if completed_attempts > 0 else 0,
                'total_points': total_points,
                'average_time': round(avg_time, 1) if avg_time else 0
            },
            'difficulty_stats': difficulty_stats,
            'type_stats': type_stats,
            'daily_progress': daily_progress,
            'recent_exercises': recent_exercises
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération des statistiques: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_leaderboard(request):
    """Récupérer le classement des utilisateurs"""
    try:
        # Paramètres optionnels
        period = request.GET.get('period', 'all')  # all, week, month
        limit = int(request.GET.get('limit', 10))

        # Filtrer par période
        from datetime import datetime, timedelta
        filter_kwargs = {'status': 'completed'}

        if period == 'week':
            week_ago = datetime.now() - timedelta(days=7)
            filter_kwargs['completed_at__gte'] = week_ago
        elif period == 'month':
            month_ago = datetime.now() - timedelta(days=30)
            filter_kwargs['completed_at__gte'] = month_ago

        # Calculer les statistiques par utilisateur
        from django.db.models import Count, Sum, Avg

        leaderboard = User.objects.filter(
            exercise_attempts__status='completed'
        ).annotate(
            total_points=Sum('exercise_attempts__points_earned',
                           filter=models.Q(exercise_attempts__status='completed')),
            total_exercises=Count('exercise_attempts',
                                filter=models.Q(exercise_attempts__status='completed')),
            correct_exercises=Count('exercise_attempts',
                                  filter=models.Q(exercise_attempts__status='completed',
                                                 exercise_attempts__is_correct=True)),
            avg_time=Avg('exercise_attempts__time_taken',
                        filter=models.Q(exercise_attempts__status='completed'))
        ).filter(
            total_exercises__gt=0
        ).order_by('-total_points')[:limit]

        leaderboard_data = []
        for i, user in enumerate(leaderboard, 1):
            success_rate = (user.correct_exercises / user.total_exercises * 100) if user.total_exercises > 0 else 0

            leaderboard_data.append({
                'rank': i,
                'username': user.username,
                'total_points': user.total_points or 0,
                'total_exercises': user.total_exercises or 0,
                'correct_exercises': user.correct_exercises or 0,
                'success_rate': round(success_rate, 1),
                'average_time': round(user.avg_time or 0, 1)
            })

        return Response({
            'leaderboard': leaderboard_data,
            'period': period,
            'total_users': len(leaderboard_data)
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération du classement: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ===== ENDPOINTS GAMIFICATION =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_gamification(request):
    """Récupérer les données de gamification de l'utilisateur"""
    try:
        user = request.user

        # Récupérer ou créer le niveau utilisateur
        user_level, created = UserLevel.objects.get_or_create(user=user)

        # Récupérer ou créer la série utilisateur
        user_streak, created = Streak.objects.get_or_create(user=user)

        # Récupérer les badges obtenus
        user_badges = UserBadge.objects.filter(user=user).select_related('badge').order_by('-earned_at')

        # Récupérer tous les badges disponibles
        all_badges = Badge.objects.filter(is_active=True).order_by('category', 'points_required', 'exercises_required')

        # Organiser les badges par catégorie
        badges_by_category = {}
        for badge in all_badges:
            if badge.category not in badges_by_category:
                badges_by_category[badge.category] = []

            # Vérifier si l'utilisateur a ce badge
            has_badge = user_badges.filter(badge=badge).exists()

            badges_by_category[badge.category].append({
                'id': badge.id,
                'name': badge.name,
                'description': badge.description,
                'icon': badge.icon,
                'points_required': badge.points_required,
                'exercises_required': badge.exercises_required,
                'streak_required': badge.streak_required,
                'difficulty_required': badge.difficulty_required,
                'earned': has_badge,
                'earned_at': user_badges.filter(badge=badge).first().earned_at if has_badge else None
            })

        # Calculer les statistiques pour les badges
        total_points = ExerciseAttempt.objects.filter(user=user).aggregate(
            total=models.Sum('points_earned')
        )['total'] or 0

        total_exercises = ExerciseAttempt.objects.filter(user=user, status='completed').count()

        return Response({
            'level': {
                'current_level': user_level.current_level,
                'total_xp': user_level.total_xp,
                'xp_to_next_level': user_level.xp_to_next_level,
                'progress_percentage': (user_level.total_xp / user_level.xp_to_next_level) * 100
            },
            'streak': {
                'current_streak': user_streak.current_streak,
                'longest_streak': user_streak.longest_streak,
                'last_activity_date': user_streak.last_activity_date
            },
            'badges': {
                'earned_count': user_badges.count(),
                'total_count': all_badges.count(),
                'by_category': badges_by_category,
                'recent_badges': [
                    {
                        'name': ub.badge.name,
                        'icon': ub.badge.icon,
                        'earned_at': ub.earned_at
                    } for ub in user_badges[:5]
                ]
            },
            'stats': {
                'total_points': total_points,
                'total_exercises': total_exercises
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération des données de gamification: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def check_and_award_badges(user):
    """Vérifier et attribuer les badges mérités par l'utilisateur"""
    try:
        # Récupérer les statistiques utilisateur
        total_points = ExerciseAttempt.objects.filter(user=user).aggregate(
            total=models.Sum('points_earned')
        )['total'] or 0

        total_exercises = ExerciseAttempt.objects.filter(user=user, status='completed').count()

        # Récupérer la série actuelle
        user_streak = Streak.objects.filter(user=user).first()
        current_streak = user_streak.current_streak if user_streak else 0

        # Récupérer les badges déjà obtenus
        earned_badges = set(UserBadge.objects.filter(user=user).values_list('badge_id', flat=True))

        # Vérifier tous les badges
        all_badges = Badge.objects.filter(is_active=True)
        new_badges = []

        for badge in all_badges:
            if badge.id in earned_badges:
                continue  # Badge déjà obtenu

            # Vérifier les conditions
            conditions_met = True

            # Condition points
            if badge.points_required > 0 and total_points < badge.points_required:
                conditions_met = False

            # Condition exercices
            if badge.exercises_required > 0:
                if badge.difficulty_required:
                    # Exercices d'une difficulté spécifique
                    exercises_count = ExerciseAttempt.objects.filter(
                        user=user,
                        status='completed',
                        exercise__difficulty=badge.difficulty_required
                    ).count()
                else:
                    # Tous les exercices
                    exercises_count = total_exercises

                if exercises_count < badge.exercises_required:
                    conditions_met = False

            # Condition série
            if badge.streak_required > 0 and current_streak < badge.streak_required:
                conditions_met = False

            # Si toutes les conditions sont remplies, attribuer le badge
            if conditions_met:
                UserBadge.objects.create(user=user, badge=badge)
                new_badges.append(badge)

        return new_badges

    except Exception as e:
        print(f"Erreur lors de la vérification des badges: {e}")
        return []


# ===== ENDPOINTS COURS STRUCTURÉS =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_structured_courses(request):
    """Récupérer la liste des cours structurés avec progression"""
    try:
        user = request.user

        # Filtres optionnels
        level = request.GET.get('level')
        featured_only = request.GET.get('featured') == 'true'

        # Base queryset
        courses = Course.objects.filter(status='published').prefetch_related(
            'chapters__lessons', 'prerequisites'
        ).order_by('order', 'title')

        # Appliquer les filtres
        if level:
            courses = courses.filter(level=level)
        if featured_only:
            courses = courses.filter(is_featured=True)

        courses_data = []
        for course in courses:
            # Calculer la progression
            progress_percentage = course.get_progress_for_user(user)

            # Vérifier l'accessibilité
            is_accessible = course.is_accessible_for_user(user)

            # Vérifier l'inscription
            enrollment = CourseEnrollment.objects.filter(user=user, course=course).first()

            # Compter les chapitres et leçons
            chapters_count = course.chapters.count()
            lessons_count = sum(chapter.lessons.count() for chapter in course.chapters.all())

            courses_data.append({
                'id': course.id,
                'title': course.title,
                'description': course.description,
                'level': course.level,
                'level_display': dict(Course.LEVEL_CHOICES)[course.level],
                'thumbnail': course.thumbnail,
                'estimated_duration': course.estimated_duration,
                'is_featured': course.is_featured,
                'chapters_count': chapters_count,
                'lessons_count': lessons_count,
                'progress_percentage': round(progress_percentage, 1),
                'is_accessible': is_accessible,
                'is_enrolled': enrollment is not None,
                'enrollment_date': enrollment.enrolled_at if enrollment else None,
                'prerequisites': [
                    {
                        'id': prereq.id,
                        'title': prereq.title,
                        'progress': round(prereq.get_progress_for_user(user), 1)
                    } for prereq in course.prerequisites.all()
                ],
                'created_at': course.created_at
            })

        return Response({
            'courses': courses_data,
            'total_count': len(courses_data),
            'filters': {
                'levels': Course.LEVEL_CHOICES
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération des cours: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_course_detail(request, course_id):
    """Récupérer les détails d'un cours avec chapitres et leçons"""
    try:
        user = request.user

        # Récupérer le cours
        try:
            course = Course.objects.prefetch_related(
                'chapters__lessons', 'prerequisites'
            ).get(id=course_id, status='published')
        except Course.DoesNotExist:
            return Response({
                'error': 'Cours non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

        # Vérifier l'accessibilité
        if not course.is_accessible_for_user(user):
            return Response({
                'error': 'Cours non accessible. Terminez d\'abord les prérequis.',
                'prerequisites': [
                    {
                        'id': prereq.id,
                        'title': prereq.title,
                        'progress': round(prereq.get_progress_for_user(user), 1)
                    } for prereq in course.prerequisites.all()
                ]
            }, status=status.HTTP_403_FORBIDDEN)

        # Récupérer l'inscription
        enrollment = CourseEnrollment.objects.filter(user=user, course=course).first()

        # Récupérer les chapitres avec leçons
        chapters_data = []
        for chapter in course.chapters.filter(is_published=True).order_by('order'):
            lessons_data = []

            for lesson in chapter.lessons.filter(is_published=True).order_by('order'):
                # Récupérer la progression de la leçon
                lesson_progress = LessonProgress.objects.filter(
                    user=user, lesson=lesson
                ).first()

                # Créer ou récupérer la progression si elle n'existe pas
                if not lesson_progress:
                    lesson_progress, created = LessonProgress.objects.get_or_create(
                        user=user,
                        lesson=lesson,
                        defaults={'completed': False, 'time_spent': 0}
                    )

                lessons_data.append({
                    'id': lesson.id,
                    'title': lesson.title,
                    'lesson_type': lesson.lesson_type,
                    'type_display': dict(Lesson.LESSON_TYPE_CHOICES)[lesson.lesson_type],
                    'order': lesson.order,
                    'estimated_duration': lesson.estimated_duration,
                    'is_accessible': lesson.is_accessible_for_user(user),
                    'is_completed': lesson_progress.completed,
                    'time_spent': lesson_progress.time_spent,
                    'exercise_id': lesson.exercise.id if lesson.exercise else None
                })

            chapter_progress = chapter.get_progress_for_user(user)
            chapters_data.append({
                'id': chapter.id,
                'title': chapter.title,
                'description': chapter.description,
                'order': chapter.order,
                'progress_percentage': round(chapter_progress, 1),
                'lessons': lessons_data
            })

        course_progress = course.get_progress_for_user(user)

        return Response({
            'id': course.id,
            'title': course.title,
            'description': course.description,
            'level': course.level,
            'level_display': dict(Course.LEVEL_CHOICES)[course.level],
            'thumbnail': course.thumbnail,
            'estimated_duration': course.estimated_duration,
            'progress_percentage': round(course_progress, 1),
            'is_enrolled': enrollment is not None,
            'enrollment_date': enrollment.enrolled_at if enrollment else None,
            'chapters': chapters_data,
            'prerequisites': [
                {
                    'id': prereq.id,
                    'title': prereq.title,
                    'progress': round(prereq.get_progress_for_user(user), 1)
                } for prereq in course.prerequisites.all()
            ],
            'created_at': course.created_at
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération du cours: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_lesson_detail(request, lesson_id):
    """Récupérer les détails d'une leçon"""
    try:
        user = request.user

        # Récupérer la leçon
        try:
            lesson = Lesson.objects.select_related(
                'chapter__course', 'exercise'
            ).get(id=lesson_id, is_published=True)
        except Lesson.DoesNotExist:
            return Response({
                'error': 'Leçon non trouvée'
            }, status=status.HTTP_404_NOT_FOUND)

        # Vérifier l'accessibilité du cours
        if not lesson.chapter.course.is_accessible_for_user(user):
            return Response({
                'error': 'Cours non accessible'
            }, status=status.HTTP_403_FORBIDDEN)

        # Vérifier l'accessibilité de la leçon
        if not lesson.is_accessible_for_user(user):
            return Response({
                'error': 'Leçon non accessible. Terminez d\'abord les leçons précédentes.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Récupérer ou créer la progression
        lesson_progress, created = LessonProgress.objects.get_or_create(
            user=user,
            lesson=lesson,
            defaults={'completed': False, 'time_spent': 0}
        )

        return Response({
            'id': lesson.id,
            'title': lesson.title,
            'content': lesson.content,
            'lesson_type': lesson.lesson_type,
            'type_display': dict(Lesson.LESSON_TYPE_CHOICES)[lesson.lesson_type],
            'estimated_duration': lesson.estimated_duration,
            'chapter': {
                'id': lesson.chapter.id,
                'title': lesson.chapter.title,
                'course': {
                    'id': lesson.chapter.course.id,
                    'title': lesson.chapter.course.title
                }
            },
            'exercise': {
                'id': lesson.exercise.id,
                'title': lesson.exercise.title
            } if lesson.exercise else None,
            'progress': {
                'is_completed': lesson_progress.completed,
                'time_spent': lesson_progress.time_spent,
                'started_at': lesson_progress.started_at.isoformat() if lesson_progress.started_at else None,
                'completed_at': lesson_progress.completed_at.isoformat() if lesson_progress.completed_at else None
            }
        })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la récupération de la leçon: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def enroll_in_course(request, course_id):
    """S'inscrire à un cours"""
    try:
        user = request.user

        # Récupérer le cours
        try:
            course = Course.objects.get(id=course_id, status='published')
        except Course.DoesNotExist:
            return Response({
                'error': 'Cours non trouvé'
            }, status=status.HTTP_404_NOT_FOUND)

        # Vérifier l'accessibilité
        if not course.is_accessible_for_user(user):
            return Response({
                'error': 'Cours non accessible. Terminez d\'abord les prérequis.',
                'prerequisites': [
                    {
                        'id': prereq.id,
                        'title': prereq.title,
                        'progress': round(prereq.get_progress_for_user(user), 1)
                    } for prereq in course.prerequisites.all()
                ]
            }, status=status.HTTP_403_FORBIDDEN)

        # Vérifier si déjà inscrit
        enrollment, created = CourseEnrollment.objects.get_or_create(
            user=user,
            course=course,
            defaults={'is_active': True}
        )

        if not created:
            return Response({
                'message': 'Déjà inscrit à ce cours',
                'enrollment_date': enrollment.enrolled_at
            })

        return Response({
            'message': 'Inscription réussie',
            'enrollment_date': enrollment.enrolled_at
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': f'Erreur lors de l\'inscription: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_lesson(request, lesson_id):
    """Marquer une leçon comme terminée"""
    try:
        user = request.user
        time_spent = request.data.get('time_spent', 0)

        # Récupérer la leçon
        try:
            lesson = Lesson.objects.select_related('chapter__course').get(
                id=lesson_id, is_published=True
            )
        except Lesson.DoesNotExist:
            return Response({
                'error': 'Leçon non trouvée'
            }, status=status.HTTP_404_NOT_FOUND)

        # Vérifier l'accessibilité
        if not lesson.is_accessible_for_user(user):
            return Response({
                'error': 'Leçon non accessible'
            }, status=status.HTTP_403_FORBIDDEN)

        # Créer ou mettre à jour la progression
        lesson_progress, created = LessonProgress.objects.get_or_create(
            user=user,
            lesson=lesson,
            defaults={'time_spent': time_spent, 'completed': False}
        )

        if not lesson_progress.completed:
            lesson_progress.time_spent = max(lesson_progress.time_spent, time_spent)
            lesson_progress.mark_completed()

            # Vérifier si le cours est terminé
            course = lesson.chapter.course
            course_progress = course.get_progress_for_user(user)

            # Créer ou mettre à jour l'inscription au cours
            enrollment, enrollment_created = CourseEnrollment.objects.get_or_create(
                user=user,
                course=course,
                defaults={'enrolled_at': timezone.now()}
            )

            if course_progress >= 100 and not enrollment.completed_at:
                enrollment.mark_completed()

            return Response({
                'message': 'Leçon terminée avec succès',
                'course_progress': round(course_progress, 1),
                'lesson_completed': True
            })
        else:
            return Response({
                'message': 'Leçon déjà terminée',
                'lesson_completed': True
            })

    except Exception as e:
        return Response({
            'error': f'Erreur lors de la completion: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ===== VUES POUR LES MESSAGES DE CONTACT =====

@api_view(['POST'])
@permission_classes([AllowAny])
def contact_message_create(request):
    """Créer un nouveau message de contact"""
    from .serializers import ContactMessageCreateSerializer

    serializer = ContactMessageCreateSerializer(data=request.data)
    if serializer.is_valid():
        # Ajouter des informations techniques
        contact_message = serializer.save(
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            ip_address=get_client_ip(request)
        )

        return Response({
            'success': True,
            'message': 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.',
            'contact_id': contact_message.id
        }, status=status.HTTP_201_CREATED)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def contact_messages_list(request):
    """Liste des messages de contact (admin seulement)"""
    if not request.user.is_staff:
        return Response({
            'error': 'Accès non autorisé'
        }, status=status.HTTP_403_FORBIDDEN)

    from .serializers import ContactMessageSerializer
    from django.db.models import Q

    # Filtres
    status_filter = request.GET.get('status', None)
    category_filter = request.GET.get('category', None)
    priority_filter = request.GET.get('priority', None)
    search = request.GET.get('search', None)

    # Query de base
    queryset = ContactMessage.objects.all()

    # Appliquer les filtres
    if status_filter:
        queryset = queryset.filter(status=status_filter)

    if category_filter:
        queryset = queryset.filter(category=category_filter)

    if priority_filter:
        queryset = queryset.filter(priority=priority_filter)

    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(subject__icontains=search) |
            Q(message__icontains=search)
        )

    # Pagination
    from django.core.paginator import Paginator
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))

    paginator = Paginator(queryset, page_size)
    page_obj = paginator.get_page(page)

    serializer = ContactMessageSerializer(page_obj.object_list, many=True)

    return Response({
        'results': serializer.data,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total_pages': paginator.num_pages,
            'total_count': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        },
        'filters': {
            'status_choices': ContactMessage.STATUS_CHOICES,
            'category_choices': ContactMessage.CATEGORY_CHOICES,
            'priority_choices': ContactMessage.PRIORITY_CHOICES
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def contact_message_detail(request, message_id):
    """Détail d'un message de contact (admin seulement)"""
    if not request.user.is_staff:
        return Response({
            'error': 'Accès non autorisé'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        message = ContactMessage.objects.get(id=message_id)
    except ContactMessage.DoesNotExist:
        return Response({
            'error': 'Message non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)

    from .serializers import ContactMessageSerializer
    serializer = ContactMessageSerializer(message)

    return Response(serializer.data)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def contact_message_update(request, message_id):
    """Mettre à jour un message de contact (admin seulement)"""
    if not request.user.is_staff:
        return Response({
            'error': 'Accès non autorisé'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        message = ContactMessage.objects.get(id=message_id)
    except ContactMessage.DoesNotExist:
        return Response({
            'error': 'Message non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)

    from .serializers import ContactMessageUpdateSerializer
    serializer = ContactMessageUpdateSerializer(message, data=request.data, partial=True)

    if serializer.is_valid():
        serializer.save()

        # Retourner le message mis à jour
        from .serializers import ContactMessageSerializer
        updated_serializer = ContactMessageSerializer(message)

        return Response({
            'success': True,
            'message': 'Message mis à jour avec succès',
            'data': updated_serializer.data
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def contact_message_mark_resolved(request, message_id):
    """Marquer un message comme résolu"""
    if not request.user.is_staff:
        return Response({
            'error': 'Accès non autorisé'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        message = ContactMessage.objects.get(id=message_id)
    except ContactMessage.DoesNotExist:
        return Response({
            'error': 'Message non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)

    # Marquer comme résolu
    message.mark_as_resolved(admin_user=request.user)

    return Response({
        'success': True,
        'message': 'Message marqué comme résolu',
        'resolved_at': message.resolved_at
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def contact_messages_stats(request):
    """Statistiques des messages de contact (admin seulement)"""
    if not request.user.is_staff:
        return Response({
            'error': 'Accès non autorisé'
        }, status=status.HTTP_403_FORBIDDEN)

    from django.db.models import Count
    from datetime import datetime, timedelta

    # Statistiques générales
    total_messages = ContactMessage.objects.count()
    new_messages = ContactMessage.objects.filter(status='new').count()
    in_progress_messages = ContactMessage.objects.filter(status='in_progress').count()
    resolved_messages = ContactMessage.objects.filter(status='resolved').count()

    # Messages par catégorie
    messages_by_category = ContactMessage.objects.values('category').annotate(
        count=Count('id')
    ).order_by('-count')

    # Messages par priorité
    messages_by_priority = ContactMessage.objects.values('priority').annotate(
        count=Count('id')
    ).order_by('-count')

    # Messages récents (7 derniers jours)
    week_ago = datetime.now() - timedelta(days=7)
    recent_messages = ContactMessage.objects.filter(
        created_at__gte=week_ago
    ).count()

    # Messages en retard (plus de 48h sans réponse)
    overdue_messages = ContactMessage.objects.filter(
        status__in=['new', 'in_progress'],
        created_at__lt=datetime.now() - timedelta(hours=48)
    ).count()

    # Temps de réponse moyen (pour les messages résolus)
    resolved_with_time = ContactMessage.objects.filter(
        status='resolved',
        resolved_at__isnull=False
    )

    avg_response_time = None
    if resolved_with_time.exists():
        total_time = sum([
            (msg.resolved_at - msg.created_at).total_seconds()
            for msg in resolved_with_time
        ])
        avg_response_time = total_time / resolved_with_time.count() / 3600  # en heures

    return Response({
        'total_messages': total_messages,
        'status_breakdown': {
            'new': new_messages,
            'in_progress': in_progress_messages,
            'resolved': resolved_messages,
            'closed': total_messages - new_messages - in_progress_messages - resolved_messages
        },
        'messages_by_category': messages_by_category,
        'messages_by_priority': messages_by_priority,
        'recent_messages': recent_messages,
        'overdue_messages': overdue_messages,
        'avg_response_time_hours': round(avg_response_time, 2) if avg_response_time else None
    })


def get_client_ip(request):
    """Obtenir l'adresse IP du client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_request(request):
    """Demander une réinitialisation de mot de passe"""
    email = request.data.get('email')

    if not email:
        return Response({
            'success': False,
            'error': 'L\'adresse email est requise'
        }, status=status.HTTP_400_BAD_REQUEST)

    User = get_user_model()

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        # Pour des raisons de sécurité, on ne révèle pas si l'email existe ou non
        return Response({
            'success': True,
            'message': 'Si cette adresse email est associée à un compte, vous recevrez un email avec les instructions de réinitialisation.'
        })

    # Générer le token de réinitialisation
    token = default_token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))

    # Créer le lien de réinitialisation
    reset_url = f"http://localhost:3001/reset-password/{uid}/{token}/"

    # Envoyer l'email
    subject = 'Réinitialisation de votre mot de passe HandyMath'
    message = f"""
Bonjour {user.first_name or user.username},

Vous avez demandé la réinitialisation de votre mot de passe pour votre compte HandyMath.

Cliquez sur le lien suivant pour réinitialiser votre mot de passe :
{reset_url}

Ce lien est valide pendant 24 heures.

Si vous n'avez pas demandé cette réinitialisation, vous pouvez ignorer cet email.

L'équipe HandyMath
"""

    try:
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [email],
            fail_silently=False,
        )
    except Exception as e:
        print(f"Erreur lors de l'envoi de l'email: {e}")
        # En développement, on peut afficher le lien dans la console
        print(f"Lien de réinitialisation: {reset_url}")

    return Response({
        'success': True,
        'message': 'Si cette adresse email est associée à un compte, vous recevrez un email avec les instructions de réinitialisation.'
    })


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_confirm(request):
    """Confirmer la réinitialisation de mot de passe"""
    uid = request.data.get('uid')
    token = request.data.get('token')
    new_password = request.data.get('new_password')

    if not all([uid, token, new_password]):
        return Response({
            'success': False,
            'error': 'Tous les champs sont requis'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Décoder l'UID
        user_id = force_str(urlsafe_base64_decode(uid))
        User = get_user_model()
        user = User.objects.get(pk=user_id)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        return Response({
            'success': False,
            'error': 'Lien de réinitialisation invalide'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Vérifier le token
    if not default_token_generator.check_token(user, token):
        return Response({
            'success': False,
            'error': 'Lien de réinitialisation invalide ou expiré'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Valider le nouveau mot de passe
    if len(new_password) < 8:
        return Response({
            'success': False,
            'error': 'Le mot de passe doit contenir au moins 8 caractères'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Mettre à jour le mot de passe
    user.set_password(new_password)
    user.save()

    return Response({
        'success': True,
        'message': 'Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter.'
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def password_reset_validate(request, uid, token):
    """Valider un lien de réinitialisation de mot de passe"""
    try:
        # Décoder l'UID
        user_id = force_str(urlsafe_base64_decode(uid))
        User = get_user_model()
        user = User.objects.get(pk=user_id)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        return Response({
            'valid': False,
            'error': 'Lien de réinitialisation invalide'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Vérifier le token
    if not default_token_generator.check_token(user, token):
        return Response({
            'valid': False,
            'error': 'Lien de réinitialisation invalide ou expiré'
        }, status=status.HTTP_400_BAD_REQUEST)

    return Response({
        'valid': True,
        'user_email': user.email
    })