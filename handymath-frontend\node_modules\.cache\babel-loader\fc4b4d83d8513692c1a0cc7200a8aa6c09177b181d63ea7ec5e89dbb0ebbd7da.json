{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  const {\n    user,\n    updateUser\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n  const [profile, setProfile] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    prenom: '',\n    nom: '',\n    email: '',\n    bio: ''\n  });\n  useEffect(() => {\n    if (user) {\n      fetchProfile();\n    }\n  }, [user]);\n  const fetchProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/users/me/');\n      setProfile(response.data);\n      setFormData({\n        prenom: response.data.prenom || '',\n        nom: response.data.nom || '',\n        email: response.data.email || '',\n        bio: response.data.bio || ''\n      });\n    } catch (error) {\n      console.error('Erreur lors du chargement du profil:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de charger le profil'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSave = async () => {\n    try {\n      const response = await api.patch('/users/me/', formData);\n      setProfile(response.data);\n      setEditing(false);\n      updateUser(response.data);\n      addNotification({\n        type: 'success',\n        title: 'Profil mis à jour',\n        message: 'Vos informations ont été sauvegardées'\n      });\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder le profil'\n      });\n    }\n  };\n  const handleCancel = () => {\n    setFormData({\n      prenom: (profile === null || profile === void 0 ? void 0 : profile.prenom) || '',\n      nom: (profile === null || profile === void 0 ? void 0 : profile.nom) || '',\n      email: (profile === null || profile === void 0 ? void 0 : profile.email) || '',\n      bio: (profile === null || profile === void 0 ? void 0 : profile.bio) || ''\n    });\n    setEditing(false);\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n        title: \"Profil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold mb-4\",\n            children: \"Acc\\xE8s refus\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Vous devez \\xEAtre connect\\xE9 pour voir votre profil.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n        title: \"Profil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Chargement du profil...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"Mon Profil\",\n      showBackButton: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold ${user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'}`,\n                children: ((profile === null || profile === void 0 ? void 0 : profile.prenom) || user.username).charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                  children: profile !== null && profile !== void 0 && profile.prenom && profile !== null && profile !== void 0 && profile.nom ? `${profile.prenom} ${profile.nom}` : profile === null || profile === void 0 ? void 0 : profile.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 dark:text-gray-400\",\n                  children: [\"@\", profile === null || profile === void 0 ? void 0 : profile.username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`,\n                  children: user.role === 'admin' ? 'Administrateur' : 'Étudiant'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setEditing(!editing),\n              className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\",\n              children: editing ? 'Annuler' : 'Modifier'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Pr\\xE9nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), editing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.prenom,\n                onChange: e => setFormData({\n                  ...formData,\n                  prenom: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-white\",\n                children: (profile === null || profile === void 0 ? void 0 : profile.prenom) || 'Non renseigné'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), editing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.nom,\n                onChange: e => setFormData({\n                  ...formData,\n                  nom: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-white\",\n                children: (profile === null || profile === void 0 ? void 0 : profile.nom) || 'Non renseigné'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), editing ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                value: formData.email,\n                onChange: e => setFormData({\n                  ...formData,\n                  email: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-white\",\n                children: profile === null || profile === void 0 ? void 0 : profile.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Membre depuis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900 dark:text-white\",\n                children: profile !== null && profile !== void 0 && profile.date_joined ? new Date(profile.date_joined).toLocaleDateString('fr-FR') : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n              children: \"Biographie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), editing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.bio,\n              onChange: e => setFormData({\n                ...formData,\n                bio: e.target.value\n              }),\n              rows: 3,\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              placeholder: \"Parlez-nous de vous...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-900 dark:text-white\",\n              children: (profile === null || profile === void 0 ? void 0 : profile.bio) || 'Aucune biographie renseignée.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), editing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancel,\n              className: \"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\",\n              children: \"Sauvegarder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), user.role === 'student' && (profile === null || profile === void 0 ? void 0 : profile.stats) && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-white mb-6\",\n            children: \"Mes statistiques\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-blue-600\",\n                children: profile.stats.total_equations\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"\\xC9quations r\\xE9solues\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-green-600\",\n                children: profile.stats.total_exercises\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Exercices compl\\xE9t\\xE9s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-purple-600\",\n                children: profile.stats.total_points\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Points gagn\\xE9s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-orange-600\",\n                children: [Math.round(profile.stats.success_rate), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Taux de r\\xE9ussite\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProfilePage, \"gfut+MOPdADQZZcncvhMbvtoL9o=\", false, function () {\n  return [useAuth, useNotifications];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "useNotifications", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfilePage", "_s", "user", "updateUser", "addNotification", "profile", "setProfile", "loading", "setLoading", "editing", "setEditing", "formData", "setFormData", "prenom", "nom", "email", "bio", "fetchProfile", "response", "get", "data", "error", "console", "type", "title", "message", "handleSave", "patch", "handleCancel", "children", "SimpleHeader", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "showBackButton", "div", "initial", "opacity", "y", "animate", "role", "username", "char<PERSON>t", "toUpperCase", "onClick", "value", "onChange", "e", "target", "date_joined", "Date", "toLocaleDateString", "rows", "placeholder", "stats", "transition", "delay", "total_equations", "total_exercises", "total_points", "Math", "round", "success_rate", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/pages/ProfilePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentLayout from '../components/StudentLayout';\nimport api from '../services/api';\n\ninterface UserProfile {\n  id: number;\n  username: string;\n  email: string;\n  nom: string;\n  prenom: string;\n  role: string;\n  niveau?: string;\n  date_joined?: string;\n  bio?: string;\n  profile_picture?: string;\n  stats?: {\n    total_equations: number;\n    total_exercises: number;\n    total_points: number;\n    success_rate: number;\n  };\n}\n\nconst ProfilePage: React.FC = () => {\n  const { user, updateUser } = useAuth();\n  const { addNotification } = useNotifications();\n\n  const [profile, setProfile] = useState<UserProfile | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    prenom: '',\n    nom: '',\n    email: '',\n    bio: ''\n  });\n\n  useEffect(() => {\n    if (user) {\n      fetchProfile();\n    }\n  }, [user]);\n\n  const fetchProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/users/me/');\n      setProfile(response.data);\n      setFormData({\n        prenom: response.data.prenom || '',\n        nom: response.data.nom || '',\n        email: response.data.email || '',\n        bio: response.data.bio || ''\n      });\n    } catch (error) {\n      console.error('Erreur lors du chargement du profil:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de charger le profil'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      const response = await api.patch('/users/me/', formData);\n      setProfile(response.data);\n      setEditing(false);\n      updateUser(response.data);\n      addNotification({\n        type: 'success',\n        title: 'Profil mis à jour',\n        message: 'Vos informations ont été sauvegardées'\n      });\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de sauvegarder le profil'\n      });\n    }\n  };\n\n  const handleCancel = () => {\n    setFormData({\n      prenom: profile?.prenom || '',\n      nom: profile?.nom || '',\n      email: profile?.email || '',\n      bio: profile?.bio || ''\n    });\n    setEditing(false);\n  };\n\n  if (!user) {\n    return (\n      <>\n        <SimpleHeader title=\"Profil\" />\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold mb-4\">Accès refusé</h1>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Vous devez être connecté pour voir votre profil.\n            </p>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  if (loading) {\n    return (\n      <>\n        <SimpleHeader title=\"Profil\" />\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 dark:text-gray-400\">Chargement du profil...</p>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <SimpleHeader title=\"Mon Profil\" showBackButton />\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* En-tête du profil */}\n          <motion.div\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <div className=\"flex items-center space-x-4\">\n                <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold ${\n                  user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'\n                }`}>\n                  {(profile?.prenom || user.username).charAt(0).toUpperCase()}\n                </div>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {profile?.prenom && profile?.nom\n                      ? `${profile.prenom} ${profile.nom}`\n                      : profile?.username\n                    }\n                  </h1>\n                  <p className=\"text-gray-600 dark:text-gray-400\">@{profile?.username}</p>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                    user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'\n                  }`}>\n                    {user.role === 'admin' ? 'Administrateur' : 'Étudiant'}\n                  </span>\n                </div>\n              </div>\n              <button\n                onClick={() => setEditing(!editing)}\n                className=\"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\"\n              >\n                {editing ? 'Annuler' : 'Modifier'}\n              </button>\n            </div>\n\n            {/* Informations du profil */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Prénom\n                </label>\n                {editing ? (\n                  <input\n                    type=\"text\"\n                    value={formData.prenom}\n                    onChange={(e) => setFormData({...formData, prenom: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-white\">{profile?.prenom || 'Non renseigné'}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nom\n                </label>\n                {editing ? (\n                  <input\n                    type=\"text\"\n                    value={formData.nom}\n                    onChange={(e) => setFormData({...formData, nom: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-white\">{profile?.nom || 'Non renseigné'}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Email\n                </label>\n                {editing ? (\n                  <input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => setFormData({...formData, email: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                ) : (\n                  <p className=\"text-gray-900 dark:text-white\">{profile?.email}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Membre depuis\n                </label>\n                <p className=\"text-gray-900 dark:text-white\">\n                  {profile?.date_joined ? new Date(profile.date_joined).toLocaleDateString('fr-FR') : 'N/A'}\n                </p>\n              </div>\n            </div>\n\n            {/* Bio */}\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Biographie\n              </label>\n              {editing ? (\n                <textarea\n                  value={formData.bio}\n                  onChange={(e) => setFormData({...formData, bio: e.target.value})}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder=\"Parlez-nous de vous...\"\n                />\n              ) : (\n                <p className=\"text-gray-900 dark:text-white\">\n                  {profile?.bio || 'Aucune biographie renseignée.'}\n                </p>\n              )}\n            </div>\n\n            {/* Boutons de sauvegarde */}\n            {editing && (\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={handleCancel}\n                  className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n                >\n                  Annuler\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\"\n                >\n                  Sauvegarder\n                </button>\n              </div>\n            )}\n          </motion.div>\n\n          {/* Statistiques (pour les étudiants) */}\n          {user.role === 'student' && profile?.stats && (\n            <motion.div\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6\">\n                Mes statistiques\n              </h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">{profile.stats.total_equations}</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Équations résolues</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">{profile.stats.total_exercises}</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Exercices complétés</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">{profile.stats.total_points}</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Points gagnés</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-600\">{Math.round(profile.stats.success_rate)}%</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Taux de réussite</div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AAEnE,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqBlC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGV,OAAO,CAAC,CAAC;EACtC,MAAM;IAAEW;EAAgB,CAAC,GAAGV,gBAAgB,CAAC,CAAC;EAE9C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAqB,IAAI,CAAC;EAChE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE;EACP,CAAC,CAAC;EAEFzB,SAAS,CAAC,MAAM;IACd,IAAIW,IAAI,EAAE;MACRe,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;EAEV,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMU,QAAQ,GAAG,MAAMvB,GAAG,CAACwB,GAAG,CAAC,YAAY,CAAC;MAC5Cb,UAAU,CAACY,QAAQ,CAACE,IAAI,CAAC;MACzBR,WAAW,CAAC;QACVC,MAAM,EAAEK,QAAQ,CAACE,IAAI,CAACP,MAAM,IAAI,EAAE;QAClCC,GAAG,EAAEI,QAAQ,CAACE,IAAI,CAACN,GAAG,IAAI,EAAE;QAC5BC,KAAK,EAAEG,QAAQ,CAACE,IAAI,CAACL,KAAK,IAAI,EAAE;QAChCC,GAAG,EAAEE,QAAQ,CAACE,IAAI,CAACJ,GAAG,IAAI;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DjB,eAAe,CAAC;QACdmB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMvB,GAAG,CAACgC,KAAK,CAAC,YAAY,EAAEhB,QAAQ,CAAC;MACxDL,UAAU,CAACY,QAAQ,CAACE,IAAI,CAAC;MACzBV,UAAU,CAAC,KAAK,CAAC;MACjBP,UAAU,CAACe,QAAQ,CAACE,IAAI,CAAC;MACzBhB,eAAe,CAAC;QACdmB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDjB,eAAe,CAAC;QACdmB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzBhB,WAAW,CAAC;MACVC,MAAM,EAAE,CAAAR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAI,EAAE;MAC7BC,GAAG,EAAE,CAAAT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,GAAG,KAAI,EAAE;MACvBC,KAAK,EAAE,CAAAV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAI,EAAE;MAC3BC,GAAG,EAAE,CAAAX,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEW,GAAG,KAAI;IACvB,CAAC,CAAC;IACFN,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,IAAI,CAACR,IAAI,EAAE;IACT,oBACEL,OAAA,CAAAE,SAAA;MAAA8B,QAAA,gBACEhC,OAAA,CAACiC,YAAY;QAACN,KAAK,EAAC;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/BrC,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAN,QAAA,eAC1ChC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAN,QAAA,gBAC1BhC,OAAA;YAAIsC,SAAS,EAAC,yBAAyB;YAAAN,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDrC,OAAA;YAAGsC,SAAS,EAAC,kCAAkC;YAAAN,QAAA,EAAC;UAEhD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,IAAI3B,OAAO,EAAE;IACX,oBACEV,OAAA,CAAAE,SAAA;MAAA8B,QAAA,gBACEhC,OAAA,CAACiC,YAAY;QAACN,KAAK,EAAC;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/BrC,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAN,QAAA,eAC1ChC,OAAA;UAAKsC,SAAS,EAAC,mBAAmB;UAAAN,QAAA,gBAChChC,OAAA;YAAKsC,SAAS,EAAC;UAAgF;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGrC,OAAA;YAAGsC,SAAS,EAAC,kCAAkC;YAAAN,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,oBACErC,OAAA,CAAAE,SAAA;IAAA8B,QAAA,gBACEhC,OAAA,CAACiC,YAAY;MAACN,KAAK,EAAC,YAAY;MAACY,cAAc;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClDrC,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAN,QAAA,eAC1ChC,OAAA;QAAKsC,SAAS,EAAC,mBAAmB;QAAAN,QAAA,gBAEhChC,OAAA,CAACL,MAAM,CAAC6C,GAAG;UACTF,SAAS,EAAC,yDAAyD;UACnEG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAE9BhC,OAAA;YAAKsC,SAAS,EAAC,wCAAwC;YAAAN,QAAA,gBACrDhC,OAAA;cAAKsC,SAAS,EAAC,6BAA6B;cAAAN,QAAA,gBAC1ChC,OAAA;gBAAKsC,SAAS,EAAE,yFACdjC,IAAI,CAACwC,IAAI,KAAK,OAAO,GAAG,YAAY,GAAG,aAAa,EACnD;gBAAAb,QAAA,EACA,CAAC,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAIX,IAAI,CAACyC,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNrC,OAAA;gBAAAgC,QAAA,gBACEhC,OAAA;kBAAIsC,SAAS,EAAC,kDAAkD;kBAAAN,QAAA,EAC7DxB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEQ,MAAM,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,GAAG,GAC5B,GAAGT,OAAO,CAACQ,MAAM,IAAIR,OAAO,CAACS,GAAG,EAAE,GAClCT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CAAC,eACLrC,OAAA;kBAAGsC,SAAS,EAAC,kCAAkC;kBAAAN,QAAA,GAAC,GAAC,EAACxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,QAAQ;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxErC,OAAA;kBAAMsC,SAAS,EAAE,4DACfjC,IAAI,CAACwC,IAAI,KAAK,OAAO,GAAG,yBAAyB,GAAG,2BAA2B,EAC9E;kBAAAb,QAAA,EACA3B,IAAI,CAACwC,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG;gBAAU;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrC,OAAA;cACEiD,OAAO,EAAEA,CAAA,KAAMpC,UAAU,CAAC,CAACD,OAAO,CAAE;cACpC0B,SAAS,EAAC,uFAAuF;cAAAN,QAAA,EAEhGpB,OAAO,GAAG,SAAS,GAAG;YAAU;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrC,OAAA;YAAKsC,SAAS,EAAC,uCAAuC;YAAAN,QAAA,gBACpDhC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPzB,OAAO,gBACNZ,OAAA;gBACE0B,IAAI,EAAC,MAAM;gBACXwB,KAAK,EAAEpC,QAAQ,CAACE,MAAO;gBACvBmC,QAAQ,EAAGC,CAAC,IAAKrC,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEE,MAAM,EAAEoC,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACpEZ,SAAS,EAAC;cAAiI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5I,CAAC,gBAEFrC,OAAA;gBAAGsC,SAAS,EAAC,+BAA+B;gBAAAN,QAAA,EAAE,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,KAAI;cAAe;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACrF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENrC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPzB,OAAO,gBACNZ,OAAA;gBACE0B,IAAI,EAAC,MAAM;gBACXwB,KAAK,EAAEpC,QAAQ,CAACG,GAAI;gBACpBkC,QAAQ,EAAGC,CAAC,IAAKrC,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEG,GAAG,EAAEmC,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACjEZ,SAAS,EAAC;cAAiI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5I,CAAC,gBAEFrC,OAAA;gBAAGsC,SAAS,EAAC,+BAA+B;gBAAAN,QAAA,EAAE,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,GAAG,KAAI;cAAe;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAClF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENrC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACPzB,OAAO,gBACNZ,OAAA;gBACE0B,IAAI,EAAC,OAAO;gBACZwB,KAAK,EAAEpC,QAAQ,CAACI,KAAM;gBACtBiC,QAAQ,EAAGC,CAAC,IAAKrC,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEI,KAAK,EAAEkC,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACnEZ,SAAS,EAAC;cAAiI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5I,CAAC,gBAEFrC,OAAA;gBAAGsC,SAAS,EAAC,+BAA+B;gBAAAN,QAAA,EAAExB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACjE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENrC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAN,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrC,OAAA;gBAAGsC,SAAS,EAAC,+BAA+B;gBAAAN,QAAA,EACzCxB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE8C,WAAW,GAAG,IAAIC,IAAI,CAAC/C,OAAO,CAAC8C,WAAW,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC,GAAG;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrC,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAN,QAAA,gBACnBhC,OAAA;cAAOsC,SAAS,EAAC,iEAAiE;cAAAN,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACPzB,OAAO,gBACNZ,OAAA;cACEkD,KAAK,EAAEpC,QAAQ,CAACK,GAAI;cACpBgC,QAAQ,EAAGC,CAAC,IAAKrC,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEK,GAAG,EAAEiC,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACjEO,IAAI,EAAE,CAAE;cACRnB,SAAS,EAAC,iIAAiI;cAC3IoB,WAAW,EAAC;YAAwB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,gBAEFrC,OAAA;cAAGsC,SAAS,EAAC,+BAA+B;cAAAN,QAAA,EACzC,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEW,GAAG,KAAI;YAA+B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLzB,OAAO,iBACNZ,OAAA;YAAKsC,SAAS,EAAC,iCAAiC;YAAAN,QAAA,gBAC9ChC,OAAA;cACEiD,OAAO,EAAElB,YAAa;cACtBO,SAAS,EAAC,2GAA2G;cAAAN,QAAA,EACtH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrC,OAAA;cACEiD,OAAO,EAAEpB,UAAW;cACpBS,SAAS,EAAC,uFAAuF;cAAAN,QAAA,EAClG;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,EAGZhC,IAAI,CAACwC,IAAI,KAAK,SAAS,KAAIrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmD,KAAK,kBACxC3D,OAAA,CAACL,MAAM,CAAC6C,GAAG;UACTF,SAAS,EAAC,oDAAoD;UAC9DG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BiB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAA7B,QAAA,gBAE3BhC,OAAA;YAAIsC,SAAS,EAAC,sDAAsD;YAAAN,QAAA,EAAC;UAErE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAKsC,SAAS,EAAC,uCAAuC;YAAAN,QAAA,gBACpDhC,OAAA;cAAKsC,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAC1BhC,OAAA;gBAAKsC,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAExB,OAAO,CAACmD,KAAK,CAACG;cAAe;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvFrC,OAAA;gBAAKsC,SAAS,EAAC,0CAA0C;gBAAAN,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACNrC,OAAA;cAAKsC,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAC1BhC,OAAA;gBAAKsC,SAAS,EAAC,mCAAmC;gBAAAN,QAAA,EAAExB,OAAO,CAACmD,KAAK,CAACI;cAAe;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxFrC,OAAA;gBAAKsC,SAAS,EAAC,0CAA0C;gBAAAN,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACNrC,OAAA;cAAKsC,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAC1BhC,OAAA;gBAAKsC,SAAS,EAAC,oCAAoC;gBAAAN,QAAA,EAAExB,OAAO,CAACmD,KAAK,CAACK;cAAY;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtFrC,OAAA;gBAAKsC,SAAS,EAAC,0CAA0C;gBAAAN,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACNrC,OAAA;cAAKsC,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAC1BhC,OAAA;gBAAKsC,SAAS,EAAC,oCAAoC;gBAAAN,QAAA,GAAEiC,IAAI,CAACC,KAAK,CAAC1D,OAAO,CAACmD,KAAK,CAACQ,YAAY,CAAC,EAAC,GAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnGrC,OAAA;gBAAKsC,SAAS,EAAC,0CAA0C;gBAAAN,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACjC,EAAA,CAvRID,WAAqB;EAAA,QACIP,OAAO,EACRC,gBAAgB;AAAA;AAAAuE,EAAA,GAFxCjE,WAAqB;AAyR3B,eAAeA,WAAW;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}