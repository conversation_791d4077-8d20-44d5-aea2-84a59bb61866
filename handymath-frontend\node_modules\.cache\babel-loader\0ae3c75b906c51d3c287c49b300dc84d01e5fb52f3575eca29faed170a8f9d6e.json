{"ast": null, "code": "/**\n * Compares two BigNumbers.\n * @param {BigNumber} a - First value to compare\n * @param {BigNumber} b - Second value to compare\n * @param {number} [relTol=1e-09] - The relative tolerance, indicating the maximum allowed difference relative to the larger absolute value. Must be greater than 0.\n * @param {number} [absTol=0] - The minimum absolute tolerance, useful for comparisons near zero. Must be at least 0.\n * @returns {boolean} whether the two numbers are nearly equal\n * @throws {Error} If `relTol` is less than or equal to 0.\n * @throws {Error} If `absTol` is less than 0.\n *\n * @example\n * nearlyEqual(1.000000001, 1.0, 1e-9);            // true\n * nearlyEqual(1.000000002, 1.0, 0);            // false\n * nearlyEqual(1.0, 1.009, undefined, 0.02);       // true\n * nearlyEqual(0.000000001, 0.0, undefined, 1e-8); // true\n */\nexport function nearlyEqual(a, b) {\n  var relTol = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1e-9;\n  var absTol = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  if (relTol <= 0) {\n    throw new Error('Relative tolerance must be greater than 0');\n  }\n  if (absTol < 0) {\n    throw new Error('Absolute tolerance must be at least 0');\n  }\n  // NaN\n  if (a.isNaN() || b.isNaN()) {\n    return false;\n  }\n  if (!a.isFinite() || !b.isFinite()) {\n    return a.eq(b);\n  }\n  // use \"==\" operator, handles infinities\n  if (a.eq(b)) {\n    return true;\n  }\n  // abs(a-b) <= max(relTol * max(abs(a), abs(b)), absTol)\n  return a.minus(b).abs().lte(a.constructor.max(a.constructor.max(a.abs(), b.abs()).mul(relTol), absTol));\n}", "map": {"version": 3, "names": ["nearlyEqual", "a", "b", "relTol", "arguments", "length", "undefined", "absTol", "Error", "isNaN", "isFinite", "eq", "minus", "abs", "lte", "constructor", "max", "mul"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/bignumber/nearlyEqual.js"], "sourcesContent": ["/**\n * Compares two BigNumbers.\n * @param {BigNumber} a - First value to compare\n * @param {BigNumber} b - Second value to compare\n * @param {number} [relTol=1e-09] - The relative tolerance, indicating the maximum allowed difference relative to the larger absolute value. Must be greater than 0.\n * @param {number} [absTol=0] - The minimum absolute tolerance, useful for comparisons near zero. Must be at least 0.\n * @returns {boolean} whether the two numbers are nearly equal\n * @throws {Error} If `relTol` is less than or equal to 0.\n * @throws {Error} If `absTol` is less than 0.\n *\n * @example\n * nearlyEqual(1.000000001, 1.0, 1e-9);            // true\n * nearlyEqual(1.000000002, 1.0, 0);            // false\n * nearlyEqual(1.0, 1.009, undefined, 0.02);       // true\n * nearlyEqual(0.000000001, 0.0, undefined, 1e-8); // true\n */\nexport function nearlyEqual(a, b) {\n  var relTol = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1e-9;\n  var absTol = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  if (relTol <= 0) {\n    throw new Error('Relative tolerance must be greater than 0');\n  }\n  if (absTol < 0) {\n    throw new Error('Absolute tolerance must be at least 0');\n  }\n  // NaN\n  if (a.isNaN() || b.isNaN()) {\n    return false;\n  }\n  if (!a.isFinite() || !b.isFinite()) {\n    return a.eq(b);\n  }\n  // use \"==\" operator, handles infinities\n  if (a.eq(b)) {\n    return true;\n  }\n  // abs(a-b) <= max(relTol * max(abs(a), abs(b)), absTol)\n  return a.minus(b).abs().lte(a.constructor.max(a.constructor.max(a.abs(), b.abs()).mul(relTol), absTol));\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACrF,IAAIG,MAAM,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAID,MAAM,IAAI,CAAC,EAAE;IACf,MAAM,IAAIK,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EACA,IAAID,MAAM,GAAG,CAAC,EAAE;IACd,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EACA;EACA,IAAIP,CAAC,CAACQ,KAAK,CAAC,CAAC,IAAIP,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE;IAC1B,OAAO,KAAK;EACd;EACA,IAAI,CAACR,CAAC,CAACS,QAAQ,CAAC,CAAC,IAAI,CAACR,CAAC,CAACQ,QAAQ,CAAC,CAAC,EAAE;IAClC,OAAOT,CAAC,CAACU,EAAE,CAACT,CAAC,CAAC;EAChB;EACA;EACA,IAAID,CAAC,CAACU,EAAE,CAACT,CAAC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EACA;EACA,OAAOD,CAAC,CAACW,KAAK,CAACV,CAAC,CAAC,CAACW,GAAG,CAAC,CAAC,CAACC,GAAG,CAACb,CAAC,CAACc,WAAW,CAACC,GAAG,CAACf,CAAC,CAACc,WAAW,CAACC,GAAG,CAACf,CAAC,CAACY,GAAG,CAAC,CAAC,EAAEX,CAAC,CAACW,GAAG,CAAC,CAAC,CAAC,CAACI,GAAG,CAACd,MAAM,CAAC,EAAEI,MAAM,CAAC,CAAC;AACzG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}