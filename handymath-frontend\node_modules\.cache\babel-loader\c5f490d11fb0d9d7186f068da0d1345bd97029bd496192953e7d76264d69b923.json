{"ast": null, "code": "import Emitter from 'tiny-emitter';\n\n/**\n * Extend given object with emitter functions `on`, `off`, `once`, `emit`\n * @param {Object} obj\n * @return {Object} obj\n */\nexport function mixin(obj) {\n  // create event emitter\n  var emitter = new Emitter();\n\n  // bind methods to obj (we don't want to expose the emitter.e Array...)\n  obj.on = emitter.on.bind(emitter);\n  obj.off = emitter.off.bind(emitter);\n  obj.once = emitter.once.bind(emitter);\n  obj.emit = emitter.emit.bind(emitter);\n  return obj;\n}", "map": {"version": 3, "names": ["Emitter", "mixin", "obj", "emitter", "on", "bind", "off", "once", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/emitter.js"], "sourcesContent": ["import Emitter from 'tiny-emitter';\n\n/**\n * Extend given object with emitter functions `on`, `off`, `once`, `emit`\n * @param {Object} obj\n * @return {Object} obj\n */\nexport function mixin(obj) {\n  // create event emitter\n  var emitter = new Emitter();\n\n  // bind methods to obj (we don't want to expose the emitter.e Array...)\n  obj.on = emitter.on.bind(emitter);\n  obj.off = emitter.off.bind(emitter);\n  obj.once = emitter.once.bind(emitter);\n  obj.emit = emitter.emit.bind(emitter);\n  return obj;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,GAAG,EAAE;EACzB;EACA,IAAIC,OAAO,GAAG,IAAIH,OAAO,CAAC,CAAC;;EAE3B;EACAE,GAAG,CAACE,EAAE,GAAGD,OAAO,CAACC,EAAE,CAACC,IAAI,CAACF,OAAO,CAAC;EACjCD,GAAG,CAACI,GAAG,GAAGH,OAAO,CAACG,GAAG,CAACD,IAAI,CAACF,OAAO,CAAC;EACnCD,GAAG,CAACK,IAAI,GAAGJ,OAAO,CAACI,IAAI,CAACF,IAAI,CAACF,OAAO,CAAC;EACrCD,GAAG,CAACM,IAAI,GAAGL,OAAO,CAACK,IAAI,CAACH,IAAI,CAACF,OAAO,CAAC;EACrC,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}