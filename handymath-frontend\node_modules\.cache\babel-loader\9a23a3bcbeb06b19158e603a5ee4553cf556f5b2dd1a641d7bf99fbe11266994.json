{"ast": null, "code": "export var atanDocs = {\n  name: 'atan',\n  category: 'Trigonometry',\n  syntax: ['atan(x)'],\n  description: 'Compute the inverse tangent of a value in radians.',\n  examples: ['atan(0.5)', 'atan(tan(0.5))'],\n  seealso: ['tan', 'acos', 'asin']\n};", "map": {"version": 3, "names": ["atanDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/atan.js"], "sourcesContent": ["export var atanDocs = {\n  name: 'atan',\n  category: 'Trigonometry',\n  syntax: ['atan(x)'],\n  description: 'Compute the inverse tangent of a value in radians.',\n  examples: ['atan(0.5)', 'atan(tan(0.5))'],\n  seealso: ['tan', 'acos', 'asin']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,oDAAoD;EACjEC,QAAQ,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;EACzCC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}