{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Depth-first search and postorder of a tree rooted at node j\n *\n * @param {Number}  j               The tree node\n * @param {Number}  k\n * @param {Array}   w               The workspace array\n * @param {Number}  head            The index offset within the workspace for the head array\n * @param {Number}  next            The index offset within the workspace for the next array\n * @param {Array}   post            The post ordering array\n * @param {Number}  stack           The index offset within the workspace for the stack array\n */\nexport function csTdfs(j, k, w, head, next, post, stack) {\n  // variables\n  var top = 0;\n  // place j on the stack\n  w[stack] = j;\n  // while (stack is not empty)\n  while (top >= 0) {\n    // p = top of stack\n    var p = w[stack + top];\n    // i = youngest child of p\n    var i = w[head + p];\n    if (i === -1) {\n      // p has no unordered children left\n      top--;\n      // node p is the kth postordered node\n      post[k++] = p;\n    } else {\n      // remove i from children of p\n      w[head + p] = w[next + i];\n      // increment top\n      ++top;\n      // start dfs on child node i\n      w[stack + top] = i;\n    }\n  }\n  return k;\n}", "map": {"version": 3, "names": ["csTdfs", "j", "k", "w", "head", "next", "post", "stack", "top", "p", "i"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csTdfs.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Depth-first search and postorder of a tree rooted at node j\n *\n * @param {Number}  j               The tree node\n * @param {Number}  k\n * @param {Array}   w               The workspace array\n * @param {Number}  head            The index offset within the workspace for the head array\n * @param {Number}  next            The index offset within the workspace for the next array\n * @param {Array}   post            The post ordering array\n * @param {Number}  stack           The index offset within the workspace for the stack array\n */\nexport function csTdfs(j, k, w, head, next, post, stack) {\n  // variables\n  var top = 0;\n  // place j on the stack\n  w[stack] = j;\n  // while (stack is not empty)\n  while (top >= 0) {\n    // p = top of stack\n    var p = w[stack + top];\n    // i = youngest child of p\n    var i = w[head + p];\n    if (i === -1) {\n      // p has no unordered children left\n      top--;\n      // node p is the kth postordered node\n      post[k++] = p;\n    } else {\n      // remove i from children of p\n      w[head + p] = w[next + i];\n      // increment top\n      ++top;\n      // start dfs on child node i\n      w[stack + top] = i;\n    }\n  }\n  return k;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACvD;EACA,IAAIC,GAAG,GAAG,CAAC;EACX;EACAL,CAAC,CAACI,KAAK,CAAC,GAAGN,CAAC;EACZ;EACA,OAAOO,GAAG,IAAI,CAAC,EAAE;IACf;IACA,IAAIC,CAAC,GAAGN,CAAC,CAACI,KAAK,GAAGC,GAAG,CAAC;IACtB;IACA,IAAIE,CAAC,GAAGP,CAAC,CAACC,IAAI,GAAGK,CAAC,CAAC;IACnB,IAAIC,CAAC,KAAK,CAAC,CAAC,EAAE;MACZ;MACAF,GAAG,EAAE;MACL;MACAF,IAAI,CAACJ,CAAC,EAAE,CAAC,GAAGO,CAAC;IACf,CAAC,MAAM;MACL;MACAN,CAAC,CAACC,IAAI,GAAGK,CAAC,CAAC,GAAGN,CAAC,CAACE,IAAI,GAAGK,CAAC,CAAC;MACzB;MACA,EAAEF,GAAG;MACL;MACAL,CAAC,CAACI,KAAK,GAAGC,GAAG,CAAC,GAAGE,CAAC;IACpB;EACF;EACA,OAAOR,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}