{"ast": null, "code": "import { isInteger } from '../../utils/number.js';\nvar n1 = 'number';\nexport function isIntegerNumber(x) {\n  return isInteger(x);\n}\nisIntegerNumber.signature = n1;\nexport function isNegativeNumber(x) {\n  return x < 0;\n}\nisNegativeNumber.signature = n1;\nexport function isPositiveNumber(x) {\n  return x > 0;\n}\nisPositiveNumber.signature = n1;\nexport function isZeroNumber(x) {\n  return x === 0;\n}\nisZeroNumber.signature = n1;\nexport function isNaNNumber(x) {\n  return Number.isNaN(x);\n}\nisNaNNumber.signature = n1;", "map": {"version": 3, "names": ["isInteger", "n1", "isIntegerNumber", "x", "signature", "isNegativeNumber", "isPositiveNumber", "isZeroNumber", "isNaNNumber", "Number", "isNaN"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/utils.js"], "sourcesContent": ["import { isInteger } from '../../utils/number.js';\nvar n1 = 'number';\nexport function isIntegerNumber(x) {\n  return isInteger(x);\n}\nisIntegerNumber.signature = n1;\nexport function isNegativeNumber(x) {\n  return x < 0;\n}\nisNegativeNumber.signature = n1;\nexport function isPositiveNumber(x) {\n  return x > 0;\n}\nisPositiveNumber.signature = n1;\nexport function isZeroNumber(x) {\n  return x === 0;\n}\nisZeroNumber.signature = n1;\nexport function isNaNNumber(x) {\n  return Number.isNaN(x);\n}\nisNaNNumber.signature = n1;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,uBAAuB;AACjD,IAAIC,EAAE,GAAG,QAAQ;AACjB,OAAO,SAASC,eAAeA,CAACC,CAAC,EAAE;EACjC,OAAOH,SAAS,CAACG,CAAC,CAAC;AACrB;AACAD,eAAe,CAACE,SAAS,GAAGH,EAAE;AAC9B,OAAO,SAASI,gBAAgBA,CAACF,CAAC,EAAE;EAClC,OAAOA,CAAC,GAAG,CAAC;AACd;AACAE,gBAAgB,CAACD,SAAS,GAAGH,EAAE;AAC/B,OAAO,SAASK,gBAAgBA,CAACH,CAAC,EAAE;EAClC,OAAOA,CAAC,GAAG,CAAC;AACd;AACAG,gBAAgB,CAACF,SAAS,GAAGH,EAAE;AAC/B,OAAO,SAASM,YAAYA,CAACJ,CAAC,EAAE;EAC9B,OAAOA,CAAC,KAAK,CAAC;AAChB;AACAI,YAAY,CAACH,SAAS,GAAGH,EAAE;AAC3B,OAAO,SAASO,WAAWA,CAACL,CAAC,EAAE;EAC7B,OAAOM,MAAM,CAACC,KAAK,CAACP,CAAC,CAAC;AACxB;AACAK,WAAW,CAACJ,SAAS,GAAGH,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}