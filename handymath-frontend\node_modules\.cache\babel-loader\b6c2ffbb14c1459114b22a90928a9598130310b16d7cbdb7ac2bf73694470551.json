{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath codeF\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\FloatingNavigation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FloatingNavigation = ({\n  items,\n  position = 'bottom-right',\n  className = ''\n}) => {\n  _s();\n  const location = useLocation();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [activeItem, setActiveItem] = useState('');\n  useEffect(() => {\n    // Déterminer l'élément actif basé sur l'URL actuelle\n    const currentItem = items.find(item => !item.isSeparator && !item.isAction && (location.pathname === item.path || item.path !== '/' && item.path && location.pathname.startsWith(item.path)));\n    setActiveItem((currentItem === null || currentItem === void 0 ? void 0 : currentItem.id) || '');\n  }, [location.pathname, items]);\n\n  // Gestion des raccourcis clavier\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.ctrlKey || event.metaKey) {\n        const item = items.find(item => item.shortcut === event.key.toLowerCase());\n        if (item) {\n          event.preventDefault();\n          window.location.href = item.path;\n        }\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [items]);\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'bottom-6 left-6';\n      case 'bottom-center':\n        return 'bottom-6 left-1/2 transform -translate-x-1/2';\n      case 'bottom-right':\n      default:\n        return 'bottom-6 right-6';\n    }\n  };\n  const getExpandDirection = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'flex-col-reverse';\n      case 'bottom-center':\n        return 'flex-row-reverse';\n      case 'bottom-right':\n      default:\n        return 'flex-col-reverse';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `fixed z-50 ${getPositionClasses()} ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex ${getExpandDirection()} items-center space-y-3`,\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isExpanded && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `flex ${getExpandDirection()} space-y-2`,\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          exit: {\n            opacity: 0,\n            scale: 0.8\n          },\n          transition: {\n            duration: 0.2\n          },\n          children: items.map((item, index) => {\n            const isActive = activeItem === item.id;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: 20\n              },\n              transition: {\n                delay: index * 0.05\n              },\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.path,\n                className: `group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${isActive ? 'bg-primary-600 text-white scale-110' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'}`,\n                onClick: () => setIsExpanded(false),\n                title: `${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\",\n                  children: [item.label, item.shortcut && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-xs opacity-75\",\n                    children: [\"Ctrl+\", item.shortcut.toUpperCase()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 21\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n        onClick: () => setIsExpanded(!isExpanded),\n        className: `flex items-center justify-center w-14 h-14 rounded-full shadow-lg transition-all duration-200 ${isExpanded ? 'bg-red-500 hover:bg-red-600 text-white rotate-45' : 'bg-primary-600 hover:bg-primary-700 text-white hover:scale-105'}`,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        title: isExpanded ? 'Fermer le menu' : 'Ouvrir le menu de navigation',\n        children: isExpanded ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4 6h16M4 12h16M4 18h16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingNavigation, \"pscPBzHr3iHEy25M6rbgTEHpc7U=\", false, function () {\n  return [useLocation];\n});\n_c = FloatingNavigation;\nexport default FloatingNavigation;\nvar _c;\n$RefreshReg$(_c, \"FloatingNavigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "FloatingNavigation", "items", "position", "className", "_s", "location", "isExpanded", "setIsExpanded", "activeItem", "setActiveItem", "currentItem", "find", "item", "isSeparator", "isAction", "pathname", "path", "startsWith", "id", "handleKeyDown", "event", "ctrl<PERSON>ey", "metaKey", "shortcut", "key", "toLowerCase", "preventDefault", "window", "href", "addEventListener", "removeEventListener", "getPositionClasses", "getExpandDirection", "children", "div", "initial", "opacity", "scale", "animate", "exit", "transition", "duration", "map", "index", "isActive", "y", "delay", "to", "onClick", "title", "label", "toUpperCase", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "whileTap", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/src/components/FloatingNavigation.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface FloatingNavItem {\n  id: string;\n  label: string;\n  path: string;\n  icon: string;\n  shortcut?: string;\n  isSeparator?: boolean;\n  isAction?: boolean;\n  action?: () => void;\n}\n\ninterface FloatingNavigationProps {\n  items: FloatingNavItem[];\n  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';\n  className?: string;\n}\n\nconst FloatingNavigation: React.FC<FloatingNavigationProps> = ({\n  items,\n  position = 'bottom-right',\n  className = ''\n}) => {\n  const location = useLocation();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [activeItem, setActiveItem] = useState<string>('');\n\n  useEffect(() => {\n    // Déterminer l'élément actif basé sur l'URL actuelle\n    const currentItem = items.find(item =>\n      !item.isSeparator && !item.isAction &&\n      (location.pathname === item.path ||\n      (item.path !== '/' && item.path && location.pathname.startsWith(item.path)))\n    );\n    setActiveItem(currentItem?.id || '');\n  }, [location.pathname, items]);\n\n  // Gestion des raccourcis clavier\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.ctrlKey || event.metaKey) {\n        const item = items.find(item => item.shortcut === event.key.toLowerCase());\n        if (item) {\n          event.preventDefault();\n          window.location.href = item.path;\n        }\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [items]);\n\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'bottom-6 left-6';\n      case 'bottom-center':\n        return 'bottom-6 left-1/2 transform -translate-x-1/2';\n      case 'bottom-right':\n      default:\n        return 'bottom-6 right-6';\n    }\n  };\n\n  const getExpandDirection = () => {\n    switch (position) {\n      case 'bottom-left':\n        return 'flex-col-reverse';\n      case 'bottom-center':\n        return 'flex-row-reverse';\n      case 'bottom-right':\n      default:\n        return 'flex-col-reverse';\n    }\n  };\n\n  return (\n    <div className={`fixed z-50 ${getPositionClasses()} ${className}`}>\n      <div className={`flex ${getExpandDirection()} items-center space-y-3`}>\n        {/* Menu items */}\n        <AnimatePresence>\n          {isExpanded && (\n            <motion.div\n              className={`flex ${getExpandDirection()} space-y-2`}\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              transition={{ duration: 0.2 }}\n            >\n              {items.map((item, index) => {\n                const isActive = activeItem === item.id;\n                \n                return (\n                  <motion.div\n                    key={item.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: 20 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      to={item.path}\n                      className={`group relative flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 ${\n                        isActive\n                          ? 'bg-primary-600 text-white scale-110'\n                          : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-primary-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 hover:scale-105'\n                      }`}\n                      onClick={() => setIsExpanded(false)}\n                      title={`${item.label}${item.shortcut ? ` (Ctrl+${item.shortcut.toUpperCase()})` : ''}`}\n                    >\n                      <span className=\"text-lg\">{item.icon}</span>\n                      \n                      {/* Tooltip */}\n                      <div className=\"absolute right-full mr-3 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap\">\n                        {item.label}\n                        {item.shortcut && (\n                          <span className=\"ml-2 text-xs opacity-75\">\n                            Ctrl+{item.shortcut.toUpperCase()}\n                          </span>\n                        )}\n                        <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700\"></div>\n                      </div>\n                    </Link>\n                  </motion.div>\n                );\n              })}\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Toggle button */}\n        <motion.button\n          onClick={() => setIsExpanded(!isExpanded)}\n          className={`flex items-center justify-center w-14 h-14 rounded-full shadow-lg transition-all duration-200 ${\n            isExpanded\n              ? 'bg-red-500 hover:bg-red-600 text-white rotate-45'\n              : 'bg-primary-600 hover:bg-primary-700 text-white hover:scale-105'\n          }`}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          title={isExpanded ? 'Fermer le menu' : 'Ouvrir le menu de navigation'}\n        >\n          {isExpanded ? (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n          ) : (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          )}\n        </motion.button>\n      </div>\n    </div>\n  );\n};\n\nexport default FloatingNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmBxD,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,KAAK;EACLC,QAAQ,GAAG,cAAc;EACzBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiB,WAAW,GAAGT,KAAK,CAACU,IAAI,CAACC,IAAI,IACjC,CAACA,IAAI,CAACC,WAAW,IAAI,CAACD,IAAI,CAACE,QAAQ,KAClCT,QAAQ,CAACU,QAAQ,KAAKH,IAAI,CAACI,IAAI,IAC/BJ,IAAI,CAACI,IAAI,KAAK,GAAG,IAAIJ,IAAI,CAACI,IAAI,IAAIX,QAAQ,CAACU,QAAQ,CAACE,UAAU,CAACL,IAAI,CAACI,IAAI,CAAE,CAC7E,CAAC;IACDP,aAAa,CAAC,CAAAC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,EAAE,KAAI,EAAE,CAAC;EACtC,CAAC,EAAE,CAACb,QAAQ,CAACU,QAAQ,EAAEd,KAAK,CAAC,CAAC;;EAE9B;EACAR,SAAS,CAAC,MAAM;IACd,MAAM0B,aAAa,GAAIC,KAAoB,IAAK;MAC9C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,EAAE;QAClC,MAAMV,IAAI,GAAGX,KAAK,CAACU,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACW,QAAQ,KAAKH,KAAK,CAACI,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;QAC1E,IAAIb,IAAI,EAAE;UACRQ,KAAK,CAACM,cAAc,CAAC,CAAC;UACtBC,MAAM,CAACtB,QAAQ,CAACuB,IAAI,GAAGhB,IAAI,CAACI,IAAI;QAClC;MACF;IACF,CAAC;IAEDW,MAAM,CAACE,gBAAgB,CAAC,SAAS,EAAEV,aAAa,CAAC;IACjD,OAAO,MAAMQ,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEX,aAAa,CAAC;EACnE,CAAC,EAAE,CAAClB,KAAK,CAAC,CAAC;EAEX,MAAM8B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQ7B,QAAQ;MACd,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,eAAe;QAClB,OAAO,8CAA8C;MACvD,KAAK,cAAc;MACnB;QACE,OAAO,kBAAkB;IAC7B;EACF,CAAC;EAED,MAAM8B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQ9B,QAAQ;MACd,KAAK,aAAa;QAChB,OAAO,kBAAkB;MAC3B,KAAK,eAAe;QAClB,OAAO,kBAAkB;MAC3B,KAAK,cAAc;MACnB;QACE,OAAO,kBAAkB;IAC7B;EACF,CAAC;EAED,oBACEH,OAAA;IAAKI,SAAS,EAAE,cAAc4B,kBAAkB,CAAC,CAAC,IAAI5B,SAAS,EAAG;IAAA8B,QAAA,eAChElC,OAAA;MAAKI,SAAS,EAAE,QAAQ6B,kBAAkB,CAAC,CAAC,yBAA0B;MAAAC,QAAA,gBAEpElC,OAAA,CAACF,eAAe;QAAAoC,QAAA,EACb3B,UAAU,iBACTP,OAAA,CAACH,MAAM,CAACsC,GAAG;UACT/B,SAAS,EAAE,QAAQ6B,kBAAkB,CAAC,CAAC,YAAa;UACpDG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjCG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAR,QAAA,EAE7BhC,KAAK,CAACyC,GAAG,CAAC,CAAC9B,IAAI,EAAE+B,KAAK,KAAK;YAC1B,MAAMC,QAAQ,GAAGpC,UAAU,KAAKI,IAAI,CAACM,EAAE;YAEvC,oBACEnB,OAAA,CAACH,MAAM,CAACsC,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAG,CAAE;cAC/BP,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAE,CAAE;cAC9BN,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAG,CAAE;cAC5BL,UAAU,EAAE;gBAAEM,KAAK,EAAEH,KAAK,GAAG;cAAK,CAAE;cAAAV,QAAA,eAEpClC,OAAA,CAACL,IAAI;gBACHqD,EAAE,EAAEnC,IAAI,CAACI,IAAK;gBACdb,SAAS,EAAE,gHACTyC,QAAQ,GACJ,qCAAqC,GACrC,0KAA0K,EAC7K;gBACHI,OAAO,EAAEA,CAAA,KAAMzC,aAAa,CAAC,KAAK,CAAE;gBACpC0C,KAAK,EAAE,GAAGrC,IAAI,CAACsC,KAAK,GAAGtC,IAAI,CAACW,QAAQ,GAAG,UAAUX,IAAI,CAACW,QAAQ,CAAC4B,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,EAAG;gBAAAlB,QAAA,gBAEvFlC,OAAA;kBAAMI,SAAS,EAAC,SAAS;kBAAA8B,QAAA,EAAErB,IAAI,CAACwC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAG5CzD,OAAA;kBAAKI,SAAS,EAAC,uMAAuM;kBAAA8B,QAAA,GACnNrB,IAAI,CAACsC,KAAK,EACVtC,IAAI,CAACW,QAAQ,iBACZxB,OAAA;oBAAMI,SAAS,EAAC,yBAAyB;oBAAA8B,QAAA,GAAC,OACnC,EAACrB,IAAI,CAACW,QAAQ,CAAC4B,WAAW,CAAC,CAAC;kBAAA;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACP,eACDzD,OAAA;oBAAKI,SAAS,EAAC;kBAA4J;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/K,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GA5BF5C,IAAI,CAACM,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BF,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBzD,OAAA,CAACH,MAAM,CAAC6D,MAAM;QACZT,OAAO,EAAEA,CAAA,KAAMzC,aAAa,CAAC,CAACD,UAAU,CAAE;QAC1CH,SAAS,EAAE,iGACTG,UAAU,GACN,kDAAkD,GAClD,gEAAgE,EACnE;QACHoD,UAAU,EAAE;UAAErB,KAAK,EAAE;QAAK,CAAE;QAC5BsB,QAAQ,EAAE;UAAEtB,KAAK,EAAE;QAAK,CAAE;QAC1BY,KAAK,EAAE3C,UAAU,GAAG,gBAAgB,GAAG,8BAA+B;QAAA2B,QAAA,EAErE3B,UAAU,gBACTP,OAAA;UAAKI,SAAS,EAAC,SAAS;UAACyD,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAA7B,QAAA,eAC5ElC,OAAA;YAAMgE,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAA4B;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,gBAENzD,OAAA;UAAKI,SAAS,EAAC,SAAS;UAACyD,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAA7B,QAAA,eAC5ElC,OAAA;YAAMgE,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAyB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CA1IIJ,kBAAqD;EAAA,QAKxCL,WAAW;AAAA;AAAAwE,EAAA,GALxBnE,kBAAqD;AA4I3D,eAAeA,kBAAkB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}