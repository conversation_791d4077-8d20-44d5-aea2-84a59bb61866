{"ast": null, "code": "export var zpk2tfDocs = {\n  name: 'zpk2tf',\n  category: 'Signal',\n  syntax: ['zpk2tf(z, p, k)'],\n  description: 'Compute the transfer function of a zero-pole-gain model.',\n  examples: ['zpk2tf([1, 2], [-1, -2], 1)', 'zpk2tf([1, 2], [-1, -2])', 'zpk2tf([1 - 3i, 2 + 2i], [-1, -2])'],\n  seealso: []\n};", "map": {"version": 3, "names": ["zpk2tfDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/signal/zpk2tf.js"], "sourcesContent": ["export var zpk2tfDocs = {\n  name: 'zpk2tf',\n  category: 'Signal',\n  syntax: ['zpk2tf(z, p, k)'],\n  description: 'Compute the transfer function of a zero-pole-gain model.',\n  examples: ['zpk2tf([1, 2], [-1, -2], 1)', 'zpk2tf([1, 2], [-1, -2])', 'zpk2tf([1 - 3i, 2 + 2i], [-1, -2])'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,iBAAiB,CAAC;EAC3BC,WAAW,EAAE,0DAA0D;EACvEC,QAAQ,EAAE,CAAC,6BAA6B,EAAE,0BAA0B,EAAE,oCAAoC,CAAC;EAC3GC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}