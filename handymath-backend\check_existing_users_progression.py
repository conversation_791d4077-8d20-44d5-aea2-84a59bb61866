#!/usr/bin/env python
"""
Script pour vérifier la progression des utilisateurs existants
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import Course, Chapter, Lesson, LessonProgress, CourseEnrollment
from django.contrib.auth import get_user_model

User = get_user_model()

def check_all_users_progression():
    """Vérifier la progression de tous les utilisateurs"""
    print("🔍 Vérification de la progression de tous les utilisateurs...\n")
    
    for user in User.objects.all():
        print(f"👤 Utilisateur: {user.username}")
        
        has_suspicious_progress = False
        
        for course in Course.objects.filter(status='published'):
            progress = course.get_progress_for_user(user)
            
            # Compter les leçons réellement terminées
            completed_lessons = LessonProgress.objects.filter(
                user=user,
                lesson__chapter__course=course,
                completed=True
            ).count()
            
            total_lessons = Lesson.objects.filter(
                chapter__course=course,
                is_published=True
            ).count()
            
            # Vérifier s'il y a une incohérence
            expected_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
            
            if abs(progress - expected_progress) > 0.1:  # Différence significative
                has_suspicious_progress = True
                print(f"   ⚠️ {course.title}:")
                print(f"      Progression affichée: {progress}%")
                print(f"      Progression attendue: {expected_progress}%")
                print(f"      Leçons terminées: {completed_lessons}/{total_lessons}")
            elif progress > 0:
                print(f"   ✅ {course.title}: {progress}% ({completed_lessons}/{total_lessons} leçons)")
        
        if not has_suspicious_progress:
            print(f"   ✅ Toutes les progressions sont cohérentes")
        
        print()

def check_course_structure():
    """Vérifier la structure des cours"""
    print("🔍 Vérification de la structure des cours...\n")
    
    for course in Course.objects.filter(status='published'):
        chapters = course.chapters.all()
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        print(f"📚 Cours: {course.title}")
        print(f"   📖 Chapitres: {chapters.count()}")
        print(f"   📝 Leçons totales: {total_lessons}")
        
        if total_lessons == 0:
            print(f"   ⚠️ PROBLÈME: Aucune leçon dans ce cours!")
        
        for chapter in chapters:
            lessons = chapter.lessons.filter(is_published=True)
            print(f"      - {chapter.title}: {lessons.count()} leçons")
            
            if lessons.count() == 0:
                print(f"        ⚠️ Chapitre vide!")
        
        print()

def fix_empty_courses():
    """Corriger les cours vides qui causent des problèmes"""
    print("🔧 Correction des cours vides...\n")
    
    empty_courses = []
    
    for course in Course.objects.filter(status='published'):
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        if total_lessons == 0:
            empty_courses.append(course)
            print(f"📚 Cours vide trouvé: {course.title}")
    
    if empty_courses:
        print(f"\n⚠️ {len(empty_courses)} cours vides trouvés.")
        print("Options de correction:")
        print("1. Marquer ces cours comme 'draft' (brouillon)")
        print("2. Les supprimer")
        print("3. Ajouter des leçons de test")
        
        # Option 1: Marquer comme brouillon
        print("\n🔧 Marquage des cours vides comme brouillon...")
        for course in empty_courses:
            course.status = 'draft'
            course.save()
            print(f"   ✅ {course.title} marqué comme brouillon")
        
        print(f"\n✅ {len(empty_courses)} cours marqués comme brouillon")
    else:
        print("✅ Aucun cours vide trouvé")

def recalculate_all_progressions():
    """Recalculer toutes les progressions pour s'assurer qu'elles sont correctes"""
    print("\n🔄 Recalcul de toutes les progressions...\n")
    
    for user in User.objects.all():
        print(f"👤 Recalcul pour {user.username}:")
        
        for course in Course.objects.filter(status='published'):
            # Obtenir la progression actuelle
            old_progress = course.get_progress_for_user(user)
            
            # Recalculer manuellement
            total_lessons = Lesson.objects.filter(
                chapter__course=course,
                is_published=True
            ).count()
            
            completed_lessons = LessonProgress.objects.filter(
                user=user,
                lesson__chapter__course=course,
                completed=True
            ).count()
            
            new_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
            
            if abs(old_progress - new_progress) > 0.1:
                print(f"   🔄 {course.title}: {old_progress}% → {new_progress}%")
            elif new_progress > 0:
                print(f"   ✅ {course.title}: {new_progress}%")
        
        print()

def create_test_user_clean():
    """Créer un utilisateur de test complètement propre"""
    print("👤 Création d'un utilisateur de test propre...\n")
    
    # Supprimer l'utilisateur de test s'il existe
    User.objects.filter(username='clean_test_user').delete()
    
    # Créer un nouvel utilisateur
    clean_user = User.objects.create_user(
        username='clean_test_user',
        email='<EMAIL>',
        password='testpass123',
        first_name='Clean',
        last_name='Test'
    )
    
    print(f"✅ Utilisateur créé: {clean_user.username}")
    
    # Vérifier sa progression sur tous les cours
    print("📊 Progression initiale:")
    
    for course in Course.objects.filter(status='published'):
        progress = course.get_progress_for_user(clean_user)
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        print(f"   📚 {course.title}: {progress}% ({total_lessons} leçons)")
        
        if progress > 0 and total_lessons > 0:
            print(f"      ⚠️ PROBLÈME: Progression > 0 pour un nouvel utilisateur!")

def main():
    print("🚀 Vérification complète des progressions...\n")
    
    try:
        check_course_structure()
        fix_empty_courses()
        check_all_users_progression()
        recalculate_all_progressions()
        create_test_user_clean()
        
        print("\n✅ Vérification terminée!")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la vérification: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
