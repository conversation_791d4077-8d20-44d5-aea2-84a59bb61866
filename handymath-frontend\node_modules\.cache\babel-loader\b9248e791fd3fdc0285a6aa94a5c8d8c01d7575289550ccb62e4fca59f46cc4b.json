{"ast": null, "code": "export var falseDocs = {\n  name: 'false',\n  category: 'Constants',\n  syntax: ['false'],\n  description: 'Boolean value false',\n  examples: ['false'],\n  seealso: ['true']\n};", "map": {"version": 3, "names": ["falseDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/false.js"], "sourcesContent": ["export var falseDocs = {\n  name: 'false',\n  category: 'Constants',\n  syntax: ['false'],\n  description: 'Boolean value false',\n  examples: ['false'],\n  seealso: ['true']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,CAAC,OAAO,CAAC;EACnBC,OAAO,EAAE,CAAC,MAAM;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}