#!/usr/bin/env python
"""
Script pour déboguer en détail la logique d'accessibilité des leçons
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import Course, Chapter, Lesson, LessonProgress
from django.contrib.auth import get_user_model

User = get_user_model()

def debug_lesson_accessibility():
    """Déboguer l'accessibilité des leçons en détail"""
    print("🔍 Débogage détaillé de l'accessibilité des leçons...\n")
    
    # Prendre un utilisateur de test
    user = User.objects.filter(username='ayoub').first()
    if not user:
        user = User.objects.first()
    
    print(f"👤 Utilisateur de test: {user.username}\n")
    
    # Examiner chaque cours
    for course in Course.objects.filter(status='published'):
        print(f"📚 Cours: {course.title}")
        course_accessible = course.is_accessible_for_user(user)
        print(f"   Accessible: {course_accessible}")
        
        if not course_accessible:
            print(f"   ❌ Cours non accessible - vérification des prérequis:")
            for prereq in course.prerequisites.all():
                progress = prereq.get_progress_for_user(user)
                print(f"      - {prereq.title}: {progress}% (requis: 80%)")
            print()
            continue
        
        # Examiner chaque chapitre
        for chapter in course.chapters.all().order_by('order'):
            print(f"   📖 Chapitre: {chapter.title}")
            
            # Examiner chaque leçon
            lessons = chapter.lessons.filter(is_published=True).order_by('order')
            for lesson in lessons:
                print(f"      📝 Leçon: {lesson.title} (ordre: {lesson.order})")
                
                # Vérifier l'accessibilité étape par étape
                print(f"         1. Cours accessible: {course_accessible}")
                
                is_first_lesson = lesson.order == 0
                print(f"         2. Première leçon (ordre 0): {is_first_lesson}")
                
                if is_first_lesson:
                    accessible = True
                    print(f"         ✅ Accessible (première leçon)")
                else:
                    # Vérifier les leçons précédentes
                    print(f"         3. Vérification des leçons précédentes:")
                    previous_lessons = Lesson.objects.filter(
                        chapter=chapter,
                        order__lt=lesson.order,
                        is_published=True
                    ).order_by('order')
                    
                    all_previous_completed = True
                    for prev_lesson in previous_lessons:
                        progress = LessonProgress.objects.filter(
                            user=user,
                            lesson=prev_lesson,
                            completed=True
                        ).exists()
                        print(f"            - {prev_lesson.title} (ordre {prev_lesson.order}): {'✅ Terminée' if progress else '❌ Non terminée'}")
                        if not progress:
                            all_previous_completed = False
                    
                    accessible = all_previous_completed
                    print(f"         {'✅ Accessible' if accessible else '🔒 Verrouillée'}")
                
                # Vérifier la progression actuelle
                current_progress = LessonProgress.objects.filter(
                    user=user,
                    lesson=lesson
                ).first()
                
                if current_progress:
                    print(f"         📊 Progression: {'Terminée' if current_progress.completed else 'En cours'}")
                else:
                    print(f"         📊 Progression: Aucune progression trouvée")
                
                print()
        print()

def test_lesson_completion_flow():
    """Tester le flux de completion des leçons"""
    print("🧪 Test du flux de completion des leçons...\n")
    
    user = User.objects.filter(username='ayoub').first()
    if not user:
        user = User.objects.first()
    
    print(f"👤 Utilisateur de test: {user.username}\n")
    
    # Trouver le premier cours accessible
    for course in Course.objects.filter(status='published'):
        if course.is_accessible_for_user(user):
            print(f"📚 Test avec le cours: {course.title}")
            
            # Trouver le premier chapitre
            chapter = course.chapters.all().order_by('order').first()
            if chapter:
                print(f"📖 Test avec le chapitre: {chapter.title}")
                
                # Obtenir les leçons dans l'ordre
                lessons = list(chapter.lessons.filter(is_published=True).order_by('order'))
                print(f"📝 Leçons trouvées: {len(lessons)}")
                
                for i, lesson in enumerate(lessons):
                    print(f"\n   Leçon {i+1}: {lesson.title} (ordre: {lesson.order})")
                    
                    # Vérifier l'accessibilité
                    accessible = lesson.is_accessible_for_user(user)
                    print(f"   Accessible: {accessible}")
                    
                    # Obtenir ou créer la progression
                    progress, created = LessonProgress.objects.get_or_create(
                        user=user,
                        lesson=lesson,
                        defaults={'completed': False, 'time_spent': 0}
                    )
                    
                    print(f"   Progression: {'Créée' if created else 'Existante'} - Terminée: {progress.completed}")
                    
                    # Si c'est la première leçon non terminée et accessible, la terminer
                    if accessible and not progress.completed:
                        print(f"   🎯 Simulation de completion de cette leçon...")
                        progress.completed = True
                        progress.time_spent = 300  # 5 minutes
                        progress.save()
                        print(f"   ✅ Leçon marquée comme terminée")
                        
                        # Vérifier l'impact sur la leçon suivante
                        if i + 1 < len(lessons):
                            next_lesson = lessons[i + 1]
                            next_accessible = next_lesson.is_accessible_for_user(user)
                            print(f"   📈 Leçon suivante '{next_lesson.title}' maintenant accessible: {next_accessible}")
                        
                        break
                    elif not accessible:
                        print(f"   🔒 Leçon non accessible - arrêt du test")
                        break
                    else:
                        print(f"   ✅ Leçon déjà terminée")
                
                break
            else:
                print(f"❌ Aucun chapitre trouvé dans le cours")
        else:
            print(f"❌ Cours '{course.title}' non accessible")

def main():
    print("🚀 Débogage complet de la logique des leçons...\n")
    
    try:
        debug_lesson_accessibility()
        test_lesson_completion_flow()
        
        print("\n✅ Débogage terminé!")
        
    except Exception as e:
        print(f"\n❌ Erreur lors du débogage: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
