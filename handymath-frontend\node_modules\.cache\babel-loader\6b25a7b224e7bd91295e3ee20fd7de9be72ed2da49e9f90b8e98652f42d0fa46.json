{"ast": null, "code": "export var expm1Docs = {\n  name: 'expm1',\n  category: 'Arithmetic',\n  syntax: ['expm1(x)'],\n  description: 'Calculate the value of subtracting 1 from the exponential value.',\n  examples: ['expm1(2)', 'pow(e, 2) - 1', 'log(expm1(2) + 1)'],\n  seealso: ['exp', 'pow', 'log']\n};", "map": {"version": 3, "names": ["expm1Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/expm1.js"], "sourcesContent": ["export var expm1Docs = {\n  name: 'expm1',\n  category: 'Arithmetic',\n  syntax: ['expm1(x)'],\n  description: 'Calculate the value of subtracting 1 from the exponential value.',\n  examples: ['expm1(2)', 'pow(e, 2) - 1', 'log(expm1(2) + 1)'],\n  seealso: ['exp', 'pow', 'log']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,mBAAmB,CAAC;EAC5DC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}