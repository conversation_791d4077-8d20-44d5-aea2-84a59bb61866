{"ast": null, "code": "export var varianceDocs = {\n  name: 'variance',\n  category: 'Statistics',\n  syntax: ['variance(a, b, c, ...)', 'variance(A)', 'variance(A, dimension)', 'variance(A, normalization)', 'variance(A, dimension, normalization)'],\n  description: 'Compute the variance of all values. Optional parameter normalization can be \"unbiased\" (default), \"uncorrected\", or \"biased\".',\n  examples: ['variance(2, 4, 6)', 'variance([2, 4, 6, 8])', 'variance([2, 4, 6, 8], \"uncorrected\")', 'variance([2, 4, 6, 8], \"biased\")', 'variance([1, 2, 3; 4, 5, 6])'],\n  seealso: ['max', 'mean', 'min', 'median', 'min', 'prod', 'std', 'sum']\n};", "map": {"version": 3, "names": ["varianceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/variance.js"], "sourcesContent": ["export var varianceDocs = {\n  name: 'variance',\n  category: 'Statistics',\n  syntax: ['variance(a, b, c, ...)', 'variance(A)', 'variance(A, dimension)', 'variance(A, normalization)', 'variance(A, dimension, normalization)'],\n  description: 'Compute the variance of all values. Optional parameter normalization can be \"unbiased\" (default), \"uncorrected\", or \"biased\".',\n  examples: ['variance(2, 4, 6)', 'variance([2, 4, 6, 8])', 'variance([2, 4, 6, 8], \"uncorrected\")', 'variance([2, 4, 6, 8], \"biased\")', 'variance([1, 2, 3; 4, 5, 6])'],\n  seealso: ['max', 'mean', 'min', 'median', 'min', 'prod', 'std', 'sum']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,wBAAwB,EAAE,aAAa,EAAE,wBAAwB,EAAE,4BAA4B,EAAE,uCAAuC,CAAC;EAClJC,WAAW,EAAE,+HAA+H;EAC5IC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,uCAAuC,EAAE,kCAAkC,EAAE,8BAA8B,CAAC;EACtKC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}