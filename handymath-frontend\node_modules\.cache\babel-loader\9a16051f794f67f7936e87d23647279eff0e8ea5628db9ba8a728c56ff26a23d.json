{"ast": null, "code": "export var setIntersectDocs = {\n  name: 'setIntersect',\n  category: 'Set',\n  syntax: ['setIntersect(set1, set2)'],\n  description: 'Create the intersection of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setIntersect([1, 2, 3, 4], [3, 4, 5, 6])', 'setIntersect([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setDifference']\n};", "map": {"version": 3, "names": ["setIntersectDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setIntersect.js"], "sourcesContent": ["export var setIntersectDocs = {\n  name: 'setIntersect',\n  category: 'Set',\n  syntax: ['setIntersect(set1, set2)'],\n  description: 'Create the intersection of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setIntersect([1, 2, 3, 4], [3, 4, 5, 6])', 'setIntersect([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,0BAA0B,CAAC;EACpCC,WAAW,EAAE,uIAAuI;EACpJC,QAAQ,EAAE,CAAC,0CAA0C,EAAE,kDAAkD,CAAC;EAC1GC,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}