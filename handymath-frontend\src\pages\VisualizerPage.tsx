import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import * as math from 'mathjs';
import StudentLayout from '../components/StudentLayout';

const VisualizerPage: React.FC = () => {
  const [functionInput, setFunctionInput] = useState('x^2');
  const [error, setError] = useState<string | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Fonction sécurisée pour évaluer les expressions mathématiques
  const evaluateFunction = (expr: string, variables: Record<string, number>) => {
    try {
      // Compiler l'expression une seule fois pour de meilleures performances
      const compiledExpr = math.compile(expr);
      return compiledExpr.evaluate(variables);
    } catch (err) {
      console.error("Erreur d'évaluation:", err);
      setError(`Erreur: ${err instanceof Error ? err.message : String(err)}`);
      return NaN;
    }
  };

  // Rendu 2D
  const renderFunction2D = () => {
    setError(null);
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Nettoyer le canvas existant
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Définir les dimensions du canvas
    const width = canvas.width;
    const height = canvas.height;

    // Effacer le canvas
    ctx.fillStyle = '#f8fafc';
    ctx.fillRect(0, 0, width, height);

    // Dessiner les axes
    ctx.strokeStyle = '#94a3b8';
    ctx.beginPath();
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2);
    ctx.moveTo(width / 2, 0);
    ctx.lineTo(width / 2, height);
    ctx.stroke();

    // Dessiner la grille
    ctx.strokeStyle = '#e2e8f0';
    ctx.beginPath();
    // Lignes verticales
    for (let x = 0; x <= width; x += width / 20) {
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
    }
    // Lignes horizontales
    for (let y = 0; y <= height; y += height / 20) {
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
    }
    ctx.stroke();

    // Dessiner la fonction
    ctx.strokeStyle = '#6366f1';
    ctx.lineWidth = 2;
    ctx.beginPath();

    try {
      // Échelle: combien de pixels par unité
      const scaleX = width / 20;
      const scaleY = height / 20;
      const offsetX = width / 2;
      const offsetY = height / 2;

      let isFirstPoint = true;

      // Dessiner la fonction point par point
      for (let pixelX = 0; pixelX <= width; pixelX++) {
        // Convertir les coordonnées du canvas en coordonnées mathématiques
        const x = (pixelX - offsetX) / scaleX;

        // Évaluer la fonction
        const y = evaluateFunction(functionInput, { x });

        // Vérifier si y est un nombre valide
        if (isNaN(y) || !isFinite(y)) continue;

        // Convertir y en coordonnées du canvas
        const pixelY = offsetY - y * scaleY;

        // Vérifier si le point est dans les limites du canvas
        if (pixelY < -1000 || pixelY > height + 1000) continue;

        if (isFirstPoint) {
          ctx.moveTo(pixelX, pixelY);
          isFirstPoint = false;
        } else {
          ctx.lineTo(pixelX, pixelY);
        }
      }
      ctx.stroke();
    } catch (error) {
      console.error("Erreur lors de l'évaluation de la fonction:", error);
      setError(`Erreur: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Effectuer le rendu lorsque la fonction change
  useEffect(() => {
    renderFunction2D();
  }, [functionInput]);

  // Exemples de fonctions
  const functionExamples = [
    { label: 'Polynôme', value: 'x^2 + 2*x - 1' },
    { label: 'Trigonométrique', value: 'sin(x) * cos(x)' },
    { label: 'Exponentielle', value: 'exp(-x^2)' },
    { label: 'Logarithmique', value: 'log(abs(x) + 0.1)' },
    { label: 'Rationnelle', value: '1 / (1 + x^2)' }
  ];

  return (
    <StudentLayout
      title="Visualiseur de fonctions"
      subtitle="Visualisez et explorez des fonctions mathématiques"
    >
      <div className="space-y-6">
        <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <label htmlFor="function" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Entrez votre fonction
          </label>
          <div className="flex">
            <input
              type="text"
              id="function"
              value={functionInput}
              onChange={(e) => setFunctionInput(e.target.value)}
              placeholder="Ex: x^2, sin(x), cos(x)"
              className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:ring-secondary-500 focus:border-secondary-500 dark:bg-gray-700 dark:text-white"
            />
            <button
              onClick={renderFunction2D}
              className="bg-secondary-500 hover:bg-secondary-600 text-white font-medium py-3 px-6 rounded-r-lg transition-colors"
            >
              Visualiser
            </button>
          </div>

          {error && (
            <div className="mt-2 text-red-500 text-sm">
              {error}
            </div>
          )}

          <div className="mt-4">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Exemples de fonctions:
            </p>
            <div className="flex flex-wrap gap-2">
              {functionExamples.map((example, index) => (
                <button
                  key={index}
                  onClick={() => setFunctionInput(example.value)}
                  className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  {example.label}: {example.value}
                </button>
              ))}
            </div>
          </div>

          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            <p className="font-medium">Syntaxe supportée:</p>
            <ul className="list-disc list-inside mt-1">
              <li>Opérateurs: +, -, *, /, ^</li>
              <li>Fonctions: sin, cos, tan, exp, log, sqrt, abs</li>
              <li>Constantes: pi, e</li>
              <li>Variable: x</li>
            </ul>
          </div>
        </div>

        <div className="flex justify-center">
          <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              width="600"
              height="400"
              className="bg-gray-50 dark:bg-gray-900"
            />
          </div>
        </div>

        <div className="mt-6">
          <h3 className="text-xl font-semibold mb-3">Informations mathématiques</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <h4 className="font-medium mb-2">Analyse de la fonction</h4>
              <p className="text-gray-700 dark:text-gray-300">
                {functionInput.includes('log') || functionInput.includes('sqrt')
                  ? 'Attention aux domaines de définition (log, sqrt)'
                  : 'Fonction définie sur ℝ'}
              </p>
            </div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <h4 className="font-medium mb-2">Conseils</h4>
              <p className="text-gray-700 dark:text-gray-300">
                Essayez de combiner différentes fonctions pour obtenir des courbes intéressantes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </StudentLayout>
  );
};

export default VisualizerPage;
