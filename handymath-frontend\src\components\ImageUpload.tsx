import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';

interface ImageUploadProps {
  onImageUpload: (file: File) => void;
  onEquationRecognized: (equation: string) => void;
  onSuggestionsReceived?: (suggestions: string[]) => void;
  loading: boolean;
  disabled?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUpload,
  onEquationRecognized,
  onSuggestionsReceived,
  loading,
  disabled = false
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || loading) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || loading) return;

    const files = e.target.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  };

  const handleFile = (file: File) => {
    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      alert('Veuillez sélectionner un fichier image (PNG, JPG, etc.)');
      return;
    }

    // Vérifier la taille (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Le fichier est trop volumineux. Taille maximale : 5MB');
      return;
    }

    // Créer l'aperçu
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Déclencher l'upload
    onImageUpload(file);
  };

  const handleClick = () => {
    if (!disabled && !loading && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const clearImage = () => {
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="w-full">
      {/* Zone d'upload */}
      <motion.div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
          ${dragActive
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500'
          }
          ${disabled || loading
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
          }
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
        whileHover={!disabled && !loading ? { scale: 1.02 } : {}}
        whileTap={!disabled && !loading ? { scale: 0.98 } : {}}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled || loading}
        />

        {loading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"></div>
            <p className="text-lg font-medium text-gray-700 dark:text-gray-300">
              Reconnaissance en cours...
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Analyse de l'équation manuscrite
            </p>
          </div>
        ) : previewUrl ? (
          <div className="space-y-4">
            <img
              src={previewUrl}
              alt="Équation uploadée"
              className="max-h-48 mx-auto rounded-lg shadow-md"
            />
            <div className="flex justify-center space-x-3">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearImage();
                }}
                className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                Supprimer
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleClick();
                }}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                Changer d'image
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 text-gray-400 dark:text-gray-500">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            <div>
              <p className="text-lg font-medium text-gray-700 dark:text-gray-300">
                📸 Reconnaissance d'équation manuscrite
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Glissez-déposez une image ou cliquez pour sélectionner
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Formats supportés : PNG, JPG, JPEG (max 5MB)
              </p>
            </div>
          </div>
        )}
      </motion.div>

      {/* Conseils pour une meilleure reconnaissance */}
      <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
          💡 Conseils pour une meilleure reconnaissance :
        </h4>
        <ul className="text-xs text-blue-700 dark:text-blue-400 space-y-1">
          <li>• Écrivez clairement et lisiblement</li>
          <li>• Utilisez un fond contrasté (papier blanc, encre noire)</li>
          <li>• Évitez les ombres et reflets</li>
          <li>• Centrez l'équation dans l'image</li>
          <li>• Utilisez une résolution suffisante</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageUpload;
