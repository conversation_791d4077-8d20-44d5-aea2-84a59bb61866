{"ast": null, "code": "import { errorTransform } from '../../transform/utils/errorTransform.js';\nimport { getSafeProperty } from '../../../utils/customs.js';\nexport function accessFactory(_ref) {\n  var {\n    subset\n  } = _ref;\n  /**\n   * Retrieve part of an object:\n   *\n   * - Retrieve a property from an object\n   * - Retrieve a part of a string\n   * - Retrieve a matrix subset\n   *\n   * @param {Object | Array | Matrix | string} object\n   * @param {Index} index\n   * @return {Object | Array | Matrix | string} Returns the subset\n   */\n  return function access(object, index) {\n    try {\n      if (Array.isArray(object)) {\n        return subset(object, index);\n      } else if (object && typeof object.subset === 'function') {\n        // Matrix\n        return object.subset(index);\n      } else if (typeof object === 'string') {\n        // TODO: move getStringSubset into a separate util file, use that\n        return subset(object, index);\n      } else if (typeof object === 'object') {\n        if (!index.isObjectProperty()) {\n          throw new TypeError('Cannot apply a numeric index as object property');\n        }\n        return getSafeProperty(object, index.getObjectProperty());\n      } else {\n        throw new TypeError('Cannot apply index: unsupported type of object');\n      }\n    } catch (err) {\n      throw errorTransform(err);\n    }\n  };\n}", "map": {"version": 3, "names": ["errorTransform", "getSafeProperty", "accessFactory", "_ref", "subset", "access", "object", "index", "Array", "isArray", "isObjectProperty", "TypeError", "getObjectProperty", "err"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/node/utils/access.js"], "sourcesContent": ["import { errorTransform } from '../../transform/utils/errorTransform.js';\nimport { getSafeProperty } from '../../../utils/customs.js';\nexport function accessFactory(_ref) {\n  var {\n    subset\n  } = _ref;\n  /**\n   * Retrieve part of an object:\n   *\n   * - Retrieve a property from an object\n   * - Retrieve a part of a string\n   * - Retrieve a matrix subset\n   *\n   * @param {Object | Array | Matrix | string} object\n   * @param {Index} index\n   * @return {Object | Array | Matrix | string} Returns the subset\n   */\n  return function access(object, index) {\n    try {\n      if (Array.isArray(object)) {\n        return subset(object, index);\n      } else if (object && typeof object.subset === 'function') {\n        // Matrix\n        return object.subset(index);\n      } else if (typeof object === 'string') {\n        // TODO: move getStringSubset into a separate util file, use that\n        return subset(object, index);\n      } else if (typeof object === 'object') {\n        if (!index.isObjectProperty()) {\n          throw new TypeError('Cannot apply a numeric index as object property');\n        }\n        return getSafeProperty(object, index.getObjectProperty());\n      } else {\n        throw new TypeError('Cannot apply index: unsupported type of object');\n      }\n    } catch (err) {\n      throw errorTransform(err);\n    }\n  };\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,yCAAyC;AACxE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASE,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACpC,IAAI;MACF,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,OAAOF,MAAM,CAACE,MAAM,EAAEC,KAAK,CAAC;MAC9B,CAAC,MAAM,IAAID,MAAM,IAAI,OAAOA,MAAM,CAACF,MAAM,KAAK,UAAU,EAAE;QACxD;QACA,OAAOE,MAAM,CAACF,MAAM,CAACG,KAAK,CAAC;MAC7B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrC;QACA,OAAOF,MAAM,CAACE,MAAM,EAAEC,KAAK,CAAC;MAC9B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrC,IAAI,CAACC,KAAK,CAACG,gBAAgB,CAAC,CAAC,EAAE;UAC7B,MAAM,IAAIC,SAAS,CAAC,iDAAiD,CAAC;QACxE;QACA,OAAOV,eAAe,CAACK,MAAM,EAAEC,KAAK,CAACK,iBAAiB,CAAC,CAAC,CAAC;MAC3D,CAAC,MAAM;QACL,MAAM,IAAID,SAAS,CAAC,gDAAgD,CAAC;MACvE;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZ,MAAMb,cAAc,CAACa,GAAG,CAAC;IAC3B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}