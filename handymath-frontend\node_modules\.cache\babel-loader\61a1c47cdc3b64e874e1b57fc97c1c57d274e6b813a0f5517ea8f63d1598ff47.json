{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { FractionDependencies } from './dependenciesFractionClass.generated.js';\nimport { absDependencies } from './dependenciesAbs.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { fixDependencies } from './dependenciesFix.generated.js';\nimport { formatDependencies } from './dependenciesFormat.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { roundDependencies } from './dependenciesRound.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { createUnitClass } from '../../factoriesAny.js';\nexport var UnitDependencies = {\n  BigNumberDependencies,\n  ComplexDependencies,\n  FractionDependencies,\n  absDependencies,\n  addScalarDependencies,\n  divideScalarDependencies,\n  equalDependencies,\n  fixDependencies,\n  formatDependencies,\n  isNumericDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  roundDependencies,\n  subtractScalarDependencies,\n  createUnitClass\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "ComplexDependencies", "FractionDependencies", "absDependencies", "addScalarDependencies", "divideScalarDependencies", "equalDependencies", "fixDependencies", "formatDependencies", "isNumericDependencies", "multiplyScalarDependencies", "numberDependencies", "powDependencies", "roundDependencies", "subtractScalarDependencies", "createUnitClass", "UnitDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUnitClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { FractionDependencies } from './dependenciesFractionClass.generated.js';\nimport { absDependencies } from './dependenciesAbs.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { fixDependencies } from './dependenciesFix.generated.js';\nimport { formatDependencies } from './dependenciesFormat.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { roundDependencies } from './dependenciesRound.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { createUnitClass } from '../../factoriesAny.js';\nexport var UnitDependencies = {\n  BigNumberDependencies,\n  ComplexDependencies,\n  FractionDependencies,\n  absDependencies,\n  addScalarDependencies,\n  divideScalarDependencies,\n  equalDependencies,\n  fixDependencies,\n  formatDependencies,\n  isNumericDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  roundDependencies,\n  subtractScalarDependencies,\n  createUnitClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BhB,qBAAqB;EACrBC,mBAAmB;EACnBC,oBAAoB;EACpBC,eAAe;EACfC,qBAAqB;EACrBC,wBAAwB;EACxBC,iBAAiB;EACjBC,eAAe;EACfC,kBAAkB;EAClBC,qBAAqB;EACrBC,0BAA0B;EAC1BC,kBAAkB;EAClBC,eAAe;EACfC,iBAAiB;EACjBC,0BAA0B;EAC1BC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}