#!/usr/bin/env python
"""
Script pour déboguer les problèmes de progression incorrecte
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import Course, Chapter, Lesson, LessonProgress, CourseEnrollment
from django.contrib.auth import get_user_model

User = get_user_model()

def debug_progression_calculation():
    """Déboguer le calcul de progression des cours"""
    print("🔍 Débogage du calcul de progression des cours...\n")
    
    # Créer un nouvel utilisateur pour tester
    test_user, created = User.objects.get_or_create(
        username='test_progression_debug',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Nouvel utilisateur créé: {test_user.username}")
    else:
        print(f"👤 Utilisateur existant: {test_user.username}")
    
    print(f"\n📊 Analyse de la progression pour {test_user.username}:\n")
    
    for course in Course.objects.filter(status='published'):
        print(f"📚 Cours: {course.title}")
        
        # Calculer la progression avec la méthode actuelle
        current_progress = course.get_progress_for_user(test_user)
        print(f"   📈 Progression calculée: {current_progress}%")
        
        # Calculer manuellement pour vérifier
        total_lessons = Lesson.objects.filter(
            chapter__course=course,
            is_published=True
        ).count()
        
        completed_lessons = LessonProgress.objects.filter(
            user=test_user,
            lesson__chapter__course=course,
            completed=True
        ).count()
        
        manual_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
        
        print(f"   📝 Leçons totales: {total_lessons}")
        print(f"   ✅ Leçons terminées: {completed_lessons}")
        print(f"   🧮 Progression manuelle: {manual_progress}%")
        
        # Vérifier les progressions existantes
        all_progressions = LessonProgress.objects.filter(
            user=test_user,
            lesson__chapter__course=course
        )
        
        print(f"   📋 Progressions trouvées: {all_progressions.count()}")
        
        for progress in all_progressions:
            status = "✅ Terminée" if progress.completed else "⏳ En cours"
            print(f"      - {progress.lesson.title}: {status}")
        
        # Vérifier l'enrollment
        enrollment = CourseEnrollment.objects.filter(
            user=test_user,
            course=course
        ).first()
        
        if enrollment:
            print(f"   📝 Inscription: {enrollment.enrolled_at}")
            print(f"   🎯 Complété: {enrollment.completed}")
            print(f"   📅 Date de completion: {enrollment.completed_at}")
        else:
            print(f"   ❌ Aucune inscription trouvée")
        
        print()

def debug_lesson_progress_creation():
    """Déboguer la création automatique des progressions"""
    print("🔍 Débogage de la création des progressions...\n")
    
    test_user = User.objects.get(username='test_progression_debug')
    
    for course in Course.objects.filter(status='published'):
        print(f"📚 Cours: {course.title}")
        
        # Compter les leçons avant création
        existing_progressions = LessonProgress.objects.filter(
            user=test_user,
            lesson__chapter__course=course
        ).count()
        
        print(f"   📊 Progressions existantes: {existing_progressions}")
        
        # Simuler la création automatique (comme dans l'API)
        created_count = 0
        for chapter in course.chapters.all():
            for lesson in chapter.lessons.filter(is_published=True):
                progress, created = LessonProgress.objects.get_or_create(
                    user=test_user,
                    lesson=lesson,
                    defaults={'completed': False, 'time_spent': 0}
                )
                if created:
                    created_count += 1
        
        print(f"   ➕ Nouvelles progressions créées: {created_count}")
        
        # Recalculer la progression
        new_progress = course.get_progress_for_user(test_user)
        print(f"   📈 Nouvelle progression: {new_progress}%")
        print()

def check_data_inconsistencies():
    """Vérifier les incohérences dans les données"""
    print("🔍 Vérification des incohérences de données...\n")
    
    # Vérifier les progressions avec completed=True mais sans raison
    suspicious_progressions = LessonProgress.objects.filter(completed=True)
    
    print(f"📊 Progressions marquées comme terminées: {suspicious_progressions.count()}")
    
    for progress in suspicious_progressions:
        print(f"   ✅ {progress.user.username} - {progress.lesson.title}")
        print(f"      Cours: {progress.lesson.chapter.course.title}")
        print(f"      Temps passé: {progress.time_spent} secondes")
        print(f"      Complété le: {progress.completed_at}")
        print()
    
    # Vérifier les cours avec progression > 0 mais sans leçons terminées
    print("🔍 Cours avec progression incohérente:")
    
    for user in User.objects.all():
        for course in Course.objects.filter(status='published'):
            progress = course.get_progress_for_user(user)
            completed_lessons = LessonProgress.objects.filter(
                user=user,
                lesson__chapter__course=course,
                completed=True
            ).count()
            
            if progress > 0 and completed_lessons == 0:
                print(f"   ⚠️ {user.username} - {course.title}: {progress}% mais 0 leçons terminées")

def fix_progression_issues():
    """Corriger les problèmes de progression"""
    print("🔧 Correction des problèmes de progression...\n")
    
    # Réinitialiser toutes les progressions pour les nouveaux utilisateurs
    test_users = ['test_progression_debug']
    
    for username in test_users:
        try:
            user = User.objects.get(username=username)
            print(f"🔄 Réinitialisation pour {username}:")
            
            # Supprimer toutes les progressions existantes
            deleted_count = LessonProgress.objects.filter(user=user).delete()[0]
            print(f"   🗑️ {deleted_count} progressions supprimées")
            
            # Supprimer les enrollments
            enrollment_count = CourseEnrollment.objects.filter(user=user).delete()[0]
            print(f"   🗑️ {enrollment_count} inscriptions supprimées")
            
            # Recréer les progressions avec les bonnes valeurs par défaut
            created_count = 0
            for course in Course.objects.filter(status='published'):
                for chapter in course.chapters.all():
                    for lesson in chapter.lessons.filter(is_published=True):
                        LessonProgress.objects.create(
                            user=user,
                            lesson=lesson,
                            completed=False,
                            time_spent=0
                        )
                        created_count += 1
            
            print(f"   ➕ {created_count} nouvelles progressions créées")
            
            # Vérifier la progression maintenant
            for course in Course.objects.filter(status='published'):
                progress = course.get_progress_for_user(user)
                print(f"   📈 {course.title}: {progress}%")
            
        except User.DoesNotExist:
            print(f"❌ Utilisateur {username} non trouvé")

def main():
    print("🚀 Débogage des problèmes de progression...\n")
    
    try:
        debug_progression_calculation()
        debug_lesson_progress_creation()
        check_data_inconsistencies()
        fix_progression_issues()
        
        print("\n✅ Débogage terminé!")
        
    except Exception as e:
        print(f"\n❌ Erreur lors du débogage: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
