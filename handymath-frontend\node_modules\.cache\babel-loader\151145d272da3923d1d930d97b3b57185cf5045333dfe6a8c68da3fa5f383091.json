{"ast": null, "code": "export var subtractDocs = {\n  name: 'subtract',\n  category: 'Operators',\n  syntax: ['x - y', 'subtract(x, y)'],\n  description: 'subtract two values.',\n  examples: ['a = 5.3 - 2', 'a + 2', '2/3 - 1/6', '2 * 3 - 3', '2.1 km - 500m'],\n  seealso: ['add']\n};", "map": {"version": 3, "names": ["subtractDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/subtract.js"], "sourcesContent": ["export var subtractDocs = {\n  name: 'subtract',\n  category: 'Operators',\n  syntax: ['x - y', 'subtract(x, y)'],\n  description: 'subtract two values.',\n  examples: ['a = 5.3 - 2', 'a + 2', '2/3 - 1/6', '2 * 3 - 3', '2.1 km - 500m'],\n  seealso: ['add']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC;EACnCC,WAAW,EAAE,sBAAsB;EACnCC,QAAQ,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;EAC7EC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}