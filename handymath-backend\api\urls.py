from django.urls import path, include
from rest_framework.routers import Defa<PERSON><PERSON><PERSON><PERSON>
from . import views
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

router = DefaultRouter()
router.register(r'users', views.UserViewSet, basename='user')
router.register(r'courses', views.CourseViewSet, basename='course')
router.register(r'equations', views.EquationViewSet, basename='equation')
router.register(r'exercises', views.ExerciseViewSet, basename='exercise')

urlpatterns = [
    path('', include(router.urls)),
    path('health/', views.health_check, name='health_check'),
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Endpoints admin
    path('admin/dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/users/', views.admin_users, name='admin_users'),
    path('admin/users/<int:user_id>/toggle/', views.admin_toggle_user, name='admin_toggle_user'),
    path('admin/system-health/', views.admin_system_health, name='admin_system_health'),

    # Endpoints étudiants
    path('progress/', views.get_user_progress, name='user_progress'),
    path('courses/recommendations/', views.course_recommendations, name='course_recommendations'),
    path('learning/path/', views.learning_path, name='learning_path'),
    path('learning/daily-challenge/', views.daily_challenge, name='daily_challenge'),
    path('learning/insights/', views.learning_insights, name='learning_insights'),

    # Endpoints de résolution d'équations
    path('equations/solve/', views.solve_equation, name='solve_equation'),
    path('equations/recognize/', views.recognize_equation_from_image, name='recognize_equation'),
    path('equations/recognize-test/', views.recognize_equation_from_image_test, name='recognize_equation_test'),

    # Endpoints professeur
    path('professor/dashboard/', views.professor_dashboard, name='professor_dashboard'),
    path('professor/students/', views.professor_students, name='professor_students'),
    path('professor/students/<int:student_id>/', views.professor_student_detail, name='professor_student_detail'),

    # Messages de contact
    path('contact/send/', views.contact_message_create, name='contact-message-create'),
    path('admin/contact/messages/', views.contact_messages_list, name='admin-contact-messages-list'),
    path('admin/contact/messages/<int:message_id>/', views.contact_message_detail, name='admin-contact-message-detail'),
    path('admin/contact/messages/<int:message_id>/update/', views.contact_message_update, name='admin-contact-message-update'),
    path('admin/contact/messages/<int:message_id>/resolve/', views.contact_message_mark_resolved, name='admin-contact-message-resolve'),
    path('admin/contact/stats/', views.contact_messages_stats, name='admin-contact-messages-stats'),
]



