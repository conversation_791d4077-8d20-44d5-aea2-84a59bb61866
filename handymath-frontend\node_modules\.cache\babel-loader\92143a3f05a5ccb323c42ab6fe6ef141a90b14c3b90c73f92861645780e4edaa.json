{"ast": null, "code": "import { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\nfunction scrapeMotionValuesFromProps(props, prevProps) {\n  const {\n    style\n  } = props;\n  const newValues = {};\n  for (const key in style) {\n    if (isMotionValue(style[key]) || prevProps.style && isMotionValue(prevProps.style[key]) || isForcedMotionValue(key, props)) {\n      newValues[key] = style[key];\n    }\n  }\n  return newValues;\n}\nexport { scrapeMotionValuesFromProps };", "map": {"version": 3, "names": ["isForcedMotionValue", "isMotionValue", "scrapeMotionValuesFromProps", "props", "prevProps", "style", "newValues", "key"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style && isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props)) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,aAAa,QAAQ,0CAA0C;AAExE,SAASC,2BAA2BA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnD,MAAM;IAAEC;EAAM,CAAC,GAAGF,KAAK;EACvB,MAAMG,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,MAAMC,GAAG,IAAIF,KAAK,EAAE;IACrB,IAAIJ,aAAa,CAACI,KAAK,CAACE,GAAG,CAAC,CAAC,IACxBH,SAAS,CAACC,KAAK,IAAIJ,aAAa,CAACG,SAAS,CAACC,KAAK,CAACE,GAAG,CAAC,CAAE,IACxDP,mBAAmB,CAACO,GAAG,EAAEJ,KAAK,CAAC,EAAE;MACjCG,SAAS,CAACC,GAAG,CAAC,GAAGF,KAAK,CAACE,GAAG,CAAC;IAC/B;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASJ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}