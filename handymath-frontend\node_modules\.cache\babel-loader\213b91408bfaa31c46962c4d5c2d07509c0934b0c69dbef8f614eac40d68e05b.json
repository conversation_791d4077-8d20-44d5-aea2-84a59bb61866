{"ast": null, "code": "export var filterDocs = {\n  name: 'filter',\n  category: 'Matrix',\n  syntax: ['filter(x, test)'],\n  description: 'Filter items in a matrix.',\n  examples: ['isPositive(x) = x > 0', 'filter([6, -2, -1, 4, 3], isPositive)', 'filter([6, -2, 0, 1, 0], x != 0)'],\n  seealso: ['sort', 'map', 'forEach']\n};", "map": {"version": 3, "names": ["filterDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/filter.js"], "sourcesContent": ["export var filterDocs = {\n  name: 'filter',\n  category: 'Matrix',\n  syntax: ['filter(x, test)'],\n  description: 'Filter items in a matrix.',\n  examples: ['isPositive(x) = x > 0', 'filter([6, -2, -1, 4, 3], isPositive)', 'filter([6, -2, 0, 1, 0], x != 0)'],\n  seealso: ['sort', 'map', 'forEach']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,iBAAiB,CAAC;EAC3BC,WAAW,EAAE,2BAA2B;EACxCC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,uCAAuC,EAAE,kCAAkC,CAAC;EAChHC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}