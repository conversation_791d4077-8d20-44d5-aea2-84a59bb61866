{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { isPositiveNumber } from '../../plain/number/index.js';\nimport { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nvar name = 'isPositive';\nvar dependencies = ['typed', 'config'];\nexport var createIsPositive = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config\n  } = _ref;\n  /**\n   * Test whether a value is positive: larger than zero.\n   * The function supports types `number`, `BigNumber`, `Fraction`, and `Unit`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isPositive(x)\n   *\n   * Examples:\n   *\n   *    math.isPositive(3)                     // returns true\n   *    math.isPositive(-2)                    // returns false\n   *    math.isPositive(0)                     // returns false\n   *    math.isPositive(-0)                    // returns false\n   *    math.isPositive(0.5)                   // returns true\n   *    math.isPositive(math.bignumber(2))     // returns true\n   *    math.isPositive(math.fraction(-2, 5))  // returns false\n   *    math.isPositive(math.fraction(1, 3))   // returns true\n   *    math.isPositive('2')                   // returns true\n   *    math.isPositive([2, 0, -3])            // returns [true, false, false]\n   *\n   * See also:\n   *\n   *    isNumeric, isZero, isNegative, isInteger\n   *\n   * @param {number | BigNumber | bigint | Fraction | Unit | Array | Matrix} x  Value to be tested\n   * @return {boolean}  Returns true when `x` is larger than zero.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: x => nearlyEqual(x, 0, config.relTol, config.absTol) ? false : isPositiveNumber(x),\n    BigNumber: x => bigNearlyEqual(x, new x.constructor(0), config.relTol, config.absTol) ? false : !x.isNeg() && !x.isZero() && !x.isNaN(),\n    bigint: x => x > 0n,\n    Fraction: x => x.s > 0n && x.n > 0n,\n    Unit: typed.referToSelf(self => x => typed.find(self, x.valueType())(x.value)),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "factory", "isPositiveNumber", "nearlyEqual", "bigNearlyEqual", "name", "dependencies", "createIsPositive", "_ref", "typed", "config", "number", "x", "relTol", "absTol", "BigNumber", "constructor", "isNeg", "isZero", "isNaN", "bigint", "Fraction", "s", "n", "Unit", "referToSelf", "self", "find", "valueType", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/isPositive.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { isPositiveNumber } from '../../plain/number/index.js';\nimport { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nvar name = 'isPositive';\nvar dependencies = ['typed', 'config'];\nexport var createIsPositive = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config\n  } = _ref;\n  /**\n   * Test whether a value is positive: larger than zero.\n   * The function supports types `number`, `BigNumber`, `Fraction`, and `Unit`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isPositive(x)\n   *\n   * Examples:\n   *\n   *    math.isPositive(3)                     // returns true\n   *    math.isPositive(-2)                    // returns false\n   *    math.isPositive(0)                     // returns false\n   *    math.isPositive(-0)                    // returns false\n   *    math.isPositive(0.5)                   // returns true\n   *    math.isPositive(math.bignumber(2))     // returns true\n   *    math.isPositive(math.fraction(-2, 5))  // returns false\n   *    math.isPositive(math.fraction(1, 3))   // returns true\n   *    math.isPositive('2')                   // returns true\n   *    math.isPositive([2, 0, -3])            // returns [true, false, false]\n   *\n   * See also:\n   *\n   *    isNumeric, isZero, isNegative, isInteger\n   *\n   * @param {number | BigNumber | bigint | Fraction | Unit | Array | Matrix} x  Value to be tested\n   * @return {boolean}  Returns true when `x` is larger than zero.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: x => nearlyEqual(x, 0, config.relTol, config.absTol) ? false : isPositiveNumber(x),\n    BigNumber: x => bigNearlyEqual(x, new x.constructor(0), config.relTol, config.absTol) ? false : !x.isNeg() && !x.isZero() && !x.isNaN(),\n    bigint: x => x > 0n,\n    Fraction: x => x.s > 0n && x.n > 0n,\n    Unit: typed.referToSelf(self => x => typed.find(self, x.valueType())(x.value)),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,WAAW,IAAIC,cAAc,QAAQ,sCAAsC;AACpF,SAASD,WAAW,QAAQ,uBAAuB;AACnD,IAAIE,IAAI,GAAG,YAAY;AACvB,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AACtC,OAAO,IAAIC,gBAAgB,GAAG,eAAeN,OAAO,CAACI,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC/E,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBM,MAAM,EAAEC,CAAC,IAAIT,WAAW,CAACS,CAAC,EAAE,CAAC,EAAEF,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,MAAM,CAAC,GAAG,KAAK,GAAGZ,gBAAgB,CAACU,CAAC,CAAC;IAC1FG,SAAS,EAAEH,CAAC,IAAIR,cAAc,CAACQ,CAAC,EAAE,IAAIA,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC,EAAEN,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,MAAM,CAAC,GAAG,KAAK,GAAG,CAACF,CAAC,CAACK,KAAK,CAAC,CAAC,IAAI,CAACL,CAAC,CAACM,MAAM,CAAC,CAAC,IAAI,CAACN,CAAC,CAACO,KAAK,CAAC,CAAC;IACvIC,MAAM,EAAER,CAAC,IAAIA,CAAC,GAAG,EAAE;IACnBS,QAAQ,EAAET,CAAC,IAAIA,CAAC,CAACU,CAAC,GAAG,EAAE,IAAIV,CAAC,CAACW,CAAC,GAAG,EAAE;IACnCC,IAAI,EAAEf,KAAK,CAACgB,WAAW,CAACC,IAAI,IAAId,CAAC,IAAIH,KAAK,CAACkB,IAAI,CAACD,IAAI,EAAEd,CAAC,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAChB,CAAC,CAACiB,KAAK,CAAC,CAAC;IAC9E,gBAAgB,EAAEpB,KAAK,CAACgB,WAAW,CAACC,IAAI,IAAId,CAAC,IAAIZ,OAAO,CAACY,CAAC,EAAEc,IAAI,CAAC;EACnE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}