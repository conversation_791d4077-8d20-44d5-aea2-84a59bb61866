[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\index.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\App.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentLayout.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentBreadcrumbs.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\FloatingNavigation.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\icons\\NavigationIcons.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ForgotPasswordPage.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ResetPasswordPage.tsx": "76"}, {"size": 277, "mtime": 1749080956168, "results": "77", "hashOfConfig": "78"}, {"size": 6024, "mtime": 1749087688409, "results": "79", "hashOfConfig": "78"}, {"size": 802, "mtime": 1749046473890, "results": "80", "hashOfConfig": "78"}, {"size": 16715, "mtime": 1749066387299, "results": "81", "hashOfConfig": "78"}, {"size": 31759, "mtime": 1748983338825, "results": "82", "hashOfConfig": "78"}, {"size": 218, "mtime": 1748952089453, "results": "83", "hashOfConfig": "78"}, {"size": 1931, "mtime": 1748954075713, "results": "84", "hashOfConfig": "78"}, {"size": 4380, "mtime": 1749046473889, "results": "85", "hashOfConfig": "78"}, {"size": 4293, "mtime": 1749292696790, "results": "86", "hashOfConfig": "78"}, {"size": 7365, "mtime": 1749046902629, "results": "87", "hashOfConfig": "78"}, {"size": 14780, "mtime": 1749087758943, "results": "88", "hashOfConfig": "78"}, {"size": 15014, "mtime": 1749047338072, "results": "89", "hashOfConfig": "78"}, {"size": 21463, "mtime": 1749071198524, "results": "90", "hashOfConfig": "78"}, {"size": 15273, "mtime": 1749047898131, "results": "91", "hashOfConfig": "78"}, {"size": 15195, "mtime": 1749048128423, "results": "92", "hashOfConfig": "78"}, {"size": 14239, "mtime": 1749048393478, "results": "93", "hashOfConfig": "78"}, {"size": 19109, "mtime": 1749051118945, "results": "94", "hashOfConfig": "78"}, {"size": 12980, "mtime": 1749051136411, "results": "95", "hashOfConfig": "78"}, {"size": 10229, "mtime": 1749070380168, "results": "96", "hashOfConfig": "78"}, {"size": 12569, "mtime": 1749051153981, "results": "97", "hashOfConfig": "78"}, {"size": 8794, "mtime": 1749070718719, "results": "98", "hashOfConfig": "78"}, {"size": 18459, "mtime": 1749070497141, "results": "99", "hashOfConfig": "78"}, {"size": 7692, "mtime": 1749051903569, "results": "100", "hashOfConfig": "78"}, {"size": 5122, "mtime": 1749052811678, "results": "101", "hashOfConfig": "78"}, {"size": 20371, "mtime": 1749070759007, "results": "102", "hashOfConfig": "78"}, {"size": 2721, "mtime": 1749293050442, "results": "103", "hashOfConfig": "78"}, {"size": 6715, "mtime": 1749068251807, "results": "104", "hashOfConfig": "78"}, {"size": 8736, "mtime": 1749073183622, "results": "105", "hashOfConfig": "78"}, {"size": 7800, "mtime": 1749076875344, "results": "106", "hashOfConfig": "78"}, {"size": 11434, "mtime": 1749076479018, "results": "107", "hashOfConfig": "78"}, {"size": 12684, "mtime": 1749087573195, "results": "108", "hashOfConfig": "78"}, {"size": 13617, "mtime": 1749073014242, "results": "109", "hashOfConfig": "78"}, {"size": 6024, "mtime": 1749072916570, "results": "110", "hashOfConfig": "78"}, {"size": 6853, "mtime": 1749076802984, "results": "111", "hashOfConfig": "78"}, {"size": 19294, "mtime": 1749087644351, "results": "112", "hashOfConfig": "78"}, {"size": 277, "mtime": 1749080956000, "results": "113", "hashOfConfig": "114"}, {"size": 6308, "mtime": 1751115721179, "results": "115", "hashOfConfig": "114"}, {"size": 4293, "mtime": 1749292696000, "results": "116", "hashOfConfig": "114"}, {"size": 5136, "mtime": 1751115162478, "results": "117", "hashOfConfig": "114"}, {"size": 17912, "mtime": 1751103161271, "results": "118", "hashOfConfig": "114"}, {"size": 10229, "mtime": 1749070380000, "results": "119", "hashOfConfig": "114"}, {"size": 2721, "mtime": 1749293050000, "results": "120", "hashOfConfig": "114"}, {"size": 18282, "mtime": 1751111512818, "results": "121", "hashOfConfig": "114"}, {"size": 15383, "mtime": 1751111692857, "results": "122", "hashOfConfig": "114"}, {"size": 7656, "mtime": 1751112337020, "results": "123", "hashOfConfig": "114"}, {"size": 19341, "mtime": 1751112287604, "results": "124", "hashOfConfig": "114"}, {"size": 13263, "mtime": 1751115090405, "results": "125", "hashOfConfig": "114"}, {"size": 14780, "mtime": 1749087758000, "results": "126", "hashOfConfig": "114"}, {"size": 8794, "mtime": 1749070718000, "results": "127", "hashOfConfig": "114"}, {"size": 21463, "mtime": 1749071198000, "results": "128", "hashOfConfig": "114"}, {"size": 13559, "mtime": 1751112312867, "results": "129", "hashOfConfig": "114"}, {"size": 15014, "mtime": 1749047338000, "results": "130", "hashOfConfig": "114"}, {"size": 20371, "mtime": 1749070758000, "results": "131", "hashOfConfig": "114"}, {"size": 15273, "mtime": 1749047898000, "results": "132", "hashOfConfig": "114"}, {"size": 13534, "mtime": 1751114873860, "results": "133", "hashOfConfig": "114"}, {"size": 7365, "mtime": 1749046902000, "results": "134", "hashOfConfig": "114"}, {"size": 7800, "mtime": 1749076874000, "results": "135", "hashOfConfig": "114"}, {"size": 12684, "mtime": 1749087572000, "results": "136", "hashOfConfig": "114"}, {"size": 11287, "mtime": 1751114168030, "results": "137", "hashOfConfig": "114"}, {"size": 4380, "mtime": 1749046472000, "results": "138", "hashOfConfig": "114"}, {"size": 6853, "mtime": 1749076802000, "results": "139", "hashOfConfig": "114"}, {"size": 19294, "mtime": 1749087644000, "results": "140", "hashOfConfig": "114"}, {"size": 802, "mtime": 1749046472000, "results": "141", "hashOfConfig": "114"}, {"size": 6024, "mtime": 1749072916000, "results": "142", "hashOfConfig": "114"}, {"size": 16567, "mtime": 1751110931043, "results": "143", "hashOfConfig": "114"}, {"size": 6715, "mtime": 1749068250000, "results": "144", "hashOfConfig": "114"}, {"size": 8736, "mtime": 1749073182000, "results": "145", "hashOfConfig": "114"}, {"size": 1931, "mtime": 1748954074000, "results": "146", "hashOfConfig": "114"}, {"size": 4544, "mtime": 1751113812656, "results": "147", "hashOfConfig": "114"}, {"size": 9318, "mtime": 1751110957213, "results": "148", "hashOfConfig": "114"}, {"size": 4428, "mtime": 1751102915634, "results": "149", "hashOfConfig": "114"}, {"size": 6554, "mtime": 1751102890658, "results": "150", "hashOfConfig": "114"}, {"size": 8843, "mtime": 1751114297193, "results": "151", "hashOfConfig": "114"}, {"size": 7583, "mtime": 1751114250173, "results": "152", "hashOfConfig": "114"}, {"size": 10091, "mtime": 1751116867764, "results": "153", "hashOfConfig": "114"}, {"size": 15785, "mtime": 1751115615311, "results": "154", "hashOfConfig": "114"}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1spffp8", {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o7876w", {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["383", "384"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["385"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["386"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["387"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["388"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["389", "390", "391", "392"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["393", "394", "395"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["396"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["397"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["398"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["399"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["400"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["401", "402", "403", "404"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["405", "406", "407", "408"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["409", "410"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["411"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["412", "413"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["414", "415", "416", "417"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["418", "419", "420", "421", "422", "423"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["424"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["425"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["426"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["427"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["428"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["429"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["430", "431", "432", "433"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["434", "435", "436", "437"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["438", "439"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentLayout.tsx", ["440"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentBreadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\FloatingNavigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\icons\\NavigationIcons.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ForgotPasswordPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ResetPasswordPage.tsx", [], [], {"ruleId": "441", "severity": 1, "message": "442", "line": 68, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 68, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "446", "line": 77, "column": 6, "nodeType": "447", "endLine": 77, "endColumn": 12, "suggestions": "448"}, {"ruleId": "445", "severity": 1, "message": "449", "line": 52, "column": 6, "nodeType": "447", "endLine": 52, "endColumn": 8, "suggestions": "450"}, {"ruleId": "445", "severity": 1, "message": "451", "line": 60, "column": 6, "nodeType": "447", "endLine": 60, "endColumn": 12, "suggestions": "452", "suppressions": "453"}, {"ruleId": "445", "severity": 1, "message": "454", "line": 82, "column": 6, "nodeType": "447", "endLine": 82, "endColumn": 22, "suggestions": "455"}, {"ruleId": "445", "severity": 1, "message": "456", "line": 60, "column": 6, "nodeType": "447", "endLine": 60, "endColumn": 16, "suggestions": "457"}, {"ruleId": "441", "severity": 1, "message": "458", "line": 69, "column": 22, "nodeType": "443", "messageId": "444", "endLine": 69, "endColumn": 35}, {"ruleId": "445", "severity": 1, "message": "459", "line": 79, "column": 6, "nodeType": "447", "endLine": 79, "endColumn": 16, "suggestions": "460"}, {"ruleId": "445", "severity": 1, "message": "461", "line": 98, "column": 6, "nodeType": "447", "endLine": 98, "endColumn": 48, "suggestions": "462"}, {"ruleId": "441", "severity": 1, "message": "463", "line": 173, "column": 60, "nodeType": "443", "messageId": "444", "endLine": 173, "endColumn": 71}, {"ruleId": "441", "severity": 1, "message": "442", "line": 43, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 43, "endColumn": 20}, {"ruleId": "441", "severity": 1, "message": "464", "line": 49, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 49, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "465", "line": 68, "column": 6, "nodeType": "447", "endLine": 68, "endColumn": 21, "suggestions": "466"}, {"ruleId": "445", "severity": 1, "message": "467", "line": 61, "column": 6, "nodeType": "447", "endLine": 61, "endColumn": 16, "suggestions": "468"}, {"ruleId": "445", "severity": 1, "message": "469", "line": 113, "column": 6, "nodeType": "447", "endLine": 113, "endColumn": 21, "suggestions": "470"}, {"ruleId": "445", "severity": 1, "message": "471", "line": 39, "column": 6, "nodeType": "447", "endLine": 39, "endColumn": 12, "suggestions": "472"}, {"ruleId": "445", "severity": 1, "message": "473", "line": 45, "column": 6, "nodeType": "447", "endLine": 45, "endColumn": 12, "suggestions": "474"}, {"ruleId": "441", "severity": 1, "message": "475", "line": 1, "column": 27, "nodeType": "443", "messageId": "444", "endLine": 1, "endColumn": 36}, {"ruleId": "476", "severity": 1, "message": "477", "line": 139, "column": 17, "nodeType": "478", "endLine": 142, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "477", "line": 161, "column": 15, "nodeType": "478", "endLine": 164, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "477", "line": 167, "column": 15, "nodeType": "478", "endLine": 170, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "477", "line": 173, "column": 15, "nodeType": "478", "endLine": 176, "endColumn": 16}, {"ruleId": "441", "severity": 1, "message": "479", "line": 50, "column": 10, "nodeType": "443", "messageId": "444", "endLine": 50, "endColumn": 25}, {"ruleId": "441", "severity": 1, "message": "480", "line": 51, "column": 10, "nodeType": "443", "messageId": "444", "endLine": 51, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "481", "line": 74, "column": 6, "nodeType": "447", "endLine": 74, "endColumn": 38, "suggestions": "482"}, {"ruleId": "441", "severity": 1, "message": "483", "line": 113, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 113, "endColumn": 27}, {"ruleId": "441", "severity": 1, "message": "484", "line": 6, "column": 8, "nodeType": "443", "messageId": "444", "endLine": 6, "endColumn": 21}, {"ruleId": "445", "severity": 1, "message": "471", "line": 41, "column": 6, "nodeType": "447", "endLine": 41, "endColumn": 12, "suggestions": "485"}, {"ruleId": "445", "severity": 1, "message": "456", "line": 61, "column": 6, "nodeType": "447", "endLine": 61, "endColumn": 16, "suggestions": "486"}, {"ruleId": "441", "severity": 1, "message": "487", "line": 2, "column": 10, "nodeType": "443", "messageId": "444", "endLine": 2, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "469", "line": 114, "column": 6, "nodeType": "447", "endLine": 114, "endColumn": 21, "suggestions": "488"}, {"ruleId": "441", "severity": 1, "message": "458", "line": 70, "column": 22, "nodeType": "443", "messageId": "444", "endLine": 70, "endColumn": 35}, {"ruleId": "445", "severity": 1, "message": "459", "line": 80, "column": 6, "nodeType": "447", "endLine": 80, "endColumn": 16, "suggestions": "489"}, {"ruleId": "445", "severity": 1, "message": "461", "line": 99, "column": 6, "nodeType": "447", "endLine": 99, "endColumn": 48, "suggestions": "490"}, {"ruleId": "441", "severity": 1, "message": "463", "line": 174, "column": 60, "nodeType": "443", "messageId": "444", "endLine": 174, "endColumn": 71}, {"ruleId": "441", "severity": 1, "message": "491", "line": 6, "column": 8, "nodeType": "443", "messageId": "444", "endLine": 6, "endColumn": 18}, {"ruleId": "441", "severity": 1, "message": "442", "line": 52, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 52, "endColumn": 20}, {"ruleId": "441", "severity": 1, "message": "464", "line": 58, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 58, "endColumn": 20}, {"ruleId": "441", "severity": 1, "message": "492", "line": 71, "column": 10, "nodeType": "443", "messageId": "444", "endLine": 71, "endColumn": 20}, {"ruleId": "441", "severity": 1, "message": "493", "line": 71, "column": 22, "nodeType": "443", "messageId": "444", "endLine": 71, "endColumn": 35}, {"ruleId": "445", "severity": 1, "message": "465", "line": 83, "column": 6, "nodeType": "447", "endLine": 83, "endColumn": 21, "suggestions": "494"}, {"ruleId": "445", "severity": 1, "message": "451", "line": 60, "column": 6, "nodeType": "447", "endLine": 60, "endColumn": 12, "suggestions": "495", "suppressions": "496"}, {"ruleId": "445", "severity": 1, "message": "454", "line": 82, "column": 6, "nodeType": "447", "endLine": 82, "endColumn": 22, "suggestions": "497"}, {"ruleId": "445", "severity": 1, "message": "467", "line": 62, "column": 6, "nodeType": "447", "endLine": 62, "endColumn": 16, "suggestions": "498"}, {"ruleId": "441", "severity": 1, "message": "475", "line": 1, "column": 27, "nodeType": "443", "messageId": "444", "endLine": 1, "endColumn": 36}, {"ruleId": "445", "severity": 1, "message": "473", "line": 45, "column": 6, "nodeType": "447", "endLine": 45, "endColumn": 12, "suggestions": "499"}, {"ruleId": "445", "severity": 1, "message": "449", "line": 52, "column": 6, "nodeType": "447", "endLine": 52, "endColumn": 8, "suggestions": "500"}, {"ruleId": "476", "severity": 1, "message": "477", "line": 139, "column": 17, "nodeType": "478", "endLine": 142, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "477", "line": 161, "column": 15, "nodeType": "478", "endLine": 164, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "477", "line": 167, "column": 15, "nodeType": "478", "endLine": 170, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "477", "line": 173, "column": 15, "nodeType": "478", "endLine": 176, "endColumn": 16}, {"ruleId": "441", "severity": 1, "message": "479", "line": 50, "column": 10, "nodeType": "443", "messageId": "444", "endLine": 50, "endColumn": 25}, {"ruleId": "441", "severity": 1, "message": "480", "line": 51, "column": 10, "nodeType": "443", "messageId": "444", "endLine": 51, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "481", "line": 74, "column": 6, "nodeType": "447", "endLine": 74, "endColumn": 38, "suggestions": "501"}, {"ruleId": "441", "severity": 1, "message": "483", "line": 113, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 113, "endColumn": 27}, {"ruleId": "441", "severity": 1, "message": "442", "line": 70, "column": 9, "nodeType": "443", "messageId": "444", "endLine": 70, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "446", "line": 79, "column": 6, "nodeType": "447", "endLine": 79, "endColumn": 12, "suggestions": "502"}, {"ruleId": "441", "severity": 1, "message": "503", "line": 35, "column": 11, "nodeType": "443", "messageId": "444", "endLine": 35, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'showSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudentData'. Either include it or remove the dependency array.", "ArrayExpression", ["504"], "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["505"], "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["506"], ["507"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["508"], "React Hook useEffect has a missing dependency: 'fetchCourse'. Either include it or remove the dependency array.", ["509"], "'setUserAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercise'. Either include it or remove the dependency array.", ["510"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["511"], "'explanation' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercises'. Either include it or remove the dependency array.", ["512"], "React Hook useEffect has a missing dependency: 'fetchLesson'. Either include it or remove the dependency array.", ["513"], "React Hook useEffect has a missing dependency: 'renderFunction2D'. Either include it or remove the dependency array.", ["514"], "React Hook useEffect has a missing dependency: 'fetchProgressData'. Either include it or remove the dependency array.", ["515"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["516"], "'useEffect' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'selectedMessage' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["517"], "'handleStatusUpdate' is assigned a value but never used.", "'TabNavigation' is defined but never used.", ["518"], ["519"], "'motion' is defined but never used.", ["520"], ["521"], ["522"], "'Pagination' is defined but never used.", "'pagination' is assigned a value but never used.", "'setPagination' is assigned a value but never used.", ["523"], ["524"], ["525"], ["526"], ["527"], ["528"], ["529"], ["530"], ["531"], "'user' is assigned a value but never used.", {"desc": "532", "fix": "533"}, {"desc": "534", "fix": "535"}, {"desc": "536", "fix": "537"}, {"kind": "538", "justification": "539"}, {"desc": "540", "fix": "541"}, {"desc": "542", "fix": "543"}, {"desc": "544", "fix": "545"}, {"desc": "546", "fix": "547"}, {"desc": "548", "fix": "549"}, {"desc": "550", "fix": "551"}, {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "556", "fix": "557"}, {"desc": "558", "fix": "559"}, {"desc": "554", "fix": "560"}, {"desc": "542", "fix": "561"}, {"desc": "552", "fix": "562"}, {"desc": "544", "fix": "563"}, {"desc": "546", "fix": "564"}, {"desc": "548", "fix": "565"}, {"desc": "536", "fix": "566"}, {"kind": "538", "justification": "539"}, {"desc": "540", "fix": "567"}, {"desc": "550", "fix": "568"}, {"desc": "556", "fix": "569"}, {"desc": "534", "fix": "570"}, {"desc": "558", "fix": "571"}, {"desc": "532", "fix": "572"}, "Update the dependencies array to be: [fetchStudentData, user]", {"range": "573", "text": "574"}, "Update the dependencies array to be: [removeNotification]", {"range": "575", "text": "576"}, "Update the dependencies array to be: [fetchAdminData, user]", {"range": "577", "text": "578"}, "directive", "", "Update the dependencies array to be: [user, navigate, loadUsers]", {"range": "579", "text": "580"}, "Update the dependencies array to be: [user, id, fetchCourse]", {"range": "581", "text": "582"}, "Update the dependencies array to be: [user, id, fetchExercise]", {"range": "583", "text": "584"}, "Update the dependencies array to be: [timeLeft, exercise?.user_attempt?.status, handleSubmit]", {"range": "585", "text": "586"}, "Update the dependencies array to be: [user, filters, fetchExercises]", {"range": "587", "text": "588"}, "Update the dependencies array to be: [user, id, fetchLesson]", {"range": "589", "text": "590"}, "Update the dependencies array to be: [functionInput, renderFunction2D]", {"range": "591", "text": "592"}, "Update the dependencies array to be: [fetchProgressData, user]", {"range": "593", "text": "594"}, "Update the dependencies array to be: [fetchProfile, user]", {"range": "595", "text": "596"}, "Update the dependencies array to be: [user, filters, pagination.page, fetchMessages]", {"range": "597", "text": "598"}, {"range": "599", "text": "594"}, {"range": "600", "text": "582"}, {"range": "601", "text": "592"}, {"range": "602", "text": "584"}, {"range": "603", "text": "586"}, {"range": "604", "text": "588"}, {"range": "605", "text": "578"}, {"range": "606", "text": "580"}, {"range": "607", "text": "590"}, {"range": "608", "text": "596"}, {"range": "609", "text": "576"}, {"range": "610", "text": "598"}, {"range": "611", "text": "574"}, [2030, 2036], "[fetchStudentData, user]", [1627, 1629], "[removeNotification]", [1564, 1570], "[fetchAdmin<PERSON><PERSON>, user]", [2196, 2212], "[user, navigate, loadUsers]", [1368, 1378], "[user, id, fetchCourse]", [2224, 2234], "[user, id, fetchExercise]", [2759, 2801], "[timeLeft, exercise?.user_attempt?.status, handleSubmit]", [1688, 1703], "[user, filters, fetchExercises]", [1549, 1559], "[user, id, fetchLesson]", [3341, 3356], "[functionInput, renderFunction2D]", [1044, 1050], "[fetchProgress<PERSON>ata, user]", [1117, 1123], "[fetchProfile, user]", [1913, 1945], "[user, filters, pagination.page, fetchMessages]", [1160, 1166], [1425, 1435], [3398, 3413], [2281, 2291], [2816, 2858], [2068, 2083], [1564, 1570], [2196, 2212], [1606, 1616], [1119, 1125], [1627, 1629], [1913, 1945], [2162, 2168]]