[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\index.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\App.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentLayout.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentBreadcrumbs.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\FloatingNavigation.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\icons\\NavigationIcons.tsx": "74"}, {"size": 277, "mtime": 1749080956168, "results": "75", "hashOfConfig": "76"}, {"size": 6024, "mtime": 1749087688409, "results": "77", "hashOfConfig": "76"}, {"size": 802, "mtime": 1749046473890, "results": "78", "hashOfConfig": "76"}, {"size": 16715, "mtime": 1749066387299, "results": "79", "hashOfConfig": "76"}, {"size": 31759, "mtime": 1748983338825, "results": "80", "hashOfConfig": "76"}, {"size": 218, "mtime": 1748952089453, "results": "81", "hashOfConfig": "76"}, {"size": 1931, "mtime": 1748954075713, "results": "82", "hashOfConfig": "76"}, {"size": 4380, "mtime": 1749046473889, "results": "83", "hashOfConfig": "76"}, {"size": 4293, "mtime": 1749292696790, "results": "84", "hashOfConfig": "76"}, {"size": 7365, "mtime": 1749046902629, "results": "85", "hashOfConfig": "76"}, {"size": 14780, "mtime": 1749087758943, "results": "86", "hashOfConfig": "76"}, {"size": 15014, "mtime": 1749047338072, "results": "87", "hashOfConfig": "76"}, {"size": 21463, "mtime": 1749071198524, "results": "88", "hashOfConfig": "76"}, {"size": 15273, "mtime": 1749047898131, "results": "89", "hashOfConfig": "76"}, {"size": 15195, "mtime": 1749048128423, "results": "90", "hashOfConfig": "76"}, {"size": 14239, "mtime": 1749048393478, "results": "91", "hashOfConfig": "76"}, {"size": 19109, "mtime": 1749051118945, "results": "92", "hashOfConfig": "76"}, {"size": 12980, "mtime": 1749051136411, "results": "93", "hashOfConfig": "76"}, {"size": 10229, "mtime": 1749070380168, "results": "94", "hashOfConfig": "76"}, {"size": 12569, "mtime": 1749051153981, "results": "95", "hashOfConfig": "76"}, {"size": 8794, "mtime": 1749070718719, "results": "96", "hashOfConfig": "76"}, {"size": 18459, "mtime": 1749070497141, "results": "97", "hashOfConfig": "76"}, {"size": 7692, "mtime": 1749051903569, "results": "98", "hashOfConfig": "76"}, {"size": 5122, "mtime": 1749052811678, "results": "99", "hashOfConfig": "76"}, {"size": 20371, "mtime": 1749070759007, "results": "100", "hashOfConfig": "76"}, {"size": 2721, "mtime": 1749293050442, "results": "101", "hashOfConfig": "76"}, {"size": 6715, "mtime": 1749068251807, "results": "102", "hashOfConfig": "76"}, {"size": 8736, "mtime": 1749073183622, "results": "103", "hashOfConfig": "76"}, {"size": 7800, "mtime": 1749076875344, "results": "104", "hashOfConfig": "76"}, {"size": 11434, "mtime": 1749076479018, "results": "105", "hashOfConfig": "76"}, {"size": 12684, "mtime": 1749087573195, "results": "106", "hashOfConfig": "76"}, {"size": 13617, "mtime": 1749073014242, "results": "107", "hashOfConfig": "76"}, {"size": 6024, "mtime": 1749072916570, "results": "108", "hashOfConfig": "76"}, {"size": 6853, "mtime": 1749076802984, "results": "109", "hashOfConfig": "76"}, {"size": 19294, "mtime": 1749087644351, "results": "110", "hashOfConfig": "76"}, {"size": 277, "mtime": 1749080956000, "results": "111", "hashOfConfig": "112"}, {"size": 6024, "mtime": 1749087688000, "results": "113", "hashOfConfig": "112"}, {"size": 4293, "mtime": 1749292696000, "results": "114", "hashOfConfig": "112"}, {"size": 5152, "mtime": 1751112492844, "results": "115", "hashOfConfig": "112"}, {"size": 17912, "mtime": 1751103161271, "results": "116", "hashOfConfig": "112"}, {"size": 10229, "mtime": 1749070380000, "results": "117", "hashOfConfig": "112"}, {"size": 2721, "mtime": 1749293050000, "results": "118", "hashOfConfig": "112"}, {"size": 18282, "mtime": 1751111512818, "results": "119", "hashOfConfig": "112"}, {"size": 15383, "mtime": 1751111692857, "results": "120", "hashOfConfig": "112"}, {"size": 7656, "mtime": 1751112337020, "results": "121", "hashOfConfig": "112"}, {"size": 19341, "mtime": 1751112287604, "results": "122", "hashOfConfig": "112"}, {"size": 13278, "mtime": 1751112415947, "results": "123", "hashOfConfig": "112"}, {"size": 14780, "mtime": 1749087758000, "results": "124", "hashOfConfig": "112"}, {"size": 8794, "mtime": 1749070718000, "results": "125", "hashOfConfig": "112"}, {"size": 21463, "mtime": 1749071198000, "results": "126", "hashOfConfig": "112"}, {"size": 13559, "mtime": 1751112312867, "results": "127", "hashOfConfig": "112"}, {"size": 15014, "mtime": 1749047338000, "results": "128", "hashOfConfig": "112"}, {"size": 20371, "mtime": 1749070758000, "results": "129", "hashOfConfig": "112"}, {"size": 15273, "mtime": 1749047898000, "results": "130", "hashOfConfig": "112"}, {"size": 13617, "mtime": 1749073014000, "results": "131", "hashOfConfig": "112"}, {"size": 7365, "mtime": 1749046902000, "results": "132", "hashOfConfig": "112"}, {"size": 7800, "mtime": 1749076874000, "results": "133", "hashOfConfig": "112"}, {"size": 12684, "mtime": 1749087572000, "results": "134", "hashOfConfig": "112"}, {"size": 11434, "mtime": 1749076478000, "results": "135", "hashOfConfig": "112"}, {"size": 4380, "mtime": 1749046472000, "results": "136", "hashOfConfig": "112"}, {"size": 6853, "mtime": 1749076802000, "results": "137", "hashOfConfig": "112"}, {"size": 19294, "mtime": 1749087644000, "results": "138", "hashOfConfig": "112"}, {"size": 802, "mtime": 1749046472000, "results": "139", "hashOfConfig": "112"}, {"size": 6024, "mtime": 1749072916000, "results": "140", "hashOfConfig": "112"}, {"size": 16567, "mtime": 1751110931043, "results": "141", "hashOfConfig": "112"}, {"size": 6715, "mtime": 1749068250000, "results": "142", "hashOfConfig": "112"}, {"size": 8736, "mtime": 1749073182000, "results": "143", "hashOfConfig": "112"}, {"size": 1931, "mtime": 1748954074000, "results": "144", "hashOfConfig": "112"}, {"size": 3330, "mtime": 1751112152231, "results": "145", "hashOfConfig": "112"}, {"size": 9318, "mtime": 1751110957213, "results": "146", "hashOfConfig": "112"}, {"size": 4428, "mtime": 1751102915634, "results": "147", "hashOfConfig": "112"}, {"size": 6554, "mtime": 1751102890658, "results": "148", "hashOfConfig": "112"}, {"size": 6018, "mtime": 1751103269793, "results": "149", "hashOfConfig": "112"}, {"size": 6614, "mtime": 1751104058758, "results": "150", "hashOfConfig": "112"}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1spffp8", {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o7876w", {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["373", "374"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["375"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["376"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["377"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["378"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["379", "380", "381", "382"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["383", "384", "385"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["386"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["387"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["388"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["389"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["390"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["391", "392", "393", "394"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["395", "396", "397", "398"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["399"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["400"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["401", "402"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["403", "404", "405", "406"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["407"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["408"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["409"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["410"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["411"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["412"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["413"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["414", "415", "416", "417"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["418", "419", "420", "421"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["422", "423"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\StudentBreadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\FloatingNavigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\icons\\NavigationIcons.tsx", [], [], {"ruleId": "424", "severity": 1, "message": "425", "line": 68, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 68, "endColumn": 20}, {"ruleId": "428", "severity": 1, "message": "429", "line": 77, "column": 6, "nodeType": "430", "endLine": 77, "endColumn": 12, "suggestions": "431"}, {"ruleId": "428", "severity": 1, "message": "432", "line": 52, "column": 6, "nodeType": "430", "endLine": 52, "endColumn": 8, "suggestions": "433"}, {"ruleId": "428", "severity": 1, "message": "434", "line": 60, "column": 6, "nodeType": "430", "endLine": 60, "endColumn": 12, "suggestions": "435", "suppressions": "436"}, {"ruleId": "428", "severity": 1, "message": "437", "line": 82, "column": 6, "nodeType": "430", "endLine": 82, "endColumn": 22, "suggestions": "438"}, {"ruleId": "428", "severity": 1, "message": "439", "line": 60, "column": 6, "nodeType": "430", "endLine": 60, "endColumn": 16, "suggestions": "440"}, {"ruleId": "424", "severity": 1, "message": "441", "line": 69, "column": 22, "nodeType": "426", "messageId": "427", "endLine": 69, "endColumn": 35}, {"ruleId": "428", "severity": 1, "message": "442", "line": 79, "column": 6, "nodeType": "430", "endLine": 79, "endColumn": 16, "suggestions": "443"}, {"ruleId": "428", "severity": 1, "message": "444", "line": 98, "column": 6, "nodeType": "430", "endLine": 98, "endColumn": 48, "suggestions": "445"}, {"ruleId": "424", "severity": 1, "message": "446", "line": 173, "column": 60, "nodeType": "426", "messageId": "427", "endLine": 173, "endColumn": 71}, {"ruleId": "424", "severity": 1, "message": "425", "line": 43, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 43, "endColumn": 20}, {"ruleId": "424", "severity": 1, "message": "447", "line": 49, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 49, "endColumn": 20}, {"ruleId": "428", "severity": 1, "message": "448", "line": 68, "column": 6, "nodeType": "430", "endLine": 68, "endColumn": 21, "suggestions": "449"}, {"ruleId": "428", "severity": 1, "message": "450", "line": 61, "column": 6, "nodeType": "430", "endLine": 61, "endColumn": 16, "suggestions": "451"}, {"ruleId": "428", "severity": 1, "message": "452", "line": 113, "column": 6, "nodeType": "430", "endLine": 113, "endColumn": 21, "suggestions": "453"}, {"ruleId": "428", "severity": 1, "message": "454", "line": 39, "column": 6, "nodeType": "430", "endLine": 39, "endColumn": 12, "suggestions": "455"}, {"ruleId": "428", "severity": 1, "message": "456", "line": 45, "column": 6, "nodeType": "430", "endLine": 45, "endColumn": 12, "suggestions": "457"}, {"ruleId": "424", "severity": 1, "message": "458", "line": 1, "column": 27, "nodeType": "426", "messageId": "427", "endLine": 1, "endColumn": 36}, {"ruleId": "459", "severity": 1, "message": "460", "line": 139, "column": 17, "nodeType": "461", "endLine": 142, "endColumn": 18}, {"ruleId": "459", "severity": 1, "message": "460", "line": 161, "column": 15, "nodeType": "461", "endLine": 164, "endColumn": 16}, {"ruleId": "459", "severity": 1, "message": "460", "line": 167, "column": 15, "nodeType": "461", "endLine": 170, "endColumn": 16}, {"ruleId": "459", "severity": 1, "message": "460", "line": 173, "column": 15, "nodeType": "461", "endLine": 176, "endColumn": 16}, {"ruleId": "424", "severity": 1, "message": "462", "line": 50, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 50, "endColumn": 25}, {"ruleId": "424", "severity": 1, "message": "463", "line": 51, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 51, "endColumn": 19}, {"ruleId": "428", "severity": 1, "message": "464", "line": 74, "column": 6, "nodeType": "430", "endLine": 74, "endColumn": 38, "suggestions": "465"}, {"ruleId": "424", "severity": 1, "message": "466", "line": 113, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 113, "endColumn": 27}, {"ruleId": null, "fatal": true, "severity": 2, "message": "467", "line": 148, "column": 8, "nodeType": null}, {"ruleId": "428", "severity": 1, "message": "439", "line": 61, "column": 6, "nodeType": "430", "endLine": 61, "endColumn": 16, "suggestions": "468"}, {"ruleId": "424", "severity": 1, "message": "469", "line": 2, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 2, "endColumn": 16}, {"ruleId": "428", "severity": 1, "message": "452", "line": 114, "column": 6, "nodeType": "430", "endLine": 114, "endColumn": 21, "suggestions": "470"}, {"ruleId": "424", "severity": 1, "message": "441", "line": 70, "column": 22, "nodeType": "426", "messageId": "427", "endLine": 70, "endColumn": 35}, {"ruleId": "428", "severity": 1, "message": "442", "line": 80, "column": 6, "nodeType": "430", "endLine": 80, "endColumn": 16, "suggestions": "471"}, {"ruleId": "428", "severity": 1, "message": "444", "line": 99, "column": 6, "nodeType": "430", "endLine": 99, "endColumn": 48, "suggestions": "472"}, {"ruleId": "424", "severity": 1, "message": "446", "line": 174, "column": 60, "nodeType": "426", "messageId": "427", "endLine": 174, "endColumn": 71}, {"ruleId": null, "fatal": true, "severity": 2, "message": "467", "line": 380, "column": 8, "nodeType": null}, {"ruleId": "428", "severity": 1, "message": "434", "line": 60, "column": 6, "nodeType": "430", "endLine": 60, "endColumn": 12, "suggestions": "473", "suppressions": "474"}, {"ruleId": "428", "severity": 1, "message": "437", "line": 82, "column": 6, "nodeType": "430", "endLine": 82, "endColumn": 22, "suggestions": "475"}, {"ruleId": "428", "severity": 1, "message": "450", "line": 62, "column": 6, "nodeType": "430", "endLine": 62, "endColumn": 16, "suggestions": "476"}, {"ruleId": "424", "severity": 1, "message": "458", "line": 1, "column": 27, "nodeType": "426", "messageId": "427", "endLine": 1, "endColumn": 36}, {"ruleId": "428", "severity": 1, "message": "456", "line": 45, "column": 6, "nodeType": "430", "endLine": 45, "endColumn": 12, "suggestions": "477"}, {"ruleId": "428", "severity": 1, "message": "432", "line": 52, "column": 6, "nodeType": "430", "endLine": 52, "endColumn": 8, "suggestions": "478"}, {"ruleId": "459", "severity": 1, "message": "460", "line": 139, "column": 17, "nodeType": "461", "endLine": 142, "endColumn": 18}, {"ruleId": "459", "severity": 1, "message": "460", "line": 161, "column": 15, "nodeType": "461", "endLine": 164, "endColumn": 16}, {"ruleId": "459", "severity": 1, "message": "460", "line": 167, "column": 15, "nodeType": "461", "endLine": 170, "endColumn": 16}, {"ruleId": "459", "severity": 1, "message": "460", "line": 173, "column": 15, "nodeType": "461", "endLine": 176, "endColumn": 16}, {"ruleId": "424", "severity": 1, "message": "462", "line": 50, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 50, "endColumn": 25}, {"ruleId": "424", "severity": 1, "message": "463", "line": 51, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 51, "endColumn": 19}, {"ruleId": "428", "severity": 1, "message": "464", "line": 74, "column": 6, "nodeType": "430", "endLine": 74, "endColumn": 38, "suggestions": "479"}, {"ruleId": "424", "severity": 1, "message": "466", "line": 113, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 113, "endColumn": 27}, {"ruleId": "424", "severity": 1, "message": "425", "line": 70, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 70, "endColumn": 20}, {"ruleId": "428", "severity": 1, "message": "429", "line": 79, "column": 6, "nodeType": "430", "endLine": 79, "endColumn": 12, "suggestions": "480"}, "@typescript-eslint/no-unused-vars", "'showSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudentData'. Either include it or remove the dependency array.", "ArrayExpression", ["481"], "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["482"], "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["483"], ["484"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["485"], "React Hook useEffect has a missing dependency: 'fetchCourse'. Either include it or remove the dependency array.", ["486"], "'setUserAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercise'. Either include it or remove the dependency array.", ["487"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["488"], "'explanation' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercises'. Either include it or remove the dependency array.", ["489"], "React Hook useEffect has a missing dependency: 'fetchLesson'. Either include it or remove the dependency array.", ["490"], "React Hook useEffect has a missing dependency: 'renderFunction2D'. Either include it or remove the dependency array.", ["491"], "React Hook useEffect has a missing dependency: 'fetchProgressData'. Either include it or remove the dependency array.", ["492"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["493"], "'useEffect' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'selectedMessage' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["494"], "'handleStatusUpdate' is assigned a value but never used.", "Parsing error: Expected corresponding JSX closing tag for 'StudentLayout'.", ["495"], "'motion' is defined but never used.", ["496"], ["497"], ["498"], ["499"], ["500"], ["501"], ["502"], ["503"], ["504"], ["505"], ["506"], {"desc": "507", "fix": "508"}, {"desc": "509", "fix": "510"}, {"desc": "511", "fix": "512"}, {"kind": "513", "justification": "514"}, {"desc": "515", "fix": "516"}, {"desc": "517", "fix": "518"}, {"desc": "519", "fix": "520"}, {"desc": "521", "fix": "522"}, {"desc": "523", "fix": "524"}, {"desc": "525", "fix": "526"}, {"desc": "527", "fix": "528"}, {"desc": "529", "fix": "530"}, {"desc": "531", "fix": "532"}, {"desc": "533", "fix": "534"}, {"desc": "517", "fix": "535"}, {"desc": "527", "fix": "536"}, {"desc": "519", "fix": "537"}, {"desc": "521", "fix": "538"}, {"desc": "511", "fix": "539"}, {"kind": "513", "justification": "514"}, {"desc": "515", "fix": "540"}, {"desc": "525", "fix": "541"}, {"desc": "531", "fix": "542"}, {"desc": "509", "fix": "543"}, {"desc": "533", "fix": "544"}, {"desc": "507", "fix": "545"}, "Update the dependencies array to be: [fetchStudentData, user]", {"range": "546", "text": "547"}, "Update the dependencies array to be: [removeNotification]", {"range": "548", "text": "549"}, "Update the dependencies array to be: [fetchAdminData, user]", {"range": "550", "text": "551"}, "directive", "", "Update the dependencies array to be: [user, navigate, loadUsers]", {"range": "552", "text": "553"}, "Update the dependencies array to be: [user, id, fetchCourse]", {"range": "554", "text": "555"}, "Update the dependencies array to be: [user, id, fetchExercise]", {"range": "556", "text": "557"}, "Update the dependencies array to be: [timeLeft, exercise?.user_attempt?.status, handleSubmit]", {"range": "558", "text": "559"}, "Update the dependencies array to be: [user, filters, fetchExercises]", {"range": "560", "text": "561"}, "Update the dependencies array to be: [user, id, fetchLesson]", {"range": "562", "text": "563"}, "Update the dependencies array to be: [functionInput, renderFunction2D]", {"range": "564", "text": "565"}, "Update the dependencies array to be: [fetchProgressData, user]", {"range": "566", "text": "567"}, "Update the dependencies array to be: [fetchProfile, user]", {"range": "568", "text": "569"}, "Update the dependencies array to be: [user, filters, pagination.page, fetchMessages]", {"range": "570", "text": "571"}, {"range": "572", "text": "555"}, {"range": "573", "text": "565"}, {"range": "574", "text": "557"}, {"range": "575", "text": "559"}, {"range": "576", "text": "551"}, {"range": "577", "text": "553"}, {"range": "578", "text": "563"}, {"range": "579", "text": "569"}, {"range": "580", "text": "549"}, {"range": "581", "text": "571"}, {"range": "582", "text": "547"}, [2030, 2036], "[fetchStudentData, user]", [1627, 1629], "[removeNotification]", [1564, 1570], "[fetchAdmin<PERSON><PERSON>, user]", [2196, 2212], "[user, navigate, loadUsers]", [1368, 1378], "[user, id, fetchCourse]", [2224, 2234], "[user, id, fetchExercise]", [2759, 2801], "[timeLeft, exercise?.user_attempt?.status, handleSubmit]", [1688, 1703], "[user, filters, fetchExercises]", [1549, 1559], "[user, id, fetchLesson]", [3341, 3356], "[functionInput, renderFunction2D]", [1044, 1050], "[fetchProgress<PERSON>ata, user]", [1117, 1123], "[fetchProfile, user]", [1913, 1945], "[user, filters, pagination.page, fetchMessages]", [1425, 1435], [3398, 3413], [2281, 2291], [2816, 2858], [1564, 1570], [2196, 2212], [1606, 1616], [1117, 1123], [1627, 1629], [1913, 1945], [2162, 2168]]