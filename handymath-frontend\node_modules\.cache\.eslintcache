[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\index.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\App.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "68"}, {"size": 277, "mtime": 1749080956168, "results": "69", "hashOfConfig": "70"}, {"size": 6024, "mtime": 1749087688409, "results": "71", "hashOfConfig": "70"}, {"size": 802, "mtime": 1749046473890, "results": "72", "hashOfConfig": "70"}, {"size": 16715, "mtime": 1749066387299, "results": "73", "hashOfConfig": "70"}, {"size": 31759, "mtime": 1748983338825, "results": "74", "hashOfConfig": "70"}, {"size": 218, "mtime": 1748952089453, "results": "75", "hashOfConfig": "70"}, {"size": 1931, "mtime": 1748954075713, "results": "76", "hashOfConfig": "70"}, {"size": 4380, "mtime": 1749046473889, "results": "77", "hashOfConfig": "70"}, {"size": 4293, "mtime": 1749292696790, "results": "78", "hashOfConfig": "70"}, {"size": 7365, "mtime": 1749046902629, "results": "79", "hashOfConfig": "70"}, {"size": 14780, "mtime": 1749087758943, "results": "80", "hashOfConfig": "70"}, {"size": 15014, "mtime": 1749047338072, "results": "81", "hashOfConfig": "70"}, {"size": 21463, "mtime": 1749071198524, "results": "82", "hashOfConfig": "70"}, {"size": 15273, "mtime": 1749047898131, "results": "83", "hashOfConfig": "70"}, {"size": 15195, "mtime": 1749048128423, "results": "84", "hashOfConfig": "70"}, {"size": 14239, "mtime": 1749048393478, "results": "85", "hashOfConfig": "70"}, {"size": 19109, "mtime": 1749051118945, "results": "86", "hashOfConfig": "70"}, {"size": 12980, "mtime": 1749051136411, "results": "87", "hashOfConfig": "70"}, {"size": 10229, "mtime": 1749070380168, "results": "88", "hashOfConfig": "70"}, {"size": 12569, "mtime": 1749051153981, "results": "89", "hashOfConfig": "70"}, {"size": 8794, "mtime": 1749070718719, "results": "90", "hashOfConfig": "70"}, {"size": 18459, "mtime": 1749070497141, "results": "91", "hashOfConfig": "70"}, {"size": 7692, "mtime": 1749051903569, "results": "92", "hashOfConfig": "70"}, {"size": 5122, "mtime": 1749052811678, "results": "93", "hashOfConfig": "70"}, {"size": 20371, "mtime": 1749070759007, "results": "94", "hashOfConfig": "70"}, {"size": 2721, "mtime": 1749293050442, "results": "95", "hashOfConfig": "70"}, {"size": 6715, "mtime": 1749068251807, "results": "96", "hashOfConfig": "70"}, {"size": 8736, "mtime": 1749073183622, "results": "97", "hashOfConfig": "70"}, {"size": 7800, "mtime": 1749076875344, "results": "98", "hashOfConfig": "70"}, {"size": 11434, "mtime": 1749076479018, "results": "99", "hashOfConfig": "70"}, {"size": 12684, "mtime": 1749087573195, "results": "100", "hashOfConfig": "70"}, {"size": 13617, "mtime": 1749073014242, "results": "101", "hashOfConfig": "70"}, {"size": 6024, "mtime": 1749072916570, "results": "102", "hashOfConfig": "70"}, {"size": 6853, "mtime": 1749076802984, "results": "103", "hashOfConfig": "70"}, {"size": 19294, "mtime": 1749087644351, "results": "104", "hashOfConfig": "70"}, {"size": 277, "mtime": 1749080956000, "results": "105", "hashOfConfig": "106"}, {"size": 6024, "mtime": 1749087688000, "results": "107", "hashOfConfig": "106"}, {"size": 4293, "mtime": 1749292696000, "results": "108", "hashOfConfig": "106"}, {"size": 5122, "mtime": 1749052810000, "results": "109", "hashOfConfig": "106"}, {"size": 14239, "mtime": 1749048392000, "results": "110", "hashOfConfig": "106"}, {"size": 10229, "mtime": 1749070380000, "results": "111", "hashOfConfig": "106"}, {"size": 2721, "mtime": 1749293050000, "results": "112", "hashOfConfig": "106"}, {"size": 18459, "mtime": 1749070496000, "results": "113", "hashOfConfig": "106"}, {"size": 15195, "mtime": 1749048128000, "results": "114", "hashOfConfig": "106"}, {"size": 7692, "mtime": 1749051902000, "results": "115", "hashOfConfig": "106"}, {"size": 19109, "mtime": 1749051118000, "results": "116", "hashOfConfig": "106"}, {"size": 12980, "mtime": 1749051136000, "results": "117", "hashOfConfig": "106"}, {"size": 14780, "mtime": 1749087758000, "results": "118", "hashOfConfig": "106"}, {"size": 8794, "mtime": 1749070718000, "results": "119", "hashOfConfig": "106"}, {"size": 21463, "mtime": 1749071198000, "results": "120", "hashOfConfig": "106"}, {"size": 12569, "mtime": 1749051152000, "results": "121", "hashOfConfig": "106"}, {"size": 15014, "mtime": 1749047338000, "results": "122", "hashOfConfig": "106"}, {"size": 20371, "mtime": 1749070758000, "results": "123", "hashOfConfig": "106"}, {"size": 15273, "mtime": 1749047898000, "results": "124", "hashOfConfig": "106"}, {"size": 13617, "mtime": 1749073014000, "results": "125", "hashOfConfig": "106"}, {"size": 7365, "mtime": 1749046902000, "results": "126", "hashOfConfig": "106"}, {"size": 7800, "mtime": 1749076874000, "results": "127", "hashOfConfig": "106"}, {"size": 12684, "mtime": 1749087572000, "results": "128", "hashOfConfig": "106"}, {"size": 11434, "mtime": 1749076478000, "results": "129", "hashOfConfig": "106"}, {"size": 4380, "mtime": 1749046472000, "results": "130", "hashOfConfig": "106"}, {"size": 6853, "mtime": 1749076802000, "results": "131", "hashOfConfig": "106"}, {"size": 19294, "mtime": 1749087644000, "results": "132", "hashOfConfig": "106"}, {"size": 802, "mtime": 1749046472000, "results": "133", "hashOfConfig": "106"}, {"size": 6024, "mtime": 1749072916000, "results": "134", "hashOfConfig": "106"}, {"size": 16715, "mtime": 1749066386000, "results": "135", "hashOfConfig": "106"}, {"size": 6715, "mtime": 1749068250000, "results": "136", "hashOfConfig": "106"}, {"size": 8736, "mtime": 1749073182000, "results": "137", "hashOfConfig": "106"}, {"size": 1931, "mtime": 1748954074000, "results": "138", "hashOfConfig": "106"}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1spffp8", {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o7876w", {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["343", "344"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["345"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["346"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["347"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["348"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["349", "350", "351", "352"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["353", "354", "355"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["356"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["357"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["358"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["359"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["360"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["361", "362", "363", "364"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["365", "366", "367", "368"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["369"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["370"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["371"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["372", "373", "374", "375"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["376", "377", "378"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["379"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["380"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["381"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["382"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["383"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["384"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["385", "386", "387", "388"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["389", "390", "391", "392"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["393", "394"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath codeF\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], {"ruleId": "395", "severity": 1, "message": "396", "line": 68, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 68, "endColumn": 20}, {"ruleId": "399", "severity": 1, "message": "400", "line": 77, "column": 6, "nodeType": "401", "endLine": 77, "endColumn": 12, "suggestions": "402"}, {"ruleId": "399", "severity": 1, "message": "403", "line": 52, "column": 6, "nodeType": "401", "endLine": 52, "endColumn": 8, "suggestions": "404"}, {"ruleId": "399", "severity": 1, "message": "405", "line": 60, "column": 6, "nodeType": "401", "endLine": 60, "endColumn": 12, "suggestions": "406", "suppressions": "407"}, {"ruleId": "399", "severity": 1, "message": "408", "line": 82, "column": 6, "nodeType": "401", "endLine": 82, "endColumn": 22, "suggestions": "409"}, {"ruleId": "399", "severity": 1, "message": "410", "line": 60, "column": 6, "nodeType": "401", "endLine": 60, "endColumn": 16, "suggestions": "411"}, {"ruleId": "395", "severity": 1, "message": "412", "line": 69, "column": 22, "nodeType": "397", "messageId": "398", "endLine": 69, "endColumn": 35}, {"ruleId": "399", "severity": 1, "message": "413", "line": 79, "column": 6, "nodeType": "401", "endLine": 79, "endColumn": 16, "suggestions": "414"}, {"ruleId": "399", "severity": 1, "message": "415", "line": 98, "column": 6, "nodeType": "401", "endLine": 98, "endColumn": 48, "suggestions": "416"}, {"ruleId": "395", "severity": 1, "message": "417", "line": 173, "column": 60, "nodeType": "397", "messageId": "398", "endLine": 173, "endColumn": 71}, {"ruleId": "395", "severity": 1, "message": "396", "line": 43, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 43, "endColumn": 20}, {"ruleId": "395", "severity": 1, "message": "418", "line": 49, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 49, "endColumn": 20}, {"ruleId": "399", "severity": 1, "message": "419", "line": 68, "column": 6, "nodeType": "401", "endLine": 68, "endColumn": 21, "suggestions": "420"}, {"ruleId": "399", "severity": 1, "message": "421", "line": 61, "column": 6, "nodeType": "401", "endLine": 61, "endColumn": 16, "suggestions": "422"}, {"ruleId": "399", "severity": 1, "message": "423", "line": 113, "column": 6, "nodeType": "401", "endLine": 113, "endColumn": 21, "suggestions": "424"}, {"ruleId": "399", "severity": 1, "message": "425", "line": 39, "column": 6, "nodeType": "401", "endLine": 39, "endColumn": 12, "suggestions": "426"}, {"ruleId": "399", "severity": 1, "message": "427", "line": 45, "column": 6, "nodeType": "401", "endLine": 45, "endColumn": 12, "suggestions": "428"}, {"ruleId": "395", "severity": 1, "message": "429", "line": 1, "column": 27, "nodeType": "397", "messageId": "398", "endLine": 1, "endColumn": 36}, {"ruleId": "430", "severity": 1, "message": "431", "line": 139, "column": 17, "nodeType": "432", "endLine": 142, "endColumn": 18}, {"ruleId": "430", "severity": 1, "message": "431", "line": 161, "column": 15, "nodeType": "432", "endLine": 164, "endColumn": 16}, {"ruleId": "430", "severity": 1, "message": "431", "line": 167, "column": 15, "nodeType": "432", "endLine": 170, "endColumn": 16}, {"ruleId": "430", "severity": 1, "message": "431", "line": 173, "column": 15, "nodeType": "432", "endLine": 176, "endColumn": 16}, {"ruleId": "395", "severity": 1, "message": "433", "line": 50, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 50, "endColumn": 25}, {"ruleId": "395", "severity": 1, "message": "434", "line": 51, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 51, "endColumn": 19}, {"ruleId": "399", "severity": 1, "message": "435", "line": 74, "column": 6, "nodeType": "401", "endLine": 74, "endColumn": 38, "suggestions": "436"}, {"ruleId": "395", "severity": 1, "message": "437", "line": 113, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 113, "endColumn": 27}, {"ruleId": "399", "severity": 1, "message": "425", "line": 39, "column": 6, "nodeType": "401", "endLine": 39, "endColumn": 12, "suggestions": "438"}, {"ruleId": "399", "severity": 1, "message": "410", "line": 60, "column": 6, "nodeType": "401", "endLine": 60, "endColumn": 16, "suggestions": "439"}, {"ruleId": "399", "severity": 1, "message": "423", "line": 113, "column": 6, "nodeType": "401", "endLine": 113, "endColumn": 21, "suggestions": "440"}, {"ruleId": "395", "severity": 1, "message": "412", "line": 69, "column": 22, "nodeType": "397", "messageId": "398", "endLine": 69, "endColumn": 35}, {"ruleId": "399", "severity": 1, "message": "413", "line": 79, "column": 6, "nodeType": "401", "endLine": 79, "endColumn": 16, "suggestions": "441"}, {"ruleId": "399", "severity": 1, "message": "415", "line": 98, "column": 6, "nodeType": "401", "endLine": 98, "endColumn": 48, "suggestions": "442"}, {"ruleId": "395", "severity": 1, "message": "417", "line": 173, "column": 60, "nodeType": "397", "messageId": "398", "endLine": 173, "endColumn": 71}, {"ruleId": "395", "severity": 1, "message": "396", "line": 43, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 43, "endColumn": 20}, {"ruleId": "395", "severity": 1, "message": "418", "line": 49, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 49, "endColumn": 20}, {"ruleId": "399", "severity": 1, "message": "419", "line": 68, "column": 6, "nodeType": "401", "endLine": 68, "endColumn": 21, "suggestions": "443"}, {"ruleId": "399", "severity": 1, "message": "405", "line": 60, "column": 6, "nodeType": "401", "endLine": 60, "endColumn": 12, "suggestions": "444", "suppressions": "445"}, {"ruleId": "399", "severity": 1, "message": "408", "line": 82, "column": 6, "nodeType": "401", "endLine": 82, "endColumn": 22, "suggestions": "446"}, {"ruleId": "399", "severity": 1, "message": "421", "line": 61, "column": 6, "nodeType": "401", "endLine": 61, "endColumn": 16, "suggestions": "447"}, {"ruleId": "395", "severity": 1, "message": "429", "line": 1, "column": 27, "nodeType": "397", "messageId": "398", "endLine": 1, "endColumn": 36}, {"ruleId": "399", "severity": 1, "message": "427", "line": 45, "column": 6, "nodeType": "401", "endLine": 45, "endColumn": 12, "suggestions": "448"}, {"ruleId": "399", "severity": 1, "message": "403", "line": 52, "column": 6, "nodeType": "401", "endLine": 52, "endColumn": 8, "suggestions": "449"}, {"ruleId": "430", "severity": 1, "message": "431", "line": 139, "column": 17, "nodeType": "432", "endLine": 142, "endColumn": 18}, {"ruleId": "430", "severity": 1, "message": "431", "line": 161, "column": 15, "nodeType": "432", "endLine": 164, "endColumn": 16}, {"ruleId": "430", "severity": 1, "message": "431", "line": 167, "column": 15, "nodeType": "432", "endLine": 170, "endColumn": 16}, {"ruleId": "430", "severity": 1, "message": "431", "line": 173, "column": 15, "nodeType": "432", "endLine": 176, "endColumn": 16}, {"ruleId": "395", "severity": 1, "message": "433", "line": 50, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 50, "endColumn": 25}, {"ruleId": "395", "severity": 1, "message": "434", "line": 51, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 51, "endColumn": 19}, {"ruleId": "399", "severity": 1, "message": "435", "line": 74, "column": 6, "nodeType": "401", "endLine": 74, "endColumn": 38, "suggestions": "450"}, {"ruleId": "395", "severity": 1, "message": "437", "line": 113, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 113, "endColumn": 27}, {"ruleId": "395", "severity": 1, "message": "396", "line": 68, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 68, "endColumn": 20}, {"ruleId": "399", "severity": 1, "message": "400", "line": 77, "column": 6, "nodeType": "401", "endLine": 77, "endColumn": 12, "suggestions": "451"}, "@typescript-eslint/no-unused-vars", "'showSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudentData'. Either include it or remove the dependency array.", "ArrayExpression", ["452"], "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["453"], "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["454"], ["455"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["456"], "React Hook useEffect has a missing dependency: 'fetchCourse'. Either include it or remove the dependency array.", ["457"], "'setUserAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercise'. Either include it or remove the dependency array.", ["458"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["459"], "'explanation' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercises'. Either include it or remove the dependency array.", ["460"], "React Hook useEffect has a missing dependency: 'fetchLesson'. Either include it or remove the dependency array.", ["461"], "React Hook useEffect has a missing dependency: 'renderFunction2D'. Either include it or remove the dependency array.", ["462"], "React Hook useEffect has a missing dependency: 'fetchProgressData'. Either include it or remove the dependency array.", ["463"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["464"], "'useEffect' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'selectedMessage' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["465"], "'handleStatusUpdate' is assigned a value but never used.", ["466"], ["467"], ["468"], ["469"], ["470"], ["471"], ["472"], ["473"], ["474"], ["475"], ["476"], ["477"], ["478"], ["479"], {"desc": "480", "fix": "481"}, {"desc": "482", "fix": "483"}, {"desc": "484", "fix": "485"}, {"kind": "486", "justification": "487"}, {"desc": "488", "fix": "489"}, {"desc": "490", "fix": "491"}, {"desc": "492", "fix": "493"}, {"desc": "494", "fix": "495"}, {"desc": "496", "fix": "497"}, {"desc": "498", "fix": "499"}, {"desc": "500", "fix": "501"}, {"desc": "502", "fix": "503"}, {"desc": "504", "fix": "505"}, {"desc": "506", "fix": "507"}, {"desc": "502", "fix": "508"}, {"desc": "490", "fix": "509"}, {"desc": "500", "fix": "510"}, {"desc": "492", "fix": "511"}, {"desc": "494", "fix": "512"}, {"desc": "496", "fix": "513"}, {"desc": "484", "fix": "514"}, {"kind": "486", "justification": "487"}, {"desc": "488", "fix": "515"}, {"desc": "498", "fix": "516"}, {"desc": "504", "fix": "517"}, {"desc": "482", "fix": "518"}, {"desc": "506", "fix": "519"}, {"desc": "480", "fix": "520"}, "Update the dependencies array to be: [fetchStudentData, user]", {"range": "521", "text": "522"}, "Update the dependencies array to be: [removeNotification]", {"range": "523", "text": "524"}, "Update the dependencies array to be: [fetchAdminData, user]", {"range": "525", "text": "526"}, "directive", "", "Update the dependencies array to be: [user, navigate, loadUsers]", {"range": "527", "text": "528"}, "Update the dependencies array to be: [user, id, fetchCourse]", {"range": "529", "text": "530"}, "Update the dependencies array to be: [user, id, fetchExercise]", {"range": "531", "text": "532"}, "Update the dependencies array to be: [timeLeft, exercise?.user_attempt?.status, handleSubmit]", {"range": "533", "text": "534"}, "Update the dependencies array to be: [user, filters, fetchExercises]", {"range": "535", "text": "536"}, "Update the dependencies array to be: [user, id, fetchLesson]", {"range": "537", "text": "538"}, "Update the dependencies array to be: [functionInput, renderFunction2D]", {"range": "539", "text": "540"}, "Update the dependencies array to be: [fetchProgressData, user]", {"range": "541", "text": "542"}, "Update the dependencies array to be: [fetchProfile, user]", {"range": "543", "text": "544"}, "Update the dependencies array to be: [user, filters, pagination.page, fetchMessages]", {"range": "545", "text": "546"}, {"range": "547", "text": "542"}, {"range": "548", "text": "530"}, {"range": "549", "text": "540"}, {"range": "550", "text": "532"}, {"range": "551", "text": "534"}, {"range": "552", "text": "536"}, {"range": "553", "text": "526"}, {"range": "554", "text": "528"}, {"range": "555", "text": "538"}, {"range": "556", "text": "544"}, {"range": "557", "text": "524"}, {"range": "558", "text": "546"}, {"range": "559", "text": "522"}, [2030, 2036], "[fetchStudentData, user]", [1627, 1629], "[removeNotification]", [1564, 1570], "[fetchAdmin<PERSON><PERSON>, user]", [2196, 2212], "[user, navigate, loadUsers]", [1368, 1378], "[user, id, fetchCourse]", [2224, 2234], "[user, id, fetchExercise]", [2759, 2801], "[timeLeft, exercise?.user_attempt?.status, handleSubmit]", [1688, 1703], "[user, filters, fetchExercises]", [1549, 1559], "[user, id, fetchLesson]", [3341, 3356], "[functionInput, renderFunction2D]", [1044, 1050], "[fetchProgress<PERSON>ata, user]", [1117, 1123], "[fetchProfile, user]", [1913, 1945], "[user, filters, pagination.page, fetchMessages]", [1044, 1050], [1368, 1378], [3341, 3356], [2224, 2234], [2759, 2801], [1688, 1703], [1564, 1570], [2196, 2212], [1549, 1559], [1117, 1123], [1627, 1629], [1913, 1945], [2030, 2036]]