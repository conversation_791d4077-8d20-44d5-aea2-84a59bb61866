{"ast": null, "code": "import { isCollection, isMatrix } from './is.js';\nimport { IndexError } from '../error/IndexError.js';\nimport { arraySize, deepMap as arrayDeepMap, deepForEach as arrayDeepForEach } from './array.js';\nimport { _switch } from './switch.js';\n\n/**\n * Test whether an array contains collections\n * @param {Array} array\n * @returns {boolean} Returns true when the array contains one or multiple\n *                    collections (Arrays or Matrices). Returns false otherwise.\n */\nexport function containsCollections(array) {\n  for (var i = 0; i < array.length; i++) {\n    if (isCollection(array[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Recursively loop over all elements in a given multi dimensional array\n * and invoke the callback on each of the elements.\n * @param {Array | Matrix} array\n * @param {Function} callback     The callback method is invoked with one\n *                                parameter: the current element in the array\n */\nexport function deepForEach(array, callback) {\n  if (isMatrix(array)) {\n    array.forEach(x => callback(x), false, true);\n  } else {\n    arrayDeepForEach(array, callback, true);\n  }\n}\n\n/**\n * Execute the callback function element wise for each element in array and any\n * nested array\n * Returns an array with the results\n * @param {Array | Matrix} array\n * @param {Function} callback   The callback is called with two parameters:\n *                              value1 and value2, which contain the current\n *                              element of both arrays.\n * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n *\n * @return {Array | Matrix} res\n */\nexport function deepMap(array, callback, skipZeros) {\n  if (!skipZeros) {\n    if (isMatrix(array)) {\n      return array.map(x => callback(x), false, true);\n    } else {\n      return arrayDeepMap(array, callback, true);\n    }\n  }\n  var skipZerosCallback = x => x === 0 ? x : callback(x);\n  if (isMatrix(array)) {\n    return array.map(x => skipZerosCallback(x), false, true);\n  } else {\n    return arrayDeepMap(array, skipZerosCallback, true);\n  }\n}\n\n/**\n * Reduce a given matrix or array to a new matrix or\n * array with one less dimension, applying the given\n * callback in the selected dimension.\n * @param {Array | Matrix} mat\n * @param {number} dim\n * @param {Function} callback\n * @return {Array | Matrix} res\n */\nexport function reduce(mat, dim, callback) {\n  var size = Array.isArray(mat) ? arraySize(mat) : mat.size();\n  if (dim < 0 || dim >= size.length) {\n    // TODO: would be more clear when throwing a DimensionError here\n    throw new IndexError(dim, size.length);\n  }\n  if (isMatrix(mat)) {\n    return mat.create(_reduce(mat.valueOf(), dim, callback), mat.datatype());\n  } else {\n    return _reduce(mat, dim, callback);\n  }\n}\n\n/**\n * Recursively reduce a matrix\n * @param {Array} mat\n * @param {number} dim\n * @param {Function} callback\n * @returns {Array} ret\n * @private\n */\nfunction _reduce(mat, dim, callback) {\n  var i, ret, val, tran;\n  if (dim <= 0) {\n    if (!Array.isArray(mat[0])) {\n      val = mat[0];\n      for (i = 1; i < mat.length; i++) {\n        val = callback(val, mat[i]);\n      }\n      return val;\n    } else {\n      tran = _switch(mat);\n      ret = [];\n      for (i = 0; i < tran.length; i++) {\n        ret[i] = _reduce(tran[i], dim - 1, callback);\n      }\n      return ret;\n    }\n  } else {\n    ret = [];\n    for (i = 0; i < mat.length; i++) {\n      ret[i] = _reduce(mat[i], dim - 1, callback);\n    }\n    return ret;\n  }\n}\n\n// TODO: document function scatter\nexport function scatter(a, j, w, x, u, mark, cindex, f, inverse, update, value) {\n  // a arrays\n  var avalues = a._values;\n  var aindex = a._index;\n  var aptr = a._ptr;\n\n  // vars\n  var k, k0, k1, i;\n\n  // check we need to process values (pattern matrix)\n  if (x) {\n    // values in j\n    for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n      // row\n      i = aindex[k];\n      // check value exists in current j\n      if (w[i] !== mark) {\n        // i is new entry in j\n        w[i] = mark;\n        // add i to pattern of C\n        cindex.push(i);\n        // x(i) = A, check we need to call function this time\n        if (update) {\n          // copy value to workspace calling callback function\n          x[i] = inverse ? f(avalues[k], value) : f(value, avalues[k]);\n          // function was called on current row\n          u[i] = mark;\n        } else {\n          // copy value to workspace\n          x[i] = avalues[k];\n        }\n      } else {\n        // i exists in C already\n        x[i] = inverse ? f(avalues[k], x[i]) : f(x[i], avalues[k]);\n        // function was called on current row\n        u[i] = mark;\n      }\n    }\n  } else {\n    // values in j\n    for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n      // row\n      i = aindex[k];\n      // check value exists in current j\n      if (w[i] !== mark) {\n        // i is new entry in j\n        w[i] = mark;\n        // add i to pattern of C\n        cindex.push(i);\n      } else {\n        // indicate function was called on current row\n        u[i] = mark;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["isCollection", "isMatrix", "IndexError", "arraySize", "deepMap", "arrayDeepMap", "deepForEach", "arrayDeepForEach", "_switch", "containsCollections", "array", "i", "length", "callback", "for<PERSON>ach", "x", "skipZ<PERSON><PERSON>", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reduce", "mat", "dim", "size", "Array", "isArray", "create", "_reduce", "valueOf", "datatype", "ret", "val", "tran", "scatter", "a", "j", "w", "u", "mark", "cindex", "f", "inverse", "update", "value", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "k", "k0", "k1", "push"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/collection.js"], "sourcesContent": ["import { isCollection, isMatrix } from './is.js';\nimport { IndexError } from '../error/IndexError.js';\nimport { arraySize, deepMap as arrayDeepMap, deepForEach as arrayDeepForEach } from './array.js';\nimport { _switch } from './switch.js';\n\n/**\n * Test whether an array contains collections\n * @param {Array} array\n * @returns {boolean} Returns true when the array contains one or multiple\n *                    collections (Arrays or Matrices). Returns false otherwise.\n */\nexport function containsCollections(array) {\n  for (var i = 0; i < array.length; i++) {\n    if (isCollection(array[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Recursively loop over all elements in a given multi dimensional array\n * and invoke the callback on each of the elements.\n * @param {Array | Matrix} array\n * @param {Function} callback     The callback method is invoked with one\n *                                parameter: the current element in the array\n */\nexport function deepForEach(array, callback) {\n  if (isMatrix(array)) {\n    array.forEach(x => callback(x), false, true);\n  } else {\n    arrayDeepForEach(array, callback, true);\n  }\n}\n\n/**\n * Execute the callback function element wise for each element in array and any\n * nested array\n * Returns an array with the results\n * @param {Array | Matrix} array\n * @param {Function} callback   The callback is called with two parameters:\n *                              value1 and value2, which contain the current\n *                              element of both arrays.\n * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n *\n * @return {Array | Matrix} res\n */\nexport function deepMap(array, callback, skipZeros) {\n  if (!skipZeros) {\n    if (isMatrix(array)) {\n      return array.map(x => callback(x), false, true);\n    } else {\n      return arrayDeepMap(array, callback, true);\n    }\n  }\n  var skipZerosCallback = x => x === 0 ? x : callback(x);\n  if (isMatrix(array)) {\n    return array.map(x => skipZerosCallback(x), false, true);\n  } else {\n    return arrayDeepMap(array, skipZerosCallback, true);\n  }\n}\n\n/**\n * Reduce a given matrix or array to a new matrix or\n * array with one less dimension, applying the given\n * callback in the selected dimension.\n * @param {Array | Matrix} mat\n * @param {number} dim\n * @param {Function} callback\n * @return {Array | Matrix} res\n */\nexport function reduce(mat, dim, callback) {\n  var size = Array.isArray(mat) ? arraySize(mat) : mat.size();\n  if (dim < 0 || dim >= size.length) {\n    // TODO: would be more clear when throwing a DimensionError here\n    throw new IndexError(dim, size.length);\n  }\n  if (isMatrix(mat)) {\n    return mat.create(_reduce(mat.valueOf(), dim, callback), mat.datatype());\n  } else {\n    return _reduce(mat, dim, callback);\n  }\n}\n\n/**\n * Recursively reduce a matrix\n * @param {Array} mat\n * @param {number} dim\n * @param {Function} callback\n * @returns {Array} ret\n * @private\n */\nfunction _reduce(mat, dim, callback) {\n  var i, ret, val, tran;\n  if (dim <= 0) {\n    if (!Array.isArray(mat[0])) {\n      val = mat[0];\n      for (i = 1; i < mat.length; i++) {\n        val = callback(val, mat[i]);\n      }\n      return val;\n    } else {\n      tran = _switch(mat);\n      ret = [];\n      for (i = 0; i < tran.length; i++) {\n        ret[i] = _reduce(tran[i], dim - 1, callback);\n      }\n      return ret;\n    }\n  } else {\n    ret = [];\n    for (i = 0; i < mat.length; i++) {\n      ret[i] = _reduce(mat[i], dim - 1, callback);\n    }\n    return ret;\n  }\n}\n\n// TODO: document function scatter\nexport function scatter(a, j, w, x, u, mark, cindex, f, inverse, update, value) {\n  // a arrays\n  var avalues = a._values;\n  var aindex = a._index;\n  var aptr = a._ptr;\n\n  // vars\n  var k, k0, k1, i;\n\n  // check we need to process values (pattern matrix)\n  if (x) {\n    // values in j\n    for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n      // row\n      i = aindex[k];\n      // check value exists in current j\n      if (w[i] !== mark) {\n        // i is new entry in j\n        w[i] = mark;\n        // add i to pattern of C\n        cindex.push(i);\n        // x(i) = A, check we need to call function this time\n        if (update) {\n          // copy value to workspace calling callback function\n          x[i] = inverse ? f(avalues[k], value) : f(value, avalues[k]);\n          // function was called on current row\n          u[i] = mark;\n        } else {\n          // copy value to workspace\n          x[i] = avalues[k];\n        }\n      } else {\n        // i exists in C already\n        x[i] = inverse ? f(avalues[k], x[i]) : f(x[i], avalues[k]);\n        // function was called on current row\n        u[i] = mark;\n      }\n    }\n  } else {\n    // values in j\n    for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n      // row\n      i = aindex[k];\n      // check value exists in current j\n      if (w[i] !== mark) {\n        // i is new entry in j\n        w[i] = mark;\n        // add i to pattern of C\n        cindex.push(i);\n      } else {\n        // indicate function was called on current row\n        u[i] = mark;\n      }\n    }\n  }\n}"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,SAAS;AAChD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,SAAS,EAAEC,OAAO,IAAIC,YAAY,EAAEC,WAAW,IAAIC,gBAAgB,QAAQ,YAAY;AAChG,SAASC,OAAO,QAAQ,aAAa;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIX,YAAY,CAACU,KAAK,CAACC,CAAC,CAAC,CAAC,EAAE;MAC1B,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASL,WAAWA,CAACI,KAAK,EAAEG,QAAQ,EAAE;EAC3C,IAAIZ,QAAQ,CAACS,KAAK,CAAC,EAAE;IACnBA,KAAK,CAACI,OAAO,CAACC,CAAC,IAAIF,QAAQ,CAACE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC9C,CAAC,MAAM;IACLR,gBAAgB,CAACG,KAAK,EAAEG,QAAQ,EAAE,IAAI,CAAC;EACzC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAST,OAAOA,CAACM,KAAK,EAAEG,QAAQ,EAAEG,SAAS,EAAE;EAClD,IAAI,CAACA,SAAS,EAAE;IACd,IAAIf,QAAQ,CAACS,KAAK,CAAC,EAAE;MACnB,OAAOA,KAAK,CAACO,GAAG,CAACF,CAAC,IAAIF,QAAQ,CAACE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACjD,CAAC,MAAM;MACL,OAAOV,YAAY,CAACK,KAAK,EAAEG,QAAQ,EAAE,IAAI,CAAC;IAC5C;EACF;EACA,IAAIK,iBAAiB,GAAGH,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGF,QAAQ,CAACE,CAAC,CAAC;EACtD,IAAId,QAAQ,CAACS,KAAK,CAAC,EAAE;IACnB,OAAOA,KAAK,CAACO,GAAG,CAACF,CAAC,IAAIG,iBAAiB,CAACH,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC1D,CAAC,MAAM;IACL,OAAOV,YAAY,CAACK,KAAK,EAAEQ,iBAAiB,EAAE,IAAI,CAAC;EACrD;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAER,QAAQ,EAAE;EACzC,IAAIS,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,GAAGjB,SAAS,CAACiB,GAAG,CAAC,GAAGA,GAAG,CAACE,IAAI,CAAC,CAAC;EAC3D,IAAID,GAAG,GAAG,CAAC,IAAIA,GAAG,IAAIC,IAAI,CAACV,MAAM,EAAE;IACjC;IACA,MAAM,IAAIV,UAAU,CAACmB,GAAG,EAAEC,IAAI,CAACV,MAAM,CAAC;EACxC;EACA,IAAIX,QAAQ,CAACmB,GAAG,CAAC,EAAE;IACjB,OAAOA,GAAG,CAACK,MAAM,CAACC,OAAO,CAACN,GAAG,CAACO,OAAO,CAAC,CAAC,EAAEN,GAAG,EAAER,QAAQ,CAAC,EAAEO,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;EAC1E,CAAC,MAAM;IACL,OAAOF,OAAO,CAACN,GAAG,EAAEC,GAAG,EAAER,QAAQ,CAAC;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,OAAOA,CAACN,GAAG,EAAEC,GAAG,EAAER,QAAQ,EAAE;EACnC,IAAIF,CAAC,EAAEkB,GAAG,EAAEC,GAAG,EAAEC,IAAI;EACrB,IAAIV,GAAG,IAAI,CAAC,EAAE;IACZ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1BU,GAAG,GAAGV,GAAG,CAAC,CAAC,CAAC;MACZ,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,GAAG,CAACR,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/BmB,GAAG,GAAGjB,QAAQ,CAACiB,GAAG,EAAEV,GAAG,CAACT,CAAC,CAAC,CAAC;MAC7B;MACA,OAAOmB,GAAG;IACZ,CAAC,MAAM;MACLC,IAAI,GAAGvB,OAAO,CAACY,GAAG,CAAC;MACnBS,GAAG,GAAG,EAAE;MACR,KAAKlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,IAAI,CAACnB,MAAM,EAAED,CAAC,EAAE,EAAE;QAChCkB,GAAG,CAAClB,CAAC,CAAC,GAAGe,OAAO,CAACK,IAAI,CAACpB,CAAC,CAAC,EAAEU,GAAG,GAAG,CAAC,EAAER,QAAQ,CAAC;MAC9C;MACA,OAAOgB,GAAG;IACZ;EACF,CAAC,MAAM;IACLA,GAAG,GAAG,EAAE;IACR,KAAKlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,GAAG,CAACR,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/BkB,GAAG,CAAClB,CAAC,CAAC,GAAGe,OAAO,CAACN,GAAG,CAACT,CAAC,CAAC,EAAEU,GAAG,GAAG,CAAC,EAAER,QAAQ,CAAC;IAC7C;IACA,OAAOgB,GAAG;EACZ;AACF;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEpB,CAAC,EAAEqB,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEC,CAAC,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC9E;EACA,IAAIC,OAAO,GAAGV,CAAC,CAACW,OAAO;EACvB,IAAIC,MAAM,GAAGZ,CAAC,CAACa,MAAM;EACrB,IAAIC,IAAI,GAAGd,CAAC,CAACe,IAAI;;EAEjB;EACA,IAAIC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAExC,CAAC;;EAEhB;EACA,IAAII,CAAC,EAAE;IACL;IACA,KAAKmC,EAAE,GAAGH,IAAI,CAACb,CAAC,CAAC,EAAEiB,EAAE,GAAGJ,IAAI,CAACb,CAAC,GAAG,CAAC,CAAC,EAAEe,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;MACxD;MACAtC,CAAC,GAAGkC,MAAM,CAACI,CAAC,CAAC;MACb;MACA,IAAId,CAAC,CAACxB,CAAC,CAAC,KAAK0B,IAAI,EAAE;QACjB;QACAF,CAAC,CAACxB,CAAC,CAAC,GAAG0B,IAAI;QACX;QACAC,MAAM,CAACc,IAAI,CAACzC,CAAC,CAAC;QACd;QACA,IAAI8B,MAAM,EAAE;UACV;UACA1B,CAAC,CAACJ,CAAC,CAAC,GAAG6B,OAAO,GAAGD,CAAC,CAACI,OAAO,CAACM,CAAC,CAAC,EAAEP,KAAK,CAAC,GAAGH,CAAC,CAACG,KAAK,EAAEC,OAAO,CAACM,CAAC,CAAC,CAAC;UAC5D;UACAb,CAAC,CAACzB,CAAC,CAAC,GAAG0B,IAAI;QACb,CAAC,MAAM;UACL;UACAtB,CAAC,CAACJ,CAAC,CAAC,GAAGgC,OAAO,CAACM,CAAC,CAAC;QACnB;MACF,CAAC,MAAM;QACL;QACAlC,CAAC,CAACJ,CAAC,CAAC,GAAG6B,OAAO,GAAGD,CAAC,CAACI,OAAO,CAACM,CAAC,CAAC,EAAElC,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAG4B,CAAC,CAACxB,CAAC,CAACJ,CAAC,CAAC,EAAEgC,OAAO,CAACM,CAAC,CAAC,CAAC;QAC1D;QACAb,CAAC,CAACzB,CAAC,CAAC,GAAG0B,IAAI;MACb;IACF;EACF,CAAC,MAAM;IACL;IACA,KAAKa,EAAE,GAAGH,IAAI,CAACb,CAAC,CAAC,EAAEiB,EAAE,GAAGJ,IAAI,CAACb,CAAC,GAAG,CAAC,CAAC,EAAEe,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;MACxD;MACAtC,CAAC,GAAGkC,MAAM,CAACI,CAAC,CAAC;MACb;MACA,IAAId,CAAC,CAACxB,CAAC,CAAC,KAAK0B,IAAI,EAAE;QACjB;QACAF,CAAC,CAACxB,CAAC,CAAC,GAAG0B,IAAI;QACX;QACAC,MAAM,CAACc,IAAI,CAACzC,CAAC,CAAC;MAChB,CAAC,MAAM;QACL;QACAyB,CAAC,CAACzB,CAAC,CAAC,GAAG0B,IAAI;MACb;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}