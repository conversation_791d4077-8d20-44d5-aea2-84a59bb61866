{"ast": null, "code": "export var bitOrDocs = {\n  name: 'bitOr',\n  category: 'Bitwise',\n  syntax: ['x | y', 'bitOr(x, y)'],\n  description: 'Bitwise OR operation. Performs the logical inclusive OR operation on each pair of corresponding bits of the two given values. The result in each position is 1 if the first bit is 1 or the second bit is 1 or both bits are 1, otherwise, the result is 0.',\n  examples: ['5 | 3', 'bitOr([1, 2, 3], 4)'],\n  seealso: ['bitAnd', 'bitNot', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};", "map": {"version": 3, "names": ["bitOrDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/bitwise/bitOr.js"], "sourcesContent": ["export var bitOrDocs = {\n  name: 'bitOr',\n  category: 'Bitwise',\n  syntax: ['x | y', 'bitOr(x, y)'],\n  description: 'Bitwise OR operation. Performs the logical inclusive OR operation on each pair of corresponding bits of the two given values. The result in each position is 1 if the first bit is 1 or the second bit is 1 or both bits are 1, otherwise, the result is 0.',\n  examples: ['5 | 3', 'bitOr([1, 2, 3], 4)'],\n  seealso: ['bitAnd', 'bitNot', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;EAChCC,WAAW,EAAE,6PAA6P;EAC1QC,QAAQ,EAAE,CAAC,OAAO,EAAE,qBAAqB,CAAC;EAC1CC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}