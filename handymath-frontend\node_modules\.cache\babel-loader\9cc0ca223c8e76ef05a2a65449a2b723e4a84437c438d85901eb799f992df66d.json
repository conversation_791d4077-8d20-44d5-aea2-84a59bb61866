{"ast": null, "code": "export var evaluateDocs = {\n  name: 'evaluate',\n  category: 'Expression',\n  syntax: ['evaluate(expression)', 'evaluate(expression, scope)', 'evaluate([expr1, expr2, expr3, ...])', 'evaluate([expr1, expr2, expr3, ...], scope)'],\n  description: 'Evaluate an expression or an array with expressions.',\n  examples: ['evaluate(\"2 + 3\")', 'evaluate(\"sqrt(16)\")', 'evaluate(\"2 inch to cm\")', 'evaluate(\"sin(x * pi)\", { \"x\": 1/2 })', 'evaluate([\"width=2\", \"height=4\",\"width*height\"])'],\n  seealso: ['parser', 'parse', 'compile']\n};", "map": {"version": 3, "names": ["evaluateDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/expression/evaluate.js"], "sourcesContent": ["export var evaluateDocs = {\n  name: 'evaluate',\n  category: 'Expression',\n  syntax: ['evaluate(expression)', 'evaluate(expression, scope)', 'evaluate([expr1, expr2, expr3, ...])', 'evaluate([expr1, expr2, expr3, ...], scope)'],\n  description: 'Evaluate an expression or an array with expressions.',\n  examples: ['evaluate(\"2 + 3\")', 'evaluate(\"sqrt(16)\")', 'evaluate(\"2 inch to cm\")', 'evaluate(\"sin(x * pi)\", { \"x\": 1/2 })', 'evaluate([\"width=2\", \"height=4\",\"width*height\"])'],\n  seealso: ['parser', 'parse', 'compile']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,sBAAsB,EAAE,6BAA6B,EAAE,sCAAsC,EAAE,6CAA6C,CAAC;EACtJC,WAAW,EAAE,sDAAsD;EACnEC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,uCAAuC,EAAE,kDAAkD,CAAC;EAChLC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}