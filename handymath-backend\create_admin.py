#!/usr/bin/env python
"""
Script pour créer un utilisateur admin
"""
import os
import sys
import django

# Configurer Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.contrib.auth import get_user_model

def create_admin():
    """Créer un utilisateur admin"""
    User = get_user_model()
    
    try:
        # Vérifier si l'admin existe déjà
        admin_user = User.objects.get(username='admin')
        print("👤 Utilisateur admin existe déjà")
        
        # Mettre à jour les permissions
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.role = 'admin'
        admin_user.save()
        print("✅ Permissions admin mises à jour")
        
    except User.DoesNotExist:
        # Créer un nouvel admin
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True,
            role='admin'
        )
        print("✅ Utilisateur admin créé")
    
    print(f"👤 Username: admin")
    print(f"🔑 Password: admin123")
    print(f"📧 Email: {admin_user.email}")
    print(f"👑 Role: {admin_user.role}")
    print(f"🛡️ Is Staff: {admin_user.is_staff}")
    print(f"🔐 Is Superuser: {admin_user.is_superuser}")

if __name__ == '__main__':
    create_admin()
