{"ast": null, "code": "export var setIsSubsetDocs = {\n  name: 'setIsSubset',\n  category: 'Set',\n  syntax: ['setIsSubset(set1, set2)'],\n  description: 'Check whether a (multi)set is a subset of another (multi)set: every element of set1 is the element of set2. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setIsSubset([1, 2], [3, 4, 5, 6])', 'setIsSubset([3, 4], [3, 4, 5, 6])'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference']\n};", "map": {"version": 3, "names": ["setIsSubsetDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setIsSubset.js"], "sourcesContent": ["export var setIsSubsetDocs = {\n  name: 'setIsSubset',\n  category: 'Set',\n  syntax: ['setIsSubset(set1, set2)'],\n  description: 'Check whether a (multi)set is a subset of another (multi)set: every element of set1 is the element of set2. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setIsSubset([1, 2], [3, 4, 5, 6])', 'setIsSubset([3, 4], [3, 4, 5, 6])'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,yBAAyB,CAAC;EACnCC,WAAW,EAAE,uMAAuM;EACpNC,QAAQ,EAAE,CAAC,mCAAmC,EAAE,mCAAmC,CAAC;EACpFC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,eAAe;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}