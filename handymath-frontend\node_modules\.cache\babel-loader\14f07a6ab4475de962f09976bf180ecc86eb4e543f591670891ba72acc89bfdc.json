{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { typeOf as _typeOf } from '../../utils/is.js';\nvar name = 'typeOf';\nvar dependencies = ['typed'];\nexport var createTypeOf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Determine the type of an entity.\n   *\n   * Syntax:\n   *\n   *    math.typeOf(x)\n   *\n   * Examples:\n   *\n   *    // This list is intended to include all relevant types, for testing\n   *    // purposes:\n   *    math.typeOf(3.5)                      // returns 'number'\n   *    math.typeOf(42n)                      // returns 'bigint'\n   *    math.typeOf(math.complex('2-4i'))     // returns 'Complex'\n   *    math.typeOf(math.unit('45 deg'))      // returns 'Unit'\n   *    math.typeOf('hello world')            // returns 'string'\n   *    math.typeOf(null)                     // returns 'null'\n   *    math.typeOf(true)                     // returns 'boolean'\n   *    math.typeOf([1, 2, 3])                // returns 'Array'\n   *    math.typeOf(new Date())               // returns 'Date'\n   *    math.typeOf(function () {})           // returns 'function'\n   *    math.typeOf({a: 2, b: 3})             // returns 'Object'\n   *    math.typeOf(/a regexp/)               // returns 'RegExp'\n   *    math.typeOf(undefined)                // returns 'undefined'\n   *    math.typeOf(math.bignumber('23e99'))  // returns 'BigNumber'\n   *    math.typeOf(math.chain(2))            // returns 'Chain'\n   *    math.typeOf(math.fraction(1, 3))      // returns 'Fraction'\n   *    math.typeOf(math.help('sqrt'))        // returns 'Help'\n   *    math.typeOf(math.index(1, 3))         // returns 'Index'\n   *    math.typeOf(math.matrix([[1],[3]]))   // returns 'DenseMatrix'\n   *    math.typeOf(math.matrix([],'sparse')) // returns 'SparseMatrix'\n   *    math.typeOf(new math.Range(0, 10))    // returns 'Range'\n   *    math.typeOf(math.evaluate('a=2\\na'))  // returns 'ResultSet'\n   *    math.typeOf(math.parse('A[2]'))       // returns 'AccessorNode'\n   *    math.typeOf(math.parse('[1,2,3]'))    // returns 'ArrayNode'\n   *    math.typeOf(math.parse('x=2'))        // returns 'AssignmentNode'\n   *    math.typeOf(math.parse('a=2; b=3'))   // returns 'BlockNode'\n   *    math.typeOf(math.parse('x<0?-1:1'))   // returns 'ConditionalNode'\n   *    math.typeOf(math.parse('2.3'))        // returns 'ConstantNode'\n   *    math.typeOf(math.parse('f(x)=x^2'))   // returns 'FunctionAssignmentNode'\n   *    math.typeOf(math.parse('sqrt(4)'))    // returns 'FunctionNode'\n   *    math.typeOf(math.parse('A[2]').index) // returns 'IndexNode'\n   *    math.typeOf(math.parse('{a:2}'))      // returns 'ObjectNode'\n   *    math.typeOf(math.parse('(2+3)'))      // returns 'ParenthesisNode'\n   *    math.typeOf(math.parse('1:10'))       // returns 'RangeNode'\n   *    math.typeOf(math.parse('a<b<c'))      // returns 'RelationalNode'\n   *    math.typeOf(math.parse('x'))          // returns 'SymbolNode'\n   *\n   * @param {*} x     The variable for which to test the type.\n   * @return {string} Returns the name of the type. Primitive types are lower case,\n   *                  non-primitive types are upper-camel-case.\n   *                  For example 'number', 'string', 'Array', 'Date'.\n   */\n  return typed(name, {\n    any: _typeOf\n  });\n});", "map": {"version": 3, "names": ["factory", "typeOf", "_typeOf", "name", "dependencies", "createTypeOf", "_ref", "typed", "any"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/utils/typeOf.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { typeOf as _typeOf } from '../../utils/is.js';\nvar name = 'typeOf';\nvar dependencies = ['typed'];\nexport var createTypeOf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Determine the type of an entity.\n   *\n   * Syntax:\n   *\n   *    math.typeOf(x)\n   *\n   * Examples:\n   *\n   *    // This list is intended to include all relevant types, for testing\n   *    // purposes:\n   *    math.typeOf(3.5)                      // returns 'number'\n   *    math.typeOf(42n)                      // returns 'bigint'\n   *    math.typeOf(math.complex('2-4i'))     // returns 'Complex'\n   *    math.typeOf(math.unit('45 deg'))      // returns 'Unit'\n   *    math.typeOf('hello world')            // returns 'string'\n   *    math.typeOf(null)                     // returns 'null'\n   *    math.typeOf(true)                     // returns 'boolean'\n   *    math.typeOf([1, 2, 3])                // returns 'Array'\n   *    math.typeOf(new Date())               // returns 'Date'\n   *    math.typeOf(function () {})           // returns 'function'\n   *    math.typeOf({a: 2, b: 3})             // returns 'Object'\n   *    math.typeOf(/a regexp/)               // returns 'RegExp'\n   *    math.typeOf(undefined)                // returns 'undefined'\n   *    math.typeOf(math.bignumber('23e99'))  // returns 'BigNumber'\n   *    math.typeOf(math.chain(2))            // returns 'Chain'\n   *    math.typeOf(math.fraction(1, 3))      // returns 'Fraction'\n   *    math.typeOf(math.help('sqrt'))        // returns 'Help'\n   *    math.typeOf(math.index(1, 3))         // returns 'Index'\n   *    math.typeOf(math.matrix([[1],[3]]))   // returns 'DenseMatrix'\n   *    math.typeOf(math.matrix([],'sparse')) // returns 'SparseMatrix'\n   *    math.typeOf(new math.Range(0, 10))    // returns 'Range'\n   *    math.typeOf(math.evaluate('a=2\\na'))  // returns 'ResultSet'\n   *    math.typeOf(math.parse('A[2]'))       // returns 'AccessorNode'\n   *    math.typeOf(math.parse('[1,2,3]'))    // returns 'ArrayNode'\n   *    math.typeOf(math.parse('x=2'))        // returns 'AssignmentNode'\n   *    math.typeOf(math.parse('a=2; b=3'))   // returns 'BlockNode'\n   *    math.typeOf(math.parse('x<0?-1:1'))   // returns 'ConditionalNode'\n   *    math.typeOf(math.parse('2.3'))        // returns 'ConstantNode'\n   *    math.typeOf(math.parse('f(x)=x^2'))   // returns 'FunctionAssignmentNode'\n   *    math.typeOf(math.parse('sqrt(4)'))    // returns 'FunctionNode'\n   *    math.typeOf(math.parse('A[2]').index) // returns 'IndexNode'\n   *    math.typeOf(math.parse('{a:2}'))      // returns 'ObjectNode'\n   *    math.typeOf(math.parse('(2+3)'))      // returns 'ParenthesisNode'\n   *    math.typeOf(math.parse('1:10'))       // returns 'RangeNode'\n   *    math.typeOf(math.parse('a<b<c'))      // returns 'RelationalNode'\n   *    math.typeOf(math.parse('x'))          // returns 'SymbolNode'\n   *\n   * @param {*} x     The variable for which to test the type.\n   * @return {string} Returns the name of the type. Primitive types are lower case,\n   *                  non-primitive types are upper-camel-case.\n   *                  For example 'number', 'string', 'Array', 'Date'.\n   */\n  return typed(name, {\n    any: _typeOf\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,IAAIC,OAAO,QAAQ,mBAAmB;AACrD,IAAIC,IAAI,GAAG,QAAQ;AACnB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,YAAY,GAAG,eAAeL,OAAO,CAACG,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC3E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAK,CAACJ,IAAI,EAAE;IACjBK,GAAG,EAAEN;EACP,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}