{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Permutes a sparse matrix C = P * A * Q\n *\n * @param {SparseMatrix}  a         The Matrix A\n * @param {Array}   pinv            The row permutation vector\n * @param {Array}   q               The column permutation vector\n * @param {boolean} values          Create a pattern matrix (false), values and pattern otherwise\n *\n * @return {Matrix}                 C = P * A * Q, null on error\n */\nexport function csPermute(a, pinv, q, values) {\n  // a arrays\n  var avalues = a._values;\n  var aindex = a._index;\n  var aptr = a._ptr;\n  var asize = a._size;\n  var adt = a._datatype;\n  // rows & columns\n  var m = asize[0];\n  var n = asize[1];\n  // c arrays\n  var cvalues = values && a._values ? [] : null;\n  var cindex = []; // (aptr[n])\n  var cptr = []; // (n + 1)\n  // initialize vars\n  var nz = 0;\n  // loop columns\n  for (var k = 0; k < n; k++) {\n    // column k of C is column q[k] of A\n    cptr[k] = nz;\n    // apply column permutation\n    var j = q ? q[k] : k;\n    // loop values in column j of A\n    for (var t0 = aptr[j], t1 = aptr[j + 1], t = t0; t < t1; t++) {\n      // row i of A is row pinv[i] of C\n      var r = pinv ? pinv[aindex[t]] : aindex[t];\n      // index\n      cindex[nz] = r;\n      // check we need to populate values\n      if (cvalues) {\n        cvalues[nz] = avalues[t];\n      }\n      // increment number of nonzero elements\n      nz++;\n    }\n  }\n  // finalize the last column of C\n  cptr[n] = nz;\n  // return C matrix\n  return a.createSparseMatrix({\n    values: cvalues,\n    index: cindex,\n    ptr: cptr,\n    size: [m, n],\n    datatype: adt\n  });\n}", "map": {"version": 3, "names": ["csPermute", "a", "pinv", "q", "values", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "m", "n", "cvalues", "cindex", "cptr", "nz", "k", "j", "t0", "t1", "t", "r", "createSparseMatrix", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csPermute.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Permutes a sparse matrix C = P * A * Q\n *\n * @param {SparseMatrix}  a         The Matrix A\n * @param {Array}   pinv            The row permutation vector\n * @param {Array}   q               The column permutation vector\n * @param {boolean} values          Create a pattern matrix (false), values and pattern otherwise\n *\n * @return {Matrix}                 C = P * A * Q, null on error\n */\nexport function csPermute(a, pinv, q, values) {\n  // a arrays\n  var avalues = a._values;\n  var aindex = a._index;\n  var aptr = a._ptr;\n  var asize = a._size;\n  var adt = a._datatype;\n  // rows & columns\n  var m = asize[0];\n  var n = asize[1];\n  // c arrays\n  var cvalues = values && a._values ? [] : null;\n  var cindex = []; // (aptr[n])\n  var cptr = []; // (n + 1)\n  // initialize vars\n  var nz = 0;\n  // loop columns\n  for (var k = 0; k < n; k++) {\n    // column k of C is column q[k] of A\n    cptr[k] = nz;\n    // apply column permutation\n    var j = q ? q[k] : k;\n    // loop values in column j of A\n    for (var t0 = aptr[j], t1 = aptr[j + 1], t = t0; t < t1; t++) {\n      // row i of A is row pinv[i] of C\n      var r = pinv ? pinv[aindex[t]] : aindex[t];\n      // index\n      cindex[nz] = r;\n      // check we need to populate values\n      if (cvalues) {\n        cvalues[nz] = avalues[t];\n      }\n      // increment number of nonzero elements\n      nz++;\n    }\n  }\n  // finalize the last column of C\n  cptr[n] = nz;\n  // return C matrix\n  return a.createSparseMatrix({\n    values: cvalues,\n    index: cindex,\n    ptr: cptr,\n    size: [m, n],\n    datatype: adt\n  });\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,CAAC,EAAEC,IAAI,EAAEC,CAAC,EAAEC,MAAM,EAAE;EAC5C;EACA,IAAIC,OAAO,GAAGJ,CAAC,CAACK,OAAO;EACvB,IAAIC,MAAM,GAAGN,CAAC,CAACO,MAAM;EACrB,IAAIC,IAAI,GAAGR,CAAC,CAACS,IAAI;EACjB,IAAIC,KAAK,GAAGV,CAAC,CAACW,KAAK;EACnB,IAAIC,GAAG,GAAGZ,CAAC,CAACa,SAAS;EACrB;EACA,IAAIC,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC;EAChB,IAAIK,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;EAChB;EACA,IAAIM,OAAO,GAAGb,MAAM,IAAIH,CAAC,CAACK,OAAO,GAAG,EAAE,GAAG,IAAI;EAC7C,IAAIY,MAAM,GAAG,EAAE,CAAC,CAAC;EACjB,IAAIC,IAAI,GAAG,EAAE,CAAC,CAAC;EACf;EACA,IAAIC,EAAE,GAAG,CAAC;EACV;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;IAC1B;IACAF,IAAI,CAACE,CAAC,CAAC,GAAGD,EAAE;IACZ;IACA,IAAIE,CAAC,GAAGnB,CAAC,GAAGA,CAAC,CAACkB,CAAC,CAAC,GAAGA,CAAC;IACpB;IACA,KAAK,IAAIE,EAAE,GAAGd,IAAI,CAACa,CAAC,CAAC,EAAEE,EAAE,GAAGf,IAAI,CAACa,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;MAC5D;MACA,IAAIC,CAAC,GAAGxB,IAAI,GAAGA,IAAI,CAACK,MAAM,CAACkB,CAAC,CAAC,CAAC,GAAGlB,MAAM,CAACkB,CAAC,CAAC;MAC1C;MACAP,MAAM,CAACE,EAAE,CAAC,GAAGM,CAAC;MACd;MACA,IAAIT,OAAO,EAAE;QACXA,OAAO,CAACG,EAAE,CAAC,GAAGf,OAAO,CAACoB,CAAC,CAAC;MAC1B;MACA;MACAL,EAAE,EAAE;IACN;EACF;EACA;EACAD,IAAI,CAACH,CAAC,CAAC,GAAGI,EAAE;EACZ;EACA,OAAOnB,CAAC,CAAC0B,kBAAkB,CAAC;IAC1BvB,MAAM,EAAEa,OAAO;IACfW,KAAK,EAAEV,MAAM;IACbW,GAAG,EAAEV,IAAI;IACTW,IAAI,EAAE,CAACf,CAAC,EAAEC,CAAC,CAAC;IACZe,QAAQ,EAAElB;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}