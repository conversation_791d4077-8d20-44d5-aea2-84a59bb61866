{"ast": null, "code": "export var setCartesianDocs = {\n  name: 'setCartesian',\n  category: 'Set',\n  syntax: ['setCartesian(set1, set2)'],\n  description: 'Create the cartesian product of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays and the values will be sorted in ascending order before the operation.',\n  examples: ['setCartesian([1, 2], [3, 4])'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference', 'setPowerset']\n};", "map": {"version": 3, "names": ["setCartesianDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setCartesian.js"], "sourcesContent": ["export var setCartesianDocs = {\n  name: 'setCartesian',\n  category: 'Set',\n  syntax: ['setCartesian(set1, set2)'],\n  description: 'Create the cartesian product of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays and the values will be sorted in ascending order before the operation.',\n  examples: ['setCartesian([1, 2], [3, 4])'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference', 'setPowerset']\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,0BAA0B,CAAC;EACpCC,WAAW,EAAE,6LAA6L;EAC1MC,QAAQ,EAAE,CAAC,8BAA8B,CAAC;EAC1CC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,aAAa;AACtE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}