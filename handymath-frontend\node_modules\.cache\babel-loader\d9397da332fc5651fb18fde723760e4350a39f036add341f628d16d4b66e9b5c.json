{"ast": null, "code": "export var log1pDocs = {\n  name: 'log1p',\n  category: 'Arithmetic',\n  syntax: ['log1p(x)', 'log1p(x, base)'],\n  description: 'Calculate the logarithm of a `value+1`',\n  examples: ['log1p(2.5)', 'exp(log1p(1.4))', 'pow(10, 4)', 'log1p(9999, 10)', 'log1p(9999) / log(10)'],\n  seealso: ['exp', 'log', 'log2', 'log10']\n};", "map": {"version": 3, "names": ["log1pDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/log1p.js"], "sourcesContent": ["export var log1pDocs = {\n  name: 'log1p',\n  category: 'Arithmetic',\n  syntax: ['log1p(x)', 'log1p(x, base)'],\n  description: 'Calculate the logarithm of a `value+1`',\n  examples: ['log1p(2.5)', 'exp(log1p(1.4))', 'pow(10, 4)', 'log1p(9999, 10)', 'log1p(9999) / log(10)'],\n  seealso: ['exp', 'log', 'log2', 'log10']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;EACtCC,WAAW,EAAE,wCAAwC;EACrDC,QAAQ,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,iBAAiB,EAAE,uBAAuB,CAAC;EACrGC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}