{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createTo } from '../../factoriesAny.js';\nexport var toDependencies = {\n  concatDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createTo\n};", "map": {"version": 3, "names": ["concatDependencies", "matrixDependencies", "typedDependencies", "createTo", "toDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesTo.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createTo } from '../../factoriesAny.js';\nexport var toDependencies = {\n  concatDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createTo\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,IAAIC,cAAc,GAAG;EAC1BJ,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}