{"ast": null, "code": "import { isBigNumber, isString, typeOf } from './is.js';\nimport { format as formatNumber } from './number.js';\nimport { format as formatBigNumber } from './bignumber/formatter.js';\n\n/**\n * Check if a text ends with a certain string.\n * @param {string} text\n * @param {string} search\n */\nexport function endsWith(text, search) {\n  var start = text.length - search.length;\n  var end = text.length;\n  return text.substring(start, end) === search;\n}\n\n/**\n * Format a value of any type into a string.\n *\n * Usage:\n *     math.format(value)\n *     math.format(value, precision)\n *     math.format(value, options)\n *\n * When value is a function:\n *\n * - When the function has a property `syntax`, it returns this\n *   syntax description.\n * - In other cases, a string `'function'` is returned.\n *\n * When `value` is an Object:\n *\n * - When the object contains a property `format` being a function, this\n *   function is invoked as `value.format(options)` and the result is returned.\n * - When the object has its own `toString` method, this method is invoked\n *   and the result is returned.\n * - In other cases the function will loop over all object properties and\n *   return JSON object notation like '{\"a\": 2, \"b\": 3}'.\n *\n * Example usage:\n *     math.format(2/7)                // '0.2857142857142857'\n *     math.format(math.pi, 3)         // '3.14'\n *     math.format(new Complex(2, 3))  // '2 + 3i'\n *     math.format('hello')            // '\"hello\"'\n *\n * @param {*} value             Value to be stringified\n * @param {Object | number | Function} [options]\n *     Formatting options. See src/utils/number.js:format for a\n *     description of the available options controlling number output.\n *     This generic \"format\" also supports the option property `truncate: NN`\n *     giving the maximum number NN of characters to return (if there would\n *     have been more, they are deleted and replaced by an ellipsis).\n * @return {string} str\n */\nexport function format(value, options) {\n  var result = _format(value, options);\n  if (options && typeof options === 'object' && 'truncate' in options && result.length > options.truncate) {\n    return result.substring(0, options.truncate - 3) + '...';\n  }\n  return result;\n}\nfunction _format(value, options) {\n  if (typeof value === 'number') {\n    return formatNumber(value, options);\n  }\n  if (isBigNumber(value)) {\n    return formatBigNumber(value, options);\n  }\n\n  // note: we use unsafe duck-typing here to check for Fractions, this is\n  // ok here since we're only invoking toString or concatenating its values\n  if (looksLikeFraction(value)) {\n    if (!options || options.fraction !== 'decimal') {\n      // output as ratio, like '1/3'\n      return \"\".concat(value.s * value.n, \"/\").concat(value.d);\n    } else {\n      // output as decimal, like '0.(3)'\n      return value.toString();\n    }\n  }\n  if (Array.isArray(value)) {\n    return formatArray(value, options);\n  }\n  if (isString(value)) {\n    return stringify(value);\n  }\n  if (typeof value === 'function') {\n    return value.syntax ? String(value.syntax) : 'function';\n  }\n  if (value && typeof value === 'object') {\n    if (typeof value.format === 'function') {\n      return value.format(options);\n    } else if (value && value.toString(options) !== {}.toString()) {\n      // this object has a non-native toString method, use that one\n      return value.toString(options);\n    } else {\n      var entries = Object.keys(value).map(key => {\n        return stringify(key) + ': ' + format(value[key], options);\n      });\n      return '{' + entries.join(', ') + '}';\n    }\n  }\n  return String(value);\n}\n\n/**\n * Stringify a value into a string enclosed in double quotes.\n * Unescaped double quotes and backslashes inside the value are escaped.\n * @param {*} value\n * @return {string}\n */\nexport function stringify(value) {\n  var text = String(value);\n  var escaped = '';\n  var i = 0;\n  while (i < text.length) {\n    var c = text.charAt(i);\n    escaped += c in controlCharacters ? controlCharacters[c] : c;\n    i++;\n  }\n  return '\"' + escaped + '\"';\n}\nvar controlCharacters = {\n  '\"': '\\\\\"',\n  '\\\\': '\\\\\\\\',\n  '\\b': '\\\\b',\n  '\\f': '\\\\f',\n  '\\n': '\\\\n',\n  '\\r': '\\\\r',\n  '\\t': '\\\\t'\n};\n\n/**\n * Escape special HTML characters\n * @param {*} value\n * @return {string}\n */\nexport function escape(value) {\n  var text = String(value);\n  text = text.replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n  return text;\n}\n\n/**\n * Recursively format an n-dimensional matrix\n * Example output: \"[[1, 2], [3, 4]]\"\n * @param {Array} array\n * @param {Object | number | Function} [options]  Formatting options. See\n *                                                lib/utils/number:format for a\n *                                                description of the available\n *                                                options.\n * @returns {string} str\n */\nfunction formatArray(array, options) {\n  if (Array.isArray(array)) {\n    var str = '[';\n    var len = array.length;\n    for (var i = 0; i < len; i++) {\n      if (i !== 0) {\n        str += ', ';\n      }\n      str += formatArray(array[i], options);\n    }\n    str += ']';\n    return str;\n  } else {\n    return format(array, options);\n  }\n}\n\n/**\n * Check whether a value looks like a Fraction (unsafe duck-type check)\n * @param {*} value\n * @return {boolean}\n */\nfunction looksLikeFraction(value) {\n  return value && typeof value === 'object' && typeof value.s === 'bigint' && typeof value.n === 'bigint' && typeof value.d === 'bigint' || false;\n}\n\n/**\n * Compare two strings\n * @param {string} x\n * @param {string} y\n * @returns {number}\n */\nexport function compareText(x, y) {\n  // we don't want to convert numbers to string, only accept string input\n  if (!isString(x)) {\n    throw new TypeError('Unexpected type of argument in function compareText ' + '(expected: string or Array or Matrix, actual: ' + typeOf(x) + ', index: 0)');\n  }\n  if (!isString(y)) {\n    throw new TypeError('Unexpected type of argument in function compareText ' + '(expected: string or Array or Matrix, actual: ' + typeOf(y) + ', index: 1)');\n  }\n  return x === y ? 0 : x > y ? 1 : -1;\n}", "map": {"version": 3, "names": ["isBigNumber", "isString", "typeOf", "format", "formatNumber", "formatBigNumber", "endsWith", "text", "search", "start", "length", "end", "substring", "value", "options", "result", "_format", "truncate", "looksLikeFraction", "fraction", "concat", "s", "n", "d", "toString", "Array", "isArray", "formatArray", "stringify", "syntax", "String", "entries", "Object", "keys", "map", "key", "join", "escaped", "i", "c", "char<PERSON>t", "controlCharacters", "escape", "replace", "array", "str", "len", "compareText", "x", "y", "TypeError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/string.js"], "sourcesContent": ["import { isBigNumber, isString, typeOf } from './is.js';\nimport { format as formatNumber } from './number.js';\nimport { format as formatBigNumber } from './bignumber/formatter.js';\n\n/**\n * Check if a text ends with a certain string.\n * @param {string} text\n * @param {string} search\n */\nexport function endsWith(text, search) {\n  var start = text.length - search.length;\n  var end = text.length;\n  return text.substring(start, end) === search;\n}\n\n/**\n * Format a value of any type into a string.\n *\n * Usage:\n *     math.format(value)\n *     math.format(value, precision)\n *     math.format(value, options)\n *\n * When value is a function:\n *\n * - When the function has a property `syntax`, it returns this\n *   syntax description.\n * - In other cases, a string `'function'` is returned.\n *\n * When `value` is an Object:\n *\n * - When the object contains a property `format` being a function, this\n *   function is invoked as `value.format(options)` and the result is returned.\n * - When the object has its own `toString` method, this method is invoked\n *   and the result is returned.\n * - In other cases the function will loop over all object properties and\n *   return JSON object notation like '{\"a\": 2, \"b\": 3}'.\n *\n * Example usage:\n *     math.format(2/7)                // '0.2857142857142857'\n *     math.format(math.pi, 3)         // '3.14'\n *     math.format(new Complex(2, 3))  // '2 + 3i'\n *     math.format('hello')            // '\"hello\"'\n *\n * @param {*} value             Value to be stringified\n * @param {Object | number | Function} [options]\n *     Formatting options. See src/utils/number.js:format for a\n *     description of the available options controlling number output.\n *     This generic \"format\" also supports the option property `truncate: NN`\n *     giving the maximum number NN of characters to return (if there would\n *     have been more, they are deleted and replaced by an ellipsis).\n * @return {string} str\n */\nexport function format(value, options) {\n  var result = _format(value, options);\n  if (options && typeof options === 'object' && 'truncate' in options && result.length > options.truncate) {\n    return result.substring(0, options.truncate - 3) + '...';\n  }\n  return result;\n}\nfunction _format(value, options) {\n  if (typeof value === 'number') {\n    return formatNumber(value, options);\n  }\n  if (isBigNumber(value)) {\n    return formatBigNumber(value, options);\n  }\n\n  // note: we use unsafe duck-typing here to check for Fractions, this is\n  // ok here since we're only invoking toString or concatenating its values\n  if (looksLikeFraction(value)) {\n    if (!options || options.fraction !== 'decimal') {\n      // output as ratio, like '1/3'\n      return \"\".concat(value.s * value.n, \"/\").concat(value.d);\n    } else {\n      // output as decimal, like '0.(3)'\n      return value.toString();\n    }\n  }\n  if (Array.isArray(value)) {\n    return formatArray(value, options);\n  }\n  if (isString(value)) {\n    return stringify(value);\n  }\n  if (typeof value === 'function') {\n    return value.syntax ? String(value.syntax) : 'function';\n  }\n  if (value && typeof value === 'object') {\n    if (typeof value.format === 'function') {\n      return value.format(options);\n    } else if (value && value.toString(options) !== {}.toString()) {\n      // this object has a non-native toString method, use that one\n      return value.toString(options);\n    } else {\n      var entries = Object.keys(value).map(key => {\n        return stringify(key) + ': ' + format(value[key], options);\n      });\n      return '{' + entries.join(', ') + '}';\n    }\n  }\n  return String(value);\n}\n\n/**\n * Stringify a value into a string enclosed in double quotes.\n * Unescaped double quotes and backslashes inside the value are escaped.\n * @param {*} value\n * @return {string}\n */\nexport function stringify(value) {\n  var text = String(value);\n  var escaped = '';\n  var i = 0;\n  while (i < text.length) {\n    var c = text.charAt(i);\n    escaped += c in controlCharacters ? controlCharacters[c] : c;\n    i++;\n  }\n  return '\"' + escaped + '\"';\n}\nvar controlCharacters = {\n  '\"': '\\\\\"',\n  '\\\\': '\\\\\\\\',\n  '\\b': '\\\\b',\n  '\\f': '\\\\f',\n  '\\n': '\\\\n',\n  '\\r': '\\\\r',\n  '\\t': '\\\\t'\n};\n\n/**\n * Escape special HTML characters\n * @param {*} value\n * @return {string}\n */\nexport function escape(value) {\n  var text = String(value);\n  text = text.replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n  return text;\n}\n\n/**\n * Recursively format an n-dimensional matrix\n * Example output: \"[[1, 2], [3, 4]]\"\n * @param {Array} array\n * @param {Object | number | Function} [options]  Formatting options. See\n *                                                lib/utils/number:format for a\n *                                                description of the available\n *                                                options.\n * @returns {string} str\n */\nfunction formatArray(array, options) {\n  if (Array.isArray(array)) {\n    var str = '[';\n    var len = array.length;\n    for (var i = 0; i < len; i++) {\n      if (i !== 0) {\n        str += ', ';\n      }\n      str += formatArray(array[i], options);\n    }\n    str += ']';\n    return str;\n  } else {\n    return format(array, options);\n  }\n}\n\n/**\n * Check whether a value looks like a Fraction (unsafe duck-type check)\n * @param {*} value\n * @return {boolean}\n */\nfunction looksLikeFraction(value) {\n  return value && typeof value === 'object' && typeof value.s === 'bigint' && typeof value.n === 'bigint' && typeof value.d === 'bigint' || false;\n}\n\n/**\n * Compare two strings\n * @param {string} x\n * @param {string} y\n * @returns {number}\n */\nexport function compareText(x, y) {\n  // we don't want to convert numbers to string, only accept string input\n  if (!isString(x)) {\n    throw new TypeError('Unexpected type of argument in function compareText ' + '(expected: string or Array or Matrix, actual: ' + typeOf(x) + ', index: 0)');\n  }\n  if (!isString(y)) {\n    throw new TypeError('Unexpected type of argument in function compareText ' + '(expected: string or Array or Matrix, actual: ' + typeOf(y) + ', index: 1)');\n  }\n  return x === y ? 0 : x > y ? 1 : -1;\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,SAAS;AACvD,SAASC,MAAM,IAAIC,YAAY,QAAQ,aAAa;AACpD,SAASD,MAAM,IAAIE,eAAe,QAAQ,0BAA0B;;AAEpE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACrC,IAAIC,KAAK,GAAGF,IAAI,CAACG,MAAM,GAAGF,MAAM,CAACE,MAAM;EACvC,IAAIC,GAAG,GAAGJ,IAAI,CAACG,MAAM;EACrB,OAAOH,IAAI,CAACK,SAAS,CAACH,KAAK,EAAEE,GAAG,CAAC,KAAKH,MAAM;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASL,MAAMA,CAACU,KAAK,EAAEC,OAAO,EAAE;EACrC,IAAIC,MAAM,GAAGC,OAAO,CAACH,KAAK,EAAEC,OAAO,CAAC;EACpC,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,IAAIC,MAAM,CAACL,MAAM,GAAGI,OAAO,CAACG,QAAQ,EAAE;IACvG,OAAOF,MAAM,CAACH,SAAS,CAAC,CAAC,EAAEE,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK;EAC1D;EACA,OAAOF,MAAM;AACf;AACA,SAASC,OAAOA,CAACH,KAAK,EAAEC,OAAO,EAAE;EAC/B,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOT,YAAY,CAACS,KAAK,EAAEC,OAAO,CAAC;EACrC;EACA,IAAId,WAAW,CAACa,KAAK,CAAC,EAAE;IACtB,OAAOR,eAAe,CAACQ,KAAK,EAAEC,OAAO,CAAC;EACxC;;EAEA;EACA;EACA,IAAII,iBAAiB,CAACL,KAAK,CAAC,EAAE;IAC5B,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACK,QAAQ,KAAK,SAAS,EAAE;MAC9C;MACA,OAAO,EAAE,CAACC,MAAM,CAACP,KAAK,CAACQ,CAAC,GAAGR,KAAK,CAACS,CAAC,EAAE,GAAG,CAAC,CAACF,MAAM,CAACP,KAAK,CAACU,CAAC,CAAC;IAC1D,CAAC,MAAM;MACL;MACA,OAAOV,KAAK,CAACW,QAAQ,CAAC,CAAC;IACzB;EACF;EACA,IAAIC,KAAK,CAACC,OAAO,CAACb,KAAK,CAAC,EAAE;IACxB,OAAOc,WAAW,CAACd,KAAK,EAAEC,OAAO,CAAC;EACpC;EACA,IAAIb,QAAQ,CAACY,KAAK,CAAC,EAAE;IACnB,OAAOe,SAAS,CAACf,KAAK,CAAC;EACzB;EACA,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,OAAOA,KAAK,CAACgB,MAAM,GAAGC,MAAM,CAACjB,KAAK,CAACgB,MAAM,CAAC,GAAG,UAAU;EACzD;EACA,IAAIhB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtC,IAAI,OAAOA,KAAK,CAACV,MAAM,KAAK,UAAU,EAAE;MACtC,OAAOU,KAAK,CAACV,MAAM,CAACW,OAAO,CAAC;IAC9B,CAAC,MAAM,IAAID,KAAK,IAAIA,KAAK,CAACW,QAAQ,CAACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,EAAE;MAC7D;MACA,OAAOX,KAAK,CAACW,QAAQ,CAACV,OAAO,CAAC;IAChC,CAAC,MAAM;MACL,IAAIiB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACpB,KAAK,CAAC,CAACqB,GAAG,CAACC,GAAG,IAAI;QAC1C,OAAOP,SAAS,CAACO,GAAG,CAAC,GAAG,IAAI,GAAGhC,MAAM,CAACU,KAAK,CAACsB,GAAG,CAAC,EAAErB,OAAO,CAAC;MAC5D,CAAC,CAAC;MACF,OAAO,GAAG,GAAGiB,OAAO,CAACK,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACvC;EACF;EACA,OAAON,MAAM,CAACjB,KAAK,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,SAASA,CAACf,KAAK,EAAE;EAC/B,IAAIN,IAAI,GAAGuB,MAAM,CAACjB,KAAK,CAAC;EACxB,IAAIwB,OAAO,GAAG,EAAE;EAChB,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAG/B,IAAI,CAACG,MAAM,EAAE;IACtB,IAAI6B,CAAC,GAAGhC,IAAI,CAACiC,MAAM,CAACF,CAAC,CAAC;IACtBD,OAAO,IAAIE,CAAC,IAAIE,iBAAiB,GAAGA,iBAAiB,CAACF,CAAC,CAAC,GAAGA,CAAC;IAC5DD,CAAC,EAAE;EACL;EACA,OAAO,GAAG,GAAGD,OAAO,GAAG,GAAG;AAC5B;AACA,IAAII,iBAAiB,GAAG;EACtB,GAAG,EAAE,KAAK;EACV,IAAI,EAAE,MAAM;EACZ,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAAC7B,KAAK,EAAE;EAC5B,IAAIN,IAAI,GAAGuB,MAAM,CAACjB,KAAK,CAAC;EACxBN,IAAI,GAAGA,IAAI,CAACoC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EAC7H,OAAOpC,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,WAAWA,CAACiB,KAAK,EAAE9B,OAAO,EAAE;EACnC,IAAIW,KAAK,CAACC,OAAO,CAACkB,KAAK,CAAC,EAAE;IACxB,IAAIC,GAAG,GAAG,GAAG;IACb,IAAIC,GAAG,GAAGF,KAAK,CAAClC,MAAM;IACtB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,GAAG,EAAER,CAAC,EAAE,EAAE;MAC5B,IAAIA,CAAC,KAAK,CAAC,EAAE;QACXO,GAAG,IAAI,IAAI;MACb;MACAA,GAAG,IAAIlB,WAAW,CAACiB,KAAK,CAACN,CAAC,CAAC,EAAExB,OAAO,CAAC;IACvC;IACA+B,GAAG,IAAI,GAAG;IACV,OAAOA,GAAG;EACZ,CAAC,MAAM;IACL,OAAO1C,MAAM,CAACyC,KAAK,EAAE9B,OAAO,CAAC;EAC/B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,iBAAiBA,CAACL,KAAK,EAAE;EAChC,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACQ,CAAC,KAAK,QAAQ,IAAI,OAAOR,KAAK,CAACS,CAAC,KAAK,QAAQ,IAAI,OAAOT,KAAK,CAACU,CAAC,KAAK,QAAQ,IAAI,KAAK;AACjJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwB,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChC;EACA,IAAI,CAAChD,QAAQ,CAAC+C,CAAC,CAAC,EAAE;IAChB,MAAM,IAAIE,SAAS,CAAC,sDAAsD,GAAG,gDAAgD,GAAGhD,MAAM,CAAC8C,CAAC,CAAC,GAAG,aAAa,CAAC;EAC5J;EACA,IAAI,CAAC/C,QAAQ,CAACgD,CAAC,CAAC,EAAE;IAChB,MAAM,IAAIC,SAAS,CAAC,sDAAsD,GAAG,gDAAgD,GAAGhD,MAAM,CAAC+C,CAAC,CAAC,GAAG,aAAa,CAAC;EAC5J;EACA,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}