{"ast": null, "code": "export var setUnionDocs = {\n  name: 'setUnion',\n  category: 'Set',\n  syntax: ['setUnion(set1, set2)'],\n  description: 'Create the union of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setUnion([1, 2, 3, 4], [3, 4, 5, 6])', 'setUnion([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setIntersect', 'setDifference']\n};", "map": {"version": 3, "names": ["setUnionDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setUnion.js"], "sourcesContent": ["export var setUnionDocs = {\n  name: 'setUnion',\n  category: 'Set',\n  syntax: ['setUnion(set1, set2)'],\n  description: 'Create the union of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setUnion([1, 2, 3, 4], [3, 4, 5, 6])', 'setUnion([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setIntersect', 'setDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,sBAAsB,CAAC;EAChCC,WAAW,EAAE,gIAAgI;EAC7IC,QAAQ,EAAE,CAAC,sCAAsC,EAAE,8CAA8C,CAAC;EAClGC,OAAO,EAAE,CAAC,cAAc,EAAE,eAAe;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}