import React from 'react';
import { Link, useLocation } from 'react-router-dom';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: string;
  isActive?: boolean;
}

interface StudentBreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

const StudentBreadcrumbs: React.FC<StudentBreadcrumbsProps> = ({ 
  items, 
  className = '',
  showHome = true 
}) => {
  const location = useLocation();
  
  // Génération automatique des breadcrumbs basée sur l'URL si aucun item n'est fourni
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(segment => segment !== '');
    const breadcrumbs: BreadcrumbItem[] = [];
    
    // Ajouter l'accueil si demandé
    if (showHome) {
      breadcrumbs.push({ 
        label: 'Accueil', 
        path: '/', 
        icon: '🏠' 
      });
    }

    // Mapping spécialisé pour l'espace étudiant
    const segmentLabels: { [key: string]: string } = {
      'etudiant': 'Espace Étudiant',
      'dashboard': 'Tableau de bord',
      'courses': 'Mes Cours',
      'lessons': 'Leçon',
      'exercises': 'Exercices',
      'progress': 'Ma Progression',
      'solver': 'Résolveur',
      'visualizer': 'Visualiseur',
      'profile': 'Mon Profil',
      'settings': 'Paramètres'
    };

    const segmentIcons: { [key: string]: string } = {
      'etudiant': '🎓',
      'dashboard': '📊',
      'courses': '📚',
      'lessons': '📖',
      'exercises': '📝',
      'progress': '📈',
      'solver': '🧮',
      'visualizer': '📊',
      'profile': '👤',
      'settings': '⚙️'
    };

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Ignorer les IDs numériques dans les breadcrumbs
      if (/^\d+$/.test(segment)) {
        return;
      }
      
      const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
      const icon = segmentIcons[segment];
      
      // Ne pas ajouter de lien pour le dernier segment (page actuelle)
      const isLast = index === pathSegments.length - 1;
      
      breadcrumbs.push({
        label,
        path: isLast ? undefined : currentPath,
        icon,
        isActive: isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  if (breadcrumbItems.length <= 1) {
    return null; // Ne pas afficher les breadcrumbs s'il n'y a qu'un seul élément
  }

  return (
    <nav 
      className={`flex items-center space-x-1 text-sm bg-gray-50 dark:bg-gray-800/50 px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`} 
      aria-label="Fil d'Ariane"
    >
      <ol className="flex items-center space-x-1">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <svg
                className="w-4 h-4 text-gray-400 mx-2 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}
            
            {item.path ? (
              <Link
                to={item.path}
                className="flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 hover:underline"
              >
                {item.icon && (
                  <span className="mr-1.5 text-base">{item.icon}</span>
                )}
                <span className="font-medium">{item.label}</span>
              </Link>
            ) : (
              <span className={`flex items-center font-semibold ${
                item.isActive 
                  ? 'text-primary-600 dark:text-primary-400' 
                  : 'text-gray-900 dark:text-white'
              }`}>
                {item.icon && (
                  <span className="mr-1.5 text-base">{item.icon}</span>
                )}
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default StudentBreadcrumbs;
