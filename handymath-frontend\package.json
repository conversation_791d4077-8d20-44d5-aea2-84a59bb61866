{"name": "handymath-frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:8000", "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.38", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "axios": "^1.4.0", "framer-motion": "^10.18.0", "mathjs": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "three": "^0.154.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/three": "^0.177.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.2"}}