{"ast": null, "code": "export var traceDocs = {\n  name: 'trace',\n  category: 'Matrix',\n  syntax: ['trace(A)'],\n  description: 'Calculate the trace of a matrix: the sum of the elements on the main diagonal of a square matrix.',\n  examples: ['A = [1, 2, 3; -1, 2, 3; 2, 0, 3]', 'trace(A)'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["traceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/trace.js"], "sourcesContent": ["export var traceDocs = {\n  name: 'trace',\n  category: 'Matrix',\n  syntax: ['trace(A)'],\n  description: 'Calculate the trace of a matrix: the sum of the elements on the main diagonal of a square matrix.',\n  examples: ['A = [1, 2, 3; -1, 2, 3; 2, 0, 3]', 'trace(A)'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,mGAAmG;EAChHC,QAAQ,EAAE,CAAC,kCAAkC,EAAE,UAAU,CAAC;EAC1DC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO;AAC1H,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}