{"ast": null, "code": "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function (global, module, define) {\n  function XorGen(seed) {\n    var me = this;\n\n    // Set up generator function.\n    me.next = function () {\n      // Update xor generator.\n      var X = me.x,\n        i = me.i,\n        t,\n        v,\n        w;\n      t = X[i];\n      t ^= t >>> 7;\n      v = t ^ t << 24;\n      t = X[i + 1 & 7];\n      v ^= t ^ t >>> 10;\n      t = X[i + 3 & 7];\n      v ^= t ^ t >>> 3;\n      t = X[i + 4 & 7];\n      v ^= t ^ t << 7;\n      t = X[i + 7 & 7];\n      t = t ^ t << 13;\n      v ^= t ^ t << 9;\n      X[i] = v;\n      me.i = i + 1 & 7;\n      return v;\n    };\n    function init(me, seed) {\n      var j,\n        w,\n        X = [];\n      if (seed === (seed | 0)) {\n        // Seed state array using a 32-bit integer.\n        w = X[0] = seed;\n      } else {\n        // Seed state using a string.\n        seed = '' + seed;\n        for (j = 0; j < seed.length; ++j) {\n          X[j & 7] = X[j & 7] << 15 ^ seed.charCodeAt(j) + X[j + 1 & 7] << 13;\n        }\n      }\n      // Enforce an array length of 8, not all zeroes.\n      while (X.length < 8) X.push(0);\n      for (j = 0; j < 8 && X[j] === 0; ++j);\n      if (j == 8) w = X[7] = -1;else w = X[j];\n      me.x = X;\n      me.i = 0;\n\n      // Discard an initial 256 values.\n      for (j = 256; j > 0; --j) {\n        me.next();\n      }\n    }\n    init(me, seed);\n  }\n  function copy(f, t) {\n    t.x = f.x.slice();\n    t.i = f.i;\n    return t;\n  }\n  function impl(seed, opts) {\n    if (seed == null) seed = +new Date();\n    var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function () {\n        return (xg.next() >>> 0) / 0x100000000;\n      };\n    prng.double = function () {\n      do {\n        var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n      } while (result === 0);\n      return result;\n    };\n    prng.int32 = xg.next;\n    prng.quick = prng;\n    if (state) {\n      if (state.x) copy(state, xg);\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n    return prng;\n  }\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.xorshift7 = impl;\n  }\n})(this, typeof module == 'object' && module,\n// present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "XorGen", "seed", "me", "next", "X", "x", "i", "t", "v", "w", "init", "j", "length", "charCodeAt", "push", "copy", "f", "slice", "impl", "opts", "Date", "xg", "state", "prng", "double", "top", "bot", "result", "int32", "quick", "exports", "amd", "xorshift7"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/seedrandom/lib/xorshift7.js"], "sourcesContent": ["// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,CAAC,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAElC,SAASC,MAAMA,CAACC,IAAI,EAAE;IACpB,IAAIC,EAAE,GAAG,IAAI;;IAEb;IACAA,EAAE,CAACC,IAAI,GAAG,YAAW;MACnB;MACA,IAAIC,CAAC,GAAGF,EAAE,CAACG,CAAC;QAAEC,CAAC,GAAGJ,EAAE,CAACI,CAAC;QAAEC,CAAC;QAAEC,CAAC;QAAEC,CAAC;MAC/BF,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;MAAEC,CAAC,IAAKA,CAAC,KAAK,CAAE;MAAEC,CAAC,GAAGD,CAAC,GAAIA,CAAC,IAAI,EAAG;MAC3CA,CAAC,GAAGH,CAAC,CAAEE,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;MAAEE,CAAC,IAAID,CAAC,GAAIA,CAAC,KAAK,EAAG;MACvCA,CAAC,GAAGH,CAAC,CAAEE,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;MAAEE,CAAC,IAAID,CAAC,GAAIA,CAAC,KAAK,CAAE;MACtCA,CAAC,GAAGH,CAAC,CAAEE,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;MAAEE,CAAC,IAAID,CAAC,GAAIA,CAAC,IAAI,CAAE;MACrCA,CAAC,GAAGH,CAAC,CAAEE,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;MAAEC,CAAC,GAAGA,CAAC,GAAIA,CAAC,IAAI,EAAG;MAAEC,CAAC,IAAID,CAAC,GAAIA,CAAC,IAAI,CAAE;MACxDH,CAAC,CAACE,CAAC,CAAC,GAAGE,CAAC;MACRN,EAAE,CAACI,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAC;MAClB,OAAOE,CAAC;IACV,CAAC;IAED,SAASE,IAAIA,CAACR,EAAE,EAAED,IAAI,EAAE;MACtB,IAAIU,CAAC;QAAEF,CAAC;QAAEL,CAAC,GAAG,EAAE;MAEhB,IAAIH,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;QACvB;QACAQ,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC,GAAGH,IAAI;MACjB,CAAC,MAAM;QACL;QACAA,IAAI,GAAG,EAAE,GAAGA,IAAI;QAChB,KAAKU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACW,MAAM,EAAE,EAAED,CAAC,EAAE;UAChCP,CAAC,CAACO,CAAC,GAAG,CAAC,CAAC,GAAIP,CAAC,CAACO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GACrBV,IAAI,CAACY,UAAU,CAACF,CAAC,CAAC,GAAGP,CAAC,CAAEO,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAI,EAAG;QACjD;MACF;MACA;MACA,OAAOP,CAAC,CAACQ,MAAM,GAAG,CAAC,EAAER,CAAC,CAACU,IAAI,CAAC,CAAC,CAAC;MAC9B,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIP,CAAC,CAACO,CAAC,CAAC,KAAK,CAAC,EAAE,EAAEA,CAAC,CAAC;MACrC,IAAIA,CAAC,IAAI,CAAC,EAAEF,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAMK,CAAC,GAAGL,CAAC,CAACO,CAAC,CAAC;MAExCT,EAAE,CAACG,CAAC,GAAGD,CAAC;MACRF,EAAE,CAACI,CAAC,GAAG,CAAC;;MAER;MACA,KAAKK,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QACxBT,EAAE,CAACC,IAAI,CAAC,CAAC;MACX;IACF;IAEAO,IAAI,CAACR,EAAE,EAAED,IAAI,CAAC;EAChB;EAEA,SAASc,IAAIA,CAACC,CAAC,EAAET,CAAC,EAAE;IAClBA,CAAC,CAACF,CAAC,GAAGW,CAAC,CAACX,CAAC,CAACY,KAAK,CAAC,CAAC;IACjBV,CAAC,CAACD,CAAC,GAAGU,CAAC,CAACV,CAAC;IACT,OAAOC,CAAC;EACV;EAEA,SAASW,IAAIA,CAACjB,IAAI,EAAEkB,IAAI,EAAE;IACxB,IAAIlB,IAAI,IAAI,IAAI,EAAEA,IAAI,GAAG,CAAE,IAAImB,IAAI,CAAD,CAAE;IACpC,IAAIC,EAAE,GAAG,IAAIrB,MAAM,CAACC,IAAI,CAAC;MACrBqB,KAAK,GAAGH,IAAI,IAAIA,IAAI,CAACG,KAAK;MAC1BC,IAAI,GAAG,SAAAA,CAAA,EAAW;QAAE,OAAO,CAACF,EAAE,CAAClB,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;MAAE,CAAC;IACjEoB,IAAI,CAACC,MAAM,GAAG,YAAW;MACvB,GAAG;QACD,IAAIC,GAAG,GAAGJ,EAAE,CAAClB,IAAI,CAAC,CAAC,KAAK,EAAE;UACtBuB,GAAG,GAAG,CAACL,EAAE,CAAClB,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;UACrCwB,MAAM,GAAG,CAACF,GAAG,GAAGC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;MACtC,CAAC,QAAQC,MAAM,KAAK,CAAC;MACrB,OAAOA,MAAM;IACf,CAAC;IACDJ,IAAI,CAACK,KAAK,GAAGP,EAAE,CAAClB,IAAI;IACpBoB,IAAI,CAACM,KAAK,GAAGN,IAAI;IACjB,IAAID,KAAK,EAAE;MACT,IAAIA,KAAK,CAACjB,CAAC,EAAEU,IAAI,CAACO,KAAK,EAAED,EAAE,CAAC;MAC5BE,IAAI,CAACD,KAAK,GAAG,YAAW;QAAE,OAAOP,IAAI,CAACM,EAAE,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC;IAClD;IACA,OAAOE,IAAI;EACb;EAEA,IAAIzB,MAAM,IAAIA,MAAM,CAACgC,OAAO,EAAE;IAC5BhC,MAAM,CAACgC,OAAO,GAAGZ,IAAI;EACvB,CAAC,MAAM,IAAInB,MAAM,IAAIA,MAAM,CAACgC,GAAG,EAAE;IAC/BhC,MAAM,CAAC,YAAW;MAAE,OAAOmB,IAAI;IAAE,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAI,CAACc,SAAS,GAAGd,IAAI;EACvB;AAEA,CAAC,EACC,IAAI,EACH,OAAOpB,MAAM,IAAK,QAAQ,IAAIA,MAAM;AAAK;AACzC,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAAG;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}