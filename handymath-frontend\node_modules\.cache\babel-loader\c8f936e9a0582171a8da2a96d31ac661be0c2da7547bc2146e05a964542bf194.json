{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPermutations } from '../../factoriesAny.js';\nexport var permutationsDependencies = {\n  factorialDependencies,\n  typedDependencies,\n  createPermutations\n};", "map": {"version": 3, "names": ["factorialDependencies", "typedDependencies", "createPermutations", "permutationsDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesPermutations.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPermutations } from '../../factoriesAny.js';\nexport var permutationsDependencies = {\n  factorialDependencies,\n  typedDependencies,\n  createPermutations\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCH,qBAAqB;EACrBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}