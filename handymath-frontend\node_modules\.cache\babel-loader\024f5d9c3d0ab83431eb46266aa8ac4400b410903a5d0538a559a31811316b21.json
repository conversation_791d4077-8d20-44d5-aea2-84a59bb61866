{"ast": null, "code": "import { isBigNumber, isObject } from './is.js';\n\n/**\n * Clone an object\n *\n *     clone(x)\n *\n * Can clone any primitive type, array, and object.\n * If x has a function clone, this function will be invoked to clone the object.\n *\n * @param {*} x\n * @return {*} clone\n */\nexport function clone(x) {\n  var type = typeof x;\n\n  // immutable primitive types\n  if (type === 'number' || type === 'bigint' || type === 'string' || type === 'boolean' || x === null || x === undefined) {\n    return x;\n  }\n\n  // use clone function of the object when available\n  if (typeof x.clone === 'function') {\n    return x.clone();\n  }\n\n  // array\n  if (Array.isArray(x)) {\n    return x.map(function (value) {\n      return clone(value);\n    });\n  }\n  if (x instanceof Date) return new Date(x.valueOf());\n  if (isBigNumber(x)) return x; // bignumbers are immutable\n\n  // object\n  if (isObject(x)) {\n    return mapObject(x, clone);\n  }\n  if (type === 'function') {\n    // we assume that the function is immutable\n    return x;\n  }\n  throw new TypeError(\"Cannot clone: unknown type of value (value: \".concat(x, \")\"));\n}\n\n/**\n * Apply map to all properties of an object\n * @param {Object} object\n * @param {function} callback\n * @return {Object} Returns a copy of the object with mapped properties\n */\nexport function mapObject(object, callback) {\n  var clone = {};\n  for (var key in object) {\n    if (hasOwnProperty(object, key)) {\n      clone[key] = callback(object[key]);\n    }\n  }\n  return clone;\n}\n\n/**\n * Extend object a with the properties of object b\n * @param {Object} a\n * @param {Object} b\n * @return {Object} a\n */\nexport function extend(a, b) {\n  for (var prop in b) {\n    if (hasOwnProperty(b, prop)) {\n      a[prop] = b[prop];\n    }\n  }\n  return a;\n}\n\n/**\n * Deep extend an object a with the properties of object b\n * @param {Object} a\n * @param {Object} b\n * @returns {Object}\n */\nexport function deepExtend(a, b) {\n  // TODO: add support for Arrays to deepExtend\n  if (Array.isArray(b)) {\n    throw new TypeError('Arrays are not supported by deepExtend');\n  }\n  for (var prop in b) {\n    // We check against prop not being in Object.prototype or Function.prototype\n    // to prevent polluting for example Object.__proto__.\n    if (hasOwnProperty(b, prop) && !(prop in Object.prototype) && !(prop in Function.prototype)) {\n      if (b[prop] && b[prop].constructor === Object) {\n        if (a[prop] === undefined) {\n          a[prop] = {};\n        }\n        if (a[prop] && a[prop].constructor === Object) {\n          deepExtend(a[prop], b[prop]);\n        } else {\n          a[prop] = b[prop];\n        }\n      } else if (Array.isArray(b[prop])) {\n        throw new TypeError('Arrays are not supported by deepExtend');\n      } else {\n        a[prop] = b[prop];\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Deep test equality of all fields in two pairs of arrays or objects.\n * Compares values and functions strictly (ie. 2 is not the same as '2').\n * @param {Array | Object} a\n * @param {Array | Object} b\n * @returns {boolean}\n */\nexport function deepStrictEqual(a, b) {\n  var prop, i, len;\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b)) {\n      return false;\n    }\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0, len = a.length; i < len; i++) {\n      if (!deepStrictEqual(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  } else if (typeof a === 'function') {\n    return a === b;\n  } else if (a instanceof Object) {\n    if (Array.isArray(b) || !(b instanceof Object)) {\n      return false;\n    }\n    for (prop in a) {\n      // noinspection JSUnfilteredForInLoop\n      if (!(prop in b) || !deepStrictEqual(a[prop], b[prop])) {\n        return false;\n      }\n    }\n    for (prop in b) {\n      // noinspection JSUnfilteredForInLoop\n      if (!(prop in a)) {\n        return false;\n      }\n    }\n    return true;\n  } else {\n    return a === b;\n  }\n}\n\n/**\n * Recursively flatten a nested object.\n * @param {Object} nestedObject\n * @return {Object} Returns the flattened object\n */\nexport function deepFlatten(nestedObject) {\n  var flattenedObject = {};\n  _deepFlatten(nestedObject, flattenedObject);\n  return flattenedObject;\n}\n\n// helper function used by deepFlatten\nfunction _deepFlatten(nestedObject, flattenedObject) {\n  for (var prop in nestedObject) {\n    if (hasOwnProperty(nestedObject, prop)) {\n      var value = nestedObject[prop];\n      if (typeof value === 'object' && value !== null) {\n        _deepFlatten(value, flattenedObject);\n      } else {\n        flattenedObject[prop] = value;\n      }\n    }\n  }\n}\n\n/**\n * Test whether the current JavaScript engine supports Object.defineProperty\n * @returns {boolean} returns true if supported\n */\nexport function canDefineProperty() {\n  // test needed for broken IE8 implementation\n  try {\n    if (Object.defineProperty) {\n      Object.defineProperty({}, 'x', {\n        get: function get() {\n          return null;\n        }\n      });\n      return true;\n    }\n  } catch (e) {}\n  return false;\n}\n\n/**\n * Attach a lazy loading property to a constant.\n * The given function `fn` is called once when the property is first requested.\n *\n * @param {Object} object         Object where to add the property\n * @param {string} prop           Property name\n * @param {Function} valueResolver Function returning the property value. Called\n *                                without arguments.\n */\nexport function lazy(object, prop, valueResolver) {\n  var _uninitialized = true;\n  var _value;\n  Object.defineProperty(object, prop, {\n    get: function get() {\n      if (_uninitialized) {\n        _value = valueResolver();\n        _uninitialized = false;\n      }\n      return _value;\n    },\n    set: function set(value) {\n      _value = value;\n      _uninitialized = false;\n    },\n    configurable: true,\n    enumerable: true\n  });\n}\n\n/**\n * Traverse a path into an object.\n * When a namespace is missing, it will be created\n * @param {Object} object\n * @param {string | string[]} path   A dot separated string like 'name.space'\n * @return {Object} Returns the object at the end of the path\n */\nexport function traverse(object, path) {\n  if (path && typeof path === 'string') {\n    return traverse(object, path.split('.'));\n  }\n  var obj = object;\n  if (path) {\n    for (var i = 0; i < path.length; i++) {\n      var key = path[i];\n      if (!(key in obj)) {\n        obj[key] = {};\n      }\n      obj = obj[key];\n    }\n  }\n  return obj;\n}\n\n/**\n * A safe hasOwnProperty\n * @param {Object} object\n * @param {string} property\n */\nexport function hasOwnProperty(object, property) {\n  return object && Object.hasOwnProperty.call(object, property);\n}\n\n/**\n * Test whether an object is a factory. a factory has fields:\n *\n * - factory: function (type: Object, config: Object, load: function, typed: function [, math: Object])   (required)\n * - name: string (optional)\n * - path: string    A dot separated path (optional)\n * - math: boolean   If true (false by default), the math namespace is passed\n *                   as fifth argument of the factory function\n *\n * @param {*} object\n * @returns {boolean}\n */\nexport function isLegacyFactory(object) {\n  return object && typeof object.factory === 'function';\n}\n\n/**\n * Get a nested property from an object\n * @param {Object} object\n * @param {string | string[]} path\n * @returns {Object}\n */\nexport function get(object, path) {\n  if (typeof path === 'string') {\n    if (isPath(path)) {\n      return get(object, path.split('.'));\n    } else {\n      return object[path];\n    }\n  }\n  var child = object;\n  for (var i = 0; i < path.length; i++) {\n    var key = path[i];\n    child = child ? child[key] : undefined;\n  }\n  return child;\n}\n\n/**\n * Set a nested property in an object\n * Mutates the object itself\n * If the path doesn't exist, it will be created\n * @param {Object} object\n * @param {string | string[]} path\n * @param {*} value\n * @returns {Object}\n */\nexport function set(object, path, value) {\n  if (typeof path === 'string') {\n    if (isPath(path)) {\n      return set(object, path.split('.'), value);\n    } else {\n      object[path] = value;\n      return object;\n    }\n  }\n  var child = object;\n  for (var i = 0; i < path.length - 1; i++) {\n    var key = path[i];\n    if (child[key] === undefined) {\n      child[key] = {};\n    }\n    child = child[key];\n  }\n  if (path.length > 0) {\n    var lastKey = path[path.length - 1];\n    child[lastKey] = value;\n  }\n  return object;\n}\n\n/**\n * Create an object composed of the picked object properties\n * @param {Object} object\n * @param {string[]} properties\n * @param {function} [transform] Optional value to transform a value when picking it\n * @return {Object}\n */\nexport function pick(object, properties, transform) {\n  var copy = {};\n  for (var i = 0; i < properties.length; i++) {\n    var key = properties[i];\n    var value = get(object, key);\n    if (value !== undefined) {\n      set(copy, key, transform ? transform(value, key) : value);\n    }\n  }\n  return copy;\n}\n\n/**\n * Shallow version of pick, creating an object composed of the picked object properties\n * but not for nested properties\n * @param {Object} object\n * @param {string[]} properties\n * @return {Object}\n */\nexport function pickShallow(object, properties) {\n  var copy = {};\n  for (var i = 0; i < properties.length; i++) {\n    var key = properties[i];\n    var value = object[key];\n    if (value !== undefined) {\n      copy[key] = value;\n    }\n  }\n  return copy;\n}\n\n// helper function to test whether a string contains a path like 'user.name'\nfunction isPath(str) {\n  return str.includes('.');\n}", "map": {"version": 3, "names": ["isBigNumber", "isObject", "clone", "x", "type", "undefined", "Array", "isArray", "map", "value", "Date", "valueOf", "mapObject", "TypeError", "concat", "object", "callback", "key", "hasOwnProperty", "extend", "a", "b", "prop", "deepExtend", "Object", "prototype", "Function", "constructor", "deepStrictEqual", "i", "len", "length", "deepFlatten", "nestedObject", "flattenedObject", "_deepFlatten", "canDefineProperty", "defineProperty", "get", "e", "lazy", "valueResolver", "_uninitialized", "_value", "set", "configurable", "enumerable", "traverse", "path", "split", "obj", "property", "call", "isLegacyFactory", "factory", "isPath", "child", "last<PERSON>ey", "pick", "properties", "transform", "copy", "pickShallow", "str", "includes"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/object.js"], "sourcesContent": ["import { isBigNumber, isObject } from './is.js';\n\n/**\n * Clone an object\n *\n *     clone(x)\n *\n * Can clone any primitive type, array, and object.\n * If x has a function clone, this function will be invoked to clone the object.\n *\n * @param {*} x\n * @return {*} clone\n */\nexport function clone(x) {\n  var type = typeof x;\n\n  // immutable primitive types\n  if (type === 'number' || type === 'bigint' || type === 'string' || type === 'boolean' || x === null || x === undefined) {\n    return x;\n  }\n\n  // use clone function of the object when available\n  if (typeof x.clone === 'function') {\n    return x.clone();\n  }\n\n  // array\n  if (Array.isArray(x)) {\n    return x.map(function (value) {\n      return clone(value);\n    });\n  }\n  if (x instanceof Date) return new Date(x.valueOf());\n  if (isBigNumber(x)) return x; // bignumbers are immutable\n\n  // object\n  if (isObject(x)) {\n    return mapObject(x, clone);\n  }\n  if (type === 'function') {\n    // we assume that the function is immutable\n    return x;\n  }\n  throw new TypeError(\"Cannot clone: unknown type of value (value: \".concat(x, \")\"));\n}\n\n/**\n * Apply map to all properties of an object\n * @param {Object} object\n * @param {function} callback\n * @return {Object} Returns a copy of the object with mapped properties\n */\nexport function mapObject(object, callback) {\n  var clone = {};\n  for (var key in object) {\n    if (hasOwnProperty(object, key)) {\n      clone[key] = callback(object[key]);\n    }\n  }\n  return clone;\n}\n\n/**\n * Extend object a with the properties of object b\n * @param {Object} a\n * @param {Object} b\n * @return {Object} a\n */\nexport function extend(a, b) {\n  for (var prop in b) {\n    if (hasOwnProperty(b, prop)) {\n      a[prop] = b[prop];\n    }\n  }\n  return a;\n}\n\n/**\n * Deep extend an object a with the properties of object b\n * @param {Object} a\n * @param {Object} b\n * @returns {Object}\n */\nexport function deepExtend(a, b) {\n  // TODO: add support for Arrays to deepExtend\n  if (Array.isArray(b)) {\n    throw new TypeError('Arrays are not supported by deepExtend');\n  }\n  for (var prop in b) {\n    // We check against prop not being in Object.prototype or Function.prototype\n    // to prevent polluting for example Object.__proto__.\n    if (hasOwnProperty(b, prop) && !(prop in Object.prototype) && !(prop in Function.prototype)) {\n      if (b[prop] && b[prop].constructor === Object) {\n        if (a[prop] === undefined) {\n          a[prop] = {};\n        }\n        if (a[prop] && a[prop].constructor === Object) {\n          deepExtend(a[prop], b[prop]);\n        } else {\n          a[prop] = b[prop];\n        }\n      } else if (Array.isArray(b[prop])) {\n        throw new TypeError('Arrays are not supported by deepExtend');\n      } else {\n        a[prop] = b[prop];\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Deep test equality of all fields in two pairs of arrays or objects.\n * Compares values and functions strictly (ie. 2 is not the same as '2').\n * @param {Array | Object} a\n * @param {Array | Object} b\n * @returns {boolean}\n */\nexport function deepStrictEqual(a, b) {\n  var prop, i, len;\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b)) {\n      return false;\n    }\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0, len = a.length; i < len; i++) {\n      if (!deepStrictEqual(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  } else if (typeof a === 'function') {\n    return a === b;\n  } else if (a instanceof Object) {\n    if (Array.isArray(b) || !(b instanceof Object)) {\n      return false;\n    }\n    for (prop in a) {\n      // noinspection JSUnfilteredForInLoop\n      if (!(prop in b) || !deepStrictEqual(a[prop], b[prop])) {\n        return false;\n      }\n    }\n    for (prop in b) {\n      // noinspection JSUnfilteredForInLoop\n      if (!(prop in a)) {\n        return false;\n      }\n    }\n    return true;\n  } else {\n    return a === b;\n  }\n}\n\n/**\n * Recursively flatten a nested object.\n * @param {Object} nestedObject\n * @return {Object} Returns the flattened object\n */\nexport function deepFlatten(nestedObject) {\n  var flattenedObject = {};\n  _deepFlatten(nestedObject, flattenedObject);\n  return flattenedObject;\n}\n\n// helper function used by deepFlatten\nfunction _deepFlatten(nestedObject, flattenedObject) {\n  for (var prop in nestedObject) {\n    if (hasOwnProperty(nestedObject, prop)) {\n      var value = nestedObject[prop];\n      if (typeof value === 'object' && value !== null) {\n        _deepFlatten(value, flattenedObject);\n      } else {\n        flattenedObject[prop] = value;\n      }\n    }\n  }\n}\n\n/**\n * Test whether the current JavaScript engine supports Object.defineProperty\n * @returns {boolean} returns true if supported\n */\nexport function canDefineProperty() {\n  // test needed for broken IE8 implementation\n  try {\n    if (Object.defineProperty) {\n      Object.defineProperty({}, 'x', {\n        get: function get() {\n          return null;\n        }\n      });\n      return true;\n    }\n  } catch (e) {}\n  return false;\n}\n\n/**\n * Attach a lazy loading property to a constant.\n * The given function `fn` is called once when the property is first requested.\n *\n * @param {Object} object         Object where to add the property\n * @param {string} prop           Property name\n * @param {Function} valueResolver Function returning the property value. Called\n *                                without arguments.\n */\nexport function lazy(object, prop, valueResolver) {\n  var _uninitialized = true;\n  var _value;\n  Object.defineProperty(object, prop, {\n    get: function get() {\n      if (_uninitialized) {\n        _value = valueResolver();\n        _uninitialized = false;\n      }\n      return _value;\n    },\n    set: function set(value) {\n      _value = value;\n      _uninitialized = false;\n    },\n    configurable: true,\n    enumerable: true\n  });\n}\n\n/**\n * Traverse a path into an object.\n * When a namespace is missing, it will be created\n * @param {Object} object\n * @param {string | string[]} path   A dot separated string like 'name.space'\n * @return {Object} Returns the object at the end of the path\n */\nexport function traverse(object, path) {\n  if (path && typeof path === 'string') {\n    return traverse(object, path.split('.'));\n  }\n  var obj = object;\n  if (path) {\n    for (var i = 0; i < path.length; i++) {\n      var key = path[i];\n      if (!(key in obj)) {\n        obj[key] = {};\n      }\n      obj = obj[key];\n    }\n  }\n  return obj;\n}\n\n/**\n * A safe hasOwnProperty\n * @param {Object} object\n * @param {string} property\n */\nexport function hasOwnProperty(object, property) {\n  return object && Object.hasOwnProperty.call(object, property);\n}\n\n/**\n * Test whether an object is a factory. a factory has fields:\n *\n * - factory: function (type: Object, config: Object, load: function, typed: function [, math: Object])   (required)\n * - name: string (optional)\n * - path: string    A dot separated path (optional)\n * - math: boolean   If true (false by default), the math namespace is passed\n *                   as fifth argument of the factory function\n *\n * @param {*} object\n * @returns {boolean}\n */\nexport function isLegacyFactory(object) {\n  return object && typeof object.factory === 'function';\n}\n\n/**\n * Get a nested property from an object\n * @param {Object} object\n * @param {string | string[]} path\n * @returns {Object}\n */\nexport function get(object, path) {\n  if (typeof path === 'string') {\n    if (isPath(path)) {\n      return get(object, path.split('.'));\n    } else {\n      return object[path];\n    }\n  }\n  var child = object;\n  for (var i = 0; i < path.length; i++) {\n    var key = path[i];\n    child = child ? child[key] : undefined;\n  }\n  return child;\n}\n\n/**\n * Set a nested property in an object\n * Mutates the object itself\n * If the path doesn't exist, it will be created\n * @param {Object} object\n * @param {string | string[]} path\n * @param {*} value\n * @returns {Object}\n */\nexport function set(object, path, value) {\n  if (typeof path === 'string') {\n    if (isPath(path)) {\n      return set(object, path.split('.'), value);\n    } else {\n      object[path] = value;\n      return object;\n    }\n  }\n  var child = object;\n  for (var i = 0; i < path.length - 1; i++) {\n    var key = path[i];\n    if (child[key] === undefined) {\n      child[key] = {};\n    }\n    child = child[key];\n  }\n  if (path.length > 0) {\n    var lastKey = path[path.length - 1];\n    child[lastKey] = value;\n  }\n  return object;\n}\n\n/**\n * Create an object composed of the picked object properties\n * @param {Object} object\n * @param {string[]} properties\n * @param {function} [transform] Optional value to transform a value when picking it\n * @return {Object}\n */\nexport function pick(object, properties, transform) {\n  var copy = {};\n  for (var i = 0; i < properties.length; i++) {\n    var key = properties[i];\n    var value = get(object, key);\n    if (value !== undefined) {\n      set(copy, key, transform ? transform(value, key) : value);\n    }\n  }\n  return copy;\n}\n\n/**\n * Shallow version of pick, creating an object composed of the picked object properties\n * but not for nested properties\n * @param {Object} object\n * @param {string[]} properties\n * @return {Object}\n */\nexport function pickShallow(object, properties) {\n  var copy = {};\n  for (var i = 0; i < properties.length; i++) {\n    var key = properties[i];\n    var value = object[key];\n    if (value !== undefined) {\n      copy[key] = value;\n    }\n  }\n  return copy;\n}\n\n// helper function to test whether a string contains a path like 'user.name'\nfunction isPath(str) {\n  return str.includes('.');\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,SAAS;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,CAAC,EAAE;EACvB,IAAIC,IAAI,GAAG,OAAOD,CAAC;;EAEnB;EACA,IAAIC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,SAAS,IAAID,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKE,SAAS,EAAE;IACtH,OAAOF,CAAC;EACV;;EAEA;EACA,IAAI,OAAOA,CAAC,CAACD,KAAK,KAAK,UAAU,EAAE;IACjC,OAAOC,CAAC,CAACD,KAAK,CAAC,CAAC;EAClB;;EAEA;EACA,IAAII,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;MAC5B,OAAOP,KAAK,CAACO,KAAK,CAAC;IACrB,CAAC,CAAC;EACJ;EACA,IAAIN,CAAC,YAAYO,IAAI,EAAE,OAAO,IAAIA,IAAI,CAACP,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC;EACnD,IAAIX,WAAW,CAACG,CAAC,CAAC,EAAE,OAAOA,CAAC,CAAC,CAAC;;EAE9B;EACA,IAAIF,QAAQ,CAACE,CAAC,CAAC,EAAE;IACf,OAAOS,SAAS,CAACT,CAAC,EAAED,KAAK,CAAC;EAC5B;EACA,IAAIE,IAAI,KAAK,UAAU,EAAE;IACvB;IACA,OAAOD,CAAC;EACV;EACA,MAAM,IAAIU,SAAS,CAAC,8CAA8C,CAACC,MAAM,CAACX,CAAC,EAAE,GAAG,CAAC,CAAC;AACpF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,SAASA,CAACG,MAAM,EAAEC,QAAQ,EAAE;EAC1C,IAAId,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,IAAIe,GAAG,IAAIF,MAAM,EAAE;IACtB,IAAIG,cAAc,CAACH,MAAM,EAAEE,GAAG,CAAC,EAAE;MAC/Bf,KAAK,CAACe,GAAG,CAAC,GAAGD,QAAQ,CAACD,MAAM,CAACE,GAAG,CAAC,CAAC;IACpC;EACF;EACA,OAAOf,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3B,KAAK,IAAIC,IAAI,IAAID,CAAC,EAAE;IAClB,IAAIH,cAAc,CAACG,CAAC,EAAEC,IAAI,CAAC,EAAE;MAC3BF,CAAC,CAACE,IAAI,CAAC,GAAGD,CAAC,CAACC,IAAI,CAAC;IACnB;EACF;EACA,OAAOF,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,UAAUA,CAACH,CAAC,EAAEC,CAAC,EAAE;EAC/B;EACA,IAAIf,KAAK,CAACC,OAAO,CAACc,CAAC,CAAC,EAAE;IACpB,MAAM,IAAIR,SAAS,CAAC,wCAAwC,CAAC;EAC/D;EACA,KAAK,IAAIS,IAAI,IAAID,CAAC,EAAE;IAClB;IACA;IACA,IAAIH,cAAc,CAACG,CAAC,EAAEC,IAAI,CAAC,IAAI,EAAEA,IAAI,IAAIE,MAAM,CAACC,SAAS,CAAC,IAAI,EAAEH,IAAI,IAAII,QAAQ,CAACD,SAAS,CAAC,EAAE;MAC3F,IAAIJ,CAAC,CAACC,IAAI,CAAC,IAAID,CAAC,CAACC,IAAI,CAAC,CAACK,WAAW,KAAKH,MAAM,EAAE;QAC7C,IAAIJ,CAAC,CAACE,IAAI,CAAC,KAAKjB,SAAS,EAAE;UACzBe,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;QACd;QACA,IAAIF,CAAC,CAACE,IAAI,CAAC,IAAIF,CAAC,CAACE,IAAI,CAAC,CAACK,WAAW,KAAKH,MAAM,EAAE;UAC7CD,UAAU,CAACH,CAAC,CAACE,IAAI,CAAC,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;QAC9B,CAAC,MAAM;UACLF,CAAC,CAACE,IAAI,CAAC,GAAGD,CAAC,CAACC,IAAI,CAAC;QACnB;MACF,CAAC,MAAM,IAAIhB,KAAK,CAACC,OAAO,CAACc,CAAC,CAACC,IAAI,CAAC,CAAC,EAAE;QACjC,MAAM,IAAIT,SAAS,CAAC,wCAAwC,CAAC;MAC/D,CAAC,MAAM;QACLO,CAAC,CAACE,IAAI,CAAC,GAAGD,CAAC,CAACC,IAAI,CAAC;MACnB;IACF;EACF;EACA,OAAOF,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,eAAeA,CAACR,CAAC,EAAEC,CAAC,EAAE;EACpC,IAAIC,IAAI,EAAEO,CAAC,EAAEC,GAAG;EAChB,IAAIxB,KAAK,CAACC,OAAO,CAACa,CAAC,CAAC,EAAE;IACpB,IAAI,CAACd,KAAK,CAACC,OAAO,CAACc,CAAC,CAAC,EAAE;MACrB,OAAO,KAAK;IACd;IACA,IAAID,CAAC,CAACW,MAAM,KAAKV,CAAC,CAACU,MAAM,EAAE;MACzB,OAAO,KAAK;IACd;IACA,KAAKF,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGV,CAAC,CAACW,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxC,IAAI,CAACD,eAAe,CAACR,CAAC,CAACS,CAAC,CAAC,EAAER,CAAC,CAACQ,CAAC,CAAC,CAAC,EAAE;QAChC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC,MAAM,IAAI,OAAOT,CAAC,KAAK,UAAU,EAAE;IAClC,OAAOA,CAAC,KAAKC,CAAC;EAChB,CAAC,MAAM,IAAID,CAAC,YAAYI,MAAM,EAAE;IAC9B,IAAIlB,KAAK,CAACC,OAAO,CAACc,CAAC,CAAC,IAAI,EAAEA,CAAC,YAAYG,MAAM,CAAC,EAAE;MAC9C,OAAO,KAAK;IACd;IACA,KAAKF,IAAI,IAAIF,CAAC,EAAE;MACd;MACA,IAAI,EAAEE,IAAI,IAAID,CAAC,CAAC,IAAI,CAACO,eAAe,CAACR,CAAC,CAACE,IAAI,CAAC,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC,EAAE;QACtD,OAAO,KAAK;MACd;IACF;IACA,KAAKA,IAAI,IAAID,CAAC,EAAE;MACd;MACA,IAAI,EAAEC,IAAI,IAAIF,CAAC,CAAC,EAAE;QAChB,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,CAAC,KAAKC,CAAC;EAChB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,WAAWA,CAACC,YAAY,EAAE;EACxC,IAAIC,eAAe,GAAG,CAAC,CAAC;EACxBC,YAAY,CAACF,YAAY,EAAEC,eAAe,CAAC;EAC3C,OAAOA,eAAe;AACxB;;AAEA;AACA,SAASC,YAAYA,CAACF,YAAY,EAAEC,eAAe,EAAE;EACnD,KAAK,IAAIZ,IAAI,IAAIW,YAAY,EAAE;IAC7B,IAAIf,cAAc,CAACe,YAAY,EAAEX,IAAI,CAAC,EAAE;MACtC,IAAIb,KAAK,GAAGwB,YAAY,CAACX,IAAI,CAAC;MAC9B,IAAI,OAAOb,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;QAC/C0B,YAAY,CAAC1B,KAAK,EAAEyB,eAAe,CAAC;MACtC,CAAC,MAAM;QACLA,eAAe,CAACZ,IAAI,CAAC,GAAGb,KAAK;MAC/B;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAAS2B,iBAAiBA,CAAA,EAAG;EAClC;EACA,IAAI;IACF,IAAIZ,MAAM,CAACa,cAAc,EAAE;MACzBb,MAAM,CAACa,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;QAC7BC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;UAClB,OAAO,IAAI;QACb;MACF,CAAC,CAAC;MACF,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;EACb,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACzB,MAAM,EAAEO,IAAI,EAAEmB,aAAa,EAAE;EAChD,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIC,MAAM;EACVnB,MAAM,CAACa,cAAc,CAACtB,MAAM,EAAEO,IAAI,EAAE;IAClCgB,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,IAAII,cAAc,EAAE;QAClBC,MAAM,GAAGF,aAAa,CAAC,CAAC;QACxBC,cAAc,GAAG,KAAK;MACxB;MACA,OAAOC,MAAM;IACf,CAAC;IACDC,GAAG,EAAE,SAASA,GAAGA,CAACnC,KAAK,EAAE;MACvBkC,MAAM,GAAGlC,KAAK;MACdiC,cAAc,GAAG,KAAK;IACxB,CAAC;IACDG,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAAChC,MAAM,EAAEiC,IAAI,EAAE;EACrC,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACpC,OAAOD,QAAQ,CAAChC,MAAM,EAAEiC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC1C;EACA,IAAIC,GAAG,GAAGnC,MAAM;EAChB,IAAIiC,IAAI,EAAE;IACR,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,IAAI,CAACjB,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpC,IAAIZ,GAAG,GAAG+B,IAAI,CAACnB,CAAC,CAAC;MACjB,IAAI,EAAEZ,GAAG,IAAIiC,GAAG,CAAC,EAAE;QACjBA,GAAG,CAACjC,GAAG,CAAC,GAAG,CAAC,CAAC;MACf;MACAiC,GAAG,GAAGA,GAAG,CAACjC,GAAG,CAAC;IAChB;EACF;EACA,OAAOiC,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAShC,cAAcA,CAACH,MAAM,EAAEoC,QAAQ,EAAE;EAC/C,OAAOpC,MAAM,IAAIS,MAAM,CAACN,cAAc,CAACkC,IAAI,CAACrC,MAAM,EAAEoC,QAAQ,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,eAAeA,CAACtC,MAAM,EAAE;EACtC,OAAOA,MAAM,IAAI,OAAOA,MAAM,CAACuC,OAAO,KAAK,UAAU;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAShB,GAAGA,CAACvB,MAAM,EAAEiC,IAAI,EAAE;EAChC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAIO,MAAM,CAACP,IAAI,CAAC,EAAE;MAChB,OAAOV,GAAG,CAACvB,MAAM,EAAEiC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,OAAOlC,MAAM,CAACiC,IAAI,CAAC;IACrB;EACF;EACA,IAAIQ,KAAK,GAAGzC,MAAM;EAClB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,IAAI,CAACjB,MAAM,EAAEF,CAAC,EAAE,EAAE;IACpC,IAAIZ,GAAG,GAAG+B,IAAI,CAACnB,CAAC,CAAC;IACjB2B,KAAK,GAAGA,KAAK,GAAGA,KAAK,CAACvC,GAAG,CAAC,GAAGZ,SAAS;EACxC;EACA,OAAOmD,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASZ,GAAGA,CAAC7B,MAAM,EAAEiC,IAAI,EAAEvC,KAAK,EAAE;EACvC,IAAI,OAAOuC,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAIO,MAAM,CAACP,IAAI,CAAC,EAAE;MAChB,OAAOJ,GAAG,CAAC7B,MAAM,EAAEiC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,EAAExC,KAAK,CAAC;IAC5C,CAAC,MAAM;MACLM,MAAM,CAACiC,IAAI,CAAC,GAAGvC,KAAK;MACpB,OAAOM,MAAM;IACf;EACF;EACA,IAAIyC,KAAK,GAAGzC,MAAM;EAClB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAEF,CAAC,EAAE,EAAE;IACxC,IAAIZ,GAAG,GAAG+B,IAAI,CAACnB,CAAC,CAAC;IACjB,IAAI2B,KAAK,CAACvC,GAAG,CAAC,KAAKZ,SAAS,EAAE;MAC5BmD,KAAK,CAACvC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB;IACAuC,KAAK,GAAGA,KAAK,CAACvC,GAAG,CAAC;EACpB;EACA,IAAI+B,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAE;IACnB,IAAI0B,OAAO,GAAGT,IAAI,CAACA,IAAI,CAACjB,MAAM,GAAG,CAAC,CAAC;IACnCyB,KAAK,CAACC,OAAO,CAAC,GAAGhD,KAAK;EACxB;EACA,OAAOM,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2C,IAAIA,CAAC3C,MAAM,EAAE4C,UAAU,EAAEC,SAAS,EAAE;EAClD,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,UAAU,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAC1C,IAAIZ,GAAG,GAAG0C,UAAU,CAAC9B,CAAC,CAAC;IACvB,IAAIpB,KAAK,GAAG6B,GAAG,CAACvB,MAAM,EAAEE,GAAG,CAAC;IAC5B,IAAIR,KAAK,KAAKJ,SAAS,EAAE;MACvBuC,GAAG,CAACiB,IAAI,EAAE5C,GAAG,EAAE2C,SAAS,GAAGA,SAAS,CAACnD,KAAK,EAAEQ,GAAG,CAAC,GAAGR,KAAK,CAAC;IAC3D;EACF;EACA,OAAOoD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAC/C,MAAM,EAAE4C,UAAU,EAAE;EAC9C,IAAIE,IAAI,GAAG,CAAC,CAAC;EACb,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,UAAU,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAC1C,IAAIZ,GAAG,GAAG0C,UAAU,CAAC9B,CAAC,CAAC;IACvB,IAAIpB,KAAK,GAAGM,MAAM,CAACE,GAAG,CAAC;IACvB,IAAIR,KAAK,KAAKJ,SAAS,EAAE;MACvBwD,IAAI,CAAC5C,GAAG,CAAC,GAAGR,KAAK;IACnB;EACF;EACA,OAAOoD,IAAI;AACb;;AAEA;AACA,SAASN,MAAMA,CAACQ,GAAG,EAAE;EACnB,OAAOA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}