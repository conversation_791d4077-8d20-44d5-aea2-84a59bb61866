{"ast": null, "code": "export var sumDocs = {\n  name: 'sum',\n  category: 'Statistics',\n  syntax: ['sum(a, b, c, ...)', 'sum(A)', 'sum(A, dimension)'],\n  description: 'Compute the sum of all values.',\n  examples: ['sum(2, 3, 4, 1)', 'sum([2, 3, 4, 1])', 'sum([2, 5; 4, 3])'],\n  seealso: ['max', 'mean', 'median', 'min', 'prod', 'std', 'variance']\n};", "map": {"version": 3, "names": ["sumDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/sum.js"], "sourcesContent": ["export var sumDocs = {\n  name: 'sum',\n  category: 'Statistics',\n  syntax: ['sum(a, b, c, ...)', 'sum(A)', 'sum(A, dimension)'],\n  description: 'Compute the sum of all values.',\n  examples: ['sum(2, 3, 4, 1)', 'sum([2, 3, 4, 1])', 'sum([2, 5; 4, 3])'],\n  seealso: ['max', 'mean', 'median', 'min', 'prod', 'std', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EAC5DC,WAAW,EAAE,gCAAgC;EAC7CC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;EACvEC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}