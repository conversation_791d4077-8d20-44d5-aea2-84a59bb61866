# Generated by Django 5.2.2 on 2025-06-05 01:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0008_add_structured_courses'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nom')),
                ('email', models.EmailField(max_length=254, verbose_name='Email')),
                ('category', models.CharField(choices=[('question', 'Question générale'), ('bug', 'Signaler un bug'), ('feature', 'Demande de fonctionnalité'), ('help', 'Aide technique'), ('other', 'Autre')], default='question', max_length=20, verbose_name='Catégorie')),
                ('subject', models.CharField(max_length=200, verbose_name='Sujet')),
                ('message', models.TextField(verbose_name='Message')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Dernière modification')),
                ('status', models.CharField(choices=[('new', 'Nouveau'), ('in_progress', 'En cours'), ('resolved', 'Résolu'), ('closed', 'Fermé')], default='new', max_length=20, verbose_name='Statut')),
                ('priority', models.CharField(choices=[('low', 'Basse'), ('medium', 'Moyenne'), ('high', 'Haute'), ('urgent', 'Urgente')], default='medium', max_length=10, verbose_name='Priorité')),
                ('admin_notes', models.TextField(blank=True, verbose_name='Notes administrateur')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='Date de résolution')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Adresse IP')),
                ('assigned_to', models.ForeignKey(blank=True, limit_choices_to={'is_staff': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Assigné à')),
            ],
            options={
                'verbose_name': 'Message de Contact',
                'verbose_name_plural': 'Messages de Contact',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status', 'created_at'], name='api_contact_status_629118_idx'), models.Index(fields=['category', 'priority'], name='api_contact_categor_ae15e4_idx')],
            },
        ),
    ]
