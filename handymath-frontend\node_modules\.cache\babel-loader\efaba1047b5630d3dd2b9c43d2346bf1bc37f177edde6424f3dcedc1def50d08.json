{"ast": null, "code": "import { number } from '../../../value/types/numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from '../../../value/types/numbers/units.mjs';\nimport { testValueType } from './test.mjs';\nimport { auto } from './type-auto.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = v => dimensionValueTypes.find(testValueType(v));\nexport { dimensionValueTypes, findDimensionValueType };", "map": {"version": 3, "names": ["number", "px", "percent", "degrees", "vw", "vh", "testValueType", "auto", "dimensionValueTypes", "findDimensionValueType", "v", "find"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/framer-motion/dist/es/render/dom/value-types/dimensions.mjs"], "sourcesContent": ["import { number } from '../../../value/types/numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from '../../../value/types/numbers/units.mjs';\nimport { testValueType } from './test.mjs';\nimport { auto } from './type-auto.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = (v) => dimensionValueTypes.find(testValueType(v));\n\nexport { dimensionValueTypes, findDimensionValueType };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,wCAAwC;AAC/D,SAASC,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,QAAQ,wCAAwC;AACrF,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,IAAI,QAAQ,iBAAiB;;AAEtC;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,CAACR,MAAM,EAAEC,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEE,IAAI,CAAC;AACxE;AACA;AACA;AACA,MAAME,sBAAsB,GAAIC,CAAC,IAAKF,mBAAmB,CAACG,IAAI,CAACL,aAAa,CAACI,CAAC,CAAC,CAAC;AAEhF,SAASF,mBAAmB,EAAEC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}