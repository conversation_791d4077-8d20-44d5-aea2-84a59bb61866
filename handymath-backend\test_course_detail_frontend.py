#!/usr/bin/env python3
"""
Test spécifique pour reproduire le problème "cours non trouvé" du frontend
"""

import os
import sys
import django
import requests

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from api.models import User

def test_course_detail_simulation():
    """Simuler exactement ce que fait le frontend CourseDetailPage"""
    print("🧪 SIMULATION: CourseDetailPage Frontend")
    print("=" * 50)
    
    # 1. Obtenir un token comme le ferait le frontend
    print("1️⃣ AUTHENTIFICATION")
    print("-" * 20)
    
    login_url = 'http://127.0.0.1:8000/api/token/'
    login_data = {
        'username': 'frontend_test_user',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get('access')
            print(f"✅ Token obtenu: {token[:30]}...")
        else:
            print(f"❌ Erreur d'authentification: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return
    
    # 2. Tester l'appel exact du frontend pour chaque cours
    print(f"\n2️⃣ TEST DES APPELS COURS")
    print("-" * 30)
    
    # Récupérer la liste des cours d'abord
    headers = {'Authorization': f'Bearer {token}'}
    courses_response = requests.get('http://127.0.0.1:8000/api/courses/', headers=headers)
    
    if courses_response.status_code == 200:
        courses_data = courses_response.json()
        courses = courses_data.get('courses', [])
        print(f"📚 {len(courses)} cours disponibles")
        
        # Tester chaque cours individuellement
        for course in courses:
            course_id = course['id']
            course_title = course['title']
            
            print(f"\n📖 Test du cours: {course_title} (ID: {course_id})")
            
            # Appel exact comme dans CourseDetailPage.tsx
            detail_url = f'http://127.0.0.1:8000/api/courses/{course_id}/'
            
            try:
                detail_response = requests.get(detail_url, headers=headers)
                print(f"   📡 GET {detail_url}")
                print(f"   📊 Status: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    course_detail = detail_response.json()
                    print(f"   ✅ Succès: {course_detail['title']}")
                    print(f"   📈 Progression: {course_detail.get('progress_percentage', 0)}%")
                    print(f"   📚 Chapitres: {len(course_detail.get('chapters', []))}")
                    
                    # Vérifier la structure des données
                    required_fields = ['id', 'title', 'description', 'chapters']
                    missing_fields = [field for field in required_fields if field not in course_detail]
                    
                    if missing_fields:
                        print(f"   ⚠️ Champs manquants: {missing_fields}")
                    else:
                        print(f"   ✅ Structure complète")
                        
                elif detail_response.status_code == 404:
                    print(f"   ❌ Cours non trouvé (404)")
                elif detail_response.status_code == 401:
                    print(f"   ❌ Non autorisé (401)")
                else:
                    print(f"   ❌ Erreur {detail_response.status_code}: {detail_response.text[:100]}")
                    
            except Exception as e:
                print(f"   ❌ Erreur de requête: {e}")
    
    # 3. Tester avec des IDs invalides
    print(f"\n3️⃣ TEST AVEC IDS INVALIDES")
    print("-" * 35)
    
    invalid_ids = [999, 0, -1, 'abc']
    
    for invalid_id in invalid_ids:
        print(f"\n🔍 Test ID invalide: {invalid_id}")
        detail_url = f'http://127.0.0.1:8000/api/courses/{invalid_id}/'
        
        try:
            detail_response = requests.get(detail_url, headers=headers)
            print(f"   📡 GET {detail_url}")
            print(f"   📊 Status: {detail_response.status_code}")
            
            if detail_response.status_code == 404:
                print(f"   ✅ Erreur 404 attendue")
            else:
                print(f"   ⚠️ Status inattendu: {detail_response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
    
    # 4. Tester sans authentification
    print(f"\n4️⃣ TEST SANS AUTHENTIFICATION")
    print("-" * 35)
    
    detail_url = f'http://127.0.0.1:8000/api/courses/8/'
    
    try:
        detail_response = requests.get(detail_url)  # Sans headers
        print(f"📡 GET {detail_url} (sans token)")
        print(f"📊 Status: {detail_response.status_code}")
        
        if detail_response.status_code == 401:
            print(f"✅ Erreur 401 attendue (authentification requise)")
        else:
            print(f"⚠️ Status inattendu: {detail_response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_frontend_localStorage_simulation():
    """Simuler le problème de localStorage du frontend"""
    print(f"\n🧪 SIMULATION: Problème localStorage Frontend")
    print("=" * 50)
    
    # Simuler différents scénarios de token
    scenarios = [
        ("Token valide", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************.ZRDyH00LYq1rIWIN1Z5S3hOmPLRSyOXCyyEfxhUPBZ_A"),
        ("Token expiré", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNjAwMDAwMDAwLCJpYXQiOjE2MDAwMDAwMDAsImp0aSI6ImV4cGlyZWQiLCJ1c2VyX2lkIjoxfQ.invalid"),
        ("Token invalide", "invalid.token.here"),
        ("Token null", None),
        ("Token vide", ""),
    ]
    
    for scenario_name, token in scenarios:
        print(f"\n🔍 Scénario: {scenario_name}")
        
        headers = {}
        if token:
            headers['Authorization'] = f'Bearer {token}'
        
        detail_url = f'http://127.0.0.1:8000/api/courses/8/'
        
        try:
            detail_response = requests.get(detail_url, headers=headers)
            print(f"   📊 Status: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                print(f"   ✅ Succès")
            elif detail_response.status_code == 401:
                print(f"   ❌ Non autorisé (token problématique)")
            elif detail_response.status_code == 404:
                print(f"   ❌ Cours non trouvé")
            else:
                print(f"   ⚠️ Status inattendu: {detail_response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Erreur: {e}")

def main():
    """Fonction principale"""
    try:
        test_course_detail_simulation()
        test_frontend_localStorage_simulation()
        
        print(f"\n\n📋 DIAGNOSTIC FINAL")
        print("=" * 20)
        print("🔍 Causes possibles du 'cours non trouvé':")
        print("   1. Token d'authentification manquant/invalide/expiré")
        print("   2. ID de cours invalide dans l'URL")
        print("   3. Problème de CORS (peu probable ici)")
        print("   4. Erreur de réseau/serveur")
        print("   5. Problème de localStorage dans le navigateur")
        print(f"\n💡 Solutions recommandées:")
        print("   1. Vérifier la console du navigateur pour les erreurs")
        print("   2. Vérifier que l'utilisateur est bien connecté")
        print("   3. Tester avec un ID de cours valide (8, 6, 9, 10)")
        print("   4. Vider le localStorage et se reconnecter")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
