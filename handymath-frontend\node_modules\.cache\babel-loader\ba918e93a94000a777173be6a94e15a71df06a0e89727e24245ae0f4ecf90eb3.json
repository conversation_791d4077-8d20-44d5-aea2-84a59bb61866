{"ast": null, "code": "export var setDifferenceDocs = {\n  name: 'setDifference',\n  category: 'Set',\n  syntax: ['setDifference(set1, set2)'],\n  description: 'Create the difference of two (multi)sets: every element of set1, that is not the element of set2. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setDifference([1, 2, 3, 4], [3, 4, 5, 6])', 'setDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setIntersect', 'setSymDifference']\n};", "map": {"version": 3, "names": ["setDifferenceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath codeF/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setDifference.js"], "sourcesContent": ["export var setDifferenceDocs = {\n  name: 'setDifference',\n  category: 'Set',\n  syntax: ['setDifference(set1, set2)'],\n  description: 'Create the difference of two (multi)sets: every element of set1, that is not the element of set2. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setDifference([1, 2, 3, 4], [3, 4, 5, 6])', 'setDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setIntersect', 'setSymDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG;EAC7BC,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,2BAA2B,CAAC;EACrCC,WAAW,EAAE,6LAA6L;EAC1MC,QAAQ,EAAE,CAAC,2CAA2C,EAAE,mDAAmD,CAAC;EAC5GC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,kBAAkB;AAC1D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}